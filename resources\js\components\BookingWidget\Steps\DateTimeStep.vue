<template>
  <div class="kivi-booking-step" id="step-datetime">
    <h2 class="kivi-step-title">Select Date and Time</h2>
    <p class="kivi-step-subtitle">Choose when you'd like to schedule your appointment.</p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader"></div>
    </div>

    <template v-else>
      <div class="kivi-date-time-container">
        <!-- Calendar Section -->
        <div class="kivi-date-section">
          <h3 class="kivi-section-title">1. Select a Date</h3>

          <!-- Month Navigation -->
          <div class="kivi-calendar-header">
            <button
              @click="previousMonth"
              class="kivi-calendar-nav-btn"
              :disabled="isPreviousMonthDisabled"
              :class="{ 'disabled': isPreviousMonthDisabled }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <span class="kivi-current-month">{{ currentMonthName }} {{ currentYear }}</span>
            <button
              @click="nextMonth"
              class="kivi-calendar-nav-btn"
              :disabled="isNextMonthDisabled"
              :class="{ 'disabled': isNextMonthDisabled }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>

          <!-- Weekday Headers -->
          <div class="kivi-calendar-weekdays">
            <div v-for="day in weekdays" :key="day" class="kivi-weekday">{{ day }}</div>
          </div>

          <!-- Calendar Days -->
          <div class="kivi-calendar-days">
            <!-- Placeholder for days from previous month -->
            <div
              v-for="emptyDay in firstDayOfMonth"
              :key="'empty-' + emptyDay"
              class="kivi-calendar-day empty"
            ></div>

            <!-- Actual days of current month -->
            <div
              v-for="day in daysInMonth"
              :key="day"
              class="kivi-calendar-day"
              :class="{
                'unavailable': isDateUnavailable(day),
                'selected': isDateSelected(day),
                'has-appointments': hasAvailableAppointments(day),
                'today': isToday(day),
                'future': isFutureDate(day)
              }"
              @click="selectDate(day)"
            >
              <span class="kivi-day-number">{{ day }}</span>
              <span v-if="hasAvailableAppointments(day)" class="kivi-availability-indicator">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="kivi-availability-icon">
                  <circle cx="12" cy="12" r="10" />
                </svg>
              </span>
            </div>

            <!-- Placeholder for days from next month -->
            <div
              v-for="emptyDay in remainingDaysInCalendarView"
              :key="'next-empty-' + emptyDay"
              class="kivi-calendar-day empty"
            ></div>
          </div>

          <div class="kivi-calendar-legend">
            <div class="kivi-legend-item">
              <span class="kivi-legend-indicator available"></span>
              <span class="kivi-legend-text">Available</span>
            </div>
            <div class="kivi-legend-item">
              <span class="kivi-legend-indicator unavailable"></span>
              <span class="kivi-legend-text">Unavailable</span>
            </div>
            <div class="kivi-legend-item">
              <span class="kivi-legend-indicator selected"></span>
              <span class="kivi-legend-text">Selected</span>
            </div>
          </div>
        </div>

        <!-- Time Slots Section -->
        <div class="kivi-time-section">
          <h3 class="kivi-section-title">2. Select a Time</h3>

          <template v-if="selectedDateObj">
            <div class="kivi-selected-date">
              <span>{{ formatSelectedDate(selectedDateObj) }}</span>
            </div>

            <div v-if="isLoadingSlots" class="kivi-loader-container">
              <div class="kivi-loader"></div>
            </div>

            <div v-else-if="timeSlots.length === 0" class="kivi-empty-state">
              <svg xmlns="http://www.w3.org/2000/svg" class="kivi-empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10" />
                <line x1="12" y1="8" x2="12" y2="12" />
                <line x1="12" y1="16" x2="12.01" y2="16" />
              </svg>
              <p>No available time slots for the selected date.</p>
              <p>Please choose another date with available slots.</p>
            </div>

            <template v-else>
              <!-- Time slot availability grouping -->
              <div class="kivi-time-slot-groups">
                <div v-if="morningSlots.length > 0" class="kivi-time-slot-group">
                  <h4 class="kivi-time-group-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="kivi-time-group-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="5" />
                      <line x1="12" y1="1" x2="12" y2="3" />
                      <line x1="12" y1="21" x2="12" y2="23" />
                      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" />
                      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" />
                      <line x1="1" y1="12" x2="3" y2="12" />
                      <line x1="21" y1="12" x2="23" y2="12" />
                      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" />
                      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" />
                    </svg>
                    Morning
                  </h4>
                  <div class="kivi-time-slots">
                    <div
                      v-for="slot in morningSlots"
                      :key="slot.time"
                      class="kivi-time-slot"
                      :class="{ 'selected': selectedTime === slot.time }"
                      @click="selectTimeSlot(slot.time)"
                    >
                      <span class="kivi-time-slot-time">{{ formatTime(slot.time) }}</span>
                    </div>
                  </div>
                </div>

                <div v-if="afternoonSlots.length > 0" class="kivi-time-slot-group">
                  <h4 class="kivi-time-group-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="kivi-time-group-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 1v22m5.5-15h-11" />
                    </svg>
                    Afternoon
                  </h4>
                  <div class="kivi-time-slots">
                    <div
                      v-for="slot in afternoonSlots"
                      :key="slot.time"
                      class="kivi-time-slot"
                      :class="{ 'selected': selectedTime === slot.time }"
                      @click="selectTimeSlot(slot.time)"
                    >
                      <span class="kivi-time-slot-time">{{ formatTime(slot.time) }}</span>
                    </div>
                  </div>
                </div>

                <div v-if="eveningSlots.length > 0" class="kivi-time-slot-group">
                  <h4 class="kivi-time-group-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="kivi-time-group-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z" />
                    </svg>
                    Evening
                  </h4>
                  <div class="kivi-time-slots">
                    <div
                      v-for="slot in eveningSlots"
                      :key="slot.time"
                      class="kivi-time-slot"
                      :class="{ 'selected': selectedTime === slot.time }"
                      @click="selectTimeSlot(slot.time)"
                    >
                      <span class="kivi-time-slot-time">{{ formatTime(slot.time) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </template>

          <div v-else class="kivi-prompt-select-date">
            <svg xmlns="http://www.w3.org/2000/svg" class="kivi-prompt-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
              <line x1="16" y1="2" x2="16" y2="6" />
              <line x1="8" y1="2" x2="8" y2="6" />
              <line x1="3" y1="10" x2="21" y2="10" />
            </svg>
            <p>Please select a date first</p>
          </div>
        </div>
      </div>

      <!-- Selection Summary -->
      <div v-if="selectedDateObj && selectedTime" class="kivi-selection-summary">
        <h3 class="kivi-summary-title">Your Selection</h3>
        <div class="kivi-summary-content">
          <div class="kivi-summary-item">
            <svg xmlns="http://www.w3.org/2000/svg" class="kivi-summary-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
              <line x1="16" y1="2" x2="16" y2="6" />
              <line x1="8" y1="2" x2="8" y2="6" />
              <line x1="3" y1="10" x2="21" y2="10" />
            </svg>
            <span>{{ formatSelectedDate(selectedDateObj) }}</span>
          </div>
          <div class="kivi-summary-item">
            <svg xmlns="http://www.w3.org/2000/svg" class="kivi-summary-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
            <span>{{ formatTime(selectedTime) }}</span>
          </div>
        </div>

        <button class="kivi-reset-selection" @click="resetSelection">
          <svg xmlns="http://www.w3.org/2000/svg" class="kivi-reset-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 12a9 9 0 0 1-9 9c-2.39 0-4.68-.94-6.4-2.6" />
            <path d="M9 6.6A9 9 0 0 1 21 12" />
            <polyline points="3 14 9 18 15 12" />
          </svg>
          Change Selection
        </button>
      </div>
    </template>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'DateTimeStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      selectedDateObj: null,
      selectedTime: null,
      timeSlots: [],
      isLoading: false,
      isLoadingSlots: false,
      datesWithAvailability: [],
      availableWeekdays: [],
      clinicHolidays: [],
      availableDaysLoaded: false,
      currentMonth: new Date().getMonth(),
      currentYear: new Date().getFullYear(),
      weekdays: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
      monthNames: [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ]
    };
  },
  computed: {
    // These are now used only for UI purposes, not for API calls
    clinicId() {
      return this.bookingData.clinic ? this.bookingData.clinic.id : null;
    },

    serviceIds() {
      return this.bookingData.services ? this.bookingData.services.map(s => s.id).join(',') : '';
    },

    doctorId() {
      return this.bookingData.doctor ? this.bookingData.doctor.id : null;
    },

    minDate() {
      // Set minimum date to today (can't book for past dates)
      return new Date();
    },

    maxDate() {
      // Set maximum date to 3 months from now
      const maxDate = new Date();
      maxDate.setMonth(maxDate.getMonth() + 3);
      return maxDate;
    },

    // Calendar navigation controls
    isPreviousMonthDisabled() {
      // Check if the previous month is earlier than the current month
      const today = new Date();
      if (this.currentYear < today.getFullYear()) {
        return true;
      }
      if (this.currentYear === today.getFullYear() && this.currentMonth <= today.getMonth()) {
        return true;
      }
      return false;
    },

    isNextMonthDisabled() {
      // Check if the next month is later than the max allowed date
      const maxAllowed = new Date(this.maxDate);
      if (this.currentYear > maxAllowed.getFullYear()) {
        return true;
      }
      if (this.currentYear === maxAllowed.getFullYear() && this.currentMonth >= maxAllowed.getMonth()) {
        return true;
      }
      return false;
    },

    currentMonthName() {
      return this.monthNames[this.currentMonth];
    },

    daysInMonth() {
      // Returns the number of days in the current month
      return new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
    },

    firstDayOfMonth() {
      // Return the day of the week for the first day of the month (0-6, where 0 is Sunday)
      return new Date(this.currentYear, this.currentMonth, 1).getDay();
    },

    remainingDaysInCalendarView() {
      // Calculate how many empty slots are needed at the end of the calendar to complete the grid
      const totalDaysInView = Math.ceil((this.daysInMonth + this.firstDayOfMonth) / 7) * 7;
      return totalDaysInView - this.daysInMonth - this.firstDayOfMonth;
    },

    morningSlots() {
      return this.timeSlots.filter(slot => {
        if (!slot?.time) return false;
        const timeStr = slot.time.toLowerCase();
        return timeStr.includes('am') || (timeStr.includes('pm') && timeStr.startsWith('12:'));
      });
    },

    afternoonSlots() {
      return this.timeSlots.filter(slot => {
        if (!slot?.time) return false;
        const timeStr = slot.time.toLowerCase();
        if (timeStr.includes('pm')) {
          const hour = parseInt(timeStr.match(/(\d+):/)?.[1] || '0');
          return hour >= 1 && hour <= 4;
        }
        return false;
      });
    },

    eveningSlots() {
      return this.timeSlots.filter(slot => {
        if (!slot?.time) return false;
        const timeStr = slot.time.toLowerCase();
        if (timeStr.includes('pm')) {
          const hour = parseInt(timeStr.match(/(\d+):/)?.[1] || '0');
          return hour >= 5 && hour <= 11;
        }
        return false;
      });
    }
  },
  watch: {
    selectedDateObj(newDate) {
      if (newDate) {
        this.fetchTimeSlots(newDate);
      } else {
        this.timeSlots = [];
        this.selectedTime = null;
      }
    },

    selectedTime(newTime) {
      if (newTime) {
        this.$emit('update:booking-data', {
          ...this.bookingData,
          date: this.formatDateForApi(this.selectedDateObj),
          time: newTime
        });
      }
    },

    // Watch for clinic and doctor data to become available
    clinicId(newClinicId) {
      if (newClinicId && this.doctorId && !this.availableDaysLoaded) {
        this.loadAvailableDays();
      }
    },

    doctorId(newDoctorId) {
      if (newDoctorId && this.clinicId && !this.availableDaysLoaded) {
        this.loadAvailableDays();
      }
    }
  },
  mounted() {
    if (this.bookingData.date && this.bookingData.time) {
      this.selectedDateObj = new Date(this.bookingData.date);
      this.selectedTime = this.bookingData.time;
      this.currentMonth = this.selectedDateObj.getMonth();
      this.currentYear = this.selectedDateObj.getFullYear();
    }

    // Only load available days if we already have the required data
    if (this.clinicId && this.doctorId) {
      this.loadAvailableDays();
    }
  },
  methods: {
    // Calendar Navigation
    previousMonth() {
      if (this.isPreviousMonthDisabled) return;

      if (this.currentMonth === 0) {
        this.currentMonth = 11;
        this.currentYear--;
      } else {
        this.currentMonth--;
      }
    },

    nextMonth() {
      if (this.isNextMonthDisabled) return;

      if (this.currentMonth === 11) {
        this.currentMonth = 0;
        this.currentYear++;
      } else {
        this.currentMonth++;
      }
    },

    // Date Selection and Validation
    selectDate(day) {
      // Create a new date object for the selected day
      const date = new Date(this.currentYear, this.currentMonth, day);

      // Check if the date is unavailable
      if (this.isDateUnavailable(day)) {
        return;
      }

      // Set as selected date
      this.selectedDateObj = date;

      // Clear any previously selected time
      this.selectedTime = null;

      // Note: fetchTimeSlots will be called automatically by the selectedDateObj watcher
    },

    isDateUnavailable(day) {
      const date = new Date(this.currentYear, this.currentMonth, day);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (date < today) return true;

      const dateStr = this.formatDateForApi(date);
      return !this.datesWithAvailability.includes(dateStr);
    },

    isDateSelected(day) {
      if (!this.selectedDateObj) return false;
      return this.selectedDateObj.getFullYear() === this.currentYear &&
             this.selectedDateObj.getMonth() === this.currentMonth &&
             this.selectedDateObj.getDate() === day;
    },

    hasAvailableAppointments(day) {
      const date = new Date(this.currentYear, this.currentMonth, day);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (date < today) return false;

      const dateStr = this.formatDateForApi(date);
      return this.datesWithAvailability.includes(dateStr);
    },

    isToday(day) {
      const today = new Date();
      return today.getFullYear() === this.currentYear &&
             today.getMonth() === this.currentMonth &&
             today.getDate() === day;
    },

    isFutureDate(day) {
      const date = new Date(this.currentYear, this.currentMonth, day);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return date > today;
    },


    async loadAvailableDays() {
      // Get IDs from URL params or booking data
      const clinicId = this.clinicId;
      const doctorId = this.doctorId;

      if (!clinicId || !doctorId) {
        console.log('Missing clinic or doctor ID, skipping loadAvailableDays');
        return;
      }

      try {
        this.isLoading = true;
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = window.ajaxData?.get_nonce || '';

        const response = await axios.get(ajaxurl, {
          params: {
            action: 'ajax_get',
            route_name: 'get_doctor_workdays',
            clinic_id: clinicId,
            doctor_id: doctorId,
            type: 'flatpicker',
            _ajax_nonce: nonce
          }
        });

        if (response.data?.status) {
          this.availableWeekdays = response.data.data || [];
          this.clinicHolidays = response.data.holiday || [];
          this.processAvailableDates();
        }
      } catch (error) {
        console.error('Error loading available days:', error);
      } finally {
        this.isLoading = false;
        this.availableDaysLoaded = true;
      }
    },

    processAvailableDates() {
      this.datesWithAvailability = [];
      const holidayDates = new Set();

      if (Array.isArray(this.clinicHolidays)) {
        this.clinicHolidays.forEach(holiday => {
          const startDate = new Date(holiday.start_date);
          const endDate = new Date(holiday.end_date);
          for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
            holidayDates.add(d.toISOString().split('T')[0]);
          }
        });
      }

      // availableWeekdays contains UNAVAILABLE days (days doctor doesn't work)
      const unavailableDays = this.availableWeekdays.length > 0 ? this.availableWeekdays : [];
      const today = new Date();
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 3);

      for (let d = new Date(today); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        const dayOfWeek = d.getDay();

        // Day is available if it's NOT in unavailable days and NOT a holiday
        if (!unavailableDays.includes(dayOfWeek) && !holidayDates.has(dateStr)) {
          this.datesWithAvailability.push(dateStr);
        }
      }
    },

    async fetchTimeSlots(date) {
      if (!date || !this.bookingData.doctor?.id || !this.bookingData.clinic?.id) {
        this.timeSlots = [];
        return;
      }

      try {
        this.isLoading = true;
        this.timeSlots = [];

        const formattedDate = this.formatDateForApi(date);
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = window.ajaxData?.get_nonce || '';

        const params = {
          action: 'ajax_get',
          route_name: 'get_time_slots',
          doctor_id: this.bookingData.doctor.id,
          clinic_id: this.bookingData.clinic.id,
          date: formattedDate,
          widgetType: 'phpWidget',
          _ajax_nonce: nonce
        };

        // Add service parameters
        if (this.bookingData.services?.length > 0) {
          this.bookingData.services.forEach((service, index) => {
            params[`service[${index}][id]`] = service.id;
            params[`service[${index}][service_id]`] = service.service_id || service.id;
            params[`service[${index}][name]`] = service.name;
            params[`service[${index}][charges]`] = service.charges || service.price || '$0';
          });
        }

        const response = await axios.get(ajaxurl, { params });

        if (response.data?.status) {
          let slots = [];

          if (Array.isArray(response.data.data)) {
            if (response.data.data.length > 0 && Array.isArray(response.data.data[0])) {
              slots = response.data.data.flat().map(slot => ({
                time: slot.time || slot.start_time,
                end_time: slot.end_time || null,
                available: slot.available !== false
              }));
            } else if (response.data.data[0]?.start_time) {
              slots = response.data.data.map(slot => ({
                time: slot.start_time,
                end_time: slot.end_time || null,
                id: slot.id || null
              }));
            } else if (typeof response.data.data[0] === 'string') {
              slots = response.data.data.map(time => ({
                time: time,
                end_time: null
              }));
            }
          }

          this.timeSlots = slots;
        }
      } catch (error) {
        console.error('Error fetching time slots:', error);
      } finally {
        this.isLoading = false;
      }
    },


    selectTimeSlot(time) {
      this.selectedTime = time;
    },

    resetSelection() {
      this.selectedTime = null;
    },

    // Formatting Helpers
    formatSelectedDate(date) {
      if (!date) return '';

      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      return date.toLocaleDateString(undefined, options);
    },

    formatDateForApi(date) {
      if (!date) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    },

    formatTime(timeString) {
      if (!timeString) return '';
      const timeStr = String(timeString);

      if (timeStr.toLowerCase().includes('am') || timeStr.toLowerCase().includes('pm')) {
        return timeStr.toUpperCase();
      }

      if (timeStr.includes(':')) {
        const [hour, min] = timeStr.split(':');
        const h = parseInt(hour) || 0;
        const ampm = h >= 12 ? 'PM' : 'AM';
        const hour12 = h % 12 || 12;
        return `${hour12}:${min || '00'} ${ampm}`;
      }

      return timeStr;
    }
  }
};
</script>

<style scoped>
:root {
  --primary-color: #4f46e5;
  --primary-light: rgba(79, 70, 229, 0.1);
  --primary-lighter: rgba(79, 70, 229, 0.05);
  --white: #ffffff;
  --light-gray: #f3f4f6;
  --gray: #6b7280;
  --dark-gray: #374151;
  --black: #111827;
  --danger: #ef4444;
  --success: #10b981;
  --warning: #f59e0b;
  --radius: 0.375rem;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --transition: all 0.2s ease;
}

/* Step Header Styles */
.kivi-step-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 1rem;
  color: var(--gray);
  margin-bottom: 2rem;
}

/* Main Container Layout */
.kivi-date-time-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .kivi-date-time-container {
    grid-template-columns: 1fr 1fr;
  }
}

/* Section Headers */
.kivi-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

/* Calendar Section Styles */
.kivi-date-section {
  background-color: var(--white);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.kivi-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.kivi-current-month {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--black);
}

.kivi-calendar-nav-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
}

.kivi-calendar-nav-btn:hover {
  background-color: var(--primary-lighter);
  border-color: var(--primary-color);
}

.kivi-calendar-nav-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.kivi-calendar-nav-btn svg {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--dark-gray);
}

/* Weekday Header Styles */
.kivi-calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 0.5rem;
}

.kivi-weekday {
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 0;
  color: var(--dark-gray);
}

/* Calendar Days */
.kivi-calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
}

.kivi-calendar-day {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2.5rem;
  font-size: 0.875rem;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.kivi-calendar-day:not(.empty):hover {
  background-color: var(--primary-lighter);
}

.kivi-calendar-day.empty {
  cursor: default;
}

.kivi-calendar-day.today {
  border: 1px dashed var(--primary-color);
}

.kivi-calendar-day.selected {
  background-color: var(--primary-color);
  color: var(--white);
}

.kivi-calendar-day.unavailable {
  color: var(--gray);
  text-decoration: line-through;
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none; /* Prevent click events on unavailable dates */
}

.kivi-calendar-day.has-appointments::after {
  content: '';
  position: absolute;
  bottom: 0.25rem;
  width: 0.375rem;
  height: 0.375rem;
  background-color: var(--success);
  border-radius: 50%;
}

.kivi-availability-indicator {
  position: absolute;
  bottom: 0.25rem;
  width: 0.375rem;
  height: 0.375rem;
}

.kivi-availability-icon {
  width: 0.375rem;
  height: 0.375rem;
  color: var(--success);
}

/* Calendar Legend */
.kivi-calendar-legend {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--light-gray);
}

.kivi-legend-item {
  display: flex;
  align-items: center;
  margin: 0 0.5rem;
}

.kivi-legend-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.375rem;
}

.kivi-legend-indicator.available {
  background-color: var(--success);
}

.kivi-legend-indicator.unavailable {
  background-color: var(--gray);
  opacity: 0.5;
}

.kivi-legend-indicator.selected {
  background-color: var(--primary-color);
}

.kivi-legend-text {
  font-size: 0.75rem;
  color: var(--gray);
}

/* Time Section Styles */
.kivi-time-section {
  background-color: var(--white);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.kivi-selected-date {
  background-color: var(--primary-lighter);
  padding: 0.75rem;
  border-radius: var(--radius);
  margin-bottom: 1rem;
  font-weight: 500;
  color: var(--primary-color);
  text-align: center;
}

.kivi-time-slot-groups {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.kivi-time-group-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.75rem;
}

.kivi-time-group-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.kivi-time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(6rem, 1fr));
  gap: 0.5rem;
}

.kivi-time-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.kivi-time-slot:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-lighter);
}

.kivi-time-slot.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: var(--white);
}

.kivi-time-slot-time {
  font-size: 0.875rem;
}

/* Empty States and Prompts */
.kivi-prompt-select-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  color: var(--gray);
}

.kivi-prompt-icon {
  width: 3rem;
  height: 3rem;
  color: var(--gray);
  opacity: 0.5;
  margin-bottom: 1rem;
}

.kivi-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  color: var(--gray);
  background-color: var(--light-gray);
  border-radius: var(--radius);
  text-align: center;
}

.kivi-empty-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: var(--gray);
  margin-bottom: 1rem;
}

/* Selection Summary */
.kivi-selection-summary {
  background-color: var(--primary-lighter);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius);
  padding: 1.5rem;
  margin-top: 2rem;
  animation: fadeIn 0.3s ease;
}

.kivi-summary-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.kivi-summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.kivi-summary-item {
  display: flex;
  align-items: center;
}

.kivi-summary-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  color: var(--primary-color);
}

.kivi-reset-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  color: var(--primary-color);
  font-weight: 500;
  padding: 0.5rem;
  margin-top: 1rem;
  cursor: pointer;
  transition: var(--transition);
}

.kivi-reset-selection:hover {
  text-decoration: underline;
}

.kivi-reset-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

/* Loading States */
.kivi-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.kivi-loader {
  border: 3px solid rgba(229, 231, 235, 0.3);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2rem;
  height: 2rem;
  animation: kivi-spin 1s linear infinite;
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(1rem); }
  to { opacity: 1; transform: translateY(0); }
}
</style>