<template>
  <div class="overflow-hidden pb-4">
    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-10">
      <div class="inline-block h-10 w-10 animate-spin rounded-full border-4 border-solid border-black border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
        <span class="sr-only">{{ formTranslation.common.loading || 'Loading...' }}</span>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else-if="!tasks.length" class="flex flex-col items-center justify-center text-center p-8 bg-gray-50 rounded-xl min-h-[350px] border border-gray-200 shadow-sm">
      <div class="bg-black bg-opacity-10 p-4 rounded-full mb-4">
        <i class="ri-dashboard-line text-black text-3xl"></i>
      </div>
      <h5 class="mb-3 text-xl font-medium text-gray-800">{{ formTranslation.task.no_tasks_found || 'No Tasks Found' }}</h5>
      <p class="text-gray-500 max-w-md">{{ formTranslation.task.no_tasks_description || 'You have no tasks yet. Create your first task to get started.' }}</p>
      <button class="mt-6 px-5 py-2.5 bg-black text-white rounded-lg hover:bg-black-dark transition-colors duration-200 flex items-center shadow-sm hover:shadow focus:outline-none" @click="$emit('add-task')">
        <i class="ri-add-line mr-2"></i> {{ formTranslation.task.add_task || 'Add Task' }}
      </button>
    </div>

    <!-- Kanban board -->
    <div v-else class="min-h-[600px] overflow-x-auto pb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-5 min-w-full lg:min-w-[1000px]">
        <!-- Generate columns dynamically -->
        <div 
          v-for="column in columns" 
          :key="column.status" 
          class="flex flex-col bg-gray-50 rounded-xl shadow-sm border border-gray-200 min-h-[450px] transition-all duration-200"
          :class="{ 'drop-highlight': isDropTarget === column.status }"
          @dragover.prevent="handleDragOver($event, column.status)"
          @dragleave.prevent="handleDragLeave($event, column.status)"
          @drop.prevent="onDrop($event, column.status)"
        >
          <div :class="`p-4 rounded-t-xl ${column.bgClass} border-b ${column.borderClass}`">
            <div class="flex items-center justify-between">
              <h5 class="flex items-center text-base font-medium m-0 text-gray-800">
                <i :class="`${column.icon} mr-2 ${column.textClass}`"></i>
                {{ getStatusLabel(column.status) }}
              </h5>
              <span :class="`inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium ${column.badgeClass}`">
                {{ tasksByStatus[column.status].length }}
              </span>
            </div>
          </div>

          <div class="flex-1 min-h-[100px] max-h-[calc(100vh-250px)] overflow-y-auto p-4 rounded-b-xl">
            <transition-group name="task-list" tag="div" class="task-container">
              <div 
                v-for="task in tasksByStatus[column.status]" 
                :key="task.id" 
                class="mb-3 cursor-grab select-none task-item transition-all duration-200 hover:translate-y-[-2px]"
                :class="{ 'is-dragging': draggedTaskId === task.id }"
                draggable="true"
                @dragstart="onDragStart($event, task.id, column.status)"
                @dragend="onDragEnd"
                @click="$emit('view-task', task.id)"
              >
                <task-card 
                  :task="task" 
                  :completed="column.status === 'completed'" 
                  :cancelled="column.status === 'cancelled'" 
                  @status-change="$emit('status-change', $event)"
                  @complete-task="$emit('complete-task', $event)"
                />
              </div>
            </transition-group>
            
            <!-- Empty column state -->
            <div v-if="tasksByStatus[column.status].length === 0" class="h-24 flex items-center justify-center text-gray-400 border-2 border-dashed border-gray-200 rounded-lg">
              <p class="text-sm">{{ 'Drag tasks here' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Ghost element for better drag visualization -->
    <div v-show="isDragging" ref="ghostEl" class="ghost-card fixed pointer-events-none z-50 opacity-0">
      <!-- This will be populated during dragstart -->
    </div>
  </div>
</template>

<script>
import TaskCard from './TaskCard.vue';

export default {
  name: 'TaskKanban',
  components: {
    TaskCard
  },
  props: {
    tasks: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      draggedTaskId: null,
      dragSourceColumn: null,
      isDropTarget: null,
      isDragging: false,
      columns: [
        { 
          status: 'pending', 
          icon: 'ri-time-line', 
          bgClass: 'bg-yellow-50', 
          borderClass: 'border-yellow-200',
          textClass: 'text-yellow-600',
          badgeClass: 'bg-yellow-100 text-yellow-800'
        },
        { 
          status: 'in-progress', 
          icon: 'ri-loader-line', 
          bgClass: 'bg-blue-50', 
          borderClass: 'border-blue-200',
          textClass: 'text-blue-600',
          badgeClass: 'bg-blue-100 text-blue-800'
        },
        { 
          status: 'completed', 
          icon: 'ri-check-line', 
          bgClass: 'bg-green-50', 
          borderClass: 'border-green-200',
          textClass: 'text-green-600',
          badgeClass: 'bg-green-100 text-green-800'
        },
        { 
          status: 'cancelled', 
          icon: 'ri-close-line', 
          bgClass: 'bg-red-50', 
          borderClass: 'border-red-200',
          textClass: 'text-red-600',
          badgeClass: 'bg-red-100 text-red-800'
        }
      ]
    };
  },
  computed: {
    tasksByStatus() {
      return Object.fromEntries(
        this.columns.map(column => [column.status, this.tasks.filter(task => task.status === column.status)])
      );
    }
  },
  methods: {
    getStatusLabel(status) {
      const labels = {
        'pending': 'Pending',
        'in-progress': 'In Progress',
        'completed': 'Completed',
        'cancelled': 'Cancelled'
      };
      
      return labels[status] || status;
    },

    onDragStart(event, taskId, sourceColumn) {
      // Prevent default to avoid any browser weird behavior
      event.stopPropagation();
      
      // Set data for transfer
      this.draggedTaskId = taskId;
      this.dragSourceColumn = sourceColumn;
      this.isDragging = true;
      
      // Set the data that will be transferred
      event.dataTransfer.setData('text/plain', taskId);
      
      // Make drag image transparent to use our custom ghost
      const img = new Image();
      img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // Transparent 1x1 pixel
      event.dataTransfer.setDragImage(img, 0, 0);
      
      // Set correct cursor
      event.dataTransfer.effectAllowed = 'move';
      
      // Delay to ensure DOM is updated
      setTimeout(() => {
        const draggedEl = document.querySelector('.is-dragging task-card');
        if (draggedEl && this.$refs.ghostEl) {
          // Clone visual for ghost element (optional - requires more complex code)
          // this.$refs.ghostEl.innerHTML = '';
          // this.$refs.ghostEl.appendChild(draggedEl.cloneNode(true));
        }
      }, 0);
    },
    
    handleDragOver(event, columnStatus) {
      // Explicitly prevent default to allow drop
      event.preventDefault();
      
      // Update visual cue
      this.isDropTarget = columnStatus;
      
      // Set the drop effect
      event.dataTransfer.dropEffect = 'move';
    },
    
    handleDragLeave(event, columnStatus) {
      // Only clear if it's not entering a child element
      if (!event.currentTarget.contains(event.relatedTarget)) {
        this.isDropTarget = null;
      }
    },
    
    onDragEnd() {
      // Reset all states
      this.isDragging = false;
      this.isDropTarget = null;
    },
    
    onDrop(event, newStatus) {
      // Prevent default browser drop behavior
      event.preventDefault();
      
      // Get the dragged task ID
      const taskId = event.dataTransfer.getData('text/plain');
      
      // Only emit event if status actually changed (optimization)
      if (taskId && this.dragSourceColumn !== newStatus) {
        this.$emit('status-change', { taskId: parseInt(taskId), status: newStatus });
      }
      
      // Reset states
      this.draggedTaskId = null;
      this.dragSourceColumn = null;
      this.isDropTarget = null;
      this.isDragging = false;
    }
  }
};
</script>

<style scoped>
.drop-highlight {
  border-color: var(--black-color, #4f46e5);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
  transform: scale(1.01);
}

.is-dragging {
  opacity: 0.5;
  transform: scale(0.95);
}

/* Transition for task list items */
.task-list-enter-active,
.task-list-leave-active {
  transition: all 0.3s ease;
}

.task-list-enter-from,
.task-list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.task-list-move {
  transition: transform 0.3s ease;
}

/* Ghost card */
.ghost-card {
  transform: translate(-50%, -50%);
  width: calc(100% - 2rem);
  max-width: 280px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Scrollbar styling */
div::-webkit-scrollbar {
  width: 6px;
}
div::-webkit-scrollbar-track {
  background: transparent;
}
div::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}
div::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}
</style>