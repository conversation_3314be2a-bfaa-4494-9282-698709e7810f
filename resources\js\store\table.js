import {helperModuleTableColumns} from "../utils/list";
export default {
  namespaced: true,
  state: {
    columns: {}
  },
  mutations: {
    SET_COLUMNS(state, { module, payload }) {
      state.columns[module] = payload;
    }
  },
  actions: {
    async fetchTableColumns({ commit,state }, { endpoint, module, params }) {
      if(state?.columns?.[module]) {
        return state?.columns?.[module];
      }
      try {
        const data = await helperModuleTableColumns(endpoint, module, params);
        if (data) {
          commit("SET_COLUMNS", { module, payload: data });
          return data;
        }
      } catch (error) {
        console.error(error);
        return null;
      }
    }
  },
  getters: {
    // Getter to access columns by module name
    getColumnsByModuleName: state => moduleName => {
      return state.columns[moduleName] || {};
    }
  }
};
