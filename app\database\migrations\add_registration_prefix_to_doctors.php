<?php
/**
 * Migration: Add Registration Prefix to Doctor Data
 */

if (!defined('ABSPATH')) {
    exit;
}

class AddRegistrationPrefixToDoctors {
    /**
     * Run the migration
     */
    public function up() {
        global $wpdb;
        
        // Get all users with the doctor role and their basic_data
        $doctor_role = 'kivi<PERSON>are_doctor';
        $doctor_query = "
            SELECT u.ID, u.display_name, um.meta_value 
            FROM {$wpdb->users} u 
            JOIN {$wpdb->usermeta} um_role ON u.ID = um_role.user_id 
            LEFT JOIN {$wpdb->usermeta} um ON u.ID = um.user_id AND um.meta_key = 'basic_data'
            WHERE um_role.meta_key = '{$wpdb->prefix}capabilities' 
            AND um_role.meta_value LIKE '%{$doctor_role}%'
        ";
        
        $doctors = $wpdb->get_results($doctor_query);
        
        if (empty($doctors)) {
            return true; // No doctors to update
        }
        
        $updated = 0;
        
        // Process each doctor
        foreach ($doctors as $doctor) {
            $doctor_id = $doctor->ID;
            $meta_value = $doctor->meta_value;
            
            try {
                if (!empty($meta_value)) {
                    // Parse existing JSON
                    $data = json_decode($meta_value, true);
                    
                    if (is_array($data)) {
                        // Check if registration_prefix already exists
                        if (!isset($data['registration_prefix'])) {
                            // Add registration_prefix field
                            $data['registration_prefix'] = '';
                            
                            // Update meta value
                            $new_meta_value = json_encode($data, JSON_UNESCAPED_UNICODE);
                            update_user_meta($doctor_id, 'basic_data', $new_meta_value);
                            $updated++;
                        }
                    } else {
                        // Invalid JSON, create new structure with essential fields
                        $data = [
                            'mobile_number' => '',
                            'gender' => '',
                            'dob' => '',
                            'address' => '',
                            'city' => '',
                            'state' => '',
                            'country' => '',
                            'postal_code' => '',
                            'gmc_no' => '',
                            'registration_prefix' => ''
                        ];
                        
                        $new_meta_value = json_encode($data, JSON_UNESCAPED_UNICODE);
                        update_user_meta($doctor_id, 'basic_data', $new_meta_value);
                        $updated++;
                    }
                } else {
                    // No basic_data exists, create new one with minimal required fields
                    $data = [
                        'mobile_number' => '',
                        'gender' => '',
                        'dob' => '',
                        'address' => '',
                        'city' => '',
                        'state' => '',
                        'country' => '',
                        'postal_code' => '',
                        'gmc_no' => '',
                        'registration_prefix' => ''
                    ];
                    
                    $new_meta_value = json_encode($data, JSON_UNESCAPED_UNICODE);
                    update_user_meta($doctor_id, 'basic_data', $new_meta_value);
                    $updated++;
                }
            } catch (Exception $e) {
                // Log error but continue with other doctors
                error_log("Error updating registration_prefix for doctor {$doctor_id}: " . $e->getMessage());
            }
        }
        
        error_log("AddRegistrationPrefixToDoctors migration: Updated {$updated} doctors");
        return true;
    }
    
    /**
     * Reverse the migration (optional)
     */
    public function down() {
        // This is a data migration, so down() would remove the field
        // from the basic_data JSON structure, but it's safer to leave it
        // in case it's already being used.
        return true;
    }
}