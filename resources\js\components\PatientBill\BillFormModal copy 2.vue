<template>
  <div
    v-if="showGenerateBillModal"
    class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 py-8""
  >
    <div class="bg-white rounded-lg w-full max-w-2xl m-4">
      <!-- Modal Header -->
      <div class="border-b px-4 py-3 flex justify-between items-center">
        <h3 class="text-lg font-semibold"> {{ formTranslation.patient_bill.generate_invoice }} </h3>
        <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
      
      <!-- Modal Body -->
      <div class="px-4 py-4">
        <div class="page-loader-section" v-if="isLoading">
          <loader-component-2></loader-component-2>
        </div>
        <form
          v-else
          id="patientBillDataForm"
          @submit.prevent="handleSubmit"
          :novalidate="true"
        >
          <div class="w-full mb-3">
            <button
              class="float-right px-3 py-1 text-sm text-white bg-blue-500 rounded hover:bg-blue-600 focus:outline-none"
              @click="handleBillModal"
            >
              <i v-if="!visible" class="fa fa-plus"></i>
              <i v-else class="fa fa-minus"></i>
              {{
                visible
                  ? formTranslation.patient_bill.bill_close
                  : formTranslation.patient_bill.bill_add_item
              }}
            </button>
          </div>

          <div v-show="showAddForm" class="transition-all duration-200">
            <div class="flex flex-wrap -mx-2">
              <div class="w-full md:w-4/12 px-2">
                <div class="mb-4">
                  <label class="block text-sm font-medium mb-1">
                    {{ formTranslation.common.service }}
                    <span class="text-red-500">*</span>
                  </label>
                  <multi-select
                    v-model="billItem.item_id"
                    :class="{
                      'border-red-500':
                        billItemSubmitted && $v.billItem.item_id.$error,
                    }"
                    :options="services"
                    label="label"
                    track-by="id"
                    @select="handleBillItemChange"
                    @remove="handleBillItemUnselect"
                    :taggable="true"
                    @tag="addServiceTag"
                    :showNoOptions="false"
                    class="w-full"
                  />
                  <span class="text-sm text-blue-500 font-semibold">{{
                    formTranslation.common.note
                  }}</span>
                  <div
                    v-if="billItemSubmitted && !$v.billItem.item_id.required"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formTranslation.patient_bill.service_required }}
                  </div>
                </div>
              </div>

              <div class="w-full md:w-3/12 px-2">
                <div class="mb-4">
                  <label class="block text-sm font-medium mb-1">
                    {{ formTranslation.common.price }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <span class="absolute left-3 top-2">{{
                      clinic_currency_prefix
                    }}</span>
                    <input
                      type="number"
                      v-model="billItem.price"
                      min="0"
                      :class="{
                        'border-red-500':
                          billItemSubmitted && $v.billItem.price.$error,
                      }"
                      class="w-full pl-8 pr-12 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter price"
                      oninput="validity.valid||(value='')"
                    />
                    <span class="absolute right-3 top-2">{{
                      clinic_currency_postfix
                    }}</span>
                  </div>
                  <div
                    v-if="billItemSubmitted && !$v.billItem.price.required"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formTranslation.patient_bill.price_required }}
                  </div>
                </div>
              </div>

              <div class="w-full md:w-2/12 px-2">
                <div class="mb-4">
                  <label class="block text-sm font-medium mb-1">
                    {{ formTranslation.common.quantity }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    v-model="billItem.qty"
                    min="0"
                    :class="{
                      'border-red-500':
                        billItemSubmitted && $v.billItem.qty.$error,
                    }"
                    class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter quantity"
                  />
                  <div
                    v-if="billItemSubmitted && !$v.billItem.qty.required"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formTranslation.patient_bill.quantity_required }}
                  </div>
                </div>
              </div>

              <div class="w-full md:w-3/12 px-2">
                <div class="mb-4">
                  <label class="block text-sm font-medium mb-1">
                    {{ formTranslation.common.total }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="px-3 py-2 border rounded bg-gray-50">
                    {{ clinic_currency_prefix }}{{ billItem.total
                    }}{{ clinic_currency_postfix }}
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-2 mb-4">
              <button
                class="px-3 py-1 text-sm text-white bg-blue-500 rounded hover:bg-blue-600 focus:outline-none"
                @click="addBillItem"
                v-html="billItemBtn"
              ></button>
              <button
                class="px-3 py-1 text-sm border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none"
                @click="cancelBillItem"
              >
                {{ formTranslation.common.cancel }}
              </button>
            </div>
          </div>

          <div v-show="showEditForm" class="transition-all duration-200">
            <!-- Similar edit form structure with same classes as add form -->
          </div>

          <div class="w-full overflow-x-auto">
            <table class="w-full text-sm border-collapse">
              <thead>
                <tr class="bg-gray-50">
                  <th class="p-2 border text-left">
                    {{ formTranslation.common.sr_no }}
                  </th>
                  <th class="p-2 border text-left">
                    {{ formTranslation.common.services }}
                  </th>
                  <th class="p-2 border text-left">
                    {{ formTranslation.common.price }}
                  </th>
                  <th class="p-2 border text-left">
                    {{ formTranslation.common.quantity }}
                  </th>
                  <th class="p-2 border text-left">
                    {{ formTranslation.common.total }}
                  </th>
                  <th class="p-2 border text-left">
                    {{ formTranslation.common.action }}
                  </th>
                </tr>
              </thead>
              <tbody
                v-if="
                  patientBillData.billItems !== undefined &&
                  patientBillData.billItems.length > 0
                "
              >
                <tr
                  v-for="(billing_item, index) in patientBillData.billItems"
                  :key="index"
                  class="hover:bg-gray-50"
                >
                  <td class="p-2 border">{{ ++index }}</td>
                  <td class="p-2 border">
                    {{
                      billing_item.item_id !== null &&
                      billing_item.item_id.label != null
                        ? billing_item.item_id.label
                        : " - "
                    }}
                  </td>
                  <td class="p-2 border">
                    {{ clinic_currency_prefix }}{{ billing_item.price
                    }}{{ clinic_currency_postfix }}
                  </td>
                  <td class="p-2 border">{{ billing_item.qty }}</td>
                  <td class="p-2 border">
                    {{ clinic_currency_prefix
                    }}{{ billing_item.price * billing_item.qty
                    }}{{ clinic_currency_postfix }}
                  </td>
                  <td class="p-2 border">
                    <div class="flex space-x-1">
                      <button
                        class="p-1 text-blue-500 hover:bg-blue-50 rounded"
                        @click="editBillItem(index)"
                      >
                        <i class="fa fa-pen-alt"></i>
                      </button>
                      <button
                        class="p-1 text-red-500 hover:bg-red-50 rounded"
                        @click="deleteBillItem(index)"
                      >
                        <i class="fa fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                  <td colspan="6" class="p-4 text-center">
                    <h4 class="text-blue-500 text-lg">
                      {{ formTranslation.common.no_records_found }}
                    </h4>
                  </td>
                </tr>
              </tbody>
            </table>
            <div
              v-if="
                submitted &&
                patientBillData.billItems !== undefined &&
                !(patientBillData.billItems.length > 0)
              "
              class="text-red-500 text-sm mt-2"
            >
              {{ formTranslation.patient_bill.please_add_bill_items }}
            </div>
          </div>

          <hr class="my-4" />

          <!-- Tax Table Section -->
          <div class="row" v-if="userData.addOns.kiviPro">
            <div class="w-full overflow-x-auto">
              <table class="min-w-full text-center border">
                <thead class="bg-gray-100">
                  <tr>
                    <th class="px-4 py-2 border">
                      {{ formTranslation.common.sr_no }}
                    </th>
                    <th class="px-4 py-2 border">
                      {{
                        formTranslation.common.tax +
                        " " +
                        formTranslation.common.name
                      }}
                    </th>
                    <th class="px-4 py-2 border">
                      {{ formTranslation.service.charges }}
                    </th>
                  </tr>
                </thead>
                <tbody
                  v-if="
                    patientBillData.taxes !== undefined &&
                    patientBillData.taxes.length > 0
                  "
                >
                  <tr
                    v-for="(tax_item, index) in patientBillData.taxes"
                    :key="index"
                    class="border"
                  >
                    <td class="px-4 py-2 border">{{ ++index }}</td>
                    <td class="px-4 py-2 border">
                      {{ tax_item.name ? tax_item.name : " - " }}
                    </td>
                    <td class="px-4 py-2 border">
                      {{ clinic_currency_prefix }}{{ tax_item.charges
                      }}{{ clinic_currency_postfix }}
                    </td>
                  </tr>
                  <tr class="border">
                    <td colspan="2" class="px-4 py-2 text-right border">
                      {{
                        formTranslation.common.total +
                        " " +
                        formTranslation.common.tax
                      }}
                    </td>
                    <td class="px-4 py-2 border">
                      {{ clinic_currency_prefix }}{{ patientBillData.tax_total
                      }}{{ clinic_currency_postfix }}
                    </td>
                  </tr>
                </tbody>
                <tbody v-else>
                  <tr>
                    <td colspan="3">
                      <h4 class="mb-0 text-blue-600">
                        {{ formTranslation.common.no_tax_found }}
                      </h4>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <hr class="my-4" />

          <!-- Amount Details Section -->
          <div class="flex flex-wrap -mx-2">
            <!-- Total Amount -->
            <div
              :class="[
                'px-2 w-full sm:w-1/2',
                userData.addOns.kiviPro && patientBillData.tax_total
                  ? 'md:w-1/4'
                  : 'md:w-1/3',
              ]"
            >
              <div class="mb-4">
                <label for="total_amount" class="block mb-2">
                  {{ formTranslation.common.total }}
                  <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <span class="absolute left-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_prefix
                  }}</span>
                  <input
                    id="total_amount"
                    readonly
                    disabled
                    :class="{
                      'border-red-500':
                        submitted && $v.patientBillData.total_amount.$error,
                    }"
                    :value="
                      userData.addOns.kiviPro && patientBillData.tax_total
                        ? patientBillData.total_amount -
                          patientBillData.tax_total
                        : patientBillData.total_amount
                    "
                    :placeholder="formTranslation.patient_bill.plh_total_amount"
                    type="text"
                    class="w-full px-8 py-2 border rounded"
                  />
                  <span class="absolute right-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_postfix
                  }}</span>
                </div>
                <div
                  v-if="submitted && !$v.patientBillData.total_amount.required"
                  class="mt-1 text-red-500 text-sm"
                >
                  {{ formTranslation.patient_bill.bill_total_required }}
                </div>
              </div>
            </div>

            <!-- Tax Total -->
            <div
              v-if="userData.addOns.kiviPro && patientBillData.tax_total"
              class="px-2 w-full sm:w-1/2 md:w-1/4"
            >
              <div class="mb-4">
                <label for="tax_total" class="block mb-2">
                  {{ formTranslation.common.tax }}
                  <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <span class="absolute left-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_prefix
                  }}</span>
                  <input
                    id="tax_total"
                    readonly
                    disabled
                    v-model="patientBillData.tax_total"
                    type="text"
                    class="w-full px-8 py-2 border rounded"
                  />
                  <span class="absolute right-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_postfix
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Discount -->
            <div
              :class="[
                'px-2 w-full sm:w-1/2',
                userData.addOns.kiviPro && patientBillData.tax_total
                  ? 'md:w-1/4'
                  : 'md:w-1/3',
              ]"
            >
              <div class="mb-4">
                <label for="discount" class="block mb-2">
                  {{ formTranslation.patient_bill.discount }}
                  <span class="text-red-500">*</span>
                  <span class="text-blue-600"
                    ><small>{{
                      formTranslation.patient_bill.discount_amount
                    }}</small></span
                  >
                </label>
                <div class="relative">
                  <span class="absolute left-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_prefix
                  }}</span>
                  <input
                    id="discount"
                    v-model="patientBillData.discount"
                    :class="{
                      'border-red-500':
                        submitted && $v.patientBillData.discount.$error,
                    }"
                    :disabled="
                      patientBillData.billItems !== undefined &&
                      !(patientBillData.billItems.length > 0)
                    "
                    type="number"
                    min="0"
                    class="w-full px-8 py-2 border rounded"
                    oninput="validity.valid||(value='');"
                  />
                  <span class="absolute right-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_postfix
                  }}</span>
                </div>
                <div
                  v-if="submitted && !$v.patientBillData.discount.required"
                  class="mt-1 text-red-500 text-sm"
                >
                  {{ formTranslation.patient_bill.discount_required }}
                </div>
              </div>
            </div>

            <!-- Payable Amount -->
            <div
              :class="[
                'px-2 w-full sm:w-1/2',
                userData.addOns.kiviPro && patientBillData.tax_total
                  ? 'md:w-1/4'
                  : 'md:w-1/3',
              ]"
            >
              <div class="mb-4">
                <label for="actual_amount" class="block mb-2">
                  {{ formTranslation.patient_bill.payable_amount }}
                </label>
                <div class="relative">
                  <span class="absolute left-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_prefix
                  }}</span>
                  <input
                    id="actual_amount"
                    readonly
                    disabled
                    v-model="patientBillData.actual_amount"
                    type="number"
                    class="w-full px-8 py-2 border rounded"
                  />
                  <span class="absolute right-3 top-1/2 -translate-y-1/2">{{
                    clinic_currency_postfix
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Status Section -->
          <div class="flex justify-end mb-4">
            <div class="w-full md:w-5/12">
              <div class="flex items-center">
                <div class="w-5/12">
                  <label class="block mt-1">
                    {{ formTranslation.patient_bill.payment_status }}
                  </label>
                </div>
                <div class="w-7/12">
                  <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                      <input
                        type="radio"
                        name="payment_status"
                        value="paid"
                        v-model="patientBillData.payment_status"
                        @change="handlePaymentChange"
                        class="form-radio"
                      />
                      <span class="ml-2">{{
                        formTranslation.patient_bill.paid
                      }}</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input
                        type="radio"
                        name="payment_status"
                        value="unpaid"
                        v-model="patientBillData.payment_status"
                        @change="handlePaymentChange"
                        class="form-radio"
                      />
                      <span class="ml-2">{{
                        formTranslation.patient_bill.unpaid
                      }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer Section -->
          <div class="flex flex-wrap items-center justify-between">
            <div>
              <h4 class="text-sm text-red-500">
                {{ formTranslation.patient_encounter.encounter_close_note }}
              </h4>
            </div>
            <div class="flex space-x-2">
              <button
                v-if="!loading"
                class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                type="submit"
                v-html="buttonText"
              ></button>
              <button
                v-else
                class="px-4 py-2 bg-blue-600 text-white rounded opacity-50 cursor-not-allowed"
                type="submit"
                disabled
              >
                <i class="fa fa-sync fa-spin"></i>&nbsp;
                {{ formTranslation.common.loading }}
              </button>
              <button
                class="px-4 py-2 border border-blue-600 text-blue-600 rounded hover:bg-blue-50"
                @click="$emit('onBillCancel')"
              >
                {{ formTranslation.common.cancel }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { maxValue, minValue, required } from "vuelidate/lib/validators";
import { get, post } from "../../config/request";
import {
  minimumValue,
  validateForm,
  maximumDiscount,
  alphaSpace,
} from "../../config/helper";

export default {
  name: "BillFormModal",
  props: {
    userData: Object,
    patientBillData: Object,
    formTranslation: Object,
    clinic_currency_prefix: String,
    clinic_currency_postfix: String,
    buttonText: String,
    loading: Boolean,
    submitted: Boolean,
    showGenerateBillModal: {
      type: Boolean,
      required: true,
    },
    encounterId: {
      type: [String, Number],
      default() {
        return 0;
      },
    },
    doctorId: {
      type: [String, Number],
      default() {
        return 0;
      },
    },
    clinic_extra: {
      type: [Object, Array, String],
    },
    appointmentData: {},
    checkOutVal: {
      type: [String, Number],
      default() {
        return 0;
      },
    },
  },
  data: () => {
    return {
      woocommerceActive: "off",
      cardTitle: "Add bill",
      buttonText: "Save",
      encounter_id: 0,
      patientBillData: {
        billItems: [],
      },
      services: [],
      billItem: {},
      loading: false,
      submitted: false,
      billItemTitle: "Add Bill Item",
      billItemBtn: '<i class="fa fa-plus"></i> Save',
      billItemSubmitted: false,
      billItemEdit: false,
      visible: false,
      showEditForm: false,
      showAddForm: false,
      staticId: -1,
      status: [
        {
          id: "paid",
          label: "Paid",
        },
        {
          id: "unpaid",
          label: "Unpaid",
        },
      ],
      clinic_currency_prefix: "",
      clinic_currency_postfix: "",
      isLoading: true,
    };
  },
  validations: {
    patientBillData: {
      total_amount: { required },
      discount: {
        required,
        maximumDiscount,
        minValue: minValue(0),
      },
      actual_amount: { required },
    },
    billItem: {
      item_id: { required },
      qty: {
        required,
        minValue: minValue(1),
        maxValue: maxValue(10000),
      },
      price: {
        required,
        minValue: minValue(0),
        maxValue: maxValue(1000000000000000000),
      },
    },
  },
  mounted() {
    this.init();
    this.clinic_currency_prefix =
      this.clinic_extra.currency_prefix !== undefined &&
      this.clinic_extra.currency_prefix != null
        ? this.clinic_extra.currency_prefix
        : "";
    this.clinic_currency_postfix =
      this.clinic_extra.currency_postfix !== undefined &&
      this.clinic_extra.currency_postfix != null
        ? this.clinic_extra.currency_postfix
        : "";
  },
  methods: {
    init: function () {
      this.buttonText = this.formTranslation.common.save;
      this.billItemBtn =
        '<i class="fa fa-plus"></i> ' + this.formTranslation.common.save;
      this.patientBillData = this.defaultMedicalRecordData();
      this.billItem = this.defaultBillingItemData();
      //this.checkWoocommerceActive();

      if (this.encounterId !== 0) {
        this.patientBillData.encounter_id = this.encounterId;
        this.editBill(this.encounterId);
      }
      get("service_list", {
        module_type: "appointment_form",
        limit: 0,
        doctor_id: this.doctorId,
        without_currency: "yes",
        clinic_id: this.appointmentData.clinic_id,
      })
        .then((response) => {
          this.appointmentTypes = response.data.data;
          if (response.data.data.length > 0) {
            let data = response.data.data;
            data = data.map(function (val) {
              let newVal = Object.keys(val).reduce((acc, elem) => {
                if (elem === "name") {
                  acc["label"] = val[elem];
                } else if (elem === "service_id") {
                  acc["id"] = val[elem];
                } else if (elem === "charges") {
                  acc["price"] = val[elem];
                }
                return acc;
              }, {});
              return newVal;
            });
            this.services = data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    checkWoocommerceActive() {
      get("get_payment_status", { status: "" })
        .then((response) => {
          this.woocommerceActive = response.data.data;
        })
        .catch((error) => {
          console.log("error", error);
        });
    },
    handleBillModal: function () {
      this.showAddForm = true;
      this.visible = !this.visible;
      this.showEditForm = !this.showAddForm;
      this.billItemReset();
      if (this.visible === false) {
        this.showAddForm = false;
        this.visible = false;
      }
    },
    SendBillToPatient: function () {
      var element = $("#send_bill").find("i");
      element.removeClass("fa fa-paper-plane");
      element.addClass("fa fa-spinner fa-spin");
    },
    handleSubmit: function () {
      this.loading = true;

      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.patientBillData.$invalid) {
        this.loading = false;
        return;
      }

      if (
        this.patientBillData.billItems !== undefined &&
        !(this.patientBillData.billItems.length > 0)
      ) {
        this.loading = false;
        return;
      }
      if (this.appointmentData.appointment_id !== null) {
        this.patientBillData.appointment_id =
          this.appointmentData.appointment_id;
      }
      this.patientBillData.checkOutVal = this.checkOutVal;
      if (validateForm("patientBillDataForm")) {
        this.patientBillData.doctor_id = this.doctorId;
        post("patient_bill_save", this.patientBillData)
          .then((response) => {
            this.loading = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              this.$emit("onBillSaved", this.patientBillData);
              this.showEditForm = false;
              this.showAddForm = false;
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    addBillItem: function () {
      this.billItemSubmitted = true;
      this.$v.billItem.$touch();

      if (this.$v.billItem.$invalid) {
        return;
      }

      this.getTaxDetails();
    },
    afterTaxDetails: function () {
      if (!this.billItemEdit) {
        if (this.patientBillData.billItems === undefined) {
          this.patientBillData.billItems = [];
        }
        this.patientBillData.billItems.push(this.billItem);
      } else {
        this.updateBillTotal();
        this.billItemEdit = false;
      }

      this.billItemTitle = this.formTranslation.patient_bill.bill_add_item;
      this.billItemBtn =
        '<i class="fa fa-plus"></i> ' +
        this.formTranslation.patient_bill.bill_add_item;
      this.billItem = this.defaultBillingItemData();

      this.billItemSubmitted = false;
    },
    editBillItem: function (index) {
      this.staticId = index;
      this.showAddForm = false;
      this.showEditForm = true;
      this.visible = true;
      this.billItemTitle = this.formTranslation.common.edit_bill_items;
      this.billItemEdit = true;
      this.billItemBtn =
        '<i class="fa fa-save"></i>' + this.formTranslation.common.save_item;
      this.billItem = this.patientBillData.billItems[index - 1];
    },
    deleteBillItem: function (index) {
      if (this.patientBillData.billItems[index - 1] !== undefined) {
        let billItemData = this.patientBillData.billItems[index - 1];
        if (billItemData.id !== undefined) {
          $.confirm({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            content: this.formTranslation.common.press_yes_delete_billitems,
            type: "red",
            buttons: {
              ok: {
                text: this.formTranslation.common.yes,
                btnClass: "btn-danger",
                keys: ["enter"],
                action: () => {
                  post("patient_bill_item_delete", {
                    bill_item_id: billItemData.id,
                  })
                    .then((data) => {
                      if (
                        data.data.status !== undefined &&
                        data.data.status === true
                      ) {
                        this.patientBillData.billItems.splice(index - 1, 1);
                        displayMessage(data.data.message);
                        this.billItem = this.defaultBillingItemData();
                        this.getTaxDetails("delete");
                      }
                    })
                    .catch((error) => {
                      if (
                        error.response.data !== undefined &&
                        error.response.data.message !== undefined
                      ) {
                        displayErrorMessage(error.response.data.message);
                      } else {
                        displayErrorMessage(
                          this.formTranslation.common.internal_server_error
                        );
                      }
                    });
                },
              },
              cancel: {
                text: this.formTranslation.common.cancel,
              },
            },
          });
        } else {
          this.patientBillData.billItems.splice(index - 1, 1);
          this.billItem = this.defaultBillingItemData();
          this.getTaxDetails("delete");
        }
      }
    },
    defaultMedicalRecordData: function () {
      return {
        title: "",
        encounter_id: 0,
        appointment_id: 0,
        doctor_id: 0,
        total_amount: 0,
        discount: 0,
        actual_amount: "",
        status: 0,
        billItems: [],
        payment_status: "unpaid",
        taxes: [],
        tax_total: "",
      };
    },
    defaultBillingItemData: function () {
      return {
        item_id: "",
        qty: 1,
        price: 0,
        total: this.billItem.price * this.billItem.qty || 0,
      };
    },
    handleBillItemChange: function (selectedOption) {
      this.billItem.price = parseFloat(selectedOption.price);
      this.billItem.qty = 1;
    },
    handlePaymentChange: function (selectedOption) {
      if (selectedOption == "paid") {
        if (this.checkOutVal === "1") {
          this.buttonText = this.formTranslation.common.save_and_close_checkout;
        } else {
          this.buttonText =
            this.formTranslation.common.save_and_close_encounters;
        }
      } else {
        this.buttonText = this.formTranslation.common.save;
      }
    },
    removePaymentChage: function (selectedOption) {
      if (selectedOption.id == "paid") {
        this.buttonText = this.formTranslation.common.save;
      }
    },
    handleBillItemUnselect: function () {
      this.billItem.price = 0;
      this.billItem.qty = 1;
    },
    addServiceTag: function (newTag) {
      const tag = {
        id: newTag,
        label: newTag,
        price: 0,
      };
      this.billItem.item_id = tag;
      this.billItem.price = 0;
      this.billItem.qty = 1;
      this.billItem.total = 0;
      this.services.push(tag);
    },
    calculateGrandTotal: function () {
      this.patientBillData.total_amount = this.patientBillData.billItem.each(
        (item) => {}
      );
    },
    editBill: function (encounterId) {
      if (parseInt(encounterId) !== 0) {
        get("patient_bill_detail", {
          encounter_id: encounterId,
        })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.isLoading = false;
              if (response.data.status) {
                if (
                  response.data.data.id !== undefined &&
                  response.data.data.id !== null &&
                  response.data.data.id !== ""
                ) {
                  this.patientBillData = response.data.data;
                }
              }
            }
          })
          .catch((error) => {
            this.isLoading = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      }
    },
    getTaxDetails: function (type) {
      if (this.userData.addOns.kiviPro === false) {
        this.afterTaxDetails();
        return;
      }
      // this.patientBillData.taxes = [];
      let data = JSON.stringify(this.patientBillData);
      data = JSON.parse(data);
      if (!this.billItemEdit && type !== "delete") {
        if (data.billItems === undefined) {
          data.billItems = [];
        }
        data.billItems.push(this.billItem);
      }
      this.patientBillData.tax_total = "";
      post("tax_calculated_encounter_data", data)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.patientBillData.taxes = response.data.data;
            this.patientBillData.tax_total = response.data.tax_total;
          } else {
            this.patientBillData.taxes = [];
            this.patientBillData.tax_total = 0;
          }
          if (type !== "delete") {
            this.afterTaxDetails();
          }
        })
        .catch((error) => {
          if (type !== "delete") {
            this.afterTaxDetails();
          }
          console.log(error);
          displayErrorMessage("Internal server error");
        });
    },
    handlePaymentChange(value) {
      this.$emit('payment-status-change', value)
    },

    updateBillTotal: function () {
      var sum = 0;
      if (
        this.patientBillData.billItems !== undefined &&
        this.patientBillData.billItems.length > 0
      ) {
        this.patientBillData.billItems.forEach((e) => {
          sum += parseFloat(e.price) * parseFloat(e.qty);
        });
      }
      if (this.patientBillData.tax_total) {
        sum += this.patientBillData.tax_total;
      }
      this.patientBillData.total_amount = sum;
    },
    billItemReset: function () {
      this.billItem = this.defaultBillingItemData();
    },
    cancelBillItem: function () {
      this.showAddForm = false;
      this.visible = !this.visible;
      this.billItem = this.defaultBillingItemData();
    },

    closeModal() {
      this.$emit("update:showGenerateBillModal", false);
    },
    paymentLink() {
      if (
        this.patientBillData.billItems.length === 0 &&
        this.woocommerceActive === "off"
      ) {
        displayErrorMessage(
          this.formTranslation.patient_bill.payment_or_bill_item_error
        );
        return;
      }
      post("send_payment_link", { id: this.encounterId })
        .then((response) => {
          displayMessage(response.data.message);
        })
        .catch((error) => {
          console.log("error", error);
        });
    },
  },
  watch: {
    "patientBillData.billItems": function () {
      this.updateBillTotal();
    },
    "patientBillData.total_amount": function () {
      this.patientBillData.actual_amount =
        this.patientBillData.total_amount - this.patientBillData.discount;
    },
    "billItem.price": function () {
      this.billItem.qty = this.billItem.qty == "" ? 0 : this.billItem.qty;
      this.billItem.price = this.billItem.price == "" ? 0 : this.billItem.price;
      this.billItem.total = isNaN(
        parseFloat(this.billItem.price) * parseFloat(this.billItem.qty)
      )
        ? 0
        : parseFloat(this.billItem.price) * parseFloat(this.billItem.qty);
    },
    "billItem.qty": function () {
      this.billItem.qty = this.billItem.qty == "" ? 0 : this.billItem.qty;
      this.billItem.price = this.billItem.price == "" ? 0 : this.billItem.price;
      this.billItem.total = isNaN(
        parseFloat(this.billItem.price) * parseFloat(this.billItem.qty)
      )
        ? 0
        : parseFloat(this.billItem.price) * parseFloat(this.billItem.qty);
    },
    "patientBillData.discount": function () {
      this.patientBillData.actual_amount =
        this.patientBillData.total_amount - this.patientBillData.discount;
    },
    encounterId(newVal) {
      this.patientBillData.encounter_id = newVal;
    },
  },
  computed: {
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
  },
};
</script>
