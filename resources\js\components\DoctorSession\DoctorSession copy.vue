<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.doctor_session.doc_sessions }}
        </h1>
      </div>
      <button
        v-if="kcCheckPermission('doctor_session_add') && !isCloseBtnShow"
        @click="handleCollapseChange('add')"
        class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
      >
        <span>{{ formTranslation.doctor_session.add_session_btn }}</span>
      </button>
    </div>

    <!-- Add/Edit Form Section -->
    <div
      v-if="isCollapseVisible"
      class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
    >
      <form id="clinicDataForm" @submit.prevent="handleSubmit">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Clinic Selection -->
          <div
            v-if="
              userData.addOns.kiviPro === true &&
              (getUserRole() === 'administrator' || getUserRole() == 'doctor')
            "
          >
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ formTranslation.clinic.select_clinic }}
              <span class="text-red-500">*</span>
            </label>
            <select
              id="clinic_id"
              v-model="clinicSession.clinic_id"
              @change="clinicChange"
              :disabled="clinicMultiselectLoader"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent"
            >
              <option value="">
                {{ formTranslation.doctor_session.plh_search }}
              </option>
              <option
                v-for="clinic in allClinics"
                :key="clinic.id"
                :value="clinic.id"
              >
                {{ clinic.label }}
              </option>
            </select>
            <div
              v-if="submitted && !$v.clinicSession.clinic_id.required"
              class="text-red-500 text-sm mt-1"
            >
              {{ formTranslation.common.clinic_is_required }}
            </div>
          </div>

          <!-- Doctor Selection -->
          <div v-if="getUserRole() !== 'doctor'">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ formTranslation.common.doctors }}
              <span class="text-red-500">*</span>
            </label>
            <select
              id="clinicSessionDoctor"
              v-model="clinicSession.doctors"
              @change="sessionDoctorsValidation"
              :disabled="doctorMultiselectLoader"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent"
            >
              <option value="">
                {{ formTranslation.doctor_session.plh_search }}
              </option>
              <option
                v-for="doctor in doctors"
                :key="doctor.id"
                :value="doctor"
              >
                {{ doctor.label }}
              </option>
            </select>
            <div
              v-if="submitted && !$v.clinicSession.doctors.required"
              class="text-red-500 text-sm mt-1"
            >
              {{ formTranslation.appointments.doc_required }}
            </div>
          </div>

          <!-- Time Slot Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ formTranslation.setup_wizard.time_slot_minute }}
              <span class="text-red-500">*</span>
            </label>
            <select
              v-model="clinicSession.buffertime"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent"
            >
              <option
                v-for="(slot, index) in time_slots"
                :key="index"
                :value="slot"
              >
                {{ slot }}
              </option>
            </select>
            <div
              v-if="submitted && !$v.clinicSession.buffertime.required"
              class="text-red-500 text-sm mt-1"
            >
              {{ formTranslation.appointments.time_slot_required }}
            </div>
          </div>
        </div>

        <!-- Weekly Schedule Table -->
        <div class="mt-8">
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr>
                  <th
                    v-for="(day, index) in clinicSession.days"
                    :key="index"
                    class="border p-4 bg-gray-50"
                  >
                    <div class="flex items-center justify-between">
                      <span class="font-medium text-gray-700">{{
                        day.label
                      }}</span>
                      <div class="flex gap-2">
                        <button
                          v-if="index !== 0"
                          type="button"
                          @click="copyFromPreviousDay(index)"
                          class="p-1 hover:bg-gray-100 rounded text-gray-600"
                        >
                          <i class="far fa-copy"></i>
                        </button>
                        <button
                          type="button"
                          @click="addSlot(index)"
                          class="p-1 hover:bg-gray-100 rounded text-gray-600"
                        >
                          <i class="fas fa-plus"></i>
                        </button>
                      </div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td
                    v-for="(day, dayIndex) in clinicSession.days"
                    :key="dayIndex"
                    class="border p-4"
                  >
                    <div class="flex flex-col gap-4">
                      <div
                        v-for="(slot, slotIndex) in day.slots"
                        :key="slotIndex"
                        class="flex items-center gap-4"
                      >
                        <vue-timepicker
                          v-model="slot.start"
                          :minute-interval="5"
                          format="HH:mm"
                          class="flex-1"
                          @change="validateTime(dayIndex)"
                        />
                        <span class="text-gray-500">to</span>
                        <vue-timepicker
                          v-model="slot.end"
                          :minute-interval="5"
                          format="HH:mm"
                          class="flex-1"
                          @change="validateTime(dayIndex)"
                        />
                        <button
                          type="button"
                          @click="removeSlot(dayIndex, slotIndex)"
                          class="p-1 hover:bg-gray-100 rounded text-red-500"
                        >
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                      <span class="text-red-500 text-sm">{{ day.error }}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="mt-6 flex justify-end gap-4">
          <button
            type="button"
            @click="resetSessionFormData"
            class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            {{ formTranslation.common.cancel }}
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
          >
            <template v-if="loading">
              <i class="fa fa-sync fa-spin mr-2"></i>
              {{ formTranslation.common.loading }}
            </template>
            <template v-else>
              <i class="fa fa-save mr-2"></i>
              {{ formTranslation.doctor_session.save_btn }}
            </template>
          </button>
        </div>
      </form>
    </div>

    <!-- Sessions List Table -->
    <div
      class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
    >
      <!-- Search Bar -->
      <div class="p-4 border-b border-gray-200">
        <div class="relative">
          <i
            class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          ></i>
          <input
            type="text"
            v-model="searchQuery"
            class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent"
            :placeholder="formTranslation.datatable.search_placeholder"
          />
        </div>
      </div>

      <!-- Table -->
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th
              v-for="col in column"
              :key="col.field"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              {{ col.label }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr
            v-for="(session, index) in filteredSessions"
            :key="index"
            class="hover:bg-gray-50"
          >
            <td
              v-for="col in column"
              :key="col.field"
              class="px-6 py-4 whitespace-nowrap"
            >
              <template v-if="col.field === 'action'">
                <div class="flex gap-2">
                  <button
                    v-if="kcCheckPermission('doctor_session_edit')"
                    @click="editSessionData({ row: session, index })"
                    class="p-1 hover:bg-gray-100 rounded"
                  >
                    <i class="fa fa-pen-alt text-gray-600"></i>
                  </button>
                  <button
                    v-if="kcCheckPermission('doctor_session_delete')"
                    @click="deleteSessionData({ row: session, index })"
                    class="p-1 hover:bg-gray-100 rounded"
                  >
                    <i class="fa fa-trash text-red-500"></i>
                  </button>
                </div>
              </template>
              <template v-else-if="col.field === 'index'">
                {{ (currentPage - 1) * perPage + index + 1 }}
              </template>
              <template v-else-if="col.field === 'days'">
                {{ tableDaysTranslation(session.days) }}
              </template>
              <template v-else>
                {{ session[col.field] }}
              </template>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div
        class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200"
      >
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select
            v-model="perPage"
            class="border border-gray-300 rounded-md text-sm p-1"
          >
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <div class="flex gap-2">
            <button
              @click="previousPage"
              :disabled="currentPage === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            <button
              @click="nextPage"
              :disabled="currentPage === totalPages"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  maxLength,
  minLength,
  required,
  requiredIf,
} from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import {
  alphaSpace,
  maxTime,
  minTime,
  objToTime,
  phoneNumber,
  postalCode,
  validateForm,
  validateTimeSlot,
  emailValidate,
} from "../../config/helper";

export default {
  name: "DoctorSession",
  data: () => {
    return {
      visible: false,
      isEdit: false,
      isAdd: true,
      isCloseBtnShow: false,
      toggleBtnHtml: "",
      activeClinicId: 0,
      cardTitle: "Edit clinic profile",
      clinicSessionTitle: "Add session",
      pageLoader: true,
      clinicData: {},
      specialization: [],
      doctors: [],
      weekDays: {},
      loading: false,
      submitted: false,
      editProfileText: '<i class="fa fa-pen-fancy"></i> Edit Profile',
      buttonText: '<i class="fa fa-plus"></i> Add',
      sessionButtonText: '<i class="fa fa-save"></i> Save session',
      sessionSubmitted: false,
      sessionEdit: false,
      days: ["mon", "tue", "wed", "thu", "fri", "sat", "sun"],
      clinicSession: {},
      s_two_end_time_required_validation: false,
      weekDaysValidationCheck: false,
      sessionDoctorValidationCheck: false,
      editSessionDataIndex: "",
      timeSlots: [],
      inValidTime: false,
      profileImage: "",
      time_slots: [],
      daysAll: 0,
      countryList: [],
      clinics: [],
      column: [],
      isCollapseVisible: false,
      doctor_name: "",
      timezone_status: true,
      timezone_msg: "",
      doctorMultiselectLoader: true,
      clinicMultiselectLoader: true,
      firstDisabledHours: [],
      secondDisabledHours: [[0, 23]],
      thirdDisabledHours: [[0, 23]],
      fourthDisabledHours: [[0, 23]],
      dropDownWeekDays: [],
      isClinicSessionDaysValid: true,
      searchQuery: "",
      currentPage: 1,
      perPage: 10,
      totalPages: 1,
    };
  },
  validations: {
    clinicData: {
      name: {
        required,
        alphaSpace,
        minLength: minLength(2),
        maxLength: maxLength(35),
      },
      email: {
        required,
        emailValidate,
      },
      telephone_no: {
        required,
        phoneNumber,
        minLength: minLength(6),
        maxLength: maxLength(15),
      },
      address: {
        required,
      },
      state: {
        required,
        alphaSpace,
        maxLength: maxLength(30),
      },
      city: {
        required,
        alphaSpace,
        maxLength: maxLength(30),
      },
      country: {
        required,
        alphaSpace,
        maxLength: maxLength(30),
      },
      specialties: {
        required,
      },
      status: { required },
    },
    clinicSession: {
      clinic_id: {
        required: requiredIf(function () {
          return (
            this.userData.addOns.kiviPro == true &&
            (this.getUserRole() === "administrator" ||
              this.getUserRole() == "doctor")
          );
        }),
      },
      doctors: {
        required: requiredIf(function () {
          return this.getUserRole() != "doctor";
        }),
      },
      buffertime: { required },
    },
  },
  mounted() {
    if (["patient"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.days.map((item) => {
      this.weekDays[item] = this.formTranslation.days[item];
      this.dropDownWeekDays.push({ value: item, text: this.weekDays[item] });
      return this.weekDays;
    });
    this.getTimezoneSetting();
    this.toggleBtnHtml =
      '<i class="fa fa-plus"></i>' +
      this.formTranslation.doctor_session.add_session_btn;
    this.column = [
      {
        field: "index",
        label: this.formTranslation.doctor_session.dt_lbl_sr,
        width: "50px",
      },
      {
        label: this.formTranslation.doctor_session.dt_lbl_doc,
        field: "doctor_name",
        width: "150px",
        filterOptions: {
          enabled: !(this.getUserRole() === "doctor"),
          placeholder: this.formTranslation.doctor_session.dt_plh_fltr_by_doc,
          filterValue: "",
        },
      },
      {
        field: "clinic_name",
        label: this.formTranslation.doctor_session.dt_lbl_clinic,
        width: "150px",
        filterOptions: {
          enabled: !(
            window.request_data.current_user_role === "kiviCare_clinic_admin" ||
            window.request_data.current_user_role === "kiviCare_receptionist"
          ),
          filterValue: "",
        },
      },
      {
        label: this.formTranslation.doctor_session.dt_lbl_days,
        field: "days",
        width: "200px",
        sortable: false,
        filterOptions: {
          enabled: true,
          filterValue: "",
          filterDropdownItems: this.dropDownWeekDays,
          filterFn: function (data, filterString) {
            return data.some(
              (obj) => obj.name == filterString && obj.slots.length != 0
            );
          },
        },
      },
      {
        label: this.formTranslation.doctor_session.dt_lblaction,
        field: "action",
        width: "50px",
        sortable: false,
        html: true,
      },
    ];
    this.clinicData = this.defaultClinicData();
    this.clinicSession = this.defaultClinicSessionData();
    this.init();
    this.profileImage =
      window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png";
    if (this.$route.params.id !== undefined) {
      let doctor_id = this.$route.params.id;
      this.toggleBtnHtml =
        '<i class="fa fa-minus"></i>' +
        this.formTranslation.doctor_session.close_form_btn;
      this.clinicSession = this.defaultClinicSessionData();
      this.isCloseBtnShow = true;
      this.isCollapseVisible = true;
      this.getDoctorsData();
      setTimeout(() => {
        this.clinicSession.doctors = this.doctors.find(
          (doctor) => doctor.id == doctor_id
        );
        this.getUserClinic(doctor_id);
      }, 500);
    } else {
      this.getUserClinic(1);
    }
  },
  filters: {
    doctorFilter: function (Doctors) {
      let doctors = Doctors;
      let result = [];
      if (doctors !== undefined && doctors.length > 0) {
        doctors.forEach(function (doctor) {
          result.push(doctor.label);
        });
        return result.join(",");
      } else {
        return "No Doctor Found";
      }
    },
    clinicSpecialityFormat: function (Speciality) {
      let doctors = Speciality;
      let result = [];
      if (doctors !== undefined && doctors.length > 0) {
        doctors.forEach(function (doctor) {
          result.push(doctor.label);
        });
        return result.join(" ,");
      } else {
        return this.formTranslation.doctor_session.no_speciality_found;
      }
    },
  },
  methods: {
    init: function () {
      // this.getCountryList();
      this.getClinicSessionsList();
      if (
        this.$store.state.userDataModule.user !== undefined &&
        this.$store.state.userDataModule.user.addOns.kiviPro != true
      ) {
        this.activeClinicId =
          this.$store.state.userDataModule.user.default_clinic_id;
      }
      this.getDoctorsData();
      this.getTimeSlots();
    },
    addSlot(dayIndex) {
      this.clinicSession.days[dayIndex].slots.push({ start: "", end: "" });
    },
    removeSlot(dayIndex, slotIndex) {
      this.clinicSession.days[dayIndex].slots.splice(slotIndex, 1);
      this.validateTime(dayIndex);
    },
    saveSchedule() {
      console.log("Saved Schedule:", this.clinicSession.days);
      alert("Schedule saved successfully!");
    },
    copyFromPreviousDay(dayIndex) {
      const previousDaySlots = this.clinicSession.days[dayIndex - 1].slots;
      this.clinicSession.days[dayIndex].slots = previousDaySlots.map(
        (slot) => ({
          start: slot.start,
          end: slot.end,
        })
      );
      this.validateTime(dayIndex);
    },
    clearSchedule() {
      this.clinicSession.days.forEach((day) => (day.slots = []));
    },
    validateTime(dayIndex) {
      const slots = this.clinicSession.days[dayIndex].slots;
      let hasOverlap = false;

      // Reset errors
      this.clinicSession.days[dayIndex].error = null;

      // Check for overlap
      for (let i = 0; i < slots.length; i++) {
        const current = slots[i];
        if (!current.start || !current.end) {
          this.clinicSession.days[dayIndex].error =
            this.formTranslation.common.emptyTimeSlots;
          return;
        }
        if (current.start >= current.end) {
          this.clinicSession.days[dayIndex].error =
            this.formTranslation.common.invalidTimeRange;
          return;
        }
        for (let j = i + 1; j < slots.length; j++) {
          const other = slots[j];
          if (
            (current.start < other.end && current.end > other.start) ||
            (other.start < current.end && other.end > current.start)
          ) {
            hasOverlap = true;
          }
        }
      }

      if (hasOverlap) {
        this.clinicSession.days[dayIndex].error =
          this.formTranslation.common.overlappingSlots;
      }
    },
    handleSubmit: function () {
      this.loading = true;

      this.submitted = true;
      let isValid = true;

      // stop here if form is invalid
      this.$v.$touch();

      this.$nextTick(() => {
        if (
          document.querySelector(".is-invalid") !== null &&
          document.querySelector(".is-invalid") !== undefined
        ) {
          document
            .querySelector(".is-invalid")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        } else if (
          document.querySelector(".invalid-feedback") !== null &&
          document.querySelector(".invalid-feedback") !== undefined
        ) {
          document
            .querySelector(".invalid-feedback")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        }
      });

      if (this.$v.clinicSession.$invalid) {
        this.loading = false;
        return;
      }

      this.days.forEach((day, index) => {
        this.validateTime(index);
        if (day.error) isValid = false;
      });

      if (this.$v.clinicSession.$invalid) {
        isValid = false;
      }

      if (!isValid) {
        if (this.sessionDoctorValidationCheck && this.sessionSubmitted) {
          displayErrorMessage(
            this.formTranslation.doctor_session.doc_already_added
          );
        }

        this.loading = false;
        this.submitted = false;
        return true;
      }

      if (validateForm("clinicDataForm")) {
        post("clinic_session_save", this.clinicSession)
          .then((response) => {
            if (this.getUserRole() === "administrator") {
              this.$store.dispatch("userDataModule/fetchUserData", {});
            }
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              if (
                this.clinicSession.id !== undefined &&
                this.clinicSession.id !== null &&
                this.clinicSession.id !== ""
              ) {
                this.clinicData.clinic_sessions[this.editSessionDataIndex] =
                  this.clinicSession;
              } else {
                this.clinicData.clinic_sessions.push(this.clinicSession);
              }
              displayMessage(response.data.message);
              location.reload();
            } else {
              this.loading = false;
              if (
                response.data.status !== undefined &&
                response.data.status === false &&
                response.data.message !== undefined
              ) {
                displayErrorMessage(response.data.message);
              } else {
                displayErrorMessage(
                  this.formTranslation.doctor_session
                    .doctor_session_not_saved_successfully
                );
              }
            }
            this.loading = false;
            this.submitted = false;
            this.sessionSubmitted = false;
            this.weekDaysValidationCheck = false;
            this.sessionDoctorValidationCheck = false;
            this.s_two_end_time_required_validation = false;
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            this.submitted = false;
            this.sessionSubmitted = false;
            this.weekDaysValidationCheck = false;
            this.sessionDoctorValidationCheck = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    defaultClinicData: function () {
      return {
        clinic_sessions: [],
      };
    },
    defaultClinicSessionData: function () {
      return {
        doctors: {},
        buffertime: 0,

        days: [
          { name: "mon", label: this.formTranslation.days["mon"], slots: [] },
          { name: "tue", label: this.formTranslation.days["tue"], slots: [] },
          { name: "wed", label: this.formTranslation.days["wed"], slots: [] },
          { name: "thu", label: this.formTranslation.days["thu"], slots: [] },
          { name: "fri", label: this.formTranslation.days["fri"], slots: [] },
          { name: "sat", label: this.formTranslation.days["sat"], slots: [] },
          { name: "sun", label: this.formTranslation.days["sun"], slots: [] },
        ],
      };
    },
    getUserClinic(doctor_id) {
      get("clinic_doctor_wise_list", {
        data_type: "doctor",
        doctor_id: doctor_id,
      })
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.clinics = data.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getDoctorsData: function () {
      this.doctorMultiselectLoader = true;
      get("get_static_data", {
        data_type: "clinic_doctors",
        clinic_id: this.activeClinicId,
      })
        .then((data) => {
          this.doctorMultiselectLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            this.doctors = data.data.data;
          }
        })
        .catch((error) => {
          this.doctorMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    editSessionData: function (data) {
      // Set edit mode flags
      this.clinicSessionTitle =
        this.formTranslation.doctor_session.edit_session;
      this.sessionEdit = true;
      this.isEdit = true;
      this.isAdd = false;
      this.isCollapseVisible = true;
      this.isCloseBtnShow = true;

      // Store the index for updating later
      this.editSessionDataIndex = data.index;

      // Create a deep copy of the session data
      this.clinicSession = {
        id: data.row.id,
        clinic_id: data.row.clinic_id,
        doctors:
          this.doctors.find((doctor) => doctor.id === data.row.doctor_id) || {},
        buffertime: data.row.buffertime || 0,
        days: data.row.days.map((day) => ({
          name: day.name,
          label: this.formTranslation.days[day.name],
          slots: day.slots.map((slot) => ({
            start: slot.start,
            end: slot.end,
          })),
          error: null,
        })),
      };

      // Update UI
      this.toggleBtnHtml =
        '<i class="fa fa-minus"></i>' +
        this.formTranslation.doctor_session.close_form_btn;

      // Scroll to top
      window.scroll({ top: 0, behavior: "smooth" });

      // If using KiviPro, fetch clinic data
      if (this.userData.addOns.kiviPro) {
        this.getUserClinic(data.row.doctor_id);
      }
    },
    deleteSessionData: function (data) {
      if (this.clinicData.clinic_sessions[data.index] !== undefined) {
        $this.$swal.fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.reset_appointment_slot,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              post("clinic_session_delete", { session_id: data.row.id })
                .then((response) => {
                  if (this.getUserRole() === "administrator") {
                    this.$store.dispatch("userDataModule/fetchUserData", {});
                  }

                  if (
                    response.data.status !== undefined &&
                    response.data.status === true
                  ) {
                    if (
                      this.clinicData.clinic_sessions[data.index] !== undefined
                    ) {
                      this.clinicData.clinic_sessions.splice(data.index, 1);
                      $this.$swal.fire({
                        title: "Success!",
                        text: response.data.message,
                        icon: "success",
                        confirmButtonColor: "#28a745", // Bootstrap success color
                      });
                    }
                  } else {
                    $this.$swal.fire({
                      title: "Error!",
                      text: response.data.message,
                      icon: "error",
                      confirmButtonColor: "#dc3545",
                    });
                  }
                })
                .catch((error) => {
                  console.log(error);
                  $this.$swal.fire({
                    title: "Error!",
                    text: this.formTranslation.common.internal_server_error,
                    icon: "error",
                    confirmButtonColor: "#dc3545",
                  });
                });
            }
          });
      } else {
        $this.$swal.fire({
          title: "Error!",
          text: this.formTranslation.doctor_session
            .clinic_profile_data_not_found,
          icon: "error",
          confirmButtonColor: "#dc3545",
        });
      }
    },
    resetSessionFormData: function () {
      this.submitted = false;
      this.loading = false;
      this.isCollapseVisible = false;
      this.toggleBtnHtml =
        '<i class="fa fa-plus"></i>' +
        this.formTranslation.doctor_session.add_session_btn;
      this.sessionButtonText = '<i class="fa fa-save"></i> Add session';
      this.clinicSession = this.defaultClinicSessionData();
      this.daysAll = 0;
      this.isCloseBtnShow = false;
      this.firstDisabledHours = [[0, 23]];
      this.secondDisabledHours = [[0, 23]];
      this.thirdDisabledHours = [[0, 23]];
      this.fourthDisabledHours = [[0, 23]];
    },
    sessionDoctorsValidation: function (selectId) {
      this.sessionDoctorValidationCheck = false;
      this.getUserClinic(selectId.id);
      this.timeSlots = [];
    },
    sessionDaysValidation: function () {
      this.weekDaysValidationCheck = false;
      for (let index = 0; index < this.clinicSession.days.length; index++) {
        for (let i = 0; i < this.clinicData.clinic_sessions.length; i++) {
          if (this.editSessionDataIndex !== "") {
            if (i != this.editSessionDataIndex) {
              if (
                this.clinicData.clinic_sessions[i].days.includes(
                  this.clinicSession.days[index]
                )
              ) {
                this.weekDaysValidationCheck = true;
                break;
              }
            }
          } else {
            if (
              this.clinicData.clinic_sessions[i].days.includes(
                this.clinicSession.days[index]
              )
            ) {
              this.weekDaysValidationCheck = true;
              break;
            }
          }
          if (this.weekDaysValidationCheck) {
            break;
          }
        }
      }
    },
    getTimeSlots: function () {
      let slot = 0;
      for (let i = 0; i < 12; i++) {
        if (slot <= 60) {
          this.time_slots.push(slot);
        }
        slot = slot + 5;
      }
    },
    getTimeSlot: function (startTime, endTime, doctor) {
      // console.log(this.clinicSession);

      var timeSlotDiff = 5;
      s;

      var newTimeSlot = "";
      let slots = [];

      if (
        startTime.HH !== "" &&
        startTime.mm !== "" &&
        endTime.HH !== "" &&
        endTime.mm !== "" &&
        timeSlotDiff !== ""
      ) {
        let sessionOneStartTime = objToTime(startTime);
        let sessionOneEndTime = objToTime(endTime);

        let timeDiff = sessionOneEndTime.diff(sessionOneStartTime, "minutes");

        let loopCount = Math.ceil(timeDiff / timeSlotDiff);

        for (let i = 0; i < loopCount; i++) {
          if (i === 0) {
            newTimeSlot = sessionOneStartTime.format("HH:mm");
          } else {
            newTimeSlot = moment(newTimeSlot, "HH:mm")
              .add(timeSlotDiff, "minutes")
              .format("HH:mm");
          }

          let temp = {
            time: newTimeSlot,
            isValid: true,
            timeSlotDiff: timeSlotDiff,
          };

          if (moment(newTimeSlot, "HH:mm").isAfter(sessionOneEndTime)) {
            let timeDiff = moment(newTimeSlot, "HH:mm").diff(
              sessionOneEndTime,
              "minutes"
            );
            temp.isValid = false;
            temp.timeSlotDiff = Math.abs(timeSlotDiff - timeDiff);
          }
          slots.push(temp);
        }
      }

      return slots;
    },
    generateTimeSlotTime: function (type) {
      switch (type) {
        case "first":
          if (
            this.clinicSession.s_one_start_time.HH !== undefined &&
            this.clinicSession.s_one_start_time.HH != ""
          ) {
            this.secondDisabledHours = [
              [parseInt(this.clinicSession.s_one_start_time.HH) + 1, 23],
            ];
          }
          break;
        case "second":
          if (
            this.clinicSession.s_one_end_time.HH !== undefined &&
            this.clinicSession.s_one_end_time.HH != ""
          ) {
            this.thirdDisabledHours = [
              [parseInt(this.clinicSession.s_one_end_time.HH) + 1, 23],
            ];
          }
          break;
        case "third":
          if (
            this.clinicSession.s_two_start_time.HH !== undefined &&
            this.clinicSession.s_two_start_time.HH != ""
          ) {
            this.fourthDisabledHours = [
              [parseInt(this.clinicSession.s_two_start_time.HH) + 1, 23],
            ];
          }
          break;
        case "fourth":
          break;
      }
      this.timeSlots = [];
      let time = [
        {
          startTime: this.clinicSession.s_one_start_time,
          endTime: this.clinicSession.s_one_end_time,
        },
        {
          startTime: this.clinicSession.s_two_start_time,
          endTime: this.clinicSession.s_two_end_time,
        },
      ];
      for (let i = 0; i < time.length; i++) {
        let slots = this.getTimeSlot(
          time[i].startTime,
          time[i].endTime,
          this.clinicSession.doctors
        );
        if (slots.length > 0) {
          this.timeSlots[i] = slots;
        }
      }
    },
    getClinicSessionsList: function () {
      this.pageLoader = true;
      this.buttonText =
        '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
      get("clinic_session_list", {})
        .then((response) => {
          this.pageLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            // console.log(response.data.data);

            this.clinicData = response.data.data;
          } else {
            this.clinicData = {
              clinic_sessions: [],
            };
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          console.log(error);
        });
    },
    handleUncheckDays: function () {
      if (
        this.clinicSession.days !== undefined &&
        this.clinicSession.days.length === 7
      ) {
        this.daysAll = true;
      } else {
        this.daysAll = false;
      }
    },
    handleAllDaysChecked: function () {
      if (this.daysAll) {
        this.clinicSession.days = this.days;
      } else {
        this.clinicSession.days = [];
      }
    },
    getCountryList: function () {
      get("get_country_list", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.countryList = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    onCollapseAction: function () {},
    clinicChange(event) {
      this.doctorMultiselectLoader = true;
      const clinicId = event.target.value;

      get("get_static_data", {
        data_type: "get_users_by_clinic",
        clinic_id: clinicId,
      })
        .then((response) => {
          this.doctorMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.doctors = response.data.data;
            this.clinicSession.doctors = [];
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.doctorMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    tableDaysTranslation(days) {
      let tempDays = [];
      days.map((item) => {
        if (item.slots.length) {
          tempDays.push(this.formTranslation.days[item.name]);
        }
        return tempDays;
      });
      return tempDays.join(", ");
    },
    handleCollapseChange: function (toggleForm) {
      if (toggleForm === "add") {
        this.toggleBtnHtml =
          '<i class="fa fa-minus"></i>' +
          this.formTranslation.doctor_session.close_form_btn;
        this.clinicSession = this.defaultClinicSessionData();
        this.isCloseBtnShow = true;
        this.isCollapseVisible = true;
      } else if (toggleForm === "edit") {
        this.toggleBtnHtml =
          '<i class="fa fa-minus"></i>' +
          this.formTranslation.doctor_session.close_form_btn;
        this.isCloseBtnShow = true;
        this.isCollapseVisible = true;
      } else {
        // close form
        this.resetSessionFormData();
      }
    },
    iUnderstandTimezone: function () {
      post("save_time_zone_option", { time_status: 1 })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.timezone_status = response.data.data;
            location.reload();
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getTimezoneSetting: function () {
      this.timezone_status = window.request_data.time_zone_data.data;
      this.timezone_msg = window.request_data.time_zone_data.message;
    },
    previousPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    },
  },
  computed: {
    filteredSessions() {
      let filtered = this.clinicData.clinic_sessions ?? [];

      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter((session) => {
          return (
            session.doctor_name.toLowerCase().includes(query) ||
            session.clinic_name.toLowerCase().includes(query) ||
            session.id.toString().includes(query)
          );
        });
      }

      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;

      return filtered.slice(start, end);
    },
    doctorSessionExport() {
      return "Doctor Session List - " + moment().format("YYYY-MM-DD");
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    allClinics() {
      this.clinicMultiselectLoader = false;
      if (this.$store.state.clinic.length > 0) {
        return this.$store.state.clinic;
      } else {
        return [];
      }
    },
  },
  watch: {},
};
</script>
<style>
#clinicSessionPrint .vgt-table thead th {
  vertical-align: middle;
}

#s_one_start_time {
  height: 3em;
}

#s_one_end_time {
  height: 3em;
}

#s_two_start_time {
  height: 3em;
}

#s_two_end_time {
  height: 3em;
}

@media (max-width: 576px) {
  #clinicSessionPrint .vgt-compact td:before {
    width: 55%;
    padding-left: 0;
  }
}
</style>
