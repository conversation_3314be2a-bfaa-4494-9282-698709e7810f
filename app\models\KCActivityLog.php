<?php

namespace App\models;

use App\baseClasses\KCBase;
use App\baseClasses\KCModel;

class KCActivityLog extends KCModel {

    public $kcbase;
    
    public function __construct() {
        $this->kcbase = (new KCBase());
        parent::__construct('activity_logs');
    }

    /**
     * Log an activity
     * 
     * @param int $user_id User ID
     * @param string $activity_type Type of activity (e.g., login, appointment_created)
     * @param string $activity_description Human-readable description of the activity
     * @param array $additional_data Additional data for the activity
     * 
     * @return int|bool Activity log ID or false on failure
     */
    public function logActivity($user_id, $activity_type, $activity_description, $additional_data = []) {
        
        $user_type = $this->kcbase->getUserRoleById($user_id);
        
        $log_data = [
            'user_id' => $user_id,
            'user_type' => $user_type,
            'activity_type' => $activity_type,
            'activity_description' => $activity_description,
            'ip_address' => $this->getIpAddress(),
        ];

        // Add clinic_id if applicable
        if (!empty($additional_data['clinic_id'])) {
            $log_data['clinic_id'] = $additional_data['clinic_id'];
        }

        // Add patient_id if applicable
        if (!empty($additional_data['patient_id'])) {
            $log_data['patient_id'] = $additional_data['patient_id'];
        }

        // Add resource info if applicable
        if (!empty($additional_data['resource_id'])) {
            $log_data['resource_id'] = $additional_data['resource_id'];
        }

        if (!empty($additional_data['resource_type'])) {
            $log_data['resource_type'] = $additional_data['resource_type'];
        }

        return $this->insert($log_data);
    }

    /**
     * Get the user's IP address
     * 
     * @return string IP address
     */
    private function getIpAddress() {
        $ip = '';
        
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        return $ip;
    }

    /**
     * Get logs based on user permissions
     * 
     * @param array $filters Filter parameters
     * @param int $page Page number
     * @param int $per_page Items per page
     * 
     * @return array Activity logs
     */
    public function getLogsByUserPermission($filters = [], $page = 1, $per_page = 10) {
        global $wpdb;
        
        // Check if activity logs table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->tableName}'") === $this->tableName;
        if (!$table_exists) {
            // Return empty result if table doesn't exist
            return [
                'total' => 0,
                'logs' => [],
                'per_page' => $per_page,
                'current_page' => $page,
                'total_pages' => 0
            ];
        }
        
        // Use the provided current_user_id from filters if available, otherwise get the current user
        $user_id = !empty($filters['current_user_id']) ? $filters['current_user_id'] : get_current_user_id();
        $user_role = $this->kcbase->getLoginUserRole($user_id);
        
        $offset = ($page - 1) * $per_page;
        
        $where_conditions = [];
        $query_params = [];
        
        // Basic permission filtering - Super admin and admin can see all logs
        if ($user_role === 'administrator' || current_user_can('administrator')) {
            // Admin can see all logs - no conditions needed
        } elseif ($user_role === $this->kcbase->getClinicAdminRole()) {
            // Clinic admin can see logs from their clinic
            $clinic_id = kcGetClinicIdOfClinicAdmin($user_id);
            if (!empty($clinic_id)) {
                $where_conditions[] = "clinic_id = %d";
                $query_params[] = $clinic_id;
            }
        } elseif ($user_role === $this->kcbase->getDoctorRole()) {
            // Doctor can see their own logs and their patients' logs
            $where_conditions[] = "(user_id = %d OR user_id IN (SELECT patient_id FROM {$wpdb->prefix}kc_patient_clinic_mapping WHERE doctor_id = %d))";
            $query_params[] = $user_id;
            $query_params[] = $user_id;
        } elseif ($user_role === $this->kcbase->getPatientRole()) {
            // Patients can only see their own logs
            $where_conditions[] = "user_id = %d";
            $query_params[] = $user_id;
        } elseif ($user_role === $this->kcbase->getReceptionistRole()) {
            // Receptionist can see logs from their clinic
            $clinic_id = kcGetClinicIdOfReceptionist($user_id);
            if (!empty($clinic_id)) {
                $where_conditions[] = "clinic_id = %d";
                $query_params[] = $clinic_id;
            }
        }
        
        // Apply additional filters
        if (!empty($filters['user_id'])) {
            $where_conditions[] = "user_id = %d";
            $query_params[] = $filters['user_id'];
        }
        
        if (!empty($filters['user_type'])) {
            $where_conditions[] = "user_type = %s";
            $query_params[] = $filters['user_type'];
        }
        
        if (!empty($filters['activity_type'])) {
            $where_conditions[] = "activity_type = %s";
            $query_params[] = $filters['activity_type'];
        }
        
        if (!empty($filters['clinic_id'])) {
            $where_conditions[] = "clinic_id = %d";
            $query_params[] = $filters['clinic_id'];
        }
        
        if (!empty($filters['patient_id'])) {
            $where_conditions[] = "patient_id = %d";
            $query_params[] = $filters['patient_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $where_conditions[] = "created_at >= %s";
            $query_params[] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $where_conditions[] = "created_at <= %s";
            $query_params[] = $filters['date_to'] . ' 23:59:59';
        }
        
        // Handle user_name filter
        if (!empty($filters['user_name'])) {
            // Simplify this by just matching directly in the user_id field
            // This is a simplified approach to avoid complex queries
            $user_search = '%' . esc_sql($filters['user_name']) . '%';
            $where_conditions[] = "user_id IN (SELECT ID FROM {$wpdb->users} WHERE display_name LIKE '{$user_search}')";
        }
        
        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }
        
        // Count total rows for pagination
        $count_query = "SELECT COUNT(*) FROM {$this->tableName} {$where_clause}";
        
        if (!empty($query_params)) {
            $count_query = $wpdb->prepare($count_query, $query_params);
        }
        
        $total = $wpdb->get_var($count_query);
        
        // Get the actual data
        $query = "SELECT * FROM {$this->tableName} {$where_clause} ORDER BY created_at DESC LIMIT %d, %d";
        $all_params = array_merge($query_params, [$offset, $per_page]);
        
        $query = $wpdb->prepare($query, $all_params);
        $logs = $wpdb->get_results($query);
        
        // Process logs to add user details
        $processed_logs = [];
        foreach ($logs as $log) {
            $user_data = get_userdata($log->user_id);
            $log->user_name = ($user_data) ? $user_data->display_name : 'Unknown User';
            
            // Add display role
            if (!empty($log->user_type)) {
                $log->display_role = $this->getFormattedUserRole($log->user_type);
            }
            
            // Add clinic name if clinic_id is present
            if (!empty($log->clinic_id)) {
                $clinic = $wpdb->get_row($wpdb->prepare(
                    "SELECT name FROM {$wpdb->prefix}kc_clinics WHERE id = %d",
                    $log->clinic_id
                ));
                $log->clinic_name = ($clinic) ? $clinic->name : 'Unknown Clinic';
            }
            
            $processed_logs[] = $log;
        }
        
        return [
            'total' => (int) $total,
            'logs' => $processed_logs,
            'per_page' => $per_page,
            'current_page' => $page,
            'total_pages' => ceil($total / $per_page)
        ];
    }
    
    /**
     * Format user role for display
     * 
     * @param string $role Role name
     * @return string Formatted role name
     */
    private function getFormattedUserRole($role) {
        // Check for specific role mappings first
        $roleMap = [
            'kivicare_clinic_admin' => 'Clinic Admin',
            'kivicare_doctor' => 'Doctor',
            'kivicare_patient' => 'Patient',
            'kivicare_receptionist' => 'Admin Staff',
            'clinic_admin' => 'Clinic Admin',
            'doctor' => 'Doctor',
            'patient' => 'Patient',
            'receptionist' => 'Admin Staff',
            'administrator' => 'Administrator'
        ];

        // Return mapped role if it exists
        if (isset($roleMap[$role])) {
            return $roleMap[$role];
        }

        // Otherwise, use the default formatting
        // Remove kivicare prefix if present
        if (strpos($role, 'kivicare_') === 0) {
            $role = str_replace('kivicare_', '', $role);
        }

        // Capitalize and replace underscores with spaces
        return ucwords(str_replace('_', ' ', $role));
    }
}