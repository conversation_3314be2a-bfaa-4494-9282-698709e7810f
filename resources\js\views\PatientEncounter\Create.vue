<template>
  <div class="p-6">
    <form id="encounterDataForm" @submit.prevent="handleSubmit" :novalidate="true">
      <div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- First Row -->
          <!-- Clinic Selection -->
          <div v-if="(userData.addOns.kiviPro == true && (getUserRole() == 'administrator' || getUserRole() == 'doctor')) && clinicField">
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
              {{ formTranslation.clinic.select_clinic }}
              <span class="text-red-500">*</span>
            </label>
            <multi-select
              v-model="encounterData.clinic_id"
              id="clinic_id"
              deselect-label=""
              select-label=""
              @select="clinicChange"
              @remove="clinicChange"
              :loading="clinicMultiselectLoader"
              :disabled="clinicMultiselectLoader"
              :tag-placeholder="formTranslation.patient_encounter.tag_select_clinic_plh"
              :placeholder="formTranslation.patient_encounter.tag_select_clinic_plh"
              label="label"
              track-by="id"
              :options="clinic"
              class="w-full"
            />
            <p v-if="submitted && !$v.encounterData.clinic_id.id.required" class="mt-1 text-sm text-red-500">
              {{ formTranslation.common.clinic_is_required }}
            </p>
          </div>

          <!-- Doctor Selection -->
          <div v-if="getUserRole() != 'doctor'">
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
              {{ formTranslation.common.doctor }}
              <span class="text-red-500">*</span>
            </label>
            <multi-select
              v-model="encounterData.doctor_id"
              id="doctor_id"
              deselect-label=""
              select-label=""
              :loading="doctorMultiselectLoader"
              :disabled="doctorMultiselectLoader"
              :tag-placeholder="formTranslation.patient_encounter.tag_select_doctor"
              :placeholder="formTranslation.patient_encounter.search_plh"
              label="label"
              track-by="id"
              :options="doctors"
              class="w-full"
            />
            <p v-if="submitted && !$v.encounterData.doctor_id.required" class="mt-1 text-sm text-red-500">
              {{ formTranslation.appointments.doc_required }}
            </p>
          </div>

          <!-- Second Row -->
          <!-- Patient Selection -->
          <div v-if="patientField">
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
              {{ formTranslation.common.patient }}
              <span class="text-red-500">*</span>
            </label>
            <multi-select
              v-model="encounterData.patient_id"
              id="patient_id"
              deselect-label=""
              select-label=""
              :loading="patientMultiselectLoader"
              :disabled="patientMultiselectLoader"
              :tag-placeholder="formTranslation.patient_encounter.tag_patient_type_plh"
              :placeholder="formTranslation.patient_encounter.search_plh"
              label="label"
              track-by="id"
              :options="patients"
              class="w-full"
            />
            <p v-if="submitted && !$v.encounterData.patient_id.required" class="mt-1 text-sm text-red-500">
              {{ formTranslation.patient_bill.patient_required }}
            </p>
          </div>

          <!-- Date Picker Section -->
          <div>
            <label for="receptionist_birthdate" class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
              {{ formTranslation.patient_encounter.encounter_date }}
              <span class="text-red-500">*</span>
            </label>
            <vc-date-picker
              id="receptionist_birthdate"
              title-position="left"
              v-model="encounterData.date"
              :popover="{ placement: 'bottom', visibility: 'click' }"
              :class="{ 'border-red-500': submitted && $v.encounterData.date.$error }"
            >
              <template v-slot="{ inputValue }">
                <input
                  class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  readonly
                  :value="inputValue"
                />
              </template>
            </vc-date-picker>
            <p v-if="submitted && !$v.encounterData.date.required" class="mt-1 text-sm text-red-500">
              {{ formTranslation.patient_encounter.encounter_date_required }}
            </p>
          </div>

          <!-- Description -->
          <div class="md:col-span-2">
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
              {{ formTranslation.appointments.description }}
            </label>
            <textarea
              id="description"
              name="description"
              v-model="encounterData.description"
              :placeholder="formTranslation.appointments.description"
              rows="4"
              class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
            ></textarea>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end gap-4">
          <button
            type="button"
            @click="getCloseForm()"
            class="px-4 py-2 bg-white border border-gray-200 text-gray-600 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-400"
          >
            {{ formTranslation.common.cancel }}
          </button>
          <button
            v-if="!loading"
            type="submit"
            class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17 21 17 13 7 13 7 21"></polyline>
              <polyline points="7 3 7 8 15 8"></polyline>
            </svg>
            {{ formTranslation.patient_encounter.proceed_btn }}
          </button>
          <button
            v-else
            type="submit"
            disabled
            class="px-4 py-2 bg-black text-white rounded-lg opacity-75 cursor-not-allowed flex items-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 animate-spin">
              <line x1="12" x2="12" y1="2" y2="6"></line>
              <line x1="12" x2="12" y1="18" y2="22"></line>
              <line x1="4.93" x2="7.76" y1="4.93" y2="7.76"></line>
              <line x1="16.24" x2="19.07" y1="16.24" y2="19.07"></line>
              <line x1="2" x2="6" y1="12" y2="12"></line>
              <line x1="18" x2="22" y1="12" y2="12"></line>
              <line x1="4.93" x2="7.76" y1="19.07" y2="16.24"></line>
              <line x1="16.24" x2="19.07" y1="7.76" y2="4.93"></line>
            </svg>
            {{ formTranslation.common.loading }}
          </button>
        </div>
      </div>
    </form>
  </div>
</template>
<script>
import { required, numeric,requiredIf, requiredUnless } from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import {validateForm} from "../../config/helper";

export default {
data: () => {
    return {
        cardTitle: 'Add encounter',
        patient_id: 0,
        encounterData: {},
        specialization: [],
        clinics: [],
        doctors: [],
        loading: false,
        submitted: false,
        buttonText: '<i class="fa fa-save"></i> Save',
        patients:[],
        encounterTemplates:[],
      clinicMultiselectLoader:true,
      doctorMultiselectLoader:true,
      patientMultiselectLoader:true,
      encounterTemplateLoader:true,
    }
},
props: ['encounterId','patientField', 'clinicField'],
validations() {
  return {
    encounterData: {
      date: { required },
      patient_id:{
        required
      },
      doctor_id: {
        required: requiredIf(function () {
          return !this.isDoctorSelectionEnabled
        })
      },
      clinic_id:{
        id:{
          required: requiredIf(function () {
            return this.userData.addOns.kiviPro == true && (this.getUserRole() == 'administrator' || this.getUserRole() == 'doctor') && (this.clinicField !== false)
          })
        }
      }
    }
  }
},
mounted() {
    this.encounterData = this.defaultEncounterData();   
    
    if(this.getUserRole() !== 'doctor'){
      this.getDoctorsData();
    }
    this.init();
    setTimeout(()=>{
      this.activeClinicId = this.userData.default_clinic_id ;
    },2000)
},
methods: {
    init: function () {

      if (this.encounterId!== undefined && this.encounterId != -1 ) {
        this.cardTitle = this.formTranslation.common.edit_encounter;
        this.buttonText = '<i class="fa fa-save"></i> '+this.formTranslation.common.save;
        get('patient_encounter_edit', {
          id:this.encounterId
        })
            .then((response) => {
              if (response.data.status !== undefined && response.data.status === true) {
                this.encounterData = response.data.data ;
                this.patient_id = this.encounterData.patient_id
                this.encounterData.date  = new Date(this.encounterData.date + ' 00:00');
              }
            })
            .catch((error) => {
              console.log(error);
              displayErrorMessage(this.formTranslation.widgets.record_not_found);
            })
      }
        if (this.getUserRole() !== 'patient') {
          this.getClinicPatients('')
        }

        if (this.$route.params.patient_id !== undefined) {
            this.encounterData.patient_id = this.$route.params.patient_id;
        }
       
    },
    getDoctorsData: function () {

        let clinic_id = 0 ; 

        if(typeof this.activeClinicId == 'object') {
            clinic_id = this.activeClinicId.id ;
        } else {
            clinic_id = this.activeClinicId ;
        }

        this.doctorMultiselectLoader = true;
      get('get_static_data', {
        data_type: 'clinic_doctors',
        clinic_id: clinic_id
      })
          .then((data) => {
            this.doctorMultiselectLoader = false;
            if (data.data.status !== undefined && data.data.status === true) {
              this.doctors = data.data.data;
            }
          })
          .catch((error) => {
            this.doctorMultiselectLoader = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.common.internal_server_error);
          })
    },
    handleSubmit: function () {
       let userRole = this.getUserRole();
        this.loading = true;
        this.submitted = true;
        // stop here if form is invalid
        this.$v.$touch();
        if (this.$v.encounterData.$invalid) {
            this.loading = false;
            return;
        }
        this.encounterData.date = moment(this.encounterData.date).format("YYYY-MM-DD") ;
        if(userRole ==='doctor'){
            this.encounterData.doctor_id = this.userId
        }
        if (validateForm("encounterDataForm")) {
            post('patient_encounter_save', this.encounterData)
                .then((response) => {
                    this.loading = false;
                    this.submitted = true;
                    if (response.data.status !== undefined && response.data.status === true) {
                        displayMessage(response.data.message);
                        if('patient_id' in this.$route.params){
                            this.$router.push({path: '/patient'})
                        }
                        this.$router.push({
                            name: 'appointment.new', 
                            params: { encounter_id: response.data.data }
                        });
                    } else {
                        displayErrorMessage(response.data.message)
                    }

                })
                .catch((error) => {
                    console.log(error);
                    this.loading = false;
                    this.submitted = true;
                    displayErrorMessage(this.formTranslation.common.internal_server_error)
                })
        }
    },
    defaultEncounterData: function () {
        return {
            date: new Date(),
            clinic_id: {id:''},
            doctor_id: "",
            patient_id:"",
            encounter_template_name:"",
            appointment_id: {},
            description: "",
            added_by: "",
            status: 1,
        }
    },
    handleClinicUnselect: function () {
        this.appointmentData.clinic_id = {};
        this.appointmentData.doctor_id = {};
        this.doctors = [];
    },
    clinicChange(selectedOption) {
    this.encounterData.doctor_id = '';
    this.encounterData.patient_id = '';
      this.doctorMultiselectLoader = true;
    if (this.getUserRole() !== 'doctor') {
      get('get_static_data', {
        data_type: 'get_users_by_clinic',
        clinic_id: selectedOption.id,
        type: 'doctor'
      })
          .then((response) => {
            this.doctorMultiselectLoader = false;
            if (response.data.status !== undefined && response.data.status === true) {
              this.doctors = response.data.data;
            } else {
              displayErrorMessage(response.data.message)
            }

          })
          .catch((error) => {
            this.doctorMultiselectLoader = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.common.internal_server_error)
          })
    }
    if (this.$route.params.patient_id !== undefined) {
      this.encounterData.patient_id = this.$route.params.patient_id;
    } else {
      if (this.getUserRole() !== 'patient') {
        this.getClinicPatients(selectedOption.id)
      }
    }
  },
    getClinicPatients(clinic_id){
      this.patientMultiselectLoader =true;
        get('get_static_data', {
              data_type: 'users',
              user_type: this.patientRoleName,
              request_clinic_id:clinic_id
          })
              .then((response) => {
                this.patientMultiselectLoader =false;
                  if (response.data.status !== undefined && response.data.status === true) {
                     this.patients = response.data.data;
                  }
              })
              .catch((error) => {
                this.patientMultiselectLoader =false;
                  console.log(error);
                  displayErrorMessage('Internal server error');
              })
    },  
    getClinicDoctors: function () {
        this.doctors = [];
        let clinic_id = 0 ; 

        if(typeof this.encounterData == 'object') {
            clinic_id = this.encounterData.id ;
        } else {
            clinic_id = this.encounterData ;
        }

        get('get_static_data', {
            data_type: 'clinic_doctors',
            clinic_id: clinic_id
        })
            .then((response) => {
                if (response.data.status !== undefined && response.data.status === true) {
                    this.doctors = response.data.data;
                } else {
                    displayErrorMessage(response.data.message)
                }
            })
            .catch((error) => {
                console.log(error);
                displayErrorMessage(this.formTranslation.common.internal_server_error)
            })
    },
    getCloseForm(){
      this.$emit('closeEncounterForm')
    },
    getEncounter(){
      this.$emit('getPatientEncountersData')
    },
  
},
computed: {
  clinicID () {
    if(this.$store.state.userDataModule.clinic.id !== undefined) {
      return this.$store.state.userDataModule.clinic.id ;
    }
  },
  isDoctorSelectionEnabled: function () {
      return !(this.getUserRole() !== 'doctor') ;
  },
  userId () {
          return this.$store.state.userDataModule.user.ID;
  },
  userData() {
    return this.$store.state.userDataModule.user;
  },
  clinic() {
    this.clinicMultiselectLoader = false;
    return this.$store.state.clinic
  }
},
watch: {

}
}
</script>
