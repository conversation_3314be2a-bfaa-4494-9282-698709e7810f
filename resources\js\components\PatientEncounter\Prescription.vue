<!-- Prescription.vue -->

<template>
  <b-row
    v-if="
      userData.addOns.kiviPro == true
        ? getEnablePrescription[0]['status'] == 1
        : true
    "
  >
    <b-col sm="12">
      <div
        class="card p-0 shadow"
        v-if="kcCheckPermission('prescription_list')"
      >
        <div class="card-header">
          <div class="row">
            <div class="col-md-4">
              <h3 class="mb-0">
                {{ formTranslation.patient_encounter.prescription }}
              </h3>
            </div>
            <div class="col-md-8">
              <div class="d-flex justify-content-end align-items-center">
                <button
                  id="send_prescription"
                  v-if="
                    prescriptionList.length > 0 && !isEcounterTemplateModule
                  "
                  @click="mailPrescriptionAdd"
                  class="btn btn-sm btn-primary"
                  type="button"
                >
                  <i class="fas fa-paper-plane"></i>
                  {{ formTranslation.clinic.dt_lbl_email }}
                </button>
                <button
                  id="send_signaturerx"
                  v-if="
                    prescriptionList.length > 0 && !isEcounterTemplateModule
                  "
                  @click="showSignatureRXModal = true"
                  class="btn btn-sm btn-primary ml-2"
                  type="button"
                  :disabled="signatureRXLoading"
                >
                  <i :class="signatureRXLoading ? 'fa fa-sync fa-spin' : 'fas fa-prescription-bottle-alt'"></i>
                  Send with SignatureRX
                </button>
                
                <!-- SignatureRX Modal -->
                <ModalPopup
                  modalId="signatureRX-modal"
                  modalSize="md"
                  :openModal="showSignatureRXModal"
                  modalTitle="SignatureRX Authentication"
                  @closeModal="showSignatureRXModal = false"
                >
                  <div class="p-4">
                    <div class="alert alert-info" role="alert">
                      <h5 class="mb-2"><i class="fas fa-info-circle"></i> SignatureRX Authentication</h5>
                      <p>Please enter your 6-digit secure PIN to authenticate with SignatureRX and select how you want to handle this prescription.</p>
                    </div>
                    
                    <div class="form-group mb-4">
                      <label for="srx-pin" class="form-label">Secure PIN</label>
                      <input 
                        type="password" 
                        id="srx-pin" 
                        v-model="signatureRXPin" 
                        class="form-control" 
                        placeholder="Enter your 6-digit PIN"
                        maxlength="6"
                      />
                      <small class="form-text text-muted">This PIN will be used to authenticate your prescription with SignatureRX.</small>
                    </div>
                    
                    <div class="form-group mb-4">
                      <label for="srx-action" class="form-label">Prescription Action</label>
                      <select 
                        id="srx-action" 
                        v-model="signatureRXAction" 
                        class="form-control"
                      >
                        <option value="">Use Default Setting</option>
                        <option value="draft">📝 Save as Draft</option>
                        <option value="issueOnly">✅ Issue Only</option>
                        <option value="issueForCollection">🏥 Issue for Collection</option>
                        <option value="issueToContact">📱 Issue to Contact</option>
                        <option value="issueForDelivery">🚚 Issue for Delivery</option>
                      </select>
                      <small class="form-text text-muted">
                        <span v-if="signatureRXAction">
                          <strong>
                            <i class="fas fa-check-circle text-success mr-1"></i>
                            {{ 
                              signatureRXAction === 'draft' ? 'Save prescription as draft' :
                              signatureRXAction === 'issueOnly' ? 'Issue prescription immediately' :
                              signatureRXAction === 'issueForCollection' ? 'Issue for pharmacy collection' :
                              signatureRXAction === 'issueToContact' ? 'Issue and send to patient contact' :
                              signatureRXAction === 'issueForDelivery' ? 'Issue for delivery to patient' :
                              'Custom action selected'
                            }}
                          </strong>
                        </span>
                        <span v-else>
                          <i class="fas fa-info-circle text-info mr-1"></i>
                          Using your default action set in doctor profile
                        </span>
                      </small>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                      <button
                        type="button"
                        class="btn btn-secondary mr-2"
                        @click="cancelSignatureRX"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        class="btn btn-primary"
                        @click="sendToSignatureRX"
                        :disabled="signatureRXLoading || !signatureRXPin"
                      >
                        <i :class="signatureRXLoading ? 'fa fa-sync fa-spin' : 'fas fa-paper-plane'"></i>
                        {{ signatureRXLoading ? 'Sending...' : 'Send Prescription' }}
                      </button>
                    </div>
                  </div>
                </ModalPopup>
                <button
                  v-if="
                    userData.addOns.kiviPro &&
                    kivicareCompareVersion(
                      requireProVersion,
                      userData.pro_version
                    ) &&
                    getUserRole() !== 'patient' &&
                    encounterStatus === '1' &&
                    kcCheckPermission('prescription_add') &&
                    !isEcounterTemplateModule
                  "
                  class="btn btn-sm btn-primary"
                  @click="$refs.module_data_import.openModel = true"
                >
                  <i class="fas fa-file-import"></i
                  >{{ formTranslation.common.import_data }}
                </button>
                <button
                  v-if="
                    encounterData.status != 0 &&
                    kcCheckPermission('prescription_add')
                  "
                  @click="handlePrescriptionAdd"
                  class="btn btn-sm btn-primary"
                  :to="{ name: 'prescription_add' }"
                >
                  <i v-if="!showAddForm" class="fa fa-plus"></i>
                  <i v-else class="fa fa-minus"></i>
                  {{
                    showAddForm
                      ? formTranslation.common.close_form_btn
                      : formTranslation.encounter_dashboard.add_prescription_btn
                  }}
                </button>
                <module-data-export
                  :module_data="exportPrescription"
                  :module_name="formTranslation.patient_encounter.prescription"
                  module_type="prescription"
                  v-if="
                    kcCheckPermission('prescription_export') &&
                    !isEcounterTemplateModule
                  "
                >
                </module-data-export>
                <button
                  v-b-tooltip.hover
                  :title="formTranslation.widget_setting.print"
                  class="btn btn-sm pr-0 pl-2 ml-1"
                  v-if="
                    userData.addOns.kiviPro == true &&
                    prescriptionList.length > 0 &&
                    !isEcounterTemplateModule
                  "
                  @click.prevent="printPrescription"
                  :disabled="printPrescriptionLoader"
                >
                  <i
                    :class="
                      printPrescriptionLoader
                        ? 'fa fa-sync fa-spin'
                        : 'fa fa-print'
                    "
                  ></i>
                </button>
              </div>
            </div>
          </div>
          <module-data-import
            :encounter_id="encounterData.encounter_id"
            v-if="userData.addOns.kiviPro"
            ref="module_data_import"
            @reloadList="getPrescriptionRecords"
            :required_data="[
              {
                label: formTranslation.common.prescription_name,
                value: 'name',
              },
              {
                label: formTranslation.common.prescription_frequency,
                value: 'frequency',
              },
              {
                label: formTranslation.common.prescription_duration,
                value: 'duration',
              },
            ]"
            :module_name="formTranslation.common.prescription"
            module_type="prescription"
          ></module-data-import>

          <ModalPopup
            v-if="showAddForm"
            modalId="appointment-details-modal"
            modalSize="lg"
            :openModal="showAddForm"
            :modalTitle="
              prescriptionData.id
                ? formTranslation.patient_encounter.edit_prescription
                : formTranslation.patient_encounter.add_prescription
            "
            @closeModal="showAddForm = false"
          >
            <div
              class="m-4"
              v-if="
                kcCheckPermission('prescription_add') && encounterStatus === '1'
              "
            >
              <form
                id="prescriptionDataForm"
                @submit.prevent="handleSubmit"
                :novalidate="true"
              >
                <div class="row">
                  <div class="col-6">
                    <div class="form-group">
                      <label for="prescription" class="form-control-label"
                        >{{ formTranslation.common.name }}
                        <span class="text-danger">*</span></label
                      >
                      <multi-select
                        deselect-label=""
                        select-label=""
                        id="prescription"
                        v-model="prescriptionData.name"
                        :tag-placeholder="
                          formTranslation.patient_encounter.tag_name_plh
                        "
                        :placeholder="formTranslation.settings.tag_plh_option"
                        label="label"
                        @tag="addPrescriptionOption"
                        track-by="id"
                        :taggable="true"
                        :options="prescriptionNames"
                      ></multi-select>
                      <span class="text-primary small"
                        ><b>{{
                          formTranslation.patient_encounter.note_prescription
                        }}</b></span
                      >
                      <div
                        v-if="submitted && !$v.prescriptionData.name.required"
                        class="invalid-feedback"
                      >
                        {{ formTranslation.common.name_required }}
                      </div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="form-group">
                      <label for="dose" class="form-control-label">
                        Dose
                        <span class="text-danger">*</span>
                      </label>
                      <input
                        id="dose"
                        type="text"
                        class="form-control"
                        v-model="prescriptionData.dose"
                        placeholder="Enter dose (e.g., 10mg, 500mcg)"
                      />
                      <div
                        v-if="
                          submitted && !$v.prescriptionData.dose.required
                        "
                        class="invalid-feedback"
                      >
                        Dose is required
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="row">
                  <div class="col-6">
                    <div class="form-group">
                      <label for="frequency" class="form-control-label">
                        {{ formTranslation.patient_encounter.frequency }}
                        <span class="text-danger">*</span>
                      </label>
                      <input
                        id="frequency"
                        type="text"
                        class="form-control"
                        v-model="prescriptionData.frequency"
                        :placeholder="
                          formTranslation.patient_encounter.frequency_plh
                        "
                      />
                      <div
                        v-if="
                          submitted && !$v.prescriptionData.frequency.required
                        "
                        class="invalid-feedback"
                      >
                        {{
                          formTranslation.patient_encounter.frequency_required
                        }}
                      </div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="form-group">
                      <label for="route" class="form-control-label">
                        Route
                        <span class="text-danger">*</span>
                      </label>
                      <select
                        id="route"
                        class="form-control"
                        v-model="prescriptionData.route"
                      >
                        <option value="Oral">Oral</option>
                        <option value="Intravenous">Intravenous</option>
                        <option value="Intramuscular">Intramuscular</option>
                        <option value="Subcutaneous">Subcutaneous</option>
                        <option value="Topical">Topical</option>
                        <option value="Inhaled">Inhaled</option>
                        <option value="Sublingual">Sublingual</option>
                        <option value="Rectal">Rectal</option>
                        <option value="Other">Other</option>
                      </select>
                      <div
                        v-if="
                          submitted && !$v.prescriptionData.route.required
                        "
                        class="invalid-feedback"
                      >
                        Route is required
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-4">
                    <div class="form-group">
                      <label for="duration" class="form-control-label">
                        {{ formTranslation.patient_encounter.duration_Days }}
                        <span class="text-danger">*</span>
                      </label>
                      <input
                        type="number"
                        name="duration"
                        v-model="prescriptionData.duration"
                        id="duration"
                        @keypress="filterNonNumeric($event)"
                        class="form-control"
                      />
                      <div
                        v-if="
                          submitted && !$v.prescriptionData.duration.required
                        "
                        class="invalid-feedback"
                      >
                        {{
                          formTranslation.patient_encounter.duration_required
                        }}
                      </div>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="form-group">
                      <label for="quantity" class="form-control-label">
                        Quantity
                      </label>
                      <input
                        type="number"
                        name="quantity"
                        v-model="prescriptionData.quantity"
                        id="quantity"
                        @keypress="filterNonNumeric($event)"
                        class="form-control"
                        placeholder="Total units to dispense"
                      />
                      <small class="text-muted">
                        Leave blank to auto-calculate from duration and frequency
                      </small>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="form-group">
                      <label for="instruction" class="form-control-label">
                        {{ formTranslation.patient_encounter.instruction }}
                      </label>
                      <textarea
                        name="instruction"
                        v-model="prescriptionData.instruction"
                        :placeholder="
                          formTranslation.patient_encounter
                            .enter_instruction_plh
                        "
                        class="form-control"
                        id="instruction"
                      >
                      </textarea>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-12">
                    <div class="float-right">
                      <button
                        v-if="!loading"
                        class="btn btn-primary"
                        type="submit"
                      >
                        <i class="fa fa-plus"></i>
                        {{
                          formTranslation.encounter_dashboard
                            .presciption_save_btn
                        }}
                      </button>
                      <button
                        v-else
                        class="btn btn-primary"
                        type="button"
                        disabled
                      >
                        <i class="fa fa-sync fa-spin"></i>&nbsp;
                        {{ formTranslation.common.loading }}
                      </button>
                      <button
                        type="button"
                        class="btn btn-outline-primary"
                        @click="close()"
                      >
                        {{ formTranslation.common.cancel }}
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </ModalPopup>
        </div>
        <div class="card-body">
          <div class="row text-uppercase mb-3 d-md-flex d-none">
            <div class="col-4 text-left">
              <h5 class="mb-0">{{ formTranslation.common.name }}</h5>
            </div>
            <div class="col-3">
              <h5 class="mb-0">
                {{ formTranslation.patient_encounter.frequency }}
              </h5>
            </div>
            <div class="col-3">
              <h5 class="mb-0">
                {{ formTranslation.patient_encounter.duration }}
              </h5>
            </div>
            <div class="col-2" v-if="getUserRole() !== 'patient'">
              <h5 class="mb-0">{{ formTranslation.common.action }}</h5>
            </div>
          </div>
          <div v-if="prescriptionList.length > 0">
            <div
              class="card encounter-prescription mb-3 p-3 shadow-lg"
              v-for="(prescription, index) in prescriptionList"
              v-if="prescription !== undefined"
              :key="index"
            >
              <div class="row animated fadeInUp">
                <div class="col-md-4">
                  <h5 class="mb-2 d-md-none">
                    {{ formTranslation.common.name }}
                  </h5>
                  <h3 class="text-primary mb-0">
                    {{ prescription.name.label }}
                  </h3>
                  <div class="d-flex align-items-center mt-1">
                    <span class="badge badge-light mr-2" v-if="prescription.dose">
                      {{ prescription.dose }}
                    </span>
                    <span class="badge badge-info mr-2" v-if="prescription.route">
                      {{ prescription.route }}
                    </span>
                    <span class="badge badge-secondary mr-2" v-if="prescription.duration">
                      {{ prescription.duration }} days
                    </span>
                    <span class="badge badge-success mr-2" v-if="prescription.quantity">
                      Qty: {{ prescription.quantity }}
                    </span>
                  </div>
                  <p class="mb-0 small mt-1">{{ prescription.instruction }}</p>
                </div>
                <div class="col-md-3 col-6 mt-md-0 mt-3">
                  <h5 class="mb-2 d-md-none">
                    {{ formTranslation.patient_encounter.frequency }}
                  </h5>
                  <h5 class="mb-0">{{ prescription.frequency }}</h5>
                </div>
                <div class="col-md-3 col-6 mt-md-0 mt-3">
                  <h5 class="mb-2 d-md-none">
                    {{ formTranslation.patient_encounter.duration }}
                  </h5>
                  <h5 class="mb-0">
                    {{
                      prescription.duration +
                      " " +
                      formTranslation.fullcalendar.day
                    }}
                  </h5>
                </div>
                <div
                  class="col-md-2 col-12 mt-md-0 mt-4"
                  v-if="getUserRole() !== 'patient'"
                >
                  <h5 class="mb-2 d-md-none">
                    {{ formTranslation.common.action }}
                  </h5>
                  <div v-if="encounterStatus === '1'">
                    <b-button-group size="sm">
                      <b-button
                        variant="outline-primary"
                        v-if="kcCheckPermission('prescription_edit')"
                        @click="
                          handlePrescriptionEdit(prescription, prescription.id)
                        "
                        v-b-tooltip.hover
                        :title="formTranslation.common.edit"
                      >
                        <i class="fa fa-edit"></i>
                      </b-button>
                      <b-button
                        variant="outline-danger"
                        v-if="kcCheckPermission('prescription_delete')"
                        v-b-tooltip.hover
                        :title="formTranslation.clinic_schedule.dt_lbl_dlt"
                        @click="deletePrescriptionData(index)"
                      >
                        <i class="fa fa-trash"></i>
                      </b-button>
                    </b-button-group>
                  </div>
                  <span v-else> - </span>
                </div>
              </div>
            </div>
          </div>
          <div
            v-else-if="prescriptionLoader"
            class="h2 font-weight-bold mb-0 text-center"
          >
            <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
          </div>
          <div v-else>
            <div class="row">
              <div class="col-md-12">
                <h4 class="text-danger text-center my-3">
                  {{ formTranslation.patient_encounter.no_prescription_found }}
                </h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </b-col>
  </b-row>
</template>

<script>
import { post, get } from "../../config/request";
import { validateForm } from "../../config/helper";
import { required } from "vuelidate/lib/validators";
import ModalPopup from "../Modal/Index";
export default {
  props: {
    encounterId: {
      type: [String, Number],
      default() {
        return 0;
      },
    },
    encounterData: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
    updateCounter: 0,
    isEcounterTemplateModule: false,
  },
  validations: {
    prescriptionData: {
      name: { required },
      frequency: { required },
      duration: { required },
      dose: { required },
      route: { required },
    },
  },
  data: () => {
    return {
      showAddForm: false,
      prescriptionList: [],
      prescriptionData: {},
      durations: [],
      formTitle: "Add prescription",
      loading: false,
      submitted: false,
      componentKey: 0,
      prescriptionLoader: true,
      prescriptionNames: [],
      printPrescriptionLoader: false,
      exportPrescription: [],
      signatureRXLoading: false,
      showSignatureRXModal: false,
      signatureRXPin: '',
      signatureRXAction: '',
    };
  },
  mounted() {
    this.init();
    console.log('isEcounterTemplateModule:', this.isEcounterTemplateModule);
    console.log('prescriptionList length:', this.prescriptionList.length);
    console.log('userData:', this.userData);
  },
  components: {
    ModalPopup,
  },
  methods: {
    init: function () {
      this.durations = this.getDurations();
      this.prescriptionData = this.defaultFormData();
      setTimeout(() => {
        this.getPrescriptionRecords();
      }, 200);
    },
    // Method to properly close the SignatureRX modal and reset fields
    cancelSignatureRX: function() {
      console.log("Closing SignatureRX modal and resetting fields");
      // Force close modal by setting openModal to false
      this.showSignatureRXModal = false;
      // Reset fields to prepare for next use
      this.signatureRXPin = '';
      this.signatureRXAction = '';
      
      // Add a small delay to ensure modal is properly closed
      setTimeout(() => {
        // Double-check that modal is closed
        if (this.showSignatureRXModal) {
          console.log("Force closing modal again");
          this.showSignatureRXModal = false;
        }
      }, 100);
    },
    
    sendToSignatureRX: function () {
      if (this.encounterData.encounter_id === undefined) {
        displayErrorMessage("Encounter ID is required");
        return;
      }
      
      if (!this.signatureRXPin) {
        displayErrorMessage("Please enter your SignatureRX secure PIN");
        return;
      }
      
      // Validate PIN format (should be 6 digits)
      if (!/^\d{6}$/.test(this.signatureRXPin)) {
        displayErrorMessage("Your SignatureRX PIN must be 6 digits");
        return;
      }
      
      console.log('Starting SignatureRX send process');
      console.log('Encounter ID:', this.encounterData.encounter_id);
      console.log('PIN length:', this.signatureRXPin.length);
      
      this.signatureRXLoading = true;
      console.log('Setting loading state, preparing to send data to API');
      
      // Show feedback to user
      displayMessage("Sending prescription to SignatureRX...", "info");
      
      console.log('Sending data to API:', { 
        encounter_id: this.encounterData.encounter_id,
        // Not logging actual PIN for security
      });
      
      // Prepare request data with optional action
      const requestData = { 
        encounter_id: this.encounterData.encounter_id,
        secure_pin: this.signatureRXPin
      };
      
      // Add action if selected
      if (this.signatureRXAction) {
        requestData.action = this.signatureRXAction;
      }
      
      console.log('Sending data to API:', {
        encounter_id: this.encounterData.encounter_id,
        secure_pin: '******', // Hide PIN for security
        action: this.signatureRXAction || '(using default)'
      });
      
      post("signaturerx_send_prescription", requestData)
        .then((response) => {
          console.log('API Response:', response);
          this.signatureRXLoading = false;
          
          if (response.data && response.data.status === true) {
            // First close the modal to prevent UI conflicts
            console.log("Success! Closing modal first");
            this.cancelSignatureRX();
            
            // Wait a moment to ensure modal is fully closed before showing success message
            setTimeout(() => {
              // Get action-specific success message and icon
              let successMessage = "Prescription sent successfully to SignatureRX";
              let successIcon = "fa-check-circle";
              
              // Determine which action was used (from request or default)
              const usedAction = this.signatureRXAction || 
                                 response.data.action_used || 
                                 requestData.action || 
                                 'issueOnly';
              
              console.log("Action used:", usedAction);
              
              // Create action-specific message and icon
              switch(usedAction) {
                case 'draft':
                  successMessage = "Prescription saved as draft in SignatureRX";
                  successIcon = "fa-save";
                  break;
                case 'issueOnly':
                  successMessage = "Prescription issued successfully in SignatureRX";
                  successIcon = "fa-clipboard-check";
                  break;
                case 'issueForCollection':
                  successMessage = "Prescription issued for collection at pharmacy";
                  successIcon = "fa-store-alt";
                  break;
                case 'issueToContact':
                  successMessage = "Prescription issued and sent to patient's contact";
                  successIcon = "fa-address-book";
                  break;
                case 'issueForDelivery':
                  successMessage = "Prescription issued for delivery to patient's address";
                  successIcon = "fa-shipping-fast";
                  break;
              }
              
              // Display success message with appropriate icon
              if (window.Toastify) {
                // Show a customized toast notification with icon
                window.Toastify({
                  text: `<i class="fas ${successIcon} mr-2"></i> ${successMessage}`,
                  duration: 5000,
                  newWindow: true,
                  close: true,
                  gravity: "top",
                  position: "center",
                  className: "info",
                  style: {
                    background: "#4caf50",
                  },
                  escapeMarkup: false
                }).showToast();
              } else {
                // Fallback to standard message display
                displayMessage(successMessage, "success");
              }
              
              // Refresh prescription data
              this.getPrescriptionRecords();
            }, 300);
          } else {
            console.log('Error response from API:', response.data);
            
            // Check if we have a detailed error message
            let errorMsg = "Failed to send prescription to SignatureRX";
            
            if (response.data && response.data.message && response.data.message.trim() !== '') {
              errorMsg = response.data.message;
            }
            
            // Log any debug info for troubleshooting
            if (response.data && response.data.debug_info) {
              console.log('Debug info:', response.data.debug_info);
            }
            
            displayErrorMessage(errorMsg);
            
            // For empty messages, show a more helpful error
            if (response.data && (!response.data.message || response.data.message.trim() === '')) {
              displayErrorMessage("The SignatureRX service is currently unavailable. Please check your internet connection and try again later.");
            }
          }
        })
        .catch((error) => {
          console.log('Error sending to SignatureRX:', error);
          this.signatureRXLoading = false;
          
          let errorMessage = "Failed to send prescription to SignatureRX";
          
          // Try to extract more meaningful error if available
          if (error.response && error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          }
          
          // First close modal to prevent UI conflicts
          this.cancelSignatureRX();
          
          // Wait a moment to ensure modal is fully closed before showing error
          setTimeout(() => {
            // Display error with icon
            if (window.Toastify) {
              // Show a customized toast notification with icon
              window.Toastify({
                text: `<i class="fas fa-exclamation-circle mr-2"></i> ${errorMessage}`,
                duration: 5000,
                newWindow: true,
                close: true,
                gravity: "top",
                position: "center",
                className: "info",
                style: {
                  background: "#f44336",
                },
                escapeMarkup: false
              }).showToast();
            } else {
              // Fallback to standard error display
              displayErrorMessage(errorMessage);
            }
          }, 300);
        });
    },
    forceRerender() {
      this.componentKey += 1;
    },
    defaultFormData: function () {
      return {
        encounter_id: this.encounterData.encounter_id,
        name: "",
        dose: "",
        frequency: "",
        route: "Oral", // Default to Oral route
        duration: "",
        quantity: "", // For manually specifying quantity
        instruction: "",
      };
    },
    getDurations: function () {
      var day = 1;
      var durations = [];
      do {
        durations.push(day + " Days");
        if (day < 5) {
          day++;
        } else {
          day = day + 5;
        }
      } while (day <= 60);

      return durations;
    },
    getPrescriptionRecords: function () {
      if (this.encounterData.encounter_id !== undefined) {
        console.log('Getting prescription records for encounter ID:', this.encounterData.encounter_id);
        get(
          this.isEcounterTemplateModule
            ? "get_encounter_template_prescription_list"
            : "prescription_list",
          this.encounterData
        )
          .then((data) => {
            console.log('Prescription data received:', data);
            this.prescriptionNames = data.data.prescriptionNames;
            this.prescriptionLoader = false;
            if (data.data.status !== undefined && data.data.status === true) {
              if (data.data.data !== undefined && data.data.data.length > 0) {
                this.prescriptionList = data.data.data;
                console.log('Prescription list updated with', this.prescriptionList.length, 'prescriptions');
              } else {
                console.log('No prescriptions found in the data');
              }
            }
          })
          .catch((error) => {
            this.prescriptionLoader = false;
            console.log('Error getting prescription records:', error);
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    handleSubmit: function () {
      this.loading = true;
      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.prescriptionData.$invalid) {
        this.loading = false;
        return;
      }

      if (validateForm("prescriptionDataForm")) {
        post(
          this.isEcounterTemplateModule
            ? "save_encounter_template_prescription"
            : "prescription_save",
          this.prescriptionData
        )
          .then((response) => {
            this.loading = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              this.showAddForm = false;
              if (this.prescriptionData.id === undefined) {
                this.prescriptionList.push(response.data.data);
              }
              this.prescriptionData = this.defaultFormData();
              this.submitted = false;
              this.prescriptionData.encounter_id =
                this.encounterData.encounter_id;
              this.forceRerender();
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    mailPrescriptionAdd: function () {
      if (this.prescriptionData.encounter_id !== undefined) {
        var element = $("#send_prescription").find("i");
        element.removeClass("fa fa-paper-plane");
        element.addClass("fa fa-spinner fa-spin");
        get("prescription_mail", this.prescriptionData)
          .then((response) => {
            element.removeClass("fa fa-spinner fa-spin");
            element.addClass("fa fa-paper-plane");
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    close() {
      this.showAddForm = false;
      this.prescriptionData = this.defaultFormData();
    },
    handlePrescriptionAdd: function () {
      this.showAddForm = !this.showAddForm;
      this.prescriptionData = this.defaultFormData();
    },
    handlePrescriptionEdit: function (prescription, collapseID) {
      this.showAddForm = true;
      this.prescriptionData = prescription;
    },
    deletePrescriptionData: function (index) {
      if (this.prescriptionList[index] !== undefined) {
        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.py_delete_prescription,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
            focusConfirm: false,
          })
          .then((result) => {
            if (result.isConfirmed) {
              get(
                this.isEcounterTemplateModule
                  ? "delete_encounter_template_prescription"
                  : "prescription_delete",
                {
                  id: this.prescriptionList[index].id,
                }
              )
                .then((data) => {
                  if (
                    data.data.status !== undefined &&
                    data.data.status === true
                  ) {
                    this.prescriptionList.splice(index, 1);
                    displayMessage(data.data.message);
                  }
                })
                .catch((error) => {
                  if (
                    error.response.data !== undefined &&
                    error.response.data.message !== undefined
                  ) {
                    displayErrorMessage(error.response.data.message);
                  } else {
                    displayErrorMessage(
                      this.formTranslation.common.internal_server_error
                    );
                  }
                });
            }
          });
      }
    },
    addPrescriptionOption: function (newTag) {
      const tag = {
        id: newTag,
        label: newTag,
      };
      let tagObj = {
        label: newTag,
        type: "prescription_medicine",
        value: newTag.replace(" ", "_"),
        status: 1,
      };
      let _this = this;
      post("static_data_save", tagObj)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            _this.prescriptionNames.push(tag);
            _this.prescriptionData.name = tag;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    printPrescription() {
      if (this.encounterData.encounter_id !== undefined) {
        this.printPrescriptionLoader = true;
        get("get_prescription_print", { id: this.encounterData.encounter_id })
          .then((response) => {
            this.printPrescriptionLoader = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              $(response.data.data).printArea();
            }
          })
          .catch((error) => {
            this.printPrescriptionLoader = false;
            console.log(error);
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    filterNonNumeric: function (evt) {
      const keysAllowed = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
      const keyPressed = evt.key;

      if (!keysAllowed.includes(keyPressed)) {
        evt.preventDefault();
      }
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    encounterStatus() {
      return this.encounterData.status !== undefined
        ? this.encounterData.status
        : "1";
    },
    getEnablePrescription() {
      if (this.userData.prescription_module_config !== undefined) {
        return this.userData.prescription_module_config;
      }
    },
    // formTranslation: function () {
    //     return this.$store.state.staticDataModule.langTranslateData ;
    // }
  },
  watch: {
    encounterData(val, oldVal) {
      this.prescriptionData.encounter_id = val.encounter_id;
    },
    prescriptionList(val, old) {
      let _this = this;
      val = val.map(function (val) {
        return Object.keys(val).reduce((acc, elem) => {
          switch (elem) {
            case "name":
              acc[_this.formTranslation.common.name] = val[elem].label;
              break;
            case "frequency":
              acc[_this.formTranslation.patient_encounter.frequency] =
                val[elem];
              break;
            case "duration":
              acc[_this.formTranslation.patient_encounter.duration_Days] =
                val[elem];
              break;
            case "instruction":
              acc[_this.formTranslation.patient_encounter.instruction] =
                val[elem];
              break;
            case "default":
              break;
          }
          return acc;
        }, {});
      });
      this.exportPrescription = val;
    },
    updateCounter() {
      this.prescriptionList.length = 0;
      this.prescriptionLoader = true;
      this.init();
    },
  },
};
</script>

<style media="print" scoped>
@page {
  margin-bottom: 0;
}
</style>
