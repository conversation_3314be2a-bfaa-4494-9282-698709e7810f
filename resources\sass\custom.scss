::selection {
  background: $primary;
  text-shadow: none;
}
body{
  background:linear-gradient(125.58deg, #F5E2EF 7.1%, #F0EFFA 49.95%, #EFE5E1 94.54%) !important;
  background-size: cover;
}
#app {
  .card {
    max-width: none !important;
  }

}

:focus {
  outline: none;
}

.sidenav {
  z-index: 99 !important;
  left: 0;
  //border: 0 0 0 1;
}

.scrollbar-inner {
  overflow: hidden;
}

.navbar-vertical .navbar-brand-img,
.navbar-vertical .navbar-brand>img {
  max-height: 3rem !important;
}

.wp-core-ui select {
  max-width: none !important;
}

.vs-icon {
  font-size: 1rem !important;
}

.nav-link.router-link-exact-active.router-link-active {
  background: $primary;
  color: #fff;
  border: none;
  border-radius: 4px;
}



.multiselect__tags {
  min-height: 46px;

  input[type=text] {
    border: none !important;
  }
}

.multiselect {
  min-height: 46px;
}

.multiselect__select {
  height: 44px;
}

.multiselect__single {
  line-height: 27px;
}

.multiselect__input,
.multiselect__single {
  border: none !important;
}

.medical-history-card {
  max-height: 250px;
  min-height: 250px;
  overflow-y: auto;
}

#hero-navbar {
  .dropdown-menu.dropdown-menu-right.show {
    top: auto !important;
    left: auto !important;
    right: 0px !important;
  }
}

.nav-link.router-link-exact-active.router-link-active {
  color: #fff !important;

  i {
    color: #fff !important;
  }
}


#panel {
  .table.b-table.table-sm {
    th {
      vertical-align: middle;
      outline: none;
    }

    td {
      white-space: normal;
    }
  }
}

#app {}

.wp-person a:focus .gravatar,
a:focus,
a:focus .media-icon img {
  box-shadow: none;
  outline: none;
}

.media-modal-close .screen-reader-text {
  display: none;
}

.elementor-screen-only,
.screen-reader-text,
.screen-reader-text span,
.ui-helper-hidden-accessible {
  width: 1px;
  height: 1px;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}

li.save-ready span.screen-reader-text {
  display: none;
}

.multiselect__tag-icon:after {
  color: #fff !important;
}

.vue__time-picker {
  width: 100% !important;

  input.display-time {
    width: 100% !important;
    font-family: $font-family-sans-serif;
    background-clip: padding-box;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
  }

  input.display-time:focus {
    color: #8898aa;
    background-color: #fff;
    border-color: rgba(50, 151, 211, .25);
  }
}

.vue__time-picker .dropdown ul li:not([disabled]).active,
.vue__time-picker .dropdown ul li:not([disabled]).active:focus,
.vue__time-picker .dropdown ul li:not([disabled]).active:hover {
  background: $primary !important;
}

.vue__time-picker .dropdown .select-list {
  font-family: $font-family-sans-serif;
}

.vc-container,
.snackbar-container {
  font-family: $font-family-sans-serif !important;
}

.fade-enter-active,
.fade-leave-active {
  transition-duration: 0.3s;
  transition-property: opacity;
  transition-timing-function: ease;
}

.fade-enter,
.fade-leave-active {
  opacity: 0
}

.cursor-pointer,
.fc-event-container {
  cursor: pointer;
}

.invalid-feedback {
  display: block !important;
  font-size: 100% !important;
}

.multiselect__tag {
  background: $primary;
}

.multiselect__option--highlight {
  background: $primary;
}

.multiselect__option--highlight:after,
.multiselect__option--selected.multiselect__option--highlight:after,
.multiselect__option--selected:after {
  content: none;
}


.multiselect__tag-icon:focus,
.multiselect__tag-icon:hover {
  background: $primary;
}

.multiselect__option--highlight::after {
  background: $primary;
}


// Code for the Time slot buttons...
.timing-list {
  li {
    display: inline-block;
    list-style-type: none;
    line-height: 12px;
    vertical-align: middle;
    width: 16%;
    border-radius: 5px;
    margin: 5px;
    cursor: pointer;
    text-align: center;
    padding: 7px;
    border: 1px solid $primary;
  }

  input {
    display: none;
  }

  .selected-time {
    background: $primary;
    color: #fff;
  }
}

.footer {
  background: transparent !important;
}

.appointment-slot {
  min-height: 345px;
}

.badge-custom {
  padding: 8px 14px !important;
  font-size: 90% !important;
  font-weight: 100 !important;
}

.b-none {
  border: none !important;
}

.align-items-center {
  align-items: center !important;
}

.vc-container {
  border: 1px solid #e8e8e8 !important;
}

.badge-outline-primary {
  background: none;
  border: 1px solid $primary;
  color: $primary
}

;

.badge-outline-secondary {
  background: none;
  border: 1px solid var(--secondary);
  color: var(--secondary)
}

;

.badge-outline-success {
  background: none;
  border: 1px solid var(--success);
  color: var(--success)
}

;

.badge-outline-danger {
  background: none;
  border: 1px solid var(--danger);
  color: var(--danger)
}

;

.badge-outline-warning {
  background: none;
  border: 1px solid var(--warning);
  color: var(--warning)
}

;

.badge-outline-info {
  background: none;
  border: 1px solid var(--info);
  color: var(--info)
}

;

.badge-outline-light {
  background: none;
  border: 1px solid var(--light);
  color: var(--light)
}

;

.badge-outline-dark {
  background: none;
  border: 1px solid var(--dark);
  color: var(--dark)
}

;


.custom-stepper {
  margin: 15px 25px;

  .stepper-icon {
    border-radius: 100%;
    height: 50px;
    width: 50px;
    margin: 10px auto;
    padding: 0;
    color: #fff;
    align-items: center;

    i {
      padding: 17px;
    }
  }

}


///////////////////// Loader Css /////////////////////

.loader-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 9999;
}

.sk-chase {
  width: 60px;
  height: 60px;
  margin: auto;
  position: relative;
  animation: sk-chase 2.5s infinite linear both;
}

.sk-chase-dot {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  animation: sk-chase-dot 2.0s infinite ease-in-out both;
}

.sk-chase-dot:before {
  content: '';
  display: block;
  width: 25%;
  height: 25%;
  background-color: $primary;
  border-radius: 100%;
  animation: sk-chase-dot-before 2.0s infinite ease-in-out both;
}

.sk-chase-dot:nth-child(1) {
  animation-delay: -1.1s;
}

.sk-chase-dot:nth-child(2) {
  animation-delay: -1.0s;
}

.sk-chase-dot:nth-child(3) {
  animation-delay: -0.9s;
}

.sk-chase-dot:nth-child(4) {
  animation-delay: -0.8s;
}

.sk-chase-dot:nth-child(5) {
  animation-delay: -0.7s;
}

.sk-chase-dot:nth-child(6) {
  animation-delay: -0.6s;
}

.sk-chase-dot:nth-child(1):before {
  animation-delay: -1.1s;
}

.sk-chase-dot:nth-child(2):before {
  animation-delay: -1.0s;
}

.sk-chase-dot:nth-child(3):before {
  animation-delay: -0.9s;
}

.sk-chase-dot:nth-child(4):before {
  animation-delay: -0.8s;
}

.sk-chase-dot:nth-child(5):before {
  animation-delay: -0.7s;
}

.sk-chase-dot:nth-child(6):before {
  animation-delay: -0.6s;
}

@keyframes sk-chase {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes sk-chase-dot {

  80%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes sk-chase-dot-before {
  50% {
    transform: scale(0.4);
  }

  100%,
  0% {
    transform: scale(1.0);
  }
}

.ql-editor {
  min-height: 300px !important;
}



#sidenav-main {
  overflow-x: hidden;
}

.ps__thumb-y {
  background-color: $primary;
}

.ps__rail-y:hover>.ps__thumb-y,
.ps__rail-y:focus>.ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
  background-color: $primary;
}

.custom-table {
  border-collapse: collapse;
  border-radius: 0.25em;
  overflow: hidden;

  tbody {
    td {
      white-space: normal;
    }
  }
}

.back_to_wordpress {
  bottom: 0;
  position: fixed;
  display: flex;
  overflow: hidden;
  transition: width 0.3s;
  background: #fff;
  width: 249px;
}

.g-sidenav-hidden:not(.g-sidenav-show) .sidenav .collapse .sidenav-normal {
  display: none !important;
}

.navbar-vertical .navbar-nav .nav-item.sub-toogle a:after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font-family: Font Awesome\ 5 Free;
  font-weight: 700;
  content: "\F105";
  margin-left: auto;
  color: #8898aa;
  transition: all .15s ease
}

.navbar-vertical .navbar-nav .nav-item[aria-expanded=true] a:after {
  color: #0a48b3;
  transform: rotate(90deg);
}

.guide {
  font-size: 8px;
  background: #97aee6;
  padding: 2px 5px;
  color: #ffffff;
  border-radius: 100%;
  vertical-align: middle;
}

.tpd-content {
  max-width: 300px;
}

.vc-bg-blue-600 {
  background-color: $primary !important;
}

.session_slots {
  overflow: hidden;
  overflow-y: auto;
  margin-bottom: 15px;
  padding: 15px;
  padding-right: 0;
}

.time-slot {
  border: 1px solid #ccc9c9;
  margin: auto;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.time-slot .session_slots .form-group {
  text-align: center;
}

.widget_session_slots {
  max-height: 268px;
  min-height: 268px;
  overflow: hidden;
  overflow-y: auto;
  margin-bottom: 15px;
}

.widget_session_slots .badge-custom {
  display: inline-block;
  width: 80px;
}

.session_slots .badge-custom {
  display: inline-block;
  width: 80px;
}

.cursor {
  cursor: pointer;
}

.field-icon {
  float: right;
  margin-left: -25px;
  margin-top: -28px;
  position: relative;
  z-index: 2;
  padding-right: 25px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.vc-leading-tight {
  line-height: 1.75 !important;
}

.dropdown.drop-down {
  width: 100% !important;

  .select-list {
    width: 100% !important;
  }
}


.date-picker.form-control:disabled,
.date-picker.form-control[readonly] {
  background-color: transparent !important;
}

// 24-10-2020
._horizontal-scroll {
  .d-inline-block {
    width: 32.33%;
    white-space: break-spaces;
  }
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  border-radius: 5px;
}

::-webkit-scrollbar-track {
  background: #CFD8DC;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: #90A4AE;
  border-radius: 5px;
}

.header-search {
  .search-field {
    padding: 5px 15px;
  }
}

// Widget custom css...
.kivi-details {
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 170px;
}

.kivi-doctor-name {
  display: inline-block;
}

.g-sidenav-show .sidenav .navbar-brand {
  display: block !important;
}

.custom-alert {
  width: 100%;
}

.custom-alert {
  .alert.alert-warning {
    color: #674e37 !important;
    background-color: #fff3cd !important;
    border-color: #ffeeba !important;
  }

  .alert {
    font-size: 1.0rem;
  }

  .alert-heading {
    font-size: 1.050rem;
  }

  a:hover {
    color: #674e37 !important;
  }

  h3 {
    color: #674e37 !important;
  }

  h2 {
    font-size: 1.125rem !important;
  }

  .note {
    color: red !important;
  }

  li {
    font-weight: 300;
  }

  p {
    font-weight: 400;
  }
}

.kc-appointment-card {
  .card {
    box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .18) !important;
  }
}

.el-table td,
.el-table th {
  padding: 5px 0 !important;
}

.doctor-session-tbl-blank {
  min-height: 200px;
}

.navbar {
  padding: 0rem 0rem !important;
}

.card-profile-image {
  // position: absolute !important;
}

.card-profile-image {
  &.img-size {
    img {
      //position: static;
      left: 50%;
      top: 50px;
      max-width: 100px;
      border: 3px solid #fff;
      border-radius: .375rem;
    }
  }
}

.card-profile .kc-profile>input>span {
  display: block;
  text-align: center;
  margin-top: 5px;
}

#clinicForm .card-profile-image.img-size img,
.card-profile-image.img-size img {
  position: relative;
}

.card-profile-image img {
  //position: static;
  left: 50%;
  max-width: 160px;
  border: 3px solid #fff;
  border-radius: .375rem;
}

#uploadFile .card-profile-image img,
#uploadFile .card-profile-image img:hover {
  transform: none !important;
}

.spinner[data-v-fa81853e]:after {
  background: $primary !important;
}

.page-loader-section {
  height: 223px;
  align-items: center;
  display: flex;
}

.loader-2-container {
  display: contents;
}

.multiselect__tags,
.form-control {
  border: 1px solid #d0cece;
}

.datepicker_clear_btn {
  position: absolute;
  right: 15px;
  top: 0;
  bottom: 0;
  height: 36px;
  cursor: pointer;
  margin: auto;
  font-size: 19px;
  font-weight: 700;
  color: #cdd5dd;
  padding: 5px;
}


.appointment_select_clear_btn {
  position: absolute;
  right: 40px;
  top: 30px;
  bottom: 0;
  height: 36px;
  cursor: pointer;
  margin: auto;
  font-size: 19px;
  font-weight: 700;
  color: #cdd5dd;
  padding: 5px;
}

.select_clear_btn {
  position: absolute;
  right: 40px;
  top: 0;
  bottom: 0;
  height: 36px;
  cursor: pointer;
  margin: auto;
  font-size: 19px;
  font-weight: 700;
  color: #cdd5dd;
  padding: 5px;
}

.kivi-pr {
  position: relative;
}

.vue__time-picker input.display-time {
  height: auto;
  padding: 0;
  border: none;
}

.settings-success {
  display: none;
}

#doctor-session-tab .custom-checkbox .custom-control-input~.custom-control-label {
  font-size: 16px;
  line-height: unset !important;
}

.notice {
  display: none;
}

.sticky-header {
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  z-index: 1;

}

.sticky-header.active {
  box-shadow: 0 0 2rem 0 rgba(136, 152, 170, 0.15) !important;
}

.print {
  display: none;
}

@media (min-width: 1024px) {
  header {
    min-height: auto;
    // border-bottom:2px solid black;

  }
}

.card-header .card-header-pills.nav-fill {
  padding: 0 15px;
}

.kc-profile ::-webkit-file-upload-button {
  display: block;
  margin: 0 auto;
}

.kc-profile ::-webkit-file-upload-button span {
  text-align: center;
  display: block;
  margin-top: 5px;
}

.btn-primary i {
  padding: 5px;
}

.table td,
.table th {
  overflow: hidden;
}

.table-fixed {
  background-color: #fbfbfb;
  width: 100%;
}

.table-fixed tbody {
  height: 50vh;
  overflow-y: auto;
  width: 100%;
}

.table-fixed thead,
.table-fixed tbody,
.table-fixed tr,
.table-fixed td,
.table-fixed th {
  display: block;
}

.table-fixed tbody td {
  float: left;
}

.table-fixed thead tr th {
  background-color: #159bd0;
  border-color: #0881b1;
  float: left;
  color: #fff;
}

.read_article {
  text-align: center;
}

.custom-cal-badge .badge a {
  color: #000000 !important;
  font-weight: bold;
}

@media print {
  .print {
    display: initial;
  }
}


@import "./customizer-components/components";
@import "./rtl-custom";


/*** Impact dashboard override **/

.nav-pills .nav-link {
  color: var(--primary) !important;
}

.text-nowrap {
  color: var(--primary) !important;
}

/*a{
  color: var(--primary) !important;
}*/

.badge-outline-primary {
  color: var(--primary) !important;
}

.badge-primary {
  color: var(--primary) !important;
  background-color: #ebe9eb;
}

.btn-outline-primary {
  color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.btn-outline-primary:hover {
  color: var(--white) !important;
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
  color: var(--white) !important;
  background-color: var(--primary) !important;
}

.footer .copyright a {
  color: var(--primary) !important;
}

.kivicare_get_help a {
  color: var(--primary) !important;
}


.md-episide-tabdiv{
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.md-inner-episode-tabdiv{
  padding: 5px 10px;
  background-color: #b3e9ca;
  color: #21a359;
  border-radius: 10px;
  cursor: pointer;
}
.kivicare_get_help .kivicare_external_link {
  color: var(--white) !important;
  background-color: var(--primary) !important;
}

.ps__thumb-y {
  background-color: var(--primary) !important;
}

.form-control:focus {
  border-color: var(--primary) !important;
}

.vc-highlights .vc-day-layer span {
  background-color: var(--primary) !important;
}

.custom-control-input::after {
  background-color: var(--primary) !important;
}

.vgt-input:focus {
  border: 1px solid var(--primary) !important;
}

// [type="radio"]:checked{
//   background-color: var(--primary) !important;
// }
#uploadFile .card-profile-image img {
  position: relative;
}

element.style {}

.custom-radio .custom-control-input~.custom-control-label {
  cursor: pointer;
  font-size: 0.7875rem;
  height: 1rem;
}

.custom-control-label {
  margin-bottom: 0;
}

.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

user agent stylesheet label {
  cursor: default;
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.375rem;
}

body {
  margin: 0;
  font-family: "Poppins", sans-serif;
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.6;
  color: #525f7f;
  text-align: left;
  background-color: #f8fafc;
}

style attribute {}

:root {
  --blue: #3490dc;
  --indigo: #6574cd;
  --purple: #9561e2;
  --pink: #f66d9b;
  --red: #e3342f;
  --orange: #f6993f;
  --yellow: #ffed4a;
  --green: #38c172;
  --teal: #4dc0b5;
  --cyan: #6cb2eb;
  --white: #fff;
  --gray: #8898aa;
  --gray-dark: #525f7f;
  --light: #ced4da;
  --lighter: #e9ecef;
  --primary: #000000;
  --secondary: #f68685;
  --success: #38c172;
  --info: #4dc0b5;
  --warning: #f6993f;
  --danger: #e3342f;
  --light: #adb5bd;
  --dark: #212529;
  --default: #8898aa;
  --white: #fff;
  --neutral: #fff;
  --darker: black;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: "Poppins", sans-serif;
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --background-color: #F8FAFC;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.custom-radio .custom-control-input:checked~.custom-control-label::before {
  border-color: #000000;
}

.custom-control-input:checked~.custom-control-label::before {
  border-color: var(--primary) !important;
  background-color: var(--primary) !important;
}

.custom-control .custom-switch .b-custom-control-lg .custom-control-input {
  border-color: var(--primary) !important;
  background-color: var(--primary) !important;
}

.custom-control-input {
  border-color: var(--primary) !important;
}

.list-group-item a {
  color: var(--primary) !important;
}

.vue__time-picker .dropdown ul li:not([disabled]).active,
.vue__time-picker .dropdown ul li:not([disabled]).active:focus,
.vue__time-picker .dropdown ul li:not([disabled]).active:hover {
  background-color: var(--primary) !important;
}

.multiselect__option--highlight {
  background-color: var(--primary) !important;
}

#appointmentForm a {
  color: var(--primary) !important;
}

.vgt-select:focus {
  border-color: var(--primary) !important;
}

.custom-select:focus {
  border-color: var(--primary) !important;
}

.btn-link {
  color: var(--primary) !important;
}

.custom-toggle input:checked+.custom-toggle-slider {
  border: 1px solid var(--primary) !important;
  background-color: var(--primary) !important;
}

.custom-toggle input:checked+.custom-toggle-slider:after {
  color: white;
}

.custom-toggle input:checked+.custom-toggle-slider:before {
  background-color: white;
}

// .kivicare_settings_custom_field_checkbox:checked{
//   background-color: var(--primary) !important;
// }

.kivicare_settings_custom_field_checkbox .kivicare_settings_custom_field_checkbox:checked~.kivicare_settings_custom_field_checkbox::before {
  background-color: var(--primary) !important;
}

.vgt-global-search {
  padding: 5px !important;
}

::selection {
  background: var(--primary);
  text-shadow: #fff;
  color: #fff;
}

button:focus {
  // background-color: yellow !important;
  box-shadow: 0 7px 14px rgb(50 50 93 / 15%), 0 3px 6px rgb(0 0 0 / 15%);
  transform: translateY(-1px);
}

.nav-link.disabled {
  color: #bbb !important;
}

.navbar-light .kivicare-navbar-inner .navbar-nav .nav-link {
  color: rgb(0, 0, 0);
}

#dashboard-setting-panel ul li a.nav-link {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@media screen and (max-width:524px) {
  #dashboard-setting-panel ul li a.nav-link {
    justify-content: center;
  }

  #dashboard-setting-panel ul li a span {
    display: none;
  }
}

.rating {
  border-radius: 8px;

  .list {
    padding: 0;
    margin: 0 20px 0 0;

    &:hover {
      .star {
        color: var(--primary);
      }
    }

    .star {
      display: inline-block;
      font-size: 42px;
      transition: all .2s ease-in-out;
      cursor: pointer;

      &:hover {
        ~.star:not(.active) {
          color: inherit;
        }
      }

      &:first-child {
        margin-left: 0;
      }

      &.active {
        color: var(--primary);
      }
    }
  }
}

.kivi-star {
  font-size: 16px;
}


.kivi-star[data-star] {
  text-align: left;
  font-style: normal;
  display: inline-block;
  position: relative;
  unicode-bidi: bidi-override;
}

.kivi-star[data-star]::before {
  display: block;
  content: '★ ★ ★ ★ ★';
  color: var(--default);
}

.kivi-star[data-star]::after {
  white-space: nowrap;
  position: absolute;
  top: 0;
  left: 0;
  content: '★ ★ ★ ★ ★';
  width: 0;
  color: var(--primary);
  overflow: hidden;
  height: 100%;
}

.kivi-star[data-star^="0.2"]::after,
[data-star^=".2"]::after {
  width: 4%
}

.kivi-star[data-star^="0.3"]::after,
[data-star^=".3"]::after {
  width: 6%
}

.kivi-star[data-star^="0.1"]::after,
[data-star^=".1"]::after {
  width: 2%
}

.kivi-star[data-star^="0.4"]::after,
[data-star^=".4"]::after {
  width: 8%
}

.kivi-star[data-star^="0.5"]::after,
[data-star^=".5"]::after {
  width: 10%
}

.kivi-star[data-star^="0.6"]::after,
[data-star^=".6"]::after {
  width: 12%
}

.kivi-star[data-star^="0.7"]::after,
[data-star^=".7"]::after {
  width: 14%
}

.kivi-star[data-star^="0.8"]::after,
[data-star^=".8"]::after {
  width: 16%
}

.kivi-star[data-star^="0.9"]::after,
[data-star^=".9"]::after {
  width: 18%
}

.kivi-star[data-star^="1"]::after {
  width: 20%
}

.kivi-star[data-star^="1.1"]::after {
  width: 22%
}

.kivi-star[data-star^="1.2"]::after {
  width: 24%
}

.kivi-star[data-star^="1.3"]::after {
  width: 26%
}

.kivi-star[data-star^="1.4"]::after {
  width: 28%
}

.kivi-star[data-star^="1.5"]::after {
  width: 30%
}

.kivi-star[data-star^="1.6"]::after {
  width: 32%
}

.kivi-star[data-star^="1.7"]::after {
  width: 34%
}

.kivi-star[data-star^="1.8"]::after {
  width: 36%
}

.kivi-star[data-star^="1.9"]::after {
  width: 38%
}

.kivi-star[data-star^="2"]::after {
  width: 40%
}

.kivi-star[data-star^="2.1"]::after {
  width: 42%
}

.kivi-star[data-star^="2.2"]::after {
  width: 44%
}

.kivi-star[data-star^="2.3"]::after {
  width: 46%
}

.kivi-star[data-star^="2.4"]::after {
  width: 48%
}

.kivi-star[data-star^="2.5"]::after {
  width: 50%
}

.kivi-star[data-star^="2.6"]::after {
  width: 52%
}

.kivi-star[data-star^="2.7"]::after {
  width: 54%
}

.kivi-star[data-star^="2.8"]::after {
  width: 56%
}

.kivi-star[data-star^="2.9"]::after {
  width: 58%
}

.kivi-star[data-star^="3"]::after {
  width: 60%
}

.kivi-star[data-star^="3.1"]::after {
  width: 62%
}

.kivi-star[data-star^="3.2"]::after {
  width: 64%
}

.kivi-star[data-star^="3.3"]::after {
  width: 66%
}

.kivi-star[data-star^="3.4"]::after {
  width: 68%
}

.kivi-star[data-star^="3.5"]::after {
  width: 70%
}

.kivi-star[data-star^="3.6"]::after {
  width: 72%
}

.kivi-star[data-star^="3.7"]::after {
  width: 74%
}

.kivi-star[data-star^="3.8"]::after {
  width: 76%
}

.kivi-star[data-star^="3.9"]::after {
  width: 78%
}

.kivi-star[data-star^="4"]::after {
  width: 80%
}

.kivi-star[data-star^="4.1"]::after {
  width: 82%
}

.kivi-star[data-star^="4.2"]::after {
  width: 84%
}

.kivi-star[data-star^="4.3"]::after {
  width: 86%
}

.kivi-star[data-star^="4.4"]::after {
  width: 88%
}

.kivi-star[data-star^="4.5"]::after {
  width: 90%
}

.kivi-star[data-star^="4.6"]::after {
  width: 92%
}

.kivi-star[data-star^="4.7"]::after {
  width: 94%
}

.kivi-star[data-star^="4.8"]::after {
  width: 96%
}

.kivi-star[data-star^="4.9"]::after {
  width: 98%
}

.kivi-star[data-star^="5"]::after {
  width: 100%
}

.get-pro-button-container {
  height: 50px;
  position: relative;
}

.get-pro-button {
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.get-pro-image img {
  width:100%;
}

.screen-reader-text{
  position: absolute;
  top: -10000em;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
table.vgt-table td{
  vertical-align: middle !important;
}

.iq-role-permission-accordion .iq-btn{
  color: rgb(255, 0, 0);
}

// .fc-license-message {
//   display: none;
// }

[dir="rtl"] .input-tel__label{
  left: auto;
  right: 13px;
}

[dir="rtl"] .input-tel__clear{
  right: auto !important;
  left: 8px;
}

.acf-browser-firefox [type=date]{
 background: none !important;
}

[dir="rtl"] .acf-browser-firefox [type=date]{
 background: none;
}

[dir="rtl"] [type="date"]::-webkit-calendar-picker-indicator{
  position: absolute;
  left: 24px;
}

#payment-status-section .custom-radio .custom-control-label{
  line-height: 1.8;
}

.object-fit-cover, 
.object-cover{
  object-fit: cover;
}