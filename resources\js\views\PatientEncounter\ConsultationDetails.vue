# ConsultationDetails.vue
<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-full max-w-3xl mx-4">
      <div class="p-6 pt-0">
        <div class="p-4">
          <!-- Header Section -->
          <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-purple-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path>
                  <rect x="2" y="6" width="14" height="12" rx="2"></rect>
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-lg">{{ consultation.doctor_name }}</h3>
                <p class="text-gray-600">{{ consultation.specialty }}</p>
                <div class="flex items-center space-x-2 mt-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                    <path d="M16 2v4"></path>
                    <path d="M8 2v4"></path>
                    <path d="M3 10h18"></path>
                  </svg>
                  <span class="text-sm text-gray-600">{{ formatDate(consultation.encounter_date) }}</span>
                  <span class="text-sm text-gray-400">•</span>
                  <span :class="[
                    'text-sm px-2 py-0.5 rounded-full',
                    consultation.status === '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]">
                    {{ consultation.status === '1' ? 'Active' : 'Closed' }}
                  </span>
                </div>
              </div>
            </div>
            <div class="mt-4 md:mt-0">
              <button
                @click="$emit('close')"
                class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>

          <!-- Details Grid -->
          <div class="mt-4 pt-4 border-t">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Consultation Notes -->
              <div>
                <h4 class="font-semibold mb-2 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M10 9H8"></path>
                    <path d="M16 13H8"></path>
                    <path d="M16 17H8"></path>
                  </svg>
                  Consultation Notes
                </h4>
                <p class="text-gray-600 text-sm">
                  {{ consultation.notes || 'No consultation notes available.' }}
                </p>
              </div>

              <!-- Prescriptions -->
              <div>
                <h4 class="font-semibold mb-2">Prescriptions</h4>
                <template v-if="consultation.prescriptions && consultation.prescriptions.length">
                  <div v-for="(prescription, index) in consultation.prescriptions" 
                       :key="index" 
                       class="text-sm text-gray-600 flex items-center space-x-2">
                    <span>• {{ prescription }}</span>
                  </div>
                </template>
                <p v-else class="text-sm text-gray-600">No prescriptions issued</p>
              </div>

              <!-- Attachments -->
              <div>
                <h4 class="font-semibold mb-2">Attachments</h4>
                <template v-if="consultation.attachments && consultation.attachments.length">
                  <button
                    v-for="(attachment, index) in consultation.attachments"
                    :key="index"
                    class="flex items-center space-x-2 text-sm text-purple-600 hover:text-purple-700 mb-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="7 10 12 15 17 10"></polyline>
                      <line x1="12" x2="12" y1="15" y2="3"></line>
                    </svg>
                    <span>{{ attachment.name }}</span>
                  </button>
                </template>
                <p v-else class="text-sm text-gray-600">No attachments available</p>
              </div>

              <!-- Satisfaction Rating -->
              <div>
                <h4 class="font-semibold mb-2">Satisfaction Rating</h4>
                <div class="flex items-center space-x-1">
                  <svg
                    v-for="n in 5"
                    :key="n"
                    xmlns="http://www.w3.org/2000/svg"
                    :class="[
                      'w-4 h-4',
                      n <= (consultation.rating || 0) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                    ]"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConsultationDetails',
  
  props: {
    consultation: {
      type: Object,
      required: true
    }
  },

  methods: {
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  }
};
</script>