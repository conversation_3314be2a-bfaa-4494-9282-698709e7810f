<?php
/**
 * Static HTML version of the Enhanced Booking Widget for testing
 */
?>
<div class="kivi-booking-widget">
    <div class="kivi-booking-container">
        <!-- Booking Widget Header with Step Indicator -->
        <div class="kivi-booking-header">
            <div class="kivi-step-indicator">
                <div class="kivi-step active" data-step="clinic">
                    <div class="kivi-step-dot">1</div>
                    <div class="kivi-step-label">Select Clinic</div>
                </div>
                <div class="kivi-step" data-step="category">
                    <div class="kivi-step-dot">2</div>
                    <div class="kivi-step-label">Select Category</div>
                </div>
                <div class="kivi-step" data-step="services">
                    <div class="kivi-step-dot">3</div>
                    <div class="kivi-step-label">Select Services</div>
                </div>
                <div class="kivi-step" data-step="datetime">
                    <div class="kivi-step-dot">4</div>
                    <div class="kivi-step-label">Date & Time</div>
                </div>
                <div class="kivi-step" data-step="details">
                    <div class="kivi-step-dot">5</div>
                    <div class="kivi-step-label">Patient Details</div>
                </div>
                <div class="kivi-step" data-step="confirm">
                    <div class="kivi-step-dot">6</div>
                    <div class="kivi-step-label">Confirmation</div>
                </div>
            </div>
        </div>

        <!-- Booking Widget Body -->
        <div class="kivi-booking-body">
            <!-- Clinic Step -->
            <div class="kivi-booking-step" id="step-clinic">
                <h2 class="kivi-step-title">Select Clinic</h2>
                <p class="kivi-step-subtitle">Choose the clinic where you'd like to book your appointment.</p>
                
                <div class="kivi-clinic-list">
                    <div class="kivi-clinic-card selected">
                        <div class="kivi-clinic-card-body">
                            <h3 class="kivi-clinic-name">Main Medical Center</h3>
                            <p class="kivi-clinic-address">123 Health Street, Medical District, City</p>
                        </div>
                    </div>
                    
                    <div class="kivi-clinic-card">
                        <div class="kivi-clinic-card-body">
                            <h3 class="kivi-clinic-name">Downtown Clinic</h3>
                            <p class="kivi-clinic-address">456 Wellness Avenue, Downtown, City</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Widget Footer -->
        <div class="kivi-booking-footer">
            <button type="button" class="kivi-btn kivi-btn-secondary kivi-btn-disabled">Previous</button>
            <button type="button" class="kivi-btn kivi-btn-primary">Next</button>
        </div>
    </div>
</div>

<style>
/* Base styles for the booking widget */
:root {
  --primary-color: #4F46E5;
  --primary-color-hover: #4338CA;
  --secondary-color: #10B981;
  --secondary-color-hover: #059669;
  --danger-color: #EF4444;
  --danger-color-hover: #DC2626;
  --light-gray: #F3F4F6;
  --gray: #6B7280;
  --dark-gray: #374151;
  --black: #111827;
  --white: #FFFFFF;
  --radius: 0.5rem;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.kivi-booking-widget {
  font-family: var(--font-family);
  color: var(--dark-gray);
  max-width: 1024px;
  margin: 0 auto;
}

.kivi-booking-widget * {
  box-sizing: border-box;
}

.kivi-booking-container {
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.kivi-booking-header {
  background-color: var(--light-gray);
  padding: 1rem;
  border-bottom: 1px solid rgba(229, 231, 235, 1);
}

.kivi-step-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  position: relative;
}

.kivi-step-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--light-gray);
  transform: translateY(-50%);
  z-index: 1;
}

.kivi-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.kivi-step-dot {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: var(--light-gray);
  border: 2px solid var(--gray);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--gray);
  transition: all 0.3s ease;
}

.kivi-step.active .kivi-step-dot {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.kivi-step.completed .kivi-step-dot {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--white);
}

.kivi-step-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--gray);
  text-align: center;
  max-width: 100px;
}

.kivi-step.active .kivi-step-label,
.kivi-step.completed .kivi-step-label {
  color: var(--black);
  font-weight: 600;
}

.kivi-booking-body {
  padding: 1.5rem;
  min-height: 400px;
}

.kivi-booking-footer {
  display: flex;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(229, 231, 235, 1);
  background-color: var(--light-gray);
}

.kivi-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  cursor: pointer;
}

.kivi-btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
}

.kivi-btn-primary:hover {
  background-color: var(--primary-color-hover);
}

.kivi-btn-secondary {
  background-color: var(--white);
  color: var(--dark-gray);
  border: 1px solid rgba(229, 231, 235, 1);
}

.kivi-btn-secondary:hover {
  background-color: var(--light-gray);
}

.kivi-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-clinic-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.kivi-clinic-card {
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.kivi-clinic-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.kivi-clinic-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
}

.kivi-clinic-card-body {
  padding: 1rem;
}

.kivi-clinic-name {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--black);
}

.kivi-clinic-address {
  font-size: 0.875rem;
  color: var(--gray);
  margin: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .kivi-booking-container {
    border-radius: 0;
  }

  .kivi-step-label {
    display: none;
  }
  
  .kivi-clinic-list {
    grid-template-columns: 1fr;
  }
}
</style>