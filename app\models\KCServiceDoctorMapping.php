<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCServiceDoctorMapping extends KCModel {

    public function __construct()
    {
        // Updated to use merged services table
        parent::__construct('services');
    }

	public function serviceUserPermission($service_id){
		$doctor_id = $this->get_var(
			[
				'id' => $service_id
			],
			'doctor_id'
		);
		
		// If mapping doesn't exist, allow admin users
		if (empty($doctor_id)) {
			$user_role = $this->getLoginUserRole();
			return $user_role === 'administrator' || $user_role === $this->getClinicAdminRole();
		}
		
		return (new KCUser())->doctorPermissionUserWise($doctor_id);
	}

}