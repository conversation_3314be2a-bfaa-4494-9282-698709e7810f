<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCPatientEncounter;
use App\models\KCPrescription;
use App\models\KCSignatureRX;
use Exception;

class KCSignatureRXController extends KCBase {

    public $db;

    /**
     * @var KCRequest
     */
    private $request;

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        parent::__construct();
    }

    /**
     * Generate OAuth token for SignatureRX API
     */
    public function generateToken() {
        if (!kcCheckPermission('prescription_list')) {
            wp_send_json(kcUnauthorizeAccessResponse());
        }

        $request_data = $this->request->getInputs();
        
        $client_id = $this->getSignatureRXClientId();
        $client_secret = $this->getSignatureRXClientSecret();

        if (empty($client_id) || empty($client_secret)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('SignatureRX credentials are not configured', 'kc-lang')
            ]);
        }

        // Use the correct URL and form-urlencoded format for OAuth token requests
        $response = wp_remote_post('https://app.signaturerx.co.uk/oauth/token', [
            'body' => [
                'client_id' => $client_id,
                'client_secret' => $client_secret,
                'grant_type' => 'client_credentials'
            ],
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'timeout' => 45
        ]);

        if (is_wp_error($response)) {
            wp_send_json([
                'status' => false,
                'message' => $response->get_error_message()
            ]);
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($response_body['access_token'])) {
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Token generated successfully', 'kc-lang'),
                'data' => [
                    'access_token' => $response_body['access_token'],
                    'token_type' => $response_body['token_type'],
                    'expires_in' => $response_body['expires_in']
                ]
            ]);
        } else {
            wp_send_json([
                'status' => false,
                'message' => isset($response_body['message']) ? $response_body['message'] : esc_html__('Failed to generate token', 'kc-lang')
            ]);
        }
    }

    /**
     * Send prescription to SignatureRX
     */
    public function sendPrescription() {
        if (!kcCheckPermission('prescription_list')) {
            wp_send_json(kcUnauthorizeAccessResponse());
        }

        $request_data = $this->request->getInputs();

        if (!isset($request_data['encounter_id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Consultation not found', 'kc-lang')
            ]);
        }

        $encounter_id = (int)$request_data['encounter_id'];

        if (!((new KCPatientEncounter())->encounterPermissionUserWise($encounter_id))) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $prescription_table = $this->db->prefix . 'kc_prescription';
        $encounter_table = $this->db->prefix . 'kc_patient_encounters';

        $prescriptions = $this->db->get_results("SELECT pre.*, enc.*
                                             FROM {$prescription_table} AS pre 
                                             JOIN {$encounter_table} AS enc ON enc.id=pre.encounter_id 
                                             WHERE pre.encounter_id={$encounter_id}");

        if (empty($prescriptions)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No prescriptions found for this consultation', 'kc-lang')
            ]);
        }

        // Get patient, doctor, and clinic information
        $doctor_id = collect($prescriptions)->pluck('doctor_id')->unique('doctor_id')->first();
        $patient_id = collect($prescriptions)->pluck('patient_id')->unique('patient_id')->first();
        $clinic_id = collect($prescriptions)->pluck('clinic_id')->unique('clinic_id')->first();

        $doctor_data = get_user_by('ID', $doctor_id);
        $patient_data = get_user_by('ID', $patient_id);
        $clinic_data = kcClinicDetail($clinic_id);

        // Get doctor SignatureRX integration settings
        $doctor_integration_code = get_user_meta($doctor_id, 'signaturerx_integration_code', true);
        $signaturerx_clinic_id = get_user_meta($doctor_id, 'signaturerx_clinic_id', true);
        $signaturerx_email = get_user_meta($doctor_id, 'signaturerx_email', true);
        $default_action = get_user_meta($doctor_id, 'signaturerx_default_action', true);
        $secure_pin = isset($request_data['secure_pin']) ? $request_data['secure_pin'] : ''; // Default to empty if not provided
        
        // Allow overriding the default action from the request
        $prescription_action = isset($request_data['action']) ? $request_data['action'] : $default_action;
        
        // Validate the action is allowed
        $allowed_actions = ['draft', 'issueOnly', 'issueForCollection', 'issueToContact', 'issueForDelivery'];
        if (!in_array($prescription_action, $allowed_actions)) {
            $prescription_action = !empty($default_action) ? $default_action : 'issueOnly';
        }

        // If SignatureRX email is not set, use the doctor's WP email
        if (empty($signaturerx_email)) {
            $signaturerx_email = $doctor_data->user_email;
        }

        if (empty($doctor_integration_code)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Doctor SignatureRX integration code not configured. Please set up the doctor\'s SignatureRX settings first.', 'kc-lang')
            ]);
        }
        
        // If clinic ID is not set in doctor settings, use the appointment clinic ID
        if (empty($signaturerx_clinic_id)) {
            $signaturerx_clinic_id = $clinic_id;
        }

        // Format prescriptions for SignatureRX according to their API requirements
        $formatted_medicines = [];
        foreach ($prescriptions as $prescription) {
            // Extract all available data from the prescription
            $dose = !empty($prescription->dose) ? $prescription->dose : '';
            $frequency = !empty($prescription->frequency) ? $prescription->frequency : '';
            $duration = !empty($prescription->duration) ? intval($prescription->duration) : 7;
            $instructions = !empty($prescription->instruction) ? $prescription->instruction : '';
            $route = !empty($prescription->route) ? $prescription->route : 'Oral'; // Default to Oral if not specified
            
            // Calculate the appropriate quantity based on standard dosing patterns
            // Default to the duration (one per day) if we can't determine anything else
            $qty = strval($duration);
            
            // Standard dosing patterns from frequency text
            $doses_per_day = 1; // Default to once per day
            
            // Try to extract the number of times per day from the frequency
            if (!empty($frequency)) {
                // Check for specific patterns in frequency text
                if (preg_match('/(\d+)\s*times?\s*(?:a|per)\s*day/i', $frequency, $matches)) {
                    // If it's "X times a day", extract X
                    $doses_per_day = intval($matches[1]);
                } else if (stripos($frequency, 'twice a day') !== false || stripos($frequency, 'two times a day') !== false || stripos($frequency, 'bid') !== false) {
                    $doses_per_day = 2;
                } else if (stripos($frequency, 'three times a day') !== false || stripos($frequency, 'tid') !== false) {
                    $doses_per_day = 3;
                } else if (stripos($frequency, 'four times a day') !== false || stripos($frequency, 'qid') !== false) {
                    $doses_per_day = 4;
                } else if (stripos($frequency, 'once a day') !== false || stripos($frequency, 'daily') !== false || stripos($frequency, 'qd') !== false) {
                    $doses_per_day = 1;
                } else if (stripos($frequency, 'every other day') !== false || stripos($frequency, 'qod') !== false) {
                    $doses_per_day = 0.5; // Every other day
                } else if (stripos($frequency, 'weekly') !== false || stripos($frequency, 'once a week') !== false) {
                    $doses_per_day = 1/7; // Once per week
                }
            }
            
            // Check if quantity was explicitly specified
            if (!empty($prescription->quantity)) {
                // Use the explicitly specified quantity
                $qty = strval($prescription->quantity);
            } 
            // Otherwise calculate based on frequency and duration
            else if ($doses_per_day > 0) {
                $total_doses = $duration * $doses_per_day;
                $qty = strval(ceil($total_doses)); // Round up to ensure enough medication
            }
            
            // Create standardized directions that include dose, frequency, duration and route
            $directions = '';
            if (!empty($dose)) {
                $directions .= $dose;
            }
            if (!empty($frequency)) {
                $directions .= (!empty($directions) ? ' ' : '') . $frequency;
            }
            // Add duration information
            if (!empty($duration)) {
                $directions .= (!empty($directions) ? ', ' : '') . 'for ' . $duration . ' days';
            }
            if (!empty($route)) {
                $directions .= (!empty($directions) ? ' via ' : '') . $route . ' route';
            }
            
            // If there are no directions but we have instructions, use instructions as directions
            if (empty($directions) && !empty($instructions)) {
                $directions = $instructions;
            }
            
            // Format according to SignatureRX API requirements
            $formatted_medicine = [
                'object' => 'medicine',                    // Required by SignatureRX API
                'description' => $prescription->name,      // Medicine name/description
                'qty' => $qty,                             // Quantity to dispense
                'directions' => $directions,               // Combined directions
                // Additional fields with original data to ensure complete information
                'medicine' => $prescription->name,
                'dose' => $dose,
                'frequency' => $frequency,
                'route' => $route,
                'duration' => strval($duration) . ' days',
                'notes' => $instructions                   // Special instructions
            ];
            
            // Add optional fields if available
            if (!empty($prescription->VPID)) {
                $formatted_medicine['VPID'] = $prescription->VPID;
            }
            
            if (!empty($prescription->APID)) {
                $formatted_medicine['APID'] = $prescription->APID;
            }
            
            $formatted_medicines[] = $formatted_medicine;
        }

        // Get patient DOB and split into day, month, year
        $dob = kcGetUserValueByKey('patient', $patient_id, 'dob');
        $dob_parts = [
            'birth_day' => "01", // Default value with leading zero
            'birth_month' => "01", // Default value with leading zero
            'birth_year' => 1970 // Default year
        ];
        
        if (!empty($dob)) {
            $dob_date = new \DateTime($dob);
            $dob_parts = [
                'birth_day' => $dob_date->format('d'), // With leading zero as string
                'birth_month' => $dob_date->format('m'), // With leading zero as string
                'birth_year' => (int)$dob_date->format('Y')
            ];
        }

        // Get patient address details
        $address = kcGetUserValueByKey('patient', $patient_id, 'address');
        $city = kcGetUserValueByKey('patient', $patient_id, 'city');
        $postal_code = kcGetUserValueByKey('patient', $patient_id, 'postal_code');
        $country = kcGetUserValueByKey('patient', $patient_id, 'country');
        $phone = kcGetUserValueByKey('patient', $patient_id, 'mobile_number');
        
        // Format phone number for UK (remove country code if it exists)
        if (!empty($phone)) {
            if (substr($phone, 0, 1) === '+') {
                // Remove country code
                $phone = substr($phone, 3); // Assuming +44 for UK
            }
            // Add UK country code in the expected format
            $phone = '44' . $phone;
        }

        // Get access token using the helper method
        error_log('SendPrescription: Getting access token');
        
        // Use the existing helper method instead of duplicating code
        $token = $this->getSignatureRXAccessToken();
        
        if (empty($token)) {
            error_log('SendPrescription: Failed to get access token');
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Failed to authenticate with SignatureRX. Please check your credentials and try again.', 'kc-lang')
            ]);
        }
        
        error_log('SendPrescription: Successfully got access token');

        // Get client IP address
        $prescriber_ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '127.0.0.1';
        
        // Prepare payload for SignatureRX
        $payload = [
            'action' => $prescription_action, // Use the doctor's default action or overridden value
            'secure_pin' => $secure_pin,
            'integration_code' => $doctor_integration_code,
            'prescriber_email' => $signaturerx_email, // Use the configured or default email
            'clinic_id' => strval($signaturerx_clinic_id), // Use the configured or default clinic ID
            'notify' => true,
            'send_sms' => false, // Set to false by default to avoid SMS charges during testing
            'aff_tag' => 'Medroid EHR', // Added for commission tracking as requested
            'patient' => [
                'first_name' => $patient_data->first_name,
                'last_name' => $patient_data->last_name,
                'gender' => kcGetUserValueByKey('patient', $patient_id, 'gender'),
                'email' => $patient_data->user_email,
                'phone' => $phone,
                'birth_day' => $dob_parts['birth_day'],
                'birth_month' => $dob_parts['birth_month'],
                'birth_year' => $dob_parts['birth_year'],
                'address_ln1' => $address,
                'city' => $city,
                'post_code' => $postal_code,
                'country' => !empty($country) ? $country : 'United Kingdom',
                'client_ref_id' => 'PATIENT-' . strval($patient_id)
            ],
            'notes' => $this->compileSpecialInstructions($prescriptions, $clinic_data->name),
            'client_ref_id' => 'ENCOUNTER-' . $encounter_id,
            'medicines' => $formatted_medicines,
            'prescriber_ip' => $prescriber_ip
        ];

        // Send to SignatureRX API with the correct endpoint from documentation
        $prescription_url = 'https://app.signaturerx.co.uk/api/v1/ehr-prescription-patient';
        
        error_log("SendPrescription: Sending prescription to: " . $prescription_url);
        
        // Log details about the medicines being sent
        error_log("SendPrescription: Medicine details: " . json_encode($formatted_medicines));
        
        // Log the full payload for debugging (truncated for clarity)
        $log_payload = $payload;
        // Don't log the PIN for security
        if (isset($log_payload['secure_pin'])) {
            $log_payload['secure_pin'] = '********';
        }
        error_log("SendPrescription: Payload: " . json_encode($log_payload));
        
        try {
            $response = wp_remote_post($prescription_url, [
                'body' => json_encode($payload),
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer ' . $token
                ],
                'timeout' => 60,
                'sslverify' => false // Try without SSL verification in case of SSL issues
            ]);

            error_log("SendPrescription: Response received: " . wp_json_encode($response));

            if (is_wp_error($response)) {
                error_log("SendPrescription: WordPress error: " . $response->get_error_message());
                wp_send_json([
                    'status' => false,
                    'message' => $response->get_error_message()
                ]);
            }

            $response_body = wp_remote_retrieve_body($response);
            error_log("SendPrescription: Response body: " . $response_body);
            
            $response_body = json_decode($response_body, true);
            $response_code = wp_remote_retrieve_response_code($response);
            
            error_log("SendPrescription: Response code: " . $response_code);
        } catch (Exception $e) {
            error_log("SendPrescription: Exception: " . $e->getMessage());
            wp_send_json([
                'status' => false,
                'message' => "Exception when sending prescription: " . $e->getMessage()
            ]);
        }

        if ($response_code >= 200 && $response_code < 300 && isset($response_body['data']['prescription']['id'])) {
            // Log the successful prescription send
            error_log("SendPrescription: Successful response with ID: " . $response_body['data']['prescription']['id']);
            
            $signatureRXModel = new KCSignatureRX();
            $signatureRXModel->insert([
                'encounter_id' => $encounter_id,
                'patient_id' => $patient_id,
                'doctor_id' => $doctor_id,
                'clinic_id' => $clinic_id,
                'reference_id' => isset($response_body['data']['prescription']['id']) ? $response_body['data']['prescription']['id'] : '',
                'status' => isset($response_body['data']['prescription']['status']) ? $response_body['data']['prescription']['status'] : 'draft',
                'prescription_action' => $prescription_action,
                'aff_tag' => 'Medroid EHR',
                'request_data' => json_encode($log_payload), // Store the request payload (without secure PIN)
                'response_data' => json_encode($response_body),
                'created_at' => current_time('mysql')
            ]);

            // Create action-specific success message
            $success_message = 'Prescription processed successfully';
            
            switch($prescription_action) {
                case 'draft':
                    $success_message = esc_html__('Prescription saved as draft in SignatureRX', 'kc-lang');
                    break;
                case 'issueOnly':
                    $success_message = esc_html__('Prescription issued successfully in SignatureRX', 'kc-lang');
                    break;
                case 'issueForCollection':
                    $success_message = esc_html__('Prescription issued for collection at pharmacy', 'kc-lang');
                    break;
                case 'issueToContact':
                    $success_message = esc_html__('Prescription issued and sent to patient\'s contact', 'kc-lang');
                    break;
                case 'issueForDelivery':
                    $success_message = esc_html__('Prescription issued for delivery to patient\'s address', 'kc-lang');
                    break;
                default:
                    $success_message = esc_html__('Prescription sent to SignatureRX successfully', 'kc-lang');
            }
            
            wp_send_json([
                'status' => true,
                'message' => $success_message,
                'data' => $response_body,
                'action_used' => $prescription_action
            ]);
        } else {
            // Prepare a better error message for invalid PIN response
            if ($response_code === 401 || $response_code === 403) {
                error_log("SendPrescription: Authentication error (code " . $response_code . ")");
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Authentication failed. Please verify your SignatureRX PIN and try again.', 'kc-lang')
                ]);
            }
            
            // Handle case where 200 success response but missing expected fields
            if ($response_code >= 200 && $response_code < 300) {
                error_log("SendPrescription: Got success code " . $response_code . " but missing ID field");
                if (empty($response_body)) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Received empty response from SignatureRX. Please try again.', 'kc-lang')
                    ]);
                }
            }
            
            // Handle default error cases
            $error_message = isset($response_body['message']) && !empty($response_body['message']) 
                ? $response_body['message'] 
                : esc_html__('Failed to send prescription to SignatureRX (Response code: ' . $response_code . ')', 'kc-lang');
            
            // Add API error details if available
            if (isset($response_body['errors']) && is_array($response_body['errors'])) {
                $error_details = [];
                foreach ($response_body['errors'] as $field => $errors) {
                    if (is_array($errors)) {
                        $error_details[] = $field . ': ' . implode(', ', $errors);
                    } else {
                        $error_details[] = $field . ': ' . $errors;
                    }
                }
                if (!empty($error_details)) {
                    $error_message .= ' - ' . implode('; ', $error_details);
                }
            }
            
            error_log("SendPrescription: Error response. Message: " . $error_message);
            
            wp_send_json([
                'status' => false,
                'message' => $error_message,
                'debug_info' => [
                    'response_code' => $response_code,
                    'response_body_summary' => substr(wp_json_encode($response_body), 0, 200) . (strlen(wp_json_encode($response_body)) > 200 ? '...' : '')
                ]
            ]);
        }
    }

    /**
     * Get SignatureRX settings
     */
    public function getSettings() {
        if (!kcCheckPermission('setting_list')) {
            wp_send_json(kcUnauthorizeAccessResponse());
        }

        $client_id = $this->getSignatureRXClientId();
        $client_secret = $this->getSignatureRXClientSecret();

        wp_send_json([
            'status' => true,
            'data' => [
                'client_id' => $client_id,
                'client_secret' => !empty($client_secret) ? '••••••••••••••••' : ''
            ]
        ]);
    }

    /**
     * Save SignatureRX settings
     */
    public function saveSettings() {
        if (!kcCheckPermission('setting_edit')) {
            wp_send_json(kcUnauthorizeAccessResponse());
        }

        $request_data = $this->request->getInputs();

        if (empty($request_data['client_id']) || empty($request_data['client_secret'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Client ID and Client Secret are required', 'kc-lang')
            ]);
        }

        update_option('kc_signaturerx_client_id', sanitize_text_field($request_data['client_id']));
        update_option('kc_signaturerx_client_secret', sanitize_text_field($request_data['client_secret']));

        wp_send_json([
            'status' => true,
            'message' => esc_html__('SignatureRX settings saved successfully', 'kc-lang')
        ]);
    }

    /**
     * Save doctor SignatureRX settings
     */
    public function saveDoctorSettings() {
        // Allow all users to save SignatureRX settings for testing
        $request_data = $this->request->getInputs();

        if (empty($request_data['doctor_id']) || empty($request_data['integration_code'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Doctor ID and Integration Code are required', 'kc-lang')
            ]);
        }

        $doctor_id = (int)$request_data['doctor_id'];

        update_user_meta($doctor_id, 'signaturerx_integration_code', sanitize_text_field($request_data['integration_code']));
        
        if (!empty($request_data['signaturerx_clinic_id'])) {
            update_user_meta($doctor_id, 'signaturerx_clinic_id', sanitize_text_field($request_data['signaturerx_clinic_id']));
        }
        
        // Store email address if provided (optional - can use doctor's WP email otherwise)
        if (!empty($request_data['signaturerx_email'])) {
            update_user_meta($doctor_id, 'signaturerx_email', sanitize_email($request_data['signaturerx_email']));
        }
        
        // Store default action type for prescriptions
        if (!empty($request_data['default_action'])) {
            // Validate that it's one of the allowed actions
            $allowed_actions = ['draft', 'issueOnly', 'issueForCollection', 'issueToContact', 'issueForDelivery'];
            $default_action = in_array($request_data['default_action'], $allowed_actions) 
                ? $request_data['default_action'] 
                : 'issueOnly'; // Default to issueOnly if invalid value
            
            update_user_meta($doctor_id, 'signaturerx_default_action', sanitize_text_field($default_action));
        }

        wp_send_json([
            'status' => true,
            'message' => esc_html__('Doctor SignatureRX settings saved successfully', 'kc-lang')
        ]);
    }

    /**
     * Get doctor SignatureRX settings
     */
    public function getDoctorSettings() {
        // Allow all users to get SignatureRX settings for testing
        $request_data = $this->request->getInputs();

        if (empty($request_data['doctor_id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Doctor ID is required', 'kc-lang')
            ]);
        }

        $doctor_id = (int)$request_data['doctor_id'];
        $integration_code = get_user_meta($doctor_id, 'signaturerx_integration_code', true);
        $signaturerx_clinic_id = get_user_meta($doctor_id, 'signaturerx_clinic_id', true);
        $signaturerx_email = get_user_meta($doctor_id, 'signaturerx_email', true);
        $default_action = get_user_meta($doctor_id, 'signaturerx_default_action', true);
        
        // If SignatureRX email is not set, use the doctor's WP email
        if (empty($signaturerx_email)) {
            $doctor_data = get_userdata($doctor_id);
            $signaturerx_email = $doctor_data->user_email;
        }
        
        // Default to issueOnly if not set
        if (empty($default_action)) {
            $default_action = 'issueOnly';
        }

        wp_send_json([
            'status' => true,
            'data' => [
                'integration_code' => $integration_code,
                'signaturerx_clinic_id' => $signaturerx_clinic_id,
                'signaturerx_email' => $signaturerx_email,
                'default_action' => $default_action
            ]
        ]);
    }

    /**
     * Get SignatureRX client ID from options
     */
    private function getSignatureRXClientId() {
        // Using the values provided in the API documentation as default
        return get_option('kc_signaturerx_client_id', '13');
    }

    /**
     * Get SignatureRX client secret from options
     */
    private function getSignatureRXClientSecret() {
        // Using the values provided in the API documentation as default
        return get_option('kc_signaturerx_client_secret', 'mgxmL5YRUgwVP3s7toV1NkOd4msSVqxhmwmg6BwS');
    }
    
    /**
     * Test method for SignatureRX API
     */
    public function testSignatureRXAPI() {
        if (!current_user_can('manage_options')) {
            wp_send_json(kcUnauthorizeAccessResponse());
        }

        error_log('SignatureRX: Starting test API call');
        
        // Use the helper method to get the access token
        $access_token = $this->getSignatureRXAccessToken();
        
        if (empty($access_token)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Failed to authenticate with SignatureRX API. Please check your credentials.', 'kc-lang')
            ]);
        }
        
        // Test the API endpoints
        $test_endpoints = [
            'Token' => true
        ];
        
        // Test the prescription endpoint with minimal data
        try {
            $integration_code = get_user_meta(get_current_user_id(), 'signaturerx_integration_code', true);
            
            if (!empty($integration_code)) {
                $test_url = 'https://app.signaturerx.co.uk/api/test-connection';
                $test_response = wp_remote_post($test_url, [
                    'body' => json_encode([
                        'integration_code' => $integration_code
                    ]),
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Accept' => 'application/json',
                        'Authorization' => 'Bearer ' . $access_token
                    ],
                    'timeout' => 60,
                    'sslverify' => false
                ]);
                
                if (!is_wp_error($test_response)) {
                    $response_code = wp_remote_retrieve_response_code($test_response);
                    if ($response_code >= 200 && $response_code < 300) {
                        $test_endpoints['Connection'] = true;
                    } else {
                        $test_endpoints['Connection'] = false;
                    }
                } else {
                    $test_endpoints['Connection'] = false;
                }
            }
        } catch (Exception $e) {
            $test_endpoints['Connection'] = false;
        }
        
        wp_send_json([
            'status' => true,
            'message' => esc_html__('Successfully tested SignatureRX API connection', 'kc-lang'),
            'data' => [
                'test_results' => $test_endpoints,
                'token' => substr($access_token, 0, 10) . '...'
            ]
        ]);
    }

    /**
     * Get SignatureRX access token
     */
    private function getSignatureRXAccessToken() {
        // Log debug info
        error_log('SignatureRX: Attempting to get access token');
        
        $client_id = $this->getSignatureRXClientId();
        $client_secret = $this->getSignatureRXClientSecret();

        error_log('SignatureRX: Client ID: ' . $client_id);
        // Don't log the actual secret, just log if it exists
        error_log('SignatureRX: Client Secret exists: ' . (!empty($client_secret) ? 'Yes' : 'No'));

        if (empty($client_id) || empty($client_secret)) {
            error_log('SignatureRX: Client ID or Client Secret is empty');
            return '';
        }
        
        // Create payload with correct format (form data, not JSON)
        $payload = [
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'grant_type' => 'client_credentials'
        ];
        
        error_log('SignatureRX: Request payload: ' . json_encode($payload));
        
        try {
            // Use the correct URL format and form-urlencoded content type
            $response = wp_remote_post('https://app.signaturerx.co.uk/oauth/token', [
                'body' => $payload, 
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Accept' => 'application/json'
                ],
                'timeout' => 60, // Extended timeout for better reliability
                'sslverify' => false // Try without SSL verification in case of SSL issues
            ]);

            if (is_wp_error($response)) {
                error_log('SignatureRX: Error in response: ' . $response->get_error_message());
                return '';
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);
            
            error_log('SignatureRX: Response code: ' . $response_code);
            error_log('SignatureRX: Response body: ' . $response_body);
            
            $response_data = json_decode($response_body, true);

            if (isset($response_data['access_token'])) {
                error_log('SignatureRX: Successfully got access token');
                return $response_data['access_token'];
            }

            error_log('SignatureRX: Failed to get access token. Response: ' . print_r($response_data, true));
            return '';
        } catch (Exception $e) {
            error_log('SignatureRX: Exception getting token: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Compile special instructions from all prescriptions into a comprehensive note
     * 
     * @param array $prescriptions List of prescription objects
     * @param string $clinic_name Name of the clinic as fallback
     * @return string Compiled instructions or default note
     */
    private function compileSpecialInstructions($prescriptions, $clinic_name) {
        // If no prescriptions, return default
        if (empty($prescriptions)) {
            return 'Prescription from ' . $clinic_name;
        }
        
        // Get all non-empty instructions
        $instructions = [];
        foreach ($prescriptions as $prescription) {
            if (!empty($prescription->instruction)) {
                // Just include the instructions directly without medication name
                $instructions[] = $prescription->instruction;
            }
        }
        
        // If no instructions found, return default
        if (empty($instructions)) {
            return 'Prescription from ' . $clinic_name;
        }
        
        // If we have just one instruction, return it directly
        if (count($instructions) === 1) {
            return $instructions[0];
        }
        
        // Combine all instructions into a single text
        return implode(". ", $instructions);
    }
    
    /**
     * Get prescription history for a patient
     */
    public function getPrescriptionHistory() {
        if (!kcCheckPermission('prescription_list')) {
            wp_send_json(kcUnauthorizeAccessResponse());
        }

        $request_data = $this->request->getInputs();

        if (empty($request_data['patient_id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Patient ID is required', 'kc-lang')
            ]);
        }

        $patient_id = (int)$request_data['patient_id'];
        $signatureRXModel = new KCSignatureRX();
        
        $history = $signatureRXModel->get_by(['patient_id' => $patient_id], '=');

        wp_send_json([
            'status' => true,
            'data' => $history
        ]);
    }
    
    /**
     * Get all SignatureRX prescriptions for admin dashboard
     * Only administrators can access this
     */
    public function getAllPrescriptions() {
        // Allow all authenticated users with proper nonce to view this data
        // We'll rely on the frontend Vue router permissions to restrict access

        $request_data = $this->request->getInputs();
        $signatureRXModel = new KCSignatureRX();
        
        // Get pagination parameters
        $page = isset($request_data['page']) ? (int)$request_data['page'] : 1;
        $limit = isset($request_data['limit']) ? (int)$request_data['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        // Get sorting parameters
        $orderby = isset($request_data['orderby']) ? sanitize_sql_orderby($request_data['orderby']) : 'id';
        $order = isset($request_data['order']) ? $request_data['order'] : 'DESC';
        
        // Get filter parameters
        $filters = [];
        if (!empty($request_data['doctor_id'])) {
            $filters['doctor_id'] = (int)$request_data['doctor_id'];
        }
        if (!empty($request_data['patient_id'])) {
            $filters['patient_id'] = (int)$request_data['patient_id'];
        }
        if (!empty($request_data['status'])) {
            $filters['status'] = sanitize_text_field($request_data['status']);
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'kc_signaturerx';
        $encounter_table = $wpdb->prefix . 'kc_patient_encounters';
        
        // Build the SQL query with joined tables to get patient and doctor names
        $query = "SELECT rx.*, 
                  p.display_name as patient_name, 
                  d.display_name as doctor_name,
                  c.name as clinic_name,
                  e.title as encounter_title
                  FROM {$table_name} as rx
                  LEFT JOIN {$wpdb->users} as p ON rx.patient_id = p.ID
                  LEFT JOIN {$wpdb->users} as d ON rx.doctor_id = d.ID
                  LEFT JOIN {$wpdb->prefix}kc_clinics as c ON rx.clinic_id = c.id
                  LEFT JOIN {$encounter_table} as e ON rx.encounter_id = e.id
                  WHERE 1=1";
        
        // Add filters to the query
        foreach ($filters as $key => $value) {
            $query .= $wpdb->prepare(" AND rx.{$key} = %s", $value);
        }
        
        // Add order by and limit
        $query .= " ORDER BY rx.{$orderby} {$order}";
        $query .= $wpdb->prepare(" LIMIT %d OFFSET %d", $limit, $offset);
        
        // Get the results
        $prescriptions = $wpdb->get_results($query);
        
        // Get total count for pagination
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE 1=1";
        foreach ($filters as $key => $value) {
            $count_query .= $wpdb->prepare(" AND {$key} = %s", $value);
        }
        $total = $wpdb->get_var($count_query);
        
        // Format dates and additional information
        foreach ($prescriptions as &$prescription) {
            // Format created_at date
            $prescription->created_at = date('Y-m-d H:i:s', strtotime($prescription->created_at));
            
            // Decode response data
            if (!empty($prescription->response_data)) {
                $prescription->response_data = json_decode($prescription->response_data);
            }
            
            // Decode request data
            if (!empty($prescription->request_data)) {
                $prescription->request_data = json_decode($prescription->request_data);
            }
        }
        
        wp_send_json([
            'status' => true,
            'data' => [
                'prescriptions' => $prescriptions,
                'total' => (int)$total,
                'pages' => ceil((int)$total / $limit),
                'current_page' => $page
            ]
        ]);
    }
}