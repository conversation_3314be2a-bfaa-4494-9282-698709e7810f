<template>
  <div v-if="showModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-black bg-opacity-30 transition-opacity" aria-hidden="true" @click="closeModal">
      </div>

      <!-- Modal panel -->
      <div
        class="inline-block align-bottom bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- Header -->
        <div class="bg-black text-white px-4 py-3 flex justify-between items-center">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 class="text-lg font-medium" id="modal-title">
              Create New Task
            </h3>
          </div>
          <button @click="closeModal"
            class="text-white hover:bg-white hover:bg-opacity-10 rounded-full w-6 h-6 flex items-center justify-center transition-colors">
            ×
          </button>
        </div>

        <!-- Body -->
        <div class="bg-white px-6 py-4 max-h-[70vh] overflow-y-auto">
          <div v-if="loading" class="flex justify-center items-center p-6">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
            <span class="ml-3 text-gray-700">{{ formTranslation.common.loading || 'Loading...' }}</span>
          </div>

          <div v-else>
            <!-- Tabs - only show if not a new task -->
            <div v-if="!isNew" class="mb-4">
              <ul class="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <li class="flex-1" @click="activeTab = 0">
                  <button :class="[
                    'w-full py-2 px-3 text-sm font-medium rounded-md transition-all duration-200',
                    activeTab === 0
                      ? 'bg-white text-black shadow'
                      : 'text-gray-700 hover:bg-gray-200'
                  ]">
                    <i class="ri-file-list-line mr-1"></i> Details
                  </button>
                </li>
                <li class="flex-1" @click="activeTab = 1">
                  <button :class="[
                    'w-full py-2 px-3 text-sm font-medium rounded-md transition-all duration-200',
                    activeTab === 1
                      ? 'bg-white text-black shadow'
                      : 'text-gray-700 hover:bg-gray-200'
                  ]">
                    <i class="ri-chat-1-line mr-1"></i> Comments
                    <span v-if="commentCount" class="ml-1 px-2 py-0.5 text-xs rounded-full bg-black text-white">
                      {{ commentCount }}
                    </span>
                  </button>
                </li>
                <li class="flex-1" @click="activeTab = 2">
                  <button :class="[
                    'w-full py-2 px-3 text-sm font-medium rounded-md transition-all duration-200',
                    activeTab === 2
                      ? 'bg-white text-black shadow'
                      : 'text-gray-700 hover:bg-gray-200'
                  ]">
                    <i class="ri-attachment-2 mr-1"></i> Attachments
                    <span v-if="attachmentCount" class="ml-1 px-2 py-0.5 text-xs rounded-full bg-black text-white">
                      {{ attachmentCount }}
                    </span>
                  </button>
                </li>
              </ul>
            </div>

            <!-- Details Tab -->
            <div v-if="activeTab === 0" class="space-y-4">
              <!-- Title and Priority -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="md:col-span-2">
                  <div class="text-left mb-1.5">
                    <label for="task-title" class="block text-sm font-medium text-gray-700">
                      Title <span class="text-red-500">*</span>
                    </label>
                  </div>
                  <input type="text" id="task-title"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-black"
                    :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.title }" v-model="form.title"
                    placeholder="Enter task title" />
                </div>

                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Priority
                    </label>
                  </div>
                  <div class="flex gap-1">
                    <button type="button"
                      class="flex-1 py-2 px-2 text-sm font-medium rounded focus:outline-none transition-colors" :class="{
                        'bg-blue-600 text-white': form.priority === 'low',
                        'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50': form.priority !== 'low'
                      }" @click="form.priority = 'low'">
                      Low
                    </button>
                    <button type="button"
                      class="flex-1 py-2 px-2 text-sm font-medium rounded focus:outline-none transition-colors" :class="{
                        'bg-yellow-500 text-white': form.priority === 'medium',
                        'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50': form.priority !== 'medium'
                      }" @click="form.priority = 'medium'">
                      Medium
                    </button>
                    <button type="button"
                      class="flex-1 py-2 px-2 text-sm font-medium rounded focus:outline-none transition-colors" :class="{
                        'bg-red-600 text-white': form.priority === 'high',
                        'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50': form.priority !== 'high'
                      }" @click="form.priority = 'high'">
                      High
                    </button>
                  </div>
                </div>
              </div>

              <!-- Clinic, Patient, Status -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div v-if="this.getUserRole() == 'administrator'">
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Clinic <span class="text-red-500">*</span>
                    </label>
                  </div>
                  <div class="relative">
                    <multiselect v-model="form.clinic" :options="clinicOptions" track-by="id" label="label"
                      :searchable="true" placeholder="Select a clinic"
                      :class="{ 'multiselect-invalid': validationErrors.clinic_id }"
                      :loading="clinicOptions.length === 0" :internal-search="true" :show-labels="false">
                    </multiselect>
                  </div>
                </div>

                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Related Patient
                    </label>
                  </div>
                  <div class="relative">
                    <multiselect v-model="form.patient" :options="patientOptions" track-by="id" label="label"
                      :searchable="true" placeholder="Select a patient" :loading="patientMultiselectLoader"
                      :disabled="!form.clinic" :internal-search="true" :show-labels="false">
                    </multiselect>
                  </div>
                </div>

                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Status
                    </label>
                  </div>
                  <div class="relative">
                    <multiselect v-model="form.status_option" :options="statusOptions" track-by="id" label="label"
                      :searchable="false" placeholder="Select status">
                      <template slot="singleLabel" slot-scope="{ option }">
                        <div class="flex items-center">
                          <span class="w-3 h-3 rounded-full mr-2" :class="{
                            'bg-gray-500': option.id === 'pending',
                            'bg-black': option.id === 'in_progress',
                            'bg-green-500': option.id === 'completed',
                            'bg-red-500': option.id === 'cancelled'
                          }"></span>
                          <span>{{ option.label }}</span>
                        </div>
                      </template>
                    </multiselect>
                  </div>
                </div>
              </div>

              <!-- Due Date, Reminder, Repeating Schedule -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Due Date
                    </label>
                  </div>
                  <div class="relative">
                    <input type="date" v-model="form.due_date"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-black"
                      placeholder="DD/MM/YYYY" />
                  </div>
                </div>

                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Reminder
                    </label>
                  </div>
                  <div class="relative">
                    <input type="date" v-model="form.reminder_date"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-black"
                      placeholder="DD/MM/YYYY" />
                  </div>
                </div>

                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Repeating Schedule
                    </label>
                  </div>
                  <div class="relative">
                    <multiselect v-model="form.repeating_option" :options="repeatingOptions" track-by="id" label="label"
                      :searchable="false" placeholder="None">
                    </multiselect>
                  </div>
                </div>
              </div>

              <!-- Category, Assignees -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Category
                    </label>
                  </div>
                  <div class="relative">
                    <multiselect v-model="form.category_option" :options="categoryOptions" track-by="id" label="label"
                      :searchable="false" placeholder="Select a category">
                    </multiselect>
                  </div>
                </div>

                <div>
                  <div class="text-left mb-1.5">
                    <label class="block text-sm font-medium text-gray-700">
                      Assignees
                    </label>
                  </div>
                  <div class="relative">
                    <multiselect v-model="form.assignees" :options="userOptions" track-by="id" label="name"
                      :multiple="true" :searchable="true" placeholder="Select staff to assign to this task"
                      :internal-search="true">
                    </multiselect>
                    <p class="mt-1 text-xs text-gray-500">
                      Doctors, Receptionists, Admins
                    </p>
                  </div>
                </div>
              </div>

              <!-- Description -->
              <div>
                <div class="text-left mb-1.5">
                  <label class="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                </div>
                <textarea
                  class="block w-full px-3 py-2 rounded-md border border-gray-300 text-sm focus:outline-none focus:ring-1 focus:ring-black resize-y"
                  v-model="form.description" placeholder="Enter task description" rows="4"></textarea>
              </div>
            </div>

            <!-- Comments Tab -->
            <div v-if="activeTab === 1 && !isNew">
              <task-comments :task-id="taskId" @comment-added="onCommentAdded" />
            </div>

            <!-- Attachments Tab -->
            <div v-if="activeTab === 2 && !isNew">
              <task-attachments :task-id="taskId" @attachment-uploaded="onAttachmentUploaded" />
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex items-center justify-end gap-2 border-t border-gray-200">
          <button type="button"
            class="px-4 py-2 bg-white border border-gray-300 rounded text-gray-700 text-sm hover:bg-gray-50 focus:outline-none"
            @click="closeModal">
            Cancel
          </button>
          <button type="button"
            class="px-4 py-2 bg-black text-white rounded text-sm hover:bg-opacity-90 focus:outline-none"
            @click="saveTask" :disabled="saving">
            <svg v-if="saving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            <span>{{ saving ? (formTranslation.common.saving || 'Saving...') : 'Save' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

import Multiselect from 'vue-multiselect';
import TaskComments from './TaskComments.vue';
import TaskAttachments from './TaskAttachments.vue';

export default {
  name: 'TaskModal',
  components: {
    Multiselect,
    TaskComments,
    TaskAttachments
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: [Number, String],
      default: null
    },
    isNew: {
      type: Boolean,
      default: true
    },
    initialDate: {
      type: Date,
      default: null
    },
    taskData: { // Add this new prop
      type: Object,
      default: null
    }
  },
  data() {
    return {
      showModal: false,
      loading: false,
      saving: false,
      task: null,
      activeTab: 0,
      clinics: [],
      commentCount: 0,
      attachmentCount: 0,
      form: {
        title: '',
        description: '',
        clinic: null,
        patient: null,
        doctor: null,
        priority: 'medium',
        status_option: null,
        due_date: null,
        reminder_date: null,
        assignees: [],
        category_option: null,
        repeating_option: null
      },

      validationErrors: {},

      clinicOptions: [],
      patientOptions: [],
      doctorOptions: [],
      userOptions: [],
      patientMultiselectLoader: false,

      statusOptions: [
        { id: 'pending', label: 'Pending' },
        { id: 'in_progress', label: 'In Progress' },
        { id: 'completed', label: 'Completed' },
        { id: 'cancelled', label: 'Cancelled' }
      ],

      categoryOptions: [
        { id: 'administrative', label: 'Administrative' },
        { id: 'clinical', label: 'Clinical' },
        { id: 'patient-followup', label: 'Patient Follow-up' },
        { id: 'billing', label: 'Billing' },
        { id: 'general', label: 'General' }
      ],

      repeatingOptions: [
        { id: 'none', label: 'None' },
        { id: 'daily', label: 'Daily' },
        { id: 'weekly', label: 'Weekly' },
        { id: 'monthly', label: 'Monthly' }
      ]
    }
  },
  computed: {
    modalTitle() {
      return this.isNew
        ? (formTranslation.task.new_task || 'New Task')
        : (formTranslation.task.edit_task || 'Edit Task');
    },

    userData() {
      // Get user data from store, similar to AppointmentForm.vue
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return {};
      }
    },
    userClinicId() {
      return this.userData?.user_clinic_id || this.userData?.default_clinic_id;
    },
  },
  watch: {
    show(val) {
      this.showModal = val;

      if (val) {
        this.resetForm();
        this.fetchOptions().then(() => {
          // If editing a task and we have taskData, use it directly
          if (!this.isNew && this.taskData) {
            this.initializeFormFromTaskData(this.taskData);
          }
          // Only fetch from API if we don't have the task data
          else if (!this.isNew && this.taskId && !this.taskData) {
            this.fetchTask();
          }
        });

        if (this.initialDate) {
          this.form.due_date = this.formatDateForInput(this.initialDate);
        }
      }
    },

    'form.clinic'(val) {
      if (val) {
        // When clinic changes, fetch both patients and staff members
        this.fetchClinicPatients(val.id);

        // Force explicit call to fetchStaffUsers with timeout to ensure it runs
        setTimeout(() => {
          console.log("Triggering fetchStaffUsers from watcher");
          this.fetchStaffUsers();
        }, 100);
      } else {
        // Clear both lists if clinic is deselected
        this.patientOptions = [];
        this.userOptions = [];
      }
    },
  },
  created() {
    // Set default status
    this.form.status_option = this.statusOptions.find(opt => opt.id === 'pending');
    this.form.repeating_option = this.repeatingOptions.find(opt => opt.id === 'none');
  },
  methods: {
    resetForm() {
      this.form = {
        title: '',
        description: '',
        clinic: null,
        patient: null,
        doctor: null,
        priority: 'medium',
        status_option: this.statusOptions.find(opt => opt.id === 'pending'),
        due_date: null,
        reminder_date: null,
        assignees: [],
        category_option: null,
        repeating_option: this.repeatingOptions.find(opt => opt.id === 'none')
      };

      this.validationErrors = {};
      this.activeTab = 0;
    },

    async fetchOptions() {
      try {
        // Step 1: Initialize based on user role
        const userRole = this.getUserRole();

        // Load clinics based on user role
        if (userRole === 'administrator') {
          // Admin sees all clinics
          await this.getClinics();
        } else {
          // Other roles use the logged-in user's clinic
          if (this.userClinicId) {
            this.form.clinic = { id: this.userClinicId };
            await this.fetchClinicPatients(this.userClinicId);
            await this.fetchStaffUsers();
          }
        }

        // Set default clinic if only one is available
        if (this.clinicOptions.length === 1) {
          this.form.clinic = this.clinicOptions[0];
        }
      } catch (error) {
        console.error('Error fetching options:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.common.error_loading_options || 'Failed to load options'
        });
      }
    },

    getCurrentUserClinic() {
      // For clinic admin, get their clinic ID and set it by default
      // We'll use the user's clinic_id from store if available
      const userData = this.$store.state.userData.user
      if (userData && userData.clinic_id) {
        this.form.clinic_id = userData.clinic_id
      }
    },

    async getClinics() {
      this.clinicMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_list",
        });

        if (response.data.status) {
          this.clinicOptions = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinics:", error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.common.error_loading_clinics || 'Failed to load clinics'
        });
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    initializeFormFromTaskData(task) {
      if (!task) return;

      this.task = task; // Store the original task

      // Populate form fields
      this.form.title = task.title || '';
      this.form.description = task.description || '';

      // Set clinic
      if (task.clinic_id) {
        const clinicId = parseInt(task.clinic_id);
        this.form.clinic = this.clinicOptions.find(c => parseInt(c.id) === clinicId) || null;

        // If clinic not found in options but we have clinic data, create a temporary object
        if (!this.form.clinic && task.clinic_name) {
          this.form.clinic = {
            id: clinicId,
            label: task.clinic_name
          };
        }

        // Fetch clinic-related data
        if (this.form.clinic) {
          this.fetchClinicPatients(this.form.clinic.id);
          this.fetchStaffUsers();
        }
      }

      // Set patient
      if (task.patient_id) {
        setTimeout(() => {
          // Wait a moment for patients to load
          this.form.patient = this.patientOptions.find(p => parseInt(p.id) === parseInt(task.patient_id)) || null;

          // If patient not found but we have patient details, create a temporary object
          if (!this.form.patient && task.patient_name) {
            this.form.patient = {
              id: parseInt(task.patient_id),
              label: task.patient_name
            };
          }
        }, 300);
      }

      // Set priority
      this.form.priority = task.priority || 'medium';

      // Set status
      const statusId = task.status.replace('-', '_'); // Convert "in-progress" to "in_progress" if needed
      this.form.status_option = this.statusOptions.find(s => s.id === task.status || s.id === statusId) ||
        this.statusOptions.find(s => s.id === 'pending');

      // Set dates - format as YYYY-MM-DD for HTML date input
      if (task.due_date) {
        this.form.due_date = this.formatDateForInput(task.due_date);
      }

      if (task.reminder_date) {
        this.form.reminder_date = this.formatDateForInput(task.reminder_date);
      }

      // Set assignees
      if (task.assignees && task.assignees.length) {
        // Create assignee objects from the data
        this.form.assignees = task.assignees.map(assignee => {
          const assigneeId = parseInt(assignee.assignee_id || assignee.id);
          const existingUser = this.userOptions.find(u => parseInt(u.id) === assigneeId);

          if (existingUser) {
            return existingUser;
          } else {
            // Create a temporary user object if not found in options
            return {
              id: assigneeId,
              name: assignee.assignee_name || assignee.display_name || 'Staff Member',
              role: 'staff'
            };
          }
        });
      } else if (task.assignees_names) {
        // Handle case where we only have assignee names as a string
        const assigneeNames = task.assignees_names.split(',').map(name => name.trim());
        this.form.assignees = assigneeNames.map((name, index) => ({
          id: 1000 + index, // Use temporary IDs that won't conflict with real ones
          name: name,
          role: 'staff'
        }));
      }

      // Set category
      if (task.category) {
        this.form.category_option = this.categoryOptions.find(c => c.id === task.category) || null;
      }

      // Set repeating
      if (task.repeating) {
        this.form.repeating_option = this.repeatingOptions.find(r => r.id === task.repeating) ||
          this.repeatingOptions.find(r => r.id === 'none');
      }
    },

    async getDoctorClinics() {
      this.clinicMultiselectLoader = true;
      try {
        // Get user data from store
        const doctorId = this.userData?.ID;

        if (!doctorId) {
          console.error("Doctor ID not found in user data");
          return;
        }

        // For doctors, get clinics they're assigned to
        const response = await get("get_static_data", {
          data_type: "get_doctor_clinic",
          doctor_id: doctorId
        });

        if (response.data.status) {
          this.clinicOptions = response.data.data;

          // If only one clinic, select it
          if (this.clinicOptions.length === 1 && !this.form.clinic) {
            this.form.clinic = this.clinicOptions[0];
          }

          // Pre-select current doctor
          if (!this.form.doctor) {
            this.form.doctor = {
              id: doctorId,
              name: this.userData?.display_name
            };
          }
        }
      } catch (error) {
        console.error("Error fetching doctor clinics:", error);
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    async getAssignedClinics() {
      this.clinicMultiselectLoader = true;
      try {
        // Get user data from store
        const userId = this.userData?.ID;
        const userRole = this.getUserRole();

        if (!userId) {
          console.error("User ID not found in user data");
          return;
        }

        // For clinic_admin and receptionist, get their assigned clinics
        let endpoint = userRole === 'receptionist' ?
          "get_receptionist_clinic" : "get_clinic_admin_clinic";

        const response = await get("get_static_data", {
          data_type: endpoint,
          user_id: userId
        });

        if (response.data.status) {
          this.clinicOptions = response.data.data;

          // If only one clinic, select it
          if (this.clinicOptions.length === 1 && !this.form.clinic) {
            this.form.clinic = this.clinicOptions[0];
          }
        }
      } catch (error) {
        console.error(`Error fetching clinics for ${this.getUserRole()}:`, error);
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    async getPatientClinics() {
      this.clinicMultiselectLoader = true;
      try {
        // Get user data from store
        const patientId = this.userData?.ID;

        if (!patientId) {
          console.error("Patient ID not found in user data");
          return;
        }

        // For patients, get clinics they're registered with
        const response = await get("get_static_data", {
          data_type: "get_patient_clinic",
          patient_id: patientId
        });

        if (response.data.status) {
          this.clinicOptions = response.data.data;

          // If only one clinic, select it
          if (this.clinicOptions.length === 1 && !this.form.clinic) {
            this.form.clinic = this.clinicOptions[0];
          }

          // Pre-select current patient
          if (!this.form.patient) {
            this.form.patient = {
              id: patientId,
              label: this.userData?.display_name
            };
          }
        }
      } catch (error) {
        console.error("Error fetching patient clinics:", error);
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    async fetchStaffUsers() {
      // Only fetch if we have a clinic selected
      if (!this.form.clinic || !this.form.clinic.id) return;

      try {
        this.userOptions = []; // Clear existing options
        const clinicId = this.form.clinic.id;

        // 1. First fetch doctors - this is working as you confirmed
        try {
          const doctorsResponse = await get("get_static_data", {
            data_type: "get_users_by_clinic",
            clinic_id: clinicId,
            module_type: "appointment"
          });

          if (doctorsResponse.data && doctorsResponse.data.status && doctorsResponse.data.data) {
            const doctors = doctorsResponse.data.data.map(doctor => ({
              id: parseInt(doctor.id),
              name: doctor.label,
              role: 'doctor'
            }));

            this.userOptions = [...this.userOptions, ...doctors];
          }
        } catch (error) {
          console.error('Error fetching doctors:', error);
        }

        // 2. Fetch receptionists using the exact payload you provided
        try {
          const receptionistsResponse = await get("tasks_receptionist_list", {
            columnFilters: {},
            sort: [{ "field": "", "type": "" }],
            page: 1,
            perPage: 10,
            searchTerm: "",
            type: "list"
          });

          if (receptionistsResponse.data && receptionistsResponse.data.status && receptionistsResponse.data.data) {
            // Filter receptionists by clinic_id to only get those from the selected clinic
            const receptionists = receptionistsResponse.data.data
              .filter(receptionist => {
                // Check if the receptionist is assigned to the selected clinic
                const receptionistClinicId = receptionist.clinic_id || receptionist.clinic;

                if (Array.isArray(receptionistClinicId)) {
                  return receptionistClinicId.some(id => parseInt(id) === parseInt(clinicId));
                } else if (typeof receptionistClinicId === 'string' || typeof receptionistClinicId === 'number') {
                  return parseInt(receptionistClinicId) === parseInt(clinicId);
                }
                return false;
              })
              .map(receptionist => ({
                id: parseInt(receptionist.ID || receptionist.id),
                name: receptionist.display_name || receptionist.user_display_name || `${receptionist.first_name || ''} ${receptionist.last_name || ''}`.trim(),
                role: 'receptionist'
              }));

            this.userOptions = [...this.userOptions, ...receptionists];
          }
        } catch (error) {
          console.error('Error fetching receptionists:', error);
        }
      } catch (error) {
        console.error('Error in fetchStaffUsers:', error);
      }
    },

    async fetchClinicPatients(clinicId) {
      if (!clinicId) return;

      // Show loading indicator
      this.patientMultiselectLoader = true;

      try {
        this.patientOptions = []; // Clear existing options

        // Fetch patients for the selected clinic using same method as AppointmentForm.vue
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: clinicId,
        });

        if (response.data && response.data.status) {
          this.patientOptions = response.data.data;
          console.log('Loaded patients:', this.patientOptions);
        } else {
          console.error('Failed to load patients, status:', response.data?.status);
        }
      } catch (error) {
        console.error('Error fetching clinic patients:', error);
      } finally {
        // Hide loading indicator
        this.patientMultiselectLoader = false;
      }
    },

    formatDateForInput(dateString) {
      if (!dateString) return null;

      // If already in YYYY-MM-DD format, return as is
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        return dateString;
      }

      try {
        const date = new Date(dateString);

        // Check if the date is valid
        if (isNaN(date.getTime())) {
          console.warn('Invalid date value in TaskModal formatDateForInput:', dateString);
          return null;
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('Error formatting date for input in TaskModal:', error, 'Date string:', dateString);
        return null;
      }
    },

    getInitials(name) {
      if (!name) return '?';

      return name.split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    },

    getAvatarColor(name) {
      if (!name) return '#6c757d';

      // Generate consistent color based on name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      const colors = [
        '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
        '#1abc9c', '#d35400', '#c0392b', '#16a085', '#8e44ad',
        '#27ae60', '#2980b9', '#f1c40f', '#e67e22', '#ecf0f1'
      ];

      return colors[Math.abs(hash) % colors.length];
    },

    validateForm() {
      this.validationErrors = {};

      if (!this.form.title || this.form.title.trim() === '') {
        this.validationErrors.title = formTranslation.task.error_title_required || 'Title is required';
      }

      if (!this.form.clinic) {
        this.validationErrors.clinic_id = formTranslation.task.error_clinic_required || 'Clinic is required';
      }

      return Object.keys(this.validationErrors).length === 0;
    },

    prepareFormData() {
      // Extract assignee IDs, ensuring we handle both object and simple ID formats
      const assigneeIds = this.form.assignees.map(assignee => assignee.id);

      return {
        id: this.isNew ? null : this.taskId,
        title: this.form.title,
        description: this.form.description,
        clinic_id: this.form.clinic ? this.form.clinic.id : null,
        patient_id: this.form.patient ? this.form.patient.id : null,
        priority: this.form.priority,
        status: this.form.status_option ? this.form.status_option.id : 'pending',
        due_date: this.form.due_date,
        reminder_date: this.form.reminder_date,
        category: this.form.category_option ? this.form.category_option.id : null,
        repeating: this.form.repeating_option ? this.form.repeating_option.id : 'none',
        assignees: assigneeIds
      };
    },

    async saveTask() {
      if (!this.validateForm()) {
        this.activeTab = 0; // Switch back to details tab if there are errors
        return;
      }

      try {
        this.saving = true;
        const formData = this.prepareFormData();

        // Use the same endpoint for both create and update operations
        const response = await post("save_task", formData);

        if (response.data && response.data.status) {
          this.$swal.fire({
            icon: 'success',
            title: formTranslation.common.success || 'Success',
            text: this.isNew
              ? (formTranslation.task.create_success || 'Task created successfully')
              : (formTranslation.task.update_success || 'Task updated successfully'),
            showConfirmButton: false,
            timer: 1500
          });

          this.$emit('task-saved', {
            id: response.data.data.id,
            isNew: this.isNew
          });

          this.closeModal();
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || (formTranslation.common.unknown_error || 'An unknown error occurred')
          });
        }
      } catch (error) {
        console.error('Error saving task:', error);

        let errorMessage = formTranslation.common.unknown_error || 'An unknown error occurred';
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }

        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: errorMessage
        });
      } finally {
        this.saving = false;
      }
    },

    confirmDelete() {
      this.$swal.fire({
        title: formTranslation.task.confirm_delete || 'Delete Task?',
        text: formTranslation.task.delete_confirmation_text || 'This action cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        confirmButtonText: formTranslation.common.delete || 'Delete',
        cancelButtonText: formTranslation.common.cancel || 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          this.deleteTask();
        }
      });
    },

    async deleteTask() {
      if (!this.taskId) return;

      try {
        const response = await post(`delete_task/${this.taskId}`);

        if (response.data && response.data.status) {
          this.$swal.fire({
            icon: 'success',
            title: formTranslation.common.success || 'Success',
            text: formTranslation.task.delete_success || 'Task deleted successfully',
            showConfirmButton: false,
            timer: 1500
          });

          this.$emit('task-deleted', this.taskId);
          this.closeModal();
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || (formTranslation.task.delete_error || 'Failed to delete task')
          });
        }
      } catch (error) {
        console.error('Error deleting task:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.delete_error || 'Failed to delete task'
        });
      }
    },

    onCommentAdded(comments) {
      // Update comment count when a comment is added or deleted
      this.commentCount = comments ? comments.length : 0;
    },

    onAttachmentUploaded() {
      // You can implement any additional logic when an attachment is uploaded
    },

    closeModal() {
      this.showModal = false;
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
/* Tailwind CSS already includes the necessary styles */

/* Custom styling for multiselect */
:deep(.multiselect-invalid .multiselect__tags) {
  border-color: #ef4444;
}
</style>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>