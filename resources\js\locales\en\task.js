export default {
  // Task General
  task_manager: 'Task Manager',
  tasks: 'Tasks',
  task: 'Task',
  new_task: 'New Task',
  edit_task: 'Edit Task',
  add_task: 'Add Task',
  no_tasks_found: 'No tasks found',
  no_tasks_description: 'Get started by creating your first task',
  
  // Task Properties
  title: 'Title',
  title_placeholder: 'Enter task title',
  description: 'Description',
  description_placeholder: 'Enter task description',
  due_date: 'Due Date',
  select_due_date: 'Select due date',
  reminder: 'Reminder',
  select_reminder_date: 'Select reminder date',
  priority: 'Priority',
  priority_low: 'Low',
  priority_medium: 'Medium',
  priority_high: 'High',
  status: 'Status',
  status_pending: 'Pending',
  status_in_progress: 'In Progress',
  status_completed: 'Completed',
  status_cancelled: 'Cancelled',
  select_status: 'Select status',
  assignees: 'Assignees',
  select_assignees: 'Select assignees',
  related_patient: 'Related Patient',
  select_patient: 'Select patient',
  clinic: 'Clinic',
  select_clinic: 'Select clinic',
  category: 'Category',
  category_administrative: 'Administrative',
  category_clinical: 'Clinical',
  category_patient_followup: 'Patient Follow-up',
  category_billing: 'Billing',
  category_general: 'General',
  select_category: 'Select category',
  
  // Task Views
  list_view: 'List View',
  calendar_view: 'Calendar View',
  kanban_view: 'Kanban View',
  
  // Task Repeating
  repeating_schedule: 'Repeating Schedule',
  repeating_none: 'None',
  repeating_daily: 'Daily',
  repeating_weekly: 'Weekly',
  repeating_monthly: 'Monthly',
  select_repeating_schedule: 'Select repeating schedule',
  
  // Task Status
  overdue: 'Overdue',
  unassigned: 'Unassigned',
  total_tasks: 'Total Tasks',
  pending_tasks: 'Pending Tasks',
  completed_tasks: 'Completed Tasks',
  overdue_tasks: 'Overdue Tasks',
  selected_tasks: 'selected tasks',
  
  // Task Actions
  mark_complete: 'Mark Complete',
  mark_pending: 'Mark Pending',
  move_to_pending: 'Move to Pending',
  move_to_in_progress: 'Move to In Progress',
  move_to_completed: 'Move to Completed',
  move_to_cancelled: 'Move to Cancelled',
  
  // Bulk Actions
  confirm_bulk_complete: 'Complete Multiple Tasks',
  bulk_complete_confirmation: 'Are you sure you want to mark {count} tasks as completed?',
  confirm_bulk_status_change: 'Change Task Status',
  bulk_status_change_confirmation: 'Are you sure you want to change {count} tasks to {status}?',
  confirm_bulk_delete: 'Delete Multiple Tasks',
  bulk_delete_confirmation: 'Are you sure you want to delete {count} tasks? This action cannot be undone.',
  
  // Search and Filters
  search_tasks: 'Search tasks...',
  filter: 'Filter',
  select_date_range: 'Select date range',
  
  // Task Deletion
  confirm_delete: 'Delete Task',
  delete_confirmation_text: 'Are you sure you want to delete this task? This action cannot be undone.',
  
  // Comments
  comments: 'Comments',
  add_comment: 'Add Comment',
  post_comment: 'Post Comment',
  comment_placeholder: 'Write your comment here...',
  no_comments: 'No comments yet. Be the first to add a comment.',
  confirm_delete_comment: 'Delete Comment',
  delete_comment_confirmation_text: 'Are you sure you want to delete this comment? This action cannot be undone.',
  
  // Attachments
  attachments: 'Attachments',
  upload_attachment: 'Upload Attachment',
  drop_files_here: 'Drop files here',
  or: 'or',
  browse_files: 'Browse Files',
  max_file_size: 'Maximum file size: {size}',
  no_attachments: 'No attachments yet. Add files to this task.',
  confirm_delete_attachment: 'Delete Attachment',
  delete_attachment_confirmation_text: 'Are you sure you want to delete this attachment? This action cannot be undone.',
  file: 'File',
  uploaded_by: 'Uploaded by',
  
  // Success Messages
  create_success: 'Task created successfully',
  update_success: 'Task updated successfully',
  delete_success: 'Task deleted successfully',
  mark_complete_success: 'Task marked as completed',
  status_updated_successfully: 'Task status updated successfully',
  file_uploaded_successfully: 'File uploaded successfully',
  attachment_deleted_successfully: 'Attachment deleted successfully',
  
  // Error Messages
  error_title_required: 'Task title is required',
  error_clinic_required: 'Clinic is required',
  fetch_error: 'Failed to fetch tasks',
  delete_error: 'Failed to delete task',
  status_update_error: 'Failed to update task status',
  mark_complete_error: 'Failed to mark task as completed',
  comment_add_error: 'Failed to add comment',
  comment_update_error: 'Failed to update comment',
  comment_delete_error: 'Failed to delete comment',
  file_upload_failed: 'Failed to upload file',
  file_too_large: 'File size exceeds maximum limit of {size}',
  attachment_delete_failed: 'Failed to delete attachment'
}