<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto">
      <!-- Loading State -->
      <div
        v-if="formLoader"
        class="flex justify-center items-center min-h-[400px]"
      >
        <loader-component-2></loader-component-2>
      </div>

      <div v-else class="p-6 space-y-8">
        <!-- Settings Header -->
        <div class="border-b border-gray-200 pb-4">
          <div class="flex items-center justify-between">
            <h2 class="text-2xl font-semibold text-gray-900">
              {{ formTranslation.common.settings }}
              <a
                v-if="request_status == 'off'"
                href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#general-setting"
                target="_blank"
                class="ml-2 text-blue-600 hover:text-blue-700"
              >
                <i class="fa fa-question-circle"></i>
              </a>
            </h2>
          </div>
        </div>

        <!-- Request Features Form -->
        <form
          v-if="request_status == 'off' || showOption"
          @submit.prevent="handleSubmit"
          class="space-y-6"
        >
          <div class="flex items-center space-x-3">
            <label class="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                v-model="request_status"
                class="sr-only peer"
                :value="'on'"
                :unchecked-value="'off'"
              />
              <div
                class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
              ></div>
              <span class="ms-3 text-sm font-medium text-gray-900">
                {{ formTranslation.common.hide_request_features }}
              </span>
            </label>
          </div>

          <!-- Remove Features Option -->
          <div
            v-if="request_status == 'on' && showOption"
            class="flex items-center space-x-3"
          >
            <label class="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                v-model="request_status"
                @change="removeFeatureRequest"
                class="sr-only peer"
                :value="'on'"
                :unchecked-value="'off'"
              />
              <div
                class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
              ></div>
              <span class="ms-3 text-sm font-medium text-gray-900">
                {{ formTranslation.common.remove_request_features }}
              </span>
            </label>
          </div>

          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="isSubmited"
              class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
            >
              <i class="fa fa-save mr-2"></i>
              {{ formTranslation.common.save }}
            </button>
          </div>
        </form>

        <!-- Date Format Section -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900">
              {{
                formTranslation.common.date_format_setting +
                " " +
                formTranslation.common.deprecated
              }}
            </h2>
          </div>

          <form @submit.prevent="handleDateFormatSubmit" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.custom_date_format }}
                </label>
                <input
                  type="text"
                  v-model="dateFormat"
                  disabled
                  class="w-full rounded-md border-gray-300 bg-gray-100 px-3 py-2"
                />
              </div>
              <div class="flex items-end">
                <button
                  type="submit"
                  disabled
                  class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm disabled:opacity-50"
                >
                  <i
                    :class="
                      dateFormatLoading ? 'fa fa-spinner fa-spin' : 'fa fa-save'
                    "
                    class="mr-2"
                  ></i>
                  {{
                    dateFormatLoading
                      ? formTranslation.common.loading
                      : formTranslation.common.save
                  }}
                </button>
              </div>
            </div>
            <p class="text-blue-600 font-semibold">
              {{
                formTranslation.appointments.plh_date + ": " + dateFormatOutput
              }}
            </p>
          </form>
        </div>

        <!-- Clinic Currency Section -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ formTranslation.common.clinic_currency }}
          </h2>

          <form @submit.prevent="handleCliniSubmit" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.clinic.currency_prefix }}
                </label>
                <input
                  type="text"
                  v-model="clinicData.currency_prefix"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.clinic.currency_postfix }}
                </label>
                <input
                  type="text"
                  v-model="clinicData.currency_postfix"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="isSubmitedClinic"
                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
              >
                <i class="fa fa-save mr-2"></i>
                {{ formTranslation.common.save }}
              </button>
            </div>
          </form>
        </div>

        <!-- Country Code Section -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ formTranslation.common.default_country_code }}
          </h2>

          <form @submit.prevent="handleCountryCodeSubmit" class="space-y-6">
            <div class="max-w-md">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.select_country_code }}
              </label>
              <VuePhoneNumberInput
                id="country_code"
                :default-country-code="defaultCountryCode"
                v-model="contact"
                @update="countryCodeUpdateHandaler"
                no-example
                clearable
                class="phone-input-custom"
              />
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="isSubmitedCountryCode"
                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
              >
                <i class="fa fa-save mr-2"></i>
                {{ formTranslation.common.save }}
              </button>
            </div>
          </form>
        </div>

        <!-- Registration Shortcode Settings -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ formTranslation.common.registration_shortcode_setting }}
          </h2>

          <form
            @submit.prevent="handleUserRegistrationShortcodeSettingSubmit"
            class="space-y-6"
          >
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Doctor Register -->
              <div class="space-y-2">
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    v-model="userRegistrationShortcodeSetting.status.doctor"
                    class="sr-only peer"
                    value="on"
                    unchecked-value="off"
                  />
                  <div
                    class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                  ></div>
                  <span class="ms-3 text-sm font-medium text-gray-900">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="
                        userRegistrationShortcodeSetting.status.doctor === 'on'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-red-100 text-red-800'
                      "
                    >
                      {{
                        userRegistrationShortcodeSetting.status.doctor === "on"
                          ? formTranslation.common.active
                          : formTranslation.common.inactive
                      }}
                    </span>
                    {{
                      formTranslation.common.default_status_when_doctor_register
                    }}
                  </span>
                </label>
              </div>

              <!-- Receptionist Register -->
              <div class="space-y-2">
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    v-model="
                      userRegistrationShortcodeSetting.status.receptionist
                    "
                    class="sr-only peer"
                    value="on"
                    unchecked-value="off"
                  />
                  <div
                    class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                  ></div>
                  <span class="ms-3 text-sm font-medium text-gray-900">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="
                        userRegistrationShortcodeSetting.status.receptionist ===
                        'on'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-red-100 text-red-800'
                      "
                    >
                      {{
                        userRegistrationShortcodeSetting.status.receptionist ===
                        "on"
                          ? formTranslation.common.active
                          : formTranslation.common.inactive
                      }}
                    </span>
                    {{
                      formTranslation.common
                        .default_status_when_receptionist_register
                    }}
                  </span>
                </label>
              </div>

              <!-- Patient Register -->
              <div class="space-y-2">
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    v-model="userRegistrationShortcodeSetting.status.patient"
                    class="sr-only peer"
                    value="on"
                    unchecked-value="off"
                  />
                  <div
                    class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                  ></div>
                  <span class="ms-3 text-sm font-medium text-gray-900">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="
                        userRegistrationShortcodeSetting.status.patient === 'on'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-red-100 text-red-800'
                      "
                    >
                      {{
                        userRegistrationShortcodeSetting.status.patient === "on"
                          ? formTranslation.common.active
                          : formTranslation.common.inactive
                      }}
                    </span>
                    {{
                      formTranslation.common
                        .default_status_when_patient_register
                    }}
                  </span>
                </label>
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="userRegistrationShortcodeSettingLoading"
                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
              >
                <i
                  :class="
                    userRegistrationShortcodeSettingLoading
                      ? 'fa fa-spinner fa-spin'
                      : 'fa fa-save'
                  "
                  class="mr-2"
                ></i>
                {{
                  userRegistrationShortcodeSettingLoading
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>
        </div>

        <!-- Google reCAPTCHA Section -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ formTranslation.common.google_recaptcha }}
          </h2>

          <form @submit.prevent="handleCaptchaSubmit" class="space-y-6">
            <div class="space-y-4">
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  v-model="googleCaptcha.status"
                  @change="handleCaptchaSubmit"
                  class="sr-only peer"
                  value="on"
                  unchecked-value="off"
                />
                <div
                  class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                ></div>
                <span class="ms-3 text-sm font-medium text-gray-900">
                  {{ formTranslation.common.enable_google_recaptcha }}
                </span>
              </label>

              <div v-if="googleCaptcha.status == 'on'" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      {{ formTranslation.common.site_key }}
                    </label>
                    <input
                      type="text"
                      v-model="googleCaptcha.site_key"
                      class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      {{ formTranslation.common.secret_key }}
                    </label>
                    <input
                      type="text"
                      v-model="googleCaptcha.secret_key"
                      class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <a
                    href="https://www.google.com/recaptcha/admin"
                    target="_blank"
                    class="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    {{ formTranslation.common.google_recaptcha_refer_link }}
                  </a>
                </div>

                <div class="flex justify-end">
                  <button
                    type="submit"
                    :disabled="googleRecaptchaLoading"
                    class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
                  >
                    <i
                      :class="
                        googleRecaptchaLoading
                          ? 'fa fa-spinner fa-spin'
                          : 'fa fa-save'
                      "
                      class="mr-2"
                    ></i>
                    {{
                      googleRecaptchaLoading
                        ? formTranslation.common.loading
                        : formTranslation.common.save
                    }}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Login Redirect Section -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">Login Redirect</h2>

          <form @submit.prevent="handleLoginRedirectSubmit" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <!-- Clinic Admin -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.clinic.clinic }}
                </label>
                <input
                  type="url"
                  v-model="login_redirect.clinic_admin"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Doctor -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.doctor_session.dt_lbl_doc }}
                </label>
                <input
                  type="url"
                  v-model="login_redirect.doctor"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Receptionist -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.clinic.receptionist }}
                </label>
                <input
                  type="url"
                  v-model="login_redirect.receptionist"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Patient -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.appointments.tag_patient_plh }}
                </label>
                <input
                  type="url"
                  v-model="login_redirect.patient"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="loginRedirectSettingLoading"
                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
              >
                <i
                  :class="
                    loginRedirectSettingLoading
                      ? 'fa fa-spinner fa-spin'
                      : 'fa fa-save'
                  "
                  class="mr-2"
                ></i>
                {{
                  loginRedirectSettingLoading
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>
        </div>

        <!-- Logout Redirect Section -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ formTranslation.common.logout_redirect }}
          </h2>

          <form @submit.prevent="handleLogoutRedirectSubmit" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <!-- Clinic Admin -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.clinic.clinic }}
                </label>
                <input
                  type="url"
                  v-model="logout_redirect.clinic_admin"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Doctor -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.doctor_session.dt_lbl_doc }}
                </label>
                <input
                  type="url"
                  v-model="logout_redirect.doctor"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Receptionist -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.clinic.receptionist }}
                </label>
                <input
                  type="url"
                  v-model="logout_redirect.receptionist"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Patient -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.appointments.tag_patient_plh }}
                </label>
                <input
                  type="url"
                  v-model="logout_redirect.patient"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="redirectSettingLoading"
                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
              >
                <i
                  :class="
                    redirectSettingLoading
                      ? 'fa fa-spinner fa-spin'
                      : 'fa fa-save'
                  "
                  class="mr-2"
                ></i>
                {{
                  redirectSettingLoading
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>
        </div>

        <!-- FullCalendar Section -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ formTranslation.common.fullcalendar_setting }}
          </h2>

          <form
            @submit.prevent="handleFullCalendarFormSubmit"
            class="space-y-6"
          >
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.fullcalendar_license_key }}
              </label>
              <div class="max-w-2xl">
                <input
                  type="text"
                  v-model="fullcalendar_key"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div
                  v-if="isFullcalendarFormKeyEmpty"
                  class="mt-1 text-sm text-red-600"
                >
                  {{ formTranslation.common.full_calender_validation }}
                </div>
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="fullcalendarFormLoading"
                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
              >
                <i
                  :class="
                    fullcalendarFormLoading
                      ? 'fa fa-spinner fa-spin'
                      : 'fa fa-save'
                  "
                  class="mr-2"
                ></i>
                {{
                  fullcalendarFormLoading
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>
        </div>

        <!-- Consultation Settings -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ $t("common.encounter_setting") }}
          </h2>

          <form @submit.prevent="handleEncounterFormSubmit" class="space-y-6">
            <div class="space-y-4">
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  v-model="
                    encounterSettingsData.encounter_edit_after_close_status
                  "
                  class="sr-only peer"
                  value="on"
                  unchecked-value="off"
                />
                <div
                  class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                ></div>
                <span class="ms-3 text-sm font-medium text-gray-900">
                  {{ $t("common.allow_encounter_edit_after_close") }}
                </span>
              </label>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="isSubmitedEncounter"
                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50"
              >
                <i
                  :class="
                    isSubmitedEncounter ? 'fa fa-spinner fa-spin' : 'fa fa-save'
                  "
                  class="mr-2"
                ></i>
                {{
                  isSubmitedEncounter
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>
        </div>

        <!-- Reset Plugin Section -->
        <div class="space-y-4 pt-6">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ formTranslation.common.reset_plugin_data }}
          </h2>

          <div>
            <button
              @click="resetPluginModel = true"
              class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md shadow-sm"
            >
              <i class="fa fa-undo mr-2"></i>
              {{ formTranslation.common.reset_plugin_data }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Reset Plugin Modal -->
    <div
      v-if="resetPluginModel"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
    >
      <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            {{ formTranslation.common.reset_plugin_data }}
          </h3>
          <button
            @click="closeResetPluginModel"
            class="text-gray-400 hover:text-gray-500"
          >
            <span class="sr-only">Close</span>
            <svg
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleResetPluginForm" class="space-y-4">
          <!-- Reset Appointments and Consultations -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="reset-appointment-status"
              v-model="resetPluginData.resetAppointmentEncounterStatus"
              value="on"
              unchecked-value="off"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              for="reset-appointment-status"
              class="ml-2 block text-sm text-gray-900"
            >
              {{ formTranslation.common.reset_appointments_and_encounter }}
            </label>
          </div>

          <!-- Reset Doctors -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="reset-doctor-status"
              v-model="resetPluginData.resetDoctorStatus"
              value="on"
              unchecked-value="off"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              for="reset-doctor-status"
              class="ml-2 block text-sm text-gray-900"
            >
              {{ formTranslation.common.reset_doctors }}
            </label>
          </div>

          <!-- Reset Receptionists -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="reset-receptionist-status"
              v-model="resetPluginData.resetReceptionistStatus"
              value="on"
              unchecked-value="off"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              for="reset-receptionist-status"
              class="ml-2 block text-sm text-gray-900"
            >
              {{ formTranslation.common.reset_receptionists }}
            </label>
          </div>

          <!-- Reset Patients -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="reset-patients-status"
              v-model="resetPluginData.resetPatientStatus"
              value="on"
              unchecked-value="off"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              for="reset-patients-status"
              class="ml-2 block text-sm text-gray-900"
            >
              {{ formTranslation.common.reset_patients }}
            </label>
          </div>

          <!-- Reset Revenue -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="reset-revenue-status"
              v-model="resetPluginData.resetRevenueStatus"
              value="on"
              unchecked-value="off"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              for="reset-revenue-status"
              class="ml-2 block text-sm text-gray-900"
            >
              {{ formTranslation.common.reset_revenue }}
            </label>
          </div>

          <!-- Reset All Data -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="reset-all-status"
              v-model="resetPluginData.resetAllDataStatus"
              value="on"
              unchecked-value="off"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              for="reset-all-status"
              class="ml-2 block text-sm font-medium text-gray-900"
            >
              {{ formTranslation.common.delete_all_reset_plugin }}
            </label>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              @click="closeResetPluginModel"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ formTranslation.common.cancel }}
            </button>
            <button
              type="submit"
              :disabled="isSubmitedReset"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <i class="fa fa-save mr-2"></i>
              {{ formTranslation.common.save }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { post, get } from "../../config/request";
import { maxValue, minValue, required } from "vuelidate/lib/validators";
import ModalPopup from "../../components/Modal/Index";
export default {
  name: "generalSetting",
  components: {
    ModalPopup,
    VuePhoneNumberInput,
  },
  data: () => {
    return {
      request_status: "yes",
      remove_request_status: "off",
      showOption: true,
      isSubmited: false,
      isSubmitedClinic: false,
      isSubmitedCountryCode: false,
      isSubmitedReset: false,
      isSubmitedEncounter: false,
      clinicData: {
        currency_prefix: "",
        currency_postfix: "",
        // decimal_point:{

        // }
      },
      googleRecaptchaLoading: false,
      googleCaptcha: {
        site_key: "",
        secret_key: "",
        status: "off",
      },
      redirectSettingLoading: false,
      loginRedirectSettingLoading: false,
      logout_redirect: {
        clinic_admin: "",
        patient: "",
        receptionist: "",
        doctor: "",
      },
      login_redirect: {
        clinic_admin: "",
        patient: "",
        receptionist: "",
        doctor: "",
      },
      datat: {},
      formLoader: true,
      decimals: [
        { id: 0, label: "100" },
        { id: 1, label: "100.0" },
        { id: 2, label: "100.00" },
        { id: 3, label: "100.000" },
        { id: 4, label: "100.0000" },
      ],
      fullcalendarFormLoading: false,
      isFullcalendarFormKeyEmpty: false,
      fullcalendar_key: "",
      dateFormatLoading: false,
      dateFormat: "D-MM-YYYY",
      dateFormatOutput: "",
      reset_status: "off",
      resetPluginModel: false,
      resetPluginData: {
        resetAppointmentEncounterStatus: "off",
        resetDoctorStatus: "off",
        resetReceptionistStatus: "off",
        resetPatientStatus: "off",
        resetRevenueStatus: "off",
        // resetClinicStatus:'off',
        resetAllDataStatus: "off",
      },
      userRegistrationShortcodeSetting: {
        status: {
          doctor: "off",
          receptionist: "off",
          patient: "on",
        },
        user_role: {
          kiviCare_doctor: "on",
          kiviCare_receptionist: "on",
          kiviCare_patient: "on",
        },
      },
      userRegistrationShortcodeSettingLoading: false,
      countryCodeData: {
        countrycode: "",
        countryCallingCode: "",
      },
      contact: "00",
      defaultCountryCode: null,
      userRegistrationFormSetting_status: "on",
      encounterSettingsData: {
        encounter_edit_after_close_status: "off",
      },
      userRegistrationFormSettingLoading: false,
    };
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.getRequestHelper();
    this.getAllSetting();
    this.getModule();
  },
  methods: {
    validateCheckboxes(userRole) {
      const user_roles = this.userRegistrationShortcodeSetting.user_role;

      const { kiviCare_doctor, kiviCare_receptionist, kiviCare_patient } =
        this.userRegistrationShortcodeSetting.user_role;

      if (
        kiviCare_doctor === "off" &&
        kiviCare_receptionist === "off" &&
        kiviCare_patient === "off"
      ) {
        user_roles[userRole] = "on";
        this.userRegistrationShortcodeSetting.user_role = user_roles;
      }
    },
    countryCodeUpdateHandaler: function (val) {
      this.countryCodeData.countrycode = val.countryCode;
      this.countryCodeData.countryCallingCode = val.countryCallingCode;
    },
    handleSubmit() {
      var element = $("#btn-general-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmited = true;
      post("save_request_helper_status", {
        request_status: this.request_status,
        remove_request_status: this.remove_request_status,
      })
        .then((response) => {
          this.isSubmited = false;
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-save");
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.showOption = false;
            if (response.data.data == "off") {
              this.request_status = response.data.data;
            }
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleUserRegistrationShortcodeSettingSubmit() {
      this.userRegistrationShortcodeSettingLoading = true;
      post("save_registration_shortcode_setting", {
        data: this.userRegistrationShortcodeSetting,
      })
        .then((response) => {
          this.userRegistrationShortcodeSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.userRegistrationShortcodeSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleDateFormatSubmit() {
      this.dateFormatLoading = true;
      post("save_date_format", { data: this.dateFormat })
        .then((response) => {
          this.dateFormatLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit("FETCH_DATE_FORMAT", { data: this.dateFormat });
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.dateFormatLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleLogoutRedirectSubmit() {
      this.redirectSettingLoading = true;
      post("save_logout_redirect_setting", { data: this.logout_redirect })
        .then((response) => {
          this.redirectSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.dispatch("logout_redirect_url", {
              data: Object.assign({}, this.logout_redirect),
            });
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.redirectSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleLoginRedirectSubmit() {
      this.loginRedirectSettingLoading = true;
      post("save_login_redirect_setting", { data: this.login_redirect })
        .then((response) => {
          this.loginRedirectSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.loginRedirectSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleFullCalendarFormSubmit() {
      if (this.fullcalendar_key.length == 0) {
        this.isFullcalendarFormKeyEmpty = true;
        return;
      }
      this.isFullcalendarFormKeyEmpty = this.fullcalendar_key.length == 0;
      this.fullcalendarFormLoading = true;
      post("save_fullcalendar_setting", {
        fullcalendar_key: this.fullcalendar_key,
      })
        .then((response) => {
          this.fullcalendarFormLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.fullcalendarFormLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleRegistrationFormSetting() {
      this.userRegistrationFormSettingLoading = true;
      post("save_registration_form_setting", {
        status: this.userRegistrationFormSetting_status,
      })
        .then((response) => {
          this.userRegistrationFormSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.userRegistrationFormSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleCaptchaSubmit() {
      this.googleRecaptchaLoading = true;
      post("save_google_recaptcha_setting", this.googleCaptcha)
        .then((response) => {
          this.googleRecaptchaLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.googleRecaptchaLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleCliniSubmit: function () {
      var element = $("#btn-clinic-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmitedClinic = true;
      post("save_clinic_currency", { clinic_data: this.clinicData })
        .then((response) => {
          this.isSubmitedClinic = false;
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-save");
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleCountryCodeSubmit: function () {
      var element = $("#btn-country-code-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmitedCountryCode = true;
      post("save_country_code", { CountryCode: this.countryCodeData })
        .then((response) => {
          this.isSubmitedCountryCode = false;
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-save");
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleEncounterFormSubmit() {
      this.isSubmitedEncounter = true;
      post("save_encounter_setting", this.encounterSettingsData)
        .then((response) => {
          this.isSubmitedEncounter = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.isSubmitedEncounter = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getRequestHelper: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
        if (this.request_status == "on") {
          this.showOption = false;
        }
      }
      this.formLoader = false;
    },
    getAllSetting: function () {
      this.formLoader = true;
      get("get_all_general_setting", {})
        .then((response) => {
          this.formLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            // this.datat =response.data.data.decimal_point
            this.clinicData = response.data.data;
            this.countryCodeData = response.data.countryCodeData;
            if (
              response.data.countryCodeData.countryCallingCode !== "" &&
              response.data.countryCodeData.countryCallingCode !== undefined
            ) {
              this.defaultCountryCode =
                response.data.countryCodeData.countrycode;
            }
            if (response.data.captcha_data !== undefined) {
              this.googleCaptcha = response.data.captcha_data;
            }
            if (response.data.logout_redirect !== undefined) {
              this.logout_redirect = response.data.logout_redirect;
            }
            if (response.data.fullcalendar !== undefined) {
              this.fullcalendar_key = response.data.fullcalendar;
            }
            if (response.data.date_format !== undefined) {
              this.dateFormat = response.data.date_format;
            }
            if (response.data.login_redirect !== undefined) {
              this.login_redirect = response.data.login_redirect;
            }
            if (response.data.userRegistrationShortcodeSetting !== undefined) {
              this.userRegistrationShortcodeSetting =
                response.data.userRegistrationShortcodeSetting;
            }
            if (response.data.userRegistrationFormSetting !== undefined) {
              this.userRegistrationFormSetting_status =
                response.data.userRegistrationFormSetting;
            }
            if (response.data.encounter_settings !== undefined) {
              this.encounterSettingsData = response.data.encounter_settings;
            }
            this.formatDateValue();
          }
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    removeFeatureRequest(status) {
      if (status == "on") {
        $.confirm({
          title: this.formTranslation.common.remove_links,
          content: "Are you sure want to remove all hard links permanently ?",
          type: "red",
          buttons: {
            ok: {
              text: this.formTranslation.common.yes,
              btnClass: "btn-danger",
              keys: ["enter"],
              action: () => {
                this.handleSubmit();
              },
            },
            cancel: {
              text: this.formTranslation.common.cancel,
            },
          },
        });
      }
    },
    closeResetPluginModel() {
      this.resetPluginModel = false;
      this.resetPluginData.resetAppointmentEncounterStatus = "";
      this.resetPluginData.resetDoctorStatus = "";
      this.resetPluginData.resetReceptionistStatus = "";
      this.resetPluginData.resetPatientStatus = "";
      this.resetPluginData.resetRevenueStatus = "";
      this.resetPluginData.resetAllDataStatus = "";
    },
    handleResetPluginForm() {
      var element = $("#btn-reset-submit").find("i");
      var content_msg =
        this.resetPluginData.resetDoctorStatus == "on" ||
        this.resetPluginData.resetPatientStatus == "on"
          ? this.formTranslation.common.action_reset_plugin_user_data
          : this.formTranslation.common.action_reset_plugin_data;
      $.confirm({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        content: content_msg,
        type: "red",
        buttons: {
          ok: {
            text: this.formTranslation.common.yes,
            btnClass: "btn-danger",
            keys: ["enter"],
            action: () => {
              this.isSubmitedReset = true;
              element.removeClass("fa fa-save ");
              element.addClass("fa fa-spinner fa-spin");
              post("reset_plugin_data", {
                reset_plugin_data: this.resetPluginData,
              })
                .then((response) => {
                  element.removeClass("fa fa-spinner fa-spin");
                  element.addClass("fa fa-save");
                  this.isSubmitedReset = false;
                  if (
                    response.data.status !== undefined &&
                    response.data.status === true
                  ) {
                    this.resetPluginModel = false;
                    displayMessage(response.data.message);
                    if (
                      response.data.reset_all !== undefined &&
                      response.data.reset_all === true
                    ) {
                      window.location.href =
                        window.request_data.homePage + "/wp-admin/plugins.php";
                    } else {
                      location.reload();
                    }
                  } else {
                    this.resetPluginModel = false;
                    displayErrorMessage(response.data.message);
                  }
                })
                .catch((error) => {
                  console.log(error);
                  displayErrorMessage(response.data.message);
                });
            },
          },
          cancel: {
            text: this.formTranslation.common.cancel,
          },
        },
      });
    },
    formatDateValue() {
      this.dateFormatOutput = window.moment
        .utc(new Date())
        .format(this.dateFormat);
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },
  computed: {},
  watch: {
    dateFormat: function () {
      this.formatDateValue();
    },
  },
};
</script>

<style>
.form-group.country-code-inline-box #country_code .flex-1 {
  display: none;
}

#user_registration .custom-switch .custom-control-label::before {
  left: -2.5rem;
}

#user_registration .custom-switch .custom-control-label::after {
  left: calc(-2.5rem + 2px);
}
</style>
