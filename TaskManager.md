# KiviCare EHR Task Manager Implementation Plan

## Overview
The Task Manager module will allow clinic administrators and staff to create, assign, track, and manage tasks within the EHR system. It will support various task types, priorities, and patient associations to streamline workflow management across clinics.

## User Roles & Permissions
- **Admin**: Create/view/edit/delete all tasks across all clinics
- **Clinic Admin**: Create/view/edit/delete tasks within their clinic
- **Doctor**: Create tasks, view assigned tasks, update status
- **Receptionist**: Create tasks, view assigned tasks, update status
- **Patient**: View tasks specifically shared with them (optional)

## Database Structure

### 1. Tasks Table (`kc_tasks`)
- `id` - Primary key
- `title` - Task title (required)
- `description` - Detailed description
- `clinic_id` - Associated clinic (foreign key to kc_clinics)
- `creator_id` - User who created the task (foreign key to wp_users)
- `patient_id` - Associated patient (foreign key to wp_users, nullable)
- `priority` - Task priority (high/medium/low)
- `status` - Task status (pending/in-progress/completed/cancelled)
- `due_date` - Task deadline
- `created_at` - Creation timestamp
- `updated_at` - Last updated timestamp
- `reminder_date` - When to send reminder
- `repeating` - Recurring schedule (none/daily/weekly/monthly)
- `category` - Task category
- `is_archived` - Boolean for archived tasks

### 2. Task Assignees (`kc_task_assignees`)
- `id` - Primary key
- `task_id` - Foreign key to kc_tasks
- `assignee_id` - User ID of the assignee
- `assigned_at` - Assignment timestamp
- `completed_at` - Completion timestamp (nullable)

### 3. Task Comments (`kc_task_comments`)
- `id` - Primary key
- `task_id` - Foreign key to kc_tasks
- `user_id` - Comment author
- `comment` - Comment text
- `created_at` - Creation timestamp

### 4. Task Attachments (`kc_task_attachments`)
- `id` - Primary key
- `task_id` - Foreign key to kc_tasks
- `file_name` - Original filename
- `file_url` - WordPress attachment URL
- `attachment_id` - WordPress attachment ID
- `uploaded_by` - User who uploaded the file
- `uploaded_at` - Upload timestamp

## Models

### 1. KCTask Model
- CRUD operations for tasks
- Methods to manage task status
- Task filtering and sorting capabilities
- Integration with notification system

### 2. KCTaskAssignee Model
- Methods to assign/unassign users
- Track assignment completion

### 3. KCTaskComment Model
- Comment management functionality

### 4. KCTaskAttachment Model
- File upload and management

## Controllers

### 1. KCTaskController
- `index()` - List tasks with filtering
- `save()` - Create/update tasks
- `delete()` - Remove tasks
- `get_task()` - Get task details
- `update_status()` - Change task status
- `get_patient_tasks()` - Filter by patient

### 2. KCTaskAssigneeController
- `assign_task()` - Assign task to users
- `unassign_task()` - Remove assignment
- `get_assigned_tasks()` - List tasks for a user

### 3. KCTaskCommentController
- `add_comment()` - Add comments to tasks
- `delete_comment()` - Remove comments
- `get_comments()` - Fetch task comments

## API Routes
- `GET /tasks` - List tasks with filters
- `POST /tasks` - Create new task
- `GET /tasks/{id}` - Get task details
- `PUT /tasks/{id}` - Update task
- `DELETE /tasks/{id}` - Delete task
- `PUT /tasks/{id}/status` - Update task status
- `GET /tasks/user/{user_id}` - Get user's tasks
- `GET /tasks/clinic/{clinic_id}` - Get clinic tasks
- `GET /tasks/patient/{patient_id}` - Get patient-related tasks
- `POST /tasks/{id}/comments` - Add comment
- `GET /tasks/{id}/comments` - Get task comments
- `POST /tasks/{id}/attachments` - Upload attachment
- `GET /tasks/{id}/attachments` - Get task attachments

## Frontend Components

### 1. Main Views
- **Task Dashboard**: Overview of all tasks with filters and sorting
- **Task Calendar**: Calendar view of all tasks with due dates
- **My Tasks**: Personal task list for current user

### 2. Task Management Components
- **TaskList.vue**: Filterable list of tasks
- **TaskItem.vue**: Individual task card/row
- **TaskForm.vue**: Task creation/editing form
- **TaskDetail.vue**: Detailed task view with comments

### 3. UI Elements
- **Priority Indicators**: Color-coded icons for task priority
- **Status Badges**: Visual indicators for different statuses
- **Filter Controls**: Dropdown/multiselect filters for tasks

## Features

### 1. Task Creation
- Title, description, priority, due date
- Patient association (select from patient list)
- File attachment capability
- Template-based quick task creation

### 2. Task Assignment
- Assign to individual users or roles
- Multiple assignees option
- Reassignment capability

### 3. Patient Association
- Link tasks to specific patients
- Include patient context in task view
- Filter tasks by patient

### 4. Notifications & Reminders
- Email notifications for new task assignments
- Due date reminders (1 day before, day of)
- Status change notifications
- In-app notification system integration

### 5. Task Tracking & Reporting
- Task status updates with timestamp
- Completion confirmation
- Task activity log
- Performance metrics (completion rates, overdue tasks)

### 6. Comments & Collaboration
- Threaded comments on tasks
- @mentions for users
- File attachments in comments

### 7. Recurring Tasks
- Set tasks to repeat daily/weekly/monthly
- Template-based recurring tasks
- Skip or reschedule instances

## Integration Points

### 1. Patient Records
- Link tasks to patient profiles
- Show relevant tasks on patient dashboard

### 2. Appointment System
- Create tasks from appointments
- Link tasks to specific appointments

### 3. Notification System
- Leverage existing notification framework
- Add task-specific notification templates

### 4. Calendar Integration
- Display tasks on clinic calendar
- Sync with external calendars (Google, Outlook)

## Implementation Phases

### Phase 1: Core Functionality
- Database schema creation
- Basic CRUD operations
- Simple list view of tasks
- Task assignment capability

### Phase 2: Enhanced Features
- Patient association
- Comments system
- File attachments
- Email notifications

### Phase 3: Advanced Features
- Recurring tasks
- Reporting and analytics
- Calendar integration
- Mobile responsiveness

## Technical Considerations

### 1. Performance
- Pagination for task lists
- Efficient filtering mechanism
- Optimization for clinics with large task volumes

### 2. Security
- Role-based access control
- Data privacy for patient-related tasks
- Audit logging for task changes

### 3. Usability
- Intuitive task creation flow
- Clear visual indicators for priority/status
- Quick-action buttons for common operations

## Future Enhancements
- Task templates for common workflows
- Automated task creation based on triggers
- AI-powered task prioritization
- Mobile app push notifications
- Task checklists and subtasks
- Time tracking functionality