import { post } from "../config/request";
import { disabledButton, enabledButton } from "./helper";
export const globalDeleteModuleData = (
  params,
  successCallback = null,
  failedCallback = null
) => {
  const id = params.id;
  const ele = $(`#${params.delete_ele}${id}`);
  const eleIconClass = "fa fa-trash";

  Swal.fire({
    title: window?.__kivicarelang?.clinic_schedule?.dt_are_you_sure,
    text: params.content_message,
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#dc3545", // Bootstrap danger color
    cancelButtonColor: "#6c757d", // Bootstrap secondary color
    confirmButtonText: window?.__kivicarelang?.common?.yes,
    cancelButtonText: window?.__kivicarelang?.common?.cancel,
    focusConfirm: false,
  }).then((result) => {
    if (result.isConfirmed) {
      disabledButton(ele, eleIconClass);
      post(params.endpoint, {
        id: id,
      })
        .then((response) => {
          enabledButton(ele, eleIconClass);
          if (response?.data?.status && response.data.status === true) {
            if (successCallback) {
              successCallback(response.data);
            }
            displayMessage(response.data.message);
          } else {
            if (failedCallback) {
              failedCallback(response.data);
            }
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          enabledButton(ele, eleIconClass);
          console.log(error);
          if (failedCallback) {
            failedCallback({
              data: [],
              status: false,
            });
          }
          displayErrorMessage(
            window?.__kivicarelang?.common?.internal_server_error
          );
        });
    } else {
      // Handle cancel case
      if (failedCallback) {
        failedCallback({
          data: [],
          status: false,
        });
      }
    }
  });
};
