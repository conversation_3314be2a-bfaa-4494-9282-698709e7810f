<template>
  <b-row>
    <b-col sm="12">
      <b-overlay :show="!(userData?.addOns?.webhooks)" variant="white" :opacity="overlayOpacity">
        <template #overlay>
          <overlay-message :addon_type="name"></overlay-message>
        </template>
        <b-card class="p-0 shadow" body-class="p-0" header-tag="header" footer-tag="footer">
          <template v-slot:header>
            <b-row>
              <b-col sm="12" md="8" lg="8">
                <h3 class="mb-0">
                  {{ formTranslation.webhooks.webhooks_list || "Webhooks list" }}
                </h3>
              </b-col>
              <b-col sm="12" md="4" lg="4">
                <div class="d-flex justify-content-end">
                  <router-link class="btn btn-sm btn-primary" :to="{ name: `${name}.create` }">
                    <i class="fa fa-plus"></i>
                    {{ formTranslation.webhooks.new_webhook || "New webhook" }}
                  </router-link>
                </div>
              </b-col>
            </b-row>
          </template>
          <b-row>
            <b-col sm="12" md="12" lg="12">
              <div v-show="isColumnLoading || isRowLoading">
                <loader-component-2></loader-component-2>
              </div>
              <div :id="name + 'Print'">
                <vue-good-table ref="dataTable" :columns="column" :rows="rows"
                  mode="remote" :search-options="{
                    enabled: true,
                    placeholder:
                      formTranslation.common.search_webhooks_data_global_placeholder,
                  }" @on-sort-change="onSortChange"
                  @on-column-filter="onColumnFilter"
                  @on-page-change="onPageChange" @on-per-page-change="onPerPageChange" :totalRows="totalRows"
                  :pagination-options="{
                      enabled: true,
                      mode: 'pages',
                    }" @on-search="globalFilter" @on-selected-rows-change="(selected_row) => {
                      globalCheckboxApplyData.data = selected_row;
                    }" :select-options="{
                      enabled: column,
                      selectOnCheckboxOnly: true, // only select when checkbox is clicked instead of the row
                      selectionInfoClass: 'text-primary bg-white',
                      selectionText: formTranslation.common.rows_selected,
                      clearSelectionText: formTranslation.common.clear,
                      disableSelectInfo: false, // disable the select info panel on top
                      selectAllByGroup: true, // when used in combination with a grouped table, add a checkbox in the header row to check/uncheck the entire group
                    }" styleClass="vgt-table striped" compactMode>
                  <div slot="emptystate" class="text-danger text-center" >
                    {{  isRowLoading ? '' : formTranslation.common.no_data_found }}
                  </div>
                  <template slot="column-filter" slot-scope="{ column, updateFilters }">
                    <div v-if="column.field === 'event_name' && eventNameColumn?.filterOptions?.enabled">
                      <select class="vgt-select" v-model="serverParams.columnFilters.event_name"
                        @change="() => {
                          updateFilters(
                            column,
                            serverParams.columnFilters.event_name
                          )
                        }">
                        <option value="">
                          {{ !serverParams.columnFilters.module_name || !(eventNameColumn?.filterOptions?.filterDropdownItemsCopy?.[serverParams.columnFilters.module_name]) ?
                            formTranslation.webhooks.please_first_select_module : column.filterOptions.placeholder  }}
                        </option>
                        <option v-for="(value, key) in eventNameColumn.filterOptions.filterDropdownItemsCopy[serverParams.columnFilters.module_name]" :value="value.value"
                          :key="key">
                          {{ value.text }}
                        </option>
                      </select>
                    </div>
                  </template>
                  <div slot="table-actions">
                    <module-data-export :module_data="rows"
                      :module_name="formTranslation.webhooks.webhooks_list || 'Webhooks list'"
                      :module_type="name">
                    </module-data-export>
                  </div>
                  <div slot="selected-row-actions">
                    <div class="d-flex justify-content-end align-items-center">
                      <select class="form-control form-control-sm" v-model="globalCheckboxApplyData.action_perform">
                        <option v-for="(
                            option
                          ) in globalCheckboxApplyDataActions" :value="option.value">
                          {{ option.label }}
                        </option>
                      </select>
                      <button class="ml-2 btn btn-sm btn-primary" @click="(params) => helperGlobalAction(params,getTableData)">
                        {{ formTranslation.common.apply }}
                      </button>
                    </div>
                  </div>
                  <template slot="table-row" slot-scope="props">
                    <div v-if="props.column.field === 'status'" class="d-flex">
                      <b-form-checkbox v-model="props.row.status" name="check-button" switch value="1" size="lg"
                        unchecked-value="0" :id="'status_change_' + props.row.id" @change="
                          changeModuleValueStatus({
                            module_type: name,
                            id: props.row.id,
                            value: props.row.status,
                          })
                          ">
                      </b-form-checkbox>
                      <span class="badge badge-success" v-if="props.row.status.toString() === '1'">
                        {{ formTranslation.common.active }}
                      </span>
                      <span class="badge badge-danger" v-if="props.row.status.toString() === '0'">
                        {{ formTranslation.common.inactive }}
                      </span>
                    </div>
                    <div class="btn-group" v-else-if="props.column.field === 'actions'">
                      <router-link class="btn btn-sm btn-outline-primary" v-b-tooltip.hover :title="formTranslation.clinic_schedule.dt_lbl_edit" :to="{
                        name: `${name}.edit`,
                        params: { id: props.row.id },
                      }">
                        <i class="fa fa-pen-alt"></i>
                      </router-link>
                      <button class="btn btn-outline-secondary btn-sm" :id="clone_ele + props.row.id" v-b-tooltip.hover :title="formTranslation.common.clone"
                        @click="cloneData(props.row.id)">
                        <i class="fa fa-clone"></i>
                      </button>
                      <router-link class="btn btn-sm btn-outline-primary" v-b-tooltip.hover :title="formTranslation.webhooks.view_logs" :to="{
                        name: `${name}.log`,
                        params: { webhook_id: props.row.id },
                      }">
                        <i class="fa fa-table"></i>
                      </router-link>
                      <button class="btn btn-outline-danger btn-sm" :id="delete_ele + props.row.id" v-b-tooltip.hover :title="formTranslation.clinic_schedule.dt_lbl_dlt"
                              @click="deleteData(props.row.id)">
                        <i class="fa fa-trash"></i>
                      </button>
                    </div>
                    <div v-else>
                      {{ column_label_hashmap[props.formattedRow[props.column.field]] || props.formattedRow[props.column.field] }}
                    </div>
                  </template>
                </vue-good-table>
              </div>
            </b-col>
          </b-row>
        </b-card>
      </b-overlay>
    </b-col>
  </b-row>
</template>
<script>
import {
  helperModuleTableData, helperModuleCloneData
} from "../../utils/list";
import * as globalAction from "../../utils/globalAction";
import { globalDeleteModuleData } from "../../utils/delete";
import { mapActions } from "vuex";
export default {
  data: () => {
    return {
      name: 'webhooks',
      column: [],
      rows: [],
      totalRows: 0,
      isRowLoading: false,
      isColumnLoading: false,
      serverParams: {
        columnFilters: {
          module_name: "",
          event_name: ""
        },
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
        type: "list",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      deleteRowId: -1,
      apiEndpoint: {
        list: 'webhooks_list',
        column: 'webhooks_column',
        delete: 'webhooks_delete',
        clone: 'webhooks_clone'
      },
      delete_ele: 'webhook_delete_',
      clone_ele:'webhook_clone_',
      globalCheckboxApplyData: {},
      globalCheckboxApplyDataActions: [],
      column_label_hashmap:{},
      eventNameColumn:{}
    };
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    if (this?.userData?.addOns?.webhooks) {
      this.init();
    }
  },
  methods: {
    ...globalAction,
    init() {
      this.getTableColumn();
      this.getTableData();
      this.globalCheckboxApplyData = this.helperDefaultGlobalCheckboxApplyData(this.name);
      this.globalCheckboxApplyDataActions = this.helperDefaultGlobalCheckboxApplyDataActions();
    },
    async getTableData() {
      this.isRowLoading = true;
      const data = await helperModuleTableData(this.apiEndpoint.list,this.serverParams)
      
      this.rows = data.rows || [];
      this.totalRows = data.response.total_row || 0;
      this.isRowLoading = false;
    },
    deleteData(id) {
      const options = {
        id: id,
        delete_ele:this.delete_ele,
        content_message:this.formTranslation.webhooks.delete_webhooks,
        endpoint:this.apiEndpoint.delete
      }
      const successCallback = () => {
        this.getTableData();
      }
      const failedCallback = () => {
        this.deleteRowId = -1;
      }
      globalDeleteModuleData(options,successCallback,failedCallback);
    },
    updateParamsAndTable(newProps){
      this.serverParams = Object.assign(
          {},
          this.serverParams,
          newProps
      );
      this.getTableData();
    },
    onPageChange(params) {
      this.updateParamsAndTable({
        page: params.currentPage,
      });
    },
    onPerPageChange (params)  {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParamsAndTable( {
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },
    onSortChange:(params) => {
      this.updateParamsAndTable({
        sort: params,
      });
    },
    globalFilter:_.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParamsAndTable({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 500),
    onColumnFilter: _.debounce(function (params) {
      let emptyValue = true;
      let emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value) {
        if (value) {
          emptyValue = false;
        }
      });
      Object.values(this.oldServerParams.columnFilters).map(function (
          value
      ) {
        if (value) {
          emptyValue2 = false;
        }
      });
      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
            {},
            params.columnFilters
        );
        this.updateParamsAndTable({
          columnFilters: params.columnFilters,
          perPage: this.serverParams.perPage,
          page: 1,
        });
      }
    }, 300),
    formatColumnData( column_data){
      Object.keys(column_data).forEach(( index) =>{
        if(column_data[index]?.filterOptions?.enabled){
          this.serverParams.columnFilters[index] = '';
        }
      } )
      if(column_data?.module_name && column_data?.module_name?.filterOptions?.filterDropdownItems){
        column_data.module_name.filterOptions.filterDropdownItems.forEach(item => {
          this.column_label_hashmap[item.value] = item.text;
        });
      }
      if(column_data?.event_name && column_data?.event_name?.filterOptions?.filterDropdownItemsCopy){
        Object.keys(column_data.event_name.filterOptions.filterDropdownItemsCopy).forEach(category => {
          column_data.event_name.filterOptions.filterDropdownItemsCopy[category].forEach(item => {
            this.column_label_hashmap[item.value] = item.text
          });
        });
      }
      if(column_data?.methods && column_data?.methods?.filterOptions?.filterDropdownItems){
        column_data.methods.filterOptions.filterDropdownItems.forEach(item => {
          this.column_label_hashmap[item.value] = item.text;
        });
      }
      this.eventNameColumn = column_data?.event_name;
    },
    async cloneData(id){
      const params = {
        cloneEle: this.clone_ele,
        id : id
      }
      const response = await helperModuleCloneData(this.apiEndpoint.clone,params)
      if(response){
        await this.getTableData();
      }
    },
    ...mapActions('tableModule', ["fetchTableColumns"]),
    async getTableColumn() {
      this.isColumnLoading = true
      const columns = await this.fetchTableColumns({
        endpoint: this.apiEndpoint.column,
        module: 'webhooks'
      });
      if(columns){
        this.column = Object.values(columns);
        this.formatColumnData(columns);
      }
      this.isColumnLoading = false
    }
  },
  computed: {
    userData() {
      return this?.$store?.state?.userDataModule && this?.$store?.state?.userDataModule?.user
        ? this.$store.state.userDataModule.user
        : [];
    }
  },
};
</script>
