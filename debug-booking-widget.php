<?php
/**
 * Plugin Name: KiviCare Booking Widget Debug
 * Description: Debug tool for the enhanced booking widget
 * Version: 1.0.0
 * Author: Claude
 */

// Add this script to the footer to check for issues
function kivicareBookingWidgetDebug() {
    ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('KiviCare Booking Widget Debug: Checking for widget elements');
            
            // Check if the widget container exists
            const widgetContainer = document.querySelector('.kivicare-enhanced-booking-widget');
            console.log('Widget container found:', widgetContainer ? true : false);
            
            // Check if Vue is loaded
            console.log('Vue loaded:', typeof window.Vue !== 'undefined');
            
            // Check for any errors in the Vue initialization
            if (typeof window.Vue !== 'undefined') {
                const appElement = document.getElementById('app');
                console.log('App element found:', appElement ? true : false);
                
                // Check if the component is registered
                console.log('BookingWidget component registered:', 
                    Vue.options && 
                    Vue.options.components && 
                    Vue.options.components['kivicare-booking-widget'] ? true : false);
            }
            
            // Log any JavaScript errors
            window.addEventListener('error', function(e) {
                console.log('JavaScript error detected:', e.message);
                console.log('Error source:', e.filename);
                console.log('Error line:', e.lineno);
            });
        });
    </script>
    <?php
}
add_action('wp_footer', 'kivicareBookingWidgetDebug');