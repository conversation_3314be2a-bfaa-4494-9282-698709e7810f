<template>
  <div v-if="showModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="closeModal">
      </div>

      <!-- Modal panel -->
      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- Header -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex justify-between items-center pb-3 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
              {{ modalTitle }}
            </h3>
            <button @click="closeModal" class="text-gray-400 hover:text-gray-500">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Body -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-[70vh] overflow-y-auto">
          <div v-if="loading" class="text-center p-5">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
            <span class="sr-only">{{ $t('common.loading') }}</span>
          </div>

          <div v-else>
            <!-- Tabs -->
            <div class="mb-4 border-b border-gray-200">
              <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
                <li class="mr-2" @click="activeTab = 0">
                  <a :class="[
                    'inline-block p-4 rounded-t-lg border-b-2',
                    activeTab === 0
                      ? 'text-black border-black'
                      : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                  ]">
                    Details
                  </a>
                </li>
                <li v-if="!isNew" class="mr-2" @click="activeTab = 1">
                  <a :class="[
                    'inline-block p-4 rounded-t-lg border-b-2',
                    activeTab === 1
                      ? 'text-black border-black'
                      : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                  ]">
                    Comments
                  </a>
                </li>
                <li v-if="!isNew" class="mr-2" @click="activeTab = 2">
                  <a :class="[
                    'inline-block p-4 rounded-t-lg border-b-2',
                    activeTab === 2
                      ? 'text-black border-black'
                      : 'border-transparent hover:text-gray-600 hover:border-gray-300'
                  ]">
                    Attachments
                  </a>
                </li>
              </ul>
            </div>

            <!-- Details Tab -->
            <div v-if="activeTab === 0" class="task-form space-y-4">
              <div>
                <label for="task-title" class="block text-sm font-medium text-gray-700">
                  {{ $t('task.title') }} <span class="text-red-500">*</span>
                </label>
                <input type="text" id="task-title"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm"
                  :class="{ 'border-red-500': validationErrors.title }" v-model="form.title"
                  :placeholder="$t('task.title_placeholder')" />
                <p v-if="validationErrors.title" class="mt-2 text-sm text-red-600">
                  {{ validationErrors.title }}
                </p>
              </div>

              <div>
                <label for="task-description" class="block text-sm font-medium text-gray-700">
                  {{ $t('task.description') }}
                </label>
                <textarea id="task-description"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm"
                  v-model="form.description" :placeholder="$t('task.description_placeholder')" rows="4"></textarea>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="task-clinic" class="block text-sm font-medium text-gray-700">
                    Clinic <span class="text-red-500">*</span>
                  </label>
                  <multiselect v-model="form.clinic" :options="clinicOptions" track-by="id" label="label"
                    :searchable="true" placeholder="Select a clinic (required)" class="mt-1"
                    :class="{ 'multiselect-invalid': validationErrors.clinic_id }" :loading="clinicOptions.length === 0"
                    :internal-search="true" :show-labels="false">
                    <template slot="noOptions">
                      <div class="text-center py-2 text-gray-500">
                        <template v-if="clinicOptions.length === 0">
                          <p>Loading clinics...</p>
                          <div
                            class="mt-2 inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-black">
                          </div>
                        </template>
                        <template v-else>
                          <p>No clinics available.</p>
                        </template>
                      </div>
                    </template>
                  </multiselect>
                  <p v-if="validationErrors.clinic_id" class="mt-2 text-sm text-red-600">
                    {{ validationErrors.clinic_id }}
                  </p>
                </div>

                <div>
                  <label for="task-patient" class="block text-sm font-medium text-gray-700">
                    Related Patient
                  </label>
                  <multiselect v-model="form.patient" :options="patientOptions" track-by="id" label="name"
                    :searchable="true" placeholder="Select a patient (optional)" class="mt-1"
                    :loading="form.clinic && patientOptions.length === 0" :disabled="!form.clinic"
                    :internal-search="true" :show-labels="false">
                    <template slot="noOptions">
                      <div class="text-center py-2 text-gray-500">
                        <template v-if="!form.clinic">
                          <p>Please select a clinic first</p>
                        </template>
                        <template v-else-if="patientOptions.length === 0">
                          <p>Loading patients...</p>
                          <div
                            class="mt-2 inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-black">
                          </div>
                        </template>
                        <template v-else>
                          <p>No patients available for this clinic.</p>
                        </template>
                      </div>
                    </template>
                  </multiselect>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ $t('task.priority') }}
                  </label>
                  <div class="mt-1 flex rounded-md shadow-sm">
                    <button type="button"
                      class="flex-1 py-2 px-4 text-sm font-medium rounded-l-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                      :class="{
                        'bg-black text-white': form.priority === 'low',
                        'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50': form.priority !== 'low'
                      }" @click="form.priority = 'low'">
                      <i class="ri-arrow-down-line mr-1"></i> {{ $t('task.priority_low') }}
                    </button>
                    <button type="button"
                      class="flex-1 py-2 px-4 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                      :class="{
                        'bg-yellow-500 text-white': form.priority === 'medium',
                        'bg-white text-gray-700 border-t border-b border-gray-300 hover:bg-gray-50': form.priority !== 'medium'
                      }" @click="form.priority = 'medium'">
                      <i class="ri-subtract-line mr-1"></i> {{ $t('task.priority_medium') }}
                    </button>
                    <button type="button"
                      class="flex-1 py-2 px-4 text-sm font-medium rounded-r-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                      :class="{
                        'bg-red-500 text-white': form.priority === 'high',
                        'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50': form.priority !== 'high'
                      }" @click="form.priority = 'high'">
                      <i class="ri-arrow-up-line mr-1"></i> {{ $t('task.priority_high') }}
                    </button>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ $t('task.status') }}
                  </label>
                  <multiselect v-model="form.status_option" :options="statusOptions" track-by="id" label="label"
                    :searchable="false" :placeholder="$t('task.select_status')" class="mt-1"></multiselect>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    Due Date
                  </label>
                  <div class="mt-1">
                    <input type="date" v-model="form.due_date"
                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm"
                      placeholder="Select due date" />
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    Reminder
                  </label>
                  <div class="mt-1">
                    <input type="date" v-model="form.reminder_date"
                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm"
                      placeholder="Select reminder date" />
                  </div>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">
                  Assignees <span class="text-sm text-gray-500">(Doctors, Receptionists, Admins)</span>
                </label>
                <multiselect v-model="form.assignees" :options="userOptions" track-by="id" label="name" :multiple="true"
                  :searchable="true" placeholder="Select staff to assign to this task" class="mt-1"
                  :loading="userOptions.length === 0" :internal-search="true" :max-height="300" :show-labels="false">
                  <template slot="tag" slot-scope="{ option, remove }">
                    <span class="multiselect__tag">
                      <span class="flex items-center">
                        <span
                          class="inline-flex items-center justify-center h-5 w-5 rounded-full mr-1 text-xs text-white"
                          :style="{ backgroundColor: getAvatarColor(option.name) }">
                          {{ getInitials(option.name) }}
                        </span>
                        <span>{{ option.name }}</span>
                        <span v-if="option.role" class="text-xs ml-1 text-gray-500">({{ option.role.replace('_', ' ')
                        }})</span>
                      </span>
                      <i aria-hidden="true" tabindex="1" @click="remove(option)" class="multiselect__tag-icon"></i>
                    </span>
                  </template>

                  <template slot="option" slot-scope="{ option }">
                    <div class="flex items-center justify-between w-full">
                      <div class="flex items-center">
                        <span
                          class="inline-flex items-center justify-center h-6 w-6 rounded-full mr-2 text-xs text-white"
                          :style="{ backgroundColor: getAvatarColor(option.name) }">
                          {{ getInitials(option.name) }}
                        </span>
                        <span>{{ option.name }}</span>
                      </div>
                      <span v-if="option.role" class="text-xs px-2 py-1 rounded-full" :class="{
                        'bg-blue-100 text-blue-800': option.role === 'doctor',
                        'bg-purple-100 text-purple-800': option.role === 'receptionist',
                        'bg-green-100 text-green-800': option.role === 'clinic_admin' || option.role === 'administrator',
                        'bg-gray-100 text-gray-800': !['doctor', 'receptionist', 'clinic_admin', 'administrator'].includes(option.role)
                      }">
                        {{ option.role.replace('_', ' ') }}
                      </span>
                    </div>
                  </template>

                  <template slot="noResult">
                    No staff members found. Try a different search term.
                  </template>

                  <template slot="noOptions">
                    <div class="text-center py-2 text-gray-500">
                      <template v-if="userOptions.length === 0">
                        <p>Loading staff members...</p>
                        <div
                          class="mt-2 inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-black">
                        </div>
                      </template>
                      <template v-else>
                        <p>No staff members available to assign.</p>
                      </template>
                    </div>
                  </template>
                </multiselect>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <multiselect v-model="form.category_option" :options="categoryOptions" track-by="id" label="label"
                  :searchable="false" placeholder="Select a category" class="mt-1">
                  <template slot="option" slot-scope="{ option }">
                    <div class="flex items-center">
                      <span class="w-3 h-3 rounded-full mr-2" :class="{
                        'bg-gray-500': option.id === 'administrative',
                        'bg-blue-500': option.id === 'clinical',
                        'bg-green-500': option.id === 'patient-followup',
                        'bg-yellow-500': option.id === 'billing',
                        'bg-purple-500': option.id === 'general'
                      }"></span>
                      <span>{{ option.label }}</span>
                    </div>
                  </template>
                </multiselect>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">
                  Repeating Schedule
                </label>
                <multiselect v-model="form.repeating_option" :options="repeatingOptions" track-by="id" label="label"
                  :searchable="false" placeholder="Select a repeating schedule" class="mt-1"></multiselect>
              </div>
            </div>

            <!-- Comments Tab -->
            <div v-if="activeTab === 1 && !isNew">
              <task-comments :task-id="taskId" @comment-added="onCommentAdded" />
            </div>

            <!-- Attachments Tab -->
            <div v-if="activeTab === 2 && !isNew">
              <task-attachments :task-id="taskId" @attachment-uploaded="onAttachmentUploaded" />
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse justify-between">
          <div class="flex space-x-2">
            <button type="button"
              class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black sm:mt-0 sm:w-auto sm:text-sm"
              @click="closeModal">
              {{ $t('common.cancel') }}
            </button>
            <button type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-black text-base font-medium text-white hover:bg-black-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black sm:ml-3 sm:w-auto sm:text-sm"
              @click="saveTask" :disabled="saving">
              <svg v-if="saving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              <span>{{ saving ? $t('common.saving') : $t('common.save') }}</span>
            </button>
          </div>

          <div>
            <button v-if="!isNew" type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
              @click="confirmDelete">
              {{ $t('common.delete') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

import { mapGetters } from 'vuex';
import Multiselect from 'vue-multiselect';
import TaskComments from './TaskComments.vue';
import TaskAttachments from './TaskAttachments.vue';

export default {
  name: 'TaskModal',
  components: {
    Multiselect,
    TaskComments,
    TaskAttachments
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: [Number, String],
      default: null
    },
    isNew: {
      type: Boolean,
      default: true
    },
    initialDate: {
      type: Date,
      default: null
    }
  },
  data() {
    return {
      showModal: false,
      loading: false,
      saving: false,
      task: null,
      activeTab: 0,
      clinics: [],
      form: {
        title: '',
        description: '',
        clinic: null,
        patient: null,
        doctor: null,
        priority: 'medium',
        status_option: null,
        due_date: null,
        reminder_date: null,
        assignees: [],
        category_option: null,
        repeating_option: null
      },

      validationErrors: {},

      clinicOptions: [],
      patientOptions: [],
      doctorOptions: [],
      userOptions: [],

      statusOptions: [
        { id: 'pending', label: 'Pending' },
        { id: 'in-progress', label: 'In Progress' },
        { id: 'completed', label: 'Completed' },
        { id: 'cancelled', label: 'Cancelled' }
      ],

      categoryOptions: [
        { id: 'administrative', label: 'Administrative' },
        { id: 'clinical', label: 'Clinical' },
        { id: 'patient-followup', label: 'Patient Follow-up' },
        { id: 'billing', label: 'Billing' },
        { id: 'general', label: 'General' }
      ],

      repeatingOptions: [
        { id: 'none', label: 'None' },
        { id: 'daily', label: 'Daily' },
        { id: 'weekly', label: 'Weekly' },
        { id: 'monthly', label: 'Monthly' }
      ]
    }
  },
  computed: {
    ...mapGetters({
      isAdmin: 'getUserIsAdmin',
      isClinicAdmin: 'getUserIsClinicAdmin',
      currentUser: 'getUserData',
      userClinics: 'getUserClinics'
    }),

    modalTitle() {
      return this.isNew
        ? this.$t('task.new_task')
        : this.$t('task.edit_task');
    }
  },
  watch: {
    show(val) {
      this.showModal = val;

      if (val) {
        this.resetForm();
        this.fetchOptions();

        if (!this.isNew && this.taskId) {
          this.fetchTask();
        }

        if (this.initialDate) {
          this.form.due_date = this.initialDate;
        }
      }
    },

    'form.clinic'(val) {
      if (val) {
        this.fetchClinicPatients(val.id);
      } else {
        this.patientOptions = [];
      }
    }
  },
  created() {
    // Set default status
    this.form.status_option = this.statusOptions.find(opt => opt.id === 'pending');
    this.form.repeating_option = this.repeatingOptions.find(opt => opt.id === 'none');
  },
  methods: {
    resetForm() {
      this.form = {
        title: '',
        description: '',
        clinic: null,
        patient: null,
        doctor: null,
        priority: 'medium',
        status_option: this.statusOptions.find(opt => opt.id === 'pending'),
        due_date: null,
        reminder_date: null,
        assignees: [],
        category_option: null,
        repeating_option: this.repeatingOptions.find(opt => opt.id === 'none')
      };

      this.validationErrors = {};
      this.activeTab = 0;
    },

    async fetchOptions() {
      try {

        if (this.getUserRole() === 'administrator') {
          this.getClinics();
        } else {
          this.clinicOptions = this.userClinics;

        }

        // // Fetch all clinics
        // const clinicResponse = await axios.get(`${window.apiUrl}/clinics`);

        // if (clinicResponse.data && clinicResponse.data.status) {
        //   this.clinicOptions = clinicResponse.data.data.map(clinic => ({
        //     id: clinic.id,
        //     name: clinic.name
        //   }));

        //   // Set default clinic if only one option is available
        //   if (this.clinicOptions.length === 1) {
        //     this.form.clinic = this.clinicOptions[0];
        //   }
        // } else {
        //   // Fallback to user's clinics from store if API fails
        //   this.clinicOptions = this.userClinics.map(clinic => ({
        //     id: clinic.id,
        //     name: clinic.name
        //   }));

        //   if (this.clinicOptions.length === 1) {
        //     this.form.clinic = this.clinicOptions[0];
        //   }
        // }

        // // Fetch all staff users for assignee selection
        // try {
        //   const staffResponse = await axios.get(`${window.apiUrl}/get-doctors`);

        //   if (staffResponse.data && staffResponse.data.status) {
        //     let staffUsers = staffResponse.data.data.map(user => ({
        //       id: user.ID || user.id,
        //       name: user.display_name || (user.first_name + ' ' + user.last_name).trim(),
        //       role: 'doctor'
        //     }));

        //     // Fetch receptionists too
        //     const receptionistResponse = await axios.get(`${window.apiUrl}/get-receptionists`);

        //     if (receptionistResponse.data && receptionistResponse.data.status) {
        //       const receptionists = receptionistResponse.data.data.map(user => ({
        //         id: user.ID || user.id,
        //         name: user.display_name || (user.first_name + ' ' + user.last_name).trim(),
        //         role: 'receptionist'
        //       }));

        //       staffUsers = [...staffUsers, ...receptionists];
        //     }

        //     // Fetch clinic admins too
        //     const adminResponse = await axios.get(`${window.apiUrl}/get-clinic-admin`);

        //     if (adminResponse.data && adminResponse.data.status) {
        //       const admins = adminResponse.data.data.map(user => ({
        //         id: user.ID || user.id,
        //         name: user.display_name || (user.first_name + ' ' + user.last_name).trim(),
        //         role: 'clinic_admin'
        //       }));

        //       staffUsers = [...staffUsers, ...admins];
        //     }

        //     // Remove duplicates
        //     this.userOptions = staffUsers.filter((user, index, self) =>
        //       index === self.findIndex((u) => u.id === user.id)
        //     );
        //   }
        // } catch (staffError) {
        //   console.error('Error fetching staff:', staffError);

        //   // Fallback to general staff API
        //   try {
        //     const allStaffResponse = await axios.get(`${window.apiUrl}/users/clinic-staff`);

        //     if (allStaffResponse.data && allStaffResponse.data.status) {
        //       this.userOptions = allStaffResponse.data.data.map(user => ({
        //         id: user.id,
        //         name: user.display_name || user.username || (user.first_name + ' ' + user.last_name).trim(),
        //         role: user.role || 'staff'
        //       }));
        //     }
        //   } catch (fallbackError) {
        //     console.error('Error fetching staff fallback:', fallbackError);
        //   }
        // }
      } catch (error) {
        console.error('Error fetching options:', error);
        // Fallback to user's clinics from store if API fails

      }
    },

    async getClinics() {
      this.clinicMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_list",
        });

        if (response.data.status) {
          this.clinicOptions = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinics:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    async fetchClinicPatients(clinicId) {
      if (!clinicId) return;

      try {
        this.patientOptions = []; // Clear existing options

        // Fetch patients for the selected clinic
        const patientResponse = await axios.get(`${window.apiUrl}/patients`, {
          params: {
            clinic_id: clinicId,
            per_page: 100 // Increase limit to get more patients
          }
        });

        if (patientResponse.data && patientResponse.data.status && patientResponse.data.data.length > 0) {
          this.patientOptions = patientResponse.data.data.map(patient => ({
            id: patient.id,
            name: patient.display_name || `${patient.first_name} ${patient.last_name}`.trim()
          }));
        } else {
          // Try alternative endpoint if the first one fails or returns empty
          const altResponse = await axios.get(`${window.apiUrl}/clinic/patients/${clinicId}`);

          if (altResponse.data && altResponse.data.status && altResponse.data.data.length > 0) {
            this.patientOptions = altResponse.data.data.map(patient => ({
              id: patient.ID || patient.id,
              name: patient.display_name || `${patient.first_name} ${patient.last_name}`.trim()
            }));
          }
        }
      } catch (error) {
        console.error('Error fetching clinic patients:', error);
      }
    },

    async fetchTask() {
      if (!this.taskId) return;

      try {
        this.loading = true;

        const response = await axios.get(`${window.apiUrl}/tasks/${this.taskId}`);

        if (response.data && response.data.status) {
          const task = response.data.data;
          this.task = task;

          // Populate form fields
          this.form.title = task.title || '';
          this.form.description = task.description || '';

          // Set clinic
          if (task.clinic_id) {
            this.form.clinic = this.clinicOptions.find(c => c.id === task.clinic_id) || null;
          }

          // Set patient (will be fetched once clinic is set)
          if (task.patient_id) {
            await this.fetchClinicPatients(task.clinic_id);
            this.form.patient = this.patientOptions.find(p => p.id === task.patient_id) || null;
          }

          // Set priority
          this.form.priority = task.priority || 'medium';

          // Set status
          this.form.status_option = this.statusOptions.find(s => s.id === task.status) ||
            this.statusOptions.find(s => s.id === 'pending');

          // Set dates - format as YYYY-MM-DD for HTML date input
          if (task.due_date) {
            const dueDate = new Date(task.due_date);
            if (!isNaN(dueDate.getTime())) {
              this.form.due_date = dueDate.toISOString().split('T')[0];
            }
          } else {
            this.form.due_date = null;
          }

          if (task.reminder_date) {
            const reminderDate = new Date(task.reminder_date);
            if (!isNaN(reminderDate.getTime())) {
              this.form.reminder_date = reminderDate.toISOString().split('T')[0];
            }
          } else {
            this.form.reminder_date = null;
          }

          // Set assignees
          if (task.assignees && task.assignees.length) {
            this.form.assignees = task.assignees.map(assignee => ({
              id: assignee.id,
              name: assignee.name
            }));
          }

          // Set category
          if (task.category) {
            this.form.category_option = this.categoryOptions.find(c => c.id === task.category) || null;
          }

          // Set repeating
          if (task.repeating) {
            this.form.repeating_option = this.repeatingOptions.find(r => r.id === task.repeating) ||
              this.repeatingOptions.find(r => r.id === 'none');
          }
        } else {
          this.$swal.fire({
            icon: 'error',
            title: this.$t('common.error'),
            text: response.data.message || this.$t('task.fetch_error')
          });
          this.closeModal();
        }
      } catch (error) {
        console.error('Error fetching task:', error);
        this.$swal.fire({
          icon: 'error',
          title: this.$t('common.error'),
          text: this.$t('task.fetch_error')
        });
        this.closeModal();
      } finally {
        this.loading = false;
      }
    },

    getInitials(name) {
      if (!name) return '?';

      return name.split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    },

    getAvatarColor(name) {
      if (!name) return '#6c757d';

      // Generate consistent color based on name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      const colors = [
        '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
        '#1abc9c', '#d35400', '#c0392b', '#16a085', '#8e44ad',
        '#27ae60', '#2980b9', '#f1c40f', '#e67e22', '#ecf0f1'
      ];

      return colors[Math.abs(hash) % colors.length];
    },

    validateForm() {
      this.validationErrors = {};

      if (!this.form.title || this.form.title.trim() === '') {
        this.validationErrors.title = this.$t('task.error_title_required');
      }

      if (!this.form.clinic) {
        this.validationErrors.clinic_id = this.$t('task.error_clinic_required');
      }

      return Object.keys(this.validationErrors).length === 0;
    },

    formatDateForApi(date) {
      if (!date) return null;

      // If date is already a string in YYYY-MM-DD format, return it
      if (typeof date === 'string') {
        // Check if the string is in YYYY-MM-DD format
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          return date;
        }
      }

      // Format date as YYYY-MM-DD
      const d = new Date(date);
      // Check if date is valid
      if (isNaN(d.getTime())) return null;

      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    },

    prepareFormData() {
      // Create an array of assignee IDs, ensuring no duplicates
      const assigneeIds = this.form.assignees.map(assignee => assignee.id);

      // If there's a doctor selected and not already in assignees, add them
      if (this.form.doctor && !assigneeIds.includes(this.form.doctor.id)) {
        assigneeIds.push(this.form.doctor.id);
      }

      return {
        title: this.form.title,
        description: this.form.description,
        clinic_id: this.form.clinic ? this.form.clinic.id : null,
        patient_id: this.form.patient ? this.form.patient.id : null,
        doctor_id: this.form.doctor ? this.form.doctor.id : null,
        priority: this.form.priority,
        status: this.form.status_option ? this.form.status_option.id : 'pending',
        due_date: this.formatDateForApi(this.form.due_date),
        reminder_date: this.formatDateForApi(this.form.reminder_date),
        category: this.form.category_option ? this.form.category_option.id : null,
        repeating: this.form.repeating_option ? this.form.repeating_option.id : 'none',
        assignees: assigneeIds
      };
    },

    async saveTask() {
      if (!this.validateForm()) {
        this.activeTab = 0; // Switch back to details tab if there are errors
        return;
      }

      try {
        this.saving = true;
        const formData = this.prepareFormData();

        let response;
        if (this.isNew) {
          response = await post("save_task", formData);
        } else {
          response = await post(`save_task/${this.taskId}`, formData, {
            method: 'PUT'
          });
        }

        if (response.data && response.data.status) {
          this.$swal.fire({
            icon: 'success',
            title: "Success",
            text: this.isNew
              ? this.$t('task.create_success')
              : this.$t('task.update_success'),
            showConfirmButton: false,
            timer: 1500
          });

          this.$emit('task-saved', response.data.data);
          this.closeModal();
        } else {
          this.$swal.fire({
            icon: 'error',
            title: this.$t('common.error'),
            text: response.data.message || this.$t('common.unknown_error')
          });
        }
      } catch (error) {
        console.error('Error saving task:', error);

        if (error.response && error.response.data && error.response.data.message) {
          this.$swal.fire({
            icon: 'error',
            title: this.$t('common.error'),
            text: error.response.data.message
          });
        } else {
          this.$swal.fire({
            icon: 'error',
            title: this.$t('common.error'),
            text: this.$t('common.unknown_error')
          });
        }
      } finally {
        this.saving = false;
      }
    },

    confirmDelete() {
      this.$swal.fire({
        title: this.$t('task.confirm_delete'),
        text: this.$t('task.delete_confirmation_text'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        confirmButtonText: this.$t('common.delete'),
        cancelButtonText: this.$t('common.cancel')
      }).then((result) => {
        if (result.isConfirmed) {
          this.deleteTask();
        }
      });
    },

    async deleteTask() {
      if (!this.taskId) return;

      try {
        const response = await axios.delete(`${window.apiUrl}/tasks/${this.taskId}`);

        if (response.data && response.data.status) {
          this.$swal.fire({
            icon: 'success',
            title: this.$t('common.success'),
            text: this.$t('task.delete_success'),
            showConfirmButton: false,
            timer: 1500
          });

          this.$emit('task-deleted', this.taskId);
        } else {
          this.$swal.fire({
            icon: 'error',
            title: this.$t('common.error'),
            text: response.data.message || this.$t('task.delete_error')
          });
        }
      } catch (error) {
        console.error('Error deleting task:', error);
        this.$swal.fire({
          icon: 'error',
          title: this.$t('common.error'),
          text: this.$t('task.delete_error')
        });
      }
    },

    onCommentAdded() {
      // You can implement any additional logic when a comment is added
    },

    onAttachmentUploaded() {
      // You can implement any additional logic when an attachment is uploaded
    },

    closeModal() {
      this.showModal = false;
      this.$emit('close');
    },

    onHide() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
/* Tailwind CSS already includes the necessary styles */

/* Custom styling for multiselect */
:deep(.multiselect-invalid .multiselect__tags) {
  border-color: #ef4444;
}
</style>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>