import { get,post } from "../config/request";

export const helperModuleGetEditData = async (endpoint,params) => {
  try {
    const serverParams = Object.assign({},params);
    const response = await get(endpoint,serverParams);
    return response?.data?.status && response.data.status === true && response?.data?.data ? response.data.data : null
  } catch (error) {
    displayErrorMessage(window?.__kivicarelang?.widgets?.record_not_found);
    console.error(error);
    return null;
  }
};

export const helperModuleSaveData = async (endpoint, params) => {
  try {
    const serverParams = Object.assign({},params);
    const response = await post(endpoint,serverParams);
    if(response?.data?.status && response.data.status === true){
      displayMessage(response.data.message);
      return true;
    }else{
      displayErrorMessage(response.data.message);
      return false
    }
  } catch (error) {
    displayErrorMessage(window?.__kivicarelang?.widgets?.record_not_found);
    console.error(error);
    return false;
  }
};
