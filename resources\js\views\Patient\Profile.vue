<!-- PatientProfile.vue -->
<template>
  <div>
    <div class="page-loader-section" v-if="formLoader">
      <loader-component-2></loader-component-2>
    </div>
    <div v-else class="p-6 max-w-7xl mx-auto">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <div
            class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <img
              v-if="patientData.user_profile"
              :src="patientData.user_profile"
              class="w-16 h-16 rounded-full object-cover"
              alt="Profile"
            />
            <svg
              v-else
              xmlns="http://www.w3.org/2000/svg"
              class="w-8 h-8 text-purple-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold">
              {{ patientData.display_name || "Patient Name" }}
            </h1>
            <p class="text-gray-600">
              Patient ID: {{ patientData.u_id || "--" }}
            </p>
          </div>
        </div>
        <button
          @click="toggleEditMode"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
        >
          {{ isEditMode ? "View Profile" : "Edit Profile" }}
        </button>
      </div>

      <!-- Navigation -->
      <div class="flex space-x-2 mb-6 border-b overflow-x-auto">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="currentTab = tab.id"
          :class="[
            'flex items-center px-4 py-2 border-b-2 whitespace-nowrap',
            currentTab === tab.id
              ? 'border-purple-600 text-purple-600'
              : 'border-transparent text-gray-600 hover:text-purple-600',
          ]"
        >
          <i :class="tab.icon" class="w-4 h-4 mr-2"></i>
          {{ tab.name }}
        </button>
      </div>

      <!-- Content -->
      <div class="space-y-6">
        <personal-info
          v-if="currentTab === 'personal'"
          :is-edit-mode="isEditMode"
          :patient-data="patientData"
          @cancel="toggleEditMode"
          @saved="handleSaved"
        />

        <health-profile
          v-if="currentTab === 'health'"
          :is-edit-mode="isEditMode"
          :patient-data="patientData"
          :custom-fields="customFields"
          @cancel="toggleEditMode"
          @saved="handleSaved"
        />

        <patient-documents
          v-if="currentTab === 'documents'"
          :patient-data="patientData"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";
import { displayErrorMessage } from "../../utils/message";
import PersonalInfo from "../../components/Patient/PersonalInfo";
import HealthProfile from "../../components/Patient/HealthProfile";
import PatientDocuments from "../../components/Patient/PatientDocuments";

export default {
  name: "PatientProfile",

  components: {
    PersonalInfo,
    HealthProfile,
    PatientDocuments,
  },

  data: () => ({
    isEditMode: false,
    currentTab: "personal",
    formLoader: true,
    tabs: [
      { id: "personal", name: "Personal Info", icon: "lucide lucide-user" },
      { id: "health", name: "Health Profile", icon: "lucide lucide-heart" },
      { id: "documents", name: "Documents", icon: "lucide lucide-file-text" },
    ],
    patientData: {
      display_name: "",
      ID: "",
      user_profile: null,
    },
    customFields: {},
    originalData: null,
  }),

  mounted() {
    this.init();
  },

  methods: {
    async init() {
      const userId = this.$store.state.userDataModule.user.ID;
      if (userId) {
        await this.fetchPatientData(userId);
      } else {
        await this.$store.dispatch("userDataModule/fetchUserData", {});
        setTimeout(async () => {
          await this.fetchPatientData(this.$store.state.userDataModule.user.ID);
        }, 1000);
      }
    },

    async fetchPatientData(profileId) {
      this.formLoader = true;
      try {
        const response = await get("patient_edit", { id: profileId });
        if (response.data.status) {
          this.patientData = response.data.data;
          this.customFields = response.data.custom_filed;
          this.originalData = JSON.parse(JSON.stringify(response.data.data));
        }
      } catch (error) {
        displayErrorMessage(this.formTranslation.widgets.record_not_found);
      } finally {
        this.formLoader = false;
      }
    },

    toggleEditMode() {
      this.isEditMode = !this.isEditMode;
      if (!this.isEditMode) {
        this.patientData = { ...this.originalData };
      } else {
        this.originalData = { ...this.patientData };
      }
    },

    handleSaved(newData) {
      this.patientData = { ...newData };
      this.isEditMode = false;
      this.fetchPatientData(this.$store.state.userDataModule.user.ID);
    },
  },
};
</script>
