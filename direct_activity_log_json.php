<?php
/*
* Direct access activity logs - JSON API
* This is for debugging purposes only
*/

// Find the WordPress installation path
$path = __DIR__;
while (!file_exists($path . '/wp-config.php')) {
    $path = dirname($path);
    if ($path === '/' || empty($path)) {
        die('WordPress installation not found.');
    }
}
require_once($path . '/wp-load.php');

// Ensure user is logged in
if (!is_user_logged_in()) {
    wp_send_json([
        'status' => false,
        'message' => 'Not authorized',
        'logs' => []
    ]);
    exit;
}

global $wpdb;
$table_name = $wpdb->prefix . 'kc_activity_logs';

// Make sure the activity logs table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
if (!$table_exists) {
    require_once(__DIR__ . '/app/database/kc-activity-log-db.php');
    kivicareCreateActivityLogTable();
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    if (!$table_exists) {
        wp_send_json([
            'status' => false,
            'message' => 'Failed to create activity logs table',
            'logs' => []
        ]);
        exit;
    }
}

// Get user role and ID
$user_id = get_current_user_id();
$user = wp_get_current_user();
$roles = (array) $user->roles;
$current_role = reset($roles);

// Determine what logs the user should see based on role
$where_clause = '';
$where_params = [];

// Administrators see everything
if (in_array('administrator', $roles)) {
    $where_clause = '1=1';
} 
// Clinic admins see logs from their clinic
else if (strpos($current_role, 'clinic_admin') !== false) {
    $clinic_id = kcGetClinicIdOfClinicAdmin();
    if ($clinic_id) {
        $where_clause = '(clinic_id = %d OR user_id = %d)';
        $where_params[] = $clinic_id;
        $where_params[] = $user_id;
    } else {
        $where_clause = 'user_id = %d';
        $where_params[] = $user_id;
    }
} 
// Doctors see their logs and their patients' logs
else if (strpos($current_role, 'doctor') !== false) {
    $where_clause = '(user_id = %d OR (patient_id IN (SELECT patient_id FROM ' . $wpdb->prefix . 'kc_patient_clinic_mapping WHERE doctor_id = %d)))';
    $where_params[] = $user_id;
    $where_params[] = $user_id;
} 
// Patients see only their logs
else if (strpos($current_role, 'patient') !== false) {
    $where_clause = 'user_id = %d OR patient_id = %d';
    $where_params[] = $user_id;
    $where_params[] = $user_id;
} 
// Receptionists and others see only their logs
else {
    $where_clause = 'user_id = %d';
    $where_params[] = $user_id;
}

// Build query
$query = "SELECT * FROM {$table_name} WHERE {$where_clause} ORDER BY id DESC LIMIT 500";
if (!empty($where_params)) {
    $query = $wpdb->prepare($query, $where_params);
}

// Get logs
$logs = $wpdb->get_results($query);

// Get user details for each log
$processed_logs = [];
foreach ($logs as $log) {
    $user_data = get_userdata($log->user_id);
    $log->user_name = ($user_data) ? $user_data->display_name : 'Unknown User';
    
    // Map role names for consistency
    $role_map = [
        'kivicare_clinic_admin' => 'Clinic Admin',
        'kivicare_doctor' => 'Doctor',
        'kivicare_patient' => 'Patient',
        'kivicare_receptionist' => 'Admin Staff',
        'clinic_admin' => 'Clinic Admin',
        'doctor' => 'Doctor',
        'patient' => 'Patient',
        'receptionist' => 'Admin Staff',
        'administrator' => 'Administrator'
    ];
    
    // Add original role for reference
    $log->original_user_type = $log->user_type;
    
    // Set mapped role if it exists
    if (isset($role_map[$log->user_type])) {
        $log->display_role = $role_map[$log->user_type];
    } else {
        // Fallback formatting
        $role = $log->user_type;
        if (strpos($role, 'kivicare_') === 0) {
            $role = substr($role, 9);
        }
        $log->display_role = ucwords(str_replace('_', ' ', $role));
    }
    
    $processed_logs[] = $log;
}

// Return JSON response
wp_send_json([
    'status' => true,
    'message' => 'Activity logs retrieved successfully',
    'logs' => $processed_logs
]);