<!-- ToggleSwitch.vue -->
<!-- <template>
    <label class="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        class="sr-only"
        :checked="value === 'on'"
        @change="toggleValue"
        :disabled="disabled"
      />
      <div
        :class="{
          'bg-blue-600': value === 'on',
          'bg-gray-300': value === 'off',
          'opacity-50': disabled
        }"
        class="w-11 h-6 rounded-full relative transition-all"
      >
        <div
          :class="{
            'left-6': value === 'on',
            'left-0.5': value === 'off'
          }"
          class="absolute top-0.5 bg-white border-gray-300 border rounded-full h-5 w-5 transition-all"
        ></div>
      </div>
      <span v-if="label" class="ml-3 text-sm font-medium text-gray-900">
        {{ label }}
      </span>
    </label>
  </template>
  
  <script>
  export default {
    name: 'ToggleSwitch',
    props: {
      value: {
        type: String,
        default: 'off'
      },
      label: {
        type: String,
        default: ''
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    emits: ['input', 'change'],
    methods: {
      toggleValue(event) {
        if (this.disabled) return
        
        const newValue = event.target.checked ? 'on' : 'off'
        this.$emit('input', newValue)
        this.$emit('change', event)
      }
    }
  }
  </script> -->

<!-- ToggleSwitch.vue -->
<!-- <template>
    <label class="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        class="sr-only"
        :checked="isChecked"
        @change="toggleValue"
        :disabled="disabled"
      />
      <div
        :class="{
          'bg-blue-600': isChecked,
          'bg-gray-300': !isChecked,
          'opacity-50': disabled
        }"
        class="w-11 h-6 rounded-full relative transition-all"
      >
        <div
          :class="{
            'left-6': isChecked,
            'left-0.5': !isChecked
          }"
          class="absolute top-0.5 bg-white border-gray-300 border rounded-full h-5 w-5 transition-all"
        ></div>
      </div>
      <span v-if="label" class="ml-3 text-sm font-medium text-gray-900">
        {{ label }}
      </span>
    </label>
  </template>
  
  <script>
  export default {
    name: 'ToggleSwitch',
    props: {
      value: {
        type: [String, Number, Boolean],
        required: true
      },
      onValue: {
        type: [String, Number, Boolean],
        required: true
      },
      offValue: {
        type: [String, Number, Boolean],
        required: true
      },
      label: {
        type: String,
        default: ''
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      isChecked() {
        return this.value === this.onValue;
      }
    },
    emits: ['input', 'change'],
    methods: {
      toggleValue(event) {
        if (this.disabled) return
        
        const newValue = event.target.checked ? this.onValue : this.offValue
        this.$emit('input', newValue)
        this.$emit('change', event)
      }
    }
  }
  </script> -->

<!-- ToggleSwitch.vue -->
<template>
  <label class="relative inline-flex items-center cursor-pointer">
    <input
      type="checkbox"
      class="sr-only"
      :checked="isChecked"
      @change="toggleValue"
      :disabled="disabled"
    />
    <div
      :class="{
        'bg-black': isChecked,
        'bg-gray-300': !isChecked,
        'opacity-50': disabled,
      }"
      class="w-11 h-6 rounded-full relative transition-all"
    >
      <div
        :class="{
          'left-6': isChecked,
          'left-0.5': !isChecked,
        }"
        class="absolute top-0.5 bg-white border-gray-300 border rounded-full h-5 w-5 transition-all"
      ></div>
    </div>
    <slot>
      <span v-if="label" class="ml-3 text-sm font-medium text-gray-900">
        {{ label }}
      </span>
    </slot>
  </label>
</template>

<script>
export default {
  name: "ToggleSwitch",
  props: {
    value: {
      type: [String, Number, Boolean],
      required: true,
    },
    onValue: {
      type: [String, Number, Boolean],
      required: true,
    },
    offValue: {
      type: [String, Number, Boolean],
      required: true,
    },
    label: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isChecked() {
      return this.value === this.onValue;
    },
  },
  emits: ["input", "change"],
  methods: {
    toggleValue(event) {
      if (this.disabled) return;

      const newValue = event.target.checked ? this.onValue : this.offValue;
      this.$emit("input", newValue);
      this.$emit("change", event);
    },
  },
};
</script>
