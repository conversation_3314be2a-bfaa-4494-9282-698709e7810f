<?php

// Your existing encounter query
global $wpdb;
$patient_encounter_table = $wpdb->prefix . 'kc_' . 'patient_encounters';
$clinics_table = $wpdb->prefix . 'kc_' . 'clinics';
$users_table = $wpdb->base_prefix . 'users';
$medical_history_table = $wpdb->prefix . 'kc_medical_history';
$custom_field = $wpdb->prefix . 'kc_custom_fields';
$custom_field_data = $wpdb->prefix . 'kc_custom_fields_data';
$encounter_tabs_table = $wpdb->prefix . 'kc_encounter_tabs';

$query = "SELECT {$patient_encounter_table}.*,
   {$patient_encounter_table}.description,
   {$patient_encounter_table}.status AS Estatus,
   doctors.display_name AS doctor_name,
   doctors.user_email AS doctor_email,    
   patients.display_name AS patient_name,
   patients.user_email AS patient_email,
   {$clinics_table}.* 
    FROM {$patient_encounter_table}
    LEFT JOIN {$users_table} doctors
            ON {$patient_encounter_table}.doctor_id = doctors.id
    LEFT JOIN {$users_table} patients
            ON {$patient_encounter_table}.patient_id = patients.id
    LEFT JOIN {$clinics_table}
            ON {$patient_encounter_table}.clinic_id = {$clinics_table}.id
    WHERE {$patient_encounter_table}.id = {$request_data['encounter_id']} LIMIT 1";

$encounter = $this->db->get_row($query);

// Get tabs data
$tabs_query = "SELECT * FROM {$encounter_tabs_table} 
   WHERE encounter_id = {$request_data['encounter_id']} 
   ORDER BY created_at ASC";
$tabs_data = $this->db->get_results($tabs_query);

// Process tabs data
$processed_tabs = [];
if (!empty($tabs_data)) {
    foreach ($tabs_data as $tab) {
        $metadata = !empty($tab->metadata) ? json_decode($tab->metadata, true) : [];
        $processed_tabs[$tab->type][] = [
            'content' => $tab->content,
            'metadata' => $metadata,
            'created_at' => $tab->created_at
        ];
    }
}

$encounter->additional_tabs = $processed_tabs;
$encounterHTML = kcEncounterHtmlUpdated($encounter, $request_data['encounter_id'], 'encounter');
ob_start(); ?>
<html>

<head>
    <style>
        /** Define the margins of your page **/
        @page {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            margin-top: 32px;
            margin-bottom: 32px;
        }

        br {
            line-height: 5px;
        }


        main {
            margin-right: 80px;
            margin-left: 80px;
        }
    </style>
</head>

<body>

    <main>
        <?php echo  $encounterHTML; ?>
    </main>
</body>

</html>
<?php return ob_get_clean() ?>