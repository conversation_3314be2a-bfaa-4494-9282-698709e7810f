<!-- TemplateModal.vue -->
<template>
  <div>
    <div v-if="show"
      class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg max-w-lg w-full mx-4">
        <div class="p-4 border-b">
          <h3 class="text-lg font-medium">Select Template</h3>
        </div>

        <div class="p-4">
          <!-- Template Selection -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">Template Format</label>
            <div v-if="isLoading" class="flex items-center space-x-2 p-2">
              <svg class="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              <span>Loading templates...</span>
            </div>
            <select v-else v-model="selectedTemplate"
              class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500" :disabled="isGenerating">
              <option value="">Choose a template</option>

              <!-- User templates group -->
              <optgroup v-if="userTemplates.length > 0" label="My Templates">
                <option v-for="template in userTemplates" :key="'user_template_' + template.id"
                  :value="'user_template_' + template.id">
                  {{ template.name }} ({{ template.category_label }})
                </option>
              </optgroup>

              <!-- System templates group -->
              <optgroup label="System Templates">
                <option v-for="(template, key) in templates" :key="key" :value="key"
                  v-if="!key.startsWith('user_template_')">
                  {{ formatTemplateName(key) }}
                </option>
              </optgroup>
            </select>

            <div class="mt-2 text-right">
              <a href="#" class="text-blue-600 text-sm hover:text-blue-800" @click.prevent="openTemplateManager">
                Manage Templates
              </a>
            </div>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end gap-2">
            <button type="button" class="px-4 py-2 text-sm border rounded hover:bg-gray-50" @click="$emit('close')">
              Cancel
            </button>
            <button type="button"
              class="px-4 py-2 text-sm bg-black text-white rounded hover:bg-black disabled:opacity-50"
              :disabled="!selectedTemplate || isGenerating" @click="handleGenerate">
              <template v-if="isGenerating">
                <span class="inline-block animate-spin mr-2">↻</span>
                Generating...
              </template>
              <template v-else>Generate</template>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Modal -->
    <SummaryModal ref="refSummaryModal" v-if="showSummaryModal" :show="showSummaryModal" :summary="generatedSummary"
      :template="selectedTemplate" @close="showSummaryModal = false" @download="handleDownloadPdf" @next="handleSave" />
  </div>
</template>

<script>
import { post, get, siteBaseUrl } from "../../config/request";
import SummaryModal from "./SummaryModal";
import { displayMessage, displayErrorMessage } from "../../utils/message";

export default {
  name: "TemplateModal",

  components: {
    SummaryModal,
  },

  props: {
    encounterId: {
      type: [String, Number],
      required: true,
    },
    show: {
      type: Boolean,
      required: true,
    },
    consultationData: {
      type: Object,
      required: true,
    },
    activeFormTypes: {
      type: Array,
      required: true,
    },
    patientDetails: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      selectedTemplate: "",
      isGenerating: false,
      showSummaryModal: false,
      generatedSummary: "",
      templates: {},
      userTemplates: [],
      isLoading: true,
    };
  },

  mounted() {
    this.loadTemplates();
  },

  methods: {
    async loadTemplates() {
      this.isLoading = true;
      // First load default system templates
      try {
        // Use the centralized siteBaseUrl from our request module
        const templatesUrl = siteBaseUrl + '/wp-content/plugins/kivicare-clinic-management-system/resources/js/lib/templates.json';
        
        console.log('Loading templates from:', templatesUrl);
        const response = await fetch(templatesUrl);
        if (response.ok) {
          this.templates = await response.json();
          console.log('Default templates loaded successfully');
        } else {
          console.warn('Failed to load default templates, status:', response.status);
          // Initialize with empty object if fetch fails
          this.templates = {};
        }
      } catch (error) {
        console.error("Failed to load default templates:", error);
        // Initialize with empty object if error occurs
        this.templates = {};
      }

      // Then load user templates
      get('templates_list')
        .then(response => {
          console.log('Templates response:', response);
          if (response.data.status) {
            this.userTemplates = Array.isArray(response.data.data) ? response.data.data : [];

            // Log template details for debugging
            if (this.userTemplates.length > 0) {
              console.log('First template example:', this.userTemplates[0]);
            }

            // Convert user templates to the format expected by the template selector
            this.userTemplates.forEach(template => {
              const key = `user_template_${template.id}`;
              this.templates[key] = template.content;
              console.log(`Added template: ${template.name} with key ${key}`);
            });
          } else {
            console.warn('Template API returned non-success status:', response.data);
          }
        })
        .catch(error => {
          console.error('Error loading templates:', error);
          this.loadError = error.message || "Error loading templates";

          // Only add fallback templates if we don't already have system templates
          if (Object.keys(this.templates).length === 0) {
            // Fallback to default templates if loading fails
            this.templates = {
              consultation_notes_template: "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nNHS Number: [NHS/ID Number]\n...",
              referral_letter_template: "Receiving Specialist Information:\nName: [Specialist's Name]\nSpecialty: [e.g., Cardiologist]\n...",
              gp_letter_template: "Doctor Information:\nReferring Doctor: [Full Name]\nClinic: [Clinic Name]\n...",
              sick_medical_leave_note_template: "Doctor Information:\nName: [Doctor's Full Name]\nClinic: [Clinic Name]\n...",
              operation_notes_procedure_report_template: "Hospital Information:\nHospital Name: [Hospital Name]\n...",
              clinic_follow_up_ward_rounds_notes_template: "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\n...",
            };
          }
        })
        .finally(() => {
          this.isLoading = false;

          // Reset retry count after successful load
          if (!this.loadError) {
            this.retryCount = 0;
          }
        });
    },

    formatTemplateName(key) {
      // Check if it's a user template
      if (key.startsWith('user_template_')) {
        const templateId = key.replace('user_template_', '');
        const template = this.userTemplates.find(t => t.id == templateId);
        return template ? `${template.name} (${template.category_label})` : key;
      }

      // Default templates
      return key
        .replace(/_/g, " ")
        .replace("template", "")
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
        .trim();
    },

    getTemplatePreview(key) {
      const template = this.templates[key];
      const lines = template.split("\n").slice(0, 5);
      return lines.join("\n") + "\n...";
    },

    async handleGenerate() {
      if (!this.selectedTemplate) return;

      this.isGenerating = true;
      try {
        // Prepare comprehensive payload with all data needed for template variables
        let payload = {
          // Basic encounter data
          encounter_id: this.encounterId,
          encounterId: this.encounterId, // Add alternate name for compatibility
          appointment_id: this.consultationData.appointment_id || null,

          // Current date and time
          current_date: new Date().toLocaleDateString('en-GB'),
          current_time: new Date().toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }),

          // Consultation data from the forms (presenting concerns, history, examination, etc.)
          ...this.consultationData,

          // Template information
          selectedTemplate: this.selectedTemplate,
          route_name: "encounter_summarize",

          // Add all patient details for variable replacement
          patient_details: this.patientDetails,
        };

        // Add vital signs from activeFormTypes when available
        if (this.activeFormTypes && this.activeFormTypes.length > 0) {
          // Find vital signs data in active form types if it exists
          const vitalsForm = this.activeFormTypes.find(form => form.type === 'vitals');
          if (vitalsForm && vitalsForm.data) {
            payload.vitals = vitalsForm.data;
          }

          // Add all other form types data
          this.activeFormTypes.forEach(form => {
            if (form.type && form.data) {
              payload[form.type] = form.data;
            }
          });
        }

        console.log('Generating template with payload:', payload);

        // If it's a user template, we need to pass the content directly
        if (this.selectedTemplate.startsWith('user_template_')) {
          const templateId = this.selectedTemplate.replace('user_template_', '');
          const template = this.userTemplates.find(t => t.id == templateId);

          if (template) {
            payload.templateContent = template.content;
            payload.isUserTemplate = true;
            
            // Use the direct template processing endpoint for user templates
            let response = await post("process_template", payload);
            if (response?.data?.status && response?.data?.data) {
              this.generatedSummary = response.data.data.summary;
              this.showSummaryModal = true;
              return; // Exit the function since we've handled the template
            } else {
              throw new Error(response?.data?.message || "Failed to process template");
            }
          }
        }

        // Fall back to the original endpoint for system templates
        let response = await post("encounter_summarize", payload);

        if (response?.data?.summary) {
          this.generatedSummary = response.data.summary;
          this.showSummaryModal = true;
        }
      } catch (error) {
        displayErrorMessage("Failed to generate summary");
      } finally {
        this.isGenerating = false;
      }
    },

    async handleDownload() {
      try {
        const response = await post("download_summary", {
          template: this.selectedTemplate,
        });

        if (response?.data?.url) {
          window.open(response.data.url, "_blank");
        } else {
          throw new Error("Download failed");
        }
      } catch (error) {
        displayErrorMessage("Failed to download summary");
      }
    },

    // Add method to handle PDF download (fix the console error)
    handleDownloadPdf() {
      // This method is referenced in the template but not implemented
      // Adding it here to fix the console warning
      console.log("Download PDF functionality not implemented");
    },

    async handleSave() {
      // Access the editedSummary directly from the ref
      // let plainText = this.$refs.refSummaryModal.editedSummary;
      this.$refs.refSummaryModal.isLoading = true;

      try {
        const response = await post("save_generate_summery", {
          encounter_id: this.encounterId,
          plainText: this.$refs.refSummaryModal.editedSummary,
          templateName: this.formatTemplateName(this.selectedTemplate),
        });

        if (response?.data?.status) {
          displayMessage(response.data.message);
          this.showSummaryModal = false;
          this.$emit('close');
          this.$emit('select', response.data.document);
          this.$emit('reloadFiles', true);
        } else {
          throw new Error(response?.data?.message || "Failed to save summary");
        }
      } catch (error) {
        displayErrorMessage(error.message || "Failed to save summary");
      } finally {
        // Update loading state in child component
        if (this.$refs.summaryModal) {
          this.$refs.summaryModal.isLoading = false;
        }
      }
    },

    openTemplateManager() {
      // Close current modal
      this.$emit('close');

      // Navigate to template manager
      this.$router.push({ name: 'templates' });
    },
  },
};
</script>
