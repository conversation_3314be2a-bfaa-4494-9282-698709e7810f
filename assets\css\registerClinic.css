*,
::before,
::after {
    --tw-border-spacing-x:0;
    --tw-border-spacing-y:0;
    --tw-translate-x:0;
    --tw-translate-y:0;
    --tw-rotate:0;
    --tw-skew-x:0;
    --tw-skew-y:0;
    --tw-scale-x:1;
    --tw-scale-y:1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness:proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width:0px;
    --tw-ring-offset-color:#fff;
    --tw-ring-color:rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow:0 0 #0000;
    --tw-ring-shadow:0 0 #0000;
    --tw-shadow:0 0 #0000;
    --tw-shadow-colored:0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}
::backdrop {
    --tw-border-spacing-x:0;
    --tw-border-spacing-y:0;
    --tw-translate-x:0;
    --tw-translate-y:0;
    --tw-rotate:0;
    --tw-skew-x:0;
    --tw-skew-y:0;
    --tw-scale-x:1;
    --tw-scale-y:1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness:proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width:0px;
    --tw-ring-offset-color:#fff;
    --tw-ring-color:rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow:0 0 #0000;
    --tw-ring-shadow:0 0 #0000;
    --tw-shadow:0 0 #0000;
    --tw-shadow-colored:0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}
/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,
                                                                   ::after,
                                                                   ::before {
                                                                       box-sizing:border-box;
                                                                       border-width:0;
                                                                       border-style:solid;
                                                                       border-color:#e5e7eb
                                                                   }
::after,
::before {
    --tw-content:''
}
:host,
html {
    line-height:1.5;
    -webkit-text-size-adjust:100%;
    -moz-tab-size:4;
    tab-size:4;
    font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-feature-settings:normal;
    font-variation-settings:normal;
    -webkit-tap-highlight-color:transparent
}
body {
    margin:0;
    line-height:inherit
}
hr {
    height:0;
    color:inherit;
    border-top-width:1px
}
abbr:where([title]) {
    -webkit-text-decoration:underline dotted;
    text-decoration:underline dotted
}
h1,
h2,
h3,
h4,
h5,
h6 {
    font-size:inherit;
    font-weight:inherit
}
a {
    color:inherit;
    text-decoration:inherit
}
b,
strong {
    font-weight:bolder
}
code,
kbd,
pre,
samp {
    font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-feature-settings:normal;
    font-variation-settings:normal;
    font-size:1em
}
small {
    font-size:80%
}
sub,
sup {
    font-size:75%;
    line-height:0;
    position:relative;
    vertical-align:baseline
}
sub {
    bottom:-.25em
}
sup {
    top:-.5em
}
table {
    text-indent:0;
    border-color:inherit;
    border-collapse:collapse
}
button,
input,
optgroup,
select,
textarea {
    font-family:inherit;
    font-feature-settings:inherit;
    font-variation-settings:inherit;
    font-size:100%;
    font-weight:inherit;
    line-height:inherit;
    letter-spacing:inherit;
    color:inherit;
    margin:0;
    padding:0
}
button,
select {
    text-transform:none
}
button,
input:where([type=button]),
input:where([type=reset]),
input:where([type=submit]) {
    -webkit-appearance:button;
    background-color:transparent;
    background-image:none
}
:-moz-focusring {
    outline:auto
}
:-moz-ui-invalid {
    box-shadow:none
}
progress {
    vertical-align:baseline
}
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height:auto
}
[type=search] {
    -webkit-appearance:textfield;
    outline-offset:-2px
}
::-webkit-search-decoration {
    -webkit-appearance:none
}
::-webkit-file-upload-button {
    -webkit-appearance:button;
    font:inherit
}
summary {
    display:list-item
}
blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
    margin:0
}
fieldset {
    margin:0;
    padding:0
}
legend {
    padding:0
}
menu,
ol,
ul {
    list-style:none;
    margin:0;
    padding:0
}
dialog {
    padding:0
}
textarea {
    resize:vertical
}
input::placeholder,
textarea::placeholder {
    opacity:1;
    color:#9ca3af
}
[role=button],
button {
    cursor:pointer
}
:disabled {
    cursor:default
}
audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
    display:block;
    vertical-align:middle
}
img,
video {
    max-width:100%;
    height:auto
}
[hidden]:where(:not([hidden=until-found])) {
    display:none
}
.absolute {
    position:absolute
}
.relative {
    position:relative
}
.right-3 {
    right:0.75rem
}
.top-1\/2 {
    top:50%
}
.mx-auto {
    margin-left:auto;
    margin-right:auto
}
.mb-1 {
    margin-bottom:0.25rem
}
.mb-4 {
    margin-bottom:1rem
}
.mb-6 {
    margin-bottom:1.5rem
}
.mb-8 {
    margin-bottom:2rem
}
.ml-1 {
    margin-left:0.25rem
}
.mt-1 {
    margin-top:0.25rem
}
.mt-6 {
    margin-top:1.5rem
}
.mt-8 {
    margin-top:2rem
}
.block {
    display:block
}
.flex {
    display:flex
}
.inline-flex {
    display:inline-flex
}
.grid {
    display:grid
}
.h-3 {
    height:0.75rem
}
.h-4 {
    height:1rem
}
.h-5 {
    height:1.25rem
}
.h-8 {
    height:2rem
}
.h-full {
    height:100%
}
.min-h-full {
    min-height:100%
}
.min-h-screen {
    min-height:100vh
}
.w-3 {
    width:0.75rem
}
.w-4 {
    width:1rem
}
.w-5 {
    width:1.25rem
}
.w-8 {
    width:2rem
}
.w-full {
    width:100%
}
.max-w-6xl {
    max-width:72rem
}
.max-w-xl {
    max-width:36rem
}
.-translate-y-1\/2 {
    --tw-translate-y:-50%;
    transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}
.grid-cols-1 {
    grid-template-columns:repeat(1, minmax(0, 1fr))
}
.items-start {
    align-items:flex-start
}
.items-center {
    align-items:center
}
.items-baseline {
    align-items:baseline
}
.justify-center {
    justify-content:center
}
.justify-between {
    justify-content:space-between
}
.gap-2 {
    gap:0.5rem
}
.gap-3 {
    gap:0.75rem
}
.gap-4 {
    gap:1rem
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse:0;
    margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom:calc(1rem * var(--tw-space-y-reverse))
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse:0;
    margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))
}
.rounded-2xl {
    border-radius:1rem
}
.rounded-full {
    border-radius:9999px
}
.rounded-lg {
    border-radius:0.5rem
}
.rounded-xl {
    border-radius:0.75rem
}
.border {
    border-width:1px
}
.border-t {
    border-top-width:1px
}
.border-gray-200 {
    --tw-border-opacity:1;
    border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))
}
.border-gray-300 {
    --tw-border-opacity:1;
    border-color:rgb(209 213 219 / var(--tw-border-opacity, 1))
}
.bg-blue-100 {
    --tw-bg-opacity:1;
    background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))
}
.bg-gray-200 {
    --tw-bg-opacity:1;
    background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))
}
.bg-purple-100 {
    --tw-bg-opacity:1;
    background-color:rgb(243 232 255 / var(--tw-bg-opacity, 1))
}
.bg-purple-600 {
    --tw-bg-opacity:1;
    background-color:rgb(147 51 234 / var(--tw-bg-opacity, 1))
}
.bg-white {
    --tw-bg-opacity:1;
    background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))
}
.bg-gradient-to-br {
    background-image:linear-gradient(to bottom right, var(--tw-gradient-stops))
}
.bg-gradient-to-r {
    background-image:linear-gradient(to right, var(--tw-gradient-stops))
}
.from-purple-50 {
    --tw-gradient-from:#faf5ff var(--tw-gradient-from-position);
    --tw-gradient-to:rgb(250 245 255 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)
}
.from-purple-600 {
    --tw-gradient-from:#9333ea var(--tw-gradient-from-position);
    --tw-gradient-to:rgb(147 51 234 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)
}
.via-white {
    --tw-gradient-to:rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
    --tw-gradient-stops:var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to)
}
.to-blue-50 {
    --tw-gradient-to:#eff6ff var(--tw-gradient-to-position)
}
.to-blue-600 {
    --tw-gradient-to:#2563eb var(--tw-gradient-to-position)
}
.bg-clip-text {
    -webkit-background-clip:text;
    background-clip:text
}
.p-2 {
    padding:0.5rem
}
.p-6 {
    padding:1.5rem
}
.p-8 {
    padding:2rem
}
.px-3 {
    padding-left:0.75rem;
    padding-right:0.75rem
}
.px-4 {
    padding-left:1rem;
    padding-right:1rem
}
.py-1 {
    padding-top:0.25rem;
    padding-bottom:0.25rem
}
.py-2 {
    padding-top:0.5rem;
    padding-bottom:0.5rem
}
.py-3 {
    padding-top:0.75rem;
    padding-bottom:0.75rem
}
.py-8 {
    padding-top:2rem;
    padding-bottom:2rem
}
.pt-6 {
    padding-top:1.5rem
}
.text-center {
    text-align:center
}
.text-2xl {
    font-size:1.5rem;
    line-height:2rem
}
.text-sm {
    font-size:0.875rem;
    line-height:1.25rem
}
.font-bold {
    font-weight:700
}
.font-medium {
    font-weight:500
}
.font-semibold {
    font-weight:600
}
.text-blue-600 {
    --tw-text-opacity:1;
    color:rgb(37 99 235 / var(--tw-text-opacity, 1))
}
.text-gray-500 {
    --tw-text-opacity:1;
    color:rgb(107 114 128 / var(--tw-text-opacity, 1))
}
.text-gray-600 {
    --tw-text-opacity:1;
    color:rgb(75 85 99 / var(--tw-text-opacity, 1))
}
.text-gray-900 {
    --tw-text-opacity:1;
    color:rgb(17 24 39 / var(--tw-text-opacity, 1))
}
.text-green-500 {
    --tw-text-opacity:1;
    color:rgb(34 197 94 / var(--tw-text-opacity, 1))
}
.text-purple-600 {
    --tw-text-opacity:1;
    color:rgb(147 51 234 / var(--tw-text-opacity, 1))
}
.text-purple-800 {
    --tw-text-opacity:1;
    color:rgb(107 33 168 / var(--tw-text-opacity, 1))
}
.text-red-500 {
    --tw-text-opacity:1;
    color:rgb(239 68 68 / var(--tw-text-opacity, 1))
}
.text-transparent {
    color:transparent
}
.text-white {
    --tw-text-opacity:1;
    color:rgb(255 255 255 / var(--tw-text-opacity, 1))
}
.shadow-xl {
    --tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}
.transition-all {
    transition-property:all;
    transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration:150ms
}
.transition-shadow {
    transition-property:box-shadow;
    transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration:150ms
}
.hover\:from-purple-700:hover {
    --tw-gradient-from:#7e22ce var(--tw-gradient-from-position);
    --tw-gradient-to:rgb(126 34 206 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)
}
.hover\:to-blue-700:hover {
    --tw-gradient-to:#1d4ed8 var(--tw-gradient-to-position)
}
.hover\:text-gray-700:hover {
    --tw-text-opacity:1;
    color:rgb(55 65 81 / var(--tw-text-opacity, 1))
}
.hover\:text-purple-700:hover {
    --tw-text-opacity:1;
    color:rgb(126 34 206 / var(--tw-text-opacity, 1))
}
.focus\:border-transparent:focus {
    border-color:transparent
}
.focus\:ring-2:focus {
    --tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}
.focus\:ring-purple-500:focus {
    --tw-ring-opacity:1;
    --tw-ring-color:rgb(168 85 247 / var(--tw-ring-opacity, 1))
}
@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns:repeat(2, minmax(0, 1fr))
    }
}
