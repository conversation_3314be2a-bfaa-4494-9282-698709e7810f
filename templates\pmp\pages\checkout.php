<?php

/**
 * Template: Checkout
 * Version: 3.2
 *
 * See documentation for how to override the PMPro templates.
 * @link https://www.paidmembershipspro.com/documentation/templates/
 *
 * @version 3.2
 *
 * <AUTHOR> Memberships Pro
 */

use App\models\KCClinic;

global $gateway, $pmpro_review, $skip_account_fields, $pmpro_paypal_token, $wpdb, $current_user, $pmpro_msg, $pmpro_msgt, $pmpro_requirebilling, $pmpro_level, $pmpro_show_discount_code, $pmpro_error_fields, $pmpro_default_country;
global $discount_code, $username, $password, $password2, $bfirstname, $blastname, $baddress1, $baddress2, $bcity, $bstate, $bzipcode, $bcountry, $bphone, $bemail, $bconfirmemail, $CardType, $AccountNumber, $ExpirationMonth, $ExpirationYear;

$pmpro_levels = pmpro_getAllLevels();

/**
 * Filter to set if PMPro uses email or text as the type for email field inputs.
 *
 * @since *******
 *
 * @param bool $use_email_type, true to use email type, false to use text type
 */
$pmpro_email_field_type = apply_filters('pmpro_email_field_type', true);

// Set the wrapping class for the checkout div based on the default gateway;
$default_gateway = get_option('pmpro_gateway');
if (empty($default_gateway)) {
	$pmpro_checkout_gateway_class = 'pmpro_section pmpro_checkout_gateway-none';
} else {
	$pmpro_checkout_gateway_class = 'pmpro_section pmpro_checkout_gateway-' . $default_gateway;
}
?>
<style>
    /* Fix scrolling issues and ensure responsiveness */
    body, html {
        height: 100%;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }
    
    body {
        overflow-y: auto !important;
        position: relative !important;
        padding-top: 0 !important;
        
    }
    
    /* Force the page to start at the top */
    #page, .site, #content, .site-content {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
    
    .pmpro {
        padding-top: 20px;
        overflow-y: auto;
        margin-top: 0;
    }
    
    /* Header and form container adjustments */
    .max-w-xl.mx-auto.mb-8 {
        margin-top: 100px !important; /* Use margin instead of padding */
        position: relative;
        margin-bottom: 20px !important;
        display: block !important;
        visibility: visible !important;
    }
    
    /* Ensure the top of the document starts with enough space */
    .pmpro {
        margin-top: 20px !important;
        padding-top: 40px !important;
    }
    
    .max-w-xl.mx-auto.p-6 {
        margin-top: 20px;
    }
    
    /* Fix form position */
    #pmpro_form {
        position: relative;
        margin-top: 0;
    }
    
    /* Fix logo display */
    .flex.items-center.gap-2 img {
        max-width: 100%;
        height: auto;
    }
    
    /* Ensure step indicator is visible */
    .flex.items-center.gap-4 {
        white-space: nowrap;
    }
    
    /* Make the page responsive */
    @media (max-width: 768px) {
        body {
            padding-top: 10px !important;
        }
        
        .max-w-xl {
            width: 100% !important;
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
            max-width: 100% !important;
        }
        
        .max-w-xl.mx-auto.mb-8 {
            padding-top: 40px;
        }
        
        .grid-cols-2 {
            grid-template-columns: 1fr !important;
        }
        
        .pmpro_form_fields.pmpro_cols-2 {
            display: block !important;
        }
        
        .pmpro_form_field {
            margin-bottom: 15px;
        }
        
        /* Adjust logo and step counter for mobile */
        .flex.items-center.justify-between.mb-8 {
            flex-direction: column;
            gap: 15px;
        }
        
        .flex.items-center.justify-between.mb-8 > div {
            margin-bottom: 10px;
        }
        
        /* Adjust form width for mobile */
        .max-w-xl.mx-auto.p-6 {
            padding: 15px;
        }
        
        /* Adjust the checkout button for mobile */
        #pmpro_submit_span input[type="submit"] {
            width: 100%;
            margin: 10px 0;
        }
    }
</style>

<?php
	$currency_detail = kcGetClinicCurrenyPrefixAndPostfix();
	$currency_prefix = !empty($currency_detail['prefix']) ? $currency_detail['prefix'] : '' ;
?>

<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro')); ?>">
	<?php do_action('pmpro_checkout_before_form'); ?>

	<section id="pmpro_level-<?php echo intval($pmpro_level->id); ?>" class="<?php echo esc_attr(pmpro_get_element_class($pmpro_checkout_gateway_class, 'pmpro_level-' . $pmpro_level->id)); ?>">

		<form id="pmpro_form" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form')); ?>" action="<?php if (!empty($_REQUEST['review'])) echo esc_url(pmpro_url("checkout", "?pmpro_level=" . $pmpro_level->id)); ?>" method="post">

			<input type="hidden" id="pmpro_level" name="pmpro_level" value="<?php echo esc_attr($pmpro_level->id) ?>" />
			<input type="hidden" id="checkjavascript" name="checkjavascript" value="1" />
			<?php if ($discount_code && $pmpro_review) { ?>
				<input class="<?php echo esc_attr(pmpro_get_element_class('pmpro_alter_price', 'pmpro_discount_code')); ?>" id="pmpro_discount_code" name="pmpro_discount_code" type="hidden" value="<?php echo esc_attr($discount_code) ?>" />
			<?php } ?>

			<?php if ($pmpro_msg) { ?>
				<div role="alert" id="pmpro_message" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_message ' . $pmpro_msgt, $pmpro_msgt)); ?>">
					<?php echo wp_kses_post(apply_filters('pmpro_checkout_message', $pmpro_msg, $pmpro_msgt)); ?>
				</div>
			<?php } else { ?>
				<div id="pmpro_message" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_message')); ?>" style="display: none;"></div>
			<?php } ?>

			<?php if ($pmpro_review) { ?>
				<p><?php echo wp_kses(__('Almost done. Review the membership information and pricing below then <strong>click the "Complete Payment" button</strong> to finish your order.', 'paid-memberships-pro'), array('strong' => array())); ?></p>
			<?php } ?>



			<?php do_action('pmpro_checkout_after_pricing_fields'); ?>

			<?php
			// Define whether we should show the Account Information box.
			$show_pmpro_user_fields_fieldset = true;

			// If $pmpro_review is set, skip.
			if ($pmpro_review) {
				$show_pmpro_user_fields_fieldset = false;
			}

			// If we are skipping the account fields and the user is logged out, skip the entire fieldset.
			// The logged out check is important since if the user is logged in, we will show a logged in message.
			if ($skip_account_fields && ! $current_user->ID) {
				$show_pmpro_user_fields_fieldset = false;
			}


			do_action('pmpro_checkout_after_user_fields'); ?>

			<?php do_action('pmpro_checkout_boxes'); ?>

			<?php
			$pmpro_include_billing_address_fields = apply_filters('pmpro_include_billing_address_fields', true);
			if ($pmpro_include_billing_address_fields) { ?>
				<fieldset id="pmpro_billing_address_fields" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_fieldset', 'pmpro_billing_address_fields')); ?>" <?php if (! $pmpro_requirebilling || apply_filters("pmpro_hide_billing_address_fields", false)) { ?>style="display: none;" <?php } ?>>
					<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_card')); ?>">
						<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_card_content')); ?>">
							<legend class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_legend')); ?>">
								<h2 class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_heading pmpro_font-large')); ?>"><?php esc_html_e('Billing Address', 'paid-memberships-pro'); ?></h2>
							</legend>
							<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_fields pmpro_cols-2')); ?>">
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-bfirstname', 'pmpro_form_field-bfirstname')); ?>">
									<label for="bfirstname" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('First Name', 'paid-memberships-pro'); ?></label>
									<input id="bfirstname" name="bfirstname" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'bfirstname')); ?>" value="<?php echo esc_attr($bfirstname); ?>" autocomplete="given-name" />
								</div> <!-- end pmpro_form_field-bfirstname -->
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-blastname', 'pmpro_form_field-blastname')); ?>">
									<label for="blastname" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Last Name', 'paid-memberships-pro'); ?></label>
									<input id="blastname" name="blastname" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'blastname')); ?>" value="<?php echo esc_attr($blastname); ?>" autocomplete="family-name" />
								</div> <!-- end pmpro_form_field-blastname -->
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-baddress1', 'pmpro_form_field-baddress1')); ?>">
									<label for="baddress1" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Address 1', 'paid-memberships-pro'); ?></label>
									<input id="baddress1" name="baddress1" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'baddress1')); ?>" value="<?php echo esc_attr($baddress1); ?>" autocomplete="billing street-address" />
								</div> <!-- end pmpro_form_field-baddress1 -->
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-baddress2', 'pmpro_form_field-baddress2')); ?>">
									<label for="baddress2" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Address 2', 'paid-memberships-pro'); ?></label>
									<input id="baddress2" name="baddress2" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'baddress2')); ?>" value="<?php echo esc_attr($baddress2); ?>" />
								</div> <!-- end pmpro_form_field-baddress2 -->
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-bcity', 'pmpro_form_field-bcity')); ?>">
									<label for="bcity" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('City', 'paid-memberships-pro'); ?></label>
									<input id="bcity" name="bcity" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'bcity')); ?>" value="<?php echo esc_attr($bcity); ?>" />
								</div> <!-- end pmpro_form_field-bcity -->
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-bstate', 'pmpro_form_field-bstate')); ?>">
									<label for="bstate" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('State', 'paid-memberships-pro'); ?></label>
									<input id="bstate" name="bstate" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'bstate')); ?>" value="<?php echo esc_attr($bstate); ?>" />
								</div> <!-- end pmpro_form_field-bstate -->
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-bzipcode', 'pmpro_form_field-bzipcode')); ?>">
									<label for="bzipcode" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Postal Code', 'paid-memberships-pro'); ?></label>
									<input id="bzipcode" name="bzipcode" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'bzipcode')); ?>" value="<?php echo esc_attr($bzipcode); ?>" autocomplete="billing postal-code" />
								</div> <!-- end pmpro_form_field-bzipcode -->
								<?php
								$show_country = apply_filters("pmpro_international_addresses", true);
								if ($show_country) { ?>
									<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-select pmpro_form_field-bcountry', 'pmpro_form_field-bcountry')); ?>">
										<label for="bcountry" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Country', 'paid-memberships-pro'); ?></label>
										<select name="bcountry" id="bcountry" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-select', 'bcountry')); ?>" autocomplete="billing country">
											<?php
											global $pmpro_countries, $pmpro_default_country;
											if (!$bcountry) {
												$bcountry = $pmpro_default_country;
											}
											foreach ($pmpro_countries as $abbr => $country) { ?>
												<option value="<?php echo esc_attr($abbr) ?>" <?php if ($abbr == $bcountry) { ?>selected="selected" <?php } ?>><?php echo esc_html($country) ?></option>
											<?php } ?>
										</select>
									</div> <!-- end pmpro_form_field-bcountry -->
								<?php } else { ?>
									<input type="hidden" name="bcountry" id="bcountry" value="<?php echo esc_attr($pmpro_default_country); ?>" />
								<?php } ?>
								<?php if ($skip_account_fields) { ?>
									<?php
									if ($current_user->ID) {
										if (!$bemail && $current_user->user_email) {
											$bemail = $current_user->user_email;
										}
										if (!$bconfirmemail && $current_user->user_email) {
											$bconfirmemail = $current_user->user_email;
										}
									}
									?>
									<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-email pmpro_form_field-bemail', 'pmpro_form_field-bemail')); ?>">
										<label for="bemail" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Email Address', 'paid-memberships-pro'); ?></label>
										<input id="bemail" name="bemail" type="<?php echo ($pmpro_email_field_type ? 'email' : 'text'); ?>" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-email', 'bemail')); ?>" value="<?php echo esc_attr($bemail); ?>" autocomplete="email" />
									</div> <!-- end pmpro_form_field-bemail -->
									<?php
									$pmpro_checkout_confirm_email = apply_filters("pmpro_checkout_confirm_email", true);
									if ($pmpro_checkout_confirm_email) { ?>
										<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-email pmpro_form_field-bconfirmemail', 'pmpro_form_field-bconfirmemail')); ?>">
											<label for="bconfirmemail" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Confirm Email', 'paid-memberships-pro'); ?></label>
											<input id="bconfirmemail" name="bconfirmemail" type="<?php echo ($pmpro_email_field_type ? 'email' : 'text'); ?>" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-email', 'bconfirmemail')); ?>" value="<?php echo esc_attr($bconfirmemail); ?>" autocomplete="email" />
										</div> <!-- end pmpro_form_field-bconfirmemail -->
									<?php } else { ?>
										<input type="hidden" name="bconfirmemail_copy" value="1" />
									<?php } ?>
								<?php } ?>
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_form_field-bphone', 'pmpro_form_field-bphone')); ?>">
									<label for="bphone" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Phone', 'paid-memberships-pro'); ?></label>
									<input id="bphone" name="bphone" type="text" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'bphone')); ?>" value="<?php echo esc_attr(formatPhone($bphone)); ?>" autocomplete="tel" />
								</div> <!-- end pmpro_form_field-bphone -->
							</div> <!-- end pmpro_form_fields -->
						</div> <!-- end pmpro_card_content -->
					</div> <!-- end pmpro_card -->
				</fieldset> <!-- end pmpro_billing_address_fields -->
			<?php } ?>

			<?php do_action('pmpro_checkout_after_billing_fields'); ?>
			<div class="max-w-xl mx-auto mb-8">
				<div class="flex items-center justify-between mb-8">
					<div class="flex items-center gap-2">
						<img src="<?php echo esc_url(!empty($config_options[KIVI_CARE_PREFIX . 'site_logo']) ? wp_get_attachment_url($config_options[KIVI_CARE_PREFIX . 'site_logo']) : esc_js(KIVI_CARE_DIR_URI.'/assets/images/logo-banner.png') ?? "#") ?>" alt="logo" class="w-48">
					</div>
					<div class="flex items-center gap-4">
						<div class="flex space-x-1">
							<div class="w-2 h-2 rounded-full bg-purple-600"></div>
							<div class="w-2 h-2 rounded-full bg-purple-600"></div>
							<div class="w-2 h-2 rounded-full bg-purple-600"></div>
						</div>
						<span class="text-sm text-gray-600">Step <span class="current-active-step">3</span> of 3</span>
					</div>
				</div>
			</div>
			<div class="max-w-xl mx-auto p-6 bg-white rounded-2xl shadow-xl space-y-6">

				<div class="mb-8">
					<h1 class="text-2xl font-semibold mb-2">Payment Summary</h1>

				</div>

				<div class="bg-gray-50 p-4 rounded-lg">
					<h2 class="font-medium mb-4">Order Summary</h2>
					<div class="space-y-3">
					<?php 
					  $level = pmpro_getLevel($_GET['pmpro_level']??-1);
						if ($level) {
							// Plan name and price
							$plan_name = $level->name;
							$plan_price = pmpro_getLevelCost($level); // Fetch formatted plan price


							
							$KCClinic = (new KCClinic)->get_by(['clinic_admin_id' => (int)get_current_user_id()], '=', true);
				
							
							// Additional fields or data (customize based on your setup)
							$base_plan_description = $plan_name." - 1 Doctor included"; // Custom text
							$additional_doctors =  $KCClinic ->allow_no_of_doc; // Example: fetch dynamic value
							$additional_doctors_price = get_option(KIVI_CARE_PREFIX . 'subscription_settings')['doctorPrice'] ?? 10;
							$total_price = $level->billing_amount + ($additional_doctors * $additional_doctors_price);
							ob_start();
							?>
							<div class="flex justify-between">
								<span><?php echo esc_html($plan_name); ?></span>
								<span>
									<?php 
									$sale_price = get_pmpro_membership_level_meta($level->id, 'sale_price', true);
									if (!empty($sale_price) && is_numeric($sale_price) && $sale_price < $level->billing_amount) {
										echo pmpro_formatPrice($sale_price);
										echo ' <span class="text-sm text-gray-500 line-through">' . pmpro_formatPrice($level->billing_amount) . '</span>';
									} else {
										echo pmpro_formatPrice($level->billing_amount);
									}
									?>
								</span>
							</div>
							<div class="flex justify-between text-sm text-gray-600">
								<span><?php echo esc_html($base_plan_description); ?></span>
								<span><?php echo pmpro_formatPrice(0) ?></span>
							</div>
							<div class="flex justify-between">
								<span>Additional Doctors (<?php echo esc_html($additional_doctors); ?>)</span>
								<span><?= $currency_prefix ?><?php echo esc_html($additional_doctors * $additional_doctors_price); ?></span>
							</div>
							<div class="border-t pt-3 mt-3">
								<div class="flex justify-between font-medium">
									<span>Total Monthly Payment</span>
									<span>
										<?php 
										// Recalculate total price with sale price if available
										if (!empty($sale_price) && is_numeric($sale_price) && $sale_price < $level->billing_amount) {
											$adjusted_total = $sale_price + ($additional_doctors * $additional_doctors_price);
											echo esc_html(pmpro_formatPrice($adjusted_total));
										} else {
											echo esc_html(pmpro_formatPrice($total_price));
										}
										?>
									</span>
									</div>
									<div class="text-sm text-purple-600 mt-2 text-left">
										7-day free trial included. <strong>Pay <?= $currency_prefix ?>0 today</strong>, <?php echo esc_html(pmpro_formatPrice($total_price)); ?> will be charged after trial ends.
									</div>
								</div>
							<?php
							echo $order_summery_html= ob_get_clean ();
						} else {
							echo '<p>Subscription plan not found.</p>';
						}
						?>
					</div>
				</div>

				<?php if ($pmpro_show_discount_code) { ?>
					<button type="button" onclick="jQuery(this).hide(); jQuery('#coupon-field-container').show(); jQuery('#pmpro_discount_code').focus();" id="show-coupon-btn" class="flex items-center text-purple-600 text-sm">
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag w-4 h-4 mr-2">
							<path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path>
							<circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
						</svg>
						<?php echo esc_html("Add coupon code", 'kc-lang') ?>
					</button>
					<div id="coupon-field-container" style="display: none;" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_payment-discount-code', 'pmpro_payment-discount-code')); ?>">
						<label for="pmpro_discount_code" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Discount Code', 'paid-memberships-pro'); ?></label>
						<div class=" flex space-x-2 <?php echo esc_attr(pmpro_get_element_class('pmpro_form_fields-inline')); ?>">
							<input id="pmpro_discount_code" name="pmpro_discount_code" type="text" value="<?php echo esc_attr($discount_code) ?>" class=" flex-1 p-2 border rounded-lg <?php echo esc_attr(pmpro_get_element_class('pmpro_form_input-text pmpro_alter_price', 'pmpro_discount_code')); ?>" />
							<button aria-label="<?php esc_html_e('Apply discount code', 'paid-memberships-pro'); ?>" type="button" id="discount_code_button" name="discount_code_button" value="<?php esc_attr_e('Apply', 'paid-memberships-pro'); ?>" class=" px-4 py-2 bg-purple-600 text-white rounded-lg <?php echo esc_attr(pmpro_get_element_class('pmpro_btn-submit-discount-code', 'discount_code_button')); ?>">
								<?php esc_attr_e('Apply', 'paid-memberships-pro'); ?>
							</button>
						</div> <!-- end pmpro_form_fields-inline -->
						<p id="discount_code_message" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_message')); ?>" style="display: none;"></p>
					</div>
				<?php } ?>

				<?php
				/**
				 * Filter to set if the payment information fields should be shown.
				 *
				 * @param bool $include_payment_information_fields
				 * @return bool
				 */
				$pmpro_include_payment_information_fields = apply_filters('pmpro_include_payment_information_fields', true);
				if ($pmpro_include_payment_information_fields) {
				?>
					<fieldset id="pmpro_payment_information_fields" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_fieldset', 'pmpro_payment_information_fields')); ?>" <?php if (! $pmpro_requirebilling || apply_filters('pmpro_hide_payment_information_fields', false)) { ?>style="display: none;" <?php } ?>>
						<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_card')); ?>">
							<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_card_content')); ?>">
								<legend class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_legend')); ?>">
									<h2 class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_heading pmpro_font-large')); ?>"><?php esc_html_e('Payment Information', 'paid-memberships-pro'); ?></h2>
								</legend>
								<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_fields')); ?>">
									<input type="hidden" id="CardType" name="CardType" value="<?php echo esc_attr($CardType); ?>" />
									<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_payment-account-number', 'pmpro_payment-account-number')); ?>">
										<label for="AccountNumber" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Card Number', 'paid-memberships-pro'); ?></label>
										<input id="AccountNumber" name="AccountNumber" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'AccountNumber')); ?>" type="text" value="<?php echo esc_attr($AccountNumber); ?>" data-encrypted-name="number" autocomplete="off" />
									</div>
									<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_cols-2')); ?>">
										<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-select pmpro_payment-expiration', 'pmpro_payment-expiration')); ?>">
											<label for="ExpirationMonth" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Expiration Date', 'paid-memberships-pro'); ?></label>
											<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_fields-inline')); ?>">
												<select id="ExpirationMonth" name="ExpirationMonth" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-select', 'ExpirationMonth')); ?>">
													<option value="01" <?php if ($ExpirationMonth == "01") { ?>selected="selected" <?php } ?>>01</option>
													<option value="02" <?php if ($ExpirationMonth == "02") { ?>selected="selected" <?php } ?>>02</option>
													<option value="03" <?php if ($ExpirationMonth == "03") { ?>selected="selected" <?php } ?>>03</option>
													<option value="04" <?php if ($ExpirationMonth == "04") { ?>selected="selected" <?php } ?>>04</option>
													<option value="05" <?php if ($ExpirationMonth == "05") { ?>selected="selected" <?php } ?>>05</option>
													<option value="06" <?php if ($ExpirationMonth == "06") { ?>selected="selected" <?php } ?>>06</option>
													<option value="07" <?php if ($ExpirationMonth == "07") { ?>selected="selected" <?php } ?>>07</option>
													<option value="08" <?php if ($ExpirationMonth == "08") { ?>selected="selected" <?php } ?>>08</option>
													<option value="09" <?php if ($ExpirationMonth == "09") { ?>selected="selected" <?php } ?>>09</option>
													<option value="10" <?php if ($ExpirationMonth == "10") { ?>selected="selected" <?php } ?>>10</option>
													<option value="11" <?php if ($ExpirationMonth == "11") { ?>selected="selected" <?php } ?>>11</option>
													<option value="12" <?php if ($ExpirationMonth == "12") { ?>selected="selected" <?php } ?>>12</option>
												</select>/<select id="ExpirationYear" name="ExpirationYear" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-select', 'ExpirationYear')); ?>">
													<?php
													$num_years = apply_filters('pmpro_num_expiration_years', 10);

													for ($i = date_i18n('Y'); $i < intval(date_i18n('Y')) + intval($num_years); $i++) {
													?>
														<option value="<?php echo esc_attr($i) ?>" <?php if ($ExpirationYear == $i) { ?>selected="selected" <?php } elseif ($i == date_i18n('Y') + 1) { ?>selected="selected" <?php } ?>><?php echo esc_html($i) ?></option>
													<?php
													}
													?>
												</select>
											</div> <!-- end pmpro_form_fields-inline -->
										</div>
										<?php
										$pmpro_show_cvv = apply_filters("pmpro_show_cvv", true);
										if ($pmpro_show_cvv) { ?>
											<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_form_field-text pmpro_payment-cvv', 'pmpro_payment-cvv')); ?>">
												<label for="CVV" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Security Code (CVC)', 'paid-memberships-pro'); ?></label>
												<input id="CVV" name="CVV" type="text" size="4" value="<?php if (!empty($_REQUEST['CVV'])) {
																											echo esc_attr(sanitize_text_field($_REQUEST['CVV']));
																										} ?>" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_input pmpro_form_input-text', 'CVV')); ?>" />
											</div>
										<?php } ?>
									</div> <!-- end pmpro_cols-2 -->

								</div> <!-- end pmpro_form_fields -->
							</div> <!-- end pmpro_card_content -->
						</div> <!-- end pmpro_card -->
					</fieldset> <!-- end pmpro_payment_information_fields -->
				<?php
				}
				?>

				<?php
				do_action('pmpro_checkout_after_payment_information_fields');
				do_action('pmpro_checkout_before_submit_button');

				// Add nonce.
				wp_nonce_field('pmpro_checkout_nonce', 'pmpro_checkout_nonce');
				?>

				<?php if ($pmpro_msg) { ?>
					<div id="pmpro_message_bottom" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_message ' . $pmpro_msgt, $pmpro_msgt)); ?>"><?php echo wp_kses_post(apply_filters('pmpro_checkout_message', $pmpro_msg, $pmpro_msgt)); ?></div>
				<?php } else { ?>
					<div id="pmpro_message_bottom" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_message')); ?>" style="display: none;"></div>
				<?php } ?>

				

		</form> <!-- end pmpro_form -->

		<?php do_action('pmpro_checkout_after_form'); ?>
		<div class="grid grid-cols-2 gap-4 py-4">
			<div class="flex items-start space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-5 h-5 text-purple-600 mt-1">
					<path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
				</svg>
				<div class="text-sm">
					<p class="font-medium">Secure Payment</p>
					<p class="text-gray-600">256-bit SSL encryption</p>
				</div>
			</div>
			<div class="flex items-start space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-5 h-5 text-purple-600 mt-1">
					<circle cx="12" cy="12" r="10"></circle>
					<polyline points="12 6 12 12 16 14"></polyline>
				</svg>
				<div class="text-sm">
					<p class="font-medium">Instant Access</p>
					<p class="text-gray-600">Set up in minutes</p>
				</div>
			</div>
		</div>
		<div class="bg-purple-50 p-4 rounded-lg space-y-3">
			<div class="flex items-center space-x-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check w-4 h-4 text-purple-600">
					<circle cx="12" cy="12" r="10"></circle>
					<path d="m9 12 2 2 4-4"></path>
				</svg><span>Refer and earn</span></div>
			<div class="flex items-center space-x-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check w-4 h-4 text-purple-600">
					<circle cx="12" cy="12" r="10"></circle>
					<path d="m9 12 2 2 4-4"></path>
				</svg><span>Cancel anytime</span></div>
			<div class="flex items-center space-x-2 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check w-4 h-4 text-purple-600">
					<circle cx="12" cy="12" r="10"></circle>
					<path d="m9 12 2 2 4-4"></path>
				</svg><span>Free updates included</span></div>
		</div>
		<div class="bg-blue-50 p-4 rounded-lg text-sm text-blue-800">
			<p>By completing your purchase, you agree to our Terms of Service and Privacy Policy. Your subscription will automatically renew monthly unless cancelled.</p>
		</div>
		<div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_submit')); ?>">

					<?php if ($pmpro_review) { ?>
						<span id="pmpro_submit_span" class="w-full">
							<input type="hidden" name="confirm" value="1" />
							<input type="hidden" name="token" value="<?php echo esc_attr($pmpro_paypal_token); ?>" />
							<input type="hidden" name="gateway" value="<?php echo esc_attr($gateway); ?>" />
							<input type="hidden" name="submit-checkout" value="1" />
							<input type="submit" id="pmpro_btn-submit" class="w-full py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors <?php echo esc_attr(pmpro_get_element_class('pmpro_btn-submit-checkout', 'pmpro_btn-submit-checkout')); ?>" value="<?php esc_attr_e('Complete Payment', 'paid-memberships-pro'); ?>" />
						</span>

					<?php } else { ?>

						<?php
						/**
						 * Filter to set the default submit button on the checkout page.
						 *
						 * @param bool $pmpro_checkout_default_submit_button Default is true.
						 * @return bool
						 */
						$pmpro_checkout_default_submit_button = apply_filters('pmpro_checkout_default_submit_button', true);
						if ($pmpro_checkout_default_submit_button) {
						?> 
							<span id="pmpro_submit_span" class="w-full">
								<input type="hidden" name="submit-checkout" value="1" />

								<input type="submit" id="pmpro_btn-submit" class="w-full py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors <?php echo esc_attr(pmpro_get_element_class('pmpro_btn-submit-checkout', 'pmpro_btn-submit-checkout')); ?>" value="<?php if ($pmpro_requirebilling) {
									esc_html_e('Submit and checkout', 'paid-memberships-pro');
								} else {
									esc_html_e('Submit and Confirm', 'paid-memberships-pro');
								} ?>" />
							</span>
						<?php
						}
						?>

					<?php } ?>

					<div id="pmpro_processing_message" style="visibility: hidden;">
						<?php
						$processing_message = apply_filters("pmpro_processing_message", __("Processing...", 'paid-memberships-pro'));
						echo wp_kses_post($processing_message);
						?>
					</div>

				</div> <!-- end pmpro_form_submit -->
		<div class="flex items-center justify-center space-x-2 text-sm text-purple-600"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4">
				<path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
			</svg><span>Questions about billing?</span><a href="https://help.medroid.ai/" target="_blank" class="font-medium hover:underline">Contact our support team</a></div>
</div>

</section> <!-- end pmpro_level-ID -->

</div> <!-- end pmpro -->