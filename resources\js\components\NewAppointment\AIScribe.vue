<template>
  <div class="flex items-center gap-2">
    <div class="flex items-center justify-center px-3 py-1 bg-blue-50 rounded-l-lg border-l-4 border-blue-500">
      <span class="text-blue-600 font-semibold mr-1">AI Scribe</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2">
        <path d="M12 18h.01"></path>
        <path d="M8 3h8a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2l-4 4v-4H8a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"></path>
      </svg>
    </div>

    <div class="flex items-center gap-3 border-t border-b border-r rounded-r-lg px-2 py-1.5">
      <!-- Record Audio Button -->
      <button @click="toggleRecording" :disabled="['processing', 'analyzing', 'populating'].includes(status)" :class="[
        'h-8 w-12 flex items-center justify-center rounded transition-all duration-300 relative overflow-hidden',
        status === 'recording' ? (isPaused ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-red-500 hover:bg-red-600') : 'bg-black hover:bg-gray-800'
      ]">
        <!-- Microphone Icon (when not recording) -->
        <svg v-if="status !== 'recording'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="w-6 h-6 text-white transition-all duration-300">
          <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
          <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
          <line x1="12" x2="12" y1="19" y2="22"></line>
        </svg>

        <!-- Stop Icon (when recording and not paused) -->
        <svg v-else-if="!isPaused" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="w-6 h-6 text-white transition-all duration-300">
          <rect x="6" y="6" width="12" height="12"></rect>
        </svg>

        <!-- Play Icon (when recording is paused) -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="w-6 h-6 text-white transition-all duration-300">
          <polygon points="5 3 19 12 5 21 5 3"></polygon>
        </svg>

        <!-- Pulsing Background Effect -->
        <div v-if="status === 'recording' && !isPaused" class="absolute inset-0 bg-red-400 opacity-30 animate-ping">
        </div>
      </button>

      <!-- Pause/Resume Button (only shown when recording) -->
      <button v-if="status === 'recording'" @click="togglePause"
        class="h-8 w-8 flex items-center justify-center rounded transition-all duration-300 bg-gray-700 hover:bg-gray-800 text-white">
        <!-- Pause Icon -->
        <svg v-if="!isPaused" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
          <line x1="6" y1="4" x2="6" y2="20"></line>
          <line x1="18" y1="4" x2="18" y2="20"></line>
        </svg>

        <!-- Resume Icon -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
          <polygon points="5 3 19 12 5 21 5 3"></polygon>
        </svg>
      </button>

      <!-- Recording Animation Waves -->
      <div v-if="status === 'recording' && !isPaused" class="flex items-end space-x-1 h-8">
        <div v-for="n in 4" :key="n" class="w-1 bg-red-500 rounded-full transform transition-all duration-200"
          :class="[`animate-wave-${n}`]" :style="`animation-delay: ${n * 0.1}s; height: ${(Math.random() * 20) + 5}px`">
        </div>
        <div class="ml-2 flex items-center space-x-1">
          <div class="w-1.5 h-1.5 rounded-full bg-red-500 animate-pulse"></div>
          <div class="w-1.5 h-1.5 rounded-full bg-red-400 animate-pulse" style="animation-delay: 0.2s"></div>
        </div>
      </div>

      <!-- Paused Indicator -->
      <div v-if="status === 'recording' && isPaused" class="text-yellow-500 text-sm font-medium">
        Paused
      </div>

      <!-- Recording Duration -->
      <div v-if="status === 'recording'" class="text-sm ml-2">
        {{ formatTime(recordingDuration) }}
      </div>

      <!-- Alternative Options -->
      <div v-if="status === 'idle'" class="flex items-center gap-2">
        <!-- File Upload Button -->
        <label
          class="flex items-center gap-1 cursor-pointer px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="17 8 12 3 7 8"></polyline>
            <line x1="12" x2="12" y1="3" y2="15"></line>
          </svg>
          Upload Audio
          <input type="file" accept="audio/*, .mp3, .wav, .ogg, .webm, .m4a, .mp4, .aac" class="hidden"
            @change="handleFileUpload" />
        </label>

        <!-- Mobile Recording Button -->
        <button @click="showMobileRecordingModal"
            class="flex items-center gap-1 px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="22"></line>
            </svg>
            Mobile Recording
          </button>

        <!-- Manual Transcript Entry Button -->
        <button @click="showManualTranscriptDialog"
          class="px-3 py-1.5 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm flex items-center gap-2">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
          Enter Transcript
        </button>
      </div>

      <!-- Error Message Display -->
      <div v-if="mediaError" class="text-sm text-red-500">
        {{ mediaError }}
      </div>

      <!-- Processing Spinner with Progress -->
      <div v-if="status === 'processing'" class="flex flex-col gap-1">
        <div class="flex items-center gap-2 text-sm text-blue-500">
          <svg class="animate-spin h-4 w-4" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none">
            </circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
          <span>{{ processingStatus || 'Processing audio...' }}</span>
        </div>

        <!-- Progress bar for longer transcriptions -->
        <div v-if="processingProgress > 0" class="w-64 bg-gray-200 rounded-full h-1.5 mt-1">
          <div class="bg-blue-500 h-1.5 rounded-full" :style="`width: ${processingProgress}%`"></div>
        </div>
      </div>

      <!-- Analyzing State -->
      <div v-if="status === 'analyzing'" class="flex items-center gap-2 text-sm text-blue-500">
        <svg class="animate-spin h-4 w-4" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
        Analyzing conversation...
      </div>

      <!-- Review Button -->
      <button v-if="status === 'transcribed'" @click="showTranscriptDialog"
        class="px-3 py-1.5 bg-black hover:bg-gray-800 text-white rounded text-sm flex items-center gap-2">
        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
        </svg>
        Review Transcript
      </button>

      <!-- Populating State -->
      <div v-if="status === 'populating'" class="flex items-center gap-2 text-sm text-blue-500">
        <div class="flex space-x-1">
          <div class="w-1.5 h-1.5 rounded-full bg-blue-500 animate-bounce"></div>
          <div class="w-1.5 h-1.5 rounded-full bg-blue-500 animate-bounce" style="animation-delay: 0.2s"></div>
          <div class="w-1.5 h-1.5 rounded-full bg-blue-500 animate-bounce" style="animation-delay: 0.4s"></div>
        </div>
        Populating records...
      </div>

      <!-- Completed State -->
      <div v-if="status === 'completed'"
        class="text-sm text-green-600 flex items-center gap-2 px-2 py-1 bg-green-50 rounded-full">
        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
        <span>Records updated</span>
      </div>
    </div>

    <!-- Audio Controls Section -->
    <div v-if="audioUrl && status !== 'recording'"
      class="flex items-center gap-2 ml-1 border rounded-lg overflow-hidden">
      <!-- Audio Player -->
      <div class="flex items-center px-2 py-1 bg-gray-50">
        <button @click="toggleAudioPlayback"
          class="w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 hover:bg-blue-600 transition-colors text-white">
          <svg v-if="isAudioPlaying" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <rect x="6" y="5" width="4" height="14"></rect>
            <rect x="14" y="5" width="4" height="14"></rect>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
        </button>

        <!-- Time Display and Progress -->
        <div class="flex flex-col ml-2 w-32">
          <div class="w-full bg-gray-200 rounded-full h-1.5 mb-1">
            <div class="bg-blue-500 h-1.5 rounded-full" :style="`width: ${audioProgress}%`"></div>
          </div>
          <div class="text-xs text-gray-500">
            {{ formatTime(audioCurrentTime) }} / {{ formatTime(audioDuration) }}
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex border-l">
        <!-- Download Button -->
        <a :href="audioUrl" :download="getAudioFileName()"
          class="px-2 py-1.5 border-r text-sm text-gray-600 hover:bg-gray-100 flex items-center gap-1 transition-colors"
          title="Download audio recording">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          <span>Download</span>
        </a>

        <!-- Restart Button -->
        <button @click="restartRecording"
          class="px-2 py-1.5 text-sm text-gray-600 hover:bg-gray-100 flex items-center gap-1 transition-colors"
          title="Start a new recording">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
            <path d="M3 3v5h5"></path>
          </svg>
          <span>Restart</span>
        </button>
      </div>
    </div>

    <!-- Manual Transcript Modal -->
    <div v-if="showManualModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Enter Consultation Transcript</h2>
          <button @click="closeManualModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="mb-4 flex-grow overflow-y-auto">
          <p class="text-sm text-gray-600 mb-2">Enter the conversation transcript below. Prefix each speaker with
            "Doctor:" or "Patient:" to help with analysis.</p>
          <textarea v-model="transcript" class="w-full min-h-[300px] p-3 border rounded" placeholder="Doctor: Hello, how are you feeling today?
Patient: I've been having headaches for the past week.
Doctor: Can you tell me more about these headaches?" style="height: 402px;"></textarea>
        </div>

        <div class="flex justify-end gap-2 mt-2">
          <button @click="closeManualModal" class="px-4 py-2 border rounded hover:bg-gray-50">
            Cancel
          </button>
          <button @click="processManualTranscript" class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded"
            :disabled="!transcript || transcript.trim() === ''">
            Analyze Transcript
          </button>
        </div>
      </div>
    </div>

    <!-- Transcript Review Modal -->
    <div v-if="showTranscriptModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Review Transcript</h2>
          <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Content area with scrolling -->
        <div class="flex-grow overflow-y-auto pr-2">
          <!-- Diarized Transcript Display -->
          <div v-if="diarizedTranscript && diarizedTranscript.length > 0" class="mb-4">
            <h3 class="text-md font-medium mb-2">Speaker Diarization</h3>
            <div class="space-y-2 mb-4">
              <div v-for="(utterance, index) in diarizedTranscript" :key="index" class="p-3 rounded-lg"
                :class="utterance.speaker === 0 ? 'bg-blue-50' : 'bg-green-50'">
                <div class="font-semibold mb-1">
                  Speaker {{ utterance.speaker === 0 ? '1 (Doctor)' : '2 (Patient)' }}
                </div>
                <div>{{ utterance.text }}</div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h3 class="text-md font-medium mb-2">Complete Transcript</h3>
            <textarea v-model="transcript"
              class="w-full min-h-[200px] p-3 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
              placeholder="Transcript will appear here..."></textarea>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-2">
          <button @click="closeModal" class="px-4 py-2 border rounded hover:bg-gray-50">
            Cancel
          </button>
          <button @click="analyzeTranscript" class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded">
            Extract Medical Data
          </button>
        </div>
      </div>
    </div>

    <!-- Results Modal -->
    <div v-if="showResultsModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Extracted Medical Information</h2>
          <button @click="closeResultsModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="flex-grow overflow-y-auto pr-2">
          <div class="space-y-4">
            <div v-for="(content, key) in analysisResults" :key="key" class="border p-4 rounded-lg">
              <h3 class="font-medium capitalize mb-2">{{ formatKey(key) }}</h3>
              <div class="text-gray-700">{{ content }}</div>
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-4">
          <button @click="populateRecords" class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded"
            :disabled="status === 'populating'">
            Populate Medical Records
          </button>
        </div>
      </div>
    </div>

        <!-- Mobile Recording Modal -->
        <div v-if="showMobileDialog"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-md my-4 flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Record With Mobile Device</h2>
          <button @click="showMobileDialog = false" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="flex justify-center mb-6">
          <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="QR Code" class="w-48 h-48" />
          <div v-else class="w-48 h-48 flex items-center justify-center bg-gray-100 rounded-lg">
            <svg class="animate-spin h-8 w-8 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
          </div>
        </div>

        <div class="text-center mb-4">
          <p class="text-sm text-gray-600 mb-2">Scan this QR code with your mobile device to record audio</p>
          <div v-if="sessionStatus" class="text-xs text-gray-500">
            Session expires in {{ sessionTimeRemaining }}
          </div>
        </div>

        <div v-if="mobileRecordingError" class="bg-red-50 p-3 rounded-md mb-4">
          <p class="text-sm text-red-600">{{ mobileRecordingError }}</p>
        </div>

        <div v-if="mobileRecordingSuccess" class="bg-green-50 p-3 rounded-md mb-4">
          <p class="text-sm text-green-600">{{ mobileRecordingSuccess }}</p>
        </div>

        <div class="flex justify-between">
          <button @click="showMobileDialog = false" class="px-4 py-2 border rounded hover:bg-gray-50">
            Cancel
          </button>

          <div>
            <button v-if="sessionProcessed" @click="retrieveTranscript"
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
              Load Transcript
            </button>

            <button v-else-if="sessionId && !sessionProcessed" @click="checkSessionStatus"
              class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded">
              Check Status
            </button>

            <button v-else @click="createMobileRecordingSession" :disabled="isCreatingSession"
              class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed">
              <span v-if="isCreatingSession" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                  viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                Generating...
              </span>
              <span v-else>Generate QR Code</span>
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>

</template>

<script>
import { post } from "../../config/request";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import Recorder from './recorder';

export default {
  name: 'AIScribe',
  emits: ['records-updated'],
  props: {
    encounterId: {
      type: [Number, String],
      required: true,
      validator: value => value !== null && value !== undefined
    }
  },

  data() {
    return {
      // UI States
      status: 'idle', // idle, recording, processing, transcribed, analyzing, analyzed, populating, completed
      mediaError: null,

      // Audio Recording
      recorder: null,
      audioChunks: [],
      audioBlob: null,
      audioUrl: null,
      recordingStartTime: null,
      recordingDuration: 0,
      isPaused: false,
      pauseStartTime: null,
      totalPausedDuration: 0,

      // Audio Playback
      audioElement: null,
      isAudioPlaying: false,
      audioProgress: 0,
      audioCurrentTime: 0,
      audioDuration: 0,

      // Transcript Data
      transcript: '',
      diarizedTranscript: [],
      analysisResults: null,

      // Modals
      showTranscriptModal: false,
      showResultsModal: false,
      showManualModal: false,

      // Configuration
      canRecord: true,
      maxRecordingTime: 45 * 60, // 45 minutes in seconds
      largeFileThresholdMB: 50,
      maxFileSizeMB: 250,
      chunkSizeMB: 25,
      downloadCounter: 1,

      // Processing Status
      processingStatus: '',
      processingProgress: 0,
      recordingTimer: null,
      isGoogleMeetActive: false,

      // Mobile recording related data properties
      showMobileDialog: false,
      isCreatingSession: false,
      sessionId: null,
      qrCodeUrl: null,
      recordUrl: null,
      sessionStatus: null,
      sessionTimeRemaining: '',
      sessionProcessed: false,
      mobileRecordingError: null,
      mobileRecordingSuccess: null,
      sessionCheckTimer: null,
      recordingDocumentId: null,
    }
  },

  watch: {
    encounterId: {
      immediate: true,
      handler(newVal) {
        console.log('AIScribe encounterId updated:', newVal);
      }
    }
  },

  mounted() {
    this.checkRecordingAvailability();
  },

  created() {
    this.detectGoogleMeet();
    window.addEventListener('beforeunload', this.deleteRecording);
  },

  beforeDestroy() {
    this.cleanup();
    this.cleanupAudioPlayer();
  },

  beforeUnmount() {
    this.deleteRecording();
    this.cleanupAudioPlayer();
  },

  methods: {
    // Recording Management
    async checkRecordingAvailability() {
      try {
        const testRecorder = new Recorder();
        await testRecorder.initialize();
        this.canRecord = true;
      } catch (error) {
        this.canRecord = false;
        this.mediaError = 'Recording not supported in this browser. Please use file upload or manual entry.';
      }
    },

    toggleRecording() {
      this.status === 'idle' ? this.startRecording() : this.stopRecording();
    },

    async startRecording() {
      try {
        this.mediaError = null;
        this.status = 'recording';
        this.audioChunks = [];

        // Set up recording timer
        this.recordingStartTime = Date.now();
        this.recordingDuration = 0;
        this.recordingTimer = setInterval(() => {
          this.recordingDuration = Math.floor((Date.now() - this.recordingStartTime) / 1000);
          if (this.recordingDuration >= this.maxRecordingTime) this.stopRecording();
        }, 1000);

        // Initialize recorder
        this.recorder = new Recorder();
        this.recorder.onDataAvailable = (data) => {
          if (data && data.size > 0) this.audioChunks.push(data);
        };
        this.recorder.onStop = () => this.processRecording();

        await this.recorder.initialize();
        this.recorder.start();
      } catch (error) {
        console.error('Error starting recording:', error);
        this.mediaError = 'Failed to start recording: ' + (error.message || 'Unknown error');
        this.canRecord = false;
        this.status = 'idle';
        displayErrorMessage(this.mediaError);
      }
    },

    stopRecording() {
      if (this.recorder) {
        try {
          this.recorder.stop();
          clearInterval(this.recordingTimer);
          this.recordingTimer = null;
          this.status = 'processing';
        } catch (error) {
          console.error('Error stopping recording:', error);
          this.mediaError = 'Error stopping recording';
          this.status = 'idle';
        }
      }
    },

    togglePause() {
      this.isPaused ? this.resumeRecording() : this.pauseRecording();
    },

    pauseRecording() {
      if (!this.recorder || this.status !== 'recording' || this.isPaused) return;

      try {
        if (this.recorder.pause) {
          this.recorder.pause();
        } else if (this.recorder.stream) {
          this.recorder.stream.getTracks().forEach(track => track.enabled = false);
        }

        this.isPaused = true;
        this.pauseStartTime = Date.now();
        clearInterval(this.recordingTimer);
      } catch (error) {
        console.error('Error pausing recording:', error);
      }
    },

    resumeRecording() {
      if (!this.recorder || this.status !== 'recording' || !this.isPaused) return;

      try {
        if (this.recorder.resume) {
          this.recorder.resume();
        } else if (this.recorder.stream) {
          this.recorder.stream.getTracks().forEach(track => track.enabled = true);
        }

        if (this.pauseStartTime) {
          this.totalPausedDuration += (Date.now() - this.pauseStartTime) / 1000;
          this.pauseStartTime = null;
        }

        this.recordingTimer = setInterval(() => {
          this.recordingDuration = Math.floor((Date.now() - this.recordingStartTime) / 1000) - Math.floor(this.totalPausedDuration);
          if (this.recordingDuration >= this.maxRecordingTime) this.stopRecording();
        }, 1000);

        this.isPaused = false;
      } catch (error) {
        console.error('Error resuming recording:', error);
      }
    },

    cleanup() {
      if (this.recorder) {
        if (this.status === 'recording') this.recorder.stop();
        this.recorder = null;
      }

      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }

      if (this.audioUrl) URL.revokeObjectURL(this.audioUrl);
    },

    deleteRecording() {
      this.cleanup();
      this.audioChunks = [];
      this.audioBlob = null;
      this.cleanupAudioPlayer();

      if (this.audioUrl) {
        URL.revokeObjectURL(this.audioUrl);
        this.audioUrl = null;
      }
    },

    restartRecording() {
      this.deleteRecording();
      this.status = 'idle';
      this.transcript = '';
      this.diarizedTranscript = [];
      this.analysisResults = null;
      this.mediaError = null;
      this.processingProgress = 0;
      this.processingStatus = '';
      this.downloadCounter++;
    },

    setupAudioPlayer() {
      // Clean up any existing audio element first
      this.cleanupAudioPlayer();

      if (!this.audioUrl) {
        console.error('No audio URL available for player setup');
        return;
      }

      // Create new audio element
      this.audioElement = new Audio(this.audioUrl);

      // Initialize with default values until loaded
      this.audioDuration = 0;
      this.audioCurrentTime = 0;
      this.audioProgress = 0;

      // Set up audio event listeners
      this.audioElement.addEventListener('loadedmetadata', () => {
        if (this.audioElement && !isNaN(this.audioElement.duration)) {
          this.audioDuration = this.audioElement.duration;
        } else {
          this.audioDuration = 0;
          console.warn('Invalid audio duration');
        }
      });

      this.audioElement.addEventListener('timeupdate', () => {
        if (this.audioElement && !isNaN(this.audioElement.currentTime)) {
          this.audioCurrentTime = this.audioElement.currentTime;
          if (this.audioDuration > 0) {
            this.audioProgress = (this.audioCurrentTime / this.audioDuration) * 100;
          } else {
            this.audioProgress = 0;
          }
        }
      });

      this.audioElement.addEventListener('ended', () => {
        this.isAudioPlaying = false;
        this.audioElement.currentTime = 0;
        this.audioProgress = 0;
      });

      this.audioElement.addEventListener('error', (e) => {
        console.error('Audio player error:', e);
        this.isAudioPlaying = false;
      });
    },

    initAudioPlayer() {
      // This is just an alias for setupAudioPlayer to maintain compatibility
      this.setupAudioPlayer();
    },

    // Audio Processing
    async processRecording() {
      try {
        if (!this.audioChunks || this.audioChunks.length === 0) {
          this.mediaError = 'No audio was recorded';
          this.status = 'idle';
          return;
        }

        this.status = 'processing';
        this.processingStatus = 'Transcribing audio...';
        this.processingProgress = 10;

        // Create the audio blob from chunks
        const mimeType = this.recorder.mimeType || 'audio/webm';
        this.audioBlob = new Blob(this.audioChunks, { type: mimeType });

        // Create URL for the audio
        if (this.audioUrl) {
          URL.revokeObjectURL(this.audioUrl);
        }
        this.audioUrl = URL.createObjectURL(this.audioBlob);

        // Set up audio player for preview
        this.setupAudioPlayer();
        this.processingProgress = 20;

        // Convert Blob to base64 for API submission
        const reader = new FileReader();
        reader.readAsDataURL(this.audioBlob);

        reader.onloadend = async () => {
          const base64Audio = reader.result;
          this.processingProgress = 30;

          // Submit to transcription API with timeout
          try {
            const response = await post("ai_scribe_transcribe", {
              audio_file: base64Audio,
              encounter_id: this.encounterId
            }, { timeout: 180000 }); // 3 minute timeout

            this.processingProgress = 80;

            if (response?.data?.status && response?.data?.data) {
              this.diarizedTranscript = response.data.data.diarized_transcript || [];
              this.transcript = response.data.data.full_transcript || '';

              this.status = 'transcribed';
              this.processingProgress = 100;

              // Show the transcript for review
              this.showTranscriptModal = true;
            } else {
              throw new Error(response?.data?.message || 'Transcription failed');
            }
          } catch (error) {
            console.error('Transcription error:', error);
            this.mediaError = `Transcription error: ${error.message || 'Service unavailable'}`;
            displayErrorMessage(this.mediaError);
            this.status = 'idle';
          }
        };

        reader.onerror = (error) => {
          console.error('Error reading audio file:', error);
          this.mediaError = 'Error reading audio file';
          displayErrorMessage(this.mediaError);
          this.status = 'idle';
        };
      } catch (error) {
        console.error('Error processing recording:', error);
        this.mediaError = `Error processing recording: ${error.message || 'Unknown error'}`;
        displayErrorMessage(this.mediaError);
        this.status = 'idle';
      }
    },

    // File Upload Handling
    async handleFileUpload(event) {
      try {
        const file = event.target.files[0];
        if (!file) return;

        this.mediaError = null;
        this.status = 'processing';

        // Validate file type
        const isAudioFile = file.type.startsWith('audio/') ||
          file.type.startsWith('video/webm') ||
          /\.(mp3|wav|ogg|webm|m4a|mp4|aac)$/i.test(file.name);

        if (!isAudioFile) {
          throw new Error('Please upload an audio file (.mp3, .wav, .webm, .ogg, .m4a, etc)');
        }

        // Check file size
        const fileSizeMB = file.size / (1024 * 1024);
        if (fileSizeMB > this.maxFileSizeMB) {
          displayMessage(`Warning: This audio file is very large (${fileSizeMB.toFixed(1)} MB). Processing may take a long time or fail.`, { timeout: 10000 });
        } else if (fileSizeMB > this.largeFileThresholdMB) {
          displayMessage(`This is a large audio file (${fileSizeMB.toFixed(1)} MB). Processing may take several minutes.`, { timeout: 8000 });
        }

        // Save audio data
        this.audioBlob = file;
        if (this.audioUrl) URL.revokeObjectURL(this.audioUrl);
        this.audioUrl = URL.createObjectURL(file);
        this.initAudioPlayer();

        // Process the file
        await this.processFileUpload();
      } catch (error) {
        console.error('Error processing uploaded file:', error);
        this.mediaError = `Failed to process file: ${error.message || 'Unknown error'}`;
        displayErrorMessage(this.mediaError);
        this.status = 'idle';
      } finally {
        event.target.value = '';
      }
    },

    async processFileUpload() {
      try {
        // Convert blob to base64
        const reader = new FileReader();
        reader.readAsDataURL(this.audioBlob);

        this.processingProgress = 20;
        this.processingStatus = 'Processing audio file...';

        reader.onloadend = async () => {
          const base64Audio = reader.result;
          this.processingProgress = 40;

          // Send to API for transcription
          const response = await post("ai_scribe_transcribe", {
            audio_file: base64Audio,
            encounter_id: this.encounterId
          });

          this.processingProgress = 90;

          if (response?.data?.status && response?.data?.data) {
            this.diarizedTranscript = response.data.data.diarized_transcript || [];
            this.transcript = response.data.data.full_transcript || '';

            this.status = 'transcribed';
            this.processingProgress = 100;

            // Show the transcript for review
            this.showTranscriptModal = true;
          } else {
            throw new Error(response?.data?.message || 'Transcription failed');
          }
        };

        reader.onerror = (error) => {
          console.error('Error reading file:', error);
          this.mediaError = 'Error reading audio file';
          this.status = 'idle';
        };
      } catch (error) {
        console.error('Error processing file:', error);
        this.mediaError = `Failed to process file: ${error.message || 'Unknown error'}`;
        displayErrorMessage(this.mediaError);
        this.status = 'idle';
      }
    },

    // Convert blob to base64 for transmission
    blobToBase64(blob) {
      return new Promise((resolve, reject) => {
        if (!blob) {
          reject(new Error('No blob provided'));
          return;
        }

        // Check if blob is too large
        const blobSizeMB = blob.size / (1024 * 1024);
        if (blobSizeMB > 50) {
          reject(new Error('File too large for base64 conversion. Try using a shorter recording or different format.'));
          return;
        }

        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.onerror = (e) => reject(new Error('Failed to read audio data'));
        reader.readAsDataURL(blob);
      });
    },

    // UI Interaction
    showManualTranscriptDialog() {
      this.transcript = '';
      this.showManualModal = true;
    },

    closeManualModal() {
      this.showManualModal = false;
    },

    showTranscriptDialog() {
      if ((!this.diarizedTranscript || this.diarizedTranscript.length === 0) && this.transcript) {
        this.diarizedTranscript = this.extractSpeakersFromText(this.transcript);
      }
      this.showTranscriptModal = true;
    },

    closeModal() {
      this.showTranscriptModal = false;
    },

    closeResultsModal() {
      this.showResultsModal = false;
    },

    // Analysis
    async processManualTranscript() {
      try {
        if (!this.transcript || this.transcript.trim() === '') {
          throw new Error('Please enter a transcript');
        }

        this.closeManualModal();
        this.status = 'analyzing';

        const wordCount = this.transcript.split(/\s+/).length;

        if (wordCount > 1000) {
          displayMessage(`This is a long transcript (${wordCount} words). Analysis may take several minutes.`, { timeout: 8000 });
        }

        // Extract speakers from text if possible
        this.diarizedTranscript = this.extractSpeakersFromText(this.transcript);
        await this.analyzeTranscript();
      } catch (error) {
        console.error('Error processing manual transcript:', error);
        this.mediaError = error.message || 'Failed to process transcript';
        displayErrorMessage(this.mediaError);
        this.status = 'idle';
      }
    },

    async analyzeTranscript() {
      try {
        if (!this.transcript || this.transcript.trim() === '') {
          throw new Error('Transcript is empty');
        }

        if (this.showTranscriptModal) this.closeModal();
        this.status = 'analyzing';

        const wordCount = this.transcript.split(/\s+/).length;
        if (wordCount > 200) {
          displayMessage('This is a longer transcript. Analysis may take up to 3 minutes...', { timeout: 8000 });
        }

        // Set up timeout handling
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 240000); // 4 minute timeout

        try {
          const response = await post("ai_scribe_analyze", {
            transcript: this.transcript,
            encounter_id: this.encounterId
          }, { signal: controller.signal });

          clearTimeout(timeoutId);

          if (!response || !response.data) {
            throw new Error('No response from server');
          }

          if (response.data && response.data.status) {
            this.analysisResults = response.data.data;
            this.showResultsModal = true;
            this.status = 'analyzed';
          } else {
            throw new Error(response.data?.message || 'Failed to analyze transcript');
          }
        } catch (abortError) {
          if (abortError.name === 'AbortError') {
            throw new Error('Analysis timed out. The transcript may be too long to process.');
          } else {
            throw abortError;
          }
        }
      } catch (error) {
        console.error('Error analyzing transcript:', error);

        let suggestion = '';
        if (error.message.includes('timed out')) {
          suggestion = ' Try shortening the transcript or breaking it into smaller sections.';
        }

        this.mediaError = `Failed to analyze transcript: ${error.message || 'Unknown error'}`;
        displayErrorMessage(this.mediaError + suggestion);
        this.status = 'transcribed';
      }
    },

    async populateRecords() {
      try {
        if (!this.analysisResults) {
          throw new Error('No analysis results to populate');
        }

        this.closeResultsModal();
        this.status = 'populating';

        // Map analysis results to appropriate fields
        const mappedData = {
          vitals: this.analysisResults.vitals || '',
          concerns: this.analysisResults.concerns || '',
          history: this.analysisResults.history || '',
          examination: this.analysisResults.examination || '',
          systems_review: this.analysisResults.systems_review || '',
          allergies: this.analysisResults.allergies || '',
          family_history: this.analysisResults.family_history || '',
          medical_history: this.analysisResults.medical_history || '',
          medications: this.analysisResults.medications || '',
          social_history: this.analysisResults.social_history || '',
          mental_health: this.analysisResults.mental_health || '',
          lifestyle: this.analysisResults.lifestyle || '',
          safeguarding: this.analysisResults.safeguarding || '',
          notes: this.analysisResults.notes || '',
          comments: this.analysisResults.comments || '',
          safety_netting: this.analysisResults.safety_netting || '',
          preventative_care: this.analysisResults.preventative_care || '',
          plan: this.analysisResults.plan || ''
        };

        // Emit event to parent for handling the data
        this.$emit('ai-populate', {
          extractedData: mappedData,
          rawData: this.analysisResults
        });

        // Save to server
        const response = await post("ai_scribe_populate", {
          data: JSON.stringify(this.analysisResults),
          encounter_id: this.encounterId
        });

        if (!response || !response.data) {
          throw new Error('No response from server');
        }

        if (response.data && response.data.status) {
          displayMessage('Medical records populated by AI');
          this.status = 'completed';

          // Reset to idle after 3 seconds
          setTimeout(() => {
            this.status = 'idle';
          }, 3000);
        } else {
          throw new Error(response.data?.message || 'Failed to populate records');
        }
      } catch (error) {
        console.error('Error populating records:', error);
        this.mediaError = `Failed to populate records: ${error.message || 'Unknown error'}`;
        displayErrorMessage(this.mediaError);
        this.status = 'analyzed';
      }
    },

    // Audio Player
    // initAudioPlayer() {
    //   this.cleanupAudioPlayer();

    //   this.audioElement = new Audio(this.audioUrl);

    //   this.audioElement.addEventListener('loadedmetadata', () => {
    //     this.audioDuration = this.audioElement.duration;
    //   });

    //   this.audioElement.addEventListener('timeupdate', () => {
    //     this.audioCurrentTime = this.audioElement.currentTime;
    //     this.audioProgress = (this.audioCurrentTime / this.audioDuration) * 100;
    //   });

    //   this.audioElement.addEventListener('ended', () => {
    //     this.isAudioPlaying = false;
    //     this.audioElement.currentTime = 0;
    //     this.audioProgress = 0;
    //   });

    //   this.audioElement.addEventListener('error', (e) => {
    //     console.error('Audio player error:', e);
    //     this.isAudioPlaying = false;
    //   });
    // },

    cleanupAudioPlayer() {
      if (this.audioElement) {
        this.audioElement.pause();
        this.audioElement.src = '';
        this.audioElement.removeAttribute('src');
        this.audioElement = null;
      }
      if (this.audioUpdateTimer) {
        clearInterval(this.audioUpdateTimer);
        this.audioUpdateTimer = null;
      }
      this.isAudioPlaying = false;
      this.audioProgress = 0;
      this.audioCurrentTime = 0;
      this.audioDuration = 0;
    },

    toggleAudioPlayback() {
      if (!this.audioElement) {
        this.setupAudioPlayer();
      }

      if (this.isAudioPlaying) {
        this.audioElement.pause();
        this.isAudioPlaying = false;
      } else {
        // Check if audio source is valid
        if (!this.audioUrl) {
          console.error('No audio URL to play');
          return;
        }

        this.audioElement.play().catch(error => {
          console.error('Error playing audio:', error);
          if (error.name === 'NotAllowedError') {
            displayErrorMessage('Browser requires user interaction before playing audio.');
          } else if (error.name === 'NotSupportedError') {
            displayErrorMessage('This audio format is not supported by your browser.');
          } else {
            displayErrorMessage('Error playing audio: ' + (error.message || 'Unknown error'));
          }
        });
        this.isAudioPlaying = true;
      }
    },

    // Helper Methods
    formatTime(seconds) {
      // Handle invalid inputs
      if (seconds === undefined || seconds === null || isNaN(seconds) || !isFinite(seconds)) {
        return '00:00';
      }

      // Ensure we're working with a valid number
      seconds = Math.max(0, Math.round(parseFloat(seconds) || 0));
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    getAudioFileName() {
      const now = new Date();
      const datePart = now.toISOString().split('T')[0];
      const patientId = this.getPatientId();
      const extension = this.getAudioExtension();

      return `patient_${patientId}_${datePart}_${this.downloadCounter}.${extension}`;
    },

    getPatientId() {
      return this.encounterId || 'unknown';
    },

    getAudioExtension() {
      if (!this.audioBlob) return 'wav';

      if (this.audioBlob.type.includes('webm')) return 'webm';
      if (this.audioBlob.type.includes('wav')) return 'wav';
      if (this.audioBlob.type.includes('mp3')) return 'mp3';
      if (this.audioBlob.type.includes('ogg')) return 'ogg';
      if (this.audioBlob.type.includes('m4a')) return 'm4a';

      return 'wav';
    },

    detectGoogleMeet() {
      this.isGoogleMeetActive = window.location.href.includes('meet.google.com') ||
        document.querySelector('[data-meeting-code]') !== null ||
        document.querySelector('[data-unresolved="GoogleMeet"]') !== null;
    },

    formatKey(key) {
      if (!key) return '';
      return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    },

    extractSpeakersFromText(text) {
      if (!text) return [];

      const lines = text.split('\n');
      const diarized = [];
      let currentSpeaker = null;
      let currentText = '';

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        const doctorMatch = trimmedLine.match(/^Doctor:(.+)/i);
        const patientMatch = trimmedLine.match(/^Patient:(.+)/i);

        if (doctorMatch) {
          if (currentSpeaker !== null && currentText) {
            diarized.push({ speaker: currentSpeaker, text: currentText.trim() });
          }
          currentSpeaker = 0;
          currentText = doctorMatch[1];
        } else if (patientMatch) {
          if (currentSpeaker !== null && currentText) {
            diarized.push({ speaker: currentSpeaker, text: currentText.trim() });
          }
          currentSpeaker = 1;
          currentText = patientMatch[1];
        } else if (currentSpeaker !== null) {
          currentText += ' ' + trimmedLine;
        } else {
          currentSpeaker = 0;
          currentText = trimmedLine;
        }
      }

      if (currentSpeaker !== null && currentText) {
        diarized.push({ speaker: currentSpeaker, text: currentText.trim() });
      }

      return diarized;
    },

    // Duration estimation from file size
    estimateDurationFromSize(audioFile) {
      const fileSizeMB = audioFile.size / (1024 * 1024);
      let estimatedDuration = 0;

      // If the audio file has a duration property, use that
      if (audioFile.duration) {
        return audioFile.duration;
      }

      // Estimate based on file type
      if (audioFile.type.includes('mp3')) {
        estimatedDuration = fileSizeMB * 60; // ~1 minute per MB for MP3
      } else if (audioFile.type.includes('wav')) {
        // WAV estimation varies based on file size
        if (fileSizeMB > 800) {
          estimatedDuration = fileSizeMB * 5.9;
        } else if (fileSizeMB > 100) {
          estimatedDuration = fileSizeMB * 6;
        } else if (fileSizeMB > 20) {
          estimatedDuration = fileSizeMB * 5.5;
        } else {
          estimatedDuration = fileSizeMB * 5;
        }
      } else if (audioFile.type.includes('webm')) {
        estimatedDuration = fileSizeMB * 120; // ~2 minutes per MB for WebM
      } else if (audioFile.type.includes('m4a') || audioFile.type.includes('aac')) {
        estimatedDuration = fileSizeMB * 90; // ~1.5 minutes per MB for M4A/AAC
      } else {
        estimatedDuration = fileSizeMB * 60; // Default estimate
      }

      // Special case correction for known file sizes
      if (audioFile.type.includes('wav') && Math.abs(fileSizeMB - 841) < 10 && Math.abs(estimatedDuration - 5000) > 500) {
        return 5000;
      }

      return estimatedDuration;
    },

    // Get actual audio duration using Audio API
    async getAudioDuration(audioFile) {
      return new Promise((resolve, reject) => {
        try {
          // For very large files, use estimation to avoid memory issues
          const fileSizeMB = audioFile.size / (1024 * 1024);
          if (fileSizeMB > 200) {
            const estimatedDuration = this.estimateDurationFromSize(audioFile);
            return resolve(estimatedDuration);
          }

          const mimeType = this.recorder.mimeType || 'audio/webm';
          this.audioBlob = new Blob(this.audioChunks, { type: mimeType });

          // Create URL for the audio
          this.audioUrl = URL.createObjectURL(this.audioBlob);

          // For smaller files, get actual duration
          // const audioUrl = URL.createObjectURL(audioFile);
          const audio = new Audio(audioUrl);

          const onLoad = () => {
            const duration = audio.duration;
            audio.removeEventListener('loadedmetadata', onLoad);
            audio.removeEventListener('error', onError);
            URL.revokeObjectURL(audioUrl);
            resolve(duration);
          };

          const onError = (error) => {
            audio.removeEventListener('loadedmetadata', onLoad);
            audio.removeEventListener('error', onError);
            URL.revokeObjectURL(audioUrl);
            reject(error);
          };

          audio.addEventListener('loadedmetadata', onLoad);
          audio.addEventListener('error', onError);

          // Set timeout fallback
          setTimeout(() => {
            const estimatedDuration = this.estimateDurationFromSize(audioFile);
            audio.removeEventListener('loadedmetadata', onLoad);
            audio.removeEventListener('error', onError);
            URL.revokeObjectURL(audioUrl);
            resolve(estimatedDuration);
          }, 3000);
        } catch (error) {
          // Fallback to estimation
          try {
            const estimatedDuration = this.estimateDurationFromSize(audioFile);
            resolve(estimatedDuration);
          } catch (err) {
            reject(error);
          }
        }
      });
    },
    // Show the mobile recording modal
    showMobileRecordingModal() {
      this.showMobileDialog = true;
      this.mobileRecordingError = null;
      this.mobileRecordingSuccess = null;

      // // If we already have a session, check its status
      // if (this.sessionId) {
      //   this.checkSessionStatus();
      // }
      this.createMobileRecordingSession();
    },

    // Create a new mobile recording session
    async createMobileRecordingSession() {
      this.isCreatingSession = true;
      this.mobileRecordingError = null;
      this.mobileRecordingSuccess = null;

      try {
        // Create a new recording session
        const response = await post("ai_scribe_create_mobile_recording", {
          encounter_id: this.encounterId,
          patient_id: this.patientId,
          expire_minutes: 30 // Session expires in 30 minutes
        });

        console.log('Mobile recording session response:', response);

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to create mobile recording session');
        }

        // Store session data
        const sessionData = response.data.data;
        this.sessionId = sessionData.session_id;
        this.qrCodeUrl = sessionData.qr_code_url;
        this.recordUrl = sessionData.record_url;

        // Start checking session status
        this.startSessionStatusCheck();

        this.mobileRecordingSuccess = 'QR code generated successfully. Scan with your mobile device to record audio.';
      } catch (error) {
        console.error('Error creating mobile recording session:', error);
        this.mobileRecordingError = `Error: ${error.message || 'Failed to create recording session'}`;
      } finally {
        this.isCreatingSession = false;
      }
    },

    // Start periodic checking of session status
    startSessionStatusCheck() {
      // Clear any existing timer
      if (this.sessionCheckTimer) {
        clearInterval(this.sessionCheckTimer);
      }

      // Set up status check every 10 seconds
      this.sessionCheckTimer = setInterval(() => {
        this.checkSessionStatus();
      }, 10000); // Check every 10 seconds
    },

    // Check the status of the mobile recording session
    async checkSessionStatus() {
      if (!this.sessionId) return;

      try {
        const response = await post("ai_scribe_check_mobile_recording", {
          session_id: this.sessionId
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to check session status');
        }

        // Update session status
        const statusData = response.data.data;
        this.sessionStatus = statusData;
        this.sessionTimeRemaining = this.formatTimeRemaining(statusData.time_remaining);
        this.sessionProcessed = statusData.processed;

        // If session has been processed and is ready for transcription
        if (statusData.processed && statusData.transcription_ready && statusData.document_id) {
          this.mobileRecordingSuccess = 'Audio recording has been uploaded! Click "Load Transcript" to continue.';

          // Stop checking status as we have everything we need
          if (this.sessionCheckTimer) {
            clearInterval(this.sessionCheckTimer);
          }
          
          console.log('Audio recording ready for transcription', statusData);
          
          // Store document ID for reference
          this.recordingDocumentId = statusData.document_id;
        } else if (statusData.processed && !statusData.transcription_ready) {
          // Processed but missing required data
          console.warn('Session processed but not ready for transcription', statusData);
          this.mobileRecordingError = 'Audio processed but not ready for transcription. Try again or contact support.';
        }
      } catch (error) {
        console.error('Error checking session status:', error);
        // Show a generic error message if checking fails repeatedly
        if (!this.mobileRecordingSuccess) {
          this.mobileRecordingError = 'Could not verify recording status. Try refreshing the page.';
        }
      }
    },

    // Format time remaining in human-readable format
    formatTimeRemaining(seconds) {
      if (!seconds) return 'Expired';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);

      if (minutes === 0) {
        return `${remainingSeconds} seconds`;
      }

      return `${minutes} minute${minutes !== 1 ? 's' : ''} ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
    },

    // Retrieve and load the transcript from a processed mobile recording
    async retrieveTranscript() {
      if (!this.sessionId) {
        this.mobileRecordingError = 'Missing session ID. Please try again.';
        return;
      }
      
      if (!this.sessionProcessed) {
        this.mobileRecordingError = 'Recording not yet processed. Please wait and try again.';
        return;
      }

      // Clear any existing errors
      this.mobileRecordingError = null;
      this.mediaError = null;
      
      try {
        // First get the session status to confirm it's ready
        this.mobileRecordingSuccess = 'Checking recording status...';
        
        const response = await post("ai_scribe_check_mobile_recording", {
          session_id: this.sessionId
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to retrieve session status');
        }

        // Check if the session is ready for transcription
        const sessionData = response.data.data;
        if (!sessionData.transcription_ready) {
          throw new Error('Audio recording not ready for transcription. Try again in a moment.');
        }
        
        if (!sessionData.document_id) {
          throw new Error('Document ID missing. Please try again or record a new audio file.');
        }

        // Close the mobile dialog and update UI
        this.showMobileDialog = false;
        this.status = 'processing';
        this.processingStatus = 'Processing mobile recording transcript...';
        this.processingProgress = 20;
        
        // Provide feedback
        try {
          displayMessage('Processing mobile recording...');
        } catch (e) {
          console.log('Processing mobile recording...');
        }

        // Short delay to ensure UI updates
        await new Promise(resolve => setTimeout(resolve, 500));
        this.processingProgress = 30;

        // Call the specific mobile recording processing endpoint
        console.log('Calling process endpoint with session ID:', this.sessionId);
        const transcriptResponse = await post("ai_scribe_process_mobile_recording", {
          session_id: this.sessionId,
          encounter_id: this.encounterId
        });

        this.processingProgress = 80;

        if (!transcriptResponse?.data?.status) {
          throw new Error(transcriptResponse?.data?.message || 'Failed to process mobile recording');
        }

        const transcriptData = transcriptResponse.data.data;
        if (!transcriptData || !transcriptData.diarized_transcript || !transcriptData.full_transcript) {
          throw new Error('Invalid transcript data received from server');
        }
        
        // Store the data
        this.diarizedTranscript = transcriptData.diarized_transcript;
        this.transcript = transcriptData.full_transcript;
        this.processingProgress = 100;

        // Show the transcript review modal
        this.showTranscriptModal = true;
        this.status = 'transcribed';
        
        try {
          displayMessage('Transcript processed successfully');
        } catch (e) {
          console.log('Transcript processed successfully');
        }
      } catch (error) {
        console.error('Error retrieving transcript:', error);
        this.mobileRecordingError = `Error: ${error.message || 'Failed to retrieve transcript'}`;
        this.status = 'idle';
        this.processingProgress = 0;
        
        try {
          displayErrorMessage(`Failed to process recording: ${error.message}`);
        } catch (e) {
          console.error('Failed to display error message');
        }
      }
    }
  }
};
</script>

<style scoped>
@keyframes wave-1 {

  0%,
  100% {
    height: 0.5rem;
  }

  50% {
    height: 1.5rem;
  }
}

@keyframes wave-2 {

  0%,
  100% {
    height: 0.75rem;
  }

  50% {
    height: 2rem;
  }
}

@keyframes wave-3 {

  0%,
  100% {
    height: 1rem;
  }

  50% {
    height: 1.75rem;
  }
}

@keyframes wave-4 {

  0%,
  100% {
    height: 0.5rem;
  }

  50% {
    height: 1.25rem;
  }
}

.animate-wave-1 {
  animation: wave-1 1s ease-in-out infinite;
}

.animate-wave-2 {
  animation: wave-2 1s ease-in-out infinite;
}

.animate-wave-3 {
  animation: wave-3 1s ease-in-out infinite;
}

.animate-wave-4 {
  animation: wave-4 1s ease-in-out infinite;
}
</style>