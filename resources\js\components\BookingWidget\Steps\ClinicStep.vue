<template>
  <div class="kivi-booking-step" id="step-clinic">
    <h2 class="kivi-step-title"></h2>
    <p class="kivi-step-subtitle"></p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader"></div>
    </div>

    <template v-else>
      <!-- Search input for filtering clinics -->
      <div v-if="clinics.length > 4" class="kivi-form-group kivi-search-input">
        <div class="kivi-search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          class="kivi-form-input"
          id="clinic-search"
          placeholder="Search clinics..."
          v-model="searchTerm"
        >
      </div>

      <!-- Clinic cards grid view -->
      <div class="kivi-grid" id="clinic-list">
        <div
          v-for="clinic in filteredClinics"
          :key="clinic.id"
          class="kivi-card clinic-card"
          :class="{ 'selected': selectedClinicId === clinic.id }"
          @click="selectClinic(clinic)"
        >
          <div class="kivi-card-header">
            <div>
              <h3 class="kivi-card-title">{{ clinic.name }}</h3>
              <div v-if="clinic.address" class="kivi-card-subtitle">{{ clinic.address }}</div>
            </div>
          </div>
          <div v-if="clinic.description" class="kivi-card-body">
            {{ clinic.description }}
          </div>
          <div v-if="clinic.specializations && clinic.specializations.length > 0" class="kivi-card-footer">
            <span v-for="(specialty, index) in clinic.specializations" :key="index" class="kivi-badge kivi-badge-purple">
              {{ specialty }}
            </span>
          </div>
        </div>
      </div>

      <div v-if="filteredClinics.length === 0 && !isLoading" class="kivi-empty-state">
        <p>No clinics found. Please try again later.</p>
      </div>
    </template>
  </div>
</template>

<script>
import { apiCall } from '../../../config/request';

export default {
  name: 'ClinicStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      clinics: [],
      searchTerm: '',
      selectedClinicId: null,
      isLoading: false
    };
  },
  computed: {
    filteredClinics() {
      if (!this.searchTerm) {
        return this.clinics;
      }

      const term = this.searchTerm.toLowerCase();
      return this.clinics.filter(clinic =>
        clinic.name.toLowerCase().includes(term) ||
        (clinic.address && clinic.address.toLowerCase().includes(term))
      );
    }
  },
  created() {
    this.fetchClinics();

    if (this.bookingData.clinic) {
      this.selectedClinicId = this.bookingData.clinic.id;
    }
  },
  methods: {
    async fetchClinics() {
      try {
        this.isLoading = true;

        const response = await apiCall.get('get_clinic_details_json', {
          params: {
            doctor_id: 0,
            searchKey: '',
            preselected_clinic: 0
          }
        });

        if (response.data?.status && Array.isArray(response.data.data)) {
          this.clinics = response.data.data.map(clinic => ({
            id: clinic.id,
            name: clinic.name,
            address: clinic.address || '',
            description: clinic.description || null,
            specializations: clinic.specialties || []
          }));

          // Auto-select if only one clinic
          if (this.clinics.length === 1) {
            this.selectClinic(this.clinics[0]);
          }
        }
      } catch (error) {
        console.error('Error fetching clinics:', error);
      } finally {
        this.isLoading = false;
      }
    },



    selectClinic(clinic) {
      this.selectedClinicId = clinic.id;

      this.$emit('update:booking-data', {
        ...this.bookingData,
        clinic: {
          id: clinic.id,
          name: clinic.name,
          address: clinic.address || ''
        },
        // Clear subsequent selections when changing clinic
        category: null,
        services: []
      });
    }
  }
};
</script>

<style scoped>
.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-form-group {
  margin-bottom: 1rem;
}

.kivi-search-input {
  position: relative;
}

.kivi-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.kivi-search-input input {
  padding-left: 2.5rem;
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.kivi-card {
  border: 2px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.kivi-card:hover {
  border-color: rgba(79, 70, 229, 0.4);
  background-color: rgba(79, 70, 229, 0.02);
}

.kivi-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
}

.kivi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.kivi-card-title {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.kivi-card-subtitle {
  font-size: 0.75rem;
  color: var(--gray);
}

.kivi-card-body {
  font-size: 0.875rem;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.kivi-card-footer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.kivi-badge-blue {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgba(29, 78, 216, 1);
}

.kivi-badge-green {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgba(5, 150, 105, 1);
}

.kivi-badge-purple {
  background-color: rgba(124, 58, 237, 0.1);
  color: rgba(109, 40, 217, 1);
}

.kivi-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.kivi-loader {
  border: 3px solid rgba(229, 231, 235, 1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2rem;
  height: 2rem;
  animation: kivi-spin 1s linear infinite;
}

.kivi-empty-state {
  text-align: center;
  padding: 2rem 0;
  color: var(--gray);
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .kivi-grid {
    grid-template-columns: 1fr;
  }
}


</style>