Overview

  The Medroid Chat Module provides a comprehensive communication system for
  all users within the clinic ecosystem (administrators, clinic admins,
  doctors, receptionists, patients). The module enables direct and group
  messaging with file sharing capabilities, notification management, and
  clinic-level control over doctor-patient communication.

  Key Features

  1. Messaging
    - Direct one-to-one messaging
    - Group conversations with multiple members
    - Text messages with formatting
    - File attachments (images, PDFs, documents)
    - Message read status tracking
  2. Notifications
    - Real-time notification counter in header
    - Notification dropdown with unread messages
    - Browser tab notification count
  3. User Experience
    - Modern Tailwind CSS interface
    - Responsive design for all devices
    - Conversation search functionality
    - Message history with timestamps and date separators
  4. Administration
    - Clinic-level settings to enable/disable doctor-patient chat
    - Group conversation management (add/remove members)
    - Permission-based access control

  Technical Implementation

  1. Database Schema

  Five tables have been created to manage the chat functionality:

  - kc_chat_conversations: Stores conversation metadata (type, name,
  created_by)
  - kc_chat_members: Maps users to conversations they're part of
  - kc_chat_messages: Stores message content, attachments, and metadata
  - kc_chat_notifications: Tracks message read status for each user
  - kc_chat_settings: Stores clinic-level chat configuration settings

  Database migration script:
  /app/database/migrations/create_chat_module_tables.php

  2. Backend Components

  PHP Models

  - KCChatConversation (/app/models/KCChatConversation.php)
    - Manages conversations and members
    - Handles finding, creating, and updating conversations
    - Controls membership operations (add/remove/list members)
  - KCChatMessage (/app/models/KCChatMessage.php)
    - Handles message CRUD operations
    - Manages read status tracking
    - Handles notification creation and management
    - Provides message counts and status information
  - KCChatSetting (/app/models/KCChatSetting.php)
    - Manages clinic-level chat settings
    - Controls doctor-patient communication permissions

  PHP Controller

  - KCChatController (/app/controllers/KCChatController.php)
    - Provides all REST API endpoints for chat functionality
    - Integrates with WordPress sidebar menus
    - Implements permission checks and security validations
    - Handles file uploads for message attachments

  3. Frontend Components

  Main Chat Interface

  - ChatDashboard.vue (/resources/js/views/Chat/ChatDashboard.vue)
    - Provides the complete chat user interface
    - Handles real-time updates via polling
    - Manages conversations, messages, and user interactions
    - Implements file uploads and attachment handling

  Notification Integration

  - Header.vue (/resources/js/components/Partials/Header.vue)
    - Enhanced with chat notification functionality
    - Displays unread message count
    - Shows notification dropdown with recent messages
    - Provides quick access to conversations

  Routing

  - router.js (/resources/js/router.js)
    - Includes route for the chat module
    - Sets up proper permissions and navigation

  Initialization and Integration

  1. Database Setup

  The database tables are created during plugin activation via the migration
  system. The migration script (create_chat_module_tables.php) handles both
  creation and cleanup of tables.

  2. Menu Integration

  The chat module automatically adds itself to the dashboard sidebar for all
  user roles using WordPress filter hooks in the KCChatController:

  add_filter('kivicare_administrator_dashboard_sidebar_data', array($this,
  'addChatToSidebar'));
  add_filter('kivicare_clinic_admin_dashboard_sidebar_data', array($this,
  'addChatToSidebar'));
  add_filter('kivicare_doctor_dashboard_sidebar_data', array($this,
  'addChatToSidebar'));
  add_filter('kivicare_receptionist_dashboard_sidebar_data', array($this,
  'addChatToSidebar'));
  add_filter('kivicare_patient_dashboard_sidebar_data', array($this,
  'addChatToSidebar'));

  3. API Endpoints

  The following REST API endpoints are available through KCChatController:

  - get_conversations - Retrieve user's conversations
  - get_messages - Get messages for a conversation
  - send_message - Send a new message (with optional file)
  - create_conversation - Create a new direct or group conversation
  - update_conversation - Update conversation details or members
  - mark_as_read - Mark messages as read
  - delete_message - Delete a message
  - leave_conversation - Leave a group conversation
  - get_chat_settings - Get clinic chat settings
  - save_chat_settings - Update clinic chat settings
  - get_users_for_chat - Get users available for chatting
  - get_unread_count - Get total unread message count
  - get_unread_messages - Get unread message details for notifications
  - mark_all_as_read - Mark all messages as read

  User Flow

  1. Access:
    - Users access the chat module from the sidebar menu
    - The chat icon is visible to all user roles
  2. Starting Conversations:
    - Users can search and start direct chats with available users
    - Non-patient users can create group conversations
  3. Messaging:
    - Send text messages
    - Upload and share files (images, documents, etc.)
    - View read status and message timestamps
  4. Notifications:
    - Receive real-time notifications for new messages
    - See unread count in the header bell icon
    - View message previews in the notification dropdown
  5. Administration:
    - Clinic admins can control doctor-patient chat permissions
    - Group creators can manage members and rename conversations

  Permissions and Access Control

  Access to conversations is controlled based on user roles and clinic
  associations:

  1. Administrators:
    - Can chat with anyone in any clinic
    - Can manage all chat settings
  2. Clinic Admins:
    - Can chat with all users in their clinic
    - Can control doctor-patient chat settings for their clinic
  3. Doctors:
    - Can chat with admins, other doctors, and receptionists
    - Can chat with patients only if enabled in clinic settings
  4. Receptionists:
    - Can chat with admins, doctors, and other receptionists
    - Cannot chat with patients
  5. Patients:
    - Can chat with admins and clinic admins
    - Can chat with doctors only if enabled in clinic settings
    - Cannot chat with other patients or receptionists

  Pending Tasks and Future Enhancements

  1. Real-time Updates:
    - Currently using polling for updates (every 10 seconds)
    - Future enhancement: Implement WebSockets for true real-time messaging
  2. Notification Sounds:
    - Sound file is referenced but browser permissions handling could be
  improved
    - Add user preference for notification sounds
  3. Message Search:
    - Add functionality to search within message content

  4. Typing Indicators:
    - Add "user is typing" indicators for better UX
  5. Message Status:
    - Add "delivered" and "seen" indicators with timestamps
  6. Message Reactions:
    - Add ability to react to messages with emojis
  7. Internationalization:
    - Add translations for all UI elements
  8. Message Forwarding:
    - Allow users to forward messages to other conversations
  9. Performance Optimization:
    - Implement lazy loading for older messages
    - Add pagination for large conversation histories
  10. Offline Support:
    - Add capability to queue messages when offline
    - Sync when connection is restored

  Getting Started (For Developers)

  Prerequisites

  - Kivicare Clinic Management System plugin installed
  - WordPress environment with PHP 7.4+
  - Proper database setup with required privileges

  Development Workflow

  1. Understanding the Architecture:
    - Review the database schema in create_chat_module_tables.php
    - Understand the model relationships (Conversation > Members > Messages >
   Notifications)
    - Get familiar with the permission system in KCChatController.php
  2. Making UI Changes:
    - Modify ChatDashboard.vue for the main chat interface
    - Update Header.vue for notification-related changes
    - Use Tailwind CSS for styling consistency
  3. Adding New Features:
    - Add new methods to appropriate model classes
    - Create API endpoints in KCChatController.php
    - Implement UI in Vue components
    - Update router if adding new pages/views
  4. Testing:
    - Test with different user roles (admin, doctor, patient, etc.)
    - Verify permissions and access control
    - Test file uploads and attachments
    - Test notification system
    - Verify mobile responsiveness

  Troubleshooting Common Issues

  1. Notification Issues:
    - Check browser permissions for notifications
    - Verify polling mechanism in Header.vue and ChatDashboard.vue
  2. File Upload Problems:
    - Check file size limitations (default max: 5MB)
    - Verify WordPress upload directory permissions
    - Check supported file types
  3. Message Delivery Problems:
    - Verify user memberships in conversations
    - Check notification entries in the database
    - Ensure polling is working correctly
  4. Performance Issues:
    - Monitor message loading performance with large histories
    - Consider implementing pagination if needed

  Conclusion

  The Chat Module provides a robust communication system integrated
  seamlessly into the Kivicare Clinic Management System. It follows best
  practices for security, performance, and user experience while providing a
  modern interface for all users. The architecture is flexible and
  extensible, allowing for future enhancements as needed.

  With proper attention to the pending tasks, the module can be further
  improved to provide an even better user experience with real-time
  capabilities and additional messaging features.