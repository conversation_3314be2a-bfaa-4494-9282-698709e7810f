<?php

use App\models\KCAppointment;

$url = $popup ? (string)wp_get_referer() : false;
$paymentComplete = true;
$appointment_id = !empty($_GET['confirm_page']) ? sanitize_text_field(wp_unslash($_GET['confirm_page'])) : 'off';

if (kcWoocommercePaymentGatewayEnable() === 'on' && $appointment_id !== 'off') {
    $appointment_id = (int)$appointment_id;
    $order_id = kcAppointmentIsWoocommerceOrder($appointment_id);
    if (!empty($order_id)) {
        $order = wc_get_order($order_id);
        $paymentComplete = false;
        if ($order->get_status() === 'completed') {
            $paymentComplete = true;
        }
    }
}

// Get the appointment ID, for example from the URL query parameter.
// Adjust the retrieval method as needed.
$appointment_id = isset($_GET['appointment_id']) ? intval($_GET['appointment_id']) : 0;

// Retrieve appointment details from Kivicare.
// Replace the following function with your actual method to get appointment details.
$appointments_table = $this->db->prefix . 'kc_appointments';
$users_table   = $this->db->base_prefix . 'users';
$clinics_table = $this->db->prefix . 'kc_clinics';

global $wpdb;
$query = " SELECT DISTINCT {$appointments_table}.*,  
doctors.display_name  AS doctor_name,
patients.display_name AS patient_name,
CONCAT({$clinics_table}.address, ', ', {$clinics_table}.city,', '
    ,{$clinics_table}.postal_code,', ',{$clinics_table}.country) AS clinic_full_address,
{$clinics_table}.name AS clinic_name,
{$clinics_table}.extra AS clinic_extra
FROM  {$appointments_table}
LEFT JOIN {$users_table} doctors
       ON {$appointments_table}.doctor_id = doctors.id
LEFT JOIN {$users_table} patients
       ON {$appointments_table}.patient_id = patients.id
LEFT JOIN {$clinics_table}
       ON {$appointments_table}.clinic_id = {$clinics_table}.id
WHERE 0 = 0 AND {$appointments_table}.id LIKE $appointment_id  ";
$appointment = $wpdb->get_row($query);


// Check if appointment details are returned; otherwise, provide fallback values.
if (! $appointment) {
    $appointment = array(
        'clinic_name'       => esc_html__('Unknown Clinic', 'kc-lang'),
        'clinic_address'    => esc_html__('No Address Found', 'kc-lang'),
        'appointment_date'  => esc_html__('N/A', 'kc-lang'),
        'appointment_time'  => esc_html__('N/A', 'kc-lang'),
        'doctor_name'       => esc_html__('Unknown Doctor', 'kc-lang'),
        'service_name'      => esc_html__('Unknown Service', 'kc-lang'),
        'price'             => esc_html__('N/A', 'kc-lang')
    );
} else {
    // $appointment->service;

    $appointments_service_table = $this->db->prefix . 'kc_appointment_service_mapping';
    $service_table = $this->db->prefix . 'kc_services';
    $get_service_query = "SELECT {$appointments_table}.id,{$service_table}.name AS service_name,{$service_table}.id AS service_id,{$service_table}.price AS charges FROM {$appointments_table}
    LEFT JOIN {$appointments_service_table} ON {$appointments_table}.id = {$appointments_service_table}.appointment_id JOIN {$service_table}
    ON {$appointments_service_table}.service_id = {$service_table}.id
    WHERE {$service_table}.clinic_id={$appointment->clinic_id}
     AND {$service_table}.doctor_id={$appointment->doctor_id} AND {$appointments_service_table}.appointment_id = {$appointment->id}";
    $services = $this->db->get_results($get_service_query, OBJECT);
    $service_array = $service_list = [];
    $service_charges = 0;

    foreach ($services as $service) {
        $service_array[] = $service->service_name;
        $service_list[] = [
            'service_id' => $service->service_id,
            'name' => $service->service_name,
            'charges' => round((float)$service->price, 3)
        ];
        $service_charges += $service->price;
    }

    if(isKiviCareProActive()){
        $tax = apply_filters('kivicare_calculate_tax', [
            'status' => false,
            'message' => '',
            'data' => []
        ], [
            "id" => $appointment->id,
            "type" => 'appointment',
        ]);

        if(!empty($tax['data']) && is_array($tax['data'])){
            $service_charges += $tax['tax_total'];
            $appointment->tax = $tax['data'];
        }
        $service_charges = round($service_charges, 3);
    }

    $currency_detail = kcGetClinicCurrenyPrefixAndPostfix();
    $currency_prefix = !empty($currency_detail['prefix']) ? $currency_detail['prefix'] : '' ;
    $currency_postfix = !empty($currency_detail['postfix']) ? $currency_detail['postfix'] : '' ;
    $appointment = array(
        'clinic_name'       => $appointment->clinic_name,
        'clinic_address'    => $appointment->clinic_full_address,
        'appointment_date'  => kcGetFormatedDate($appointment->appointment_start_date),
        'appointment_time'  => kcGetFormatedTime(date('h:i A', strtotime($appointment->appointment_start_time))),
        'doctor_name'       => $appointment->doctor_name,
        'service_name'      =>  implode(", ", $service_array),
        'price'             =>  $currency_prefix  . $service_charges .$currency_postfix,
    );
}
?>

<div class="max-w-2xl mx-auto">
    <!-- Header Section -->
    <div class="text-center mb-8 transform animate-fade-in">
        <div class="inline-flex items-center justify-center w-20 h-20 mb-6 rounded-full bg-green-100">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big w-12 h-12 text-green-500">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <path d="m9 11 3 3L22 4"></path>
            </svg>
        </div>
        <h1 class="text-xl md:text-3xl font-bold text-gray-900 mb-2">
            <?php echo esc_html__('Appointment Confirmed!', 'kc-lang'); ?>
        </h1>
        <p class="text-gray-600">
            <?php echo esc_html__('An email confirmation has been sent to your inbox', 'kc-lang'); ?>
        </p>
    </div>

    <!-- Appointment Details Card -->
    <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 text-left">
        <div class="grid md:grid-cols-2 gap-6">
            <!-- Left Column: Clinic & Doctor -->
            <div class="space-y-6">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">
                        <?php echo esc_html__('Clinic Details', 'kc-lang'); ?>
                    </h2>
                    <div class="flex items-start space-x-3 text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin w-5 h-5 mt-1 flex-shrink-0">
                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                        <div>
                            <p class="font-medium">
                                <?php echo esc_html($appointment['clinic_name']); ?>
                            </p>
                            <p class="text-sm">
                                <?php echo esc_html($appointment['clinic_address']); ?>
                            </p>
                        </div>
                    </div>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">
                        <?php echo esc_html__('Doctor', 'kc-lang'); ?>
                    </h2>
                    <p class="text-gray-600">
                        <?php echo esc_html($appointment['doctor_name']); ?>
                    </p>
                </div>
            </div>
            <!-- Right Column: Date/Time & Service -->
            <div class="space-y-6">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">
                        <?php echo esc_html__('Date &amp; Time', 'kc-lang'); ?>
                    </h2>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-5 h-5">
                                <path d="M8 2v4"></path>
                                <path d="M16 2v4"></path>
                                <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                <path d="M3 10h18"></path>
                            </svg>
                            <span>
                                <?php echo esc_html($appointment['appointment_date']); ?>
                            </span>
                        </div>
                        <div class="flex items-center space-x-3 text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-5 h-5">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            <span>
                                <?php echo esc_html($appointment['appointment_time']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">
                        <?php echo esc_html__('Service', 'kc-lang'); ?>
                    </h2>
                    <div class="flex justify-between items-center text-gray-600">
                        <span>
                            <?php echo esc_html($appointment['service_name']); ?>
                        </span>
                        <span class="font-medium">
                            <?php echo esc_html($appointment['price']); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row justify-center gap-4">
        <?php
        if(kcGetSingleWidgetSetting('widget_print')){
            ?>
            <button type="button" id='kivicare_print_detail'
                    class="iq-button iq-button-secondary inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-printer w-5 h-5 mr-2">
                        <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                        <path d="M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6"></path>
                        <rect x="6" y="14" width="12" height="8" rx="1"></rect>
                    </svg>
                    <?php echo esc_html__('Print Detail', 'kc-lang'); ?>
            </button>
            <?php
        }?>
        <!-- <button class="inline-flex items-center justify-center px-6 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-5 h-5 mr-2">
                <path d="M8 2v4"></path>
                <path d="M16 2v4"></path>
                <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                <path d="M3 10h18"></path>
            </svg>
            <?php echo esc_html__('Add to Calendar', 'kc-lang'); ?>
            
        </button> -->
        <add-to-calendar-button
            name="Title"
            options="'Apple','Google'"
            location="World Wide Web"
            startDate="2024-03-15"
            endDate="2024-03-15"
            startTime="10:15"
            endTime="23:30"
            timeZone="America/Los_Angeles"
        ></add-to-calendar-button>
    </div>

    <!-- Important Information Alert -->
    <div role="alert" class="relative w-full rounded-lg border px-4 py-3 text-sm bg-blue-50 border-blue-200 mt-6">
        <h5 class="mb-1 font-medium leading-none tracking-tight text-blue-800">
            <?php echo esc_html__('Important Information', 'kc-lang'); ?>
        </h5>
        <div class="text-sm text-blue-700">
            <?php echo esc_html__("Please arrive 10 minutes before your appointment time. Don't forget to bring any relevant medical records or test results.", 'kc-lang'); ?>
        </div>
    </div>
</div>