/**
 * Book a pending appointment after login/registration
 *
 * @param {Object} appointmentData - The appointment data from localStorage
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
export async function bookPendingAppointment(appointmentData) {
  try {
    const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
    let nonce = '';

    // Try to get nonce from different possible sources
    if (window.ajaxData && window.ajaxData.post_nonce) {
      nonce = window.ajaxData.post_nonce;
    } else if (window.ajaxData && window.ajaxData.nonce) {
      nonce = window.ajaxData.nonce;
    } else if (window.request_data && window.request_data.nonce) {
      nonce = window.request_data.nonce;
    }

    // Format time to AM/PM format if needed
    let formattedTime = appointmentData.time || '';
    if (formattedTime && !formattedTime.toLowerCase().includes('am') && !formattedTime.toLowerCase().includes('pm')) {
      const timeParts = formattedTime.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = timeParts[1] || '00';
      const ampm = hours >= 12 ? 'pm' : 'am';
      const formattedHours = hours % 12 || 12;
      formattedTime = `${formattedHours}:${minutes} ${ampm}`;
    }

    // Prepare the data for the API call
    const params = {
      action: 'ajax_post',
      route_name: appointmentData.route_name || 'appointment_confirm_page',
      clinic_id: appointmentData.clinic_id,
      doctor_id: appointmentData.doctor_id,
      service_list: appointmentData.service_list,
      time: formattedTime,
      date: appointmentData.date || '',
      description: appointmentData.description || '',
      file: [],
      custom_field: appointmentData.custom_field || {},
      _ajax_nonce: nonce
    };

    console.log('Booking pending appointment with params:', params);

    // Make the API request
    const response = await fetch(ajaxurl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(prepareFormData(params))
    });

    const responseData = await response.json();

    if (responseData.status) {
      console.log('Appointment booked successfully:', responseData);
      return true;
    } else {
      console.error('Error in API response:', responseData);
      return false;
    }
  } catch (error) {
    console.error('Error booking pending appointment:', error);
    return false;
  }
}

/**
 * Helper method to prepare form data for submission
 *
 * @param {Object} params - The parameters to prepare
 * @returns {Object} - The prepared form data
 */
function prepareFormData(params) {
  const formData = {};

  // Handle simple key-value pairs
  Object.keys(params).forEach(key => {
    if (key !== 'service_list' && key !== 'custom_field' && key !== 'file' && key !== 'visit_type') {
      formData[key] = params[key];
    }
  });

  // Handle service_list array
  if (params.service_list && Array.isArray(params.service_list)) {
    params.service_list.forEach((service, index) => {
      formData[`service_list[${index}]`] = service;
    });
  }

  // Handle visit_type array
  if (params.visit_type && Array.isArray(params.visit_type)) {
    params.visit_type.forEach((service, index) => {
      formData[`visit_type[${index}]`] = service;
    });
  }

  // Handle custom_field object
  if (params.custom_field) {
    Object.keys(params.custom_field).forEach(key => {
      formData[`custom_field[${key}]`] = params.custom_field[key];
    });
  }

  return formData;
}