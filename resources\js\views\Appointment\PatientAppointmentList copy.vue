<!-- Template Section -->
<template>
  <div class="p-6 max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">
        {{ formTranslation?.common?.appointments || "Appointments" }}
      </h1>
      <button
        v-if="kcCheckPermission('appointment_create')"
        @click="navigateToBooking"
        class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="w-4 h-4 mr-2"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M5 12h14" />
          <path d="M12 5v14" />
        </svg>
        {{ formTranslation?.appointments?.book_new || "Book New Appointment" }}
      </button>
    </div>

    <!-- AI Assistant Section -->
    <div
      role="alert"
      class="relative w-full rounded-lg border px-4 py-3 text-sm [&amp;>svg+div]:translate-y-[-3px] [&amp;>svg]:absolute [&amp;>svg]:left-4 [&amp;>svg]:top-4 [&amp;>svg]:text-foreground [&amp;>svg~*]:pl-7 bg-background text-foreground mb-6 bg-purple-50 border-purple-100"
    >
      <div class="absolute -top-3 right-6">
        <span
          class="bg-purple-100 text-purple-600 text-xs font-medium px-5 py-1 rounded-full"
        >
          Coming Soon
        </span>
      </div>
      <div class="flex items-start space-x-4">
        <div
          class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-message-square w-4 h-4 text-purple-600"
          >
            <path
              d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
            ></path>
          </svg>
        </div>
        <div>
          <h3 class="font-semibold text-purple-900">AI Scheduling Assistant</h3>
          <div class="text-sm [&amp;_p]:leading-relaxed mt-1 text-purple-800">
            Based on your health history and previous appointments, I recommend
            scheduling a follow-up with Dr. Smith in 3 weeks. Would you like me
            to suggest available time slots?
          </div>
          <div class="mt-3 flex space-x-4">
            <button
              class="text-sm px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              Show Available Slots</button
            ><button
              class="text-sm px-3 py-1 text-purple-600 hover:bg-purple-100 rounded-md"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Appointment Types Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div
        @click="navigateToBooking"
        class="bg-white text-card-foreground rounded-xl border shadow hover:bg-gray-50 cursor-pointer"
      >
        <div class="p-4 flex items-center">
          <div
            class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-map-pin w-5 h-5 text-purple-600"
            >
              <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
          <div class="flex-1">
            <h3 class="font-semibold">In-Person Visit</h3>
            <p class="text-sm text-gray-600">Book Now</p>
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-arrow-right w-5 h-5 text-gray-400"
          >
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </div>
      </div>
      <div
        @click="navigateToBooking"
        class="bg-white text-card-foreground rounded-xl border shadow hover:bg-gray-50 cursor-pointer"
      >
        <div class="p-4 flex items-center">
          <div
            class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-video w-5 h-5 text-purple-600"
            >
              <path
                d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"
              ></path>
              <rect x="2" y="6" width="14" height="12" rx="2"></rect>
            </svg>
          </div>
          <div class="flex-1">
            <h3 class="font-semibold">Video Consultation</h3>
            <p class="text-sm text-gray-600">Book Now</p>
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-arrow-right w-5 h-5 text-gray-400"
          >
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </div>
      </div>
      <div
        @click="navigateToBooking"
        class="bg-white text-card-foreground rounded-xl border shadow hover:bg-gray-50 cursor-pointer"
      >
        <div class="p-4 flex items-center">
          <div
            class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-message-square w-5 h-5 text-purple-600"
            >
              <path
                d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
              ></path>
            </svg>
          </div>
          <div class="flex-1">
            <h3 class="font-semibold">Chat Consultation</h3>
            <p class="text-sm text-gray-600">Book Now</p>
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-arrow-right w-5 h-5 text-gray-400"
          >
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
      <div class="flex space-x-2">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="switchTab(tab.id)"
          :class="[
            'px-4 py-2 rounded-lg text-sm',
            currentTab === tab.id
              ? 'bg-purple-600 text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
          ]"
        >
          {{ tab.label }}
        </button>
      </div>

      <div class="flex items-center space-x-4">
        <div class="relative">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4 absolute left-3 top-3 text-gray-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.3-4.3" />
          </svg>
          <input
            v-model="searchQuery"
            :placeholder="
              formTranslation?.appointments?.search || 'Search appointments...'
            "
            class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            type="text"
          />
        </div>

        <button
          @click="filterOpenClose"
          class="flex items-center px-3 py-2 border rounded-lg hover:bg-gray-50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
          </svg>
          {{ formTranslation?.common?.filter || "Filter" }}
        </button>
      </div>
    </div>

    <!-- Appointments List -->
    <div class="space-y-4">
      <TransitionGroup name="list" tag="div" class="space-y-4">
        <div
          v-for="appointment in filteredAppointments"
          :key="appointment.id"
          class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden"
        >
          <div class="flex flex-col md:flex-row">
            <div class="p-4 md:w-64 bg-purple-50">
              <div class="flex items-center space-x-2 text-purple-600 mb-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-4 h-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect width="18" height="18" x="3" y="4" rx="2" />
                  <path d="M16 2v4" />
                  <path d="M8 2v4" />
                  <path d="M3 10h18" />
                </svg>
                <span class="font-semibold">{{
                  formatDate(appointment.appointment_start_date)
                }}</span>
              </div>
              <div class="flex items-center space-x-2 text-gray-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-4 h-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <circle cx="12" cy="12" r="10" />
                  <polyline points="12 6 12 12 16 14" />
                </svg>
                <span
                  >{{ appointment.appointment_start_time }} -
                  {{ appointment.appointment_end_time }}</span
                >
              </div>
            </div>

            <div class="flex-1 p-4">
              <div
                class="flex flex-col md:flex-row md:items-center md:justify-between"
              >
                <div>
                  <h3 class="font-semibold text-lg mb-1">
                    {{ appointment.doctor_name }}
                  </h3>
                  <p class="text-gray-600">{{ appointment.clinic_name }}</p>
                  <p class="text-sm text-gray-500 mt-1">
                    {{ appointment.clinic_full_address }}
                  </p>
                </div>

                <div
                  class="mt-4 md:mt-0 flex flex-col md:flex-row items-start md:items-center space-y-2 md:space-y-0 md:space-x-4"
                >
                  <span
                    :class="getStatusClasses(appointment.status)"
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm"
                  >
                    {{ getStatusText(appointment.status) }}
                  </span>

                  <div class="flex space-x-2">
                    <!-- Video Consultation -->
                    <template
                      v-if="
                        appointment.video_consultation &&
                        appointment.status === '4'
                      "
                    >
                      <button
                        v-if="isDoctor || isAdmin"
                        @click="startVideoCall(appointment)"
                        class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                      >
                        Start Call
                      </button>
                      <button
                        v-if="isPatient"
                        @click="joinVideoCall(appointment)"
                        class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                      >
                        Join Call
                      </button>
                    </template>

                    <!-- Action Buttons -->
                    <button
                      v-if="canCheckIn(appointment)"
                      @click="handleAppointmentStatus(appointment, '4')"
                      class="px-4 py-2 border border-purple-300 text-purple-600 rounded-lg hover:bg-purple-50"
                    >
                      Check In
                    </button>
                    <button
                      v-if="canCheckOut(appointment)"
                      @click="handleAppointmentStatus(appointment, '3')"
                      class="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50"
                    >
                      Check Out
                    </button>
                    <button
                      v-if="canCancel(appointment)"
                      @click="handleAppointmentDelete(appointment)"
                      class="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </TransitionGroup>
    </div>

    <!-- Pre-Appointment Check-in Card -->
    <div v-if="showCheckInCard" class="bg-white rounded-xl border shadow mt-6">
      <div class="flex flex-col space-y-1.5 p-6">
        <h3 class="font-semibold leading-none tracking-tight">
          Pre-Appointment Check-in
        </h3>
      </div>
      <div class="p-6 pt-0">
        <div class="space-y-4">
          <p class="text-gray-600">
            Save time by completing your pre-appointment check-in online. Update
            your information and fill out necessary forms before your visit.
          </p>
          <button
            @click="startCheckIn"
            class="px-4 py-2 text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100"
          >
            Start Check-in Process
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// Script Section
import { defineComponent } from "vue";
import moment from "moment";
import { post, get } from "../../config/request";

export default defineComponent({
  name: "PatientAppointmentList",

  components: {},

  props: {
    patient_profile_id: {
      type: [Number, String],
      default: "",
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      isInitialLoading: true,
      isAppointmentLoading: true,
      filterOpen: false,
      searchQuery: "",
      currentTab: "upcoming",
      appointmentFormObj: {},
      isAddAppointmentModalOpen: false,
      selectedAppointmentType: null,
      errorMessage: "",
      currentPage: 1,
      perPage: 10,
      showCheckInCard: true,
      clinicSessionNotice: {
        status: false,
        msg: "Based on your health history and previous appointments, I recommend scheduling a follow-up with Dr. Smith in 3 weeks. Would you like me to suggest available time slots?",
      },
      doctors: [],
      patients: [],
      appointmentTypes: [
        {
          title: "In-Person Visit",
          subtitle: "Book Now",
          icon: "MapPin",
          type: "in_person",
        },
        {
          title: "Video Consultation",
          subtitle: "Book Now",
          icon: "Video",
          type: "video",
        },
        {
          title: "Chat Consultation",
          subtitle: "Book Now",
          icon: "MessageSquare",
          type: "chat",
        },
      ],
      tabs: [
        { id: "upcoming", label: "Upcoming", count: 0 },
        { id: "past", label: "Past", count: 0 },
        { id: "cancelled", label: "Cancelled", count: 0 },
      ],
      filterData: {
        date: new Date(),
        patient_id: "",
        status: "1",
        clinic_id: "",
        doctor_id: "",
      },
    };
  },

  computed: {
    appointmentList() {
      return this.$store.state.appointmentModule.appointmentList || [];
    },

    filteredAppointments() {
      let appointments = [...this.appointmentList];

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        appointments = appointments.filter(
          (apt) =>
            apt.doctor_name?.toLowerCase().includes(query) ||
            apt.patient_name?.toLowerCase().includes(query) ||
            apt.clinic_name?.toLowerCase().includes(query)
        );
      }

      // Apply tab filters
      switch (this.currentTab) {
        case "upcoming":
          return appointments.filter(
            (apt) =>
              ["1", "2"].includes(apt.status) &&
              moment(apt.appointment_start_date).isSameOrAfter(moment(), "day")
          );
        case "past":
          return appointments.filter(
            (apt) =>
              apt.status === "3" ||
              moment(apt.appointment_start_date).isBefore(moment(), "day")
          );
        case "cancelled":
          return appointments.filter((apt) => apt.status === "0");
        default:
          return appointments;
      }
    },

    isDoctor() {
      return this.getUserRole() === "doctor";
    },

    isAdmin() {
      return ["administrator", "clinic_admin"].includes(this.getUserRole());
    },

    isPatient() {
      return this.getUserRole() === "patient";
    },

    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
  },

  watch: {
    isLoading: {
      handler(value) {
        if (value) {
          this.isAppointmentLoading = true;
          this.refreshAppointment();
        } else {
          this.isAppointmentLoading = false;
        }
      },
      immediate: true,
    },

    currentPage() {
      this.loadAppointments();
    },

    "filterData.date"() {
      this.loadAppointments();
    },

    appointmentList: {
      handler(newList) {
        this.updateTabCounts();
        this.$emit("csvData", newList);
      },
      deep: true,
    },
  },

  async mounted() {
    try {
      await this.init();
    } catch (error) {
      console.error("Initialization error:", error);
      this.showError(
        this.formTranslation?.common?.initialization_error ||
          "Failed to initialize"
      );
    } finally {
      this.isInitialLoading = false;
    }
  },

  methods: {
    async init() {
      await Promise.all([
        this.checkIfClinicHaveSession(),
        this.loadAppointments(),
      ]);

      if (!this.isPatient) {
        await this.getClinicPatients("");
      }

      if (!this.isDoctor) {
        await this.doctorListDropDown("");
      }
    },

    showError(message) {
      this.errorMessage = message;
      setTimeout(() => {
        this.errorMessage = "";
      }, 5000);
    },

    async loadAppointments() {
      this.isAppointmentLoading = true;
      try {
        const filterData = {
          ...this.filterData,
          pagination: this.currentPage,
          date: moment(this.filterData.date).format("YYYY-MM-DD"),
        };

        await this.$store.dispatch("appointmentModule/fetchAppointmentData", {
          filterData,
        });
        this.updateTabCounts();
      } catch (error) {
        console.error("Error loading appointments:", error);
        this.showError(
          this.formTranslation?.common?.loading_error ||
            "Failed to load appointments"
        );
      } finally {
        this.isAppointmentLoading = false;
      }
    },

    updateTabCounts() {
      const counts = {
        upcoming: this.appointmentList.filter(
          (apt) =>
            ["1", "2"].includes(apt.status) &&
            moment(apt.appointment_start_date).isSameOrAfter(moment(), "day")
        ).length,
        past: this.appointmentList.filter(
          (apt) =>
            apt.status === "3" ||
            moment(apt.appointment_start_date).isBefore(moment(), "day")
        ).length,
        cancelled: this.appointmentList.filter((apt) => apt.status === "0")
          .length,
      };

      this.tabs = this.tabs.map((tab) => ({
        ...tab,
        count: counts[tab.id] || 0,
      }));
    },

    switchTab(tabId) {
      this.currentTab = tabId;
    },

    navigateToBooking() {
      window.location.href = "/appointment";
    },

    handleAppointmentTypeSelect(type) {
      this.selectedAppointmentType = type;
      this.handleAppointmentForm();
    },

    handleAppointmentForm(appointment = {}) {
      this.appointmentFormObj = appointment;
      this.isAddAppointmentModalOpen = true;
    },

    closeAppointmentForm() {
      this.isAddAppointmentModalOpen = false;
      this.selectedAppointmentType = null;
      this.appointmentFormObj = {};
    },

    async handleAppointmentSave() {
      this.closeAppointmentForm();
      await this.loadAppointments();
    },

    getStatusText(status) {
      const statusMap = {
        1: "Booked",
        0: "Cancelled",
        2: "Pending",
        4: "Checked In",
        3: "Checked Out",
      };
      return statusMap[status] || "Unknown";
    },

    getStatusClasses(status) {
      const statusMap = {
        1: "bg-green-100 text-green-800",
        0: "bg-red-100 text-red-800",
        2: "bg-yellow-100 text-yellow-800",
        4: "bg-blue-100 text-blue-800",
        3: "bg-gray-100 text-gray-800",
      };
      return statusMap[status] || "bg-gray-100 text-gray-600";
    },

    canCheckIn(appointment) {
      return (
        this.kcCheckPermission("patient_appointment_status_change") &&
        !["3", "4", "0", "2"].includes(appointment.status) &&
        this.currentDate === appointment.appointment_end_date
      );
    },

    canCheckOut(appointment) {
      return (
        this.kcCheckPermission("patient_appointment_status_change") &&
        appointment.status === "4" &&
        this.currentDate === appointment.appointment_end_date
      );
    },

    canCancel(appointment) {
      return (
        this.kcCheckPermission("appointment_delete") &&
        appointment.status !== "0" &&
        this.currentTab === "upcoming"
      );
    },

    startVideoCall(appointment) {
      window.open(appointment.zoom_data?.start_url, "_blank");
    },

    joinVideoCall(appointment) {
      window.open(appointment.zoom_data?.join_url, "_blank");
    },

    async handleAppointmentStatus(appointment, status) {
      if (
        status === "3" &&
        appointment.encounter_id !== null &&
        appointment.encounter_detail?.status === "1"
      ) {
        this.showError("Consultation not closed");
        return;
      }

      try {
        const response = await get("appointment_update_status", {
          appointment_id: appointment.id,
          appointment_status: status,
        });

        if (response.data?.status) {
          await this.loadAppointments();
          this.$store.dispatch("showMessage", {
            message: response.data.message,
            type: "success",
          });
        } else {
          throw new Error(response.data?.message);
        }
      } catch (error) {
        console.error("Status update error:", error);
        this.showError(error.message || "Failed to update status");
      }
    },

    async handleAppointmentDelete(appointment) {
      if (!appointment.id) return;

      try {
        const response = await get("appointment_delete", {
          id: appointment.id,
        });

        if (response.data?.status) {
          await this.loadAppointments();
          this.$emit("refreshDashboard");
          this.$store.dispatch("showMessage", {
            message: response.data.message,
            type: "success",
          });
        } else {
          throw new Error(response.data?.message);
        }
      } catch (error) {
        console.error("Delete error:", error);
        this.showError(error.message || "Failed to delete appointment");
      }
    },

    async checkIfClinicHaveSession() {
      try {
        const response = await get("clinic-sessions/check");
        this.clinicSessionNotice.status = response.data?.status || false;
        if (response.data?.message) {
          this.clinicSessionNotice.msg = response.data.message;
        }
      } catch (error) {
        console.error("Error checking clinic session:", error);
      }
    },

    dismissClinicNotice() {
      this.clinicSessionNotice.status = false;
    },

    filterOpenClose() {
      this.filterOpen = !this.filterOpen;
    },

    getUserRole() {
      return this.$store.state.userDataModule?.user?.role || "";
    },

    async getClinicPatients(clinic_id) {
      try {
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: clinic_id,
        });

        if (response.data?.status) {
          this.patients = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinic patients:", error);
      }
    },

    async doctorListDropDown(clinic_id) {
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          clinic_id: clinic_id,
          module_type: "appointment_filter",
        });

        if (response.data?.status) {
          this.doctors = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching doctors:", error);
      }
    },

    formatDate(date) {
      return moment(date).format("MMMM D, YYYY");
    },

    startCheckIn() {
      // Implement pre-appointment check-in logic
      console.log("Starting pre-appointment check-in process");
    },
  },
});
</script>

<style scoped>
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* Add responsive styles */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons > * {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}
</style>
