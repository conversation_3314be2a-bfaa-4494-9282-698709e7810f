<template>
  <div class="border rounded p-2">
    <div class="flex justify-between items-center">
      <div class="flex items-center gap-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="w-4 h-4 text-gray-400"
        >
          <path
            d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"
          ></path>
          <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
          <path d="M10 9H8"></path>
          <path d="M16 13H8"></path>
          <path d="M16 17H8"></path>
        </svg>
        <div>
          <span class="text-sm">{{ document.name }}</span>
          <div class="flex items-center gap-2">
            <span class="text-xs text-gray-500">{{
              formatDate(document.created_at)
            }}</span>
            <span
              v-if="document.type"
              :class="[
                'text-xs px-2 py-0.5 rounded',
                document.type === 'identity_document'
                  ? 'bg-yellow-100 text-yellow-600'
                  : 'bg-green-100 text-green-600',
              ]"
            >
              {{ formatDocumentType(document.type) }}
            </span>
          </div>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <document-viewer :document="document"/>
        <!-- <a
          title="Share"
          class="p-1 text-gray-400 hover:text-gray-600"
          :href="document.document_url"
          target="_blank"
          rel="noopener noreferrer"
        >
          <i class="fas fa-eye"></i>
        </a> -->
        <!-- hiding this button the encounter generated template - allow to edit -->
        <!-- <button
          v-if="document.type == 'encounter_document'"
          title="Edit"
          class="p-1 text-gray-400 hover:text-gray-600"
          @click="$emit('edit', document)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4"
          >
            <path d="M12 20h9"></path>
            <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"></path>
          </svg>
        </button> -->
        <button
          title="Share"
          class="p-1 text-gray-400 hover:text-gray-600"
          @click="$emit('share', document)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4"
          >
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line>
            <line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line>
          </svg>
        </button>
        <button
          title="Download"
          class="p-1 text-gray-400 hover:text-gray-600"
          @click="$emit('download', document)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
        </button>
        <button
          title="Delete"
          class="p-1 text-gray-400 hover:text-red-500"
          @click="$emit('delete', document)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4"
          >
            <path d="M3 6h18"></path>
            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            <line x1="10" x2="10" y1="11" y2="17"></line>
            <line x1="14" x2="14" y1="11" y2="17"></line>
          </svg>
        </button>
      </div>
    </div>
    
  </div>
</template>

<script>
import moment from "moment";
import { get, post } from "../../config/request";
import DocumentViewer from "../Common/DocumentViewer.vue";

export default {
  name: "DocumentItem",
  
  components: {
    DocumentViewer
  },

  props: {
    document: {
      type: Object,
      required: true,
    }
  },
  
  data() {
    return {
      showPdfViewer: false,
      pdfPassword: "",
      isLoading: false
    };
  },
  
  methods: {
    formatDate(date) {
      return moment(date).format("DD/MM/YYYY");
    },
    
    formatDocumentType(type) {
      if (type.includes("_")) {
        return type
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
      }
      return type.charAt(0).toUpperCase() + type.slice(1);
    },
  }
};
</script>

<style scoped>
.pdf-viewer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pdf-page {
  margin-bottom: 20px;
}
</style>