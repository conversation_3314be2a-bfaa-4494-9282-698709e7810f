$ = jQuery;
function displayMessage(message, duration = 10000) {
    displayToastMessage('success', message);
}

function displayErrorMessage(message, duration = 10000) {
    displayToastMessage('error', message);
}

function displayToastMessage(type, message) {
    // Define toast configurations for different types
    const toastConfigs = {
        success: {
            background: "rgba(16, 185, 129, 0.1)", // Emerald/green with transparency
            icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(16, 185, 129)" stroke-width="2">
                    <path d="M20 6L9 17L4 12"></path>
                   </svg>`,
            iconColor: "rgb(16, 185, 129)",
            borderColor: "rgba(16, 185, 129, 0.2)"
        },
        error: {
            background: "rgba(239, 68, 68, 0.1)", // Red with transparency
            icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(239, 68, 68)" stroke-width="2">
                    <path d="M18 6L6 18M6 6l12 12"></path>
                   </svg>`,
            iconColor: "rgb(239, 68, 68)",
            borderColor: "rgba(239, 68, 68, 0.2)"
        },
        warning: {
            background: "rgba(245, 158, 11, 0.1)", // Amber with transparency
            icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(245, 158, 11)" stroke-width="2">
                    <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                   </svg>`,
            iconColor: "rgb(245, 158, 11)",
            borderColor: "rgba(245, 158, 11, 0.2)"
        },
        info: {
            background: "rgba(59, 130, 246, 0.1)", // Blue with transparency
            icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(59, 130, 246)" stroke-width="2">
                    <path d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                   </svg>`,
            iconColor: "rgb(59, 130, 246)",
            borderColor: "rgba(59, 130, 246, 0.2)"
        }
    };

    // Get config for the specified type, default to 'info' if type not found
    const config = toastConfigs[type.toLowerCase()] || toastConfigs.info;

    // Create wrapper for message with icon
    const messageWithIcon = `
        <div class="flex items-center">
            ${config.icon}
            <span>${message}</span>
        </div>
    `;

    // Add required CSS if not already added
    if (!document.getElementById('toast-custom-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'toast-custom-styles';
        styleSheet.textContent = `
            @keyframes toast-progress {
                from { width: 100%; }
                to { width: 0%; }
            }
            .toast-progress-bar {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 2px;
                background: ${config.iconColor};
                opacity: 0.7;
                animation: toast-progress 3s linear forwards;
            }
        `;
        document.head.appendChild(styleSheet);
    }

    Toastify({
        text: messageWithIcon,
        duration: 3000,
        close: true,
        gravity: "top",
        position: "right",
        stopOnFocus: true,
        className: 'toast-modern',
        style: {
            background: config.background,
            borderRadius: "12px",
            padding: "16px",
            fontSize: "14px",
            fontWeight: "500",
            color: "#1f2937", // Dark gray text
            border: `1px solid ${config.borderColor}`,
            backdropFilter: "blur(8px)",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            minWidth: "300px",
            maxWidth: "400px"
        },
        callback: function(toast) {
            // Add progress bar
            const progressBar = document.createElement('div');
            progressBar.className = 'toast-progress-bar';
            // toast.element.appendChild(progressBar);

            // // Add hover effect
            // toast.element.style.transition = 'transform 0.2s ease';
            // toast.element.addEventListener('mouseenter', () => {
            //     toast.element.style.transform = 'translateY(-2px)';
            // });
            // toast.element.addEventListener('mouseleave', () => {
            //     toast.element.style.transform = 'translateY(0)';
            // });
        },
        escapeMarkup: false,
        onClick: function() {
            this.close();
        }
    }).showToast();
}

function escapeHtml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

function displayAlert(title, message, color = "red") {
  $.alert({
    title: title,
    content: message,
    type: color,
  });
}

function displayTooltip(object = {}) {
  setTimeout(() => {
    let classElement = object.class !== undefined ? object.class : ".guide";
    window.Tipped.create(
      classElement,
      function (element) {
        return {
          content: $(element).data("content"),
        };
      },
      {
        position: object.position !== undefined ? object.position : "right",
        skin: object.skin !== undefined ? object.skin : "light",
        size: object.size !== undefined ? object.size : "large",
      }
    );
  }, 1000);
}

function numberWithCommas(x) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

window.onload = function () {
  $(".fc-toolbar.fc-header-toolbar").addClass("row col-lg-12");
};

function kiviOpenPaymentWindow(url) {
  const parsedUrl = new URL(url);
  let hostname = parsedUrl.hostname;
  hostname = hostname.replace(/^www\./, "");
  const parts = hostname.split(".");
  const domainName = parts.slice(-2).join(".");
  const currentHost = window.location.hostname;

  if (domainName === "paypal.com" || hostname === currentHost) {
    // Domain matches, open the payment window
    return window.open(
      url,
      "_blank",
      "popup=yes,toolbar=0,status=0,width=360,height=500,top=100,left=" +
        (window.screen ? Math.round(screen.width / 2 - 275) : 100)
    );
  } else {
    console.error("Unauthorized domain:", domainName);
    return null;
  }
}

function kivicare_generate_time(n, a = "delimiters", c = "general") {
  var o = n.startDate.split("-"),
    i = n.endDate.split("-");
  let l = "",
    r = "",
    s = !1;
  if (null != n.startTime && null != n.endTime)
    if (null != n.timeZoneOffset && "" != n.timeZoneOffset)
      (l = new Date(
        o[0] +
          "-" +
          o[1] +
          "-" +
          o[2] +
          "T" +
          n.startTime +
          ":00.000" +
          n.timeZoneOffset
      )),
        (r = new Date(
          i[0] +
            "-" +
            i[1] +
            "-" +
            i[2] +
            "T" +
            n.endTime +
            ":00.000" +
            n.timeZoneOffset
        )),
        (l = l.toISOString().replace(".000", "")),
        (r = r.toISOString().replace(".000", "")),
        "clean" == a &&
          ((l = l.replace(/\-/g, "").replace(/\:/g, "")),
          (r = r.replace(/\-/g, "").replace(/\:/g, "")));
    else {
      if (
        ((l = new Date(
          o[0] + "-" + o[1] + "-" + o[2] + "T" + n.startTime + ":00.000+00:00"
        )),
        (r = new Date(
          i[0] + "-" + i[1] + "-" + i[2] + "T" + n.endTime + ":00.000+00:00"
        )),
        null != n.timeZone && "" != n.timeZone)
      ) {
        let e = new Date(l.toLocaleString("en-US", { timeZone: "UTC" })),
          t =
            ("currentBrowser" == n.timeZone &&
              (n.timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone),
            new Date(l.toLocaleString("en-US", { timeZone: n.timeZone })));
        n = e.getTime() - t.getTime();
        l.setTime(l.getTime() + n), r.setTime(r.getTime() + n);
      }
      (l = l.toISOString().replace(".000", "")),
        (r = r.toISOString().replace(".000", "")),
        "clean" == a &&
          ((l = l.replace(/\-/g, "").replace(/\:/g, "")),
          (r = r.replace(/\-/g, "").replace(/\:/g, "")));
    }
  else {
    (s = !0), (l = new Date(Date.UTC(o[0], o[1] - 1, o[2])));
    let e = l.toISOString().replace(/T(.+)Z/g, ""),
      t =
        ((r = new Date(Date.UTC(i[0], i[1] - 1, i[2]))),
        ("google" != c && "microsoft" != c && "ical" != c) ||
          r.setDate(r.getDate() + 1),
        r.toISOString().replace(/T(.+)Z/g, ""));
    "clean" == a && ((e = e.replace(/\-/g, "")), (t = t.replace(/\-/g, ""))),
      (l = e),
      (r = t);
  }
  return { start: l, end: r, allday: s };
}

function kivicare_generate_google(e) {
  try {
    let t = "https://calendar.google.com/calendar/render?action=TEMPLATE";
    var n = kivicare_generate_time(e, "clean", "google");
    if (!n) {
      throw new Error("Failed to generate time for Google calendar.");
    }
    t += "&dates=" + n.start + "%2F" + n.end;
    if (e.name && e.name !== "") {
      t += "&text=" + encodeURIComponent(e.name);
    }
    if (e.location && e.location !== "") {
      t += "&location=" + encodeURIComponent(e.location);
      if (kivicare_isiOS()) {
        e.description = e.description || "";
        e.description += "<br><br>&#128205;: " + e.location;
      }
    }
    if (e.description && e.description !== "") {
      t += "&details=" + encodeURIComponent(e.description);
    }
    window.open(t, "_blank").focus();
    console.log(t);
  } catch (error) {
    console.error("Error generating Google calendar link:", error);
  }
}

function kivicare_generate_yahoo(e) {
  try {
    let t = "https://calendar.yahoo.com/?v=60";
    var n = kivicare_generate_time(e, "clean");
    if (!n) {
      throw new Error("Failed to generate time for Yahoo calendar.");
    }
    t += "&st=" + n.start + "&et=" + n.end;
    if (n.allday) {
      t += "&dur=allday";
    }
    if (e.name && e.name !== "") {
      t += "&title=" + encodeURIComponent(e.name);
    }
    if (e.location && e.location !== "") {
      t += "&in_loc=" + encodeURIComponent(e.location);
    }
    if (e.description && e.description !== "") {
      t += "&desc=" + encodeURIComponent(e.description);
    }
    window.open(t, "_blank").focus();
    console.log(t);
  } catch (error) {
    console.error("Error generating Yahoo calendar link:", error);
  }
}

function kivicare_generate_microsoft(e, t = "365") {
  try {
    let n = "https://";
    n += e.provider === "outlook" ? "outlook.live.com" : "outlook.office.com";
    n +=
      "/calendar/0/deeplink/compose?path=%2Fcalendar%2Faction%2Fcompose&rru=addevent";

    let t = kivicare_generate_time(e, "delimiters", "microsoft");
    if (!t) {
      throw new Error(
        "Failed to generate time for Microsoft Outlook calendar."
      );
    }
    n += "&startdt=" + t.start + "&enddt=" + t.end;
    if (t.allday) {
      n += "&allday=true";
    }
    if (e.name && e.name !== "") {
      n += "&subject=" + encodeURIComponent(e.name);
    }
    if (e.location && e.location !== "") {
      n += "&location=" + encodeURIComponent(e.location);
    }
    if (e.description && e.description !== "") {
      n += "&body=" + encodeURIComponent(e.description.replace(/\n/g, "<br>"));
    }
    window.open(n, "_blank").focus();
    console.log(n);
  } catch (error) {
    console.error("Error generating Microsoft Outlook calendar link:", error);
  }
}

function kivicare_generate_teams(e) {
  let t = "https://teams.microsoft.com/l/meeting/new?";
  var n = kivicare_generate_time(e, "delimiters", "microsoft");
  t += "&startTime=" + n.start + "&endTime=" + n.end;
  let a = "";
  null != e.name &&
    "" != e.name &&
    (t += "&subject=" + encodeURIComponent(e.name)),
    null != e.location &&
      "" != e.location &&
      ((a = encodeURIComponent(e.location)),
      (t += "&location=" + a),
      (a += " // ")),
    null != e.description &&
      "" != e.description &&
      (t += "&content=" + a + encodeURIComponent(e.description)),
    // window.open(t, "_blank").focus();
    console.log(t);
}

function kivicare_generate_ical(t) {
  let e = new Date();
  e = e
    .toISOString()
    .replace(/\..../g, "")
    .replace(/[^a-z0-9]/gi, "");
  var n = kivicare_generate_time(t, "clean", "ical");
  let a = "",
    c =
      (n.allday && (a = ";VALUE=DATE"),
      [
        "BEGIN:VCALENDAR",
        "VERSION:2.0",
        "CALSCALE:GREGORIAN",
        "BEGIN:VEVENT",
        "DTSTAMP:" + n.start,
        "DTSTART" + a + ":" + n.start,
        "DTEND" + a + ":" + n.end,
        "SUMMARY:" + t.name,
      ]);
  null != t.description_iCal &&
    "" != t.description_iCal &&
    c.push("DESCRIPTION:" + t.description_iCal.replace(/\n/g, "\\n")),
    null != t.location && "" != t.location && c.push("LOCATION:" + t.location),
    c.push(
      "STATUS:CONFIRMED",
      "LAST-MODIFIED:" + e,
      "SEQUENCE:0",
      "END:VEVENT",
      "END:VCALENDAR"
    );
  n = "data:text/calendar;charset=utf-8," + encodeURIComponent(c.join("\r\n"));
  try {
    if (!window.ActiveXObject) {
      let e = document.createElement("a");
      (e.href = n),
        (e.target = "_blank"),
        (e.download = t.iCalFileName || "event-to-save-in-my-calendar");
      var o = new MouseEvent("click", {
        view: window,
        bubbles: !0,
        cancelable: !1,
      });
      e.dispatchEvent(o),
        (window.URL || window.webkitURL).revokeObjectURL(e.href);
    }
  } catch (e) {
    console.error(e);
  }
}

function kivicare_isiOS() {
  if (!kivicare_isBrowser()) {
    return false;
  }

  // Directly check the conditions without using new Function
  return (
    (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) ||
    (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1)
  );
}

function kivicare_isBrowser() {
  try {
    return this === window;
  } catch (e) {
    return false;
  }
}

function kivicare_add_to_calendar_url(config, type) {
  switch (type) {
    case "googleCalender":
      kivicare_generate_google(config);
      break;
    case "microSoftOutlookLive":
      kivicare_generate_microsoft(config, "outlook");
      break;
    case "microSoftOutlookoffice":
      kivicare_generate_microsoft(config);
      break;
    case "microSoftTeam":
      kivicare_generate_teams(config);
      break;
    case "yahoo":
      kivicare_generate_yahoo(config);
      break;
    case "apple":
      kivicare_generate_ical(config);
      break;
  }
}
function kivicareCustomImageUploader(
  formTranslation,
  type = "",
  multiple = false,
  extraData = {}
) {
  let options = {
    title: ["report", "csv", "xls", "json", "custom_field"].includes(type)
      ? formTranslation.common.choose_file
      : formTranslation.common.choose_image,
    button: {
      text: ["report", "csv", "xls", "json", "custom_field"].includes(type)
        ? formTranslation.common.choose_file
        : formTranslation.common.choose_image,
    },
    library: {
      type: ["image"],
    },
    multiple: multiple,
  };

  if (type === "report") {
    options.library.type = Object.values(
      kc_custom_request_data.support_mime_type
    );
  } else if (type === "csv") {
    options.library.type = ["text/csv"];
  } else if (type === "xls") {
    options.library.type = [
      "application/vnd.oasis.opendocument.spreadsheet",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-excel.sheet.macroEnabled.12",
      "application/vnd.ms-excel.sheet.binary.macroEnabled.12",
    ];
  } else if (type === "json") {
    options.library.type = ["application/json"];
  } else if (type === "custom_field") {
    delete options.library;
    options.library = {
      type: extraData.mediaType,
    };
  }

  const wp_media_instance = (wp.media.frames.file_frame = wp.media(options));

  if (
    options.library &&
    options.library.type &&
    options.library.type.length > 0
  ) {
    wp_media_instance.on("uploader:ready", function () {
      jQuery('.moxie-shim-html5 input[type="file"]').attr(
        "accept",
        options.library.type.join(",")
      );
    });
  }

  return wp_media_instance;
}
