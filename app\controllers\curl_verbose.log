* Host api.deepgram.com:443 was resolved.
* IPv6: (none)
* IPv4: **************
*   Trying **************:443...
* Connected to api.deepgram.com (**************) port 443
* ALPN: curl offers h2,http/1.1
*  CAfile: /Users/<USER>/Local Sites/medroid-12-feb/app/public/wp-includes/certificates/ca-bundle.crt
*  CApath: none
* SSL connection using TLSv1.3 / AEAD-CHACHA20-POLY1305-SHA256 / [blank] / UNDEF
* ALPN: server accepted h2
* Server certificate:
*  subject: CN=api.deepgram.com
*  start date: Feb 10 13:05:31 2025 GMT
*  expire date: May 11 13:05:30 2025 GMT
*  subjectAltName: host "api.deepgram.com" matched cert's "api.deepgram.com"
*  issuer: C=US; O=Let's Encrypt; CN=R10
*  SSL certificate verify ok.
* using HTTP/2
* [HTTP/2] [1] OPENED stream for https://api.deepgram.com/v1/listen?model=nova-3&smart_format=true&diarize=true&punctuate=true
* [HTTP/2] [1] [:method: POST]
* [HTTP/2] [1] [:scheme: https]
* [HTTP/2] [1] [:authority: api.deepgram.com]
* [HTTP/2] [1] [:path: /v1/listen?model=nova-3&smart_format=true&diarize=true&punctuate=true]
* [HTTP/2] [1] [accept: */*]
* [HTTP/2] [1] [authorization: Token ****************************************]
* [HTTP/2] [1] [content-type: audio/wav]
* [HTTP/2] [1] [content-length: 80030869]
> POST /v1/listen?model=nova-3&smart_format=true&diarize=true&punctuate=true HTTP/2
Host: api.deepgram.com
Accept: */*
Authorization: Token ****************************************
Content-Type: audio/wav
Content-Length: 80030869

* upload completely sent off: 80030869 bytes
< HTTP/2 200 
< content-type: application/json
< vary: origin, access-control-request-method, access-control-request-headers
< vary: accept-encoding
< access-control-allow-credentials: true
< access-control-expose-headers: dg-model-name,dg-model-uuid,dg-char-count,dg-request-id,dg-error
< dg-request-id: 760f7f24-4719-4771-98ec-0257a267a447
< content-length: 553
< date: Fri, 07 Mar 2025 15:29:54 GMT
< 
* Connection #0 to host api.deepgram.com left intact
