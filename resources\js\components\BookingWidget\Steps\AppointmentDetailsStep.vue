<template>
  <div class="kivi-booking-step" id="step-details">
    <h2 class="kivi-step-title">More About Appointment</h2>
    <p class="kivi-step-subtitle">Please provide your medical information</p>

    <!-- Appointment Description Section -->
    <div class="kivi-form-section mb-6 text-left">
      <div class="kivi-form-group">
        <label class="kivi-form-label" for="appointment-description">Appointment Description (reason for your visit today)</label>
        <textarea
          class="kivi-form-input"
          id="appointment-description"
          rows="3"
          v-model="visitReason"
          :class="{ 'error': visitReasonError }"
        ></textarea>
        <div class="kivi-form-error" v-if="visitReasonError">{{ visitReasonError }}</div>
      </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="kivi-form-section mb-6 text-left">
      <div class="kivi-checkbox-container">
        <input
          type="checkbox"
          id="terms-agreement"
          v-model="agreedToTerms"
        />
        <label for="terms-agreement" class="kivi-checkbox-label">
          I agree to the terms and conditions
          <span class="text-red-500">*</span>
        </label>
      </div>
      <div class="kivi-form-error" v-if="errors && errors.terms">
        {{ errors.terms }}
      </div>
    </div>

  </div>
</template>

<script>
// No imports needed

export default {
  name: 'AppointmentDetailsStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      patientData: {
        name: '',
        email: '',
        phone: '',
        notes: ''
      },
      visitReason: '',
      visitReasonError: '',
      customFields: [],
      customFieldValues: {},
      customFieldValuesCheckbox: {},
      customFieldErrors: {},
      agreedToTerms: false,
      errors: {},
      isLoading: false
    };
  },
  created() {
    if (this.bookingData.patient) {
      this.patientData = { ...this.bookingData.patient };
    }

    this.checkLoggedInUser();
    this.loadCustomFields();

    this.$nextTick(() => {
      this.updatePatientDataFromFields();
    });
  },
  watch: {
    visitReason(newValue) {
      this.$emit('update:booking-data', {
        ...this.bookingData,
        description: newValue || '',
        patient: this.patientData
      });
    },

    customFieldValues: {
      deep: true,
      handler() {
        this.updatePatientDataFromFields();
      }
    },

    customFieldValuesCheckbox: {
      deep: true,
      handler() {
        this.updatePatientDataFromFields();
      }
    }
  },
  methods: {
    async checkLoggedInUser() {
      try {
        // Use the ajaxurl and nonce for the WordPress AJAX API
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = window.ajaxData && window.ajaxData.get_nonce ? window.ajaxData.get_nonce : '';

        // Prepare parameters for the API call
        const params = {
          action: 'ajax_get',
          route_name: 'login_user_detail',
          _ajax_nonce: nonce
        };

        // Make the API request
        const response = await fetch(`${ajaxurl}?${new URLSearchParams(params).toString()}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        const responseData = await response.json();

        if (responseData.status && responseData.data) {
          const userData = responseData.data;

          // Prefill the form with user data
          this.patientData.name = userData.display_name || '';
          this.patientData.email = userData.user_email || '';

          // Extract phone from basic_data if available
          if (userData.basic_data) {
            try {
              const basicData = typeof userData.basic_data === 'string'
                ? JSON.parse(userData.basic_data)
                : userData.basic_data;

              if (basicData && basicData.mobile_number) {
                this.patientData.phone = basicData.mobile_number;
              }
            } catch (e) {
              console.warn('Error parsing user basic data:', e);
            }
          } else {
            this.patientData.phone = userData.mobile_number || '';
          }

          // Emit authentication status to parent component
          this.$emit('authenticated', true);

          return true;
        }
      } catch (error) {
        console.error('Error checking login status:', error);
      }

      return false;
    },

    async loadCustomFields() {
      if (!this.bookingData.clinic || !this.bookingData.clinic.id) {
        console.warn('No clinic selected, cannot load custom fields');
        return;
      }

      try {
        this.isLoading = true;
        console.log('Loading custom fields for clinic:', this.bookingData.clinic.id);

        // Use the ajaxurl and nonce for the WordPress AJAX API
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = window.ajaxData && window.ajaxData.get_nonce ? window.ajaxData.get_nonce : '';

        // Prepare parameters for the API call
        const params = {
          action: 'ajax_get',
          route_name: 'get_appointment_custom_field',
          clinic_id: this.bookingData.clinic.id,
          _ajax_nonce: nonce
        };

        // Add doctor_id if available
        if (this.bookingData.services && this.bookingData.services.length > 0 && this.bookingData.services[0].doctor_id) {
          params.doctor_id = this.bookingData.services[0].doctor_id;
        }

        // Make the API request
        const response = await fetch(`${ajaxurl}?${new URLSearchParams(params).toString()}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        const responseData = await response.json();

        if (responseData.status && responseData.data) {
          // If we get HTML, parse it to extract fields
          if (typeof responseData.data === 'string' && responseData.data.includes('<')) {
            this.customFields = this.extractCustomFieldsFromHtml(responseData.data);
          } else if (Array.isArray(responseData.data)) {
            // If we get an array of fields directly
            this.customFields = responseData.data.map(field => ({
              id: field.id,
              label: field.label || field.name,
              type: field.type || 'text',
              required: field.required === true || field.required === 'true',
              options: field.options || []
            }));
          }

          // Initialize checkbox arrays
          this.customFields.forEach(field => {
            if (field.type === 'checkbox') {
              this.customFieldValuesCheckbox[field.id] = [];
            }
          });

          console.log('Loaded custom fields:', this.customFields);
        } else {
          console.warn('No custom fields found or error loading fields');
        }
      } catch (error) {
        console.error('Error loading custom fields:', error);
      } finally {
        this.isLoading = false;
      }
    },

    extractCustomFieldsFromHtml(htmlString) {
      try {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlString;
        const customFields = [];

        // Look for form inputs
        const inputs = tempDiv.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
          const id = input.id || input.name;
          if (!id) return;

          // Find associated label
          const label = tempDiv.querySelector(`label[for="${id}"]`) ||
                        input.closest('label') ||
                        { textContent: id };

          const field = {
            id,
            label: label.textContent.trim(),
            type: input.type || 'text',
            required: input.hasAttribute('required'),
            placeholder: input.getAttribute('placeholder') || ''
          };

          // Handle select elements
          if (input.tagName.toLowerCase() === 'select') {
            field.type = 'select';
            field.options = Array.from(input.options).map(option => ({
              value: option.value,
              label: option.textContent.trim()
            }));
          }

          customFields.push(field);
        });

        return customFields;
      } catch (error) {
        console.error('Error extracting custom fields from HTML:', error);
        return [];
      }
    },

    async submitForm() {
      if (!this.validateForm(true)) {
        return false;
      }

      const updatedPatientData = { ...this.patientData };

      if (this.visitReason?.trim()) {
        updatedPatientData.notes = `Reason for visit: ${this.visitReason.trim()}\n\n${updatedPatientData.notes || ''}`;
      }

      // Prepare custom field data
      const customField = {};

      // Add regular field values
      Object.keys(this.customFieldValues).forEach(key => {
        const value = this.customFieldValues[key];
        if (value !== null && value !== undefined && value !== '') {
          customField[`custom_field_${key}`] = value;
        }
      });

      // Add checkbox field values
      Object.keys(this.customFieldValuesCheckbox).forEach(key => {
        if (this.customFieldValuesCheckbox[key]?.length > 0) {
          customField[`custom_field_${key}`] = this.customFieldValuesCheckbox[key].join(',');
        }
      });

      this.patientData = updatedPatientData;

      this.$emit('update:booking-data', {
        ...this.bookingData,
        patient: updatedPatientData,
        customField: customField,
        description: this.visitReason || ''
      });

      return true;
    },

    // Helper method to prepare form data for submission
    prepareFormData(params) {
      const formData = {};

      // Handle simple key-value pairs
      Object.keys(params).forEach(key => {
        if (key !== 'service_list' && key !== 'custom_field' && key !== 'file' && key !== 'visit_type') {
          formData[key] = params[key];
        }
      });

      // Handle service_list array
      if (params.service_list && Array.isArray(params.service_list)) {
        params.service_list.forEach((service, index) => {
          formData[`service_list[${index}]`] = service;
        });
      }

      // Handle visit_type array
      if (params.visit_type && Array.isArray(params.visit_type)) {
        params.visit_type.forEach((service, index) => {
          formData[`visit_type[${index}]`] = service;
        });
      }

      // Handle custom_field object
      if (params.custom_field) {
        Object.keys(params.custom_field).forEach(key => {
          formData[`custom_field[${key}]`] = params.custom_field[key];
        });
      }

      return formData;
    },

    // The loadConfirmationPage functionality has been moved directly into the submitForm method

    validateForm(showErrors = true) {
      let isValid = true;
      const errors = {
        terms: ''
      };
      const customFieldErrors = {};

      // Update patient data from custom fields
      // This is now handled by a separate method to avoid infinite loops
      this.updatePatientDataFromFields();

      // Validate visit reason
      if (!this.visitReason.trim()) {
        this.visitReasonError = 'Please provide a reason for your visit';
        isValid = false;
      } else {
        this.visitReasonError = '';
      }

      // Validate terms agreement
      if (!this.agreedToTerms) {
        errors.terms = 'You must agree to the terms and conditions';
        isValid = false;
      }

      // Validate required custom fields
      this.customFields.forEach(field => {
        if (field.required) {
          if (field.type === 'checkbox') {
            const values = this.customFieldValuesCheckbox[field.id] || [];
            if (values.length === 0) {
              customFieldErrors[field.id] = `${field.label} is required`;
              isValid = false;
            }
          } else {
            const value = this.customFieldValues[field.id];
            if (!value || (typeof value === 'string' && !value.trim())) {
              customFieldErrors[field.id] = `${field.label} is required`;
              isValid = false;
            } else if (field.type === 'email' && !this.isValidEmail(value)) {
              customFieldErrors[field.id] = 'Please enter a valid email address';
              isValid = false;
            } else if (field.type === 'tel' && !this.isValidPhone(value)) {
              customFieldErrors[field.id] = 'Please enter a valid phone number';
              isValid = false;
            }
          }
        }
      });

      // Update error states if showing errors
      if (showErrors) {
        this.errors = errors;
        this.customFieldErrors = customFieldErrors;
      }

      // Prepare custom fields data for submission
      const customFieldsData = {};

      // Add simple fields
      Object.entries(this.customFieldValues).forEach(([key, value]) => {
        customFieldsData[key] = value;
      });

      // Add checkbox fields
      Object.entries(this.customFieldValuesCheckbox).forEach(([key, values]) => {
        customFieldsData[key] = values.join(', ');
      });

      // Update the validity state in the parent component with custom fields
      this.$emit('update:booking-data', {
        ...this.bookingData,
        patient: {
          ...this.patientData,
          custom_fields: customFieldsData
        },
        description: this.visitReason || ''
      });

      // Log the updated patient data
      console.log('Updated patient data:', this.patientData);

      // Log validation status
      if (!isValid && showErrors) {
        console.log('Form validation failed:', {
          errors,
          customFieldErrors
        });
      }

      return isValid;
    },

    isValidEmail(email) {
      const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(String(email).toLowerCase());
    },

    isValidPhone(phone) {
      // Basic phone validation - can be enhanced based on requirements
      return phone.length >= 10;
    },



    isAuthenticated() {
      return window.ajaxData?.is_user_logged_in === 'yes' ||
             (window.ajaxData?.user_id && this.patientData?.name && this.patientData?.email);
    },

    isFormValid() {
      return this.validateForm(false);
    },

    updatePatientDataFromFields() {
      const nameField = this.customFields.find(f =>
        f.label.toLowerCase().includes('name') || f.id.toLowerCase().includes('name')
      );
      const emailField = this.customFields.find(f =>
        f.type === 'email' || f.label.toLowerCase().includes('email') || f.id.toLowerCase().includes('email')
      );
      const phoneField = this.customFields.find(f =>
        f.type === 'tel' || f.label.toLowerCase().includes('phone') || f.id.toLowerCase().includes('phone')
      );

      const updatedPatientData = {
        ...this.patientData,
        name: nameField ? (this.customFieldValues[nameField.id] || '') : '',
        email: emailField ? (this.customFieldValues[emailField.id] || '') : '',
        phone: phoneField ? (this.customFieldValues[phoneField.id] || '') : ''
      };

      this.patientData = updatedPatientData;

      this.$emit('update:booking-data', {
        ...this.bookingData,
        patient: updatedPatientData,
        description: this.visitReason || ''
      });
    }
  }
};
</script>

<style scoped>
.mb-6 {
  margin-bottom: 1.5rem;
}

.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
  text-align: left;
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
  text-align: left;
}

.kivi-patient-form {
  max-width: 100%;
  margin: 0;
  text-align: left;
}

.kivi-form-group {
  margin-bottom: 1.5rem;
}

.kivi-form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--dark-gray);
  text-align: left;
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-form-input.error {
  border-color: var(--danger-color, #ef4444);
}

.kivi-form-error {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--danger-color, #ef4444);
  text-align: left;
}

.kivi-section-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark-gray);
  text-align: left;
}

.kivi-checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.kivi-checkbox-container input[type="checkbox"] {
  margin-top: 0.25rem;
}

.kivi-checkbox-label {
  font-size: 0.875rem;
  color: var(--gray);
  text-align: left;
}

.kivi-link {
  color: var(--primary-color);
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  text-decoration: underline;
}

.kivi-link:hover {
  color: var(--primary-dark-color);
}



.kivi-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.kivi-btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.kivi-btn-primary:hover {
  background-color: var(--primary-dark-color);
}

.kivi-checkbox-group,
.kivi-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.kivi-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.kivi-modal {
  background-color: white;
  border-radius: var(--radius);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.kivi-modal-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(229, 231, 235, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.kivi-modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.kivi-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--gray);
}

.kivi-modal-body {
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
}

.kivi-modal-footer {
  padding: 1rem;
  border-top: 1px solid rgba(229, 231, 235, 1);
  display: flex;
  justify-content: flex-end;
}

.required {
  color: var(--danger-color, #ef4444);
}

.kivi-navigation-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.kivi-btn-secondary {
  background-color: transparent;
  color: #374151;
  border: none;
}

.kivi-btn-secondary:hover {
  text-decoration: underline;
}

.kivi-btn-primary {
  background-color: #c026d3; /* Pink color as requested */
  color: white;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.kivi-btn-primary:hover {
  background-color: #a21caf; /* Darker pink on hover */
}
</style>
