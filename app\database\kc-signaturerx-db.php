<?php

/**
 * Create KiviCare SignatureRX table
 */
function kcCreateSignatureRXTable() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

    $table_name = $wpdb->prefix . 'kc_signaturerx';

    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        encounter_id bigint(20) NOT NULL,
        patient_id bigint(20) NOT NULL,
        doctor_id bigint(20) NOT NULL,
        clinic_id bigint(20) DEFAULT NULL,
        reference_id varchar(255) DEFAULT NULL,
        status varchar(50) NOT NULL DEFAULT 'sent',
        prescription_action varchar(50) DEFAULT NULL,
        aff_tag varchar(255) DEFAULT 'Medroid EHR',
        request_data text DEFAULT NULL,
        response_data text DEFAULT NULL,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT NULL,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    dbDelta($sql);
}