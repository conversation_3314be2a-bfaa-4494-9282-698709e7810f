<template>
  <div class="kivi-booking-widget">
    <div class="glass-card">
      <div class="decor-circle decor-circle-1"></div>
      <div class="decor-circle decor-circle-2"></div>

      <div class="kivi-booking-container">
        <!-- Header -->
        <div class="kivi-booking-header">
          <div class="header-brand">
            <svg class="caduceus-icon" viewBox="0 0 24 24">
              <path d="M12,2L12,2c-0.6,0-1,0.4-1,1v1H9C7.9,4,7,4.9,7,6v0c0,0.6,0.4,1,1,1h3v2.1C9.4,9.6,8.2,10.4,7.4,11H7c-1.1,0-2,0.9-2,2v0c0,0.6,0.4,1,1,1h0.3c-0.2,0.6-0.3,1.3-0.3,2v4c0,1.1,0.9,2,2,2h2c1.1,0,2-0.9,2-2v-1h2v1c0,1.1,0.9,2,2,2h2c1.1,0,2-0.9,2-2v-4c0-0.7-0.1-1.4-0.3-2H17c0.6,0,1-0.4,1-1v0c0-1.1-0.9-2-2-2h-0.4c-0.8-0.6-2-1.4-3.6-1.9V7h3c0.6,0,1-0.4,1-1v0c0-1.1-0.9-2-2-2h-2V3C13,2.4,12.6,2,12,2z"></path>
            </svg>
            <h2 class="header-title">Medical Appointment</h2>
          </div>
        </div>

        <!-- Progress Bar exactly like screenshot -->
        <div class="screenshot-progress">
          <div class="progress-steps">
            <div
              v-for="(step, index) in steps"
              :key="index"
              :class="['progress-step',
                      { 'active': currentStep === index,
                        'completed': currentStep > index,
                        'upcoming': currentStep < index }]"
              :data-step="step.id"
              @click="goToStep(index)"
            >
              <!-- Step Icon -->
              <div class="step-icon">
                <!-- Clinic Icon (document with pulse) -->
                <svg v-if="step.id === 'clinic'" viewBox="0 0 24 24" class="icon">
                  <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM6 20V4h7v5h5v11H6z"/>
                  <path d="M10 14.5h1.5v1.5H10zM12.5 14.5H14v1.5h-1.5z"/>
                  <path d="M10 12h1.5v1.5H10zM12.5 12H14v1.5h-1.5z"/>
                  <path d="M10 9.5h1.5V11H10zM12.5 9.5H14V11h-1.5z"/>
                </svg>

                <!-- Category Icon (medical document) -->
                <svg v-else-if="step.id === 'category'" viewBox="0 0 24 24" class="icon">
                  <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM6 20V4h7v5h5v11H6z"/>
                  <path d="M13 12H9v-1h4v1zm4 0h-3v-1h3v1z"/>
                  <path d="M13 14H9v-1h4v1zm4 0h-3v-1h3v1z"/>
                  <path d="M13 16H9v-1h4v1zm4 0h-3v-1h3v1z"/>
                </svg>

                <!-- Services Icon (stethoscope) -->
                <svg v-else-if="step.id === 'services'" viewBox="0 0 24 24" class="icon">
                  <path d="M19 8C19.56 8 20 8.43 20 9S19.56 10 19 10 18 9.57 18 9 18.44 8 19 8M2 2V11C2 13.96 4.19 16.5 7.14 16.91C7.76 19.92 10.42 22 13.5 22C17.09 22 20 19.09 20 15.5V15H18V15.5C18 18 16 20 13.5 20C11 20 9 18 9 15.5V15H7V7H9V6H12V2H2M4 4H10V9H4V4Z"/>
                </svg>

                <!-- DateTime Icon (calendar) -->
                <svg v-else-if="step.id === 'datetime'" viewBox="0 0 24 24" class="icon">
                  <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
                </svg>

                <!-- Patient Details Icon (user) -->
                <svg v-else-if="step.id === 'details'" viewBox="0 0 24 24" class="icon">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>

                <!-- Confirmation Icon (target) -->
                <svg v-else-if="step.id === 'confirm'" viewBox="0 0 24 24" class="icon">
                  <path d="M12 2A10 10 0 1 0 22 12 10 10 0 0 0 12 2Zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8Z"/>
                  <path d="M12 6a6 6 0 1 0 6 6 6 6 0 0 0-6-6Zm0 10a4 4 0 1 1 4-4 4 4 0 0 1-4 4Z"/>
                  <circle cx="12" cy="12" r="2"/>
                </svg>
              </div>

              <!-- Step Line (only show between steps) -->
              <div v-if="index < steps.length - 1" class="step-line"></div>

              <!-- Step Label -->
              <div class="step-label">{{ step.label }}</div>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-bar-fill" :style="{ width: `${(currentStep / (steps.length - 1)) * 100}%` }"></div>
            </div>
            <div class="progress-indicator" :style="{ left: `${(currentStep / (steps.length - 1)) * 100}%` }">
              {{ currentStep + 1 }}/{{ steps.length }}
            </div>
          </div>
        </div>

        <!-- Booking Widget Body -->
        <div class="kivi-booking-body">
          <div class="form-card">
            <keep-alive>
              <component
                :is="currentComponent"
                ref="currentComponent"
                :booking-data="bookingData"
                @update:booking-data="updateBookingData"
                @next-step="nextStep"
                @prev-step="prevStep"
                @go-to-step="goToStep"
                @confirmation-page-loaded="handleConfirmationPage"
              ></component>
            </keep-alive>
          </div>
        </div>

        <!-- Action buttons footer -->
        <div class="kivi-booking-footer">
          <button
            type="button"
            class="kivi-btn kivi-btn-secondary"
            :class="{ 'disabled': currentStep === 0 }"
            @click="prevStep"
          >
            <span class="btn-content">
              <svg class="btn-icon" viewBox="0 0 24 24">
                <path d="M15 19l-7-7 7-7"></path>
              </svg>
              <span class="btn-text">Back</span>
            </span>
          </button>

          <button
            type="button"
            class="kivi-btn kivi-btn-primary kivi-btn-purple"
            :class="{ 'disabled': !isCurrentStepValid }"
            @click="handleNextStep"
          >
            <span class="btn-content">
              <span class="btn-text">{{ currentStep === steps.length - 1 ? 'Confirm Appointment' : 'Next' }}</span>
              <svg v-if="currentStep !== steps.length - 1" class="btn-icon" viewBox="0 0 24 24">
                <path d="M9 5l7 7-7 7"></path>
              </svg>
              <svg v-else class="btn-icon" viewBox="0 0 24 24">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"></path>
              </svg>
            </span>
          </button>
        </div>

        <!-- Trust badge -->
        <div class="trust-badge">
          <svg class="shield-icon" viewBox="0 0 24 24">
            <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"></path>
          </svg>
          <span class="badge-text">Encrypted & HIPAA Compliant</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ClinicStep from './Steps/ClinicStep.vue';
import CategoryStep from './Steps/CategoryStep.vue';
import ServiceStep from './Steps/ServiceStep.vue';
import DateTimeStep from './Steps/DateTimeStep.vue';
import AppointmentDetailsStep from './Steps/AppointmentDetailsStep.vue';
import ConfirmationStep from './Steps/ConfirmationStep.vue';
import { apiCall } from '../../config/request';

export default {
  name: 'BookingWidget',
  components: {
    ClinicStep,
    CategoryStep,
    ServiceStep,
    DateTimeStep,
    AppointmentDetailsStep,
    ConfirmationStep
  },
  props: {
    presetClinicId: {
      type: [String, Number],
      default: 0
    },
    presetCategoryId: {
      type: String,
      default: ''
    },
    presetServiceId: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      currentStep: 0,
      steps: [
        { id: 'clinic', label: 'Select Clinic', component: 'ClinicStep', valid: false },
        { id: 'category', label: 'Select Category', component: 'CategoryStep', valid: false },
        { id: 'services', label: 'Select Services', component: 'ServiceStep', valid: false },
        { id: 'datetime', label: 'Date & Time', component: 'DateTimeStep', valid: false },
        { id: 'details', label: 'Appointment Details', component: 'AppointmentDetailsStep', valid: false },
        { id: 'confirm', label: 'Confirmation', component: 'ConfirmationStep', valid: true }
      ],
      bookingData: {
        clinic: null,
        category: null,
        services: [],
        date: null,
        time: null,
        patient: {
          name: '',
          email: '',
          phone: '',
          notes: ''
        }
      },
      isLoading: false
    };
  },
  computed: {
    currentComponent() {
      return this.steps[this.currentStep].component;
    },
    isCurrentStepValid() {
      return this.steps[this.currentStep].valid;
    }
  },
  mounted() {
    // If preset values are provided, we'll use them
    if (this.presetClinicId) {
      this.preloadClinic(this.presetClinicId);
    }
  },
  methods: {
    async preloadClinic(clinicId) {
      try {
        this.isLoading = true;
        const response = await apiCall.get(`clinic/${clinicId}`);

        if (response.data.status) {
          const clinic = response.data.data;
          this.bookingData.clinic = {
            id: clinic.id,
            name: clinic.name,
            address: clinic.address
          };

          // Update clinic step validity
          this.steps[0].valid = true;

          // If we have a preset category, preload it
          if (this.presetCategoryId) {
            this.preloadCategory(this.presetCategoryId);
          } else {
            // Skip to category step
            this.goToStep(1);
          }
        }
      } catch (error) {
        console.error('Error preloading clinic:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async preloadCategory(categoryId) {
      try {
        this.isLoading = true;

        // Get categories for this clinic
        const response = await apiCall.get('service/category', {
          params: {
            clinic_id: this.bookingData.clinic.id,
            widgetType: 'phpWidget'
          }
        });

        if (response.data.status) {
          const categories = response.data.data;
          const selectedCategory = categories.find(c => c.category_value === categoryId);

          if (selectedCategory) {
            this.bookingData.category = {
              id: selectedCategory.category_value,
              name: selectedCategory.category_value.replace('_', ' ')
            };

            // Update category step validity
            this.steps[1].valid = true;

            // If we have a preset service, preload it
            if (this.presetServiceId) {
              this.preloadService(this.presetServiceId);
            } else {
              // Skip to service step
              this.goToStep(2);
            }
          }
        }
      } catch (error) {
        console.error('Error preloading category:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async preloadService(serviceId) {
      try {
        this.isLoading = true;

        // Get services for this clinic and category
        const response = await apiCall.get('service/clinic-services', {
          params: {
            clinic_id: this.bookingData.clinic.id,
            service_category: this.bookingData.category.id,
            widgetType: 'phpWidget'
          }
        });

        if (response.data.status) {
          const services = response.data.data;
          const selectedService = services.find(s => parseInt(s.service_id) === parseInt(serviceId));

          if (selectedService) {
            this.bookingData.services = [{
              id: selectedService.service_id,
              name: selectedService.name,
              price: selectedService.price || '0',
              type: selectedService.telemed_service === 'yes' ? 'virtual' : 'clinic',
              duration: selectedService.duration || '30'
            }];

            // Update service step validity
            this.steps[2].valid = true;

            // Skip to datetime step
            this.goToStep(3);
          }
        }
      } catch (error) {
        console.error('Error preloading service:', error);
      } finally {
        this.isLoading = false;
      }
    },

    updateBookingData(data) {
      this.bookingData = { ...this.bookingData, ...data };

      // Update current step validity
      this.steps[this.currentStep].valid = this.validateCurrentStep();

      console.log(`Step ${this.currentStep} (${this.steps[this.currentStep].id}) validity updated to: ${this.steps[this.currentStep].valid}`);
      console.log('Current booking data:', this.bookingData);
    },

    validateCurrentStep() {
      switch (this.currentStep) {
        case 0: // Clinic
          return this.bookingData.clinic !== null;
        case 1: // Category
          return this.bookingData.category !== null;
        case 2: // Services
          return this.bookingData.services.length > 0;
        case 3: // Date & Time
          return this.bookingData.date !== null && this.bookingData.time !== null;
        case 4: // Patient Details
          return this.validatePatientData();
        case 5: // Confirmation
          return true;
        default:
          return false;
      }
    },

    validatePatientData() {
      const { name, email, phone } = this.bookingData.patient;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      return (
        name.trim() !== '' &&
        email.trim() !== '' &&
        emailRegex.test(email) &&
        phone.trim() !== ''
      );
    },

    nextStep() {
      // Ensure the current step is valid before proceeding
      if (this.validateCurrentStep() && this.currentStep < this.steps.length - 1) {
        // Mark the current step as valid
        this.steps[this.currentStep].valid = true;

        // Move to the next step
        this.currentStep++;

        console.log(`Advanced to step ${this.currentStep}: ${this.steps[this.currentStep].id}`);
      } else if (this.currentStep === this.steps.length - 1 && this.validateCurrentStep()) {
        // If we're on the last step and it's valid, confirm the appointment
        this.confirmAppointment();
      } else {
        console.log(`Cannot advance: current step ${this.currentStep} is not valid`);
      }
    },

    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
        console.log(`Moved back to step ${this.currentStep}: ${this.steps[this.currentStep].id}`);
      }
    },

    goToStep(stepIndex) {
      // Only allow going to steps that are accessible
      // - Either earlier steps that we've already visited
      // - Or the next step if the current step is valid
      if (stepIndex >= 0 && stepIndex < this.steps.length) {
        if (stepIndex < this.currentStep || (stepIndex === this.currentStep + 1 && this.validateCurrentStep())) {
          this.currentStep = stepIndex;
          console.log(`Jumped to step ${this.currentStep}: ${this.steps[this.currentStep].id}`);
        } else {
          console.log(`Cannot go to step ${stepIndex}: not accessible yet`);
        }
      }
    },

    async confirmAppointment() {
      try {
        this.isLoading = true;

        const appointmentData = {
          clinic_id: this.bookingData.clinic.id,
          service_id: this.bookingData.services.map(s => s.id).join(','),
          appointment_date: this.bookingData.date,
          appointment_time: this.bookingData.time,
          patient_name: this.bookingData.patient.name,
          patient_email: this.bookingData.patient.email,
          patient_phone: this.bookingData.patient.phone,
          description: this.bookingData.patient.notes || '',
          status: 1, // Pending status
          type: 'appointment_booking'
        };

        const response = await apiCall.post('appointment/save', appointmentData);

        if (response.data.status) {
          // Show success message
          this.$emit('appointment-confirmed', response.data.data);
        } else {
          // Show error message
          console.error('Error confirming appointment:', response.data.message);
        }
      } catch (error) {
        console.error('Error confirming appointment:', error);
      } finally {
        this.isLoading = false;
      }
    },

    handleConfirmationPage(data) {
      // Emit the event to the root component so it can be caught by the ConfirmationStep
      this.$root.$emit('confirmation-page-loaded', data);

      // Move to the confirmation step
      this.goToStep(5); // Assuming confirmation is step 5
    },

    handleNextStep() {
      // Get the current component instance
      const currentComponent = this.$refs.currentComponent;

      console.log("test");
      // If we're on the appointment details step and the component has a submitForm method
      // The index depends on your steps array, adjust as needed
      const detailsStepIndex = this.steps.findIndex(step => step.component === 'AppointmentDetailsStep');
      const confirmStepIndex = this.steps.findIndex(step => step.component === 'ConfirmationStep');

      if (this.currentStep === detailsStepIndex && currentComponent && typeof currentComponent.submitForm === 'function') {
        // Call the component's submitForm method
        currentComponent.submitForm();
      } else if (this.currentStep === confirmStepIndex) {
        // For confirmation step, we need to check if we should show payment page or submit appointment
        if (currentComponent) {
          // Always call showPaymentPage when on the confirmation step and the button is clicked
          console.log('Confirm appointment button clicked, calling showPaymentPage');
          if (typeof currentComponent.showPaymentPage === 'function') {
            currentComponent.showPaymentPage();
          } else if (typeof currentComponent.submitAppointment === 'function') {
            // Fallback to direct submission if showPaymentPage is not available
            console.log('showPaymentPage not available, falling back to submitAppointment');
            currentComponent.submitAppointment();
          }
        }
      } else {
        // Otherwise, just call the regular nextStep method
        this.nextStep();
      }
    }
  }
};
</script>

<style scoped>
.kivi-booking-widget {
  --primary: #0288d1;
  --primary-light: #e1f5fe;
  --primary-dark: #01579b;
  --accent: #00acc1;
  --accent-light: #e0f7fa;
  --text-primary: #263238;
  --text-secondary: #546e7a;
  --text-tertiary: #78909c;
  --surface: #ffffff;
  --surface-1: #f5f5f5;
  --surface-2: #e0f7fa;
  --border: rgba(176, 190, 197, 0.5);
  --shadow-color: rgba(0, 0, 0, 0.05);
  --success: #26a69a;
  --card-shadow:
    0 10px 15px -3px rgba(0, 131, 143, 0.05),
    0 4px 6px -2px rgba(0, 131, 143, 0.025);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 24px;
  --transition: 280ms cubic-bezier(0.4, 0, 0.2, 1);

  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 48rem;
  margin: 0 auto;
  perspective: 1000px;
  color: var(--text-primary);
}

/* Glass card effect */
.glass-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
  backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 25px -5px var(--shadow-color),
    0 10px 10px -5px var(--shadow-color),
    0 0 0 1px rgba(255, 255, 255, 0.8) inset;
  overflow: hidden;
  position: relative;
  isolation: isolate;
}

/* Decorative elements */
.decor-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.3;
  z-index: -1;
}

.decor-circle-1 {
  top: -5rem;
  right: -5rem;
  width: 15rem;
  height: 15rem;
  background: radial-gradient(circle, var(--primary-light), transparent 70%);
}

.decor-circle-2 {
  bottom: -6rem;
  left: -6rem;
  width: 20rem;
  height: 20rem;
  background: radial-gradient(circle, var(--accent-light), transparent 70%);
}

.kivi-booking-container {
  padding: 2.5rem;
  position: relative;
}

/* Header styles */
.kivi-booking-header {
  margin-bottom: 2rem;
}

.header-brand {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.caduceus-icon {
  width: 1.5rem;
  height: 1.5rem;
  fill: var(--primary);
  margin-right: 0.75rem;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.01em;
  margin: 0;
}

/* Screenshot-style Progress Bar */
.screenshot-progress {
  margin-bottom: 2.5rem;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  /* width: calc(100% / v-bind(steps.length)); */
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  z-index: 2;
}

.icon {
  width: 2.5rem;
  height: 2.5rem;
  fill: var(--text-tertiary);
  transition: var(--transition);
  opacity: 0.7;
}

.progress-step.active .icon {
  fill: var(--primary);
  opacity: 1;
}

.progress-step.completed .icon {
  fill: var(--primary);
  opacity: 0.9;
}

.step-line {
  position: absolute;
  top: 1.25rem;
  left: 50%;
  width: 100%;
  height: 1px;
  background-color: #e0e0e0;
  z-index: 1;
}

.progress-step.completed .step-line {
  background-color: var(--primary);
}

.step-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-tertiary);
  text-align: center;
  margin-top: 0.5rem;
  max-width: 100%;
  padding: 0 0.5rem;
  transition: var(--transition);
}

.progress-step.active .step-label {
  color: var(--primary);
  font-weight: 600;
}

.progress-step.completed .step-label {
  color: var(--primary);
}

/* Progress Bar */
.progress-container {
  position: relative;
  margin-top: 1rem;
  padding: 0 0.5rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: #e0f7fa;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary), var(--accent));
  border-radius: 0.25rem;
  transition: width var(--transition);
}

.progress-indicator {
  position: absolute;
  bottom: -1.5rem;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: left var(--transition);
}

/* Body styles */
.kivi-booking-body {
  margin-bottom: 2rem;
}

.form-card {
  background-color: var(--surface);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  padding: 2rem;
  min-height: 18rem;
  position: relative;
  border: 1px solid var(--border);
  overflow: hidden;
}

.form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary), var(--accent));
}

/* Footer styles */
.kivi-booking-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.kivi-btn {
  position: relative;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
  overflow: hidden;
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3rem;
  padding: 0 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: var(--transition);
}

.kivi-btn-primary .btn-content {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.kivi-btn-purple .btn-content {
  background: linear-gradient(135deg, #c026d3, #a21caf);
  color: white;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.kivi-btn-primary:hover:not(.disabled) .btn-content,
.kivi-btn-purple:hover:not(.disabled) .btn-content {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  transform: translateY(-1px);
}

.kivi-btn-primary:active:not(.disabled) .btn-content,
.kivi-btn-purple:active:not(.disabled) .btn-content {
  transform: translateY(1px);
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.kivi-btn-secondary .btn-content {
  background-color: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.kivi-btn-secondary:hover:not(.disabled) .btn-content {
  background-color: var(--surface-1);
  transform: translateY(-1px);
}

.kivi-btn-secondary:active:not(.disabled) .btn-content {
  transform: translateY(1px);
}

.kivi-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  width: 1rem;
  height: 1rem;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.kivi-btn-secondary .btn-icon {
  margin-right: 0.5rem;
}

.kivi-btn-primary .btn-icon {
  margin-left: 0.5rem;
}

/* Trust badge */
.trust-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: 0.75rem;
}

.shield-icon {
  width: 0.875rem;
  height: 0.875rem;
  fill: currentColor;
  margin-right: 0.5rem;
}

.badge-text {
  letter-spacing: 0.01em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .kivi-booking-container {
    padding: 1.5rem;
  }

  .form-card {
    padding: 1.5rem;
  }

  .header-title {
    font-size: 1.25rem;
  }

  .icon {
    width: 2rem;
    height: 2rem;
  }

  .step-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 640px) {
  .kivi-booking-container {
    padding: 1.25rem;
  }

  .form-card {
    padding: 1.25rem;
    min-height: 16rem;
  }

  .icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .step-label {
    font-size: 0.625rem;
  }

  .kivi-booking-footer {
    flex-direction: column;
    gap: 1rem;
  }

  .kivi-btn {
    width: 100%;
  }
}
</style>