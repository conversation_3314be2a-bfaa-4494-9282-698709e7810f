<?php
ob_start();

$patient = get_user_by( 'ID', $encounter_detail->patient_id );
$clinic = kcClinicDetail($encounter_detail->clinic_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A Document from Your Doctor is Now Available</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        .email-header {
            background-color: #2c7be5;
            color: white;
            padding: 25px 20px;
            text-align: center;
        }
        .email-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            letter-spacing: 0.3px;
        }
        .email-body {
            padding: 30px;
            background-color: #fff;
        }
        .email-body p {
            font-size: 16px;
            margin-bottom: 15px;
            color: #4a5568;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .action-button {
            display: inline-block;
            background-color: #2c7be5;
            color: white !important;
            padding: 14px 32px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        .action-button:hover {
            background-color: #1a68d1;
        }
        .notice-box {
            background-color: #f8fafc;
            border-left: 4px solid #3182ce;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 4px 4px 0;
        }
        .notice-box h3 {
            margin-top: 0;
            color: #2d3748;
            font-size: 18px;
        }
        .notice-box ul {
            padding-left: 20px;
            margin: 15px 0;
        }
        .notice-box li {
            margin-bottom: 8px;
            color: #4a5568;
        }
        .example {
            background-color: #ebf4ff;
            padding: 12px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .footer {
            margin-top: 20px;
            padding: 20px 30px 30px;
            color: #718096;
            text-align: center;
            background-color: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }
        .footer p {
            margin: 5px 0;
            font-size: 15px;
        }
        .clinic-name {
            font-weight: 600;
            color: #4a5568;
        }
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100%;
                margin: 0;
                border-radius: 0;
            }
            .email-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h2>A Document from Your Doctor is Now Available</h2>
        </div>

        <div class="email-body">
            <p>Dear <?php echo esc_html($patient->display_name); ?>,</p>

            <p>Your doctor has shared a document with you on Medroid. The document is attached to this email, you will find it at the bottom of the screen.</p>

            <div class="notice-box">
                <h3>Important: Document Password Information</h3>
                <p><strong>Password: <?php echo esc_html(get_pdf_password_from_patient_id($encounter_detail->patient_id)); ?></strong></p>
                <p>The document is password protected for your security. To open it, please use the attached password.</p>
            </div>

            <p>To login to your medical records, please click the button below and log in to your account. Remember, to login you will need to use the password you received at the time of your initial registration (not the password above):</p>

            <div class="button-container">
                <a href="<?php echo admin_url().'admin.php?page=dashboard#/patient-encounter/dashboard/'.$encounter_detail->id ?>" target="_blank" class="action-button">Login to my Medical Records</a>
            </div>

            <p>If you experience any issues accessing your account or opening the document, please contact our support team for assistance.</p>
        </div>

        <div class="footer">
            <p>Thank you,</p>
            <p class="clinic-name"><?php echo esc_html($clinic->name) ?></p>
        </div>
    </div>
</body>
</html>

<?php
return ob_get_clean();
?>
