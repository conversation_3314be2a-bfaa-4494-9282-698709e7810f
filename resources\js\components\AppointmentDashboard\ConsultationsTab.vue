<template>
  <div class="">
    <div class="flex h-[calc(100vh-200px)]">
      <!-- Consultation List -->
      <div class="w-2/5 border-r p-4">
        <div v-if="encounters.length" class="space-y-3">
          <div
            v-for="encounter in encounters"
            :key="encounter.id"
            class="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
            :class="{ 'bg-gray-50': selectedEncounter.id === encounter.id }"
            @click="selectEncounter(encounter)"
          >
            <h3 class="font-medium">{{ encounter.doctor_name }}</h3>
            <div class="text-sm text-gray-500 mt-1">
              {{ formatDate(encounter.encounter_date) }}
            </div>
          </div>
        </div>
        <div v-else class="text-center text-gray-500 mt-8">No data found</div>
      </div>

      <!-- Consultation Details -->
      <div class="w-3/5 p-6" v-if="encounters.length && selectedEncounter">
        <div class="flex justify-between items-center mb-6">
          <div>
            <h2 class="text-xl font-semibold">
              {{ selectedEncounter.doctor_name }}
            </h2>
            <p class="text-sm text-gray-500">
              {{ formatDate(selectedEncounter.encounter_date) }}
            </p>
          </div>
          <div class="flex gap-2">
            <button
              v-if="
                getUserRole() === 'doctor' || getUserRole() === 'administrator'
              "
              class="p-2 text-gray-500 hover:bg-gray-100 rounded-lg"
              title="Edit"
              @click="goToEdit"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-pen w-5 h-5"
              >
                <path
                  d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"
                ></path>
              </svg>
            </button>
            <button
              class="p-2 text-gray-500 hover:bg-gray-100 rounded-lg"
              title="Share"
              @click="sendPrescriptionToEmail"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-share2 w-5 h-5"
              >
                <circle cx="18" cy="5" r="3"></circle>
                <circle cx="6" cy="12" r="3"></circle>
                <circle cx="18" cy="19" r="3"></circle>
                <line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line>
                <line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line>
              </svg>
            </button>
            <button
              class="p-2 text-gray-500 hover:bg-gray-100 rounded-lg"
              title="Print"
              @click="printEncounter"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-printer w-5 h-5"
              >
                <path
                  d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"
                ></path>
                <path d="M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6"></path>
                <rect x="6" y="14" width="12" height="8" rx="1"></rect>
              </svg>
            </button>
          </div>
        </div>
        <div class="space-y-6">
          <section
            v-for="(items, type) in groupedMedicalHistory"
            :key="type"
            class="border-b pb-4"
          >
            <h3 class="font-medium text-gray-900 mb-2">
              {{ capitalize(type) }}
            </h3>
            <div v-if="items.length">
              <div
                v-for="(item, itemK) in items"
                :key="itemK"
                class="flex justify-between items-center text-gray-600"
              >
                <span>{{ item.title }}</span>
                <span class="text-sm text-gray-400"
                  >Recorded: {{ formatDate(item.created_at) }}</span
                >
                <!-- Assuming encounter_date is the recorded date -->
              </div>
            </div>
            <div v-else class="text-gray-600">No data recorded</div>
          </section>
        </div>
      </div>
      <div
        v-else-if="!encounters.length"
        class="w-3/5 p-6 flex items-center justify-center"
      >
        <div class="text-gray-500">Select an encounter to view details</div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";
import { displayErrorMessage } from "../../utils/message";
import { formatDate } from "../../utils/helper"; // Adjust the path according to your project structure

export default {
  name: "SummaryTab",

  props: {
    patientId: {
      type: [String, Number],
    },
  },

  data() {
    return {
      encounters: [],
      selectedEncounter: {},
    };
  },

  methods: {
    formatDate,
    login_id() {
      return this.$store.state.userDataModule.user.ID;
    },
    getSummaryTabDetails() {
      get("get_encounter_list_by_patient_id", {
        login_id: this.login_id(),
        patient_id: this.patientId,
      })
        .then((response) => {
          if (response.data.status) {
            this.encounters = response.data.data;
            this.selectedEncounter = this.encounters[0] || null;
          }
        })
        .catch((error) => {
          console.error("Error fetching summary data:", error);
          displayErrorMessage("Internal server error");
        });
    },
    selectEncounter(encounter) {
      console.log("encounter", encounter);
      this.selectedEncounter = encounter;
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    },
    capitalize(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    redirectToCreateEncounter() {
      this.$router.push({
        name: "appointment.new",
        params: { encounter_id: this.patientId },
      });
    },
    goToEdit() {
      console.log("Selected Encounter:", this.selectedEncounter);
      console.log("Selected Encounter ID:", this.selectedEncounter?.id);

      if (this.selectedEncounter && this.selectedEncounter.id) {
        this.$router.push({
          name: "appointment.new",
          params: { encounter_id: this.selectedEncounter.id },
        });
      } else {
        console.warn("No encounter selected or invalid encounter ID");
      }
    },

    async confirmAction(title, message) {
      const result = await this.$swal.fire({
        title: title,
        text: message,
        icon: "question",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });
      return result.isConfirmed;
    },

    /**
     * Initiates the print process for the encounter details.
     */
    async printEncounter() {
      if (this.isButtonDisabled) return;

      const confirmed = await this.confirmAction(
        "Print Consultation",
        "Do you want to print this consultation?"
      );

      if (!confirmed) return;

      this.isButtonDisabled = true;
      this.iconClass = "fa fa-spinner fa-spin";

      try {
        // Collect all the data
        const printData = {
          encounter_id: this.encounterId,
          problems: this.$refs.medical_history_problems
            ? this.$refs.medical_history_problems.medicalHistoryList
            : [],
          observations: this.$refs.medical_history_observation
            ? this.$refs.medical_history_observation.medicalHistoryList
            : [],
          notes: this.$refs.medical_history_note
            ? this.$refs.medical_history_note.medicalHistoryList
            : [],
          prescription: this.$refs.prescription_ref
            ? {
                medicines: this.$refs.prescription_ref.prescriptionList || [],
                notes: this.$refs.prescription_ref.notes || "",
              }
            : null,
        };

        const response = await get("get_encounter_print", {
          ...printData,
        });

        if (response?.data?.status && response?.data?.data) {
          // Create a temporary iframe for printing
          const printFrame = document.createElement("iframe");
          printFrame.style.display = "none";
          document.body.appendChild(printFrame);

          const frameDoc = printFrame.contentWindow.document;
          frameDoc.open();
          frameDoc.write(`
                <!DOCTYPE html>
                <html>
                    <head>
                        <title>Print</title>
                        <style>
                            @media print {
                                @page { margin: 2cm; }
                                body { font-family: Arial, sans-serif; }
                                .print-container { width: 100%; }
                                .header { text-align: center; margin-bottom: 20px; }
                                .patient-info { margin-bottom: 20px; }
                                .section { margin-bottom: 15px; }
                                table { width: 100%; border-collapse: collapse; }
                                th, td { padding: 8px; border: 1px solid #ddd; }
                                .signature { margin-top: 50px; text-align: right; }
                            }
                        </style>
                    </head>
                    <body>${response.data.data}</body>
                </html>
            `);
          frameDoc.close();

          // Wait for content and images to load
          setTimeout(() => {
            printFrame.contentWindow.print();
            // Remove the frame after printing
            setTimeout(() => {
              document.body.removeChild(printFrame);
            }, 500);
          }, 500);
        } else {
          throw new Error(
            response?.data?.message || "Failed to generate print data"
          );
        }
      } catch (error) {
        console.error("Print error:", error);
        this.$bvToast.toast("Failed to prepare print data. Please try again.", {
          title: "Error",
          variant: "danger",
          solid: true,
        });
      } finally {
        this.isButtonDisabled = false;
        this.iconClass = "fa fa-print";
      }
    },

    async sendPrescriptionToEmail() {
      try {
        // Close the dropdown
        this.isPrescriptionActionDropdownOpen = false;

        if (!this.encounterId) {
          throw new Error("No encounter ID found");
        }

        // Ask for confirmation using SweetAlert2
        const result = await this.$swal.fire({
          title: "Send Prescription",
          text: "Are you sure you want to send this prescription via email?",
          icon: "question",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, send it!",
          cancelButtonText: "Cancel",
        });

        // If user confirms
        if (result.isConfirmed) {
          // Show loading state
          this.$swal.fire({
            title: "Sending...",
            text: "Please wait while we send the prescription",
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            showConfirmButton: false,
            didOpen: () => {
              this.$swal.showLoading();
            },
          });

          // Send the email with just encounter_id
          const response = await get("prescription_mail", {
            encounter_id: this.encounterId,
          });

          // Close loading state
          this.$swal.close();

          if (response.data?.status === true) {
            // Show success message
            this.$swal.fire({
              title: "Success!",
              text: response.data.message || "Prescription sent successfully",
              icon: "success",
              confirmButtonColor: "#3085d6",
            });
          } else {
            throw new Error(response.data?.message || "Failed to send email");
          }
        }
      } catch (error) {
        console.error("Error sending prescription email:", error);
        // Show error message
        this.$swal.fire({
          title: "Error!",
          text:
            error.message ||
            "Failed to send prescription email. Please try again.",
          icon: "error",
          confirmButtonColor: "#3085d6",
        });
      }
    },
  },

  computed: {
    encounterId() {
      return this.selectedEncounter?.id;
    },
    groupedMedicalHistory() {
      // Check if medical_history exists and is an object
      if (
        typeof this.selectedEncounter.medical_history !== "object" ||
        this.selectedEncounter.medical_history === null
      ) {
        return {}; // Return empty object if it's not an object
      }

      // Return the medical history grouped by type
      return Object.entries(this.selectedEncounter.medical_history).reduce(
        (grouped, [type, items]) => {
          // Ensure items are in an array format
          if (Array.isArray(items)) {
            grouped[type] = items;
          }
          return grouped;
        },
        {}
      );
    },
  },

  watch: {
    patientId: {
      immediate: true,
      handler() {
        this.getSummaryTabDetails();
      },
    },
  },
};
</script>
