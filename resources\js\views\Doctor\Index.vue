<!-- Template Part -->
<template>
  <div>
    <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
      <!-- Header Section -->
      <div class="mb-8 flex justify-between items-center">
        <div class="flex items-center gap-4">
          <router-link
            :to="{ path: '/' }"
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg shadow-sm hover:bg-gray-800"
          >
            <i class="fa fa-arrow-left w-4 h-4"></i>
            <span>Back</span>
          </router-link>
          <h1 class="text-2xl font-semibold text-gray-800">
            {{ formTranslation.doctor.doctors_list }}
          </h1>
        </div>
        <div class="flex gap-2">
          <!-- <span 
            :class="[
              'px-6 py-3 text-left text-xs font-medium uppercase tracking-wider',
              (userData.allow_no_of_doc-userData.total_doctor) > 0 ? 'text-green-500' : 'text-red-500'
            ]"
          >
            Doctor capacity: {{ (userData.allow_no_of_doc-userData.total_doctor) > 0 ? `${(userData.allow_no_of_doc-userData.total_doctor)} slots available` : 'No slots available' }} ({{ userData.total_doctor }}/{{ userData.allow_no_of_doc }} used)
          </span> -->
          <button
            v-if="kcCheckPermission('doctor_add')"
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            @click="openDoctorForm()"
          >
            <i class="fa fa-plus"></i>
            {{ formTranslation.doctor.add_doctor }}
          </button>
        </div>
      </div>

      <!-- Search Bar -->
      <div class="relative mb-6">
        <i
          class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
        ></i>
        <input
          v-model="serverParams.searchTerm"
          @input="globalFilter({ searchTerm: serverParams.searchTerm })"
          :placeholder="formTranslation.common.search_doctor_global_placeholder"
          class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text"
        />
      </div>

      <!-- Filters Grid -->
      <div class="grid grid-cols-6 gap-4 mb-6">
        <input
          v-model="serverParams.columnFilters.ID"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.common.id"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.display_name"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.doctor.dt_name_filter_plh"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.clinic_name"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.doctor.dt_lbl_clinic_name"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.user_email"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.doctor.dt_email_fltr_plh"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.user_registered"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          type="date"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <select
          v-model="serverParams.columnFilters.user_status"
          @change="
            onColumnFilter({ columnFilters: serverParams.columnFilters })
          "
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        >
          <option value="">
            {{ formTranslation.static_data.dt_lbl_plh_sr_fltr_status }}
          </option>
          <option value="0">{{ formTranslation.common.active }}</option>
          <option value="1">{{ formTranslation.common.inactive }}</option>
        </select>
      </div>

      <!-- Table -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
      >
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                <input
                  type="checkbox"
                  class="rounded border-gray-300"
                  v-model="selectAllChecked"
                  @change="handleSelectAll"
                />
              </th>
              <th
                v-for="column in doctorsList.column"
                :key="column.field"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                @click="onSortChange({ field: column.field })"
              >
                {{ column.label }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="(row, index) in doctorsList.data"
              :key="row.ID"
              class="hover:bg-gray-50"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  class="rounded border-gray-300"
                  v-model="selectedRows"
                  :value="row.ID"
                  @change="handleRowSelection"
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.ID }}</td>
              <td class="px-6 py-4 whitespace-nowrap flex items-center gap-3">
                <img
                  v-if="row.profile_image"
                  :src="row.profile_image"
                  class="h-8 w-8 rounded-full object-cover"
                  alt="profile"
                />
                <span
                  v-else
                  class="h-8 w-8 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center font-medium"
                >
                  {{ getInitials(row.display_name) }}
                </span>
                {{ row.display_name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.clinic_name }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.user_email }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ row.mobile_number }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ getSpeciality(row.specialties) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                  <!-- Toggle button -->
                  <toggle-switch
                    v-if="kcCheckPermission('doctor_edit')"
                    :value="row.user_status === '0' ? 'on' : 'off'"
                    @input="
                      (value) => {
                        // Update the local state immediately for the toggle animation
                        row.user_status = value === 'on' ? '0' : '1';
                        // Then call the status change function
                        changeModuleValueStatus({
                          module_type: 'doctors',
                          id: row.ID,
                          value: value === 'on' ? '0' : '1',
                        });
                      }
                    "
                    on-value="on"
                    off-value="off"
                  />
                  <span
                    :class="[
                      'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                      row.user_status === '0'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800',
                    ]"
                  >
                    {{
                      row.user_status === "0"
                        ? formTranslation.common.active
                        : formTranslation.common.inactive
                    }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="relative actions-dropdown">
                  <button
                    @click.stop="toggleActionMenu(row.ID)"
                    class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring h-8 px-3 text-xs bg-black text-white hover:bg-gray-800"
                  >
                    Actions
                  </button>

                  <!-- Actions Dropdown Menu -->
                  <div
                    v-if="showActionsMenu === row.ID"
                    @click.stop
                    class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                  >
                    <!-- Edit Doctor -->
                    <button
                      v-if="kcCheckPermission('doctor_edit')"
                      @click="
                        openDoctorForm(row.ID);
                        showActionsMenu = null;
                      "
                      class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i class="fa fa-pen w-4 h-4 mr-2"></i>
                      {{ formTranslation.common.edit }}
                    </button>

                    <button
                      v-if="kcCheckPermission('doctor_edit')"
                      :id="'resend_' + row.ID"
                      @click="resendRequest(row.ID)"
                      class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i class="fa fa-calendar w-4 h-4 mr-2"></i>
                      {{ formTranslation.receptionist.resend_credential }}
                    </button>

                    <!-- Add Session -->
                    <button
                      v-if="kcCheckPermission('doctor_session_add')"
                      @click="
                        $router.push({
                          name: 'doctor-session.create',
                          params: { id: row.ID },
                        })
                      "
                      class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i class="fa fa-calendar w-4 h-4 mr-2"></i>
                      {{ formTranslation.common.add_session }}
                    </button>

                    <!-- Add Service -->
                    <button
                      v-if="kcCheckPermission('service_add')"
                      @click="
                        addService(row.ID);
                        showActionsMenu = null;
                      "
                      class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i class="fa fa-server w-4 h-4 mr-2"></i>
                      {{ formTranslation.common.service_add }}
                    </button>

                    <!-- Verify Doctor -->
                    <button
                      v-if="
                        row.user_deactivate === 'no' &&
                        getUserRole() === 'administrator'
                      "
                      @click="
                        openVerifyModal(row);
                        showActionsMenu = null;
                      "
                      class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i class="fas fa-check-circle w-4 h-4 mr-2"></i>
                      {{ formTranslation.common.verify }}
                    </button>

                    <!-- Delete Doctor -->
                    <button
                      v-if="kcCheckPermission('doctor_delete')"
                      @click="
                        deleteDoctorData(index + 1);
                        showActionsMenu = null;
                      "
                      class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i class="fa fa-trash w-4 h-4 mr-2 text-red-500"></i>
                      {{ formTranslation.clinic_schedule.dt_lbl_dlt }}
                    </button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div
          class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
        >
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-700">Rows per page:</span>
            <select
              v-model="serverParams.perPage"
              @change="onPerPageChange({ currentPerPage: serverParams.perPage, currentPage: serverParams.page })"
              class="border border-gray-300 rounded-md text-sm p-1"
            >
              <option>10</option>
              <option>25</option>
              <option>50</option>
            </select>
          </div>
          <div class="flex items-center gap-4">
            <span class="text-sm text-gray-700">
              Page {{ serverParams.page }} of
              {{ Math.ceil(totalRows / serverParams.perPage) }}
            </span>
            <div class="flex gap-2">
              <button
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                :disabled="serverParams.page === 1"
                @click="onPageChange({ currentPage: serverParams.page - 1 })"
              >
                <i class="fa fa-chevron-left text-gray-600"></i>
              </button>
              <button
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                :disabled="
                  serverParams.page >=
                  Math.ceil(totalRows / serverParams.perPage)
                "
                @click="onPageChange({ currentPage: serverParams.page + 1 })"
              >
                <i class="fa fa-chevron-right text-gray-600"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Modals -->
      <module-data-import
        v-if="userData.addOns.kiviPro && kcCheckPermission('doctor_add')"
        ref="module_data_import"
        :is-import-modal-open="showImportModal"
        @reloadList="getDoctorList"
        module-type="doctor"
        :module-name="formTranslation.common.doctors"
        :required-data="[
            { label: formTranslation.doctor.first_name, value: 'first_name' },
            { label: formTranslation.doctor.last_name, value: 'last_name' },
            { label: formTranslation.doctor.email, value: 'email' },
            { label: formTranslation.common.country_calling_code, value: 'country_calling_code' },
            { label: formTranslation.common.country_code, value: 'country_code' },
            { label: formTranslation.doctor.doctor_contact, value: 'contact' },
            { label: formTranslation.doctor.gender, value: 'gender' },
            { label: formTranslation.doctor.specilization, value: 'specialization' },
          ]"
        :encounter-id="0"
      />

      <!-- Other existing modals -->
      <doctor-service
        v-if="doctorServiceOpen"
        :doctor_id="serviceDoctorId"
        @close="closeServiceModal"
      />

      <!-- Verify Modal -->
      <modal-popup v-if="verifyPopupModal" @close="verifyPopupModal = false">
        <!-- Verify modal content -->
      </modal-popup>

      <!-- Other modals -->
      <appointment-review-card
        v-if="appointmentReviewModal"
        :doctor_id="select_doctor_id"
        @close="appointmentReviewModal = false"
      />

      <custom-form
        v-if="doctorCustomFormModal"
        :data="doctorCustomFormData"
        :viewMode="doctorCustomFormViewMode"
        @close="doctorCustomFormModal = false"
      />
    </div>

    <DoctorFormModal
      v-if="showDoctorForm"
      :openModal="showDoctorForm"
      :doctorId="selectedDoctorId"
      @close="closeDoctorForm"
      @saved="handleDoctorSaved"
    />
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import doctorService from "../Service/Create";
import { required } from "vuelidate/lib/validators";
import AppointmentReviewCard from "../../components/appointment/AppointmentReviewCard";
import ModalPopup from "../../components/Modal/Index";
import CustomForm from "../CustomForm/Form.vue";
import DoctorFormModal from "./DoctorFormModal.vue";

export default {
  name: "DoctorsList",
  components: {
    doctorService,
    AppointmentReviewCard,
    ModalPopup,
    CustomForm,
    DoctorFormModal,
  },

  data: () => ({
    showImportModal: false,
    showActionsMenu: null,
    isSending: false,
    isLoading: true,
    showDoctorForm: false,
    selectedDoctorId: null,
    doctorsList: {
      data: [],
      column: [],
    },
    clinic: [],
    filterClinic: [],
    selectedRows: [], // For bulk selection
    selectAllChecked: false,
    doctorRequest: {},
    doctorSpecialization: [],
    serverParams: {
      columnFilters: {
        specialties: "",
        ID: "",
        display_name: "",
        clinic_name: "",
        user_email: "",
        user_registered: "",
        user_status: "",
      },
      sort: [
        {
          field: "",
          type: "",
        },
      ],
      page: 1,
      perPage: 10,
      searchTerm: "",
      type: "list",
    },
    oldServerParams: {
      columnFilters: {},
      searchTerm: "",
      perPage: 10,
    },
    totalRows: 0,
    verifyModalData: {},
    verifyPopupModal: false,
    verifyButtonLoading: false,
    doctorServiceOpen: false,
    serviceDoctorId: -1,
    telemed: {},
    telemedSubmitted: false,
    doctorTelemedConfigurationModal: false,
    doctorTelemedConfigurationLoader: true,
    telemedFormLoader: false,
    appointmentReviewModal: false,
    select_doctor_id: 0,
    globalCheckboxApplyData: {},
    globalCheckboxApplyDataActions: [],
    doctorCustomFormData: {},
    doctorCustomFormModal: false,
    doctorCustomFormViewMode: false,
    usedSlots:0,
    totalSlots:0,
    availableSlots:0
  }),

  validations: {
    telemed: {
      api_key: { required },
      api_secret: { required },
    },
  },
  beforeUnmount() {
    document.removeEventListener("click", this.handleClickOutside);
  },
  mounted() {
    this.init();
    this.telemed = this.defaultZoomConfifurationValue();
    document.addEventListener("click", this.handleClickOutside);
  },
  unmounted() {
    document.removeEventListener("click", this.handleClickOutside);
  },

  methods: {
    init() {
      this.getDoctorList();
      this.doctorsList = this.defaultDoctorDataList();
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData();
      this.globalCheckboxApplyDataActions =
        this.defaultGlobalCheckboxApplyDataActions();

      setTimeout(() => {
        this.clinic = this.clinics;
        this.clinic.forEach((element) => {
          this.filterClinic.push({ value: element.id, text: element.label });
        });
      }, 1000);

      
      this.usedSlots=this.userData.total_doctor
      this.totalSlots=this.userData.allow_no_of_doc

      this.availableSlots=this.totalSlots- this.usedSlots
    },

    // New methods for bulk selection
    handleSelectAll() {
      if (this.selectAllChecked) {
        this.selectedRows = this.doctorsList.data.map((row) => row.ID);
      } else {
        this.selectedRows = [];
      }
      this.updateGlobalCheckboxData();
    },

    handleRowSelection() {
      this.selectAllChecked =
        this.selectedRows.length === this.doctorsList.data.length;
      this.updateGlobalCheckboxData();
    },

    updateGlobalCheckboxData() {
      this.globalCheckboxApplyData.data = this.selectedRows;
    },

    openDoctorForm(doctorId = null) {
      // Check if this is an Essential membership plan
      if(this.isEssentialPlan){
        this.$swal.fire({
          title: "Subscription Required",
          text: "Adding a new doctor requires a Momentum subscription plan.",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Upgrade Now",
          cancelButtonText: "Cancel",
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
        })
        .then((result) => {
          if (result.isConfirmed) {
            // Route to the subscription settings page
            this.$router.push({ name: 'setting.subscription' });
          }
        });
      } else {
        // Check if we have available doctor slots based on the plan
        if ((this.userData.allow_no_of_doc - this.userData.total_doctor) > 0) {
          this.selectedDoctorId = doctorId;
          this.showDoctorForm = true;
        } else {
          // Show warning about additional charges with dynamic price
          const doctorPrice = this.getDoctorPrice();
          const currencyPrefix = this.userData.clinic_currency_detail && this.userData.clinic_currency_detail.prefix ? this.userData.clinic_currency_detail.prefix : '';
          const currencyPostfix = this.userData.clinic_currency_detail && this.userData.clinic_currency_detail.postfix ? this.userData.clinic_currency_detail.postfix : '';
          
          this.$swal
            .fire({
              text: `Your monthly billing will increase by ${currencyPrefix}${doctorPrice}${currencyPostfix} when you add this doctor. Confirm this change?`,
              icon: "warning",
              showCancelButton: true,
              confirmButtonColor: "#d33",
              cancelButtonColor: "#3085d6",
            })
            .then((result) => {
              if (result.isConfirmed) {
                this.selectedDoctorId = doctorId;
                this.showDoctorForm = true;
              }
            });
        }
      }
    },

    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: "delete",
        module: "clinics",
        data: [],
      };
    },

    defaultGlobalCheckboxApplyDataActions: function () {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active,
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive,
        },
        {
          value: "resend_credential",
          label: this.formTranslation.receptionist.resend_credential,
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt,
        },
      ];
    },

    handleDoctorSaved() {
      this.getDoctorList();
      this.showDoctorForm = false;
      this.selectedDoctorId = null;
      this.$store.dispatch("userDataModule/fetchUserData", {});
    },

    getDoctorList() {
      this.isLoading = true;
      get("doctor_list", this.serverParams)
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.doctorsList.data = data.data.data;
            this.totalRows = data.data.total_rows;
          } else {
            this.doctorsList.data = [];
            this.totalRows = 0;
          }
          this.isLoading = false;
        })
        .catch((error) => {
          console.error(error);
          this.isLoading = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleClickOutside(event) {
      // Only close the dropdown if clicking outside of any actions dropdown
      const clickedDropdown = event.target.closest(".actions-dropdown");
      if (!clickedDropdown) {
        this.showActionsMenu = null;
      }
    },

    toggleActionMenu(id) {
      // Close dropdown if clicking the same button, otherwise open the new one
      this.showActionsMenu = this.showActionsMenu === id ? null : id;
    },

    defaultDoctorDataList() {
      return {
        column: [
          {
            field: "ID",
            label: this.formTranslation.common.id,
            sortable: true,
          },
          {
            field: "display_name",
            label: this.formTranslation.doctor.dt_lbl_name,
            sortable: true,
          },
          {
            field: "clinic_name",
            label: this.formTranslation.doctor.dt_lbl_clinic_name,
            sortable: false,
          },
          {
            field: "user_email",
            label: this.formTranslation.doctor.dt_lbl_email,
            sortable: true,
          },
          {
            field: "mobile_number",
            label: this.formTranslation.doctor.dt_lbl_mobile_number,
            sortable: false,
          },
          {
            field: "specialties",
            label: this.formTranslation.doctor.dt_lbl_specialties,
            sortable: false,
          },
          {
            field: "user_status",
            label: this.formTranslation.service.dt_lbl_status,
            sortable: true,
          },
          {
            field: "actions",
            label: this.formTranslation.doctor.dt_lbl_actions,
            sortable: false,
          },
        ],
        data: [],
      };
    },

    // Updated pagination and filter methods
    updateParams(newProps) {
      this.serverParams = { ...this.serverParams, ...newProps };
      this.getDoctorList();
    },

    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) return;

      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },

    onSortChange(params) {
      const currentSort = this.serverParams.sort[0];
      const newSort = {
        field: params.field,
        type:
          currentSort.field === params.field && currentSort.type === "asc"
            ? "desc"
            : "asc",
      };

      this.updateParams({ sort: [newSort] });
    },

    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) return;

      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({
        searchTerm: params.searchTerm,
        page: 1,
      });
    }, 300),

    onColumnFilter: _.debounce(function (params) {
      this.oldServerParams.columnFilters = { ...params.columnFilters };
      this.updateParams({
        columnFilters: params.columnFilters,
        page: 1,
      });
    }, 300),

    resendRequest: function (id) {
      var element = $('#resend_' + id).find("i");
      element.removeClass('fa fa-paper-plane ')
      element.addClass("fa fa-spinner fa-spin");
      post('resend_credential', { id: id })
        .then((data) => {
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-paper-plane")
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message)
          } else {
            displayErrorMessage(data.data.message)
          }
        })
        .catch((error) => {
          console.log(error);
        })
    },

    // Action methods
    deleteDoctorData(index) {
      if (!this.doctorsList.data[index - 1]) return;
      
      // Get price and currency info for the delete warning message
      const doctorPrice = this.getDoctorPrice();
      const currencyPrefix = this.userData.clinic_currency_detail && this.userData.clinic_currency_detail.prefix ? this.userData.clinic_currency_detail.prefix : '$';
      const currencyPostfix = this.userData.clinic_currency_detail && this.userData.clinic_currency_detail.postfix ? this.userData.clinic_currency_detail.postfix : '';
      
      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          html: `
            <p>${this.formTranslation.clinic_schedule.dt_delete_doctor_appointment}</p>
            <p class="mt-2 font-medium text-gray-700">Your subscription billing will be reduced by ${currencyPrefix}${doctorPrice}${currencyPostfix} from the next billing cycle.</p>
          `,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.performDoctorDelete(index);
          }
        });
    },

    async performDoctorDelete(index) {
      try {
        const response = await get("doctor_delete", {
          id: this.doctorsList.data[index - 1].ID,
        });

        if (response.data.status === true) {
          if (this.getUserRole() === "administrator" || this.getUserRole() === 'clinic_admin') {
            await this.$store.dispatch("userDataModule/fetchUserData", {});
          }
          this.doctorsList.data.splice(index - 1, 1);
          displayMessage(response.data.message);
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    // Utility methods
    getInitials(name) {
      if (!name) return "-";
      return name
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase())
        .join("");
    },

    defaultZoomConfifurationValue() {
      return {
        enableTeleMed: "off",
        api_key: "",
        api_secret: "",
        zoom_id: "",
        video_price: "",
      };
    },

    closeDoctorForm() {
      this.showDoctorForm = false;
      this.selectedDoctorId = null;
    },
    
    // Get the additional doctor price from subscription settings
    getDoctorPrice() {
      // Default price if settings don't exist
      const defaultPrice = 10;
      
      try {
        // Check if subscription settings exist and have doctorPrice
        console.log(this?.$store?.state?.userDataModule?.user?.subscription_settings?.doctorPrice,'ritesh');
        
        if (this?.$store?.state?.userDataModule?.user?.subscription_settings?.doctorPrice) {
          return this.$store.state.userDataModule.user.subscription_settings.doctorPrice;
        }
        
        // Return default price if not found in settings
        return defaultPrice;
      } catch (error) {
        console.error('Error getting doctor price:', error);
        return defaultPrice;
      }
    },
  },

  computed: {
    clinics() {
      return this.$store.state.clinic;
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },

    specialization() {
      const staticData = this.$store.state.staticDataModule.static_data;
      return staticData.specialization || [];
    },

    getSpeciality() {
      return (specialties) => {
        if (!specialties || !specialties.length) return "-";

        if (typeof specialties === "string") return specialties;

        return specialties.map((spec) => spec.label).join(", ");
      };
    },
  },
};
</script>

<style scoped>
.modal-header {
  min-height: unset;
}

/* Add any additional custom styles here */
</style>
