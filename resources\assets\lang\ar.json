{"common": {"kivicare_Management_system": "KiviCare - نظام إدارة العيادات والمرضى (EHR)", "no_appointments": "لم يتم العثور على مواعيد", "loading": "تحميل ...", "cancel": "إلغاء", "date": "التاريخ", "close": "إغلاق", "closed": "مغلق", "select_option": "- حدد خيارًا -", "all": "الكل", "back_to_wordpress": "رجوع إلى Wordpress ", "update": "لم يتم العثور على مواعيد0", "my_profile": "لم يتم العثور على مواعيد1", "change_password": "لم يتم العثور على مواعيد2", "logout": "لم يتم العثور على مواعيد3", "full_screen": "لم يتم العثور على مواعيد4", "warning_zoom_configuration": "لم يتم العثور على مواعيد5", "zoom_configuration_link": "لم يتم العثور على مواعيد6", "dob": "لم يتم العثور على مواعيد7", "dob_required": "لم يتم العثور على مواعيد8", "gender": "لم يتم العثور على مواعيد9", "gender_required": "تحميل ...0", "male": "تحميل ...1", "female": "تحميل ...2", "other": "تحميل ...3", "service": "تحميل ...4", "services": "تحميل ...5", "sr_no": "تحميل ...6", "item_name": "تحميل ...7", "price": "تحميل ...8", "quantity": "تحميل ...9", "total": "إلغاء0", "no_records_found": "إلغاء1", "_note": "إلغاء2", "note": "إلغاء3", "status": "إلغاء4", "action": "إلغاء5", "title": "إلغاء6", "name": "إلغاء7", "doctor": "إلغاء8", "doctors": "إلغاء9", "patient": "التاريخ0", "fname": "التاريخ1", "fname_required": "التاريخ2", "lname": "التاريخ3", "lname_required": "التاريخ4", "email": "التاريخ5", "email_required": "التاريخ6", "password": "التاريخ7", "pwd_required": "التاريخ8", "repeat_pwd": "التاريخ9", "repeat_password_required": "إغلاق0", "pwd_not_match": "إغلاق1", "login_btn": "إغلاق2", "sign_up": "إغلاق3", "no": "إغلاق4", "dr": "دكتور", "filters": "فلاتر", "back": "رجوع", "save": "<PERSON><PERSON><PERSON>", "invalid_email": "تنسيق بريد إلكتروني غير صالح", "active": "نشط", "inactive": "غير نشط", "name_required": "مطلو<PERSON> الاسم", "email_address": "عنوان البريد الإلكتروني", "contact_info": "معلومات جهة الاتصال", "settings": "فلاتر0", "fname_validation_1": "فلاتر1", "fname_validation_2": "فلاتر2", "lname_validation_1": "فلاتر3", "lname_validation_2": "فلاتر4", "contact": "فلاتر5", "contact_required": "فلاتر6", "contact_validation_1": "فلاتر7", "contact_validation_2": "فلاتر8", "telemed": "فلاتر9", "to": "رجوع0", "time": "رجوع1", "contact_no": "رجوع2", "contact_num_required": "رجوع3", "city": "رجوع4", "city_required": "رجوع5", "city_validation_1": "رجوع6", "city_validation_2": "رجوع7", "state": "رجوع8", "state_validation_1": "رجوع9", "state_validation_2": "حفظ0", "country": "حفظ1", "country_required": "حفظ2", "country_validation_1": "حفظ3", "country_validation_2": "حفظ4", "address": "حفظ5", "address_required": "حفظ6", "postal_code": "حفظ7", "postal_code_required": "حفظ8", "postal_code_validation_1": "حفظ9", "postal_code_validation_2": "تنسيق بريد إلكتروني غير صالح0", "profile": "تنسيق بريد إلكتروني غير صالح1", "static_data": "تنسيق بريد إلكتروني غير صالح2", "handle_request": "تنسيق بريد إلكتروني غير صالح3", "email_to_get_help": "تنسيق بريد إلكتروني غير صالح4", "note_options": "تنسيق بريد إلكتروني غير صالح5", "note_1": "تنسيق بريد إلكتروني غير صالح6", "note_2": "تنسيق بريد إلكتروني غير صالح7", "wp_rollback": "تنسيق بريد إلكتروني غير صالح8", "plugin": "تنسيق بريد إلكتروني غير صالح9", "keep_improving": "نشط0", "currency_setting": "نشط1", "module": "نشط2", "i_understand": "نشط3", "version": "نشط4", "read_notice": "الرجاء قراءة هذا السجل أدناه قبل المضي قدمًا:", "faced_issue": "هل تواجه مشكلات؟", "if_use_older_version": "إذا كنت تواجه مشكلات مع هذا الإصدار وتريد متابعة الإصدار القديم ، فيرجى التثبيت والاستخدام", "check_video": "للانتقال السلس إلى الإصدار الجديد ، تحقق من دليل الفيديو التالي: ", "kivicare_v2": "ترقية Kivicare V2.0.0", "appointment_flow": "تدفق المواعيد", "basic_details": "التفاصيل الأساسية"}, "dashboard": {"dashboard": "لوحة المعلومات", "total_patients": "إجمالي المرضى", "total_visited_patients": "إجمالي المرضى الذين تمت زيارتها", "total_doctors": "إجمالي الأطباء", "total_clinic_doctors": "إجمالي أطباء العيادة", "total_appointments": "إجمالي المواعيد", "total_clinic_appointments": "إجمالي مواعيد العيادة", "latest_appointments": "أح<PERSON><PERSON> المواعيد", "reload": "إعادة تحميل", "view_all": "عر<PERSON> الكل", "weekly_appointments": "إجمالي المرضى0", "weekly_total_appointments": "إجمالي المرضى1", "today_appointment_list": "إجمالي المرضى2", "total_revenue": "إجمالي المرضى3", "total_clinic_revenue": "إجمالي المرضى4", "total_generated_revenue": "إجمالي المرضى5", "filter": "إجمالي المرضى6", "reset": "إجمالي المرضى7", "total_today_appointments": "إجمالي المرضى8", "total_service": "إجمالي المرضى9", "patients": "إجمالي المرضى الذين تمت زيارتها0", "medical_dashboard": "إجمالي المرضى الذين تمت زيارتها1"}, "doctor": {"doctor_name": "اسم الدكتور", "doctor_specialization_required": "مطلوب تخصص دكتور", "experience_year": "الخبرة (في العام)", "address_details": "تفاصيل العنوان", "degree": "الدرجة المطلوبة", "degree_required": "الدرجة العلمية مطلوبة", "degree_validation_1": "تسمح الدرجة فقط بقيمة الحرف", "university": "الجامعة", "university_required": "الجامعة مطلوبة", "university_validation": "الجامعة تسمح فقط بقيمة الحرف ", "year": "مطلوب تخصص دكتور0", "select_year": "مطلوب تخصص دكتور1", "year_required": "مطلوب تخصص دكتور2", "college_university": "مطلوب تخصص دكتور3", "api_key": "مطلوب تخصص دكتور4", "api_secret": "مطلوب تخصص دكتور5", "api_secret_required": "مطلوب تخصص دكتور6", "api_key_required": "مطلوب تخصص دكتور7", "zoom_configuration_guide": "مطلوب تخصص دكتور8", "zoom_step1": "مطلوب تخصص دكتور9", "zoom_step2": "الخبرة (في العام)0", "zoom_step3": "الخبرة (في العام)1", "zoom_step4": "الخبرة (في العام)2", "zoom_step5": "الخبرة (في العام)3", "other_detail": "الخبرة (في العام)4", "consultation_fees": "الخبرة (في العام)5", "video_consultation_fees": "الخبرة (في العام)6", "doctor_fees_required": "الخبرة (في العام)7", "zoom_market_place_portal": "الخبرة (في العام)8", "create_app": "الخبرة (في العام)9", "doctors_list": "تفاصيل العنوان0", "other_details": "تفاصيل العنوان1", "extra_detail": "تفاصيل العنوان2", "add_doctor": "تفاصيل العنوان3", "edit_profile": "تفاصيل العنوان4", "basic_information": "تفاصيل العنوان5", "basic_settings": "تفاصيل العنوان6", "type": "تفاصيل العنوان7", "type_required": "النوع مطلوب", "fees_type": "نوع الرسوم", "range": "النطاق", "fixed": "ثابت", "fees": "الرسوم", "fees_type_required": "نوع الرسوم مطلوب", "doc_fee_required": "رسوم الطبيب مطلوبة", "doc_fee_validation_1": "يجب أن تكون رسوم الطبيب أكبر من صفر", "doc_fee_validation_2": "يجب أن تكون رسوم الطبيب بين 0 إلى 1000000000000000000", "doc_fee_validation_3": " رسوم الطبيب مطلوب الحد الأدنى للرسوم والحد الأقصى للرسوم", "doc_fee_validation_4": "نوع الرسوم0", "doc_fee_validation_5": "نوع الرسوم1", "doc_fee_validation_6": "نوع الرسوم2", "qualification_information": "نوع الرسوم3", "qualification_speciality_details": "نوع الرسوم4", "doctor_working_days_sessions": "نوع الرسوم5", "charge_n_doc_selection": "نوع الرسوم6", "doc_field_customization": "نوع الرسوم7"}, "patient": {"patient_name": "اسم المريض", "add_patient": "إضافة مريض", "patients_lists": "قوائم المرضى", "medical_report": "تقرير طبي", "add_medical_report": "إضافة تقرير طبي", "upload_report": "تحميل تقرير"}, "clinic": {"clinic": "العيادة", "receptionist": "موظف الاستقبال", "receptionists_list": "قائمة موظفي الاستقبال", "add_receptionist": "إضافة موظف استقبال", "clinic_name": "اسم العيادة", "clinic_info": "معلومات العيادة", "clinic_profile": "ملف تعريف العيادة", "add_clinic": "إضافة عيادة", "admin_profile": "ملف تعريف المسؤول", "clinic_admin_detail": "تفاصيل مسؤول العيادة", "clinic_name_validation_1": "موظف الاستقبال0", "clinic_name_validation_2": "موظف الاستقبال1", "select_clinic": "موظف الاستقبال2", "speciality": "موظف الاستقبال3", "specialties": "موظف الاستقبال4", "specialities": "موظف الاستقبال5", "note_specialization": "موظف الاستقبال6", "clinic_specialities_required": "موظف الاستقبال7", "currency_prefix": "موظف الاستقبال8", "currency_postfix": "موظف الاستقبال9", "currency_decimals": "قائمة موظفي الاستقبال0", "profile_img": "قائمة موظفي الاستقبال1", "doctor_record_not_found": "قائمة موظفي الاستقبال2", "blood_group": "قائمة موظفي الاستقبال3", "select_blood_group": "قائمة موظفي الاستقبال4", "update_profile": "قائمة موظفي الاستقبال5"}, "appointments": {"appointment": "مو<PERSON><PERSON>", "appointments": "موا<PERSON>يد", "description": "الوصف", "booked": "<PERSON><PERSON><PERSON>", "cancelled": "تم الإلغاء", "arrived": "تم الوصول", "check_in": "تسجيل الوصول", "check_out": "تسجيل المغادرة", "start": "بدء", "join": "الانضمام", "doc_required": "مواعيد0", "visit_type_required": "مواعيد1", "appointment_date": "مواعيد2", "appointment_date_required": "مواعيد3", "select_status": "مواعيد4", "status_required": "مواعيد5", "available_slot": "مواعيد6", "session": "مواعيد7", "no_time_slots_found": "مواعيد8", "time_slot_required": "مواعيد9", "appointment_details": "الوصف0", "appointment_type": "الوصف1", "completed": "الوصف2", "appointment_time": "الوصف3", "appointment_time_required": "الوصف4", "book_appointment": "الوصف5", "today_appointment": "الوصف6", "tomorrow_appointment": "الوصف7", "appointment_booking": "الوصف8", "available_appointments_on": "الوصف9", "appointment_visit_type_required": "حجز0", "appointment_detail": "حجز1", "save_appointment": "حجز2", "appointment_list": "حجز3"}, "clinic_schedule": {"schedule": "الجدول الزمني", "holiday_of": "عطلة", "module_type_required": "مطلوب نوع الوحدة النمطية", "schedule_date": "تاريخ الجدول", "schedule_date_required": "تاريخ الجدول مطلوب", "holiday_list": "قائمة العطلات"}, "doctor_session": {"doc_sessions": "جلسات الطبيب", "session_doc_required": "مطلوب طبيب الجلسة", "doc_already_added": "تمت إضافة طبيب مختار بالفعل في جلسة أخرى", "week_days": "أيام الأسبوع", "days_required": "الأيام المطلوبة", "days_already_exist": "الأيام المحددة موجودة بالفعل في الجلسة الأخرى", "morning_session": "الجلسة الصباحية", "start_time_required": "وقت البدء مطلوب", "start_time_smaller_then_end": "يجب أن يكون وقت البدء أصغر من وقت الانتهاء", "end_time_required": "وقت الانتهاء مطلوب", "end_time_bigger_then_start": "مطلوب طبيب الجلسة0", "evening_session": "مطلوب طبيب الجلسة1", "start_time_smaller_then_first_session_end_time": "مطلوب طبيب الجلسة2", "set_session_for_doc": "مطلوب طبيب الجلسة3"}, "patient_encounter": {"encounters": "لقاءات", "encounter_dashboard": "لوحة معلومات اللقاء", "is_required": "مطلوب", "note_prescription": "ملاحظة: اكتب واضغط على Enter لإنشاء وصفة طبية جديدة", "frequency": "التردد", "frequency_required": "التردد مطلوب", "duration_Days": "المدة (بالأيام)", "duration_required": "المدة مطلوبة", "instruction": "التعليمات", "duration": "المدة", "no_prescription_found": "لوحة معلومات اللقاء0", "add_prescription": "لوحة معلومات اللقاء1", "encounter_date": "لوحة معلومات اللقاء2", "encounter_date_required": "لوحة معلومات اللقاء3", "encounter_module": "لوحة معلومات اللقاء4", "prescription": "لوحة معلومات اللقاء5", "encounter_details": "لوحة معلومات اللقاء6"}, "medical_records": {"problem_type": "نوع المشكلة", "problem_start_date_required": "تاريخ بدء المشكلة مطلوب", "problem_start_date": "تاريخ بدء المشكلة", "problem_end_date": "تاريخ انتهاء المشكلة", "problem_outcome": "نتيجة المشكلة", "medical_records": "السجلات الطبية", "add_medical_problems": "إضافة مشاكل طبية"}, "reports": {"reports": "التقارير", "filter_by": "تصفية حسب", "clinic_revenue_overall": "إيرادات العيادة (بشكل عام)", "clinic_revenue_detail": "إيرادات العيادة (التفاصيل)", "clinic_doctor_revenue": "إيرادات طبيب العيادة", "prescription_module": "الوحدة الطبية", "report_required": "التقرير مطلوب."}, "patient_front_widget": {"specialization": "التخصص", "username_email": "اسم المستخدم أو البريد الإلكتروني", "fill_form": "يرجى ملء هذا النموذج لإنشاء حساب."}, "service": {"service_list": "Lista de servicios", "service_category": "Categoría de servicio", "service_category_required": "Se requiere categoría de servicio", "note_category": "Nota: Escriba y presione enter para agregar una nueva categoría", "service_name": "Nombre del servicio", "service_name_required": "Se requiere el nombre del servicio", "service_validation": "La longitud del nombre del servicio debe tener entre 2 y 100 caracteres", "charges": "Cargos ", "service_charge": "cargo por servicio del módulo de servicio. ", "service_charges_required": "Se requieren cargos por servicio", "service_charge_length": "Categoría de servicio0", "select_all": "Categoría de servicio1"}, "patient_bill": {"invoice_id": "معرّف الفاتورة:", "created_at": "تم الإنشاء في:", "payment_status": "حالة الدفع:", "paid": "مدفوعة", "unpaid": "غير مدفوعة", "patient_details": "تفاصيل المريض", "amount_due": "المبلغ المستحق", "print": "طباعة", "service_required": "الخدمة مطلوبة", "price_required": "السعر مطلوب", "prize_greater_then_0": "تم الإنشاء في:0", "prize_between_number": "تم الإنشاء في:1", "quantity_required": "تم الإنشاء في:2", "please_add_bill_items": "تم الإنشاء في:3", "bill_total_required": "تم الإنشاء في:4", "discount": "تم الإنشاء في:5", "discount_amount": "تم الإنشاء في:6", "discount_required": "تم الإنشاء في:7", "discount_greater_then_0": "تم الإنشاء في:8", "discount_less_then_total_bill_amount": "تم الإنشاء في:9", "payable_amount": "حالة الدفع:0", "bill_title": "حالة الدفع:1", "bill_title_required": "حالة الدفع:2", "bill_items": "حالة الدفع:3", "grand_total": "حالة الدفع:4", "grand_total_required": "حالة الدفع:5", "print_bill": "حالة الدفع:6", "billing_records": "حالة الدفع:7", "add_bill": "حالة الدفع:8", "patient_required": "حالة الدفع:9", "encounter_close": "مدفوعة0", "bill_details": "مدفوعة1", "other_info": "مدفوعة2", "patients_encounter_list": "مدفوعة3", "bills": "مدفوعة4", "payment_setting": "مدفوعة5", "woocommerce_payment_gateway": "مدفوعة6", "amount": "مدفوعة7", "items": "مدفوعة8", "notes": "مدفوعة9", "invoice_n_payment": "غير مدفوعة0", "currency": "غير مدفوعة1"}, "settings": {"general": "عام", "holidays": "الإجازات", "configurations": "التكوينات", "email_template": "قالب البريد الإلكتروني", "sms_template": "قالب SMS", "listings": "القوائم", "custom_field": "<PERSON>ق<PERSON> مخصص", "payment": "الدفع", "new_setting": "إعدادات محسنة جديدة بإعدادات متنوعة مثل البريد الإلكتروني والفاتورة والعملة وما إلى ذلك", "pro_settings": "إعدادات Pro", "language_settings": "إعدادات اللغة"}, "pro_setting": {"set_site_logo": "تعيين شعار الموقع", "set_language": "<PERSON>ب<PERSON> اللغة", "set_theme_color": "تعيين لون الموضوع", "rtl_mode": "وضع RTL", "on": "تشغيل", "twilo_sms_configration": "Twilo SMS Configration", "account_sid": "ACCOUNT SID", "auth_token": "AUTH TOKEN", "phone_number": "رقم الهاتف (اختياري)", "twilo_sms_guide": "دليل الرسائل القصيرة Twilo", "twilio_step_1": "<PERSON><PERSON><PERSON> اللغة0", "twilo_sms_portal": "<PERSON>ب<PERSON> اللغة1", "twilio_step_2": "<PERSON><PERSON><PERSON> اللغة2", "get_console": "<PERSON><PERSON><PERSON> اللغة3", "unique_sid": "<PERSON>ب<PERSON> اللغة4", "twilio_step_3": "<PERSON><PERSON><PERSON> اللغة5", "twilio_step_4": "<PERSON><PERSON><PERSON> اللغة6", "head_on_console": "<PERSON><PERSON><PERSON> اللغة7", "phone_msg_sid": "<PERSON><PERSON><PERSON> اللغة8"}, "custom_field": {"label_name_required": "اسم التصنيف مطلوب", "label_name_validation": "اسم التصنيف يسمح فقط بالقيمة الأبجدية", "where_it_look_like": "<PERSON>ين يبدو", "shows_in_doc_creation_form": "يظهر في نموذج إنشاء الطبيب", "shows_in_patient_encounter_dashboard": "يظهر في لوحة معلومات لقاء المريض", "shows_in_patient_creation_form": "يظهر في نموذج إنشاء المريض", "filed_name": "اسم الملف:", "invalid_label_name": " اسم التصنيف غير صالح", "label_required": "الملصق مطلوب", "field_name_used": "اسم الحقل مستخدم بالفعل.", "input_type": "اسم التصنيف يسمح فقط بالقيمة الأبجدية0", "input_type_required": "اسم التصنيف يسمح فقط بالقيمة الأبجدية1", "placeholder": "اسم التصنيف يسمح فقط بالقيمة الأبجدية2", "options": "اسم التصنيف يسمح فقط بالقيمة الأبجدية3", "validation": "اسم التصنيف يسمح فقط بالقيمة الأبجدية4", "mandatory_field": "اسم التصنيف يسمح فقط بالقيمة الأبجدية5", "custom_field_list": "اسم التصنيف يسمح فقط بالقيمة الأبجدية6", "add_custom_field": "اسم التصنيف يسمح فقط بالقيمة الأبجدية7"}, "setup_wizard": {"previous": "السابق", "add_session_details": "إضافة تفاصيل الجلسة", "session_doctors": "أطباء الجلسة", "days": "الأيام", "no_sessions_found": "لم يتم العثور على جلسات", "time_slot_minute": "فترة زمنية (بالدقيقة)", "open_time": "وقت الفتح", "close_time": "وقت الإغلاق", "session_demo": "عرض الجلسة", "invalid_time_slot": "تم العثور على فترة زمنية غير صالحة. الوقت غير الصالح هو", "doctor_list": "إضافة تفاصيل الجلسة0", "kivicare_ehr": "إضافة تفاصيل الجلسة1", "prev": "إضافة تفاصيل الجلسة2"}, "notification": {"notification": "إرسال بريد إلكتروني تجريبي", "test_sender_email_required": "اختبار البريد الإلكتروني للمرسل مطلوب", "test_content": "اخت<PERSON><PERSON><PERSON> المحتوى", "test_content_required": "اختبار المحتوى مطلوب", "email_notification": "تمكين / تعطيل إعلام البريد الإلكتروني.", "forbidden_403": "403 | محرم"}, "static_data": {"listing_data": "بيانات القائمة", "terms_n_condition": "الشروط والأحكام", "version_update": "تحديث الإصدار (V2.0.0)", "new_filters_n_view": "عوامل التصفية المحسنة الجديدة وعرضها", "booking_widget_updated": "تم تحديث أداة الحجز", "visit_type_replaced": "تم استبدال نوع الزيارة بالخدمات (يرجى التحقق من علامة تبويب الخدمة)", "appointment_flow_update": "فحص الموعد- تم تحديث تدفق الدخول والمغادرة"}, "widgets": {"doc_not_found": "لم يتم العثور على الطبيب", "zoom_config": "تكوين التكبير", "terms_condition": "الشروط والأحكام", "date_required": "مطلوب التاريخ", "current_pwd": "كلمة المرور الحالية", "current_pwd_required": "كلمة المرور الحالية مطلوبة", "new_pwd": "كلمة مرور جديدة", "appointment_info": "معلومات الموعد", "available_slots": "الفتحات المتاحة", "service_detail": "تفاصيل الخدمة", "no_service_detail_found": "تكوين التكبير0", "book_now": "تكوين التكبير1", "registration_success": "تكوين التكبير2", "more_detail": "تكوين التكبير3", "username_email_required": "تكوين التكبير4", "new_pwd_required": "تكوين التكبير5", "confirm_pwd": "تكوين التكبير6", "confirm_pwd_required": "تكوين التكبير7", "pwd_validation": "تكوين التكبير8", "home": "تكوين التكبير9", "change_pwd": "الشروط والأحكام0", "logging_out": "الشروط والأحكام1", "total_visits": "الشروط والأحكام2", "upcoming_visits": "الشروط والأحكام3", "example_component": "الشروط والأحكام4", "email_to_get_help_1": "الشروط والأحكام5", "email_to_get_help_2": "الشروط والأحكام6", "email_to_get_help_3": "الشروط والأحكام7", "feedback_note": "الشروط والأحكام8", "imp_version_update": "الشروط والأحكام9", "replace_appointment": "مطلوب التاريخ0", "option_as": "مطلوب التاريخ1", "service_type": "مطلوب التاريخ2", "add_charges": "مطلوب التاريخ3", "manage_doctor": "مطلوب التاريخ4", "send_test_email": "مطلوب التاريخ5"}}