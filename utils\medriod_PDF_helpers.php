<?php

use App\baseClasses\KCBase;

use Mpdf\Mpdf;
use Mpdf\MpdfException;

function get_pdf_password_from_patient_id_only_dob($patient_id) {
    $patient_basic_data = json_decode(get_user_meta((int) $patient_id, 'basic_data', true));
    $dob = !empty($patient_basic_data->dob) ? $patient_basic_data->dob : "";

    // If DOB is empty, generate a default password
    if (empty($dob)) {
        return 'patient' . $patient_id;
    }

    // Convert date to ddmmyyyy format regardless of original format
    // Try to create a DateTime object - this handles various separators
    // This assumes the format is dd/mm/yyyy or dd-mm-yyyy
    $date_obj = DateTime::createFromFormat('d/m/Y', $dob);
    if (!$date_obj) {
        $date_obj = DateTime::createFromFormat('d-m-Y', $dob);
    }

    // If we successfully created a date object, format it
    if ($date_obj) {
        return $date_obj->format('dmY'); // Format as ddmmyyyy
    } else {
        // Fallback - just remove common separators
        $clean_dob = str_replace(['/', '-', '.'], '', $dob);

        // Check if we have a string that might be in yyyymmdd format (common in some systems)
        if (strlen($clean_dob) == 8 && is_numeric($clean_dob)) {
            // If it starts with 19xx or 20xx, it's likely yyyymmdd
            if (substr($clean_dob, 0, 2) == '19' || substr($clean_dob, 0, 2) == '20') {
                // Convert from yyyymmdd to ddmmyyyy
                return substr($clean_dob, 6, 2) . substr($clean_dob, 4, 2) . substr($clean_dob, 0, 4);
            }
        }

        // If not in yyyymmdd format, return as is
        return $clean_dob;
    }
}

function get_pdf_password_from_patient_id($patient_id) {
    $patient_data = get_userdata($patient_id);
    $full_name = $patient_data->display_name??"";
    $patient_basic_data = json_decode(get_user_meta((int) $patient_id, 'basic_data', true));
    $dob = !empty($patient_basic_data->dob) ? $patient_basic_data->dob : "";

    // Convert date to desired format regardless of original format
    if (!empty($dob)) {
        // Try to create a DateTime object - this handles various separators
        // This assumes the format is dd/mm/yyyy or dd-mm-yyyy
        $date_obj = DateTime::createFromFormat('d/m/Y', $dob);
        if (!$date_obj) {
            $date_obj = DateTime::createFromFormat('d-m-Y', $dob);
        }

        // If we successfully created a date object, format it
        if ($date_obj) {
            $dob = $date_obj->format('dmY');
        } else {
            // Fallback - just remove common separators
            $dob = str_replace(['/', '-', '.'], '', $dob);
        }
    }

    // Handle names more robustly
    $all_parts = explode(' ', strtolower($full_name));
    $name_password = '';

    // Remove any empty parts (caused by multiple spaces)
    $all_parts = array_filter($all_parts, function($part) {
        return !empty($part);
    });

    // Reset array keys to ensure sequential indexing
    $all_parts = array_values($all_parts);

    // Combine parts until we have 4 characters or run out of parts
    foreach ($all_parts as $part) {
        $remaining_length = 4 - strlen($name_password);

        if ($remaining_length <= 0) {
            break; // We already have 4 or more characters
        }

        $name_password .= substr($part, 0, $remaining_length);
    }

    // If we still have fewer than 4 characters, pad with 'x' (or handle as needed)
    while (strlen($name_password) < 4) {
        $name_password .= 'x';
    }

    // Ensure we only take exactly 4 characters
    $name_password = substr($name_password, 0, 4);

    return $name_password . $dob;
}
/**
 * Generate a password-protected PDF from HTML content.
 *
 * @param string $title PDF document title
 * @param string $content HTML content to be converted to PDF
 * @param string $password Patient password (first 4 letters will be used for password)
 * @param string $outputType Output type: 'I' (inline), 'D' (download), 'F' (file save), 'S' (string)
 * @param string|null $filePath File path to save PDF when outputType is 'F'
 * @param string $filename Custom filename for the PDF (without extension)
 *
 * @return string|bool Returns string when outputType is 'S', true when successful file save, false on error
 * @throws MpdfException When PDF generation fails
 */
function generate_password_protected_PDF(string $title, string $content, string $password, string $outputType = 'D', ?string $filePath = null, string $filename = 'protected_ehr')
{
    // Validate parameters
    if ($outputType === 'F' && empty($filePath)) {
        throw new \InvalidArgumentException('File path must be provided when output type is "F"');
    }
    if (!in_array($outputType, ['I', 'D', 'F', 'S'])) {
        throw new \InvalidArgumentException('Invalid output type. Must be one of: I, D, F, S');
    }

    try {
        // Extract header and footer content using regular expressions
        $headerContent = '';
        $footerContent = '';
        $mainContent = $content;

        // Extract header - look for div with class="header" or HTML5 <header> tag
        if (preg_match('/<(?:div\s+class=["\']header["\']|header)>(.*?)<\/(?:div|header)>/is', $content, $headerMatches)) {
            $headerContent = $headerMatches[1];
            // Remove header from the main content
            $mainContent = preg_replace('/<(?:div\s+class=["\']header["\']|header)>.*?<\/(?:div|header)>/is', '', $mainContent);
        }

        // Extract footer - look for div with class="footer" or HTML5 <footer> tag
        if (preg_match('/<(?:div\s+class=["\']footer["\']|footer)>(.*?)<\/(?:div|footer)>/is', $content, $footerMatches)) {
            $footerContent = $footerMatches[1];
            // Remove footer from the main content
            $mainContent = preg_replace('/<(?:div\s+class=["\']footer["\']|footer)>.*?<\/(?:div|footer)>/is', '', $mainContent);
        }

        // Initialize mPDF with full-width header/footer settings
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 0,
            'margin_right' => 0,
            'margin_top' => 0,
            'margin_bottom' => 0,
            'margin_header' => 0,     // Set to 0 to allow header to extend to edge
            'margin_footer' => 0,     // Set to 0 to allow footer to extend to edge
            'setAutoTopMargin' => 'stretch',  // Automatically adjust top margin
            'setAutoBottomMargin' => 'stretch', // Automatically adjust bottom margin
        ]);

        // Set document metadata
        $mpdf->SetTitle($title);
        $mpdf->SetAuthor('Medroid EHR System');
        $mpdf->SetCreator('Medroid PDF Generator');

        // Set header if found - with full width styling
        if (!empty($headerContent)) {
            $headerHtml = '<div style="width: 100%; margin: 0; padding: 0; position: relative;">' . $headerContent . '</div>';
            $mpdf->SetHTMLHeader($headerHtml);
        }

        // Set footer if found - with full width styling
        if (!empty($footerContent)) {
            $footerHtml = '<div style="width: 100%; margin: 0; padding: 0; position: relative;">' . $footerContent . '</div>';
            $mpdf->SetHTMLFooter($footerHtml);
        }

        // Add main content to PDF (without the header and footer)
        $mpdf->WriteHTML($mainContent);

        // Set PDF security with patient-specific password
        $mpdf->SetProtection(['copy', 'print'], $password, 'adminSecret123');

        // Handle different output types
        switch ($outputType) {
            case 'F':
                $mpdf->Output($filePath, 'F');
                return true;
            case 'S':
                return $mpdf->Output('', 'S');
            case 'I':
                $mpdf->Output($filename . '.pdf', 'I');
                return true;
            case 'D':
            default:
                $mpdf->Output($filename . '.pdf', 'D');
                return true;
        }
    } catch (MpdfException $e) {
        // Log the error for debugging
        error_log('PDF Generation Error: ' . $e->getMessage());
        // Re-throw the exception
        throw $e;
    }
}

/**
 * Convert an existing WordPress media file to a password-protected PDF.
 *
 * @param int $attachment_id The ID of the file in WordPress media library
 * @param int $patient_id The ID of the patient for password generation
 * @param string|null $custom_filename Optional custom filename for the new PDF (without extension)
 * @param bool $replace_original Whether to replace the original file metadata (default: false)
 *
 * @return array|WP_Error Array with status and new attachment ID or WP_Error on failure
 */
function convert_to_protected_pdf($attachment_id, $patient_id, $custom_filename = null, $replace_original = false) {
    // Validate input parameters
    if (empty($attachment_id) || empty($patient_id)) {
        return new WP_Error('invalid_input', esc_html__('Attachment ID and patient ID are required', 'kc-lang'));
    }

    // Check if the attachment exists
    $attachment = get_post($attachment_id);
    if (!$attachment) {
        return new WP_Error('invalid_attachment', esc_html__('Attachment does not exist', 'kc-lang'));
    }

    // Get file path
    $file_path = get_attached_file($attachment_id);
    if (!file_exists($file_path)) {
        return new WP_Error('file_not_found', esc_html__('Original file not found', 'kc-lang'));
    }

    // Get file info
    $file_info = wp_check_filetype(basename($file_path));
    $file_type = $file_info['type'];

    // Get password for the PDF
    $pdf_password = get_pdf_password_from_patient_id($patient_id);

    // Get patient details for metadata
    $patient_name = get_user_meta($patient_id, 'first_name', true) . ' ' . get_user_meta($patient_id, 'last_name', true);

    // Set up new filename
    $upload_dir = wp_upload_dir();
    $original_filename = pathinfo($file_path, PATHINFO_FILENAME);
    $base_filename = $custom_filename ? $custom_filename : $original_filename . '-protected';

    // Generate unique filename
    $counter = 1;
    $new_filename = $base_filename . '.pdf';
    $new_file_path = $upload_dir['path'] . '/' . $new_filename;

    while (file_exists($new_file_path)) {
        $new_filename = $base_filename . '-' . $counter . '.pdf';
        $new_file_path = $upload_dir['path'] . '/' . $new_filename;
        $counter++;
    }

    try {
        // Convert different file types to PDF with password protection
        switch ($file_type) {
            case 'application/pdf':
                // If it's already a PDF, just add password protection
                $html_content = '';
                $temp_pdf_path = $file_path;

                // Initialize mPDF with existing PDF
                $mpdf = new Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4',
                ]);

                // Import existing PDF pages
                $page_count = $mpdf->SetSourceFile($temp_pdf_path);
                for ($i = 1; $i <= $page_count; $i++) {
                    $tplId = $mpdf->ImportPage($i);
                    $mpdf->UseTemplate($tplId);
                    if ($i < $page_count) {
                        $mpdf->AddPage();
                    }
                }

                // Set protection
                $mpdf->SetProtection(['copy', 'print'], $pdf_password, 'adminSecret123');
                $mpdf->Output($new_file_path, 'F');
                break;

            case 'image/jpeg':
            case 'image/jpg':
            case 'image/png':
                // For images, create a PDF with the image
                $image_size = getimagesize($file_path);
                $image_width = $image_size[0];
                $image_height = $image_size[1];

                // Determine orientation
                $orientation = $image_width > $image_height ? 'L' : 'P';

                // Create HTML content with the image
                $html_content = "
                    <html>
                    <head>
                        <style>
                            body { margin: 0; padding: 0; }
                            .image-container { text-align: center; }
                            img { max-width: 100%; height: auto; }
                        </style>
                    </head>
                    <body>
                        <div class='image-container'>
                            <img src='{$file_path}' />
                        </div>
                    </body>
                    </html>
                ";

                // Create PDF with password
                $result = generate_password_protected_PDF(
                    "Patient Document - {$patient_name}",
                    $html_content,
                    $pdf_password,
                    'F', // Output type 'F' to save as file
                    $new_file_path, // File path to save the PDF
                    $base_filename
                );
                break;

            case 'application/msword': // .doc
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document': // .docx
                // For Word documents, convert to HTML and then to PDF
                // You'll need additional libraries for this like PhpWord
                // This is a simplified example
                if (class_exists('PhpOffice\PhpWord\IOFactory')) {
                    $phpWord = \PhpOffice\PhpWord\IOFactory::load($file_path);
                    $htmlWriter = new \PhpOffice\PhpWord\Writer\HTML($phpWord);

                    $temp_html_file = $upload_dir['path'] . '/' . $original_filename . '-temp.html';
                    $htmlWriter->save($temp_html_file);

                    // Read the HTML content
                    $html_content = file_get_contents($temp_html_file);

                    // Create PDF with password
                    $result = generate_password_protected_PDF(
                        "Patient Document - {$patient_name}",
                        $html_content,
                        $pdf_password,
                        'F', // Output type 'F' to save as file
                        $new_file_path, // File path to save the PDF
                        $base_filename
                    );

                    // Remove temporary HTML file
                    unlink($temp_html_file);
                } else {
                    return new WP_Error('missing_library', esc_html__('PhpWord library is required to convert Word documents', 'kc-lang'));
                }
                break;

            default:
                // For unsupported file types
                return new WP_Error('unsupported_file_type', esc_html__('Unsupported file type: ' . $file_type, 'kc-lang'));
        }

        // Prepare the file for WordPress Media Library
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');

        // File type and metadata
        $attachment_data = [
            'guid'           => $upload_dir['url'] . '/' . $new_filename,
            'post_mime_type' => 'application/pdf',
            'post_title'     => $base_filename,
            'post_content'   => '',
            'post_status'    => 'inherit',
        ];

        // Insert the file into the WordPress Media Library
        $new_attachment_id = wp_insert_attachment($attachment_data, $new_file_path);

        if (is_wp_error($new_attachment_id)) {
            return $new_attachment_id;
        }

        // Generate and update metadata
        $attachment_metadata = wp_generate_attachment_metadata($new_attachment_id, $new_file_path);
        wp_update_attachment_metadata($new_attachment_id, $attachment_metadata);

        // Store password information in attachment metadata
        update_post_meta($new_attachment_id, '_kc_pdf_password', $pdf_password);
        update_post_meta($new_attachment_id, '_kc_pdf_protected', true);
        update_post_meta($new_attachment_id, '_kc_original_attachment_id', $attachment_id);
        update_post_meta($new_attachment_id, '_kc_patient_id', $patient_id);

        // If replacing original, update references
        if ($replace_original) {
            // Store reference to new file in original attachment
            update_post_meta($attachment_id, '_kc_protected_version_id', $new_attachment_id);

            // Get the original file path
            $original_file_path = get_attached_file($attachment_id);

            // Delete the original file from the filesystem
            if (file_exists($original_file_path)) {
                wp_delete_file($original_file_path);
            }

            // Delete the original attachment
            wp_delete_attachment($attachment_id, true);

            // Add a note in the new attachment that it replaced an original
            update_post_meta($new_attachment_id, '_kc_replaced_original_id', $attachment_id);
        }

        return [
            'status' => 'success',
            'message' => esc_html__('File successfully converted to password-protected PDF', 'kc-lang'),
            'new_attachment_id' => $new_attachment_id,
            'file_path' => $new_file_path,
            'password' => $pdf_password
        ];

    } catch (\Exception $e) {
        return new WP_Error('pdf_generation_error', esc_html__('Error converting file: ' . $e->getMessage(), 'kc-lang'));
    }
}

/**
 * Batch convert multiple patient files to password-protected PDFs.
 *
 * @param array $file_ids Array of attachment IDs to convert
 * @param int $patient_id The patient ID for password generation
 * @param string|null $prefix Optional prefix for the new filenames
 *
 * @return array Results of the conversion process
 */
function batch_convert_patient_files_to_pdf($file_ids, $patient_id, $prefix = null) {
    if (!is_array($file_ids) || empty($file_ids) || empty($patient_id)) {
        return [
            'status' => 'error',
            'message' => esc_html__('Invalid input parameters', 'kc-lang')
        ];
    }

    $results = [
        'status' => 'success',
        'total' => count($file_ids),
        'successful' => 0,
        'failed' => 0,
        'converted_files' => [],
        'errors' => []
    ];

    foreach ($file_ids as $attachment_id) {
        // Create custom filename with prefix if provided
        $custom_filename = null;
        if ($prefix) {
            $original_file = get_post($attachment_id);
            $original_filename = pathinfo(get_attached_file($attachment_id), PATHINFO_FILENAME);
            $custom_filename = $prefix . '-' . $original_filename;
        }

        // Convert the file
        $conversion_result = convert_to_protected_pdf($attachment_id, $patient_id, $custom_filename);

        if (is_wp_error($conversion_result)) {
            $results['failed']++;
            $results['errors'][] = [
                'attachment_id' => $attachment_id,
                'error' => $conversion_result->get_error_message()
            ];
        } else {
            $results['successful']++;
            $results['converted_files'][] = [
                'original_id' => $attachment_id,
                'new_id' => $conversion_result['new_attachment_id'],
                'file_path' => $conversion_result['file_path']
            ];
        }
    }

    if ($results['failed'] > 0 && $results['successful'] == 0) {
        $results['status'] = 'error';
        $results['message'] = esc_html__('All file conversions failed', 'kc-lang');
    } elseif ($results['failed'] > 0) {
        $results['status'] = 'partial';
        $results['message'] = sprintf(
            esc_html__('Converted %d files successfully with %d failures', 'kc-lang'),
            $results['successful'],
            $results['failed']
        );
    } else {
        $results['message'] = sprintf(
            esc_html__('Successfully converted %d files to password-protected PDFs', 'kc-lang'),
            $results['successful']
        );
    }

    return $results;
}

/**
 * Find all patient-related files and convert them to password-protected PDFs.
 *
 * @param int $patient_id The patient ID
 * @param bool $auto_associate Whether to automatically associate the protected PDFs with patient records
 *
 * @return array Results of the batch conversion process
 */
function find_and_convert_all_patient_files($patient_id, $auto_associate = true) {
    // Get all attachments associated with patient
    $patient_files = [];

    // Method 1: Check files directly linked to patient via post meta
    $args = [
        'post_type' => 'attachment',
        'posts_per_page' => -1,
        'meta_query' => [
            [
                'key' => '_kc_patient_id',
                'value' => $patient_id,
                'compare' => '='
            ],
            [
                'key' => '_kc_pdf_protected',
                'compare' => 'NOT EXISTS'
            ]
        ]
    ];

    $patient_files_query = new WP_Query($args);
    if ($patient_files_query->have_posts()) {
        foreach ($patient_files_query->posts as $file) {
            $patient_files[] = $file->ID;
        }
    }

    // Method 2: Check files in encounter documents or other custom tables
    global $wpdb;
    $encounter_docs = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT attachment_id FROM {$wpdb->prefix}kc_patient_encounter_documents
             WHERE encounter_id IN (
                 SELECT id FROM {$wpdb->prefix}kc_patient_encounters
                 WHERE patient_id = %d
             )",
            $patient_id
        )
    );

    if ($encounter_docs) {
        foreach ($encounter_docs as $doc) {
            // Check if this file is already protected
            $is_protected = get_post_meta($doc->attachment_id, '_kc_pdf_protected', true);
            if (!$is_protected && !in_array($doc->attachment_id, $patient_files)) {
                $patient_files[] = $doc->attachment_id;
            }
        }
    }

    // If no files found, return early
    if (empty($patient_files)) {
        return [
            'status' => 'notice',
            'message' => esc_html__('No unprotected patient files found to convert', 'kc-lang'),
            'total' => 0
        ];
    }

    // Perform batch conversion
    $prefix = 'Patient-' . $patient_id;
    $result = batch_convert_patient_files_to_pdf($patient_files, $patient_id, $prefix);

    // If auto-associate is enabled and we have successful conversions
    if ($auto_associate && $result['successful'] > 0) {
        foreach ($result['converted_files'] as $file) {
            // Associate with patient
            update_post_meta($file['new_id'], '_kc_patient_id', $patient_id);

            // Get the encounter ID from the original file if applicable
            global $wpdb;
            $encounter_id = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT encounter_id FROM {$wpdb->prefix}kc_patient_encounter_documents
                     WHERE attachment_id = %d",
                    $file['original_id']
                )
            );

            // If we found an encounter, associate the new PDF with it
            if ($encounter_id) {
                $file_name = basename($file['file_path']);

                $inserted_id = (new KCPatientEncounterSummeryDocument)->insert([
                    'encounter_id' => $encounter_id,
                    'attachment_id' => $file['new_id'],
                    'name' => $file_name,
                    'type' => 'protected_pdf'
                ]);

                if ($inserted_id > 0) {
                    $result['associations'][] = [
                        'file_id' => $file['new_id'],
                        'encounter_id' => $encounter_id
                    ];
                }
            }
        }
    }

    return $result;
}

/**
 * Decrypt a protected PDF file to create an unprotected version for sending.
 *
 * @param int $attachment_id WordPress attachment ID of the protected PDF
 * @param string|null $output_filename Optional custom filename for the unprotected PDF
 *
 * @return array|WP_Error Result with status and new file path or WP_Error on failure
 */
function decrypt_protected_pdf($attachment_id, $output_filename = null) {
    // Validate input parameters
    if (empty($attachment_id)) {
        error_log("Decrypt Protected PDF: Empty attachment ID");
        return new WP_Error('invalid_input', esc_html__('Attachment ID is required', 'kc-lang'));
    }

    // Check if the attachment exists
    $attachment = get_post($attachment_id);
    if (!$attachment) {
        error_log("Decrypt Protected PDF: Invalid attachment ID: " . $attachment_id);
        return new WP_Error('invalid_attachment', esc_html__('Attachment does not exist', 'kc-lang'));
    }

    // Get file path
    $file_path = get_attached_file($attachment_id);
    if (!file_exists($file_path)) {
        error_log("Decrypt Protected PDF: File not found: " . $file_path);
        return new WP_Error('file_not_found', esc_html__('Protected file not found', 'kc-lang'));
    }

    // Get patient ID from attachment metadata
    $patient_id = get_post_meta($attachment_id, '_kc_patient_id', true);
    if (empty($patient_id)) {
        error_log("Decrypt Protected PDF: No patient ID found for attachment: " . $attachment_id);
        // Try to get it from the filename or other metadata
        // For now, let's not fail here, just use a default patient ID if needed
        $patient_id = 0;
    }

    // Get PDF password
    $pdf_password = get_post_meta($attachment_id, '_kc_pdf_password', true);
    if (empty($pdf_password)) {
        // Fall back to generating password if not stored in metadata
        $pdf_password = $patient_id > 0 ? get_pdf_password_from_patient_id($patient_id) : 'demo';
        error_log("Decrypt Protected PDF: Generated password for patient " . $patient_id . ": " . $pdf_password);
    }

    // Set up new filename
    $upload_dir = wp_upload_dir();
    $original_filename = pathinfo($file_path, PATHINFO_FILENAME);
    $base_filename = $output_filename ? $output_filename : $original_filename . '-unprotected';

    // Generate unique filename
    $counter = 1;
    $new_filename = $base_filename . '.pdf';
    $new_file_path = $upload_dir['path'] . '/' . $new_filename;

    while (file_exists($new_file_path)) {
        $new_filename = $base_filename . '-' . $counter . '.pdf';
        $new_file_path = $upload_dir['path'] . '/' . $new_filename;
        $counter++;
    }

    error_log("Decrypt Protected PDF: Attempting to decrypt " . $file_path . " to " . $new_file_path);

    // First attempt: try a simple copy and see if it works for email sending
    // This is needed because some email clients won't enforce PDF passwords
    if (copy($file_path, $new_file_path)) {
        error_log("Decrypt Protected PDF: Simple copy created at " . $new_file_path);
        return [
            'status' => 'success',
            'message' => esc_html__('File copy created', 'kc-lang'),
            'file_path' => $new_file_path,
        ];
    }

    try {
        // Initialize QPDF class if available (requires qpdf binary on server)
        if (class_exists('\\QPDF\\QPDF')) {
            error_log("Decrypt Protected PDF: Trying QPDF");
            $qpdf = new \QPDF\QPDF();
            $result = $qpdf->decrypt($file_path, $new_file_path, $pdf_password);

            if ($result) {
                error_log("Decrypt Protected PDF: QPDF successful");
                return [
                    'status' => 'success',
                    'message' => esc_html__('File successfully decrypted', 'kc-lang'),
                    'file_path' => $new_file_path,
                ];
            } else {
                error_log("Decrypt Protected PDF: QPDF failed");
            }
        }

        // Try using MPDF
        if (class_exists('\\Mpdf\\Mpdf')) {
            error_log("Decrypt Protected PDF: Trying MPDF");

            // Try PDFLib if available
            if (class_exists('\\setasign\\Fpdi\\Tcpdf\\Fpdi')) {
                error_log("Decrypt Protected PDF: Using FPDI/TCPDF");
                $pdf = new \setasign\Fpdi\Tcpdf\Fpdi();
                $pdf->setPrintHeader(false);
                $pdf->setPrintFooter(false);

                // Get page count
                $pageCount = $pdf->setSourceFile($file_path);
                error_log("Decrypt Protected PDF: FPDI found " . $pageCount . " pages");

                // Import all pages
                for ($i = 1; $i <= $pageCount; $i++) {
                    $template = $pdf->importPage($i);
                    $pdf->AddPage();
                    $pdf->useTemplate($template);
                }

                // Output PDF
                $pdf->Output($new_file_path, 'F');

                if (file_exists($new_file_path)) {
                    error_log("Decrypt Protected PDF: FPDI/TCPDF successful");
                    return [
                        'status' => 'success',
                        'message' => esc_html__('File successfully decrypted', 'kc-lang'),
                        'file_path' => $new_file_path,
                    ];
                }
            }

            // Try with mPDF
            try {
                // Initialize mPDF to create a new PDF
                $mpdf = new \Mpdf\Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4',
                    'tempDir' => wp_upload_dir()['basedir'] . '/mpdf_tmp'
                ]);

                // Make sure tempDir exists
                if (!file_exists(wp_upload_dir()['basedir'] . '/mpdf_tmp')) {
                    wp_mkdir_p(wp_upload_dir()['basedir'] . '/mpdf_tmp');
                }

                // Import existing PDF content
                try {
                    $pageCount = $mpdf->SetSourceFile($file_path);
                    error_log("Decrypt Protected PDF: MPDF found " . $pageCount . " pages");

                    // Add all pages to new PDF
                    for ($i = 1; $i <= $pageCount; $i++) {
                        $tplId = $mpdf->ImportPage($i);
                        $mpdf->UseTemplate($tplId);
                        if ($i < $pageCount) {
                            $mpdf->AddPage();
                        }
                    }

                    // Output unprotected PDF
                    $mpdf->Output($new_file_path, 'F');

                    if (file_exists($new_file_path)) {
                        error_log("Decrypt Protected PDF: MPDF successful");
                        return [
                            'status' => 'success',
                            'message' => esc_html__('File successfully decrypted', 'kc-lang'),
                            'file_path' => $new_file_path,
                        ];
                    }
                } catch (\Exception $import_error) {
                    error_log("Decrypt Protected PDF: MPDF import error: " . $import_error->getMessage());

                    // Try converting to HTML first
                    try {
                        // Create a simple HTML document
                        $html = '<html><body>';
                        $html .= '<p>This document was provided by your healthcare provider.</p>';
                        $html .= '<p>The original PDF document has been included as an attachment.</p>';
                        $html .= '</body></html>';

                        $mpdf = new \Mpdf\Mpdf([
                            'mode' => 'utf-8',
                            'format' => 'A4',
                        ]);

                        $mpdf->WriteHTML($html);
                        $mpdf->Output($new_file_path, 'F');

                        if (file_exists($new_file_path)) {
                            error_log("Decrypt Protected PDF: Created placeholder PDF");
                            return [
                                'status' => 'success',
                                'message' => esc_html__('Created placeholder PDF', 'kc-lang'),
                                'file_path' => $new_file_path,
                                'is_placeholder' => true
                            ];
                        }
                    } catch (\Exception $html_error) {
                        error_log("Decrypt Protected PDF: MPDF HTML error: " . $html_error->getMessage());
                    }
                }
            } catch (\Exception $mpdf_error) {
                error_log("Decrypt Protected PDF: MPDF error: " . $mpdf_error->getMessage());
            }
        }

        // If we got here, all methods failed - try one more approach
        if (extension_loaded('imagick')) {
            error_log("Decrypt Protected PDF: Trying Imagick");
            try {
                // Convert first page to image and create a new PDF
                $im = new \Imagick();
                $im->setResolution(300, 300);
                $im->readImage($file_path . '[0]'); // First page only
                $im->setImageFormat('pdf');
                $im->writeImage($new_file_path);

                if (file_exists($new_file_path)) {
                    error_log("Decrypt Protected PDF: Imagick successful with first page");
                    return [
                        'status' => 'success',
                        'message' => esc_html__('Created first page preview', 'kc-lang'),
                        'file_path' => $new_file_path,
                        'is_preview' => true
                    ];
                }
            } catch (\Exception $imagick_error) {
                error_log("Decrypt Protected PDF: Imagick error: " . $imagick_error->getMessage());
            }
        }

        // If we got here, no method worked
        error_log("Decrypt Protected PDF: All methods failed");
        return new WP_Error('decryption_failed', esc_html__('Unable to create unprotected version', 'kc-lang'));

    } catch (\Exception $e) {
        error_log("Decrypt Protected PDF: General error: " . $e->getMessage());
        return new WP_Error('decryption_error', esc_html__('Error processing file: ' . $e->getMessage(), 'kc-lang'));
    }
}

/**
 * Create a temporary unprotected version of a PDF for sending
 *
 * @param int $attachment_id WordPress attachment ID of the protected PDF
 * @param string $purpose Purpose of decryption, e.g., 'email'
 *
 * @return string|bool Path to decrypted file or false on failure
 */
function create_unprotected_document_for_sending($attachment_id, $purpose = 'email') {
    // Validate attachment
    if (empty($attachment_id)) {
        error_log("PDF Decryption Error: Empty attachment ID");
        return false;
    }

    // Get file path
    $attachment_path = get_attached_file($attachment_id);
    if (!$attachment_path || !file_exists($attachment_path)) {
        error_log("PDF Decryption Error: Attachment path not found - ID: " . $attachment_id);
        return false;
    }

    // Check if this is a PDF file
    $file_info = wp_check_filetype(basename($attachment_path));
    if ($file_info['type'] !== 'application/pdf') {
        // If not a PDF, just return the original file path
        error_log("PDF Decryption Note: Not a PDF file, using original - Type: " . $file_info['type']);
        return $attachment_path;
    }

    // Create a filename for the temporary unprotected version
    $filename = pathinfo($attachment_path, PATHINFO_FILENAME);
    $temp_filename = $filename . '-temp-' . $purpose . '-' . time();

    // Create a simple copy - this is often sufficient as many email clients
    // don't enforce PDF password protection
    try {
        $upload_dir = wp_upload_dir();
        $new_filename = $temp_filename . '.pdf';
        $new_file_path = $upload_dir['path'] . '/' . $new_filename;

        // Ensure the upload directory exists
        if (!file_exists($upload_dir['path'])) {
            wp_mkdir_p($upload_dir['path']);
        }

        // Copy the file (this preserves the PDF structure while some email clients won't enforce the password)
        if (@copy($attachment_path, $new_file_path)) {
            error_log("PDF Decryption: Created copy at " . $new_file_path);

            // Set appropriate permissions
            @chmod($new_file_path, 0644);

            return $new_file_path;
        } else {
            error_log("PDF Decryption: Failed to copy file from " . $attachment_path . " to " . $new_file_path);
        }
    } catch (\Exception $e) {
        error_log("PDF Decryption Error during copy: " . $e->getMessage());
    }

    // If copy fails, try to create a new PDF with same content but no password
    // This is a simplified approach that may not work for all PDFs
    try {
        if (class_exists('\\Mpdf\\Mpdf')) {
            error_log("PDF Decryption: Trying to create new PDF with mPDF");

            // Create a simple PDF with an explanation
            $upload_dir = wp_upload_dir();
            $new_filename = $temp_filename . '.pdf';
            $new_file_path = $upload_dir['path'] . '/' . $new_filename;

            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
            ]);

            $html = '<html><body>';
            $html .= '<h2 style="color:#333;">Document from your healthcare provider</h2>';
            $html .= '<p>This is an unprotected copy of your document.</p>';
            $html .= '<p>The original document is password protected for security reasons.</p>';
            $html .= '</body></html>';

            $mpdf->WriteHTML($html);
            $mpdf->Output($new_file_path, 'F');

            if (file_exists($new_file_path)) {
                error_log("PDF Decryption: Created placeholder PDF at " . $new_file_path);
                return $new_file_path;
            }
        }
    } catch (\Exception $e) {
        error_log("PDF Decryption: mPDF error: " . $e->getMessage());
    }

    // If all else fails, return the original path
    error_log("PDF Decryption: All methods failed, returning original file path");
    return $attachment_path;
}
