<!-- <div class="d-flex justify-content-between align-items-center flex-wrap gap-1">
<div class="iq-kivi-tab-panel-title-animation">
    <h3 class="iq-kivi-tab-panel-title"><?php echo esc_html__("More About Appointment", "kc-lang"); ?></h3>
    </div>
</div>
<hr>
<div class="widget-content">
    
    <div class="card-list-data pt-2 pe-2 mb-3">
    <?php if (kcCheckExtraTabConditionInAppointmentWidget('description')) {
        ?>
        <div id="appointment-descriptions" >
            <div class="form-group mb-2">
                <label class="form-label"
                       for="appointment-descriptions-field"> <?php echo esc_html__('Appointment Descriptions', 'kc-lang'); ?>
                </label>
                <textarea class="iq-kivicare-form-control"
                          id="appointment-descriptions-field"
                          placeholder="<?php echo esc_html__('Enter Appointment Descriptions', 'kc-lang'); ?>"></textarea>
            </div>
        </div>
        <?php
    } ?>
    <div>
        <div id="file-upload" >
            <?php
            if (kcAppointmentMultiFileUploadEnable()) {
                ?>
                <div class="form-group mb-2 ">
                    <label class="form-label" for="addMedicalReport"> <?php echo esc_html__('Add Medical Report', 'kc-lang'); ?>
                    </label>
                    <input type="file" name="file_multi[]" class="iq-kivicare-form-control" id="kivicareaddMedicalReport"
                        placeholder="<?php echo esc_html__('Add Your Medical Report', 'kc-lang'); ?>"  <?php echo esc_html(isKiviCareProActive() ? 'multiple' : ''); ?> >
                </div>
                <div id="kivicare_file_upload_review">
                </div>
                <?php
            }
            ?>
        </div>
        <div  id="customFieldsListAppointment">

        </div>
    </div>
    </div>
</div> -->

<div class="">
    <div class="space-y-4">
        <!-- Header Section -->
        <div class="mb-6">
            <h1 class="text-2xl font-semibold text-gray-900">
                <?php echo esc_html__("More About Appointment", "kc-lang"); ?>
            </h1>
            <p class="text-sm text-gray-500 mt-1">Please provide your medical information</p>
        </div>

        <div class="widget-content space-y-4">

            <!-- Content Section -->
            <div class="border rounded-lg overflow-hidden bg-white transition-all duration-200 ">
                <button data-toggle type="button" class="w-full px-4 py-3 flex items-center justify-between bg-white hover:bg-gray-50">
                    <div class="flex items-center space-x-3">
                        <div class="p-1 rounded-full bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-file-text w-5 h-5 text-purple-600">
                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                <path d="M10 9H8"></path>
                                <path d="M16 13H8"></path>
                                <path d="M16 17H8"></path>
                            </svg></div>
                        <h3 class="text-lg font-medium text-gray-900">
                            Description &amp; Reports
                            <span class="text-red-500 ml-1">*</span>
                        </h3>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-right w-5 h-5 text-gray-400 transition-transform duration-200 ">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
                <div class="toggle-section transition-all duration-200 hidden">
                    <div class="p-4 border-t">
                        <div class="space-y-4">
                            <div class="space-y-1">
                                <label class="block text-sm font-medium text-gray-700">Appointment
                                    Description <span class="text-red-500">*</span></label><textarea
                                    class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                    placeholder="Enter detailed description" rows="3"></textarea>
                            </div>
                            <div class="space-y-2"><label class="block text-sm font-medium text-gray-700">Medical
                                    Reports </label>
                                <div class="border-2 border-dashed rounded-lg p-4">
                                    <div class="flex flex-col items-center justify-center space-y-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-upload w-8 h-8 text-gray-400">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="17 8 12 3 7 8"></polyline>
                                            <line x1="12" x2="12" y1="3" y2="15"></line>
                                        </svg>
                                        <span class="text-sm text-gray-600">Drop files here or click to
                                            upload</span>
                                    </div>
                                    <input class="hidden" type="file">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Fields -->
            <div id="customFieldsListAppointment"></div>

            <!-- Content Section old -->
            <div class="p-4 border-t">
                <div class="space-y-4">
                    <!-- Appointment Description -->
                    <?php if (kcCheckExtraTabConditionInAppointmentWidget('description')) { ?>
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-700">
                                Appointment Descriptions
                            </label>
                            <textarea
                                class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                placeholder="Enter Appointment Descriptions"></textarea>
                        </div>
                    <?php } ?>

                    <!-- Medical Report Upload -->
                    <?php if (kcAppointmentMultiFileUploadEnable()) { ?>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                Add Medical Report
                            </label>
                            <div class="border-2 border-dashed rounded-lg p-4">
                                <div class="flex flex-col items-center justify-center space-y-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-upload w-8 h-8 text-gray-400">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17 8 12 3 7 8"></polyline>
                                        <line x1="12" x2="12" y1="3" y2="15"></line>
                                    </svg>
                                    <span class="text-sm text-gray-600">Drop files here or click to upload</span>
                                </div>
                                <input class="hidden" type="file" <?php echo esc_html(isKiviCareProActive() ? 'multiple' : ''); ?>>
                            </div>
                            <div id="kivicare_file_upload_review"></div>
                        </div>
                    <?php } ?>

                    <!-- Custom Fields -->
                    <div id="customFieldsListAppointment"></div>
                </div>
            </div>

        </div>

    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Get all buttons and sections
        const buttons = document.querySelectorAll('button[data-toggle]');
        const sections = document.querySelectorAll('.toggle-section');

        buttons.forEach((button, index) => {
            button.addEventListener('click', () => {
                const section = sections[index]; // Get the corresponding section
                const icon = button.querySelector('svg.lucide-chevron-right'); // Chevron icon

                // Toggle visibility and animation
                if (section.classList.contains('hidden')) {
                    section.classList.remove('hidden');
                    section.classList.add('block');
                    icon.classList.add('rotate-90'); // Rotate icon
                } else {
                    section.classList.remove('block');
                    section.classList.add('hidden');
                    icon.classList.remove('rotate-90'); // Reset icon rotation
                }
            });
        });
    });
</script>