<template>
    <div class="w-full px-4">
        <div class="w-full">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-xl font-medium text-gray-800">{{ $t('TDL Lab Test Catalog') }}</h4>
                    </div>
                    <div class="p-6">
                        <!-- Search Controls -->
                        <div class="grid grid-cols-1 md:grid-cols-12 gap-6 mb-6">
                            <div class="md:col-span-6">
                                <label for="search_query" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Search Tests') }}</label>
                                <div class="flex rounded-md shadow-sm">
                                    <input
                                        type="text"
                                        class="flex-grow focus:ring-blue-500 focus:border-blue-500 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300"
                                        id="search_query"
                                        v-model="searchQuery"
                                        :placeholder="$t('Search by test name or code...')"
                                        @keyup.enter="searchTests"
                                    />
                                    <button 
                                        class="inline-flex items-center px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" 
                                        type="button" 
                                        @click="searchTests"
                                    >
                                        <i class="fa fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="md:col-span-4">
                                <label for="category_filter" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Category') }}</label>
                                <select
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    id="category_filter"
                                    v-model="categoryFilter"
                                    @change="searchTests"
                                >
                                    <option value="">{{ $t('All Categories') }}</option>
                                    <option v-for="category in categories" :key="category" :value="category">
                                        {{ category }}
                                    </option>
                                </select>
                            </div>
                            <div class="md:col-span-2 flex items-end">
                                <button
                                    @click="refreshCatalog"
                                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    :disabled="refreshing"
                                >
                                    <i :class="{'fa fa-sync mr-2': true, 'fa-spin': refreshing}"></i>
                                    {{ $t('Refresh') }}
                                </button>
                            </div>
                        </div>
                        
                        <!-- Tests Table -->
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <div v-else-if="tests.length === 0" class="bg-blue-50 border-l-4 border-blue-500 p-4 text-blue-700">
                            {{ $t('No tests found matching your criteria.') }}
                        </div>
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Test Code') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Test Name') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Category') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Sample Type') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Reference Range') }}
                                        </th>
                                        <th v-if="showPrice" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Price') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Last Updated') }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="test in tests" :key="test.id" class="hover:bg-gray-50">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{{ test.test_code }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ test.test_name }}</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ test.test_category || 'N/A' }}</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ test.sample_type || 'N/A' }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ test.normal_range || 'N/A' }}</td>
                                        <td v-if="showPrice" class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ formatPrice(test.price) }}</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ formatDate(test.last_updated) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div v-if="totalPages > 1" class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-700">
                                {{ $t('Showing {start} to {end} of {total} tests', {
                                    start: (currentPage - 1) * perPage + 1,
                                    end: Math.min(currentPage * perPage, total),
                                    total: total
                                }) }}
                            </div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a
                                    href="#"
                                    @click.prevent="changePage(currentPage - 1)"
                                    :class="[
                                        'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                                        currentPage === 1 
                                            ? 'text-gray-300 cursor-not-allowed' 
                                            : 'text-gray-500 hover:bg-gray-50'
                                    ]"
                                >
                                    <span class="sr-only">{{ $t('Previous') }}</span>
                                    <i class="fa fa-chevron-left h-5 w-5"></i>
                                </a>
                                <a
                                    v-for="page in pageNumbers"
                                    :key="page"
                                    href="#"
                                    @click.prevent="changePage(page)"
                                    :class="[
                                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                                        page === currentPage
                                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                    ]"
                                >
                                    {{ page }}
                                </a>
                                <a
                                    href="#"
                                    @click.prevent="changePage(currentPage + 1)"
                                    :class="[
                                        'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                                        currentPage === totalPages 
                                            ? 'text-gray-300 cursor-not-allowed' 
                                            : 'text-gray-500 hover:bg-gray-50'
                                    ]"
                                >
                                    <span class="sr-only">{{ $t('Next') }}</span>
                                    <i class="fa fa-chevron-right h-5 w-5"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
    name: 'TDLTests',
    data() {
        return {
            loading: true,
            refreshing: false,
            tests: [],
            categories: [],
            searchQuery: '',
            categoryFilter: '',
            currentPage: 1,
            perPage: 25,
            total: 0,
            totalPages: 0,
            showPrice: false,
            refreshBtn: null
        };
    },
    computed: {
        pageNumbers() {
            const range = [];
            const showPages = 5;
            const halfShow = Math.floor(showPages / 2);
            
            let start = this.currentPage - halfShow;
            if (start < 1) start = 1;
            
            let end = start + showPages - 1;
            if (end > this.totalPages) {
                end = this.totalPages;
                start = Math.max(1, end - showPages + 1);
            }
            
            for (let i = start; i <= end; i++) {
                range.push(i);
            }
            
            return range;
        },
        currentLocale() {
            return this.$i18n.locale || 'en';
        }
    },
    created() {
        this.fetchTests();
        this.checkPriceVisibility();
    },
    methods: {
        fetchTests() {
            this.loading = true;
            
            const params = {
                page: this.currentPage,
                per_page: this.perPage,
                search: this.searchQuery || '',
                category: this.categoryFilter || ''
            };
            
            get('tdl_get_tests_catalog', params)
                .then(response => {
                    this.loading = false;
                    
                    if (response.data.status === true) {
                        this.tests = response.data.data.tests || [];
                        this.total = response.data.data.total || 0;
                        this.totalPages = response.data.data.total_pages || 0;
                        this.currentPage = response.data.data.current_page || 1;
                        
                        // Extract unique categories for the filter if not already loaded
                        if (this.tests.length > 0 && this.categories.length === 0) {
                            this.extractCategories();
                        }
                    } else {
                        this.tests = [];
                        this.total = 0;
                        this.totalPages = 0;
                        
                        displayErrorMessage(response.data.message || this.$t('Failed to load tests.'));
                    }
                })
                .catch(error => {
                    this.loading = false;
                    console.error('Error fetching tests:', error);
                    
                    displayErrorMessage(this.$t('Something went wrong while fetching the tests. Please try again.'));
                });
        },
        searchTests() {
            this.currentPage = 1;
            this.fetchTests();
        },
        changePage(page) {
            if (page < 1 || page > this.totalPages) return;
            
            this.currentPage = page;
            this.fetchTests();
        },
        refreshCatalog() {
            this.refreshing = true;
            this.refreshBtn = document.querySelector('button:has(.fa-sync)');
            
            if (this.refreshBtn) {
                this.refreshBtn.disabled = true;
            }
            
            get('tdl_refresh_tests_catalog')
                .then(response => {
                    this.refreshing = false;
                    if (this.refreshBtn) {
                        this.refreshBtn.disabled = false;
                    }
                    
                    if (response.data.status === true) {
                        displayMessage(response.data.message || this.$t('Test catalog has been refreshed.'));
                        this.currentPage = 1;
                        this.fetchTests();
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to refresh test catalog.'));
                    }
                })
                .catch(error => {
                    this.refreshing = false;
                    if (this.refreshBtn) {
                        this.refreshBtn.disabled = false;
                    }
                    
                    console.error('Error refreshing catalog:', error);
                    displayErrorMessage(this.$t('Something went wrong while refreshing the catalog. Please try again.'));
                });
        },
        formatDate(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const options = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                };
                
                return new Date(dateString).toLocaleDateString(this.currentLocale, options);
            } catch (error) {
                console.error('Error formatting date:', error);
                return dateString;
            }
        },
        formatPrice(price) {
            if (!price) return 'N/A';
            
            try {
                // Assume GBP for TDL
                return '£' + parseFloat(price).toFixed(2);
            } catch (error) {
                console.error('Error formatting price:', error);
                return price;
            }
        },
        extractCategories() {
            const categorySet = new Set();
            this.tests.forEach(test => {
                if (test.test_category) {
                    categorySet.add(test.test_category);
                }
            });
            this.categories = Array.from(categorySet).sort();
        },
        checkPriceVisibility() {
            // Check if user has permission to view prices
            // This is a placeholder - implement according to your app's logic
            get('tdl_check_price_visibility')
                .then(response => {
                    if (response.data.status === true) {
                        this.showPrice = response.data.data.show_price || false;
                    }
                })
                .catch(error => {
                    console.error('Error checking price visibility:', error);
                    this.showPrice = false;
                });
        }
    }
};
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>