<?php
/**
 * Plugin Name: Medroid - Clinic & Patient Management System (EHR)
 * Plugin URI: https://medroid.ai
 * Description: Medroid is an impressive clinic and patient management plugin (EHR).
 * Version:100.4.0
 * Author: medroid
 * Text Domain: kc-lang
 * Domain Path: /languages
 * Author URI: http://medroid.ai/
 **/
use App\baseClasses\KCActivate;
use App\baseClasses\KCDeactivate;
defined( 'ABSPATH' ) or die( 'Something went wrong' );

// Require once the Composer Autoload
if ( file_exists( dirname( __FILE__ ) . '/vendor/autoload.php' ) ) {
	require_once dirname( __FILE__ ) . '/vendor/autoload.php';
} else {
	die( 'Something went wrong' );
}

if (!defined('KIVI_CARE_DIR'))
{
	define('KIVI_CARE_DIR', plugin_dir_path(__FILE__));
}

if (!defined('KIVI_CARE_DIR_URI'))
{
	define('KIVI_CARE_DIR_URI', plugin_dir_url(__FILE__));
}

if (!defined('KIVI_CARE_BASE_NAME'))
{
    define('KIVI_CARE_BASE_NAME', plugin_basename(__FILE__));
}

if (!defined('KIVI_CARE_NAMESPACE'))
{
	define('KIVI_CARE_NAMESPACE', "kivi-care");
}

if (!defined('KIVI_CARE_PREFIX'))
{
	define('KIVI_CARE_PREFIX', "kiviCare_");
}

if (!defined('KIVI_CARE_VERSION'))
{
    define('KIVI_CARE_VERSION', "100.4.0");
}

// Suppress PHP 8.2+ dynamic property deprecation warnings for better compatibility
if (PHP_VERSION_ID >= 80200) {
    // Only suppress deprecation warnings, not other error types
    $current_error_reporting = error_reporting();
    if ($current_error_reporting & E_DEPRECATED) {
        error_reporting($current_error_reporting & ~E_DEPRECATED);
    }
}

/**
 * The code that runs during plugin activation
 */
register_activation_hook( __FILE__, [ KCActivate::class, 'activate'] );

/**
 * The code that runs during plugin deactivation
 */
register_deactivation_hook( __FILE__, [KCDeactivate::class, 'deActivate'] );

// Add AIScribe recording fix
add_action('admin_enqueue_scripts', function() {
    wp_enqueue_script('fix-aiscribe-script', plugins_url('assets/js/fix-aiscribe.js', __FILE__), array('jquery'), KIVI_CARE_VERSION, true);
});

( new KCActivate )->init();



( new KCDeactivate() );

// Include the patient check-in system
require_once(KIVI_CARE_DIR . 'patient-checkin.php');

// Handle mobile upload requests directly
add_action('parse_request', function($wp) {
    // Check if the URL contains 'mobile-upload' and has a session_id parameter
    if (strpos($_SERVER['REQUEST_URI'], 'mobile-upload') !== false && isset($_GET['session_id'])) {
        // Include our mobile upload template
        include_once(plugin_dir_path(__FILE__) . 'templates/mobile-upload.php');
        exit;
    }

    // Handle AI Scribe mobile audio recording requests
    if (strpos($_SERVER['REQUEST_URI'], 'mobile-audio-record') !== false && isset($_GET['session_id'])) {
        // Include our mobile audio recording template
        include_once(plugin_dir_path(__FILE__) . 'templates/mobile-audio-record.php');
        exit;
    }
});

do_action('kivicare_activate_init');

// Initialize the template manager
require_once(KIVI_CARE_DIR . 'app/database/md-template-manager-db.php');
// (new App\controllers\MDTemplateManagerController())->init();

// Initialize the database migration system
require_once(KIVI_CARE_DIR . 'app/database/kc-database-migration.php');

// Initialize task helper functions
require_once(KIVI_CARE_DIR . 'utils/medriod_Task_helpers.php');

// Initialize service import fix
require_once(KIVI_CARE_DIR . 'app/filters/kc-service-import-fix.php');

// Register cron event for task overdue notifications
if (!wp_next_scheduled('kc_task_overdue_check')) {
    wp_schedule_event(time(), 'daily', 'kc_task_overdue_check');
}

// Hook the overdue check function to the cron event
add_action('kc_task_overdue_check', 'kc_check_overdue_tasks');

// Add headers to prevent browser caching
// add_action('send_headers', function() {
//     header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
//     header('Cache-Control: post-check=0, pre-check=0', false);
//     header('Pragma: no-cache');
//     header('Expires: 0');
// });