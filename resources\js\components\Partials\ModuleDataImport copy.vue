<template>
  <div>
    <ModalPopup
      v-if="openModal"
      modalId="appointment-details-modal"
      modalSize="lg"
      :openModal="openModal"
      :modalTitle="moduleName + ' ' + formTranslation.common.import"
      @closeModal="handleModalClose"
    >
      <!-- Loading State -->
      <div v-if="isSubmitting" class="flex justify-center items-center min-h-[200px]">
        <loader-component-2></loader-component-2>
      </div>

      <!-- Import Done State -->
      <div v-if="isImportDone" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Summary Stats -->
          <div class="bg-gray-50 rounded-lg p-4">
            <p class="flex justify-between items-center text-sm">
              <span class="font-medium text-gray-700">{{ formTranslation.common.total_rows }}:</span>
              <span class="text-gray-900">{{ totalRows }}</span>
            </p>
          </div>
          <div class="bg-gray-50 rounded-lg p-4">
            <p class="flex justify-between items-center text-sm">
              <span class="font-medium text-gray-700">{{ formTranslation.common.total_rows_inserted }}:</span>
              <span class="text-gray-900">{{ totalRowsInserted }}</span>
            </p>
          </div>
          
          <!-- Detailed Reports -->
          <template v-for="(value, key) in detailReport">
            <div v-if="value.value > 0" :key="key" class="bg-gray-50 rounded-lg p-4">
              <p class="flex justify-between items-center text-sm">
                <span class="font-medium text-gray-700">{{ value.label }}:</span>
                <span class="text-gray-900">{{ value.value }}</span>
              </p>
            </div>
          </template>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-center gap-3 mt-6">
          <button 
            @click="resetForm"
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            {{formTranslation.common.import_more_file}}
          </button>
          <button 
            @click="handleModalClose"
            type="button"
            class="px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            {{formTranslation.common.close}}
          </button>
        </div>
      </div>

      <!-- Import Form -->
      <form v-else @submit.prevent="handleSubmit" class="space-y-6 p-6">
        <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
          <!-- File Type Selection -->
          <div class="md:col-span-4">
            <label for="select_type" class="block text-sm font-medium text-gray-700 mb-2">
              {{formTranslation.static_data.tag_select_type_plh}}
            </label>
            <b-select 
              @change="handleTypeChange" 
              v-model="uploadFileDetail.type" 
              class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              id="select_type"
            >
              <option v-for="(option, key) in importTypeOptions" :key="key" :value="option.type">
                {{option.label}}
              </option>
            </b-select>
            <p v-if="showValidation && !$v.uploadFileDetail.type.required" class="mt-1 text-sm text-red-600">
              {{formTranslation.common.file_type + formTranslation.common.required}}
            </p>
          </div>

          <!-- File Upload -->
          <div class="md:col-span-8">
            <label for="file_upload" class="block text-sm font-medium text-gray-700 mb-2">
              {{ formTranslation.common.upload_file }}
            </label>
            <div class="flex">
              <button 
                @click.prevent="handleFileUpload"
                :disabled="isFileUploading"
                type="button"
                class="flex-shrink-0 px-4 py-2 bg-blue-600 text-white rounded-l-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50"
              >
                {{ isFileUploading ? formTranslation.common.loading : formTranslation.common.choose_file }}
              </button>
              <div class="flex-grow px-3 py-2 bg-gray-50 border border-l-0 border-gray-300 rounded-r-lg text-sm text-gray-500">
                {{ uploadFileDetail.name }}
              </div>
            </div>
            <p v-if="showValidation && !$v.uploadFileDetail.url.required" class="mt-1 text-sm text-red-600">
              {{ formTranslation.common.no_file_chosen}}
            </p>
          </div>
        </div>

        <!-- Sample File & Required Fields -->
        <div class="space-y-4 bg-gray-50 rounded-lg p-4">
          <a 
            v-if="sampleFiles[uploadFileDetail.type]"
            :href="sampleFiles[uploadFileDetail.type]"
            class="inline-block text-blue-600 hover:text-blue-700 font-medium"
            target="_blank"
            :download="moduleType + '.' + (uploadFileDetail.type === 'xls' ? 'xlsx' : uploadFileDetail.type)"
          >
            {{formTranslation.common.lbl_download_sample_file}}
          </a>

          <div v-if="requiredData.length">
            <h4 class="font-medium text-gray-900 mb-2">
              {{formTranslation.common.lbl_required_field + ' ' + uploadFileDetail.type + ' ' + formTranslation.common.lbl_file }}
            </h4>
            <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li v-for="(field, index) in requiredData" :key="index">
                {{field.label}}
              </li>
            </ul>
          </div>
        </div>

        <!-- Notification Options -->
        <div v-if="showNotificationOptions" class="space-y-3">
          <h4 class="font-medium text-gray-900">
            {{formTranslation.common.send_notification_when_user_register }}
          </h4>
          
          <div class="flex gap-4">
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="uploadFileDetail.email"
                class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">{{ formTranslation.common.email }}</span>
            </label>

            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="uploadFileDetail.sms"
                class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">{{ formTranslation.common.message }}</span>
            </label>
          </div>

          <p class="text-sm text-blue-600 font-medium">
            {{formTranslation.common.note_notification}}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end gap-3 pt-4 border-t">
          <button 
            type="submit"
            :disabled="isSubmitting"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            <i :class="['fa', isSubmitting ? 'fa-sync fa-spin' : 'fa-save']"></i>
            {{ isSubmitting ? formTranslation.common.loading : formTranslation.common.save }}
          </button>
          <button 
            @click="handleModalClose"
            type="button"
            :disabled="isSubmitting"
            class="px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            {{formTranslation.common.cancel}}
          </button>
        </div>
      </form>
    </ModalPopup>
  </div>
</template>

<script>
import {get} from "../../config/request";
import {required} from "vuelidate/lib/validators";
import ModalPopup from '../Modal/Index';

export default {
  name: "ModuleDataImport",
  components:{
    ModalPopup
  },
  props:{
    moduleType:{
      type: String,
      required: true,
      default: ''
    },
    moduleName:{
      type: String,
      required: true,
      default: ''
    },
    requiredData:{
      type: Array,
      default: () => []
    },
    encounterId:{
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      openModal: false,
      importTypeOptions: [
        {
          type: 'csv',
          label: 'CSV'
        },
        {
          type: 'xls',
          label: 'XLS'
        }
      ],
      uploadFileDetail: {
        name: '',
        type: 'csv',
        url: '',
        id: '',
        moduleType: '',
        sms: false,
        email: false
      },
      isFileUploading: false,
      isSubmitting: false,
      showValidation: false,
      sampleFiles: {
        csv: '',
        xls: ''
      },
      totalRows: 0,
      totalRowsInserted: 0,
      isImportDone: false,
      detailReport: {}
    }
  },
  validations: {
    uploadFileDetail: {
      url: { required },
      type: { required },
      id: { required }
    }
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    showNotificationOptions() {
      return ['clinic', 'doctor', 'patient', 'receptionist'].includes(this.moduleType);
    }
  },
  mounted() {
    this.uploadFileDetail.name = this.formTranslation.common.no_file_chosen;
    this.fetchSampleFiles();
  },
  methods: {
    async handleFileUpload() {
      if (!this.userData.addOns.kiviPro) return;

      this.isFileUploading = true;
      this.openModal = false;

      try {
        const uploader = await kivicareCustomImageUploader(this.formTranslation, this.uploadFileDetail.type);
        
        uploader.on('select', () => {
          const attachment = uploader.state().get('selection').first().toJSON();
          this.uploadFileDetail.name = attachment.filename;
          this.uploadFileDetail.url = attachment.url;
          this.uploadFileDetail.id = attachment.id;
          this.isFileUploading = false;
          this.openModal = true;
        });

        uploader.on('close', () => {
          this.openModal = true;
          this.isFileUploading = false;
          uploader.close();
        });

        uploader.open();
      } catch (error) {
        console.error('File upload error:', error);
        this.isFileUploading = false;
        this.openModal = true;
      }
    },
    handleTypeChange() {
      this.isFileUploading = false;
      this.uploadFileDetail.id = '';
      this.uploadFileDetail.url = '';
      this.uploadFileDetail.name = this.formTranslation.common.no_file_chosen;
    },
    handleModalClose() {
      this.openModal = false;
      this.resetForm();
    },
    resetForm() {
      this.uploadFileDetail = {
        name: this.formTranslation.common.no_file_chosen,
        type: 'csv',
        url: '',
        id: '',
        moduleType: this.moduleType,
        sms: false,
        email: false
      };
      this.isImportDone = false;
      this.isFileUploading = false;
      this.detailReport = {};
      this.showValidation = false;
    },
    async handleSubmit() {
      this.showValidation = true;
      this.$v.$touch();
      
      if (this.$v.uploadFileDetail.$invalid) {
        return;
      }

      try {
        this.isSubmitting = true;
        const response = await get('import_module_data', {
          ...this.uploadFileDetail,
          required_field: this.requiredData,
          encounter_id: this.encounterId
        });

        if (response.data?.status) {
          this.totalRows = response.data.total_row;
          this.totalRowsInserted = response.data.total_data_insert;
          this.detailReport = response.data.detail_report;
          this.isImportDone = true;
          this.$emit('reloadList');
          displayMessage(response.data.message);
        } else {
          displayErrorMessage(response.data.message);
        }
      } catch (error) {
        console.error('Submit error:', error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.isSubmitting = false;
      }
    },
    async fetchSampleFiles() {
      try {
        const response = await get('import_demo_files', { module_type: this.moduleType });
        if (response.data?.status) {
          this.sampleFiles = response.data.data;
        }
      } catch (error) {
        console.error('Error fetching sample files:', error);
      }
    }
  }
}
</script>