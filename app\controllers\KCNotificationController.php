<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCNotification;
use Exception;
use WP_User;

class KCNotificationController extends KCBase {

    public $db;
    public $module = 'notification';
    private $request;
    private $notification_model;

    /**
     * Constructor for KCNotificationController
     */
    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        $this->notification_model = new KCNotification();
        
        parent::__construct();
    }

    /**
     * Get notifications for the current user
     *
     * @return void
     */
    public function getUserNotifications() {
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
            return;
        }
        
        try {
            $request_data = $this->request->getInputs();
            
            // Get parameters from request
            $per_page = isset($request_data['per_page']) ? (int) $request_data['per_page'] : 20;
            $page = isset($request_data['page']) ? (int) $request_data['page'] : 1;
            $only_unread = isset($request_data['only_unread']) ? (bool) $request_data['only_unread'] : false;
            $reference_type = isset($request_data['reference_type']) ? sanitize_text_field($request_data['reference_type']) : '';
            
            // Add detailed logging for debugging
            error_log('getUserNotifications request for user_id: ' . $user_id);
            error_log('Request params: ' . json_encode([
                'per_page' => $per_page,
                'page' => $page,
                'only_unread' => $only_unread,
                'reference_type' => $reference_type
            ]));
            
            // Check if database tables exist before proceeding
            global $wpdb;
            $tables_exist = true;
            $table_name = $wpdb->prefix . 'kc_notifications';
            
            $table_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                    DB_NAME,
                    $table_name
                )
            );
            
            if (!$table_exists) {
                $tables_exist = false;
                error_log('Missing required table: ' . $table_name);
                
                
                // Check again if table exists
                $table_exists = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                        DB_NAME,
                        $table_name
                    )
                );
                
                if (!$table_exists) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Notifications functionality is not available. Please contact support.', 'kc-lang')
                    ]);
                    return;
                }
            }
            
            // Query params
            $args = [
                'per_page' => $per_page,
                'page' => $page,
                'only_unread' => $only_unread,
                'reference_type' => $reference_type,
                'order_by' => 'created_at',
                'order' => 'DESC'
            ];
            
            // Get notifications for current user
            $notifications = [];
            
            // Try direct SQL approach first (for better error control)
            $offset = ($page - 1) * $per_page;
            $where_clauses = ["user_id = %d"];
            $where_values = [$user_id];
            
            if ($only_unread) {
                $where_clauses[] = "is_read = 0";
            }
            
            if (!empty($reference_type)) {
                $where_clauses[] = "reference_type = %s";
                $where_values[] = $reference_type;
            }
            
            $where_sql = implode(' AND ', $where_clauses);
            
            // Prepare the query with proper escaping
            $query = $wpdb->prepare(
                "SELECT * FROM {$table_name} 
                WHERE {$where_sql}
                ORDER BY created_at DESC
                LIMIT %d, %d",
                array_merge($where_values, [$offset, $per_page])
            );
            
            error_log('Executing SQL query: ' . $query);
            $notifications = $wpdb->get_results($query);
            
            if ($notifications === null) {
                error_log('Database error in getUserNotifications: ' . $wpdb->last_error);
                
                // Fall back to model method
                $notifications = $this->notification_model->getUserNotifications($user_id, $args);
            }
            
            error_log('Retrieved ' . count($notifications) . ' notifications for user ' . $user_id);
            
            // Get total count for pagination (using direct SQL for consistency)
            $count_query = $wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE {$where_sql}",
                $where_values
            );
            
            $total_count = $wpdb->get_var($count_query);
            
            if ($total_count === null) {
                error_log('Database error in count query: ' . $wpdb->last_error);
                // Fall back to model method
                $total_count = $this->notification_model->countUserNotifications($user_id, $only_unread);
            }
            
            $total_count = (int)$total_count;
            $total_pages = ceil($total_count / $per_page);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Notifications retrieved successfully', 'kc-lang'),
                'data' => [
                    'notifications' => $notifications,
                    'pagination' => [
                        'total_items' => $total_count,
                        'total_pages' => $total_pages,
                        'current_page' => $page,
                        'per_page' => $per_page
                    ]
                ]
            ]);
        } catch (Exception $e) {
            error_log('Error in getUserNotifications: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error retrieving notifications', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get count of unread notifications for current user
     *
     * @return void
     */
    public function getUnreadCount() {
        // Ensure request method is GET
        $request_method = $_SERVER['REQUEST_METHOD'];
        if ($request_method !== 'GET') {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Method is not allowed', 'kc-lang')
            ], 405);
            exit;
        }
        
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
            return;
        }
        
        try {
            // Check if database tables exist before proceeding
            global $wpdb;
            $table_name = $wpdb->prefix . 'kc_notifications';
            
            $table_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                    DB_NAME,
                    $table_name
                )
            );
            
            if (!$table_exists) {
                error_log('Notifications table does not exist when trying to get unread count');
            
                
                // Check again if table exists
                $table_exists = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                        DB_NAME,
                        $table_name
                    )
                );
                
                if (!$table_exists) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Notifications functionality is not available. Please contact support.', 'kc-lang')
                    ]);
                    return;
                }
            }
            
            // Use direct SQL query for better error handling
            $query = $wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE user_id = %d AND is_read = 0",
                $user_id
            );
            
            error_log('Executing unread count query: ' . $query);
            $count = $wpdb->get_var($query);
            
            if ($count === null) {
                error_log('Database error in getUnreadCount: ' . $wpdb->last_error);
                
                // Fall back to model method
                $count = $this->notification_model->countUserNotifications($user_id, true);
                error_log('Fall back to model method returned count: ' . $count);
            }
            
            $count = (int)$count;
            error_log('Unread count for user ' . $user_id . ': ' . $count);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Unread count retrieved successfully', 'kc-lang'),
                'data' => [
                    'count' => $count
                ]
            ]);
        } catch (Exception $e) {
            error_log('Exception in getUnreadCount: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error retrieving unread count', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Mark notification(s) as read
     *
     * @return void
     */
    public function markAsRead() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Add detailed logging for debugging
        error_log('markAsRead request for user_id: ' . $user_id);
        error_log('Request data: ' . json_encode($request_data));
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
            return;
        }
        
        // Validate request
        $rules = [
            'notification_ids' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            error_log('Validation errors in markAsRead: ' . json_encode($errors));
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
            return;
        }
        
        try {
            // Check if database tables exist before proceeding
            global $wpdb;
            $table_name = $wpdb->prefix . 'kc_notifications';
            
            $table_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                    DB_NAME,
                    $table_name
                )
            );
            
            if (!$table_exists) {
                error_log('Notifications table does not exist when trying to mark as read');

                
                // Check again if table exists
                $table_exists = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                        DB_NAME,
                        $table_name
                    )
                );
                
                if (!$table_exists) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Notifications functionality is not available. Please contact support.', 'kc-lang')
                    ]);
                    return;
                }
            }
            
            $notification_ids = $request_data['notification_ids'];
            
            // Convert single ID to array
            if (!is_array($notification_ids)) {
                $notification_ids = [$notification_ids];
            }
            
            // Sanitize IDs
            $notification_ids = array_map('intval', $notification_ids);
            error_log('Marking notifications as read: ' . implode(',', $notification_ids));
            
            // Ensure we are only updating notifications that belong to the current user
            // Using direct SQL for better error control and security
            if (!empty($notification_ids)) {
                // Prepare placeholders for SQL IN clause
                $placeholders = implode(',', array_fill(0, count($notification_ids), '%d'));
                
                // Build the query
                $query = $wpdb->prepare(
                    "UPDATE {$table_name} 
                    SET is_read = 1, updated_at = %s
                    WHERE id IN ({$placeholders}) AND user_id = %d",
                    array_merge([current_time('mysql')], $notification_ids, [$user_id])
                );
                
                error_log('Executing mark as read query: ' . $query);
                $result = $wpdb->query($query);
                
                if ($result === false) {
                    error_log('Database error in markAsRead: ' . $wpdb->last_error);
                    
                    // Fall back to model method
                    $result = $this->notification_model->markAsRead($notification_ids, $user_id);
                } else {
                    error_log('Successfully marked ' . $result . ' notifications as read');
                }
                
                if ($result === false || $result < 1) {
                    // Check if any of the notifications actually exist and belong to the user
                    $check_query = $wpdb->prepare(
                        "SELECT COUNT(*) FROM {$table_name} 
                        WHERE id IN ({$placeholders}) AND user_id = %d",
                        array_merge($notification_ids, [$user_id])
                    );
                    
                    $check_result = $wpdb->get_var($check_query);
                    
                    if ($check_result < 1) {
                        wp_send_json([
                            'status' => false,
                            'message' => esc_html__('No matching notifications found', 'kc-lang')
                        ]);
                        return;
                    }
                }
            }
            
            // Get updated unread count using direct SQL
            $count_query = $wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE user_id = %d AND is_read = 0",
                $user_id
            );
            
            $unread_count = $wpdb->get_var($count_query);
            
            if ($unread_count === null) {
                error_log('Database error in unread count query: ' . $wpdb->last_error);
                // Fall back to model method
                $unread_count = $this->notification_model->countUserNotifications($user_id, true);
            }
            
            $unread_count = (int)$unread_count;
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Notification(s) marked as read successfully', 'kc-lang'),
                'data' => [
                    'unread_count' => $unread_count
                ]
            ]);
        } catch (Exception $e) {
            error_log('Exception in markAsRead: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error marking notifications as read', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Mark all notifications as read for current user
     *
     * @return void
     */
    public function markAllAsRead() {
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
            return;
        }
        
        try {

            
            $result = $this->notification_model->markAllAsRead($user_id);
            
            if (!$result) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to mark all notifications as read', 'kc-lang')
                ]);
                return;
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('All notifications marked as read successfully', 'kc-lang'),
                'data' => [
                    'unread_count' => 0
                ]
            ]);
        } catch (Exception $e) {
            error_log('Error in markAllAsRead: ' . $e->getMessage());
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error marking all notifications as read', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete a notification
     *
     * @return void
     */
    public function deleteNotification($request) {
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
            return;
        }
        
        $notification_id = isset($request['id']) ? intval($request['id']) : 0;
        
        if (empty($notification_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Notification ID required', 'kc-lang')
            ]);
            return;
        }
        
        try {

            
            // Get notification to verify ownership
            $notification = $this->notification_model->getNotification($notification_id);
            
            if (empty($notification)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Notification not found', 'kc-lang')
                ]);
                return;
            }
            
            // Check if the notification belongs to the current user
            if ($notification->user_id != $user_id && !current_user_can('administrator')) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to delete this notification', 'kc-lang')
                ]);
                return;
            }
            
            // Delete the notification
            $result = $this->notification_model->deleteNotification($notification_id);
            
            if (!$result) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to delete notification', 'kc-lang')
                ]);
                return;
            }
            
            // Get updated unread count
            $unread_count = $this->notification_model->countUserNotifications($user_id, true);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Notification deleted successfully', 'kc-lang'),
                'data' => [
                    'unread_count' => $unread_count
                ]
            ]);
        } catch (Exception $e) {
            error_log('Error in deleteNotification: ' . $e->getMessage());
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error deleting notification', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create a new notification
     *
     * @return void
     */
    public function createNotification() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Detailed logging of request data
        error_log('createNotification request_data: ' . json_encode($request_data));
        error_log('Current user ID: ' . $user_id);
        
        // Only administrators can create notifications for others
        if (!current_user_can('administrator')) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You do not have permission to perform this action', 'kc-lang')
            ]);
            return;
        }
        
        // Validate request
        $rules = [
            'user_id' => 'required',
            'title' => 'required',
            'message' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            error_log('Validation errors: ' . json_encode($errors));
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
            return;
        }
        
        try {
            // Check if database tables exist before proceeding
            global $wpdb;
            $table_name = $wpdb->prefix . 'kc_notifications';
            
            $table_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                    DB_NAME,
                    $table_name
                )
            );
            
            if (!$table_exists) {
                error_log('Notifications table does not exist. Attempting to create it.');
                
                
                // Check again if table exists
                $table_exists = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                        DB_NAME,
                        $table_name
                    )
                );
                
                if (!$table_exists) {
                    error_log('Failed to create notifications table');
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Notifications functionality is not available. Please contact support.', 'kc-lang')
                    ]);
                    return;
                }
            }
            
            // Validate user exists
            $target_user_id = intval($request_data['user_id']);
            $user = get_user_by('id', $target_user_id);
            
            if (!$user) {
                error_log('User does not exist: ' . $target_user_id);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('User does not exist', 'kc-lang')
                ]);
                return;
            }
            
            // Create notification data
            $notification_data = [
                'user_id' => $target_user_id,
                'title' => sanitize_text_field($request_data['title']),
                'message' => sanitize_textarea_field($request_data['message']),
                'type' => isset($request_data['type']) ? sanitize_text_field($request_data['type']) : 'info',
                'reference_id' => isset($request_data['reference_id']) ? intval($request_data['reference_id']) : null,
                'reference_type' => isset($request_data['reference_type']) ? sanitize_text_field($request_data['reference_type']) : null,
                'is_read' => 0,
                'created_at' => current_time('mysql')
            ];
            
            error_log('About to insert notification with data: ' . json_encode($notification_data));
            
            // Try direct SQL insert approach first
            $result = $wpdb->insert($table_name, $notification_data);
            
            if ($result === false) {
                error_log('Direct wpdb insert failed: ' . $wpdb->last_error);
                
                // Fall back to model method
                $notification_id = $this->notification_model->createNotification($notification_data);
            } else {
                $notification_id = $wpdb->insert_id;
                error_log('Direct wpdb insert succeeded with ID: ' . $notification_id);
            }
            
            if (!$notification_id) {
                error_log('Failed to insert notification after all attempts');
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to create notification', 'kc-lang')
                ]);
                return;
            }
            
            // Get the new notification
            $notification = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$table_name} WHERE id = %d",
                $notification_id
            ));
            
            if (!$notification) {
                error_log('Notification was inserted but could not be retrieved');
                // Try model method as fallback
                $notification = $this->notification_model->getNotification($notification_id);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Notification created successfully', 'kc-lang'),
                'data' => $notification
            ]);
        } catch (Exception $e) {
            error_log('Exception in createNotification: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error creating notification', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create bulk notifications for multiple users or roles
     *
     * @return void
     */
    public function createBulkNotifications() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Only administrators and clinic admins can create bulk notifications
        if (!current_user_can('administrator') && !current_user_can(KIVI_CARE_PREFIX . 'clinic_admin')) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You do not have permission to perform this action', 'kc-lang')
            ]);
            return;
        }
        
        // Validate request
        $rules = [
            'title' => 'required',
            'message' => 'required'
        ];
        
        // Either user_ids or roles must be provided
        if (empty($request_data['user_ids']) && empty($request_data['roles'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Either user IDs or roles must be provided', 'kc-lang')
            ]);
            return;
        }
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
            return;
        }
        
        try {

            
            $title = sanitize_text_field($request_data['title']);
            $message = sanitize_textarea_field($request_data['message']);
            $type = isset($request_data['type']) ? sanitize_text_field($request_data['type']) : 'info';
            $reference_id = isset($request_data['reference_id']) ? intval($request_data['reference_id']) : null;
            $reference_type = isset($request_data['reference_type']) ? sanitize_text_field($request_data['reference_type']) : null;
            $clinic_id = isset($request_data['clinic_id']) ? intval($request_data['clinic_id']) : null;
            
            $user_ids = [];
            
            // If user_ids are provided directly
            if (!empty($request_data['user_ids']) && is_array($request_data['user_ids'])) {
                $user_ids = array_map('intval', $request_data['user_ids']);
            }
            
            // If roles are provided
            if (!empty($request_data['roles']) && is_array($request_data['roles'])) {
                $roles = array_map('sanitize_text_field', $request_data['roles']);
                
                // Map Kivicare role names to WordPress role names
                $mapped_roles = [];
                foreach ($roles as $role) {
                    if (in_array($role, ['administrator', 'subscriber'])) {
                        $mapped_roles[] = $role;
                    } else {
                        $mapped_roles[] = KIVI_CARE_PREFIX . $role;
                    }
                }
                
                // Get users with these roles for this clinic
                if (!empty($clinic_id) && function_exists('kcGetClinicUsers')) {
                    $clinic_users = kcGetClinicUsers($clinic_id);
                    
                    // Filter users by role
                    foreach ($clinic_users as $clinic_user_id) {
                        $user = get_user_by('id', $clinic_user_id);
                        
                        if ($user) {
                            $has_role = false;
                            foreach ($mapped_roles as $role) {
                                if (in_array($role, $user->roles)) {
                                    $has_role = true;
                                    break;
                                }
                            }
                            
                            if ($has_role) {
                                $user_ids[] = $clinic_user_id;
                            }
                        }
                    }
                } else {
                    // Get all users with these roles
                    foreach ($mapped_roles as $role) {
                        $role_users = get_users(['role' => $role]);
                        foreach ($role_users as $user) {
                            $user_ids[] = $user->ID;
                        }
                    }
                }
            }
            
            // Remove duplicates
            $user_ids = array_unique($user_ids);
            
            if (empty($user_ids)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('No users found matching the specified criteria', 'kc-lang')
                ]);
                return;
            }
            
            // Create notifications
            $created_count = 0;
            $notifications = [];
            
            foreach ($user_ids as $target_user_id) {
                $notification_data = [
                    'user_id' => $target_user_id,
                    'title' => $title,
                    'message' => $message,
                    'type' => $type,
                    'reference_id' => $reference_id,
                    'reference_type' => $reference_type,
                ];
                
                $notification_id = $this->notification_model->createNotification($notification_data);
                
                if ($notification_id) {
                    $created_count++;
                    $notifications[] = $notification_id;
                }
            }
            
            wp_send_json([
                'status' => true,
                'message' => sprintf(esc_html__('Created %d notifications successfully', 'kc-lang'), $created_count),
                'data' => [
                    'count' => $created_count,
                    'notification_ids' => $notifications
                ]
            ]);
        } catch (Exception $e) {
            error_log('Error in createBulkNotifications: ' . $e->getMessage());
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error creating bulk notifications', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get notification settings
     * 
     * @return void
     */
    public function getNotificationSettings() {
        $user_id = get_current_user_id();
        
        // Only administrators and clinic admins can view settings
        if (!current_user_can('administrator') && !current_user_can(KIVI_CARE_PREFIX . 'clinic_admin')) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You do not have permission to view notification settings', 'kc-lang')
            ]);
            return;
        }
        
        try {
            // Get clinic ID from request or user's clinic
            $request_data = $this->request->getInputs();
            $clinic_id = isset($request_data['clinic_id']) ? intval($request_data['clinic_id']) : 0;
            
            if (empty($clinic_id) && current_user_can(KIVI_CARE_PREFIX . 'clinic_admin')) {
                $clinic_id = $this->getUserClinicId($user_id);
            }
            
            // Get notification settings for the clinic
            $settings = $this->getClinicNotificationSettings($clinic_id);
            
            wp_send_json([
                'status' => true,
                'data' => $settings
            ]);
        } catch (Exception $e) {
            error_log('Error in getNotificationSettings: ' . $e->getMessage());
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error retrieving notification settings', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Save notification settings
     * 
     * @return void
     */
    public function saveNotificationSettings() {
        $user_id = get_current_user_id();
        $request_data = $this->request->getInputs();
        
        // Only administrators and clinic admins can save settings
        if (!current_user_can('administrator') && !current_user_can(KIVI_CARE_PREFIX . 'clinic_admin')) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You do not have permission to save notification settings', 'kc-lang')
            ]);
            return;
        }
        
        // Validate request
        $rules = [
            'clinic_id' => 'required',
            'settings' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
            return;
        }
        
        // If clinic admin, make sure they're updating their own clinic
        if (current_user_can(KIVI_CARE_PREFIX . 'clinic_admin')) {
            $user_clinic_id = $this->getUserClinicId($user_id);
            
            if ($user_clinic_id != $request_data['clinic_id']) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to update settings for this clinic', 'kc-lang')
                ]);
                return;
            }
        }
        
        try {
            $clinic_id = intval($request_data['clinic_id']);
            $settings = $request_data['settings'];
            
            // Validate and sanitize settings
            $sanitized_settings = [];
            $valid_settings = [
                'enable_appointment_notifications',
                'enable_patient_notifications',
                'enable_doctor_notifications',
                'enable_receptionist_notifications',
                'enable_clinic_admin_notifications',
                'auto_cleanup_days'
            ];
            
            foreach ($valid_settings as $key) {
                if (isset($settings[$key])) {
                    if ($key === 'auto_cleanup_days') {
                        $sanitized_settings[$key] = intval($settings[$key]);
                    } else {
                        $sanitized_settings[$key] = $settings[$key] ? 'yes' : 'no';
                    }
                }
            }
            
            // Save settings
            $this->saveClinicNotificationSettings($clinic_id, $sanitized_settings);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Notification settings saved successfully', 'kc-lang')
            ]);
        } catch (Exception $e) {
            error_log('Error in saveNotificationSettings: ' . $e->getMessage());
            
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error saving notification settings', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get user's role
     * 
     * @param int $user_id User ID
     * @return string User role
     */
    private function getUserRole($user_id) {
        $user = get_userdata($user_id);
        
        if (!$user) {
            return '';
        }
        
        if (in_array('administrator', $user->roles)) {
            return 'administrator';
        } elseif (in_array(KIVI_CARE_PREFIX . 'clinic_admin', $user->roles)) {
            return 'clinic_admin';
        } elseif (in_array(KIVI_CARE_PREFIX . 'doctor', $user->roles)) {
            return 'doctor';
        } elseif (in_array(KIVI_CARE_PREFIX . 'receptionist', $user->roles)) {
            return 'receptionist';
        } elseif (in_array(KIVI_CARE_PREFIX . 'patient', $user->roles)) {
            return 'patient';
        }
        
        return '';
    }

    /**
     * Get clinic ID for a user
     * 
     * @param int $user_id User ID
     * @return int Clinic ID
     */
    private function getUserClinicId($user_id) {
        $user_role = $this->getUserRole($user_id);
        
        switch ($user_role) {
            case 'clinic_admin':
                $clinic = $this->db->get_row("SELECT id FROM {$this->db->prefix}kc_clinics WHERE clinic_admin_id = {$user_id}");
                return $clinic ? $clinic->id : 0;
                
            case 'doctor':
                $doctor_mapping = $this->db->get_row("SELECT clinic_id FROM {$this->db->prefix}kc_doctor_clinic_mappings WHERE doctor_id = {$user_id} LIMIT 1");
                return $doctor_mapping ? $doctor_mapping->clinic_id : 0;
                
            case 'receptionist':
                $receptionist_mapping = $this->db->get_row("SELECT clinic_id FROM {$this->db->prefix}kc_receptionist_clinic_mappings WHERE receptionist_id = {$user_id} LIMIT 1");
                return $receptionist_mapping ? $receptionist_mapping->clinic_id : 0;
                
            case 'patient':
                $patient_mapping = $this->db->get_row("SELECT clinic_id FROM {$this->db->prefix}kc_patient_clinic_mappings WHERE patient_id = {$user_id} LIMIT 1");
                return $patient_mapping ? $patient_mapping->clinic_id : 0;
                
            default:
                return 0;
        }
    }
    
    /**
     * Get all clinics associated with a user
     * 
     * @param int $user_id User ID
     * @return array Clinic IDs
     */
    private function getUserClinicsPrivate($user_id) {
        $user_role = $this->getUserRole($user_id);
        $clinic_ids = [];
        
        try {
            switch ($user_role) {
                case 'administrator':
                    // Admin has access to all clinics
                    $clinics = $this->db->get_results("SELECT id FROM {$this->db->prefix}kc_clinics");
                    if ($clinics) {
                        foreach ($clinics as $clinic) {
                            $clinic_ids[] = $clinic->id;
                        }
                    }
                    break;
                    
                case 'clinic_admin':
                    $clinic = $this->db->get_row($this->db->prepare(
                        "SELECT id FROM {$this->db->prefix}kc_clinics WHERE clinic_admin_id = %d",
                        $user_id
                    ));
                    if ($clinic) {
                        $clinic_ids[] = $clinic->id;
                    }
                    break;
                    
                case 'doctor':
                    $doctor_mappings = $this->db->get_results($this->db->prepare(
                        "SELECT clinic_id FROM {$this->db->prefix}kc_doctor_clinic_mappings WHERE doctor_id = %d",
                        $user_id
                    ));
                    if ($doctor_mappings) {
                        foreach ($doctor_mappings as $mapping) {
                            $clinic_ids[] = $mapping->clinic_id;
                        }
                    }
                    break;
                    
                case 'receptionist':
                    $receptionist_mappings = $this->db->get_results($this->db->prepare(
                        "SELECT clinic_id FROM {$this->db->prefix}kc_receptionist_clinic_mappings WHERE receptionist_id = %d",
                        $user_id
                    ));
                    if ($receptionist_mappings) {
                        foreach ($receptionist_mappings as $mapping) {
                            $clinic_ids[] = $mapping->clinic_id;
                        }
                    }
                    break;
                    
                case 'patient':
                    $patient_mappings = $this->db->get_results($this->db->prepare(
                        "SELECT clinic_id FROM {$this->db->prefix}kc_patient_clinic_mappings WHERE patient_id = %d",
                        $user_id
                    ));
                    if ($patient_mappings) {
                        foreach ($patient_mappings as $mapping) {
                            $clinic_ids[] = $mapping->clinic_id;
                        }
                    }
                    break;
            }
            
            // If no clinics found, return array with default clinic
            if (empty($clinic_ids)) {
                $default_clinic = $this->db->get_var("SELECT id FROM {$this->db->prefix}kc_clinics LIMIT 1");
                if ($default_clinic) {
                    $clinic_ids[] = $default_clinic;
                }
            }
            
            return $clinic_ids;
        } catch (\Exception $e) {
            error_log('Error getting user clinics: ' . $e->getMessage());
            // Return array with at least one clinic if possible
            $default_clinic = $this->db->get_var("SELECT id FROM {$this->db->prefix}kc_clinics LIMIT 1");
            if ($default_clinic) {
                return [$default_clinic];
            }
            return [];
        }
    }
    
    /**
     * Get all users in a clinic with specific roles
     * 
     * @param int $clinic_id Clinic ID
     * @param array $roles User roles to include
     * @return array Users
     */
    private function getAllClinicUsers($clinic_id, $roles = ['administrator', 'clinic_admin', 'doctor', 'receptionist', 'patient']) {
        $users = [];
        
        // Add administrators
        if (in_array('administrator', $roles)) {
            $admin_users = get_users(['role' => 'administrator']);
            $users = array_merge($users, $admin_users);
        }
        
        // Add clinic admins
        if (in_array('clinic_admin', $roles)) {
            $clinic = $this->db->get_row("SELECT clinic_admin_id FROM {$this->db->prefix}kc_clinics WHERE id = {$clinic_id}");
            if ($clinic && $clinic->owner_id) {
                $clinic_admin = get_user_by('id', $clinic->owner_id);
                if ($clinic_admin) {
                    $users[] = $clinic_admin;
                }
            }
        }
        
        // Add doctors
        if (in_array('doctor', $roles)) {
            $users = array_merge($users, $this->getClinicDoctors($clinic_id));
        }
        
        // Add receptionists
        if (in_array('receptionist', $roles)) {
            $receptionist_mappings = $this->db->get_results("
                SELECT receptionist_id FROM {$this->db->prefix}kc_receptionist_clinic_mappings 
                WHERE clinic_id = {$clinic_id}
            ");
            
            foreach ($receptionist_mappings as $mapping) {
                $receptionist = get_user_by('id', $mapping->receptionist_id);
                if ($receptionist) {
                    $users[] = $receptionist;
                }
            }
        }
        
        // Add patients
        if (in_array('patient', $roles)) {
            $users = array_merge($users, $this->getClinicPatients($clinic_id));
        }
        
        return $users;
    }
    
    /**
     * Get all doctors in a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return array Doctors
     */
    private function getClinicDoctors($clinic_id) {
        $doctors = [];
        $doctor_mappings = $this->db->get_results("
            SELECT doctor_id FROM {$this->db->prefix}kc_doctor_clinic_mappings 
            WHERE clinic_id = {$clinic_id}
        ");
        
        foreach ($doctor_mappings as $mapping) {
            $doctor = get_user_by('id', $mapping->doctor_id);
            if ($doctor) {
                $doctors[] = $doctor;
            }
        }
        
        return $doctors;
    }
    
    /**
     * Get all patients in a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return array Patients
     */
    private function getClinicPatients($clinic_id) {
        $patients = [];
        $patient_mappings = $this->db->get_results("
            SELECT patient_id FROM {$this->db->prefix}kc_patient_clinic_mappings 
            WHERE clinic_id = {$clinic_id}
        ");
        
        foreach ($patient_mappings as $mapping) {
            $patient = get_user_by('id', $mapping->patient_id);
            if ($patient) {
                $patients[] = $patient;
            }
        }
        
        return $patients;
    }

    /**
     * Get notification settings for a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return array Settings
     */
    private function getClinicNotificationSettings($clinic_id) {
        // Default settings
        $default_settings = [
            'enable_appointment_notifications' => 'yes',
            'enable_patient_notifications' => 'yes',
            'enable_doctor_notifications' => 'yes',
            'enable_receptionist_notifications' => 'yes',
            'enable_clinic_admin_notifications' => 'yes',
            'auto_cleanup_days' => 90
        ];
        
        // Get settings from options
        $option_name = 'kivicare_notification_settings_' . $clinic_id;
        $saved_settings = get_option($option_name, []);
        
        // Merge with defaults
        $settings = array_merge($default_settings, $saved_settings);
        
        return $settings;
    }

    /**
     * Save notification settings for a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @param array $settings Settings array
     * @return bool Success or failure
     */
    private function saveClinicNotificationSettings($clinic_id, $settings) {
        $option_name = 'kivicare_notification_settings_' . $clinic_id;
        return update_option($option_name, $settings);
    }
}