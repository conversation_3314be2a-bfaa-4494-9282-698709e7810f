<template>
    <div class="relative" v-click-outside="closeDropdown">
      <button
        type="button"
        class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 text-left relative"
        @click="toggleDropdown"
      >
        <span :class="{ 'text-gray-500': !selectedOption }">
          {{ selectedOption ? (selectedOption.label || selectedOption.display_name) : placeholder }}
        </span>
        
        <!-- Clear and Dropdown Icons Container -->
        <span class="absolute inset-y-0 right-0 flex items-center gap-1 pr-2">
          <!-- Clear Button - Show only when there's a selection -->
          <button
            v-if="selectedOption"
            type="button"
            class="p-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            @click.stop="clearSelection"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-4 h-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          
          <!-- Dropdown Arrow -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-5 h-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="none"
            stroke="currentColor"
          >
            <path 
              d="M7 7l3-3 3 3m0 6l-3 3-3-3" 
              stroke-width="1.5" 
              stroke-linecap="round" 
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </button>
  
      <div
        v-if="isOpen"
        class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg"
      >
        <div class="p-2">
          <input
            type="text"
            class="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-400"
            placeholder="Search..."
            v-model="searchTerm"
            @click.stop
            ref="searchInput"
          />
        </div>
  
        <div class="max-h-60 overflow-y-auto">
          <template v-if="filteredOptions.length">
            <button
              v-for="option in filteredOptions"
              :key="option.id"
              class="w-full px-4 py-2 text-left hover:bg-gray-100 focus:outline-none"
              @click="selectOption(option)"
            >
              {{ option.label || option.display_name }}
            </button>
          </template>
          <div v-else class="px-4 py-2 text-gray-500">
            No results found
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'SearchableSelect',
    
    props: {
      options: {
        type: Array,
        required: true
      },
      value: {
        required: true
      },
      placeholder: {
        type: String,
        default: 'Select an option'
      }
    },
  
    data() {
      return {
        isOpen: false,
        searchTerm: '',
      }
    },
  
    computed: {
      selectedOption() {
        return this.options.find(opt => opt.id === this.value)
      },
      
      filteredOptions() {
        return this.options.filter(option => {
          const label = (option.label || option.display_name || '').toLowerCase()
          return label.includes(this.searchTerm.toLowerCase())
        })
      }
    },
  
    methods: {
      toggleDropdown() {
        this.isOpen = !this.isOpen
        if (this.isOpen) {
          this.$nextTick(() => {
            this.$refs.searchInput?.focus()
          })
        }
      },
  
      closeDropdown() {
        this.isOpen = false
        this.searchTerm = ''
      },
  
      selectOption(option) {
        this.$emit('input', option.id)
        this.$emit('change', option.id)
        this.closeDropdown()
      },
  
      clearSelection(event) {
        event.stopPropagation()
        this.$emit('input', '')
        this.$emit('change', '')
      }
    },
  
    directives: {
      'click-outside': {
        bind(el, binding) {
          el.clickOutsideEvent = function(event) {
            if (!(el === event.target || el.contains(event.target))) {
              binding.value(event)
            }
          }
          document.addEventListener('click', el.clickOutsideEvent)
        },
        unbind(el) {
          document.removeEventListener('click', el.clickOutsideEvent)
        }
      }
    }
  }
  </script>