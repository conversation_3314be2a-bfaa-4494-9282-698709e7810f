(function (jQuery) {
    'use strict';

    class  RegisterClinic {
        shortCodeElement
        currentStep=0
        constructor(element) {
            this.shortCodeElement=element;
            this.steps = this.shortCodeElement.find(".form-step");
            let currentStep = 0;

            this.addEventListener()
            this.initQueryValidation()

            this.updateSteps();
        }
        initQueryValidation(){
            this.clinicRegisterFormValidateInstace= this.shortCodeElement.find('.clinic_register_form').validate({
                errorClass: "text-red-500 text-sm mt-1", // Tailwind classes for error messages
                errorElement: "div",
                errorPlacement: function(error, element) {
                    if (element.attr("name") == "fname" || element.attr("name") == "lname" ) {
                        error.insertAfter("#lastname");
                    } else if(element.attr("name") == "terms"){
                        error.insertAfter(element.parent())
                    }else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    subscription_plan: {
                        required: true,
                    },
                    clinic_user_first_name: {
                        required: true,
                        minlength: 2
                    },
                    clinic_user_last_name: {
                        required: true,
                        minlength: 2
                    },
                    clinic_email: {
                        required: true,
                        email: true
                    },
                    clinic_phone: {
                        required: true,
                        // digits: true,
                        minlength: 10
                    },
                    clinic_password: {
                        required: true,
                        minlength: 6
                    },
                    terms:{
                        required: true,
                    }
                },
                messages: {
                    clinic_user_first_name: {
                        required: "First name is required.",
                        minlength: "First name must be at least 2 characters long."
                    },
                    clinic_user_last_name: {
                        required: "Last name is required.",
                        minlength: "Last name must be at least 2 characters long."
                    },
                    clinic_email: {
                        required: "Email address is required.",
                        email: "Please enter a valid email address."
                    },
                    clinic_phone: {
                        required: "Phone number is required.",
                        digits: "Please enter only digits.",
                        minlength: "Phone number must be at least 10 digits."
                    },
                    clinic_password: {
                        required: "Password is required.",
                        minlength: "Password must be at least 6 characters long."
                    },
                    terms: {
                        required: "You must agree to the Terms of Service and Privacy Policy." // Custom error message
                    }
                },
                submitHandler: (form)=> {
                    this.clinicRegisterFormSubmit(form)
                }
            });

            this.clinicDetailFormValidateInstace= this.shortCodeElement.find('.clinic_details').validate({
                rules: {
                    doctor_count: {
                        required: true,
                        min: 0
                    }
                },
                messages: {
                    doctor_count: {
                        required: "Please specify the number of doctors.",
                        min: "Number of additional doctors must be 0 or greater."
                    }
                },
                errorPlacement:  (error, element) =>{
                    if (element.attr("name") === "doctor_count") {
                        jQuery("#doctorCountError").text(error.text()).removeClass("hidden");
                    }
                },
                success:  (label, element) =>{
                    if (jQuery(element).attr("name") === "doctor_count") {
                        jQuery("#doctorCountError").addClass("hidden");
                    }
                },
                submitHandler: (form) =>{
                    this.clinicDetailFormSubmit(form)
                }
            });

        }
        addEventListener(){
            this.shortCodeElement.find('[name="subscription_plan"]').on('change',this.updateSelectedSubcription.bind(this))


            const $counterWidget = this.shortCodeElement.find(".counter-widget");
            const $decrementButton = $counterWidget.find(".decrement-btn");
            const $incrementButton = $counterWidget.find(".increment-btn");
            const $valueDisplay = $counterWidget.find(".counter-value");
            const $hiddenValueDisplay = $counterWidget.find(".doctor_count");


            $decrementButton.on("click", function () {
                let currentValue = parseInt($valueDisplay.text(), 10);
                if (currentValue > 0) {
                    $valueDisplay.text(currentValue - 1);
                    $hiddenValueDisplay.val(currentValue - 1)
                }
            });

            $incrementButton.on("click", function () {
                let currentValue = parseInt($valueDisplay.text(), 10);
                $valueDisplay.text(currentValue + 1);
                $hiddenValueDisplay.val(currentValue + 1)
            });
        }
        clinicDetailFormSubmit(form){
            // e.preventDefault()
            // Gather form data
            const formData = new FormData(form);

            formData.append('subscription_plan_id',jQuery("[name='subscription_plan']:checked").val())
            // Show the loader
            this.shortCodeElement.find(".buttonText").addClass("hidden"); // Hide the button text
            this.shortCodeElement.find(".loader").removeClass("hidden"); // Show the spinner
            // Call the post function
            this.post('update_clinic_detail', formData)
                .then(response => {
                    console.log('Success:', response.data);
                    if(response.data.status && response.data.data && response.data.data.redirect_to){
                        // Make sure we have a valid URL before redirecting
                        const redirectUrl = response.data.data.redirect_to;
                        console.log('Redirecting to:', redirectUrl);
                        
                        // Ensure the URL is valid and contains 'membership-checkout'
                        if(redirectUrl.indexOf('undefined') === -1 && redirectUrl.indexOf('membership-checkout') !== -1) {
                            // Add a small delay to ensure the browser has time to process
                            setTimeout(function() {
                                window.location.href = redirectUrl;
                            }, 100);
                        } else {
                            console.error('Invalid redirect URL:', redirectUrl);
                            alert('There was a problem with the checkout process. Please try again.');
                            this.shortCodeElement.find(".buttonText").removeClass("hidden");
                            this.shortCodeElement.find(".loader").addClass("hidden");
                        }
                    } else {
                        console.error('Invalid response format:', response.data);
                        alert('There was a problem with the checkout process. Please try again.');
                        this.shortCodeElement.find(".buttonText").removeClass("hidden");
                        this.shortCodeElement.find(".loader").addClass("hidden");
                    }
                })
                .catch(error => {
                    this.shortCodeElement.find(".buttonText").removeClass("hidden"); // Hide the button text
                    this.shortCodeElement.find(".loader").addClass("hidden"); // Show the spinner
                    console.error('Error:', error);
                    alert('There was a problem with the checkout process. Please try again.');
                    // Handle error (e.g., show an error message)
                });

        }
        clinicRegisterFormSubmit(form){
            // Gather form data
            const formData = new FormData(form);

            // Show the loader
            this.shortCodeElement.find(".buttonText").addClass("hidden"); // Hide the button text
            this.shortCodeElement.find(".loader").removeClass("hidden"); // Show the spinner
            // Call the post function
            this.post('register_clinic', formData)
                .then(response => {
                    console.log('Success:', response.data);
                    window.KC_CLINIC_REGISTER.ajax_get_nonce =response.data.ajax_get_nonce
                    window.KC_CLINIC_REGISTER.ajax_post_nonce=response.data.ajax_post_nonce

                    this.shortCodeElement.find(".buttonText").removeClass("hidden"); // Hide the button text
                    this.shortCodeElement.find(".loader").addClass("hidden"); // Show the spinner
                    if (this.currentStep < this.steps.length - 1) {
                        this.currentStep++;
                        this.updateSteps();
                    }
                })
                .catch(error => {
                    this.shortCodeElement.find(".buttonText").removeClass("hidden"); // Hide the button text
                    this.shortCodeElement.find(".loader").addClass("hidden"); // Show the spinner
                    console.error('Error:', error);
                    // Handle error (e.g., show an error message)
                });
        }
        updateSelectedSubcription(e){
            jQuery('.subscription-plan-card').removeClass(['border-purple-600', 'ring-1', 'ring-purple-600'])
            if (e.target.checked) {
                const selectedCard = jQuery(e.target).closest('.subscription-plan-card-wrapper').find('.subscription-plan-card');
                selectedCard.addClass(['border-purple-600', 'ring-1', 'ring-purple-600']);
            }
        }

        updateSteps(){
            let self=this
            self.steps.each(function (index) {
                if (index === self.currentStep) {
                    jQuery(this).removeClass("hidden").addClass("active");
                } else {
                    jQuery(this).addClass("hidden").removeClass("active");
                }
            });

            jQuery('.current-active-step').html(self.currentStep + 1);
            jQuery('.current-active-step-dot').children().each(function ($dot) {
                if (self.currentStep >= $dot) {
                    jQuery(this).addClass('bg-purple-600')
                    jQuery(this).removeClass('bg-gray-200')
                } else {
                    jQuery(this).removeClass('bg-purple-600')
                    jQuery(this).addClass('bg-gray-200')
                }
            })
        }
        post(route, data = {}, frontEnd = false, headers = {                headers: {'Content-Type': 'application/json'}            }) {
                window.ajaxurl = KC_CLINIC_REGISTER.ajax_url;
                window.nonce = KC_CLINIC_REGISTER.ajax_post_nonce;

                let url = ajaxurl;
                if (data.action === undefined) {
                    url = ajaxurl + '?action=ajax_post';
                }

                if (route === undefined) {
                    return false
                }

                if (data.append !== undefined) {
                    data.append('route_name', route);
                    data.append('_ajax_nonce', nonce)
                } else {
                    data.route_name = route;
                    data._ajax_nonce = nonce;
                }

                return new Promise((resolve, reject, headers) => {
                    axios.post(url, data, headers)
                        .then((data) => {

                            resolve(data)
                        })
                        .catch((error) => {
                            reject(error)
                        });
                })
        }
        get(route, data, frontEnd = false) {

            window.ajaxurl = KC_CLINIC_REGISTER.ajax_url;
            window.nonce = KC_CLINIC_REGISTER.ajax_get_nonce;

            data._ajax_nonce = KC_CLINIC_REGISTER.ajax_get_nonce;
            let url = ajaxurl;
            if (data.action === undefined) {
                url = ajaxurl + '?action=ajax_get';
            }

            if (route === undefined) {
                return false
            }

            url = url + '&route_name=' + route;
            return new Promise((resolve, reject) => {
                axios.get(url, {params: data})
                    .then((data) => {

                        resolve(data)
                    })
                    .catch((error) => {
                        reject(error)
                    });
            })
        }
    }
    jQuery(function (){
        jQuery('.kc-clinic-register').each(function (el){
            new RegisterClinic(jQuery(this));
        })

    });
    // jQuery(function () {
    //
    //     const stripe = Stripe(window.KC_CLINIC_REGISTER.stripe.publishableKey);
    //     const elements = stripe.elements();
    //
    //     const style = {
    //         base: {
    //             fontSize: '16px',
    //             color: '#32325d',
    //             fontFamily: '"Inter", sans-serif',
    //         }
    //     };
    //
    //     const cardNumber = elements.create('cardNumber', {style});
    //     const cardExpiry = elements.create('cardExpiry', {style});
    //     const cardCvc = elements.create('cardCvc', {style});
    //
    //     cardNumber.mount('#card-number-element');
    //     cardExpiry.mount('#card-expiry-element');
    //     cardCvc.mount('#card-cvc-element');
    //
    //     // Handle validation errors
    //     [cardNumber, cardExpiry, cardCvc].forEach(element => {
    //         element.addEventListener('change', ({error}) => {
    //             const displayError = document.getElementById('card-errors');
    //             displayError.textContent = error ? error.message : '';
    //         });
    //     });
    //
    //     // Handle form submission
    //     // document.getElementById('clinic_register_form').addEventListener('submit', async (e) => {
    //     //     e.preventDefault();
    //     //
    //     //     const {token, error} = await stripe.createToken(cardNumber);
    //     //
    //     //     if (error) {
    //     //         document.getElementById('card-errors').textContent = error.message;
    //     //         return;
    //     //     }
    //     //
    //     //     // Add token to form
    //     //     const hiddenInput = document.createElement('input');
    //     //     hiddenInput.setAttribute('type', 'hidden');
    //     //     hiddenInput.setAttribute('name', 'stripeToken');
    //     //     hiddenInput.setAttribute('value', token.id);
    //     //     e.target.appendChild(hiddenInput);
    //     //
    //     //     // Submit form
    //     //     e.target.submit();
    //     // });
    //
    //
    //
    // })

})(jQuery);

