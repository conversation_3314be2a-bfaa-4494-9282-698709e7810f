<template>
  <div v-if="openModal" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="handleClose"
    ></div>

    <!-- Modal -->
    <div class="flex min-h-screen items-start justify-center p-4">
      <div class="relative w-full max-w-7xl bg-white rounded-lg shadow-xl p-4">
        <form @submit.prevent="handleSubmit" id="doctorDataForm" novalidate>
          <!-- Basic Details Section -->
          <div class="mb-8">
            {{
              doctorId
                ? formTranslation.doctor.edit_doctor
                : formTranslation.doctor.add_doctor
            }}

            <h2 class="text-xl font-semibold text-gray-800 mb-6">
              {{ formTranslation.common.basic_details }}
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- First Name -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.fname }}
                  <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  v-model="doctorData.first_name"
                  :placeholder="formTranslation.doctor.doctor_name"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  :class="{
                    'border-red-500':
                      submitted && $v.doctorData.first_name.$error,
                  }"
                />
                <span
                  v-if="submitted && !$v.doctorData.first_name.required"
                  class="text-xs text-red-500"
                >
                  {{ formTranslation.common.fname_required }}
                </span>
              </div>

              <!-- Last Name -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.lname }}
                  <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  v-model="doctorData.last_name"
                  :placeholder="formTranslation.doctor.lname_placeholder"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  :class="{
                    'border-red-500':
                      submitted && $v.doctorData.last_name.$error,
                  }"
                />
                <span
                  v-if="submitted && !$v.doctorData.last_name.required"
                  class="text-xs text-red-500"
                >
                  {{ formTranslation.common.lname_required }}
                </span>
              </div>

              <!-- Email -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.email }}
                  <span class="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  v-model="doctorData.user_email"
                  :placeholder="formTranslation.doctor.email_placeholder"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  :class="{
                    'border-red-500':
                      submitted && $v.doctorData.user_email.$error,
                  }"
                />
                <span
                  v-if="submitted && !$v.doctorData.user_email.required"
                  class="text-xs text-red-500"
                >
                  {{ formTranslation.common.email_required }}
                </span>
              </div>

              <!-- Clinic Selection (for admin) -->
              <div
                v-if="
                  userData.addOns.kiviPro && getUserRole() === 'administrator'
                "
              >
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.clinic.select_clinic }}
                  <span class="text-red-500">*</span>
                </label>
                <multi-select
                  v-model="doctorData.clinic_id"
                  :options="clinics"
                  :multiple="true"
                  :loading="clinicMultiselectLoader"
                  label="label"
                  track-by="id"
                  :placeholder="formTranslation.doctor.search_placeholder"
                  class="w-full"
                />
                <span
                  v-if="submitted && !$v.doctorData.clinic_id.required"
                  class="text-xs text-red-500"
                >
                  {{ formTranslation.common.clinic_is_required }}
                </span>
              </div>

              <!-- Phone Number -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.contact_no }}
                  <span class="text-red-500">*</span>
                </label>
                <vue-phone-number-input
                  v-model="doctorData.mobile_number"
                  @update="contactUpdateHandaler"
                  :default-country-code="defaultCountryCode"
                  clearable
                  no-example
                  class="phone-input-custom"
                />
                <span
                  v-if="submitted && !$v.doctorData.mobile_number.required"
                  class="text-xs text-red-500"
                >
                  {{ formTranslation.common.contact_num_required }}
                </span>
              </div>

              <!-- Date of Birth -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.dob }}
                </label>
                <input
                  type="date"
                  v-model="doctorData.dob"
                  :max="new Date().toISOString().slice(0, 10)"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                />
              </div>
            </div>
          </div>

          <!-- Specialization and Gender Section -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.patient_front_widget.specialization }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select
                v-model="doctorData.specialties"
                :options="doctorSpecialization"
                :multiple="true"
                :loading="specializationMultiselectLoader"
                label="label"
                track-by="id"
                :placeholder="formTranslation.doctor.add_sp_plh"
                @tag="addNewSpecialization"
                :taggable="true"
                class="w-full"
              />
              <span
                v-if="submitted && !$v.doctorData.specialties.required"
                class="text-xs text-red-500"
              >
                {{ formTranslation.doctor.doctor_specialization_required }}
              </span>
            </div>

            <!-- Gender -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.gender }}
                <span class="text-red-500">*</span>
              </label>
              <div class="flex gap-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="doctorData.gender"
                    value="male"
                    class="form-radio h-4 w-4 text-purple-600"
                  />
                  <span class="ml-2">{{ formTranslation.common.male }}</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="doctorData.gender"
                    value="female"
                    class="form-radio h-4 w-4 text-purple-600"
                  />
                  <span class="ml-2">{{ formTranslation.common.female }}</span>
                </label>
                <label
                  v-if="defaultUserRegistrationFormSettingData === 'on'"
                  class="inline-flex items-center"
                >
                  <input
                    type="radio"
                    v-model="doctorData.gender"
                    value="other"
                    class="form-radio h-4 w-4 text-purple-600"
                  />
                  <span class="ml-2">{{ formTranslation.common.other }}</span>
                </label>
              </div>
              <span
                v-if="submitted && !$v.doctorData.gender.required"
                class="text-xs text-red-500"
              >
                {{ formTranslation.common.gender_required }}
              </span>
            </div>

            <!-- Status -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.status }}
                <span class="text-red-500">*</span>
              </label>
              <select
                v-model="doctorData.user_status"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                :class="{
                  'border-red-500':
                    submitted && $v.doctorData.user_status.$error,
                }"
              >
                <option value="">
                  {{ formTranslation.appointments.select_status }}
                </option>
                <option value="0">{{ formTranslation.common.active }}</option>
                <option value="1">{{ formTranslation.common.inactive }}</option>
              </select>
              <span
                v-if="submitted && !$v.doctorData.user_status.required"
                class="text-xs text-red-500"
              >
                {{ formTranslation.appointments.status_required }}
              </span>
            </div>

            <!-- GMC Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                GMC No.
              </label>
              <input
                type="text"
                v-model="doctorData.gmc_no"
                placeholder="Enter GMC No."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              />
            </div>

            <!-- Experience -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.doctor.experience_year }}
              </label>
              <input
                type="number"
                v-model="doctorData.no_of_experience"
                :placeholder="formTranslation.doctor.experience_plh"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              />
            </div>
          </div>

          <!-- Profile Image and Experience -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <!-- Profile Image -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.receptionist.upload_profile }}
              </label>
              <div class="flex items-center gap-4">
                <div
                  class="relative h-32 w-32 rounded-full overflow-hidden border-2 border-gray-200"
                >
                  <img
                    :src="imagePreview"
                    class="h-full w-full object-cover"
                    alt="Profile Preview"
                  />
                  <button
                    type="button"
                    @click="uploadProfile"
                    class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white py-1 text-sm hover:bg-opacity-70"
                  >
                    {{ formTranslation.clinic.edit_profile_img }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Add this after the Profile Image section -->
            <div class="col-md-4">
              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.signature }}
                </label>
                <div
                  id="signaturePreview"
                  class="w-full h-32 bg-gray-100 rounded-lg mb-2 bg-contain bg-no-repeat"
                  :style="
                    signaturePreview
                      ? `background-image: url(${signaturePreview})`
                      : ''
                  "
                ></div>
                <div class="flex gap-2">
                  <button
                    type="button"
                    @click="uploadSignature"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                  >
                    {{ formTranslation.common.upload }}
                  </button>
                  <button
                    type="button"
                    @click="removeSignature"
                    class="px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200"
                  >
                    {{ formTranslation.common.remove }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Description Section -->
            <div class="mb-8">
              <h2 class="text-xl font-semibold text-gray-800 mb-6">
                {{ formTranslation.appointments.description }}
              </h2>
              <div class="w-full">
                <vue-editor
                  v-model="doctorData.description"
                  class="min-h-[200px]"
                ></vue-editor>
              </div>
            </div>

            <!-- Address Section -->
            <div class="mb-8">
              <h2 class="text-xl font-semibold text-gray-800 mb-6">
                {{ formTranslation.doctor.address_details }}
              </h2>

              <!-- Full Address -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.address }}
                </label>
                <textarea
                  v-model="doctorData.address"
                  :placeholder="formTranslation.doctor.address_placeholder"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 min-h-[100px]"
                ></textarea>
              </div>
              <!-- Country -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.country }}
                </label>
                <input
                  type="text"
                  v-model="doctorData.country"
                  :placeholder="formTranslation.doctor.country_placeholder"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                />
              </div>

              <!-- City -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.city }}
                </label>
                <input
                  type="text"
                  v-model="doctorData.city"
                  :placeholder="formTranslation.doctor.city_placeholder"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                />
              </div>

              <!-- Postal Code -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.common.postal_code }}
                </label>
                <input
                  type="text"
                  v-model="doctorData.postal_code"
                  :placeholder="formTranslation.doctor.pcode_placeholder"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                />
              </div>
            </div>
          </div>

          <!-- Custom Fields Section -->
          <div v-if="isCustomeFieldExist" class="mb-8">
            <hr class="my-6" />
            <h2 class="text-xl font-semibold text-gray-800 mb-6">
              {{ formTranslation.doctor.other_detail }}
            </h2>

            <!-- Custom Fields Component -->
            <div>
              <get-custom-fields
                v-if="!doctorId"
                module_type="doctor_module"
                :module_id="String(0)"
                @bindCustomField="getCustomFieldsValues"
                :fieldsValue="customFieldsData"
                @customFieldAvailable="isCustomeFieldExist = true"
                @requiredCustomField="getRequireFields"
              ></get-custom-fields>

              <edit-custom-fields
                v-else
                module_type="doctor_module"
                :module_id="String(doctorId)"
                @bindCustomField="getCustomFieldsValues"
                :fieldsValue="customFieldsData"
                @requiredCustomField="getRequireFields"
              ></edit-custom-fields>
            </div>
          </div>

          <!-- Submit Buttons -->
          <div class="flex justify-end gap-3 mt-8">
            <button
              type="button"
              @click="handleClose"
              class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              {{ formTranslation.common.cancel }}
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
              :disabled="loading"
            >
              <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
              {{
                loading
                  ? formTranslation.common.saving
                  : formTranslation.common.save
              }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import {
  required,
  email,
  minLength,
  maxLength,
} from "vuelidate/lib/validators";
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { post, get } from "../../config/request";
import { VueEditor } from "vue2-editor";

export default {
  name: "DoctorFormModal",

  components: {
    VuePhoneNumberInput,
    VueEditor,
  },

  props: {
    openModal: {
      type: Boolean,
      required: true,
    },
    doctorId: {
      type: [Number, String],
      default: null,
    },
  },

  data: () => ({
    formLoader: false,
    loading: false,
    submitted: false,
    imagePreview: `${pluginBASEURL}assets/images/kc-demo-img.png`,
    signaturePreview: "",
    doctorData: {
      first_name: "",
      last_name: "",
      user_email: "",
      mobile_number: "",
      country_code: "",
      country_calling_code: "",
      gender: "",
      dob: "",
      clinic_id: [],
      specialties: [],
      profile_image: "",
      no_of_experience: 0,
      user_status: "0",
      gmc_no: "",
      signature: "",
      description: "",
    },
    clinics: [],
    clinicMultiselectLoader: false,
    defaultCountryCode: null,
    defaultUserRegistrationFormSettingData: "on",
  }),

  validations: {
    doctorData: {
      first_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      last_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      user_email: {
        required,
        email,
      },
      mobile_number: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      gender: { required },
      // clinic_id: {
      //   required: function () {
      //     return (
      //       this.userData.addOns.kiviPro &&
      //       this.getUserRole() === "administrator"
      //     );
      //   },
      // },
      // clinic_id: {
      //   required(value) {
      //     // Only require clinic_id validation for administrators
      //     if (this.getUserRole() === 'administrator') {
      //       return this.userData.addOns.kiviPro;
      //     }
      //     return false; // Don't require validation for non-admin roles
      //   }
      // },
      clinic_id: {
        required: requiredIf(function () {
          return (
            this.userData.addOns.kiviPro == true &&
            this.getUserRole() == "administrator"
          );
        }),
      },
      user_status: { required },
      specialties: { required },
    },
  },

  async mounted() {
    await this.init();
  },

  watch: {
    openModal: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.init();
        } else {
          this.resetForm();
        }
      },
    },
  },

  methods: {
    async init() {
      this.getCountryCodeData();
      this.getUserRegistrationFormData();

      if (this.userData.addOns.kiviPro) {
        await this.getClinics();
      }

      if (this.doctorId) {
        await this.fetchDoctorData();
      }
    },

    uploadSignature() {
      const custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", () => {
        const attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        this.signaturePreview = attachment.url;
        this.doctorData.signature = attachment.id;
      });

      custom_uploader.open();
    },

    removeSignature() {
      this.signaturePreview = "";
      this.doctorData.signature = "";
    },

    async fetchDoctorData() {
      this.formLoader = true;
      try {
        const response = await get("doctor_edit", { id: this.doctorId });
        if (response.data.status) {
          this.doctorData = response.data.data;
          if (this.doctorData.user_profile) {
            this.imagePreview = this.doctorData.user_profile;
          }
        }
      } catch (error) {
        console.error("Error fetching doctor data:", error);
        displayErrorMessage(this.formTranslation.widgets.record_not_found);
      } finally {
        this.formLoader = false;
      }
    },

    async handleSubmit() {
      this.submitted = true;
      this.loading = true;

      // Handle clinic_id assignment before validation
      if (this.getUserRole() !== "administrator" && this.defaultClinicData) {
        this.doctorData.clinic_id = [
          {
            id: this.defaultClinicData.id,
            label: this.defaultClinicData.label,
          },
        ];
      }

      console.log("this.doctorData.clinic_id", this.doctorData.clinic_id);
      console.log("this.doctorData", this.doctorData);

      this.$v.$touch();
      if (this.$v.$invalid) {
        this.loading = false;
        this.$nextTick(() => {
          const firstError = document.querySelector(
            ".is-invalid, .invalid-feedback"
          );
          if (firstError) {
            firstError.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        });
        return;
      }

      try {
        const response = await post("doctor_save", {
          ...this.doctorData,
          id: this.doctorId,
        });

        if (response.data.status) {
          displayMessage(response.data.message);
          if (this.getUserRole() === "administrator") {
            await this.$store.dispatch("userDataModule/fetchUserData", {});
          }
          this.$emit("saved");
          this.handleClose();
        } else {
          displayErrorMessage(response.data.message);
        }
      } catch (error) {
        console.error("Error saving doctor:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.loading = false;
      }
    },

    contactUpdateHandaler(val) {
      this.doctorData.country_code = val.countryCode;
      this.doctorData.country_calling_code = val.countryCallingCode;
    },

    async getClinics() {
      this.clinicMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_list",
        });

        if (response.data.status) {
          this.clinics = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinics:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    uploadProfile() {
      const custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", () => {
        const attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        this.imagePreview = attachment.url;
        this.doctorData.profile_image = attachment.id;
      });

      custom_uploader.open();
    },

    async getCountryCodeData() {
      try {
        const response = await get("get_country_code_settings_data", {});
        if (response.data.status) {
          this.defaultCountryCode = response.data.data.country_code;
        }
      } catch (error) {
        console.error("Error fetching country code:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    async getUserRegistrationFormData() {
      try {
        const response = await get(
          "get_user_registration_form_settings_data",
          {}
        );
        if (response.data.status) {
          this.defaultUserRegistrationFormSettingData =
            response.data.data.userRegistrationFormSettingData;
        }
      } catch (error) {
        console.error("Error fetching registration form data:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    addNewSpecialization(value) {
      const specialitiesObj = {
        label: value,
        type: "specialization",
        value: value.replace(" ", "_"),
        status: 1,
      };

      post("static_data_save", specialitiesObj)
        .then((response) => {
          if (response.data.status) {
            this.doctorData.specialties.push({
              id: response.data.insert_id,
              label: value,
            });
            this.$store.commit("staticDataModule/ADD_OPTION_STATIC_DATA", {
              dataType: "specialization",
              option: {
                id: response.data.insert_id,
                label: value,
              },
            });
          }
        })
        .catch((error) => {
          console.error("Error adding specialization:", error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleClose() {
      this.$emit("close");
    },
  },

  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    teleMedEn() {
      return this.userData.addOns.telemed;
    },
    doctorSpecialization() {
      return (
        this.$store.state.staticDataModule.static_data.specialization || []
      );
    },
    specializationMultiselectLoader() {
      return this.$store.state.staticDataModule.static_data_loader;
    },
    defaultClinicData() {
      return this.$store.state.userDataModule.clinic;
    },
  },
};
</script>

<style scoped>
.phone-input-custom {
  --text-color: rgb(55, 65, 81);
  --border-color: rgb(209, 213, 219);
  --border-radius: 0.5rem;
  --height: 42px;
}

.phone-input-custom:focus-within {
  --border-color: rgb(192, 132, 252);
}

input[type="date"] {
  background: #fff
    url(https://cdn1.iconfinder.com/data/icons/cc_mono_icon_set/blacks/16x16/calendar_2.png)
    97% 50% no-repeat;
}

input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}

.form-radio:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}
</style>
