<template>
  <div>
    <div class="page-loader-section" v-if="isAppointmentLoading">
      <loader-component-2></loader-component-2>
    </div>
    <div v-else>
      <div v-if="appointmentList?.length > 0" class="space-y-4">
        <div
          v-for="appointment in appointmentList"
          :key="appointment.id"
          class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
        >
          <div>
            <p class="font-semibold">
              {{ appointment?.patient_name || "N/A" }}
            </p>
            <p class="text-sm text-gray-600">
              {{ appointment?.doctor_name || "N/A" }} -
              {{ appointment?.appointment_start_time || "N/A" }}
            </p>
          </div>
          <div class="flex space-x-2">
            <button
              v-if="appointment.status == 1"
              :id="`appointment_edit_${appointment.id}`"
              class="px-4 py-2 text-sm text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100 disabled:opacity-50"
              @click="handleAppointmentReschedule(appointment)"
              :disabled="isLoading"
            >
              <i class="fa fa-calendar"></i>
            </button>
            <button
              v-if="kcCheckPermission('appointment_edit')"
              :id="`appointment_edit_${appointment.id}`"
              class="px-4 py-2 text-sm text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100 disabled:opacity-50"
              @click="handleAppointmentEdit(appointment)"
              :disabled="isLoading"
            >
              <i class="fa fa-calendar"></i>
            </button>
            <button
              v-if="kcCheckPermission('patient_appointment_status_change')"
              :id="`status_update_${appointment.id}`"
              class="px-4 py-2 text-sm text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100 disabled:opacity-50"
              @click="handleAppointmentStatus(appointment, '4')"
              :disabled="isLoading"
            >
              {{ formTranslation?.appointments?.check_in || "Check In" }}
            </button>
            <button
              v-if="kcCheckPermission('appointment_delete')"
              :id="`appointment_delete_${appointment.id}`"
              class="px-4 py-2 text-sm text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100 disabled:opacity-50"
              @click="handleAppointmentDelete(appointment)"
              :disabled="isLoading"
            >
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="flex items-center justify-center h-64">
          <p class="text-gray-600">
            {{
              formTranslation?.common?.no_appointments ||
              "No appointments available"
            }}
          </p>
        </div>
      </div>

      <RescheduleModal
        :showModal="showRescheduleModal"
        :appointment="selectedAppointment"
        @update:showModal="showRescheduleModal = $event"
        @rescheduled="handleAppointmentRescheduled"
        @close="handleModalClose"
      />
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import RescheduleModal from "./RescheduleModal.vue";
import moment from "moment";

export default {
  components: {
    RescheduleModal,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  name: "PatientDashboardAppointmentList",
  data() {
    return {
      showRescheduleModal: false,
      selectedAppointment: null,
      appointmentRequest: {},
      appointmentData: [],
      isAppointmentLoading: true,
      editAppointment: false,
      editId: null,
      appointmentFormObj: {},
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.todayAppointmentList();
      this.appointmentRequest = this.defaultAppointmentRequest();
    },
    defaultAppointmentRequest() {
      return {
        date: new Date(),
      };
    },
    async todayAppointmentList() {
      try {
        await this.$store.dispatch("userDataModule/fetchUserForDropdown", {
          userRoleName: this.patientRoleName,
        });

        const filterData = {
          date: moment(this.appointmentRequest.date).format("YYYY-MM-DD"),
        };

        await this.$store.dispatch("appointmentModule/fetchAppointmentData", {
          filterData,
        });

        this.appointmentData =
          this.$store.state.appointmentModule.appointmentList;
      } catch (error) {
        console.error("Error fetching appointment list:", error);
        this.$toast?.error("Error loading appointments");
      } finally {
        setTimeout(() => {
          this.isAppointmentLoading = false;
        }, 500);
      }
    },
    handleAppointmentReschedule(appointment) {
      if (!appointment?.id) {
        console.error("Invalid appointment data");
        return;
      }
      this.selectedAppointment = appointment;
      this.showRescheduleModal = true;
    },
    handleModalClose() {
      this.selectedAppointment = null;
    },
    handleAppointmentRescheduled(response) {
      this.showRescheduleModal = false;
      this.selectedAppointment = null;
      this.reloadAppointment();
      this.$emit("refreshDashboard");
    },
    handleAppointmentEdit: function (appointment, collapseID) {
      this.editAppointment = true;
      this.$root.$emit("bv::toggle::collapse", collapseID);
      this.$store.commit("TOGGLE_APPOINTMENT_FORM", false);
      this.editId = appointment.id;
      setTimeout(() => {
        this.appointmentFormObj = Object.assign({}, appointment);
      }, 200);
    },
    handleAppointmentDelete: function (appointment) {
      if (appointment.id !== undefined) {
        let but = $("#appointment_delete_" + appointment.id);
        but.prop("disabled", true);
        but.html(`<i class='fa fa-sync fa-spin'> </i>`);

        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.press_yes_delete_billitems,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
            focusConfirm: false,
          })
          .then((result) => {
            if (result.isConfirmed) {
              get("appointment_delete", {
                id: appointment.id,
              })
                .then((data) => {
                  but.prop("disabled", false);
                  but.html(`<i class='fa fa-trash'> </i>`);
                  if (
                    data.data.status !== undefined &&
                    data.data.status === true
                  ) {
                    displayMessage(data.data.message);
                    this.reloadAppointment();
                    this.$emit("refreshDashboard");
                  }
                })
                .catch((error) => {
                  but.prop("disabled", false);
                  but.html(`<i class='fa fa-trash'> </i>`);
                  console.log(error);
                  displayErrorMessage(
                    this.formTranslation.common.internal_server_error
                  );
                });
            } else {
              but.prop("disabled", false);
              but.html(`<i class='fa fa-trash'> </i>`);
            }
          });
      }
    },
    handleAppointmentStatus: function (appointment, status) {
      if (status === "3") {
        if (
          appointment.encounter_id !== null &&
          appointment.encounter_detail !== undefined &&
          [1, "1"].includes(appointment.encounter_detail.status)
        ) {
          displayErrorMessage(this.formTranslation.common.encounter_not_close);
          return;
        }
      }
      var element = $("#status_update_" + appointment.id).find("i");
      $("#status_update_" + appointment.id).prop("disabled", true);
      if (status === "4") {
        element.removeClass("fa fa-sign-in-alt");
      } else {
        element.removeClass("fa fa-sign-out-alt");
      }
      element.addClass("fa fa-spinner fa-spin");

      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: this.formTranslation.common.update_appointment_status,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#0d6efd", // Bootstrap primary color
          cancelButtonColor: "#6c757d", // Bootstrap secondary color
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
          focusConfirm: false,
        })
        .then((result) => {
          if (result.isConfirmed) {
            get("appointment_update_status", {
              appointment_id: appointment.id,
              appointment_status: status,
            })
              .then((response) => {
                $("#status_update_" + appointment.id).prop("disabled", false);
                element.removeClass("fa fa-spinner fa-spin");
                if (status === "4") {
                  element.addClass("fa fa-sign-in-alt");
                } else {
                  element.addClass("fa fa-sign-out-alt");
                }
                if (
                  response.data.status !== undefined &&
                  response.data.status === true
                ) {
                  this.reloadAppointment();
                  displayMessage(response.data.message);
                } else {
                  displayErrorMessage(response.data.message);
                }
              })
              .catch((error) => {
                $("#status_update_" + appointment.id).prop("disabled", false);
                element.removeClass("fa fa-spinner fa-spin");
                if (status === "4") {
                  element.addClass("fa fa-sign-in-alt");
                } else {
                  element.addClass("fa fa-sign-out-alt");
                }
                console.log(error);
                displayErrorMessage(
                  this.formTranslation.common.internal_server_error
                );
              });
          } else {
            $("#status_update_" + appointment.id).prop("disabled", false);
            element.removeClass("fa fa-spinner fa-spin");
            if (status === "4") {
              element.addClass("fa fa-sign-in-alt");
            } else {
              element.addClass("fa fa-sign-out-alt");
            }
          }
        });
    },
    reloadAppointment: function () {
      // let filterData = Object.assign({}, this.appointmentRequest);
      // filterData.date = moment(this.appointmentRequest.date).format('YYYY-MM-DD');
      setTimeout(() => {
        this.$store.dispatch("appointmentModule/fetchAppointmentData", {
          filterData: { date: moment(new Date()).format("YYYY-MM-DD") },
        });
      }, 300);
    },
    refreshAppointment: function () {
      this.isAppointmentLoading = true;
      // let filterData = Object.assign({},  { date: moment(new Date()).format('YYYY-MM-DD') });
      // filterData.date = moment(new Date()).format('YYYY-MM-DD');
      // console.log(filterData);
      get("get_appointment_queue", {
        filterData: { date: moment(new Date()).format("YYYY-MM-DD") },
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit(
              "appointmentModule/FETCH_APPOINTMENT_DATA",
              response.data.data
            );
            this.$emit("isReloadTrue", false);
          }
        })
        .catch((error) => {
          this.isAppointmentLoading = false;
          console.log(error);
        });
    },
  },
  computed: {
    appointmentList() {
      return this.$store.state.appointmentModule.appointmentList || [];
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    enableDisableAppointmentDescriptionStatus() {
      return this.$store.state.appointmentModule.description_status;
    },
  },
  watch: {
    isLoading: {
      immediate: true,
      handler(value) {
        if (value) {
          this.isAppointmentLoading = true;
          this.refreshAppointment();
        } else {
          this.isAppointmentLoading = false;
        }
      },
    },
  },
};
</script>
