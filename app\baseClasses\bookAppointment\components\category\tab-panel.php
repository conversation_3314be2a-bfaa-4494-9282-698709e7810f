<div class="p-6">
    <!-- Header with title and search -->
    <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
        <h3 class="text-xl font-semibold text-gray-900">Select Category</h3>
        <div class="relative w-full">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input type="text" id="serviceCategorySearch" placeholder="Search categories..."
             style="padding-left: 2.5rem;"
                class="pl-10 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:outline-none text-sm w-full" />
        </div>
    </div>

    <!-- Category list -->
    <div id="serviceCategoryLists" class="space-y-4">
        <span class="loader-class hidden">
            <!-- Loader placeholder -->
            <div class="flex justify-center items-center py-6">
                <div class="double-lines-spinner"></div>
            </div>
        </span>

        <!-- Category cards will be loaded dynamically -->
    </div>
</div>

<script>
    // Search functionality for categories
    document.getElementById('serviceCategorySearch').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const categoryItems = document.querySelectorAll('.category-item');
        
        categoryItems.forEach(item => {
            const categoryName = item.querySelector('label span').textContent.toLowerCase();
            if (categoryName.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // When a category is selected, highlight it visually but don't proceed automatically
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('service-category')) {
            // First, reset all categories
            document.querySelectorAll('.category-item label').forEach(label => {
                label.classList.remove('border-purple-500', 'bg-purple-50');
                label.classList.add('border-gray-200', 'hover:border-purple-200');
            });
            
            // Then highlight the selected one
            const selectedLabel = document.querySelector(`label[for="${e.target.id}"]`);
            if (selectedLabel) {
                selectedLabel.classList.remove('border-gray-200', 'hover:border-purple-200');
                selectedLabel.classList.add('border-purple-500', 'bg-purple-50');
            }
            
            // Load services for this category
            const categoryValue = e.target.value;
            const categoryCount = e.target.getAttribute('data-count');
            
            // Show loading state
            const servicesTab = document.getElementById('services');
            if (servicesTab) {
                const servicesList = servicesTab.querySelector('#serviceLists');
                if (servicesList) {
                    servicesList.innerHTML = '<div class="flex justify-center items-center py-6"><div class="double-lines-spinner"></div></div>';
                }
            }
            
            // Store selected category in a hidden field for the next step
            const hiddenCategoryField = document.getElementById('selected_category');
            if (!hiddenCategoryField) {
                const hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.id = 'selected_category';
                hiddenField.name = 'selected_category';
                hiddenField.value = categoryValue;
                document.getElementById('category').appendChild(hiddenField);
            } else {
                hiddenCategoryField.value = categoryValue;
            }
            
            // Only proceed to next tab if clicked the Next button, not automatically
        }
    });
</script>