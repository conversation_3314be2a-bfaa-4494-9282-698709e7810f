<template>
  <div v-if="openModal" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div
      class="fixed inset-0 bg-black opacity-50 backdrop-blur-sm transition-opacity"
      @click="handleClose"
    ></div>

    <!-- Modal -->
    <div class="flex min-h-screen items-start justify-center p-4">
      <div
        class="relative w-full max-w-7xl transform bg-white rounded-xl shadow-2xl transition-all"
      >
        <!-- Modal Header -->
        <div
          class="flex items-center justify-between border-b border-gray-200 px-6 py-4"
        >
          <h1 class="text-xl font-semibold text-gray-900">
            {{
              doctorId
                ? formTranslation.doctor.edit_doctor
                : formTranslation.doctor.add_doctor
            }}
          </h1>
          <button
            @click="handleClose"
            class="rounded-lg p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
          >
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <!-- Form Content -->
        <form
          @submit.prevent="handleSubmit"
          id="doctorDataForm"
          class="px-6 py-4"
          novalidate
        >
          <!-- Loading Overlay -->
          <div
            v-if="formLoader"
            class="absolute inset-0 flex items-center justify-center bg-white/80"
          >
            <div class="flex flex-col items-center gap-3">
              <div
                class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"
              ></div>
              <p class="text-sm text-gray-500">Loading...</p>
            </div>
          </div>

          <div class="space-y-8">
            <!-- Basic Details Section -->
            <section>
              <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900">
                  {{ formTranslation.common.basic_details }}
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                  Please fill in the basic information about the doctor.
                </p>
              </div>

              <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                <!-- First Name -->
                <div class="form-group">
                  <label class="form-label required">
                    {{ formTranslation.common.fname }}
                  </label>
                  <input
                    type="text"
                    v-model="doctorData.first_name"
                    :placeholder="formTranslation.doctor.doctor_name"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    :class="{ error: getFieldError('first_name') }"
                  />
                  <span
                    v-if="getFieldError('first_name')"
                    class="error-message"
                  >
                    {{ getFieldError("first_name") }}
                  </span>
                </div>

                <!-- Last Name -->
                <div class="form-group">
                  <label class="form-label required">
                    {{ formTranslation.common.lname }}
                  </label>
                  <input
                    type="text"
                    v-model="doctorData.last_name"
                    :placeholder="formTranslation.doctor.lname_placeholder"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    :class="{ error: getFieldError('last_name') }"
                  />
                  <span v-if="getFieldError('last_name')" class="error-message">
                    {{ getFieldError("last_name") }}
                  </span>
                </div>

                <!-- Email -->
                <div class="form-group">
                  <label class="form-label required">
                    {{ formTranslation.common.email }}
                  </label>
                  <input
                    type="email"
                    v-model="doctorData.user_email"
                    :placeholder="formTranslation.doctor.email_placeholder"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    :class="{ error: getFieldError('user_email') }"
                  />
                  <span
                    v-if="getFieldError('user_email')"
                    class="error-message"
                  >
                    {{ getFieldError("user_email") }}
                  </span>
                </div>

                <!-- Phone Number -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <vue-phone-number-input
                    v-model="doctorData.mobile_number"
                    @update="contactUpdateHandaler"
                    :default-country-code="defaultCountryCode"
                    clearable
                    no-example
                    class="phone-input-custom"
                  />
                  <span
                    v-if="submitted && !$v.doctorData.mobile_number.required"
                    class="text-xs text-red-500"
                  >
                    {{ formTranslation.common.contact_num_required }}
                  </span>
                </div>

                <!-- Date of Birth -->
                <!-- <div class="form-group">
                  <label class="form-label">
                    {{ formTranslation.common.dob }}
                  </label>
                  <input
                    type="date"
                    v-model="doctorData.dob"
                    :max="new Date().toISOString().slice(0, 10)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  />
                </div> -->

                <!-- Clinic Selection (for admin) -->
                <div
                  v-if="
                    userData.addOns.kiviPro && getUserRole() === 'administrator'
                  "
                >
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.clinic.select_clinic }}
                    <span class="text-red-500">*</span>
                  </label>
                  <multi-select
                    v-model="doctorData.clinic_id"
                    :options="clinics"
                    :multiple="true"
                    :loading="clinicMultiselectLoader"
                    label="label"
                    track-by="id"
                    :placeholder="formTranslation.doctor.search_placeholder"
                    class="w-full"
                  />
                  <span
                    v-if="submitted && !$v.doctorData.clinic_id.required"
                    class="text-xs text-red-500"
                  >
                    {{ formTranslation.common.clinic_is_required }}
                  </span>
                </div>

                <!-- Status -->
                <div class="form-group">
                  <label class="form-label required">
                    {{ formTranslation.common.status }}
                  </label>
                  <select
                    v-model="doctorData.user_status"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    :class="{ error: getFieldError('user_status') }"
                  >
                    <option value="">
                      {{ formTranslation.appointments.select_status }}
                    </option>
                    <option value="0">
                      {{ formTranslation.common.active }}
                    </option>
                    <option value="1">
                      {{ formTranslation.common.inactive }}
                    </option>
                  </select>
                  <span
                    v-if="getFieldError('user_status')"
                    class="error-message"
                  >
                    {{ getFieldError("user_status") }}
                  </span>
                </div>
              </div>
            </section>

            <!-- Professional Details -->
            <section>
              <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900">
                  Professional Information
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                  Add the doctor's professional details and qualifications.
                </p>
              </div>

              <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Specialization -->
                <div class="form-group">
                  <label class="form-label required">
                    {{ formTranslation.patient_front_widget.specialization }}
                  </label>
                  <multi-select
                    v-model="doctorData.specialties"
                    :options="doctorSpecialization"
                    :multiple="true"
                    :loading="specializationMultiselectLoader"
                    label="label"
                    track-by="id"
                    :placeholder="formTranslation.doctor.add_sp_plh"
                    @tag="addNewSpecialization"
                    :taggable="true"
                    class="multiselect-custom"
                  />
                  <span
                    v-if="getFieldError('specialties')"
                    class="error-message"
                  >
                    {{ getFieldError("specialties") }}
                  </span>
                </div>

                <!-- Experience -->
                <div class="form-group">
                  <label class="form-label">
                    {{ formTranslation.doctor.experience_year }}
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      v-model="doctorData.no_of_experience"
                      :placeholder="formTranslation.doctor.experience_plh"
                      min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                    <span class="absolute right-3 top-2 text-sm text-gray-500"
                      >years</span
                    >
                  </div>
                </div>

                <!-- GMC Number -->
                <div class="form-group">
                  <label class="form-label"> Registration No. </label>
                  <input
                    type="text"
                    v-model="doctorData.gmc_no"
                    placeholder="Enter Registration No."
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  />
                </div>
                
                <div class="form-group">
                  <label class="form-label"> Registration Prefix </label>
                  <input
                    type="text"
                    v-model="doctorData.registration_prefix"
                    placeholder="Enter Registration Prefix (e.g., NMC, GMC)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 uppercase"
                  />
                  <div class="mt-1 text-xs text-gray-500">
                    This prefix will be displayed before "No." (Will appear in uppercase)
                  </div>
                </div>

                <!-- Gender -->
                <div class="form-group">
                  <label class="form-label required">
                    {{ formTranslation.common.gender }}
                  </label>
                  <div class="flex gap-6">
                    <label class="flex items-center gap-2">
                      <input
                        type="radio"
                        v-model="doctorData.gender"
                        value="male"
                        class="h-4 w-4 text-primary"
                      />
                      <span>{{ formTranslation.common.male }}</span>
                    </label>
                    <label class="flex items-center gap-2">
                      <input
                        type="radio"
                        v-model="doctorData.gender"
                        value="female"
                        class="h-4 w-4 text-primary"
                      />
                      <span>{{ formTranslation.common.female }}</span>
                    </label>
                    <label
                      v-if="defaultUserRegistrationFormSettingData === 'on'"
                      class="flex items-center gap-2"
                    >
                      <input
                        type="radio"
                        v-model="doctorData.gender"
                        value="other"
                        class="h-4 w-4 text-primary"
                      />
                      <span>{{ formTranslation.common.other }}</span>
                    </label>
                  </div>
                  <span v-if="getFieldError('gender')" class="error-message">
                    {{ getFieldError("gender") }}
                  </span>
                </div>
              </div>
            </section>

            <!-- Profile & Signature -->
            <section>
              <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900">
                  Profile & Signature
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                  Upload profile picture and signature for the doctor.
                </p>
              </div>

              <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
                <!-- Profile Image -->
                <div>
                  <label class="form-label">
                    {{ formTranslation.receptionist.upload_profile }}
                  </label>
                  <div class="mt-2 flex items-center gap-4">
                    <div
                      class="relative h-32 w-32 overflow-hidden rounded-full border-2 border-gray-200"
                    >
                      <img
                        :src="imagePreview"
                        class="h-full w-full object-cover"
                        alt="Profile Preview"
                      />
                      <button
                        type="button"
                        @click="uploadProfile"
                        class="absolute bottom-0 left-0 right-0 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
                      >
                        {{ formTranslation.clinic.edit_profile_img }}
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Signature -->
                <div>
                  <label class="form-label">
                    {{ formTranslation.common.signature }}
                  </label>
                  <div class="mt-2">
                    <div
                      class="mb-2 h-32 w-full rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 bg-contain bg-center bg-no-repeat p-2"
                      :style="
                        signaturePreview
                          ? `background-image: url(${signaturePreview})`
                          : ''
                      "
                    >
                      <div
                        v-if="!signaturePreview"
                        class="flex h-full items-center justify-center"
                      >
                        <span class="text-sm text-gray-500"
                          >No signature uploaded</span
                        >
                      </div>
                    </div>
                    <div class="flex gap-2">
                      <button
                        type="button"
                        @click="uploadSignature"
                        class="inline-flex items-center justify-center rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        {{ formTranslation.common.upload }}
                      </button>
                      <button
                        type="button"
                        @click="removeSignature"
                        class="inline-flex items-center justify-center rounded-lg bg-red-100 px-4 py-2 text-sm font-medium text-red-700 transition hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        :disabled="!signaturePreview"
                      >
                        {{ formTranslation.common.remove }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Description -->
            <section>
              <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900">
                  {{ formTranslation.appointments.description }}
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                  Add a detailed description about the doctor.
                </p>
              </div>

              <vue-editor
                v-model="doctorData.description"
                class="min-h-[100px] prose max-w-none"
              ></vue-editor>
            </section>
          </div>

          <!-- Form Actions -->
          <div
            class="mt-8 flex items-center justify-end gap-3 border-t border-gray-200 pt-6"
          >
            <button
              type="button"
              @click="handleClose"
              class="inline-flex items-center justify-center rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              :disabled="loading"
            >
              {{ formTranslation.common.cancel }}
            </button>
            <button
              type="submit"
              class="inline-flex items-center justify-center rounded-lg bg-black px-4 py-2 text-sm font-medium text-white shadow-sm transition hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              :disabled="loading"
            >
              <svg
                v-if="loading"
                class="mr-2 h-4 w-4 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              {{
                loading
                  ? formTranslation.common.saving
                  : formTranslation.common.save
              }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import {
  required,
  email,
  minLength,
  maxLength,
} from "vuelidate/lib/validators";
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { VueEditor } from "vue2-editor";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import { post, get } from "../../config/request";

export default defineComponent({
  name: "DoctorFormModal",

  components: {
    VuePhoneNumberInput,
    VueEditor,
  },

  props: {
    openModal: {
      type: Boolean,
      required: true,
    },
    doctorId: {
      type: [Number, String],
      default: null,
    },
  },

  data() {
    return {
      formLoader: false,
      loading: false,
      submitted: false,
      imagePreview: `${pluginBASEURL}assets/images/kc-demo-img.png`,
      signaturePreview: "",
      doctorData: {
        first_name: "",
        last_name: "",
        user_email: "",
        mobile_number: "",
        country_code: "",
        country_calling_code: "",
        gender: "",
        clinic_id: [],
        specialties: [],
        profile_image: "",
        no_of_experience: 0,
        user_status: "0",
        gmc_no: "",
        registration_prefix: "",
        signature: "",
        description: "",
      },
      clinics: [],
      clinicMultiselectLoader: false,
      defaultCountryCode: null,
      defaultUserRegistrationFormSettingData: "on",
    };
  },

  validations: {
    doctorData: {
      first_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      last_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      user_email: {
        required,
        email,
      },
      mobile_number: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      gender: { required },
      clinic_id: {
        required(value) {
          return this.showClinicSelection;
        },
      },
      user_status: { required },
      specialties: { required },
    },
  },

  computed: {
    showClinicSelection() {
      return (
        this.userData?.addOns?.kiviPro && this.getUserRole() === "administrator"
      );
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    doctorSpecialization() {
      return (
        this.$store.state.staticDataModule.static_data.specialization || []
      );
    },
    specializationMultiselectLoader() {
      return this.$store.state.staticDataModule.static_data_loader;
    },
    defaultClinicData() {
      return this.$store.state.userDataModule.clinic;
    },
  },

  watch: {
    openModal: {
      immediate: true,
      async handler(newVal) {
        if (newVal) {
          await this.init();
        } else {
          this.resetForm();
        }
      },
    },
    doctorData: {
      deep: true,
      handler() {
        if (this.submitted) {
          this.$v.$touch();
        }
      },
    },
  },

  mounted() {
    // Initialize Vuelidate
    this.$v.$touch();
    this.$v.$reset();
  },
  methods: {
    async init() {
      try {
        this.formLoader = true;
        await Promise.all([
          this.getCountryCodeData(),
          this.getUserRegistrationFormData(),
          this.userData?.addOns?.kiviPro && this.getClinics(),
          this.doctorId && this.fetchDoctorData(),
        ]);
      } catch (error) {
        console.error("Error initializing form:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.formLoader = false;
      }
    },

    contactUpdateHandaler(val) {
      this.doctorData.country_code = val.countryCode;
      this.doctorData.country_calling_code = val.countryCallingCode;
    },

    async fetchDoctorData() {
      try {
        const response = await get("doctor_edit", { id: this.doctorId });

        if (response.data.status) {
          const doctorData = response.data.data;

          // Map the API response to our form data structure
          this.doctorData = {
            first_name: doctorData.first_name,
            last_name: doctorData.last_name,
            user_email: doctorData.user_email,
            mobile_number: doctorData.mobile_number,
            country_code: doctorData.country_code,
            country_calling_code: doctorData.country_calling_code,
            gender: doctorData.gender.toLowerCase(),
            clinic_id: doctorData.clinic_id
              ? doctorData.clinic_id.map((clinic) => ({
                  id: clinic.id,
                  label: clinic.label,
                }))
              : [],
            specialties: doctorData.specialties
              ? doctorData.specialties.map((spec) => ({
                  id: spec.id,
                  label: spec.label,
                }))
              : [],
            profile_image: doctorData.profile_image,
            no_of_experience: doctorData.no_of_experience || 0,
            user_status: doctorData.user_status,
            gmc_no: doctorData.gmc_no || "",
            registration_prefix: doctorData.registration_prefix || "",
            signature: doctorData.signature || "",
            description: doctorData.description || "",
          };

          // Update image previews
          if (doctorData.profile_image_url) {
            this.imagePreview = doctorData.profile_image_url;
          }
          if (doctorData.signature_url) {
            this.signaturePreview = doctorData.signature_url;
          }
        } else {
          displayErrorMessage(
            response.data.message ||
              this.formTranslation.common.internal_server_error
          );
        }
      } catch (error) {
        console.error("Error fetching doctor data:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    async handleSubmit() {
      try {
        this.submitted = true;
        this.loading = true;
        // this.$v.$touch();

        // if (this.$v.$invalid) {
        //   this.scrollToError();
        //   return;
        // }

        // Handle clinic_id assignment
        if (this.getUserRole() !== "administrator" && this.defaultClinicData) {
          this.doctorData.clinic_id = [
            {
              id: this.defaultClinicData.id,
              label: this.defaultClinicData.label,
            },
          ];
        }

        let formData = {
          ...this.doctorData,
          ID: this.doctorId, // This will be null for create operations
        };

        console.log("doctorData", formData);

        // Use the correct API endpoint
        const response = await post("doctor_save", formData);

        if (response.data.status) {
          displayMessage(response.data.message);

          if (this.getUserRole() === "administrator") {
            await this.$store.dispatch("userDataModule/fetchUserData");
          }

          this.$emit("saved");
          this.handleClose();
        } else {
          displayErrorMessage(
            response.data.message ||
              this.formTranslation.common.internal_server_error
          );
        }
      } catch (error) {
        console.error("Error submitting form:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.loading = false;
      }
    },

    async saveDoctorData(formData) {
      const endpoint = "doctor_save";
      return await this.$axios.post(endpoint, formData);
    },

    getFieldError(fieldName) {
      if (!this.submitted) return "";
      const field = this.$v.doctorData[fieldName];
      if (!field?.$error) return "";

      if (!field.required) {
        return this.formTranslation.common[`${fieldName}_required`];
      }
      if (!field.email) {
        return this.formTranslation.common.invalid_email;
      }
      if (!field.minLength) {
        return this.formTranslation.common.min_length_error;
      }
      if (!field.maxLength) {
        return this.formTranslation.common.max_length_error;
      }

      return "";
    },

    async getClinics() {
      this.clinicMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_list",
        });

        if (response.data.status) {
          this.clinics = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinics:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    handlePhoneUpdate(phoneData) {
      this.doctorData.country_code = phoneData.countryCode;
      this.doctorData.country_calling_code = phoneData.countryCallingCode;
      this.doctorData.mobile_number = phoneData.phoneNumber;
    },

    scrollToError() {
      this.$nextTick(() => {
        const firstError = document.querySelector(".error-message");
        if (firstError) {
          firstError.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      });
    },

    resetForm() {
      this.doctorData = {
        first_name: "",
        last_name: "",
        user_email: "",
        mobile_number: "",
        country_code: "",
        country_calling_code: "",
        gender: "",
        clinic_id: [],
        specialties: [],
        profile_image: "",
        no_of_experience: 0,
        user_status: "0",
        gmc_no: "",
        registration_prefix: "",
        signature: "",
        description: "",
      };
      this.submitted = false;
      this.imagePreview = `${pluginBASEURL}assets/images/kc-demo-img.png`;
      this.signaturePreview = "";
      if (this.$v) {
        this.$v.$reset();
      }
    },

    async getCountryCodeData() {
      try {
        const response = await get("get_country_code_settings_data", {});
        if (response.data.status) {
          this.defaultCountryCode = response.data.data.country_code;
        }
      } catch (error) {
        console.error("Error fetching country code:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    async getUserRegistrationFormData() {
      try {
        const response = await get(
          "get_user_registration_form_settings_data",
          {}
        );
        if (response.data.status) {
          this.defaultUserRegistrationFormSettingData =
            response.data.data.userRegistrationFormSettingData;
        }
      } catch (error) {
        console.error("Error fetching registration form data:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    addNewSpecialization(value) {
      const specialitiesObj = {
        label: value,
        type: "specialization",
        value: value.replace(" ", "_"),
        status: 1,
      };

      post("static_data_save", specialitiesObj)
        .then((response) => {
          if (response.data.status) {
            this.doctorData.specialties.push({
              id: response.data.insert_id,
              label: value,
            });
            this.$store.commit("staticDataModule/ADD_OPTION_STATIC_DATA", {
              dataType: "specialization",
              option: {
                id: response.data.insert_id,
                label: value,
              },
            });
          }
        })
        .catch((error) => {
          console.error("Error adding specialization:", error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleClose() {
      this.$emit("close");
    },

    // Image handling methods
    uploadProfile() {
      const uploader = this.createImageUploader();
      uploader.on("select", () => {
        const attachment = uploader.state().get("selection").first().toJSON();
        this.imagePreview = attachment.url;
        this.doctorData.profile_image = attachment.id;
      });
      uploader.open();
    },

    uploadSignature() {
      const uploader = this.createImageUploader();
      uploader.on("select", () => {
        const attachment = uploader.state().get("selection").first().toJSON();
        this.signaturePreview = attachment.url;
        this.doctorData.signature = attachment.id;
      });
      uploader.open();
    },

    removeSignature() {
      this.signaturePreview = "";
      this.doctorData.signature = "";
    },

    createImageUploader() {
      return window.wp.media({
        title: this.formTranslation.common.select_image,
        multiple: false,
        library: { type: "image" },
      });
    },
  },
});
</script>

<style>
.phone-input-custom {
  --text-color: rgb(55, 65, 81);
  --border-color: rgb(209, 213, 219);
  --border-radius: 0.5rem;
  --height: 42px;
}

.phone-input-custom:focus-within {
  --border-color: rgb(139, 92, 246);
}

/* Use regular CSS instead of PostCSS/Tailwind @apply */
.vue-phone-number-input .select-country-container {
  border-radius: 0.5rem;
}

.vue-phone-number-input .input-tel {
  border-radius: 0.5rem !important;
}

.ql-container {
  border-radius: 0 0 0.5rem 0.5rem;
  border: 1px solid rgb(209, 213, 219);
}

.ql-toolbar {
  border-radius: 0.5rem 0.5rem 0 0;
  border: 1px solid rgb(209, 213, 219);
}

.ql-editor {
  min-height: 200px;
}
</style>
