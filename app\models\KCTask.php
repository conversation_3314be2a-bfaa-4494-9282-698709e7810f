<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTask extends KCModel
{
    public function __construct()
    {
        parent::__construct('tasks');
    }

    /**
     * Get all tasks with filtering
     *
     * @param array $filters - Filters to apply
     * @return array - Tasks data
     */
    public function getAllTasks($filters = [])
    {
        global $wpdb;

        $table_name = $this->get_table_name();
        $users_table = $wpdb->base_prefix . 'users';
        $assignees_table = $wpdb->prefix . 'kc_task_assignees';
        $clinics_table = $wpdb->prefix . 'kc_clinics';

        $query = "
            SELECT $table_name.*,
                   creator.display_name AS creator_name,
                   patient.display_name AS patient_name,
                   $clinics_table.name AS clinic_name,
                   GROUP_CONCAT(DISTINCT assignee.display_name) AS assignees_names
            FROM $table_name
            LEFT JOIN $users_table creator ON $table_name.creator_id = creator.ID
            LEFT JOIN $users_table patient ON $table_name.patient_id = patient.ID
            LEFT JOIN $assignees_table ON $table_name.id = $assignees_table.task_id
            LEFT JOIN $users_table assignee ON $assignees_table.assignee_id = assignee.ID
            LEFT JOIN $clinics_table ON $table_name.clinic_id = $clinics_table.id
            WHERE 1=1
        ";

        // Add filters
        if (!empty($filters['clinic_id'])) {
            $clinic_id = (int)$filters['clinic_id'];
            $query .= " AND $table_name.clinic_id = $clinic_id";
        }
        
        // Support for multiple clinics filter
        if (!empty($filters['clinic_ids']) && is_array($filters['clinic_ids'])) {
            $clinic_ids = array_map('intval', $filters['clinic_ids']);
            $clinic_ids_str = implode(',', $clinic_ids);
            $query .= " AND $table_name.clinic_id IN ($clinic_ids_str)";
        }

        if (!empty($filters['creator_id'])) {
            $creator_id = (int)$filters['creator_id'];
            $query .= " AND $table_name.creator_id = $creator_id";
        }
        
        // Search query filter - optimized for multi-word searches
        if (!empty($filters['search'])) {
            $search = trim(esc_sql($filters['search']));
            
            // First try an exact match with the complete search string
            $query .= " AND (
                $table_name.title LIKE '%$search%' OR 
                $table_name.description LIKE '%$search%' OR
                creator.display_name LIKE '%$search%' OR
                patient.display_name LIKE '%$search%' OR
                assignee.display_name LIKE '%$search%'
            )";
            
            // For multi-word searches, use more sophisticated matching
            $search_terms = explode(' ', $search);
            if (count($search_terms) > 1) {
                // First clear the previous exact-match condition so we can use our improved logic
                $query = str_replace(" AND ($table_name.title LIKE '%$search%' OR 
                $table_name.description LIKE '%$search%' OR
                creator.display_name LIKE '%$search%' OR
                patient.display_name LIKE '%$search%' OR
                assignee.display_name LIKE '%$search%')", "", $query);
                
                // Build conditions for each search field
                $title_conditions = [];
                $description_conditions = [];
                $creator_conditions = [];
                $patient_conditions = [];
                $assignee_conditions = [];
                
                foreach ($search_terms as $term) {
                    if (strlen($term) > 2) { // Skip very short terms
                        $term = esc_sql($term);
                        $title_conditions[] = "$table_name.title LIKE '%$term%'";
                        $description_conditions[] = "$table_name.description LIKE '%$term%'";
                        $creator_conditions[] = "creator.display_name LIKE '%$term%'";
                        $patient_conditions[] = "patient.display_name LIKE '%$term%'";
                        $assignee_conditions[] = "assignee.display_name LIKE '%$term%'";
                    }
                }
                
                // Add terms to the query using OR within each field group, and combine with OR between groups
                if (!empty($title_conditions) || !empty($description_conditions) || 
                    !empty($creator_conditions) || !empty($patient_conditions) || 
                    !empty($assignee_conditions)) {
                    
                    $query .= " AND (";
                    $field_groups = [];
                    
                    if (!empty($title_conditions)) {
                        $field_groups[] = "(" . implode(" AND ", $title_conditions) . ")";
                    }
                    
                    if (!empty($description_conditions)) {
                        $field_groups[] = "(" . implode(" AND ", $description_conditions) . ")";
                    }
                    
                    if (!empty($creator_conditions)) {
                        $field_groups[] = "(" . implode(" AND ", $creator_conditions) . ")";
                    }
                    
                    if (!empty($patient_conditions)) {
                        $field_groups[] = "(" . implode(" AND ", $patient_conditions) . ")";
                    }
                    
                    if (!empty($assignee_conditions)) {
                        $field_groups[] = "(" . implode(" AND ", $assignee_conditions) . ")";
                    }
                    
                    $query .= implode(" OR ", $field_groups) . ")";
                }
            }
        }

        if (!empty($filters['logged_in_user_id'])) {
            $creator_id = (int)$filters['logged_in_user_id'];
            $query .= " AND $table_name.creator_id = $creator_id";
            $query .= " OR $assignees_table.assignee_id = $creator_id";
        }

        if (!empty($filters['patient_id'])) {
            $patient_id = (int)$filters['patient_id'];
            $query .= " AND $table_name.patient_id = $patient_id";
        }
        
        // Support for multiple patients filter
        if (!empty($filters['patient_ids']) && is_array($filters['patient_ids'])) {
            $patient_ids = array_map('intval', $filters['patient_ids']);
            $patient_ids_str = implode(',', $patient_ids);
            $query .= " AND $table_name.patient_id IN ($patient_ids_str)";
        }

        if (!empty($filters['status']) && $filters['status'] !== 'all') {
            $status = esc_sql($filters['status']);
            $query .= " AND $table_name.status = '$status'";
        }

        if (!empty($filters['priority']) && $filters['priority'] !== 'all') {
            $priority = esc_sql($filters['priority']);
            $query .= " AND $table_name.priority = '$priority'";
        }

        if (!empty($filters['category'])) {
            $category = esc_sql($filters['category']);
            $query .= " AND $table_name.category = '$category'";
        }

        if (!empty($filters['assignee_id'])) {
            $assignee_id = (int)$filters['assignee_id'];
            $query .= " AND $assignees_table.assignee_id = $assignee_id";
        }

        if (isset($filters['is_archived'])) {
            $is_archived = (int)$filters['is_archived'];
            $query .= " AND $table_name.is_archived = $is_archived";
        } else {
            // By default, don't show archived tasks
            $query .= " AND $table_name.is_archived = 0";
        }

        if (!empty($filters['due_date_start']) && !empty($filters['due_date_end'])) {
            $start_date = esc_sql($filters['due_date_start']);
            $end_date = esc_sql($filters['due_date_end']);
            $query .= " AND $table_name.due_date BETWEEN '$start_date' AND '$end_date'";
        } else if (!empty($filters['due_date_start'])) {
            $start_date = esc_sql($filters['due_date_start']);
            $query .= " AND $table_name.due_date >= '$start_date'";
        } else if (!empty($filters['due_date_end'])) {
            $end_date = esc_sql($filters['due_date_end']);
            $query .= " AND $table_name.due_date <= '$end_date'";
        }

        // Group by task ID to handle multiple assignees
        $query .= " GROUP BY $table_name.id";

        // Add ordering
        if (!empty($filters['orderby'])) {
            $orderby = esc_sql($filters['orderby']);
            $order = !empty($filters['order']) ? esc_sql($filters['order']) : 'ASC';
            $query .= " ORDER BY $table_name.$orderby $order";
        } else {
            // Default ordering by due date
            $query .= " ORDER BY $table_name.due_date ASC";
        }

        // Get total count for pagination (before LIMIT is applied)
        $count_query = str_replace('SELECT ' . $table_name . '.*,', 'SELECT COUNT(DISTINCT ' . $table_name . '.id) as total', $query);
        $count_query = preg_replace('/GROUP BY.*$/is', '', $count_query);
        $total_count = $wpdb->get_var($count_query);
        
        // Apply pagination
        if (!empty($filters['per_page']) && !empty($filters['page'])) {
            $per_page = (int)$filters['per_page'];
            $page = (int)$filters['page'];
            $offset = ($page - 1) * $per_page;
            $query .= " LIMIT $offset, $per_page";
        }
        
        // Get the tasks with basic assignee info
        $tasks = $wpdb->get_results($query);
        
        // Now get complete assignee details for each task
        if (!empty($tasks)) {
            // Extract all task IDs
            $task_ids = array_map(function($task) {
                return $task->id;
            }, $tasks);
            
            $task_ids_str = implode(',', $task_ids);
            
            // Query to get all assignees with complete details
            $assignees_query = "
                SELECT a.task_id, 
                       a.id AS assignee_record_id,
                       a.assignee_id,
                       a.assigned_at,
                       a.completed_at,
                       u.display_name AS assignee_name,
                       u.user_email AS assignee_email
                FROM $assignees_table a
                LEFT JOIN $users_table u ON a.assignee_id = u.ID
                WHERE a.task_id IN ($task_ids_str)
                ORDER BY a.task_id, u.display_name
            ";
            
            $assignees = $wpdb->get_results($assignees_query);
            
            // Group assignees by task_id
            $assignees_by_task = [];
            foreach ($assignees as $assignee) {
                if (!isset($assignees_by_task[$assignee->task_id])) {
                    $assignees_by_task[$assignee->task_id] = [];
                }
                $assignees_by_task[$assignee->task_id][] = $assignee;
            }
            
            // Attach complete assignee data to each task
            foreach ($tasks as $task) {
                $task->assignees = isset($assignees_by_task[$task->id]) ? $assignees_by_task[$task->id] : [];
            }
        }

        // Return both the tasks and pagination data
        return [
            'tasks' => $tasks,
            'total' => (int)$total_count,
            'page' => isset($filters['page']) ? (int)$filters['page'] : 1,
            'per_page' => isset($filters['per_page']) ? (int)$filters['per_page'] : count($tasks),
            'total_pages' => isset($filters['per_page']) ? ceil((int)$total_count / (int)$filters['per_page']) : 1
        ];
    }

    /**
     * Get task by ID with all related data
     *
     * @param int $task_id - Task ID
     * @return object - Task data
     */
    public function getTaskById($task_id)
    {
        global $wpdb;

        $task_id = (int)$task_id;
        $table_name = $this->get_table_name();
        $users_table = $wpdb->base_prefix . 'users';
        $clinics_table = $wpdb->prefix . 'kc_clinics';

        $query = "
            SELECT $table_name.*,
                   creator.display_name AS creator_name,
                   patient.display_name AS patient_name,
                   $clinics_table.name AS clinic_name
            FROM $table_name
            LEFT JOIN $users_table creator ON $table_name.creator_id = creator.ID
            LEFT JOIN $users_table patient ON $table_name.patient_id = patient.ID
            LEFT JOIN $clinics_table ON $table_name.clinic_id = $clinics_table.id
            WHERE $table_name.id = $task_id
        ";

        $task = $wpdb->get_row($query);

        if ($task) {
            // Get task assignees
            $assignees_table = $wpdb->prefix . 'kc_task_assignees';
            $assignees_query = "
                SELECT $assignees_table.*, $users_table.display_name AS assignee_name
                FROM $assignees_table
                JOIN $users_table ON $assignees_table.assignee_id = $users_table.ID
                WHERE $assignees_table.task_id = $task_id
            ";
            $task->assignees = $wpdb->get_results($assignees_query);

            // Get task comments
            $comments_table = $wpdb->prefix . 'kc_task_comments';
            $comments_query = "
                SELECT $comments_table.*, $users_table.display_name AS user_name
                FROM $comments_table
                JOIN $users_table ON $comments_table.user_id = $users_table.ID
                WHERE $comments_table.task_id = $task_id
                ORDER BY $comments_table.created_at DESC
            ";
            $task->comments = $wpdb->get_results($comments_query);

            // Get task attachments
            $attachments_table = $wpdb->prefix . 'kc_task_attachments';
            $attachments_query = "
                SELECT $attachments_table.*, $users_table.display_name AS uploader_name
                FROM $attachments_table
                JOIN $users_table ON $attachments_table.uploaded_by = $users_table.ID
                WHERE $attachments_table.task_id = $task_id
            ";
            $task->attachments = $wpdb->get_results($attachments_query);
        }

        return $task;
    }

    /**
     * Create or update a task
     *
     * @param array $data - Task data
     * @param int $task_id - Task ID (null for create)
     * @return int - Task ID
     */
    public function saveTask($data, $task_id = null)
    {
        global $wpdb;

        $task_data = [
            'title' => sanitize_text_field($data['title']),
            'description' => isset($data['description']) ? sanitize_textarea_field($data['description']) : '',
            'clinic_id' => (int)$data['clinic_id'],
            'creator_id' => get_current_user_id(),
            'patient_id' => !empty($data['patient_id']) ? (int)$data['patient_id'] : null,
            'priority' => !empty($data['priority']) ? sanitize_text_field($data['priority']) : 'medium',
            'status' => !empty($data['status']) ? sanitize_text_field($data['status']) : 'pending',
            'updated_at' => current_time('mysql')
        ];

        if (!empty($data['due_date'])) {
            $task_data['due_date'] = sanitize_text_field($data['due_date']);
        }

        if (!empty($data['reminder_date'])) {
            $task_data['reminder_date'] = sanitize_text_field($data['reminder_date']);
        }

        if (!empty($data['repeating'])) {
            $task_data['repeating'] = sanitize_text_field($data['repeating']);
        }

        if (!empty($data['category'])) {
            $task_data['category'] = sanitize_text_field($data['category']);
        }

        if (isset($data['is_archived'])) {
            $task_data['is_archived'] = (int)$data['is_archived'];
        }

        if ($task_id) {
            // Update existing task
            $this->update($task_data, ['id' => $task_id]);
            return $task_id;
        } else {
            // Create new task
            $task_data['created_at'] = current_time('mysql');
            return $this->insert($task_data);
        }
    }

    /**
     * Update task status
     *
     * @param int $task_id - Task ID
     * @param string $status - New status
     * @return bool - Success
     */
    public function updateTaskStatus($task_id, $status)
    {
        return $this->update(
            [
                'status' => sanitize_text_field($status),
                'updated_at' => current_time('mysql')
            ],
            ['id' => (int)$task_id]
        );
    }
}