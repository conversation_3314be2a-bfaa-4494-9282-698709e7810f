/* Appointment Booking Widget Styles */
:root {
  --primary-color: #7093e5;
  --primary-color-dark: #4367b9;
  --secondary-color: #f68685;
  --secondary-color-dark: #df504e;
  --black: #333;
  --gray: #6b7280;
  --gray-dark: #4b5563;
  --gray-light: #e5e7eb;
  --radius: 0.375rem;
}

/* Base styles */
.kivi-booking-widget {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  max-width: 1000px;
  margin: 0 auto;
  color: var(--black);
}

.kivi-booking-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

/* Header styles */
.kivi-booking-header {
  border-bottom: 1px solid var(--gray-light);
}

.kivi-step-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.kivi-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.kivi-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 12px;
  right: calc(-50% + 10px);
  width: calc(100% - 20px);
  height: 2px;
  background-color: var(--gray-light);
  z-index: 1;
}

.kivi-step.active:not(:last-child)::after,
.kivi-step.completed:not(:last-child)::after {
  background-color: var(--primary-color);
}

.kivi-step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--gray-light);
  color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

.kivi-step.active .kivi-step-dot,
.kivi-step.completed .kivi-step-dot {
  background-color: var(--primary-color);
  color: white;
}

.kivi-step-label {
  font-size: 0.75rem;
  color: var(--gray);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.kivi-step.active .kivi-step-label,
.kivi-step.completed .kivi-step-label {
  color: var(--primary-color);
  font-weight: 500;
}

/* Progress bar */
.progress-container {
  position: relative;
  height: 6px;
  background-color: var(--gray-light);
  border-radius: 3px;
  margin-top: 0.5rem;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-indicator {
  position: absolute;
  top: -20px;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: var(--gray);
  transition: left 0.3s ease;
}

/* Body styles */
.kivi-booking-body {
  padding: 1.5rem;
}

.form-card {
  min-height: 300px;
}

/* Trust badge */
.trust-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-top: 1px solid var(--gray-light);
  font-size: 0.75rem;
  color: var(--gray);
}

.shield-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  color: var(--gray);
}

.badge-text {
  font-weight: 500;
}

/* Step styles */
.kivi-booking-step {
  animation: fadeIn 0.3s ease-in-out;
}

.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

/* Form elements */
.kivi-form-group {
  margin-bottom: 1.25rem;
}

.kivi-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--black);
}

.kivi-required {
  color: #ef4444;
}

.kivi-form-input,
.kivi-form-textarea,
.kivi-form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-light);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus,
.kivi-form-textarea:focus,
.kivi-form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(112, 147, 229, 0.1);
}

.kivi-input-error {
  border-color: #ef4444;
}

.kivi-error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Buttons */
.kivi-form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.kivi-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.kivi-btn svg {
  margin: 0 0.5rem;
}

.kivi-btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.kivi-btn-primary:hover {
  background-color: var(--primary-color-dark);
}

.kivi-btn-secondary {
  background-color: white;
  color: var(--gray-dark);
  border: 1px solid var(--gray-light);
}

.kivi-btn-secondary:hover {
  background-color: #f9fafb;
}

/* Cards */
.kivi-card {
  border: 1px solid var(--gray-light);
  border-radius: var(--radius);
  padding: 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.kivi-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.kivi-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(112, 147, 229, 0.05);
}

.kivi-card-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
}

.kivi-card-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
}

/* Grid layout */
.kivi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Loader */
.kivi-loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.kivi-loader {
  border: 3px solid rgba(229, 231, 235, 0.3);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2rem;
  height: 2rem;
  animation: kivi-spin 1s linear infinite;
}

.kivi-loader-text {
  margin-top: 1rem;
  color: var(--gray);
  font-size: 0.875rem;
}

/* Animations */
@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(1rem); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .kivi-step-label {
    font-size: 0.7rem;
  }
  
  .kivi-booking-header {
    padding: 1rem;
  }
  
  .kivi-booking-body {
    padding: 1rem;
  }
  
  .kivi-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .kivi-step-label {
    display: none;
  }
  
  .kivi-step-dot {
    margin-bottom: 0;
  }
  
  .kivi-form-actions {
    flex-direction: column;
    gap: 1rem;
  }
  
  .kivi-btn {
    width: 100%;
  }
}
