<!-- PersonalInfo.vue -->
<template>
  <div>
    <!-- View Mode -->
    <div
      v-if="!isEditMode"
      class="bg-white text-card-foreground rounded-xl border shadow"
    >
      <div class="flex flex-col space-y-1.5 p-6">
        <h3 class="font-semibold leading-none tracking-tight">
          Personal Information
        </h3>
      </div>
      <div class="p-6 pt-0">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">name</label>
            <p class="font-medium">
              {{ localData.first_name }} {{ localData.last_name }}
            </p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">dob</label>
            <p class="font-medium">{{ formatDate(localData.dob) }}</p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">gender</label>
            <p class="font-medium">{{ capitalize(localData.gender) }}</p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">NHS</label>
            <p class="font-medium">
              {{
                localData.nhs !== ""
                  ? localData.nhs
                  : "--"
              }}
            </p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">email</label>
            <p class="font-medium">{{ localData.user_email }}</p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">contact number</label>
            <p class="font-medium">{{ localData.mobile_number }}</p>
          </div>
          <div v-if="!hideFields.includes('address')" class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">address</label>
            <p class="font-medium">{{ localData.address || "--" }}</p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">clinic</label>
            <p class="font-medium">{{ getClinicNames }}</p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">Registered GP Name</label>
            <p class="font-medium">{{ localData.registered_gp_name || "--" }}</p>
          </div>
          <div class="space-y-1">
            <label class="text-sm text-gray-600 capitalize">Registered GP's address</label>
            <p class="font-medium">{{ localData.registered_gp_address || "--" }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Mode -->
    <form v-else @submit.prevent="saveChanges" :novalidate="true">
      <div class="bg-white text-card-foreground rounded-xl border shadow">
        <div class="flex flex-col space-y-1.5 p-6">
          <h3 class="font-semibold leading-none tracking-tight">
            Personal Information
          </h3>
        </div>
        <div class="p-6 pt-0">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- First Name -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.common.fname }}
                <span class="text-red-500">*</span>
              </label>
              <input
                v-model="localData.first_name"
                :class="[
                  'w-full rounded-lg border p-2',
                  {
                    'border-red-500':
                      submitted && $v.localData.first_name.$error,
                  },
                ]"
                :placeholder="formTranslation.patient.fname_plh"
              />
              <p
                v-if="submitted && !$v.localData.first_name.required"
                class="text-red-500 text-sm"
              >
                {{ formTranslation.common.fname_required }}
              </p>
            </div>

            <!-- Last Name -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.common.lname }}
                <span class="text-red-500">*</span>
              </label>
              <input
                v-model="localData.last_name"
                :class="[
                  'w-full rounded-lg border p-2',
                  {
                    'border-red-500':
                      submitted && $v.localData.last_name.$error,
                  },
                ]"
                :placeholder="formTranslation.patient.lname_placeholder"
              />
              <p
                v-if="submitted && !$v.localData.last_name.required"
                class="text-red-500 text-sm"
              >
                {{ formTranslation.common.lname_required }}
              </p>
            </div>

            <!-- Email -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.common.email_address }}
                <span class="text-red-500">*</span>
              </label>
              <input
                type="email"
                v-model="localData.user_email"
                :class="[
                  'w-full rounded-lg border p-2',
                  {
                    'border-red-500':
                      submitted && $v.localData.user_email.$error,
                  },
                ]"
                :placeholder="formTranslation.patient.address_placeholder"
              />
              <p
                v-if="submitted && !$v.localData.user_email.required"
                class="text-red-500 text-sm"
              >
                {{ formTranslation.common.email_required }}
              </p>
            </div>

            <!-- Clinic -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.patient.select_clinic }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select
                v-model="localData.clinic_id"
                :options="clinics"
                :loading="clinicMultiselectLoader"
                label="label"
                track-by="id"
                :multiple="true"
                :taggable="true"
                class="multiselect-purple"
                :class="{
                  'border-red-500': submitted && $v.localData.clinic_id.$error,
                }"
                disabled
              />
              <p
                v-if="submitted && !$v.localData.clinic_id.required"
                class="text-red-500 text-sm"
              >
                {{ formTranslation.common.clinic_is_required }}
              </p>
            </div>

            <!-- Phone -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.common.contact_no }}
                <span class="text-red-500">*</span>
              </label>
              <VuePhoneNumberInput
                v-model="localData.mobile_number"
                :default-country-code="defaultCountryCode"
                @update="handlePhoneUpdate"
                class="phone-input-container"
                :class="{
                  error: submitted && $v.localData.mobile_number.$error,
                }"
                no-example
              />
            </div>

            <!-- Date of Birth -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">{{
                formTranslation.common.dob
              }}</label>
              <input
                type="date"
                v-model="localData.dob"
                class="w-full rounded-lg border p-2"
                :max="new Date().toISOString().slice(0, 10)"
              />
            </div>

            <!-- Gender -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.common.gender }}
                <span class="text-red-500">*</span>
              </label>
              <div class="flex space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.gender"
                    value="male"
                    class="form-radio text-purple-600"
                  />
                  <span class="ml-2">{{ formTranslation.common.male }}</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.gender"
                    value="female"
                    class="form-radio text-purple-600"
                  />
                  <span class="ml-2">{{ formTranslation.common.female }}</span>
                </label>
                <label
                  v-if="defaultUserRegistrationFormSettingData === 'on'"
                  class="inline-flex items-center"
                >
                  <input
                    type="radio"
                    v-model="localData.gender"
                    value="other"
                    class="form-radio text-purple-600"
                  />
                  <span class="ml-2">{{ formTranslation.common.other }}</span>
                </label>
              </div>
            </div>

            <!-- Registered Gp Name -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.common.registered_gp_name }}
                <span class="text-red-500">*</span>
              </label>
              <input
                type="email"
                v-model="localData.registered_gp_name"
                :class="[
                  'w-full rounded-lg border p-2',
                  {
                    'border-red-500':
                      submitted && $v.localData.registered_gp_name.$error,
                  },
                ]"
                :placeholder="formTranslation.common.registered_gp_name"
              />
              <p
                v-if="submitted && !$v.localData.registered_gp_name.required"
                class="text-red-500 text-sm"
              >
                {{ formTranslation.common.registered_gp_name }}
              </p>
            </div>

            <!-- Registered Gp Address -->
            <div class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">
                {{ formTranslation.common.registered_gp_address }}
                <span class="text-red-500">*</span>
              </label>
              <input
                type="email"
                v-model="localData.registered_gp_address"
                :class="[
                  'w-full rounded-lg border p-2',
                  {
                    'border-red-500':
                      submitted && $v.localData.registered_gp_address.$error,
                  },
                ]"
                :placeholder="formTranslation.common.registered_gp_address"
              />
              <p
                v-if="submitted && !$v.localData.registered_gp_address.required"
                class="text-red-500 text-sm"
              >
                {{ formTranslation.common.registered_gp_address }}
              </p>
            </div>

            <!-- Address -->
            <div v-if="!hideFields.includes('address')" class="space-y-1">
              <label class="text-sm text-gray-600 capitalize">{{
                formTranslation.common.address
              }}</label>
              <textarea
                v-model="localData.address"
                :placeholder="formTranslation.patient.address_placeholder"
                class="w-full rounded-lg border p-2"
                rows="3"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end mt-6 space-x-4">
        <button
          type="button"
          @click="cancelEdit"
          class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          {{ formTranslation.common.cancel }}
        </button>
        <button
          type="submit"
          :disabled="loading"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center"
        >
          <i v-if="loading" class="fa fa-sync fa-spin mr-2"></i>
          {{
            loading
              ? formTranslation.common.loading
              : formTranslation.common.save
          }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { required, minLength, maxLength } from "vuelidate/lib/validators";
import VuePhoneNumberInput from "vue-phone-number-input";
import { get, post } from "../../config/request";
import { emailValidate } from "../../config/helper";
import { displayMessage, displayErrorMessage } from "../../utils/message";

export default {
  name: "PersonalInfo",

  components: {
    VuePhoneNumberInput,
  },

  props: {
    isEditMode: Boolean,
    patientData: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      localData: { ...this.patientData },
      loading: false,
      submitted: false,
      bloodGroups: ["A+", "B+", "AB+", "O+", "A-", "B-", "AB-", "O-"],
      hideFields: [],
      defaultCountryCode: null,
      clinicMultiselectLoader: true,
      clinics: [],
      defaultUserRegistrationFormSettingData: "on",
    };
  },

  validations: {
    localData: {
      first_name: { required },
      last_name: { required },
      user_email: { required, emailValidate },
      mobile_number: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
    },
  },

  watch: {
    patientData: {
      handler(newVal) {
        if (JSON.stringify(this.localData) !== JSON.stringify(newVal)) {
          this.localData = { ...newVal };
        }
      },
      deep: true,
    },
  },

  mounted() {
    this.initializeData();
  },

  methods: {
    async initializeData() {
      await Promise.all([
        this.getCountryCodeData(),
        this.getUserRegistrationFormData(),
        this.getClinics(),
        this.getHideFieldsArrayFromFilter(),
      ]);
    },

    formatDate(date) {
      if (!date) return "--";
      return new Date(date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },

    capitalize(str) {
      if (!str) return "--";
      return str.charAt(0).toUpperCase() + str.slice(1);
    },

    handlePhoneUpdate(val) {
      this.localData = {
        ...this.localData,
        country_code: val.countryCode,
        country_calling_code: val.countryCallingCode,
      };
    },

    async saveChanges() {
      this.submitted = true;
      this.$v.$touch();

      if (this.$v.$invalid) return;

      this.loading = true;
      try {
        const response = await post("patient_save", this.localData);
        if (response.data.status) {
          displayMessage(response.data.message);
          this.$emit("saved", this.localData);
        } else {
          throw new Error(response.data.message);
        }
      } catch (error) {
        displayErrorMessage(error.message || "Failed to save changes");
      } finally {
        this.loading = false;
      }
    },

    cancelEdit() {
      this.localData = { ...this.patientData };
      this.$emit("cancel");
    },

    async getCountryCodeData() {
      const response = await get("get_country_code_settings_data", {});
      if (response.data.status) {
        this.defaultCountryCode = response.data.data.country_code;
      }
    },

    async getClinics() {
      const response = await get("get_static_data", { data_type: "clinics" });
      if (response.data.status) {
        this.clinics = response.data.data;
      }
      this.clinicMultiselectLoader = false;
    },

    async getUserRegistrationFormData() {
      const response = await get(
        "get_user_registration_form_settings_data",
        {}
      );
      if (response.data.status) {
        this.defaultUserRegistrationFormSettingData =
          response.data.data.userRegistrationFormSettingData;
      }
    },

    async getHideFieldsArrayFromFilter() {
      const response = await get("get_hide_fields_array_from_filter", {});
      if (response.data.status) {
        this.hideFields = response.data.data;
      }
    },
  },

  computed: {
    getClinicNames() {
      if (!this.localData.clinic_id?.length || !this.clinics?.length)
        return "--";
      return (
        this.localData.clinic_id
          .map((id) => this.clinics.find((c) => c.id === id)?.label)
          .filter(Boolean)
          .join(", ") || "--"
      );
    },
  },
};
</script>

<style scoped>
.phone-input-container {
  --text-color: rgb(55, 65, 81);
  --border-radius: 0.5rem;
  --border-color: rgb(229, 231, 235);
  --border-color-hover: rgb(107, 114, 128);
  --border-color-focus: rgb(147, 51, 234);
}

.phone-input-container.error {
  --border-color: rgb(239, 68, 68);
  --border-color-hover: rgb(239, 68, 68);
  --border-color-focus: rgb(239, 68, 68);
}

.multiselect-purple {
  --ms-tag-bg: rgb(147, 51, 234);
  --ms-tag-color: white;
  --ms-option-bg-selected: rgb(147, 51, 234);
}

input[type="radio"] {
  @apply h-4 w-4 text-purple-600 border-gray-300 focus:ring-purple-500;
}

/* Date input styling */
input[type="date"] {
  @apply appearance-none;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  @apply opacity-0 absolute right-0 top-0 bottom-0 w-full cursor-pointer;
}

input[type="date"] {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>');
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
}
</style>
