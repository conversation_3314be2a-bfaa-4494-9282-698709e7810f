<template>
  <div class="kivi-booking-step" id="step-services">
    <h2 class="kivi-step-title"></h2>
    <p class="kivi-step-subtitle"></p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader-circle"></div>
      <p class="kivi-loader-text">Loading services...</p>
    </div>

    <template v-else>
      <div class="kivi-form-group selected-category-info">
        <div class="kivi-form-label">Selected Category: <span id="selected-category-name">{{ bookingData.category ? bookingData.category.name : 'Please select a category' }}</span></div>
        <button type="button" class="kivi-btn kivi-btn-secondary" @click="changeCategory">Change</button>
      </div>

      <div class="kivi-form-group kivi-search-input">
        <div class="kivi-search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          class="kivi-form-input"
          id="service-search"
          placeholder="Search services..."
          v-model="searchTerm"
        >
      </div>

      <div class="kivi-form-group">
        <div class="kivi-form-label">Filter By:</div>
        <div class="kivi-filter-buttons">
          <button
            type="button"
            class="kivi-btn kivi-btn-secondary"
            :class="{ 'active': activeFilter === 'all' }"
            @click="filterServices('all')"
          >
            All
          </button>
          <button
            type="button"
            class="kivi-btn kivi-btn-secondary"
            :class="{ 'active': activeFilter === 'virtual' }"
            @click="filterServices('virtual')"
          >
            Virtual
          </button>
          <button
            type="button"
            class="kivi-btn kivi-btn-secondary"
            :class="{ 'active': activeFilter === 'clinic' }"
            @click="filterServices('clinic')"
          >
            In-Clinic
          </button>
        </div>
      </div>

      <div class="kivi-grid" id="service-list">
        <div
          v-for="service in filteredServices"
          :key="service.id"
          class="kivi-card service-card"
          :class="{
            'selected': isServiceSelected(service.id),
            'disabled': !canSelectService(service),
            'virtual-service': service.telemed_service === 'yes',
            'clinic-service': service.telemed_service !== 'yes'
          }"
          :data-service-id="service.id"
          :data-service-type="service.telemed_service === 'yes' ? 'virtual' : 'clinic'"
          @click="toggleService(service)"
        >
          <!-- Service type indicator icon at top right -->
          <div class="kivi-service-type-indicator">
            <div v-if="service.telemed_service === 'yes'" class="kivi-service-type-icon virtual" title="Virtual Appointment">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
              </svg>
            </div>
            <div v-else class="kivi-service-type-icon clinic" title="In-Clinic Appointment">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>
          </div>

          <div class="kivi-card-header">
            <div>
              <h3 class="kivi-card-title">{{ service.name }}</h3>
              <div class="kivi-card-subtitle" v-if="service.doctor_name">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="doctor-icon">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                  <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
                Dr. {{ service.doctor_name }}
              </div>
            </div>
            <div class="kivi-card-price pt-7">{{ service.charges }}</div>
          </div>

          <div class="kivi-card-body" v-if="service.description">
            <p>{{ service.description }}</p>
          </div>

          <div class="kivi-card-badges">
            <div class="kivi-badge kivi-badge-blue" v-if="service.duration">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="duration-icon">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              {{ formatDuration(service.duration) }}
            </div>
            <div class="kivi-badge kivi-badge-green" v-if="service.telemed_service === 'yes'">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="virtual-icon">
                <path d="M15.6 11.6L22 7v10l-6.4-4.5v-1zM4 5h9a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V7c0-1.1.9-2 2-2z"></path>
              </svg>
              Virtual
            </div>
            <div class="kivi-badge kivi-badge-purple" v-else>
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="clinic-icon">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="3" y1="9" x2="21" y2="9"></line>
                <line x1="9" y1="21" x2="9" y2="9"></line>
              </svg>
              In-Clinic
            </div>
          </div>

          <!-- Next available appointment with enhanced visual presentation -->
          <div class="kivi-next-available" v-if="service.next_available">
            <div class="kivi-next-available-label">
              <div class="kivi-next-available-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
              </div>
              <span v-if="!service.next_available.includes('Next available')">Next available:</span>
            </div>
            <div class="kivi-next-available-date">
              <strong>{{ service.next_available.includes('Next available') ? service.next_available : service.next_available }}</strong>
              <span class="kivi-available-indicator"></span>
            </div>
          </div>

          <!-- Warning message for non-multiservice items -->
          <div class="kivi-service-warning" v-if="!canSelectService(service)">
            <p>
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
              This service cannot be combined with other services.
            </p>
          </div>
        </div>
      </div>

      <div v-if="filteredServices.length === 0" class="kivi-empty-state">
        <div class="kivi-empty-state-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </div>
        <p class="kivi-empty-state-title">No services found</p>
        <p class="kivi-empty-state-subtitle">Try changing your search terms or selecting a different category.</p>
        <button type="button" class="kivi-btn kivi-btn-secondary kivi-empty-state-btn" @click="changeCategory">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Return to categories
        </button>
      </div>
    </template>
  </div>
</template>

<script>
import { apiCall } from '../../../config/request';

export default {
  name: 'ServiceStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      services: [],
      searchTerm: '',
      selectedServices: [],
      activeFilter: 'all',
      isLoading: false
    };
  },
  computed: {
    filteredServices() {
      if (!this.services.length) return [];

      let result = this.services;

      // Apply service type filter
      if (this.activeFilter !== 'all') {
        const isVirtual = this.activeFilter === 'virtual';
        result = result.filter(service =>
          isVirtual ? service.telemed_service === 'yes' : service.telemed_service !== 'yes'
        );
      }

      // Apply search term filter
      if (this.searchTerm) {
        const term = this.searchTerm.toLowerCase();
        result = result.filter(service =>
          service.name.toLowerCase().includes(term) ||
          (service.description && service.description.toLowerCase().includes(term))
        );
      }

      return result;
    },

    // Check if any selected service has multiple=no
    hasSingleServiceSelected() {
      return this.selectedServices.some(service => service.multiple === 'no');
    },

    // Get ID of the single service if one is selected
    singleServiceId() {
      const singleService = this.selectedServices.find(service => service.multiple === 'no');
      return singleService ? singleService.id : null;
    }
  },
  watch: {
    'bookingData.category': {
      immediate: true,
      handler(newCategory) {
        if (newCategory?.id) {
          this.fetchServices(newCategory.id);
        }
      }
    },

    'bookingData.services': {
      immediate: true,
      handler(newServices) {
        this.selectedServices = newServices ? [...newServices] : [];
      }
    }
  },
  methods: {

    async fetchServices(categoryId) {
      if (!this.bookingData.clinic?.id) return;

      try {
        this.isLoading = true;
        this.services = [];

        const response = await apiCall.get('get_clinic_service', {
          params: {
            clinic_id: this.bookingData.clinic.id,
            service_category: categoryId,
            format: 'json'
          }
        });

        if (response.data?.status && Array.isArray(response.data.data)) {
          this.services = response.data.data.map(service => ({
            id: service.id || service.mapping_id || service.service_id,
            name: service.name || service.service_name,
            charges: service.price || service.service_base_price || service.charges || '$0',
            duration: service.duration || '30',
            description: service.description || '',
            doctor_id: service.doctor_id || service.doctor_mapping_id || null,
            doctor_name: service.doctor_name || '',
            telemed_service: service.telemed_service || 'no',
            serviceType: service.telemed_service === 'yes' ? 'virtual' : 'clinic',
            multiple: service.multiple || 'yes',
            next_available: service.next_available_date || 'Available soon'
          }));
        }
      } catch (error) {
        console.error('Error fetching services:', error);
      } finally {
        this.isLoading = false;
      }
    },

    formatDuration(duration) {
      if (!duration) return '';

      // Convert duration to minutes if it's not already
      const minutes = parseInt(duration);
      if (isNaN(minutes)) return '';

      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;

      if (hours > 0) {
        return `${hours}h ${mins > 0 ? mins + 'min' : ''}`;
      }

      return `${mins} min`;
    },

    filterServices(filterType) {
      this.activeFilter = filterType;
    },

    isServiceSelected(serviceId) {
      return this.selectedServices.some(service => service.id === serviceId);
    },

    canSelectService(service) {
      // If this service has multiple=no
      if (service.multiple === 'no') {
        // Only allow if no other services are selected
        return this.selectedServices.length === 0 ||
               (this.selectedServices.length === 1 && this.selectedServices[0].id === service.id);
      }

      // If a single service is already selected, don't allow adding more
      if (this.hasSingleServiceSelected) {
        return this.singleServiceId === service.id;
      }

      return true;
    },

    toggleService(service) {
      if (!this.canSelectService(service)) return;

      const index = this.selectedServices.findIndex(s => s.id === service.id);

      if (index === -1) {
        // Add service
        const serviceToAdd = {
          id: service.id,
          service_id: service.service_id || service.id,
          name: service.name,
          price: service.charges,
          type: service.telemed_service === 'yes' ? 'virtual' : 'clinic',
          duration: service.duration || '30',
          multiple: service.multiple || 'yes',
          doctor_id: service.doctor_id || null,
          clinic_id: this.bookingData.clinic.id,
          doctor_name: service.doctor_name || null,
          category: this.bookingData.category?.name || null,
          telemed_service: service.telemed_service || 'no',
          charges: service.charges
        };

        this.selectedServices.push(serviceToAdd);
      } else {
        // Remove service
        this.selectedServices.splice(index, 1);
      }

      // Update doctor info from first selected service
      let doctorInfo = null;
      if (this.selectedServices.length > 0) {
        const firstService = this.selectedServices[0];
        if (firstService.doctor_id) {
          doctorInfo = {
            id: firstService.doctor_id,
            name: firstService.doctor_name || 'Doctor'
          };
        }
      }

      this.$emit('update:booking-data', {
        ...this.bookingData,
        services: [...this.selectedServices],
        doctor: doctorInfo || this.bookingData.doctor
      });
    },

    changeCategory() {
      this.$emit('go-to-step', 1); // Go back to category step
    }
  }
};
</script>

<style scoped>
.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-form-group {
  margin-bottom: 1rem;
}

.selected-category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: var(--light-gray);
  border-radius: var(--radius);
}

.kivi-form-label {
  font-weight: 500;
  color: var(--dark-gray);
}

.kivi-search-input {
  position: relative;
}

.kivi-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.kivi-search-input input {
  padding-left: 2.5rem;
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-filter-buttons {
  display: flex;
  gap: 0.5rem;
}

.kivi-filter-buttons button {
  padding: 0.375rem 0.75rem;
}

.kivi-filter-buttons button.active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.kivi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.kivi-card {
  border: 2px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.kivi-card:hover:not(.disabled) {
  border-color: rgba(79, 70, 229, 0.4);
  background-color: rgba(79, 70, 229, 0.02);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kivi-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.kivi-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Next available styling */
.kivi-next-available {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px dashed rgba(229, 231, 235, 0.8);
  color: var(--gray);
  font-size: 0.8125rem;
}

.kivi-next-available-label {
  display: flex;
  align-items: center;
}

.kivi-next-available-icon {
  display: flex;
  margin-right: 0.375rem;
  color: var(--primary-color);
}

.kivi-next-available-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.kivi-next-available-date strong {
  color: var(--dark-gray);
  font-weight: 600;
}

.kivi-available-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10B981;
  position: relative;
}

.kivi-available-indicator:after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background-color: rgba(16, 185, 129, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  70% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.kivi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.kivi-card-title {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.kivi-card-subtitle {
  font-size: 0.75rem;
  color: var(--gray);
}

.kivi-card-price {
  font-weight: 600;
  color: var(--primary-color);
}

.kivi-card-body {
  font-size: 0.875rem;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.kivi-card-footer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.kivi-badge-blue {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgba(29, 78, 216, 1);
}

.kivi-badge-green {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgba(5, 150, 105, 1);
}

.kivi-badge-purple {
  background-color: rgba(124, 58, 237, 0.1);
  color: rgba(109, 40, 217, 1);
}

.kivi-card-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.5;
}

.kivi-service-warning {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--danger-color);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  backdrop-filter: blur(2px);
  border-radius: var(--radius);
}

.kivi-service-warning p {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.kivi-service-warning svg {
  color: var(--danger-color);
}

.kivi-card.disabled:hover .kivi-service-warning {
  opacity: 1;
}

.kivi-service-type-indicator {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  z-index: 5;
}

.kivi-service-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kivi-service-type-icon.virtual {
  color: rgba(16, 185, 129, 0.9);
  background-color: rgba(16, 185, 129, 0.1);
}

.kivi-service-type-icon.clinic {
  color: rgba(124, 58, 237, 0.9);
  background-color: rgba(124, 58, 237, 0.1);
}

.doctor-icon, .virtual-icon, .clinic-icon, .duration-icon {
  margin-right: 0.25rem;
}

.virtual-service {
  border-left: 4px solid rgba(16, 185, 129, 0.7);
}

.clinic-service {
  border-left: 4px solid rgba(124, 58, 237, 0.7);
}

.kivi-card-subtitle {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: var(--gray);
  margin-top: 0.25rem;
}

.kivi-loader-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
  gap: 1rem;
}

.kivi-loader-circle {
  border: 3px solid rgba(229, 231, 235, 1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2.5rem;
  height: 2.5rem;
  animation: kivi-spin 1s linear infinite;
}

.kivi-loader-text {
  color: var(--gray);
  font-size: 0.875rem;
  font-weight: 500;
}

.kivi-empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem 1rem;
  color: var(--gray);
  background-color: rgba(243, 244, 246, 0.5);
  border-radius: var(--radius);
  min-height: 16rem;
  text-align: center;
}

.kivi-empty-state-icon {
  color: var(--gray);
  margin-bottom: 1rem;
  opacity: 0.4;
}

.kivi-empty-state-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.kivi-empty-state-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
  max-width: 20rem;
}

.kivi-empty-state-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius);
  border: 1px solid rgba(229, 231, 235, 1);
  background-color: white;
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.2s ease;
}

.kivi-empty-state-btn:hover {
  background-color: rgba(243, 244, 246, 1);
  border-color: rgba(209, 213, 219, 1);
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .kivi-grid {
    grid-template-columns: 1fr;
  }

  .kivi-filter-buttons {
    flex-wrap: wrap;
  }
}
</style>