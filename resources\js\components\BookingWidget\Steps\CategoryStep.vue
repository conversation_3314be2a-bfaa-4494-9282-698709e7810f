<template>
  <div class="kivi-booking-step" id="step-category">
    <h2 class="kivi-step-title"></h2>
    <p class="kivi-step-subtitle"></p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader"></div>
    </div>

    <template v-else>
      <div class="kivi-form-group kivi-search-input">
        <div class="kivi-search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          class="kivi-form-input"
          id="category-search"
          placeholder="Search categories..."
          v-model="searchTerm"
        >
      </div>

      <div class="kivi-grid" id="category-list">
        <div
          v-for="category in filteredCategories"
          :key="category.category_id || category.category_value"
          class="kivi-card category-card"
          :class="{ 'selected': selectedCategoryId === (category.category_id || category.category_value) }"
          @click="selectCategory(category)"
        >
          <div class="kivi-card-header">
            <div>
              <h3 class="kivi-card-title">{{ category.category_name || category.name || formatCategoryName(category.category_value) }}</h3>
              <div class="kivi-card-subtitle">{{ category.service_count || '?' }} services available</div>
            </div>
          </div>
          <div class="kivi-card-footer">
            <span class="kivi-badge kivi-badge-purple">In-clinic</span>
            <span v-if="hasVirtualServices(category)" class="kivi-badge kivi-badge-green">Virtual</span>
          </div>
        </div>
      </div>

      <div v-if="filteredCategories.length === 0 && !isLoading" class="kivi-empty-state">
        <p>No categories found. Please try another search term or select a different clinic.</p>
      </div>
    </template>
  </div>
</template>

<script>
import { apiCall } from '../../../config/request';

export default {
  name: 'CategoryStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      categories: [],
      searchTerm: '',
      selectedCategoryId: null,
      isLoading: false,
      virtualCategoryIds: [],
      currentFetchingClinicId: null // Track which clinic we're currently fetching for
    };
  },
  computed: {
    filteredCategories() {
      if (!this.searchTerm) {
        return this.categories;
      }

      const term = this.searchTerm.toLowerCase();
      return this.categories.filter(category => {
        const categoryName = category.category_name || category.name || this.formatCategoryName(category.category_value);
        return categoryName.toLowerCase().includes(term);
      });
    }
  },
  watch: {
    'bookingData.clinic.id': {
      immediate: true,
      handler(newClinicId, oldClinicId) {
        if (newClinicId && newClinicId !== oldClinicId) {
          this.fetchCategories(newClinicId);
        }
      }
    }
  },

  mounted() {
    // The watcher with immediate: true will handle the initial fetch
    // so we don't need to call fetchCategories here to avoid duplicate calls
  },
  methods: {

    async fetchCategories(clinicId) {
      if (!clinicId) return;

      // Prevent multiple simultaneous calls for the same clinic
      if (this.currentFetchingClinicId === clinicId) {
        console.log('Already fetching categories for clinic', clinicId);
        return;
      }

      try {
        this.currentFetchingClinicId = clinicId;
        this.isLoading = true;
        this.categories = [];
        this.selectedCategoryId = null;

        const response = await apiCall.get('get_clinic_service_category', {
          params: {
            clinic_id: clinicId,
            format: 'json'
          }
        });

        if (response.data?.status && Array.isArray(response.data.data)) {
          this.categories = response.data.data.map(category => ({
            category_id: category.category_id || category.id,
            category_name: category.category_name || category.name,
            category_slug: category.category_slug || category.slug,
            category_visibility: category.category_visibility || category.visibility,
            service_count: category.service_count || 0,
            category_value: category.category_id || category.id || category.category_value,
            name: category.category_name || category.name || this.formatCategoryName(category.category_value || category.id)
          }));

          // Auto-select if only one category
          if (this.categories.length === 1) {
            this.selectCategory(this.categories[0]);
          }

          // Restore previous selection if exists
          if (this.bookingData.category?.id) {
            const existingCategory = this.categories.find(
              c => c.category_value === this.bookingData.category.id
            );
            if (existingCategory) {
              this.selectedCategoryId = existingCategory.category_value;
            }
          }

          await this.fetchVirtualServiceData(clinicId);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        this.isLoading = false;
        this.currentFetchingClinicId = null;
      }
    },

    async fetchVirtualServiceData(clinicId) {
      try {
        const response = await apiCall.get('get_clinic_service', {
          params: {
            clinic_id: clinicId,
            format: 'json'
          }
        });

        if (response.data?.status && Array.isArray(response.data.data)) {
          const virtualCategories = new Set();

          response.data.data.forEach(service => {
            if (service.telemed_service === 'yes' || service.type === 'virtual') {
              const categoryValue = service.category_id || service.service_name_alias || service.type;
              if (categoryValue) {
                virtualCategories.add(categoryValue);
              }
            }
          });

          this.virtualCategoryIds = Array.from(virtualCategories);
        }
      } catch (error) {
        console.error('Error fetching services:', error);
      }
    },

    formatCategoryName(categoryValue) {
      if (!categoryValue) return '';

      // Replace underscores with spaces and capitalize each word
      return categoryValue
        .replace(/_/g, ' ')
        .replace(/\b\w/g, char => char.toUpperCase());
    },

    hasVirtualServices(category) {
      const categoryId = category.category_id || category.category_value;
      return this.virtualCategoryIds.includes(categoryId);
    },

    selectCategory(category) {
      const categoryId = category.category_id || category.category_value;
      const categoryName = category.category_name || category.name || this.formatCategoryName(category.category_value);

      this.selectedCategoryId = categoryId;

      const updateData = {
        ...this.bookingData,
        category: {
          id: categoryId,
          name: categoryName,
          slug: category.category_slug,
          visibility: category.category_visibility
        }
      };

      // Only clear services if we don't have URL parameters with service_id
      if (!this.bookingData.urlParams?.serviceId || !this.bookingData.services?.length) {
        updateData.services = [];
      }

      this.$emit('update:booking-data', updateData);
    }
  }
};
</script>

<style scoped>
.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-form-group {
  margin-bottom: 1rem;
}

.kivi-search-input {
  position: relative;
}

.kivi-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.kivi-search-input input {
  padding-left: 2.5rem;
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.kivi-card {
  border: 2px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.kivi-card:hover {
  border-color: rgba(79, 70, 229, 0.4);
  background-color: rgba(79, 70, 229, 0.02);
}

.kivi-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
}

.kivi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.kivi-card-title {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.kivi-card-subtitle {
  font-size: 0.75rem;
  color: var(--gray);
}

.kivi-card-footer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.kivi-badge-purple {
  background-color: rgba(124, 58, 237, 0.1);
  color: rgba(109, 40, 217, 1);
}

.kivi-badge-green {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgba(5, 150, 105, 1);
}

.kivi-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.kivi-loader {
  border: 3px solid rgba(229, 231, 235, 1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2rem;
  height: 2rem;
  animation: kivi-spin 1s linear infinite;
}

.kivi-empty-state {
  text-align: center;
  padding: 2rem 0;
  color: var(--gray);
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .kivi-grid {
    grid-template-columns: 1fr;
  }
}
</style>