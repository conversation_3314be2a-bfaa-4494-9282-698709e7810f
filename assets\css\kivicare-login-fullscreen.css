/* Ki<PERSON><PERSON> Login Fullscreen CSS - Aggressive Theme Element Removal */

/* General reset for login pages */
body.kivicare-fullscreen-page {
    overflow-x: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
    min-height: 100vh !important;
}

/* More aggressive targeting of theme elements */
body.kivicare-fullscreen-page header,
body.kivicare-fullscreen-page footer,
body.kivicare-fullscreen-page .site-header,
body.kivicare-fullscreen-page .site-footer,
body.kivicare-fullscreen-page .header,
body.kivicare-fullscreen-page .footer,
body.kivicare-fullscreen-page nav,
body.kivicare-fullscreen-page aside,
body.kivicare-fullscreen-page .widget-area,
body.kivicare-fullscreen-page .sidebar,
body.kivicare-fullscreen-page .navigation,
body.kivicare-fullscreen-page .menu,
body.kivicare-fullscreen-page .main-navigation,
body.kivicare-fullscreen-page #masthead,
body.kivicare-fullscreen-page #colophon,
body.kivicare-fullscreen-page .site-info,
body.kivicare-fullscreen-page .site-branding,
body.kivicare-fullscreen-page .breadcrumbs,
body.kivicare-fullscreen-page .entry-header,
body.kivicare-fullscreen-page .entry-footer,
body.kivicare-fullscreen-page .post-navigation,
body.kivicare-fullscreen-page .comments-area,
body.kivicare-fullscreen-page .site-navigation,
body.kivicare-fullscreen-page #header,
body.kivicare-fullscreen-page #footer,
body.kivicare-fullscreen-page #site-header,
body.kivicare-fullscreen-page #site-footer,
body.kivicare-fullscreen-page .navbar,
body.kivicare-fullscreen-page .topbar,
body.kivicare-fullscreen-page [class*="header-"],
body.kivicare-fullscreen-page [class*="footer-"],
body.kivicare-fullscreen-page [class*="main-header"],
body.kivicare-fullscreen-page [class*="main-footer"],
body.kivicare-fullscreen-page [class*="site-header"],
body.kivicare-fullscreen-page [class*="site-footer"],
body.kivicare-fullscreen-page [class*="-header-"],
body.kivicare-fullscreen-page [class*="-footer-"],
body.kivicare-fullscreen-page .comment-respond,
body.kivicare-fullscreen-page .social-navigation,
body.kivicare-fullscreen-page .bottom-header-wrapper,
body.kivicare-fullscreen-page .top-header-wrapper,
body.kivicare-fullscreen-page .footer-widget-area,
body.kivicare-fullscreen-page .site-info-wrapper,
body.kivicare-fullscreen-page [id*="header"],
body.kivicare-fullscreen-page [id*="footer"],
body.kivicare-fullscreen-page [id*="sidebar"],
body.kivicare-fullscreen-page [id*="menu"],
body.kivicare-fullscreen-page [id*="navigation"] {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -999 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
}

/* Ensure content containers are full width */
body.kivicare-fullscreen-page #page,
body.kivicare-fullscreen-page .site,
body.kivicare-fullscreen-page #content,
body.kivicare-fullscreen-page .site-content,
body.kivicare-fullscreen-page .content-area,
body.kivicare-fullscreen-page .entry-content,
body.kivicare-fullscreen-page .content,
body.kivicare-fullscreen-page main,
body.kivicare-fullscreen-page .main,
body.kivicare-fullscreen-page .page,
body.kivicare-fullscreen-page .hentry,
body.kivicare-fullscreen-page .site-main,
body.kivicare-fullscreen-page article,
body.kivicare-fullscreen-page #main,
body.kivicare-fullscreen-page [class*="content-"],
body.kivicare-fullscreen-page [class*="page-"],
body.kivicare-fullscreen-page [class*="main-"] {
    width: 100% !important;
    max-width: 100% !important;
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    float: none !important;
}

/* Hide admin bar completely */
body.kivicare-fullscreen-page {
    margin-top: 0 !important;
}

body.kivicare-fullscreen-page #wpadminbar {
    display: none !important;
}

html.kivicare-fullscreen-page {
    margin-top: 0 !important;
}

/* Ensure WordPress login styles don't interfere */
.wp-block-kivi-care-register-login {
    min-height: 100vh !important;
    display: flex !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Override any container fixed width */
body.kivicare-fullscreen-page .container,
body.kivicare-fullscreen-page .wp-site-blocks,
body.kivicare-fullscreen-page .wrap,
body.kivicare-fullscreen-page [class*="container-"],
body.kivicare-fullscreen-page [class*="wrapper-"] {
    width: 100% !important;
    max-width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Show only shortcode content */
body.kivicare-fullscreen-page * {
    box-sizing: border-box !important;
}

body.kivicare-fullscreen-page .wp-block-kivi-care-register-login,
body.kivicare-fullscreen-page .wp-block-shortcode {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    min-height: 100vh !important;
}

/* Animation and transition blocking to prevent theme effects */
body.kivicare-fullscreen-page * {
    transition: none !important;
    animation: none !important;
}