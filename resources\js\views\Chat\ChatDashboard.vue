<template>
  <div class="h-screen flex flex-col bg-white">
    <!-- Chat <PERSON>er -->
    <div class="border-b border-gray-200 px-6 py-4 flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-800">Chat</h1>

      <!-- Right side actions -->
      <div class="flex items-center gap-3" v-if="(getUserRole() !== 'patient')">
        <!-- New Group Chat Button -->
        <button v-if="canCreateGroupChat" @click="showCreateGroupModal = true"
          class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
          <i class="fas fa-plus-circle mr-2"></i>
          New Group
        </button>

        <!-- New Chat Button -->
        <button @click="showNewChatModal = true"
          class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
          <i class="fas fa-plus-circle mr-2"></i>
          New Chat
        </button>

        <!-- Settings Button -->
        <button v-if="getUserRole() == 'clinic_admin' || getUserRole() == 'administrator'"
          @click="showSettingsModal = true"
          class="p-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg transition-colors duration-200">
          <i class="fas fa-cog"></i>
        </button>
      </div>
    </div>

    <!-- Main Chat Area -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Conversation List -->
      <div class="w-80 border-r border-gray-200 flex flex-col bg-gray-50">
        <!-- Search Box -->
        <div class="p-4 border-b border-gray-200 bg-white">
          <div class="relative">
            <input type="text" v-model="searchQuery" placeholder="Search conversations..."
              class="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:border-pink-500 focus:ring-1 focus:ring-pink-500" />
            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            <button v-if="searchQuery" @click="searchQuery = ''"
              class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Conversations List -->
        <div class="flex-1 overflow-y-auto">
          <div v-if="loading" class="p-4 text-center">
            <div class="animate-spin mx-auto h-8 w-8 border-4 border-gray-300 border-t-pink-500 rounded-full"></div>
            <p class="mt-2 text-gray-500">Loading conversations...</p>
          </div>

          <div v-else-if="filteredConversations.length === 0" class="p-6 text-center">
            <div class="text-gray-400 mb-2">
              <i class="fas fa-comments text-5xl"></i>
            </div>
            <p class="text-gray-500 mb-4">No conversations found</p>
            <button @click="showNewChatModal = true"
              class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg text-sm font-medium">
              Start a new chat
            </button>
          </div>

          <div v-else>
            <button v-for="conversation in filteredConversations" :key="conversation.id"
              @click="selectConversation(conversation)"
              class="w-full px-4 py-3 flex items-start border-b border-gray-200 hover:bg-white transition-colors duration-200"
              :class="{ 'bg-white': activeConversation && activeConversation.id === conversation.id }">

              <!-- Avatar/Group Icon -->
              <div
                class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 flex-shrink-0">
                <i :class="conversation.type === 'direct' ? 'fas fa-user' : 'fas fa-users'"></i>
              </div>

              <!-- Conversation Info -->
              <div class="ml-3 flex-1 min-w-0">
                <div class="flex justify-between items-center mb-1">
                  <h3 class="font-medium text-gray-900 truncate">
                    {{ getConversationName(conversation) }}
                  </h3>
                  <span class="text-xs text-gray-500">
                    {{ formatTime(conversation.last_activity) }}
                  </span>
                </div>

                <div class="flex justify-between items-center">
                  <p class="text-sm text-gray-600 truncate">
                    {{ getLastMessage(conversation) }}
                  </p>

                  <!-- Unread badge -->
                  <span v-if="conversation.unread_count > 0"
                    class="flex-shrink-0 w-5 h-5 bg-pink-500 text-white text-xs rounded-full flex items-center justify-center">
                    {{ conversation.unread_count > 9 ? '9+' : conversation.unread_count }}
                  </span>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- Chat Messages Area -->
      <div class="flex-1 flex flex-col">
        <div v-if="!activeConversation" class="flex-1 flex flex-col items-center justify-center p-6 bg-gray-50">
          <div class="text-gray-400 mb-4">
            <i class="fas fa-comments text-6xl"></i>
          </div>
          <h2 class="text-2xl font-medium text-gray-700 mb-2">Select a conversation</h2>
          <p class="text-gray-500 text-center mb-6">Choose an existing conversation or start a new one</p>
          <button @click="showNewChatModal = true"
            class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg font-medium">
            Start a new chat
          </button>
        </div>

        <template v-else>
          <!-- Chat Header -->
          <div class="p-4 border-b border-gray-200 flex justify-between items-center bg-white">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-700">
                <i :class="activeConversation.type === 'direct' ? 'fas fa-user' : 'fas fa-users'"></i>
              </div>
              <div class="ml-3">
                <h3 class="font-medium text-gray-900">{{ getConversationName(activeConversation) }}</h3>
                <p v-if="activeConversation.type === 'group'" class="text-xs text-gray-500">
                  {{ activeConversation.members.length }} members
                </p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-3">
              <button v-if="activeConversation.type === 'group'" @click="showManageGroupModal = true"
                class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full">
                <i class="fas fa-user-cog"></i>
              </button>
              <button v-if="activeConversation.type === 'group' && isConversationCreator"
                @click="showLeaveGroupModal = true"
                class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full">
                <i class="fas fa-sign-out-alt"></i>
              </button>
            </div>
          </div>

          <!-- Messages List -->
          <div class="flex-1 overflow-y-auto p-4 space-y-4" ref="messagesContainer">
            <div v-if="messagesLoading" class="flex justify-center items-center h-full">
              <div class="animate-spin h-8 w-8 border-4 border-gray-300 border-t-pink-500 rounded-full"></div>
            </div>

            <template v-else>
              <div v-for="(message, index) in messages" :key="message.id" class="flex"
                :class="message.user_id === currentUserId ? 'justify-end' : 'justify-start'">

                <!-- Date separator if needed -->
                <div v-if="shouldShowDate(message, index)" class="w-full text-center my-4">
                  <div class="inline-block px-3 py-1 bg-gray-200 rounded-full text-xs text-gray-600">
                    {{ formatMessageDate(message.created_at) }}
                  </div>
                </div>

                <!-- Message bubble -->
                <div class="max-w-[70%] px-4 py-3 rounded-lg shadow-sm"
                  :class="message.user_id === currentUserId ? 'bg-pink-100 text-gray-900' : 'bg-gray-100 text-gray-900'">

                  <!-- Sender name (for group chats) -->
                  <div v-if="activeConversation.type === 'group' && message.user_id !== currentUserId"
                    class="text-xs text-pink-600 font-medium mb-1">
                    {{ message.sender_name }}
                  </div>

                  <!-- File attachment if any -->
                  <div v-if="message.file_url" class="mb-2">
                    <!-- Image -->
                    <img v-if="isImage(message.file_type)" :src="message.file_url"
                      class="max-w-full rounded-lg max-h-60 cursor-pointer" @click="openAttachment(message.file_url)"
                      alt="Image attachment" />

                    <!-- PDF Document -->
                    <div v-else-if="isPDF(message.file_type)"
                      class="flex items-center p-3 bg-white rounded-lg border border-gray-200">
                      <i class="far fa-file-pdf text-2xl text-red-500 mr-3"></i>
                      <div class="flex-grow min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">PDF Document</p>
                        <p class="text-xs text-gray-500">Click to open</p>
                      </div>
                      <button @click="openAttachment(message.file_url)"
                        class="ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                        <i class="fas fa-external-link-alt"></i>
                      </button>
                    </div>

                    <!-- Other file types -->
                    <div v-else class="flex items-center p-3 bg-white rounded-lg border border-gray-200">
                      <i class="far fa-file text-2xl text-blue-500 mr-3"></i>
                      <div class="flex-grow min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">File attachment</p>
                        <p class="text-xs text-gray-500">Click to download</p>
                      </div>
                      <button @click="openAttachment(message.file_url)"
                        class="ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                        <i class="fas fa-download"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Message text -->
                  <p class="whitespace-pre-wrap break-words">{{ message.message }}</p>

                  <!-- Message time -->
                  <div class="text-xs text-gray-500 text-right mt-1">
                    {{ formatMessageTime(message.created_at) }}
                  </div>
                </div>
              </div>

              <!-- No messages placeholder -->
              <div v-if="messages.length === 0" class="flex flex-col items-center justify-center h-full p-6">
                <div class="text-gray-400 mb-4">
                  <i class="fas fa-comments text-5xl"></i>
                </div>
                <p class="text-gray-500 text-center">No messages yet. Send your first message to start the conversation.
                </p>
              </div>
            </template>
          </div>

          <!-- Message Input -->
          <div class="p-4 border-t border-gray-200 bg-white">
            <div class="flex items-end gap-2">
              <!-- File Attachment Button -->
              <label
                class="p-3 text-gray-500 hover:text-pink-600 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors duration-200">
                <input type="file" class="hidden" @change="handleFileUpload" />
                <i class="fas fa-paperclip text-lg"></i>
              </label>

              <!-- Text Input -->
              <div class="flex-1 border border-gray-200 rounded-lg overflow-hidden bg-gray-50">
                <textarea v-model="newMessage" @keydown.enter.exact.prevent="sendMessage"
                  placeholder="Type a message..." rows="1"
                  class="w-full p-3 bg-transparent focus:outline-none resize-none" ref="messageInput"></textarea>

                <!-- Preview of attached file if any -->
                <div v-if="fileToUpload"
                  class="p-3 bg-gray-100 border-t border-gray-200 flex justify-between items-center">
                  <div class="flex items-center">
                    <i :class="getFileIcon(fileToUpload.type)" class="mr-2 text-lg"></i>
                    <span class="text-sm truncate max-w-md">{{ fileToUpload.name }}</span>
                  </div>
                  <button @click="fileToUpload = null"
                    class="p-1 text-gray-500 hover:text-red-600 hover:bg-gray-200 rounded-full">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>

              <!-- Send Button -->
              <button @click="sendMessage" :disabled="!canSendMessage"
                :class="{ 'bg-pink-600 hover:bg-pink-700': canSendMessage, 'bg-gray-300 cursor-not-allowed': !canSendMessage }"
                class="p-3 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- New Chat Modal -->
    <div v-if="showNewChatModal"
      class="fixed inset-0 z-10 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-xl font-medium text-gray-900">New Conversation</h3>
          <button @click="showNewChatModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="p-4">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Select User</label>
            <div class="relative">
              <input type="text" v-model="userSearchQuery" placeholder="Search by name or email..."
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-pink-500 focus:border-pink-500"
                @input="filterUsers" />

              <div v-if="!selectedUser && userSearchQuery && filteredUsers.length > 0"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm">
                <p class="px-4 py-2 text-xs text-gray-500 bg-gray-50 font-medium">Search results ({{
                  filteredUsers.length
                }})</p>
                <button v-for="user in filteredUsers" :key="user.ID || user.id" @click.prevent.stop="selectUser(user)"
                  type="button" class="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center">
                  <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mr-3">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="flex-grow min-w-0">
                    <p class="font-medium text-gray-900 truncate">
                      {{ user.data ? user.data.display_name : user.display_name }}
                    </p>
                    <div class="flex items-center">
                      <span class="text-xs text-gray-500 truncate mr-2">
                        {{ user.data ? user.data.user_email : user.user_email }}
                      </span>
                      <span class="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
                        {{ getUserRoleLabel(user.roles && user.roles.length ? user.roles[0] : '') }}
                      </span>
                    </div>
                  </div>
                </button>
              </div>

              <div v-if="!selectedUser && !userSearchQuery && users.length > 0"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm">
                <p class="px-4 py-2 text-xs text-gray-500 bg-gray-50 font-medium">Available users ({{ users.length }})
                </p>
                <button v-for="user in users.slice(0, 10)" :key="user.ID || user.id"
                  @click.prevent.stop="selectUser(user)" type="button"
                  class="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center">
                  <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mr-3">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="flex-grow min-w-0">
                    <p class="font-medium text-gray-900 truncate">
                      {{ user.data ? user.data.display_name : user.display_name }}
                    </p>
                    <div class="flex items-center">
                      <span class="text-xs text-gray-500 truncate mr-2">
                        {{ user.data ? user.data.user_email : user.user_email }}
                      </span>
                      <span class="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
                        {{ getUserRoleLabel(user.roles && user.roles.length ? user.roles[0] : '') }}
                      </span>
                    </div>
                  </div>
                </button>
              </div>

              <div v-else-if="userSearchQuery && filteredUsers.length === 0 && !loadingUsers"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-6 text-center">
                <p class="text-gray-500">No users matching '{{ userSearchQuery }}'</p>
              </div>

              <div v-else-if="users.length === 0 && !loadingUsers"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-6 text-center">
                <p class="text-gray-500">No users available for chat</p>
              </div>

              <div v-if="loadingUsers" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-6 text-center">
                <div class="animate-spin h-6 w-6 border-4 border-gray-300 border-t-pink-500 rounded-full mx-auto mb-2">
                </div>
                <p class="text-gray-500">Loading available users...</p>
              </div>
            </div>
          </div>

          <div v-if="selectedUser" class="p-3 bg-gray-50 rounded-lg flex justify-between items-center mb-4">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mr-3">
                <i class="fas fa-user"></i>
              </div>
              <div>
                <p class="font-medium text-gray-900">{{ selectedUser.display_name }}</p>
                <p class="text-xs text-gray-500">{{ selectedUser.user_email }}</p>
              </div>
            </div>
            <button @click="selectedUser = null"
              class="text-gray-500 hover:text-gray-700 p-1 hover:bg-gray-200 rounded-full">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="flex justify-end mt-6">
            <button @click="showNewChatModal = false"
              class="mr-3 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg">
              Cancel
            </button>
            <button @click="createConversation" :disabled="!selectedUser || creatingConversation"
              :class="{ 'bg-pink-600 hover:bg-pink-700': selectedUser && !creatingConversation, 'bg-gray-300 cursor-not-allowed': !selectedUser || creatingConversation }"
              class="px-4 py-2 text-white rounded-lg new-chat-modal-start-chat-button">
              <span v-if="creatingConversation">
                <i class="fas fa-spinner fa-spin mr-2"></i>Creating...
              </span>
              <span v-else>Start Chat</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Modal -->
    <div v-if="showSettingsModal"
      class="fixed inset-0 z-10 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-xl font-medium text-gray-900">Chat Settings</h3>
          <button @click="showSettingsModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="p-4">
          <div class="mb-4" v-if="getUserRole() == 'administrator'">
            <label class="block text-sm font-medium text-gray-700 mb-1">Select Clinic</label>
            <select v-model="selectedClinicId"
              class="w-full p-2 border border-gray-300 rounded-lg focus:ring-pink-500 focus:border-pink-500">
              <option v-for="clinic in clinicOptions" :key="clinic.id" :value="clinic.id">
                {{ clinic.label }}
              </option>
            </select>
          </div>

          <div class="mb-6">
            <div class="flex items-center justify-between py-2">
              <div>
                <h4 class="font-medium text-gray-900">Allow patient-doctor direct chats</h4>
                <p class="text-sm text-gray-500">If enabled, patients can directly message doctors</p>
              </div>
              <div class="relative inline-block w-14 align-middle select-none">
                <input type="checkbox" v-model="allowDoctorPatientChat" id="toggleDoctorPatientChat"
                  class="toggle-checkbox absolute block w-8 h-8 mr-8 rounded-full bg-white border-4 appearance-none cursor-pointer" />
                <label for="toggleDoctorPatientChat"
                  class="toggle-label block overflow-hidden h-8 rounded-full bg-gray-300 cursor-pointer"></label>
              </div>
            </div>
          </div>

          <div class="flex justify-end">
            <button @click="showSettingsModal = false"
              class="mr-3 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg">
              Cancel
            </button>
            <button @click="saveSettings" :disabled="savingSettings"
              :class="{ 'bg-pink-600 hover:bg-pink-700': !savingSettings, 'bg-pink-400 cursor-not-allowed': savingSettings }"
              class="px-4 py-2 text-white rounded-lg">
              <span v-if="savingSettings">
                <i class="fas fa-spinner fa-spin mr-2"></i>Saving...
              </span>
              <span v-else>Save Changes</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Other modals remain the same... -->
  </div>
</template>

<script>
import { post, get } from '../../config/request';
import NotificationSound from '../../utils/NotificationSound';

export default {
  name: 'ChatDashboard',
  data() {
    return {
      // Conversations
      conversations: [],
      activeConversation: null,
      loading: true,
      searchQuery: '',

      // Messages - explicitly initialize as empty array to avoid type issues
      messages: Array.isArray(this.messages) ? this.messages : [],
      messagesLoading: false,
      newMessage: '',
      fileToUpload: null,

      // Message tracking to prevent duplicates and flickering
      pendingMessages: new Map(), // Track messages waiting for server confirmation
      recentlyAddedServerMessages: new Set(), // Track server message IDs we've recently added
      lastPollingRequestId: null, // Track the most recent polling request

      // User data
      currentUserId: null,
      users: [],
      filteredUsers: [],
      loadingUsers: false,
      userSearchQuery: '',
      selectedUser: null,

      // Modals
      showNewChatModal: false,
      showCreateGroupModal: false,
      showSettingsModal: false,
      showManageGroupModal: false,
      showLeaveGroupModal: false,

      // Group chat
      groupName: '',
      groupNameError: '',
      groupMembers: [],
      editGroupName: '',

      // Settings
      clinicOptions: [],
      selectedClinicId: null,
      allowDoctorPatientChat: false,

      // Loading states
      creatingConversation: false,
      updatingGroup: false,
      savingSettings: false,
      leavingGroup: false,
    };
  },
  methods: {
    getUserId() {
      return this.$store.state.userDataModule.user.ID || null;
    },

    loadConversations(showLoading = true) {
      if (showLoading) {
        this.loading = true;
      }

      get('get_conversations', {})
        .then(response => {
          if (response.data.status) {
            // Process the received conversations
            const conversations = response.data.data || [];

            // Make sure unread_count is a number, not a string
            conversations.forEach(conv => {
              // Ensure unread_count is a number
              if (conv.unread_count !== undefined) {
                conv.unread_count = parseInt(conv.unread_count, 10) || 0;
              } else {
                conv.unread_count = 0;
              }

              // Log unread counts for debugging
              if (conv.unread_count > 0) {
                console.log(`Conversation ${conv.id} has ${conv.unread_count} unread messages`);
              }
            });

            this.conversations = conversations;

            // If we have an active conversation, refresh its data
            if (this.activeConversation) {
              const updatedConversation = this.conversations.find(c => c.id === this.activeConversation.id);
              if (updatedConversation) {
                this.activeConversation = updatedConversation;

                // If this is the active conversation, mark messages as read
                // This ensures unread count is 0 for the active conversation
                if (updatedConversation.unread_count > 0) {
                  post('mark_as_read', {
                    conversation_id: updatedConversation.id
                  }).then(markResponse => {
                    console.log('Marked messages as read response:', markResponse.data);
                    // Update the unread count locally too
                    updatedConversation.unread_count = 0;
                  }).catch(err => {
                    console.error('Error marking messages as read:', err);
                  });
                }
              }
            }
          } else {
            console.error('Error loading conversations:', response.data.message);
          }
        })
        .catch(error => {
          console.error('Error loading conversations:', error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // Other methods...

    checkForNewMessages() {
      if (!this.activeConversation) return;

      // Ensure messages is an array
      if (!Array.isArray(this.messages)) {
        console.error('Messages is not an array in checkForNewMessages');
        this.messages = [];
      }

      // Initialize tracking data structures if needed
      if (!this.recentlyAddedServerMessages) {
        this.recentlyAddedServerMessages = new Set();
      }

      // Get the ID of the last real (non-temporary) message we have
      let lastMessageId = 0;
      const realMessages = this.messages.filter(m => !m.isTemporary);
      if (realMessages.length > 0) {
        const lastMessage = realMessages[realMessages.length - 1];
        lastMessageId = lastMessage.id || 0;
      }

      // Skip polling if we just sent a message and are waiting for it to process
      // This helps avoid the flicker issue
      const hasRecentPendingMessages = Array.from(this.pendingMessages.values()).some(
        data => Date.now() - data.lastChecked < 5000 && !data.processed
      );

      if (hasRecentPendingMessages) {
        console.log('Skipping polling as we have recent pending messages');
        return;
      }

      // Create a unique request ID for this polling operation
      const pollingRequestId = 'poll-' + Date.now();
      this.lastPollingRequestId = pollingRequestId;

      get('get_messages', {
        conversation_id: this.activeConversation.id
      })
        .then(response => {
          // If another polling request has started since this one, discard these results
          if (this.lastPollingRequestId !== pollingRequestId) {
            console.log('Discarding stale polling results from request', pollingRequestId);
            return;
          }

          if (response.data.status) {
            let serverMessages = [];

            // Handle the new response format
            if (response.data.data && response.data.data.messages) {
              serverMessages = response.data.data.messages;

              // Update conversation and members if available
              if (response.data.data.conversation) {
                this.activeConversation = {
                  ...this.activeConversation,
                  ...response.data.data.conversation
                };
              }

              if (response.data.data.members) {
                this.activeConversation.members = response.data.data.members;
              }
            } else if (Array.isArray(response.data.data)) {
              // Legacy format
              serverMessages = response.data.data;
            }

            // Ensure we have an array of server messages
            if (!Array.isArray(serverMessages)) {
              serverMessages = [];
            }

            // Process the messages from the server
            try {
              // Create a map of existing messages by ID for quick lookup
              const existingMessagesById = new Map();
              this.messages.forEach(msg => {
                if (msg && msg.id) {
                  // Use the msg ID as the key, and store the whole message
                  existingMessagesById.set(msg.id.toString(), msg);
                }
              });

              // Create a deduplicated array of server messages
              // Prefer server messages over existing messages
              const uniqueMessages = new Map();

              // First add all server messages to the map
              serverMessages.forEach(serverMsg => {
                if (serverMsg && serverMsg.id) {
                  const msgId = serverMsg.id.toString();

                  // Skip if we've recently added this message through the direct API response
                  if (this.recentlyAddedServerMessages.has(msgId)) {
                    console.log(`Skipping recently added server message: ${msgId}`);
                    return;
                  }

                  // Add to our unique messages map
                  uniqueMessages.set(msgId, serverMsg);
                }
              });

              // Then identify any temporary messages that correspond to server messages
              const temporaryMessages = this.messages.filter(m => m.isTemporary);
              const processedTempIds = new Set();

              temporaryMessages.forEach(tempMsg => {
                if (this.pendingMessages.has(tempMsg.id)) {
                  const pendingData = this.pendingMessages.get(tempMsg.id);

                  // If this temporary message was already processed, we can skip it
                  if (pendingData.processed && pendingData.serverMessageId) {
                    processedTempIds.add(tempMsg.id);
                    return;
                  }

                  // Look for a matching real message from the server
                  const matchingServerMsg = serverMessages.find(serverMsg =>
                    serverMsg.user_id === tempMsg.user_id &&
                    serverMsg.message === tempMsg.message &&
                    Math.abs(new Date(serverMsg.created_at) - new Date(tempMsg.created_at)) < 10000 // Within 10 seconds
                  );

                  if (matchingServerMsg) {
                    console.log(`Found match for temp message ${tempMsg.id}: ${matchingServerMsg.id}`);

                    // Mark this temp message as processed and store the server ID
                    pendingData.processed = true;
                    pendingData.serverMessageId = matchingServerMsg.id;
                    this.pendingMessages.set(tempMsg.id, pendingData);

                    // Add this to our set of processed temp IDs
                    processedTempIds.add(tempMsg.id);

                    // Remember we've seen this server message
                    this.recentlyAddedServerMessages.add(matchingServerMsg.id.toString());
                  }
                }
              });

              // Now add only the temporary messages that haven't been matched
              temporaryMessages.forEach(tempMsg => {
                if (!processedTempIds.has(tempMsg.id)) {
                  // This is a temp message that hasn't been matched yet
                  uniqueMessages.set(tempMsg.id, tempMsg);
                }
              });

              // Finally, add any existing messages that weren't in the server response
              this.messages.forEach(existingMsg => {
                if (existingMsg && existingMsg.id && !existingMsg.isTemporary) {
                  const msgId = existingMsg.id.toString();

                  // Only add if it wasn't already in the unique messages (not overwriting server values)
                  if (!uniqueMessages.has(msgId)) {
                    uniqueMessages.set(msgId, existingMsg);
                  }
                }
              });

              // Convert to array and sort by creation time
              const uniqueMessagesArray = Array.from(uniqueMessages.values());
              uniqueMessagesArray.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

              // Only update if the message array is actually different
              const messageIdsChanged =
                this.messages.length !== uniqueMessagesArray.length ||
                this.messages.some((msg, index) => msg.id !== uniqueMessagesArray[index].id);

              if (messageIdsChanged) {
                console.log('Messages have changed, updating the view');
                this.messages = uniqueMessagesArray;

                // Always scroll to bottom when messages change
                this.$nextTick(() => {
                  this.scrollToBottom();
                });

                // Check if we added new server messages for sound notification
                const hasNewServerMessages = serverMessages.some(serverMsg =>
                  !existingMessagesById.has(serverMsg.id.toString())
                );

                if (hasNewServerMessages) {
                  // Play notification sound for new messages from others
                  const newMessagesFromOthers = serverMessages.filter(m =>
                    !existingMessagesById.has(m.id.toString()) &&
                    m.user_id !== this.currentUserId
                  );

                  if (newMessagesFromOthers.length > 0) {
                    NotificationSound.play();
                  }
                }
              }

              // Clean up old pending messages
              const now = Date.now();
              this.pendingMessages.forEach((data, id) => {
                // Remove if it's processed or over 30 seconds old
                if (data.processed || now - data.lastChecked > 60000) {
                  this.pendingMessages.delete(id);
                }
              });

              // Clean up old recentlyAddedServerMessages entries
              if (this.recentlyAddedServerMessages.size > 100) {
                // If we have too many entries, just reset it
                this.recentlyAddedServerMessages = new Set();
              }
            } catch (error) {
              console.error('Error processing messages during check:', error);
            }

            // Update conversations list to get updated unread counts
            // Less frequently now to avoid excess API calls
            if (Math.random() < 0.2) { // 20% chance to update conversations list
              this.loadConversations(false);
            }
          }
        })
        .catch(error => {
          console.error('Error checking for new messages:', error);
        });
    },

    createConversation() {
      if (!this.selectedUser || this.creatingConversation) return;

      this.creatingConversation = true;

      console.log('Creating conversation with user:', this.selectedUser);

      // Use the normalized user ID from our structure
      const userID = parseInt(this.selectedUser.id);

      if (!userID) {
        console.error('Invalid user ID:', this.selectedUser);
        this.creatingConversation = false;
        return;
      }

      console.log('Using user ID for conversation:', userID);

      post('create_conversation', {
        members: [userID],
        type: 'direct'
      })
        .then(response => {
          if (response.data.status) {
            // Close modal
            this.showNewChatModal = false;

            // Reset form
            this.selectedUser = null;
            this.userSearchQuery = '';

            // Reload conversations and select the new one
            this.loadConversations(false);

            // Only select if we have a valid conversation object
            if (response.data.data && response.data.data.id) {
              this.selectConversation(response.data.data);
              console.log('Conversation created successfully:', response.data.data);
            } else {
              console.warn('Conversation created but response data is incomplete:', response.data);
            }
          } else {
            console.error('Error creating conversation:', response.data.message);
            alert('Error creating conversation: ' + response.data.message);
          }
        })
        .catch(error => {
          console.error('Error creating conversation:', error);
          alert('Error creating conversation. Please try again later.');
        })
        .finally(() => {
          this.creatingConversation = false;
        });
    },

    filterUsers() {
      if (!this.userSearchQuery.trim()) {
        this.filteredUsers = this.users.slice(0, 10); // Show first 10 users when no search query
        return;
      }

      if (!this.users.length) {
        this.filteredUsers = [];
        return;
      }

      console.log('Filtering users with query:', this.userSearchQuery);
      console.log('Sample user object:', this.users[0]);

      const query = this.userSearchQuery.toLowerCase();
      this.filteredUsers = this.users.filter(user => {
        // Get the display name from correct property path
        let displayName = '';
        let email = '';

        // WordPress User objects might have different structures
        // Try both direct properties and nested data property
        if (user.data && user.data.display_name) {
          displayName = user.data.display_name;
        } else if (user.display_name) {
          displayName = user.display_name;
        }

        if (user.data && user.data.user_email) {
          email = user.data.user_email;
        } else if (user.user_email) {
          email = user.user_email;
        }

        // Match against both display name and email
        return (displayName && displayName.toLowerCase().includes(query)) ||
          (email && email.toLowerCase().includes(query));
      });

      console.log('Filtered users:', this.filteredUsers.length);
    },

    loadUsersForChat() {
      this.loadingUsers = true;

      get('get_users_for_chat', {})
        .then(response => {
          if (response.data.status) {
            this.users = response.data.data;
            // Initialize filtered users with the first 10 users
            this.filteredUsers = this.users.slice(0, 10);
          } else {
            console.error('Error loading users for chat:', response.data.message);
          }
        })
        .catch(error => {
          console.error('Error loading users for chat:', error);
        })
        .finally(() => {
          this.loadingUsers = false;
        });
    },

    selectUser(user) {
      console.log('User selected:', user);

      // Normalize user data structure
      this.selectedUser = {
        id: user.ID || user.id,
        display_name: user.data ? user.data.display_name : user.display_name,
        user_email: user.data ? user.data.user_email : user.user_email
      };

      // Log what we're using for user ID
      console.log('Selected user normalized data:', this.selectedUser);

      // Clear the search UI
      this.userSearchQuery = '';
      this.filteredUsers = [];

      // Force UI update
      this.$nextTick(() => {
        // Focus on "Start Chat" button for easier completion
        const startChatButton = document.querySelector('.new-chat-modal-start-chat-button');
        if (startChatButton) {
          startChatButton.focus();
        }
      });
    },

    selectConversation(conversation) {
      this.activeConversation = conversation;
      this.loadMessages(conversation.id);

      // Update URL without reloading
      const query = { ...this.$route.query, conversation: conversation.id };
      this.$router.replace({ query }).catch(() => { });

      // Also scroll to bottom when conversation is selected
      // This helps when switching between conversations
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // If this conversation has unread messages, mark them as read
      if (conversation.unread_count && conversation.unread_count > 0) {
        // Send request to mark messages as read
        post('mark_as_read', {
          conversation_id: conversation.id
        }).then(response => {
          console.log('Mark as read response:', response.data);

          // Update the unread count in our local data
          conversation.unread_count = 0;

          // Find this conversation in the full list and update it there too
          const index = this.conversations.findIndex(c => c.id === conversation.id);
          if (index !== -1) {
            this.conversations[index].unread_count = 0;
          }
        }).catch(error => {
          console.error('Error marking messages as read:', error);
        });
      }
    },

    loadMessages(conversationId) {
      this.messagesLoading = true;
      this.messages = [];

      get('get_messages', { conversation_id: conversationId })
        .then(response => {
          if (response.data.status) {
            let messagesArray = [];

            // Check the structure of the response
            if (response.data.data && response.data.data.messages) {
              // New response format
              messagesArray = response.data.data.messages;

              // Update active conversation with more details if available
              if (response.data.data.conversation) {
                this.activeConversation = {
                  ...this.activeConversation,
                  ...response.data.data.conversation
                };
              }

              // Update members if available
              if (response.data.data.members) {
                this.activeConversation.members = response.data.data.members;
              }
            } else {
              // Legacy format or flat array of messages
              messagesArray = Array.isArray(response.data.data) ? response.data.data : [];
            }

            // Ensure messages is an array and remove duplicates by message ID
            if (Array.isArray(messagesArray)) {
              // Create a map to deduplicate messages by ID
              const messageMap = new Map();

              messagesArray.forEach(message => {
                if (message && message.id) {
                  // Only add if not already in the map
                  if (!messageMap.has(message.id)) {
                    messageMap.set(message.id, message);
                  }
                }
              });

              // Convert back to array and sort by created_at
              this.messages = Array.from(messageMap.values()).sort((a, b) => {
                return new Date(a.created_at) - new Date(b.created_at);
              });

              console.log(`Processed ${messagesArray.length} messages, reduced to ${this.messages.length} unique messages`);
            } else {
              console.error('Messages data is not an array:', messagesArray);
              this.messages = [];
            }

            // Scroll to bottom after messages load - use double nextTick to ensure rendering is complete
            this.$nextTick(() => {
              this.$nextTick(() => {
                this.scrollToBottom();
              });
            });

            // Load the conversation again to refresh unread count
            this.loadConversations(false);
          } else {
            console.error('Error loading messages:', response.data.message);
          }
        })
        .catch(error => {
          console.error('Error loading messages:', error);
        })
        .finally(() => {
          this.messagesLoading = false;
        });
    },

    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        // Use a small timeout to ensure DOM has fully updated
        setTimeout(() => {
          this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight;
        }, 10);
      }
    },

    getConversationName(conversation) {
      // For direct chats, show the other person's name
      if (conversation.type === 'direct') {
        // Find the other member
        const otherMember = conversation.members?.find(member => member.user_id !== this.currentUserId);
        return otherMember ? otherMember.display_name : 'Chat';
      }

      // For group chats, show the group name
      return conversation.name || 'Group Chat';
    },

    getLastMessage(conversation) {
      if (!conversation.last_message) {
        return 'No messages yet';
      }

      if (conversation.last_message.file_url) {
        // If message has an attachment
        const fileType = this.getFileTypeName(conversation.last_message.file_type);
        return `${conversation.last_message.sender_name}: [${fileType}]${conversation.last_message.message ? ' ' + conversation.last_message.message : ''}`;
      }

      // Regular text message
      return conversation.last_message.sender_name + ': ' + conversation.last_message.message;
    },

    getFileTypeName(fileType) {
      if (!fileType) return 'File';

      if (fileType.startsWith('image/')) {
        return 'Image';
      } else if (fileType === 'application/pdf') {
        return 'PDF';
      } else if (fileType.startsWith('audio/')) {
        return 'Audio';
      } else if (fileType.startsWith('video/')) {
        return 'Video';
      } else {
        return 'File';
      }
    },

    formatTime(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      const now = new Date();
      const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) {
        // Today
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } else if (diffInDays === 1) {
        // Yesterday
        return 'Yesterday';
      } else if (diffInDays < 7) {
        // This week
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        return days[date.getDay()];
      } else {
        // More than a week ago
        return date.toLocaleDateString();
      }
    },

    formatMessageTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },

    formatMessageDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const now = new Date();
      const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) {
        return 'Today';
      } else if (diffInDays === 1) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString(undefined, {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    },

    shouldShowDate(message, index) {
      if (index === 0) return true;

      // Ensure message and previous message have created_at property
      if (!message || !message.created_at || !this.messages[index - 1] || !this.messages[index - 1].created_at) {
        return false;
      }

      const currentDate = new Date(message.created_at).toLocaleDateString();
      const prevDate = new Date(this.messages[index - 1].created_at).toLocaleDateString();

      return currentDate !== prevDate;
    },

    isImage(fileType) {
      return fileType && fileType.startsWith('image/');
    },

    isPDF(fileType) {
      return fileType === 'application/pdf';
    },

    getFileIcon(fileType) {
      if (!fileType) return 'far fa-file';

      if (fileType.startsWith('image/')) {
        return 'far fa-file-image text-green-500';
      } else if (fileType === 'application/pdf') {
        return 'far fa-file-pdf text-red-500';
      } else if (fileType.startsWith('audio/')) {
        return 'far fa-file-audio text-blue-500';
      } else if (fileType.startsWith('video/')) {
        return 'far fa-file-video text-purple-500';
      } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'far fa-file-word text-blue-500';
      } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
        return 'far fa-file-excel text-green-500';
      } else {
        return 'far fa-file text-gray-500';
      }
    },

    openAttachment(url) {
      window.open(url, '_blank');
    },

    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }

      this.fileToUpload = file;

      // Focus on input field
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.focus();
        }
      });
    },

    sendMessage() {
      if (!this.canSendMessage) return;

      // Ensure this.messages is an array
      if (!Array.isArray(this.messages)) {
        console.error('Messages is not an array, initializing it');
        this.messages = [];
      }

      const formData = new FormData();
      formData.append('conversation_id', this.activeConversation.id);
      formData.append('message', this.newMessage);

      if (this.fileToUpload) {
        formData.append('file', this.fileToUpload);
      }

      // Create a unique temp ID with timestamp and random value to avoid duplication
      const tempId = 'temp-' + Date.now() + '-' + Math.floor(Math.random() * 10000);

      // Store the message content and timestamp to help identify it later in polling
      const messageContent = this.newMessage;
      const messageSentTime = new Date().toISOString();

      // Store details about this message to help with identification later
      this.pendingMessages.set(tempId, {
        content: messageContent,
        time: messageSentTime,
        processed: false,
        lastChecked: Date.now(),
        // Used to avoid duplicates during polling
        preventPollingDuplication: true
      });

      // Add optimistic message
      const tempMessage = {
        id: tempId,
        conversation_id: this.activeConversation.id,
        user_id: this.currentUserId,
        message: messageContent,
        file_url: this.fileToUpload ? URL.createObjectURL(this.fileToUpload) : '',
        file_type: this.fileToUpload ? this.fileToUpload.type : '',
        created_at: messageSentTime,
        sender_name: this.$store.state.userDataModule.user.display_name || 'You',
        // Flag this as a temporary message
        isTemporary: true
      };

      try {
        // Add to messages array - create a new array to trigger reactivity
        this.messages = [...this.messages, tempMessage];

        // Scroll to bottom with double nextTick to ensure render completes
        this.$nextTick(() => {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        });
      } catch (error) {
        console.error('Error updating messages array:', error);
      }

      // Clear input
      this.newMessage = '';
      this.fileToUpload = null;

      // Focus on input field
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.focus();
        }
      });

      console.log('Request to send_message:', { message: messageContent, tempId });

      // Send message
      post('send_message', formData, true)
        .then(response => {
          console.log('API response from send_message:', response.data);

          if (response.data.status) {
            try {
              // Get the new message from the response
              // Handle different possible response formats
              let newMessage = null;

              console.log('Send message response structure:', response.data);

              if (response.data.data) {
                if (Array.isArray(response.data.data) && response.data.data.length > 0) {
                  // Handle array response format
                  newMessage = response.data.data[0];
                  console.log('Using first item from array response:', newMessage);
                } else if (typeof response.data.data === 'object' && response.data.data !== null) {
                  // Handle object response format
                  newMessage = response.data.data;
                  console.log('Using object response:', newMessage);
                }
              }

              if (!newMessage || typeof newMessage !== 'object') {
                console.warn('Invalid message format in response:', response.data.data);

                // We still want to mark the pending message as processed 
                // even though we couldn't get proper server data
                if (this.pendingMessages.has(tempId)) {
                  const pendingMsg = this.pendingMessages.get(tempId);
                  pendingMsg.processed = true;
                  this.pendingMessages.set(tempId, pendingMsg);
                }

                // Just keep the temporary message as is - don't try to replace it
                console.log('Keeping temporary message since server data is invalid');
                return;
              }

              // Important: Remember the server message ID to prevent duplication during polling
              if (!this.recentlyAddedServerMessages) {
                this.recentlyAddedServerMessages = new Set();
              }

              // Check if the message has a valid ID
              if (newMessage.id) {
                const messageId = newMessage.id.toString();
                console.log('Adding message ID to recently added set:', messageId);
                this.recentlyAddedServerMessages.add(messageId);

                // After 30 seconds, we can forget about this message to free up memory
                setTimeout(() => {
                  if (this.recentlyAddedServerMessages) {
                    this.recentlyAddedServerMessages.delete(messageId);
                  }
                }, 30000);
              } else {
                console.warn('Message from server does not have an ID:', newMessage);
              }

              // Mark this pending message as processed
              if (this.pendingMessages.has(tempId)) {
                const pendingMsg = this.pendingMessages.get(tempId);
                pendingMsg.processed = true;

                if (newMessage.id) {
                  pendingMsg.serverMessageId = newMessage.id;
                }

                this.pendingMessages.set(tempId, pendingMsg);
              }

              // Only proceed with replacing the message if we have a valid ID
              if (!newMessage.id) {
                console.warn('Cannot replace temporary message - no valid ID in response');
                return;
              }

              // Check for duplicates using exact ID matching first
              const existingMessage = this.messages.find(m =>
                !m.isTemporary && m.id === newMessage.id);

              if (existingMessage) {
                console.log(`Message with ID ${newMessage.id} already exists, removing temporary message`);
                this.messages = this.messages.filter(m => m.id !== tempId);
              } else {
                // Replace temp message with real one
                const index = this.messages.findIndex(m => m.id === tempId);
                if (index !== -1) {
                  console.log(`Replacing temporary message ${tempId} with server message ${newMessage.id}`);
                  const updatedMessages = [...this.messages];
                  updatedMessages[index] = newMessage;
                  this.messages = updatedMessages;
                } else {
                  // This should rarely happen - add the message if the temp wasn't found
                  console.log(`Temporary message ${tempId} not found, adding server message ${newMessage.id}`);
                  this.messages = [...this.messages, newMessage];
                }
              }

              // Update conversations list - do this after a slight delay to avoid race conditions
              setTimeout(() => {
                this.loadConversations(false);
              }, 500);
            } catch (error) {
              console.error('Error updating message after sending:', error);
            }
          } else {
            console.error('Error sending message:', response.data.message);
            // Remove failed message and from pending list
            try {
              this.messages = this.messages.filter(m => m.id !== tempId);
              if (this.pendingMessages.has(tempId)) {
                this.pendingMessages.delete(tempId);
              }
            } catch (error) {
              console.error('Error removing failed message:', error);
            }
          }
        })
        .catch(error => {
          console.error('Error sending message:', error);
          // Remove failed message and from pending list
          try {
            this.messages = this.messages.filter(m => m.id !== tempId);
            if (this.pendingMessages.has(tempId)) {
              this.pendingMessages.delete(tempId);
            }
          } catch (error) {
            console.error('Error removing failed message after error:', error);
          }
        });
    },

    loadChatSettings(clinicId) {
      post('get_chat_settings', { clinic_id: clinicId })
        .then(response => {
          if (response.data.status) {
            this.allowDoctorPatientChat = response.data.data.allow_patient_doctor_chat === 1;
          } else {
            console.error('Error loading chat settings:', response.data.message);
          }
        })
        .catch(error => {
          console.error('Error loading chat settings:', error);
        });
    },

    async getClinics() {
      this.clinicMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_list",
        });

        if (response.data.status) {
          this.clinicOptions = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinics:", error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.common.error_loading_clinics || 'Failed to load clinics'
        });
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    saveSettings() {
      if (this.getUserRole() !== 'administrator') {
        // Get clinic ID based on role
        let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole()) ? this.userData.user_clinic_id : (this.appointmentFormObj?.clinic_id?.id || this.userData.default_clinic_id);
        this.selectedClinicId = clinic_id;
      }

      if (!this.selectedClinicId) {
        alert('Please select a clinic');
        return;
      }

      this.savingSettings = true;

      post('save_chat_settings', {
        clinic_id: this.selectedClinicId,
        allow_patient_doctor_chat: this.allowDoctorPatientChat ? 1 : 0,
      })
        .then(response => {
          if (response.data.status) {
            // Close modal
            this.showSettingsModal = false;
            alert('Chat settings saved successfully');
          } else {
            console.error('Error saving chat settings:', response.data.message);
            alert('Error saving chat settings: ' + response.data.message);
          }
        })
        .catch(error => {
          console.error('Error saving chat settings:', error);
          alert('Error saving chat settings. Please try again later.');
        })
        .finally(() => {
          this.savingSettings = false;
        });
    }
  },
  mounted() {
    // Get current user ID
    this.currentUserId = this.getUserId();

    // Load conversations
    this.loadConversations();

    // Load available users for chat
    this.loadUsersForChat();

    // Load clinics for administrator only
    if (this.getUserRole() === 'administrator') {
      this.getClinics();
    }

    // Check for conversation in URL query
    const conversationId = this.$route.query.conversation;
    console.log('conversationId:', conversationId);
    if (conversationId) {
      this.loadMessages(parseInt(conversationId));
    }

    // Set up polling for new messages less frequently
    this.messagePollingInterval = setInterval(() => {
      if (this.activeConversation) {
        this.checkForNewMessages();
      }
      // Don't reload conversations in the interval - we do this selectively in checkForNewMessages
    }, 15000); // Every 15 seconds instead of 10

    // Clean up on component unmount
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.messagePollingInterval);
    });
  },
  computed: {
    filteredConversations() {
      if (!this.searchQuery) {
        return this.conversations;
      }

      const query = this.searchQuery.toLowerCase();
      return this.conversations.filter(conversation => {
        // Search in conversation name
        const name = this.getConversationName(conversation).toLowerCase();
        if (name.includes(query)) return true;

        // Search in last message
        const lastMessage = this.getLastMessage(conversation).toLowerCase();
        if (lastMessage.includes(query)) return true;

        // Search in members
        if (conversation.members) {
          const memberMatch = conversation.members.some(member =>
            member.display_name.toLowerCase().includes(query)
          );
          if (memberMatch) return true;
        }

        return false;
      });
    },
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
    canSendMessage() {
      return (this.newMessage.trim() !== '' || this.fileToUpload) && this.activeConversation;
    },
    isAdminOrClinicAdmin() {
      const role = this.getUserRole();
      return role === 'administrator' || role === 'clinic_admin';
    },
    canCreateGroupChat() {
      // All roles except patients can create group chats
      const role = this.getUserRole();
      return role !== 'patient';
    },
    isConversationCreator() {
      return this.activeConversation &&
        this.activeConversation.created_by === this.currentUserId;
    },
    canCreateGroup() {
      return this.groupName.trim() !== '' && this.groupMembers.length > 0;
    }
  },
  watch: {
    showNewChatModal(isOpen) {
      if (isOpen) {
        // Reset search and selection
        this.userSearchQuery = '';
        this.selectedUser = null;

        // If users aren't loaded yet, load them
        if (this.users.length === 0) {
          this.loadUsersForChat();
        } else {
          // Just initialize the filtered users from the existing user list
          this.filteredUsers = this.users.slice(0, 10);
        }
      }
    },
    userSearchQuery(val) {
      this.filterUsers();
    },
    selectedClinicId(newId) {
      if (newId) {
        this.loadChatSettings(newId);
      }
    },
    showManageGroupModal(isOpen) {
      if (isOpen && this.activeConversation) {
        this.editGroupName = this.activeConversation.name;
      }
    }
  }
}
</script>

<style scoped>
/* Toggle switch styling */
input[type="checkbox"]+label {
  transition: background-color 0.3s;
}

input[type="checkbox"]:checked+label {
  background-color: #f9a8d4;
}

input[type="checkbox"] {
  transition: transform 0.3s, border-color 0.3s;
}

input[type="checkbox"]:checked {
  transform: translateX(100%);
  border-color: #ec4899;
}

/* Settings toggle checkbox */
.toggle-checkbox:checked {
  right: 0;
  border-color: #ec4899;
  transform: translateX(100%);
}

.toggle-checkbox:checked+.toggle-label {
  background-color: #f9a8d4;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>