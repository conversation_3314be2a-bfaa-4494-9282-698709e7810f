<template>
  <div class="row">
    <AppointmentViewCard :appointmentDetails="appointmentModalData" :appointmentDetailsModel="appointmentDetailModal"
      v-if="appointmentDetailModal" @closeModal="appointmentDetailModal = false"></AppointmentViewCard>
    <AppointmentReviewCard :appointmentDetails="appointmentReviewData" :appointmentReviewModal="appointmentReviewModal"
      v-if="appointmentReviewModal" @closeModal="appointmentReviewModal = false"></AppointmentReviewCard>
    <CustomForm :data="appointmentCustomFormData" :viewMode="appointmentCustomFormViewMode"
      :customFormModal="appointmentCustomFormModal" v-if="appointmentCustomFormModal"
      @closeModal="appointmentCustomFormModal = false"></CustomForm>

    <!-- Table Component -->
    <table class="w-full">
      <!-- Table Header -->
      <thead>
        <tr class="bg-gray-50/50 border-b border-gray-200">
          <th v-if="
            (filter_status == 'past' || filter_status == 'all') &&
            enable_delete_multiple
          " class="w-12 px-4 py-3">
            <input type="checkbox" class="rounded border-gray-300" v-model="selectAll"
              @change="allAppointmentSelectCheck" :value="1" :unchecked-value="0" :id="'appointment_id_select_all'"
              :name="'appointment_id_select_all'" />
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.patient.patient_name }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.common.services }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.service.charges }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.common.status }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ 'Payment Status' }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.common.action }}
          </th>
        </tr>
      </thead>

      <!-- Table Body -->
      <tbody>
        <template v-if="!isLoading && (hasTodayAppointments || hasUpcomingAppointments || hasPastAppointments)">
          <!-- Today's Appointments Section -->
          <template v-if="hasTodayAppointments">
            <tr class="bg-blue-50 border-b border-blue-200">
              <td :colspan="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple ? 7 : 6"
                class="px-6 py-3">
                <h3 class="text-lg font-semibold text-blue-800">Today's Appointments ({{ todayAppointments.length }})
                </h3>
              </td>
            </tr>
            <template v-for="(appointment, index) in todayAppointments">
              <tr :key="`today-${index}`" class="group hover:bg-gray-50/50 border-l-4 border-blue-400">
                <!-- Checkbox Column -->
                <td v-if="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple"
                  class="px-4 py-3">
                  <input type="checkbox" class="rounded border-gray-300" :id="'appointment_id' + appointment.id"
                    :name="'appointment_id' + appointment.id" @change="appointmentSelectCheck(appointment.id)" value="1"
                    unchecked-value="0" />
                </td>

                <!-- Patient Info Column -->
                <td class="px-4 py-3">
                  <div class="relative pl-4">
                    <div class="flex flex-col gap-3">
                      <!-- Patient Name and Info -->
                      <div class="space-y-1">
                        <div class="flex items-center gap-2">
                          <h3 class="text-lg font-semibold text-gray-900">
                            <template v-if="getUserRole() === 'patient'">
                              {{ appointment.patient_name }}
                            </template>
                            <template v-else>
                              <a :href="admin_url + 'admin.php?page=dashboard#/patient/profile-view/' + appointment.patient_id.id"
                                class="hover:text-indigo-600">
                                {{ appointment.patient_name }}
                              </a>
                            </template>
                          </h3>
                          <span v-if="appointment.video_consultation" class="text-indigo-500">
                            <i class="fas fa-video"></i>
                          </span>
                        </div>

                        <!-- Doctor and Clinic Info -->
                        <div class="flex gap-4 text-sm text-gray-600">
                          <div class="flex items-center" v-if="getUserRole() !== 'doctor'">
                            <span class="mr-1">Doctor:</span>
                            <span class="font-medium">{{ appointment.doctor_name }}</span>
                          </div>
                          <div class="flex items-center"
                            v-if="['administrator', 'doctor', 'patient'].includes(getUserRole())">
                            <span class="mr-1">Clinic:</span>
                            <span class="font-medium">{{ appointment.clinic_name }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Time and Date -->
                      <div class="flex items-center gap-6">
                        <div class="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-indigo-500" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor">
                            <circle cx="12" cy="12" r="10" />
                            <polyline points="12 6 12 12 16 14" />
                          </svg>
                          <span class="font-medium text-gray-800">
                            {{ appointment.appointment_start_time + " - " + appointment.appointment_end_time }}
                          </span>
                        </div>
                        <div class="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-indigo-500" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor">
                            <rect width="18" height="18" x="3" y="4" rx="2" />
                            <path d="M3 10h18" />
                            <path d="M16 2v4" />
                            <path d="M8 2v4" />
                          </svg>
                          <span class="text-gray-800">{{ appointment.appointment_formated_start_date }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Services Column -->
                <td class="px-4 py-3">
                  <div class="inline-block">
                    <div class="px-3 py-2 bg-blue-50 border border-blue-100 rounded-lg">
                      <div class="text-blue-700 font-medium">
                        {{ serviceTypeFormat(appointment.all_services, appointment.visit_type_old) }}
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Charges Column -->
                <td class="px-4 py-3">
                  <span class="text-xl font-semibold text-gray-900">
                    {{ appointment.clinic_prefix }}{{ appointment.all_service_charges || "0" }}{{
                      appointment.clinic_postfix }}
                  </span>
                </td>

                <!-- Status Column -->
                <td class="px-4 py-3">
                  <div class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold" :class="{
                    'bg-green-100 text-green-700 border-green-200': appointment.status === '1',
                    'bg-red-100 text-red-700 border-red-200': appointment.status === '0',
                    'bg-yellow-100 text-yellow-700 border-yellow-200': appointment.status === '2',
                    'bg-gray-100 text-gray-700 border-gray-200': appointment.status === '4',
                    'bg-blue-100 text-blue-700 border-blue-200': appointment.status === '3',
                  }">
                    {{
                      appointment.status === "1"
                        ? formTranslation.appointments.booked
                        : appointment.status === "0"
                          ? formTranslation.appointments.cancelled
                          : appointment.status === "2"
                            ? formTranslation.appointments.pending
                            : appointment.status === "4"
                              ? formTranslation.appointments.check_in
                              : formTranslation.appointments.check_out
                    }}
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold" :class="{
                    'bg-green-100 text-green-700 border-green-200': appointment.payment_status === 'approved',
                    'bg-yellow-100 text-yellow-700 border-yellow-200': appointment.payment_status === 'pending',
                  }">
                    {{ appointment.payment_status === "pending"? "Pending" : "Paid" }}
                  </div>
                </td>

                <!-- Actions Column -->
                <td class="px-4 py-3">
                  <div class="relative actions-dropdown" v-if="enable_delete_multiple != true">
                    <button @click="showActionsMenu = appointment.id"
                      class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring h-8 px-3 text-xs bg-black text-white hover:bg-gray-800">
                      Actions
                    </button>

                    <!-- Actions Dropdown -->
                    <div v-if="showActionsMenu === appointment.id"
                      class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <!-- View Details Button -->
                      <button v-if="kcCheckPermission('appointment_view')" @click="
                        appointmentModalData = appointment;
                      appointmentDetailModal = true;
                      " class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-sign-in-alt w-4 h-4 mr-2"></i>
                        {{ formTranslation.common.view }}
                      </button>

                      <!-- New Appointment Dashboard Button -->
                      <button v-if="
                        appointment.encounter_id !== null &&
                        appointment.status == 4
                      " @click="
                        $router.push({
                          name: 'appointment.new',
                          params: { encounter_id: appointment.encounter_id },
                        })
                        " class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-tachometer-alt mr-2"></i>
                        {{ formTranslation.patient_encounter.encounter_details }}
                      </button>

                      <!-- Check-in Button -->
                      <button v-if="
                        kcCheckPermission('patient_appointment_status_change') &&
                        !['3', '4', '0', '2'].includes(appointment.status) &&
                        currentDate === appointment.appointment_end_date
                      " @click="handleAppointmentStatus(appointment, '4')"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-sign-in-alt w-4 h-4 mr-2"></i>
                        {{ formTranslation.appointments.check_in }}
                      </button>

                      <!-- Check-out Button -->
                      <button v-if="
                        kcCheckPermission('patient_appointment_status_change') &&
                        appointment.status === '4' &&
                        currentDate === appointment.appointment_end_date
                      " @click="handleAppointmentStatus(appointment, '3')"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-sign-out-alt w-4 h-4 mr-2"></i>
                        {{ formTranslation.appointments.check_out }}
                      </button>

                      <!-- Booked Button -->
                      <button v-if="
                        kcCheckPermission('patient_appointment_status_change') &&
                        appointment.status === '2' &&
                        currentDate === appointment.appointment_end_date
                      " @click="handleAppointmentStatus(appointment, '1')"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-sign-in-alt w-4 h-4 mr-2"></i>
                        {{ formTranslation.appointments.booked }}
                      </button>

                      <!-- Resend Video Conference Link -->
                      <button
                        v-if="getUserRole() != 'patient' && kcCheckPermission('appointment_add') && appointment.status == '1' && appointment.video_consultation"
                        @click="resendRequest(appointment.id)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-paper-plane w-4 h-4 mr-2"></i>
                        {{
                          formTranslation.appointments.resend_video_conference_link
                        }}
                      </button>

                      <!-- Consultation Dashboard -->
                      <button v-if="
                        appointment.isEncounterTemp
                          ? kcCheckPermission('encounters_template_view')
                          : kcCheckPermission('patient_encounter_view')
                      " @click="handleDashboardNavigation(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-tachometer-alt mr-2"></i>
                        {{ formTranslation.patient_encounter.encounter_dashboard }}
                      </button>

                      <!-- Delete Button -->
                      <button v-if="
                        kcCheckPermission('appointment_delete') &&
                        getUserRole() != 'patient'
                      " @click="handleAppointmentDelete(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-trash w-4 h-4 mr-2"></i>
                        {{ formTranslation.clinic_schedule.dt_lbl_dlt }}
                      </button>

                      <!-- Cancel Button -->
                      <button v-if="
                        kcCheckPermission('appointment_delete') &&
                        getUserRole() == 'patient' &&
                        appointment.cancellation_buffer
                      " @click="handleAppointmentCancel(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-ban w-4 h-4 mr-2"></i>
                        {{ formTranslation.common.cancel }}
                      </button>

                      <!-- Add Review Button -->
                      <button v-if="
                        getUserRole() === 'patient' &&
                        appointment.status === '3' &&
                        user_data.addOns.kiviPro
                      " @click="
                        appointmentReviewData = appointment;
                      appointmentReviewModal = true;
                      " class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-star w-4 h-4 mr-2"></i>
                        {{ formTranslation.appointments.add_review }}
                      </button>

                      <a :href="appointment.zoom_data !== null ? appointment.zoom_data.start_url : '#'" target="_blank"
                        data-trigger="hover" :title="formTranslation.appointments.start_video_call"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" v-if="
                          (getUserRole() === 'doctor' || getUserRole() === 'clinic_admin' || getUserRole() === 'administrator') &&
                          appointment.video_consultation &&
                          ['4', '1'].includes(appointment.status) &&
                          currentDate === appointment.appointment_end_date">
                        <i class="fa fa-video w-4 h-4 mr-2"></i> {{ formTranslation.appointments.start }}
                      </a>
                      <a :href="appointment.zoom_data !== null ? appointment.zoom_data.join_url : '#'"
                        data-trigger="hover" :title="formTranslation.appointments.join_video_call" target="_blank"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" v-if="
                          getUserRole() === 'patient' &&
                          appointment.video_consultation &&
                          ['4', '1'].includes(appointment.status) &&
                          currentDate === appointment.appointment_end_date">
                        <i class="fa fa-video w-4 h-4 mr-2"></i> {{ formTranslation.appointments.join }}
                      </a>

                      <!-- Reschedule Appointment -->
                      <button v-if="
                        ['2', '1'].includes(appointment.status) &&
                        kcCheckPermission('appointment_edit')
                      " @click="$emit('rescheduleAppointment', appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-calendar w-4 h-4 mr-2"></i>
                        {{ formTranslation.appointments.reschedule }}
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </template>
          </template>

          <!-- Upcoming Appointments Section -->
          <template v-if="hasUpcomingAppointments">
            <tr class="bg-green-50 border-b border-green-200">
              <td :colspan="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple ? 7 : 6"
                class="px-6 py-3">
                <h3 class="text-lg font-semibold text-green-800">Upcoming Appointments ({{ upcomingAppointments.length
                  }})</h3>
              </td>
            </tr>
            <template v-for="(appointment, index) in upcomingAppointments">
              <tr :key="`upcoming-${index}`" class="group hover:bg-gray-50/50 border-l-4 border-green-400">
                <!-- Checkbox Column -->
                <td v-if="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple"
                  class="px-4 py-3">
                  <input type="checkbox" class="rounded border-gray-300" :id="'appointment_id' + appointment.id"
                    :name="'appointment_id' + appointment.id" @change="appointmentSelectCheck(appointment.id)" value="1"
                    unchecked-value="0" />
                </td>

                <!-- Patient Info Column -->
                <td class="px-4 py-3">
                  <div class="relative pl-4">
                    <div class="flex flex-col gap-3">
                      <!-- Patient Name and Info -->
                      <div class="space-y-1">
                        <div class="flex items-center gap-2">
                          <h3 class="text-lg font-semibold text-gray-900">
                            <template v-if="getUserRole() === 'patient'">
                              {{ appointment.patient_name }}
                            </template>
                            <template v-else>
                              <a :href="admin_url + 'admin.php?page=dashboard#/patient/profile-view/' + appointment.patient_id.id"
                                class="hover:text-indigo-600">
                                {{ appointment.patient_name }}
                              </a>
                            </template>
                          </h3>
                          <span v-if="appointment.video_consultation" class="text-indigo-500">
                            <i class="fas fa-video"></i>
                          </span>
                        </div>

                        <!-- Doctor and Clinic Info -->
                        <div class="flex gap-4 text-sm text-gray-600">
                          <div class="flex items-center" v-if="getUserRole() !== 'doctor'">
                            <span class="mr-1">Doctor:</span>
                            <span class="font-medium">{{ appointment.doctor_name }}</span>
                          </div>
                          <div class="flex items-center"
                            v-if="['administrator', 'doctor', 'patient'].includes(getUserRole())">
                            <span class="mr-1">Clinic:</span>
                            <span class="font-medium">{{ appointment.clinic_name }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Time and Date -->
                      <div class="flex items-center gap-6">
                        <div class="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-indigo-500" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor">
                            <circle cx="12" cy="12" r="10" />
                            <polyline points="12 6 12 12 16 14" />
                          </svg>
                          <span class="font-medium text-gray-800">
                            {{ appointment.appointment_start_time + " - " + appointment.appointment_end_time }}
                          </span>
                        </div>
                        <div class="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-indigo-500" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor">
                            <rect width="18" height="18" x="3" y="4" rx="2" />
                            <path d="M3 10h18" />
                            <path d="M16 2v4" />
                            <path d="M8 2v4" />
                          </svg>
                          <span class="text-gray-800">{{ appointment.appointment_formated_start_date }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Services Column -->
                <td class="px-4 py-3">
                  <div class="inline-block">
                    <div class="px-3 py-2 bg-blue-50 border border-blue-100 rounded-lg">
                      <div class="text-blue-700 font-medium">
                        {{ serviceTypeFormat(appointment.all_services, appointment.visit_type_old) }}
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Charges Column -->
                <td class="px-4 py-3">
                  <span class="text-xl font-semibold text-gray-900">
                    {{ appointment.clinic_prefix }}{{ appointment.all_service_charges || "0" }}{{
                      appointment.clinic_postfix }}
                  </span>
                </td>

                <!-- Status Column -->
                <td class="px-4 py-3">
                  <div class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold" :class="{
                    'bg-green-100 text-green-700 border-green-200': appointment.status === '1',
                    'bg-red-100 text-red-700 border-red-200': appointment.status === '0',
                    'bg-yellow-100 text-yellow-700 border-yellow-200': appointment.status === '2',
                    'bg-gray-100 text-gray-700 border-gray-200': appointment.status === '4',
                    'bg-blue-100 text-blue-700 border-blue-200': appointment.status === '3',
                  }">
                    {{
                      appointment.status === "1"
                        ? formTranslation.appointments.booked
                        : appointment.status === "0"
                          ? formTranslation.appointments.cancelled
                          : appointment.status === "2"
                            ? formTranslation.appointments.pending
                            : appointment.status === "4"
                              ? formTranslation.appointments.check_in
                              : formTranslation.appointments.check_out
                    }}
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold" :class="{
                    'bg-green-100 text-green-700 border-green-200': appointment.payment_status === 'approved',
                    'bg-yellow-100 text-yellow-700 border-yellow-200': appointment.payment_status === 'pending' ,
                  }">
                    {{ appointment.payment_status === "pending"? "Pending" : "Paid" }}
                  </div>
                </td>

                <!-- Actions Column -->
                <td class="px-4 py-3">
                  <div class="relative actions-dropdown" v-if="enable_delete_multiple != true">
                    <button @click="showActionsMenu = appointment.id"
                      class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring h-8 px-3 text-xs bg-black text-white hover:bg-gray-800">
                      Actions
                    </button>

                    <!-- Actions Dropdown -->
                    <div v-if="showActionsMenu === appointment.id"
                      class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <!-- Same actions as for today's appointments -->
                      <button v-if="kcCheckPermission('appointment_view')"
                        @click="appointmentModalData = appointment; appointmentDetailModal = true;"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-sign-in-alt w-4 h-4 mr-2"></i>
                        {{ formTranslation.common.view }}
                      </button>

                      <!-- New Appointment Dashboard Button -->
                      <button v-if="
                        appointment.encounter_id !== null &&
                        appointment.status == 4
                      " @click="
                        $router.push({
                          name: 'appointment.new',
                          params: { encounter_id: appointment.encounter_id },
                        })
                        " class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-tachometer-alt mr-2"></i>
                        {{ formTranslation.patient_encounter.encounter_details }}
                      </button>

                      <!-- Resend Video Conference Link -->
                      <button
                        v-if="getUserRole() != 'patient' && kcCheckPermission('appointment_add') && appointment.status == '1' && appointment.video_consultation"
                        @click="resendRequest(appointment.id)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-paper-plane w-4 h-4 mr-2"></i>
                        {{
                          formTranslation.appointments.resend_video_conference_link
                        }}
                      </button>

                      <!-- Reschedule Appointment -->
                      <button v-if="
                        ['2', '1'].includes(appointment.status) &&
                        kcCheckPermission('appointment_edit')
                      " @click="$emit('rescheduleAppointment', appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-calendar w-4 h-4 mr-2"></i>
                        {{ formTranslation.appointments.reschedule }}
                      </button>

                      <button v-if="
                        appointment.isEncounterTemp
                          ? kcCheckPermission('encounters_template_view')
                          : kcCheckPermission('patient_encounter_view')
                      " @click="handleDashboardNavigation(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-tachometer-alt mr-2"></i>
                        {{ formTranslation.patient_encounter.encounter_dashboard }}
                      </button>

                      <!-- Delete Button -->
                      <button v-if="
                        kcCheckPermission('appointment_delete') &&
                        getUserRole() != 'patient'
                      " @click="handleAppointmentDelete(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-trash w-4 h-4 mr-2"></i>
                        {{ formTranslation.clinic_schedule.dt_lbl_dlt }}
                      </button>

                      <!-- Cancel Button -->
                      <button v-if="
                        kcCheckPermission('appointment_delete') &&
                        getUserRole() == 'patient' &&
                        appointment.cancellation_buffer
                      " @click="handleAppointmentCancel(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-ban w-4 h-4 mr-2"></i>
                        {{ formTranslation.common.cancel }}
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </template>
          </template>

          <!-- Past Appointments Section -->
          <template v-if="hasPastAppointments">
            <tr class="bg-gray-50 border-b border-gray-200">
              <td :colspan="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple ? 7 : 6"
                class="px-6 py-3">
                <h3 class="text-lg font-semibold text-gray-800">Past Appointments ({{ pastAppointments.length }})</h3>
              </td>
            </tr>
            <template v-for="(appointment, index) in pastAppointments">
              <tr :key="`past-${index}`" class="group hover:bg-gray-50/50 border-l-4 border-gray-400">
                <!-- Checkbox Column -->
                <td v-if="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple"
                  class="px-4 py-3">
                  <input type="checkbox" class="rounded border-gray-300" :id="'appointment_id' + appointment.id"
                    :name="'appointment_id' + appointment.id" @change="appointmentSelectCheck(appointment.id)" value="1"
                    unchecked-value="0" />
                </td>

                <!-- Patient Info Column -->
                <td class="px-4 py-3">
                  <div class="relative pl-4">
                    <div class="flex flex-col gap-3">
                      <!-- Patient Name and Info -->
                      <div class="space-y-1">
                        <div class="flex items-center gap-2">
                          <h3 class="text-lg font-semibold text-gray-900">
                            <template v-if="getUserRole() === 'patient'">
                              {{ appointment.patient_name }}
                            </template>
                            <template v-else>
                              <a :href="admin_url + 'admin.php?page=dashboard#/patient/profile-view/' + appointment.patient_id.id"
                                class="hover:text-indigo-600">
                                {{ appointment.patient_name }}
                              </a>
                            </template>
                          </h3>
                          <span v-if="appointment.video_consultation" class="text-indigo-500">
                            <i class="fas fa-video"></i>
                          </span>
                        </div>

                        <!-- Doctor and Clinic Info -->
                        <div class="flex gap-4 text-sm text-gray-600">
                          <div class="flex items-center" v-if="getUserRole() !== 'doctor'">
                            <span class="mr-1">Doctor:</span>
                            <span class="font-medium">{{ appointment.doctor_name }}</span>
                          </div>
                          <div class="flex items-center"
                            v-if="['administrator', 'doctor', 'patient'].includes(getUserRole())">
                            <span class="mr-1">Clinic:</span>
                            <span class="font-medium">{{ appointment.clinic_name }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Time and Date -->
                      <div class="flex items-center gap-6">
                        <div class="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-indigo-500" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor">
                            <circle cx="12" cy="12" r="10" />
                            <polyline points="12 6 12 12 16 14" />
                          </svg>
                          <span class="font-medium text-gray-800">
                            {{ appointment.appointment_start_time + " - " + appointment.appointment_end_time }}
                          </span>
                        </div>
                        <div class="flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-indigo-500" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor">
                            <rect width="18" height="18" x="3" y="4" rx="2" />
                            <path d="M3 10h18" />
                            <path d="M16 2v4" />
                            <path d="M8 2v4" />
                          </svg>
                          <span class="text-gray-800">{{ appointment.appointment_formated_start_date }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Services Column -->
                <td class="px-4 py-3">
                  <div class="inline-block">
                    <div class="px-3 py-2 bg-blue-50 border border-blue-100 rounded-lg">
                      <div class="text-blue-700 font-medium">
                        {{ serviceTypeFormat(appointment.all_services, appointment.visit_type_old) }}
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Charges Column -->
                <td class="px-4 py-3">
                  <span class="text-xl font-semibold text-gray-900">
                    {{ appointment.clinic_prefix }}{{ appointment.all_service_charges || "0" }}{{
                      appointment.clinic_postfix }}
                  </span>
                </td>

                <!-- Status Column -->
                <td class="px-4 py-3">
                  <div class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold" :class="{
                    'bg-green-100 text-green-700 border-green-200': appointment.status === '1',
                    'bg-red-100 text-red-700 border-red-200': appointment.status === '0',
                    'bg-yellow-100 text-yellow-700 border-yellow-200': appointment.status === '2',
                    'bg-gray-100 text-gray-700 border-gray-200': appointment.status === '4',
                    'bg-blue-100 text-blue-700 border-blue-200': appointment.status === '3',
                  }">
                    {{
                      appointment.status === "1"
                        ? formTranslation.appointments.booked
                        : appointment.status === "0"
                          ? formTranslation.appointments.cancelled
                          : appointment.status === "2"
                            ? formTranslation.appointments.pending
                            : appointment.status === "4"
                              ? formTranslation.appointments.check_in
                              : formTranslation.appointments.check_out
                    }}
                  </div>
                </td>

                <td class="px-4 py-3">
                  <div class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold" :class="{
                    'bg-green-100 text-green-700 border-green-200': appointment.payment_status === 'approved',
                    'bg-yellow-100 text-yellow-700 border-yellow-200': appointment.payment_status === 'pending',
                  }">
                    {{ appointment.payment_status === "pending"? "Pending" : "Paid" }}
                  </div>
                </td>



                <!-- Actions Column -->
                <td class="px-4 py-3">
                  <div class="relative actions-dropdown" v-if="enable_delete_multiple != true">
                    <button @click="showActionsMenu = appointment.id"
                      class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring h-8 px-3 text-xs bg-black text-white hover:bg-gray-800">
                      Actions
                    </button>

                    <!-- Actions Dropdown -->
                    <div v-if="showActionsMenu === appointment.id"
                      class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <!-- Reduced action set for past appointments -->
                      <button v-if="kcCheckPermission('appointment_view')"
                        @click="appointmentModalData = appointment; appointmentDetailModal = true;"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-sign-in-alt w-4 h-4 mr-2"></i>
                        {{ formTranslation.common.view }}
                      </button>

                      <!-- New Appointment Dashboard Button -->
                      <button v-if="
                        appointment.encounter_id !== null &&
                        appointment.status == 4
                      " @click="
                        $router.push({
                          name: 'appointment.new',
                          params: { encounter_id: appointment.encounter_id },
                        })
                        " class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-tachometer-alt mr-2"></i>
                        {{ formTranslation.patient_encounter.encounter_details }}
                      </button>

                      <button v-if="
                        appointment.isEncounterTemp
                          ? kcCheckPermission('encounters_template_view')
                          : kcCheckPermission('patient_encounter_view')
                      " @click="handleDashboardNavigation(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-tachometer-alt mr-2"></i>
                        {{ formTranslation.patient_encounter.encounter_dashboard }}
                      </button>

                      <!-- Delete Button -->
                      <button v-if="
                        kcCheckPermission('appointment_delete') &&
                        getUserRole() != 'patient'
                      " @click="handleAppointmentDelete(appointment)"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-trash w-4 h-4 mr-2"></i>
                        {{ formTranslation.clinic_schedule.dt_lbl_dlt }}
                      </button>

                      <!-- Add Review Button -->
                      <button v-if="
                        getUserRole() === 'patient' &&
                        appointment.status === '3' &&
                        user_data.addOns.kiviPro
                      " @click="
                        appointmentReviewData = appointment;
                      appointmentReviewModal = true;
                      " class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fa fa-star w-4 h-4 mr-2"></i>
                        {{ formTranslation.appointments.add_review }}
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </template>
          </template>

          <!-- Appointment edit forms -->
          <template v-for="(appointment, index) in appointmentList">
            <tr :key="'edit_' + index">
              <td colspan="6" class="p-0">
                <div v-show="editId === appointment.id" class="border-t mt-3 p-4 bg-gray-50">
                  <AppointmentForm v-if="editId === appointment.id" :appointmentData="appointmentFormObj"
                    @appointmentSaved="handleAppointmentSave" @closeAppointmentForm="
                      closeAppointmentForm('accordion_' + index)
                      " ref="kc_appointment_form" :patient_profile_id="patient_profile_id" />
                </div>
              </td>
            </tr>
          </template>
        </template>

        <!-- No Appointments Message -->
        <tr v-if="!isLoading && (!hasTodayAppointments && !hasUpcomingAppointments && !hasPastAppointments)">
          <td :colspan="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple ? 7 : 6" class="px-4 py-8 text-center">
            <h4 class="text-red-600 font-medium">
              {{ formTranslation.common.no_appointments }}
            </h4>
          </td>
        </tr>

        <!-- Loading State -->
        <tr v-if="isLoading">
          <td :colspan="(filter_status == 'past' || filter_status == 'all') && enable_delete_multiple ? 7 : 6" class="px-4 py-8">
            <loader-component-2></loader-component-2>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Modals -->
    <ModalPopup v-if="appointmentModel" modalId="appointment-detail" modalSize="lg" :openModal="appointmentModel"
      :modalTitle="formTranslation.widgets.summary" @closeModal="
        appointmentModel = false;
      loading = false;
      ">
      <div class="page-loader-section" v-if="overlaySpinner">
        <loader-component-2></loader-component-2>
      </div>
      <AppointmentDetail ref="appointment_detail" :appointment-data="appointmentFormObj" :user-data="userData"
        :prefix="prefix" :postfix="postfix" @bookAppointment="bookAppointmentHandle" @cancelAppointment="
          appointmentModel = false;
        loading = false;
        " :lazy="true" />
    </ModalPopup>
  </div>
</template>

<script>
import AppointmentForm from "./AppointmentForm";
import AppointmentViewCard from "./AppointmentViewCard";
import AppointmentReviewCard from "./AppointmentReviewCard";
import CustomForm from "../../views/CustomForm/Form.vue";
import { post, get } from "../../config/request";
import "print-area-js";
import addToCalendar from "./addToCalendar";

export default {
  components: {
    AppointmentForm,
    AppointmentViewCard,
    AppointmentReviewCard,
    addToCalendar,
    CustomForm,
  },
  props: {
    appointmentData: {
      type: [Object, Array],
      default() {
        return [];
      },
    },
    filter_status: {
      type: [String, Number, Object],
      default() {
        return [1];
      },
    },
    enable_delete_multiple: {
      type: [Boolean],
      default() {
        return [];
      },
    },
    delete_multiple_appointment: {
      type: [Boolean],
      default() {
        return [];
      },
    },
    patient_profile_id: {
      type: [Number, String],
      default() {
        return "";
      },
    },
  },
  data: () => {
    return {
      appointmentModel: false,
      showActionsMenu: null,
      isLoading: true,
      appointmentList: [],
      patientNumber: [],
      selectAll: false,
      addAppointment: false,
      editAppointment: false,
      appointmentFormObj: {},
      editId: "",
      showCale: false,
      hideFormBtn: false,
      activeAccordianDetail: {
        id: 0,
        status: false,
        for: "view",
      },
      admin_url: "",
      appointmentModalData: {},
      appointmentReviewData: {},
      appointmentReviewModal: false,
      appointmentDetailModal: false,
      appointmentCustomFormData: {},
      appointmentCustomFormModal: false,
      appointmentCustomFormViewMode: false,
      restrictAppointment: "",
      cancellationBuffer: 0,
    };
  },
  mounted() {
    this.getRestrictAppointmentDay();
    setTimeout(() => {
      this.init();
    }, 1000);

    this.admin_url = window.request_data.adminUrl;
    // watch appointment accordian state
    this.$root.$on("bv::collapse::state", (collapseID, isJustShown) => {
      this.activeAccordianDetail.id = collapseID;
      this.activeAccordianDetail.status = isJustShown;
    });

    // Add click handler for outside clicks
    document.addEventListener("click", this.handleClickOutside);

    // Initialize with empty selection
    this.patientNumber = [];
    this.selectAll = false;
    this.$emit("enabledDeleteBtn", this.patientNumber);
  },
  unmounted() {
    document.removeEventListener("click", this.handleClickOutside);
  },
  clickOutside: {
    mounted(el, binding) {
      el._clickOutside = (event) => {
        if (!(el === event.target || el.contains(event.target))) {
          binding.value(event);
        }
      };
      document.addEventListener("click", el._clickOutside);
    },
    unmounted(el) {
      document.removeEventListener("click", el._clickOutside);
    },
  },
  methods: {
    init: function () {
      this.appointmentFormObj = this.defaultAppointment();
      setTimeout(() => {
        this.appointmentFormObj.doctor_id = this.doctor_id;
        this.appointmentFormObj.clinic_id = this.clinic_id;
      }, 500);
    },
    defaultAppointment: function () {
      return {
        id: "",
        date: "",
        endDate: "",
        appointment_start_date: new Date(),
        appointment_start_time: "",
        visit_type: "",
        description: "",
        title: "",
        patient_id: {},
        doctor_id: {},
        clinic_id: {},
        status: "",
        video_consultation: false,
      };
    },
    resendRequest: function (id) {
      var element = $("#resend_" + id).find("i");
      element.removeClass("fa fa-paper-plane ");
      element.addClass("fa fa-spinner fa-spin");
      post("resend_zoom_link_patient", { id: id })
        .then((data) => {
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-paper-plane");
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    addToCalender() {
      this.showCale = true;
    },
    handleClickOutside(event) {
      // If click target is not part of any dropdown, close all dropdowns
      if (!event.target.closest(".actions-dropdown")) {
        this.showActionsMenu = null;
      }
    },
    getEncounterDetails: function (appointment) {
      return {
        html: true,
        title: () => {
          // Note this is called only when the popover is opened
          return "Hello <b>Popover:</b> " + appointment.clinic_name;
        },
        content: () => {
          // Note this is called only when the popover is opened
          return "The date is:";
        },
      };
    },
    handleAppointmentEdit_delete: function (
      appointment,
      collapseID,
      hideFormBtn = false
    ) {
      // hideFormBtn : view - true, edit - false
      let isStatusChanges = false;

      if (hideFormBtn) {
        this.hideFormBtn = false;
        if (this.activeAccordianDetail.for == "view") {
          this.$root.$emit("bv::hide::collapse", collapseID);
          isStatusChanges = true;
        }
        this.activeAccordianDetail.for = "view";
        // this.$root.$emit('bv::toggle::collapse', collapseID);
      } else {
        this.activeAccordianDetail.for = "edit";
        if (this.activeAccordianDetail.for == "edit") {
          this.$root.$emit("bv::hide::collapse", collapseID);
          isStatusChanges = true;
        }
        this.hideFormBtn = true;
      }

      this.editAppointment = true;
      if (isStatusChanges) {
        this.$root.$emit("bv::toggle::collapse", collapseID);
      }

      this.editId = appointment.id;
      let editAppointment = Object.assign({}, appointment, {
        restrictAppointment: this.restrictAppointment,
      });

      setTimeout(() => {
        editAppointment.appointment_start_date = new Date(
          appointment.appointment_start_date + " 00:00"
        );
        this.appointmentFormObj = Object.assign({}, editAppointment);
      }, 1000);
    },
    getRestrictAppointmentDay: function () {
      get("restrict_appointment_edit", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.restrictAppointment = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    handleAppointmentSave: function () {
      this.$emit("closeFilterForm");
      setTimeout(() => {
        this.$store.dispatch("appointmentModule/fetchAppointmentData", {
          filterData: {
            date: moment(new Date()).format("YYYY-MM-DD"),
            status: 1,
          },
        });
      }, 1000);
    },
    closeAppointmentForm: function (id) {
      this.$root.$emit("bv::toggle::collapse", id);
    },

    handleDashboardNavigation(appointment) {
      // if (this.appointment.encounter_id && this.appointment.encounter_id !== 0) {
      //   // If encounter_id exists and is not 0, go to encounter dashboard
      //   this.$router.push({
      //     name: "patient-encounter.dashboard",
      //     params: { encounter_id: appointment.encounter_id },
      //     query: { isEncounterTemp: appointment.isEncounterTemp },
      //   });
      // } else {
      // If encounter_id is 0 or undefined, go to clinical dashboard
      this.$router.push({
        name: "patient-clinical.dashboard",
        params: { patient_id: appointment.patient_id.id },
      });
      // }
    },
    handleAppointmentCancel: async function (appointment) {
      if (appointment.id !== undefined) {
        try {
          // Get reference to the button
          const but = document.querySelector(
            `#appointment_cancel_${appointment.id}`
          );
          but.disabled = true;
          but.innerHTML = `<i class='fa fa-sync fa-spin'></i>`;

          // Show confirmation dialog
          const result = await this.$swal.fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.py_cancel_appointment,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
            focusConfirm: false,
          });

          if (result.isConfirmed) {
            const data = await get("appointment_update_status", {
              appointment_id: appointment.id,
              appointment_status: 0,
            });

            if (data.data.status !== undefined && data.data.status === true) {
              // Show success message
              await this.$swal.fire({
                icon: "success",
                title: "Success",
                text: data.data.message,
                timer: 1500,
              });

              // Update store
              await this.$store.dispatch(
                "appointmentModule/fetchAppointmentSlots",
                {
                  date: moment(appointment.appointment_start_date).format(
                    "YYYY-MM-DD"
                  ),
                  appointment_id: "",
                  clinic_id: appointment.clinic_id.id,
                  doctor_id: appointment.doctor_id.id,
                  patient_id: "",
                }
              );

              // Find and remove appointment from list
              const index = this.appointmentList.findIndex(
                (x) => x.id === appointment.id
              );
              this.$store.commit("appointmentModule/DELETE_APPOINTMENT_DATA", {
                id: index,
              });
            }
          }
        } catch (error) {
          console.error(error);

          // Show error message
          await this.$swal.fire({
            icon: "error",
            title: "Error",
            text: this.formTranslation.common.internal_server_error,
          });
        } finally {
          // Always reset button state
          const but = document.querySelector(
            `#appointment_cancel_${appointment.id}`
          );
          if (but) {
            but.disabled = false;
            but.innerHTML = `<i class='fa fa-ban'></i>`;
          }
        }
      }
    },
    handleAppointmentDelete: function (appointment) {
      if (appointment.id !== undefined) {
        let but = $("#appointment_delete_" + appointment.id);
        but.prop("disabled", true);
        but.html(`<i class='fa fa-sync fa-spin'></i>`);

        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.py_delete_appointment,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
            focusConfirm: false,
          })
          .then((result) => {
            if (result.isConfirmed) {
              get("appointment_delete", {
                id: appointment.id,
              })
                .then((data) => {
                  but.prop("disabled", false);
                  but.html(`<i class='fa fa-trash'></i>`);

                  if (
                    data.data.status !== undefined &&
                    data.data.status === true
                  ) {
                    this.$swal.fire({
                      icon: "success",
                      title: "Success",
                      text: data.data.message,
                      timer: 1500,
                    });

                    this.$store.dispatch(
                      "appointmentModule/fetchAppointmentSlots",
                      {
                        date: moment(appointment.appointment_start_date).format(
                          "YYYY-MM-DD"
                        ),
                        appointment_id: "",
                        clinic_id: appointment.clinic_id.id,
                        doctor_id: appointment.doctor_id.id,
                        patient_id: "",
                      }
                    );

                    var index = this.appointmentList
                      .map((x) => x.id)
                      .indexOf(appointment.id);

                    this.$store.commit(
                      "appointmentModule/DELETE_APPOINTMENT_DATA",
                      {
                        id: index,
                      }
                    );
                  }
                })
                .catch((error) => {
                  but.prop("disabled", false);
                  but.html(`<i class='fa fa-trash'></i>`);
                  console.log(error);

                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: this.formTranslation.common.internal_server_error,
                  });
                });
            } else {
              // If canceled
              but.prop("disabled", false);
              but.html(`<i class='fa fa-trash'></i>`);
            }
          });
      }
    },
    handleAppointmentStatus: function (appointment, status) {
      if (status === "3") {
        if (
          appointment.encounter_id !== null &&
          appointment.encounter_detail !== undefined &&
          [1, "1"].includes(appointment.encounter_detail.status)
        ) {
          displayErrorMessage(this.formTranslation.common.encounter_not_close);
          return;
        }
      }

      const element = $("#status_update_" + appointment.id).find("i");
      $("#status_update_" + appointment.id).prop("disabled", true);
      if (status === "4") {
        element.removeClass("fa fa-sign-in-alt");
      } else {
        element.removeClass("fa fa-sign-out-alt");
      }
      element.addClass("fa fa-spinner fa-spin");

      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: this.formTranslation.common.update_appointment_status,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then((result) => {
          if (result.isConfirmed) {
            get("appointment_update_status", {
              appointment_id: appointment.id,
              appointment_status: status,
            })
              .then((response) => {
                $("#status_update_" + appointment.id).prop("disabled", false);
                element.removeClass("fa fa-spinner fa-spin");
                if (status === "4") {
                  element.addClass("fa fa-sign-in-alt");
                } else {
                  element.addClass("fa fa-sign-out-alt");
                }
                if (
                  response.data.status !== undefined &&
                  response.data.status === true
                ) {
                  this.$emit("reloadAppointment");
                  this.$swal.fire({
                    icon: "success",
                    title: this.formTranslation.common.success,
                    text: response.data.message,
                  });
                } else {
                  displayErrorMessage(response.data.message);
                }
              })
              .catch((error) => {
                $("#status_update_" + appointment.id).prop("disabled", false);
                element.removeClass("fa fa-spinner fa-spin");
                if (status === "4") {
                  element.addClass("fa fa-sign-in-alt");
                } else {
                  element.addClass("fa fa-sign-out-alt");
                }
                console.log(error);
                displayErrorMessage(
                  this.formTranslation.common.internal_server_error
                );
              });
          } else {
            $("#status_update_" + appointment.id).prop("disabled", false);
            element.removeClass("fa fa-spinner fa-spin");
            if (status === "4") {
              element.addClass("fa fa-sign-in-alt");
            } else {
              element.addClass("fa fa-sign-out-alt");
            }
          }
        });
    },
    serviceTypeFormat: function (newVal, oldVal) {
      if (oldVal === undefined || oldVal === null || oldVal === "") {
        return newVal;
      } else {
        return oldVal.replace(/_/g, " ");
      }
    },
    appointmentSelectCheck(value) {
      if (!this.patientNumber.includes(value)) {
        this.patientNumber.push(value);
        // Add selected class to the row
        this.$nextTick(() => {
          const checkbox = document.getElementById('appointment_id' + value);
          if (checkbox) {
            const row = checkbox.closest('tr');
            if (row) {
              row.classList.add('bg-blue-50', 'border-blue-300');
            }
          }
        });
      } else {
        const index = this.patientNumber.indexOf(value);
        if (index > -1) {
          this.patientNumber.splice(index, 1);
          // Remove selected class from the row
          this.$nextTick(() => {
            const checkbox = document.getElementById('appointment_id' + value);
            if (checkbox) {
              const row = checkbox.closest('tr');
              if (row) {
                row.classList.remove('bg-blue-50', 'border-blue-300');
              }
            }
          });
        }
      }
      this.$emit("enabledDeleteBtn", this.patientNumber);
    },
    allAppointmentSelectCheck() {
      // Get all checkboxes with input type checkbox
      const checkboxes = document.querySelectorAll('input[type="checkbox"]');

      if (this.selectAll == 1) {
        // Select all checkboxes
        this.patientNumber = []; // Reset to avoid duplicates

        checkboxes.forEach(checkbox => {
          if (checkbox.id && checkbox.id !== "appointment_id_select_all") {
            const appointmentId = checkbox.id.replace("appointment_id", "");
            checkbox.checked = true;

            // Add to selected list
            if (!this.patientNumber.includes(appointmentId)) {
              this.patientNumber.push(appointmentId);
            }

            // Add highlighting to the row
            const row = checkbox.closest('tr');
            if (row) {
              row.classList.add('bg-blue-50', 'border-blue-300');
            }
          }
        });
      } else {
        // Deselect all checkboxes
        checkboxes.forEach(checkbox => {
          if (checkbox.id && checkbox.id !== "appointment_id_select_all") {
            checkbox.checked = false;

            // Remove highlighting from the row
            const row = checkbox.closest('tr');
            if (row) {
              row.classList.remove('bg-blue-50', 'border-blue-300');
            }
          }
        });

        // Clear selected appointments
        this.patientNumber = [];
      }

      // Notify parent component of selection change
      this.$emit("enabledDeleteBtn", this.patientNumber);
    },
    async deleteMultipleAppointment() {
      try {
        // Get selected appointment IDs
        const selectedIds = this.patientNumber;

        if (selectedIds.length === 0) {
          this.$swal.fire({
            icon: 'warning',
            title: 'No Appointments Selected',
            text: 'Please select at least one appointment to delete.',
            showConfirmButton: true
          });
          return;
        }

        // Confirm operation with detailed info
        const result = await this.$swal.fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          html: `
            <div class="text-left">
              <p>You are about to delete <strong>${selectedIds.length}</strong> appointment${selectedIds.length > 1 ? 's' : ''}.</p>
              <p>This action cannot be undone.</p>
            </div>
          `,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#dc3545", // Bootstrap danger color
          cancelButtonColor: "#6c757d", // Bootstrap secondary color
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
          focusConfirm: false,
        });

        if (result.isConfirmed) {
          // Show loading indicator with progress
          let progressAlert = this.$swal.fire({
            title: 'Deleting Appointments',
            html: `
              <div>
                <p>Processing 0 of ${selectedIds.length} appointments...</p>
                <div class="progress mt-3" style="height: 20px;">
                  <div class="progress-bar progress-bar-striped progress-bar-animated bg-info"
                    role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                    style="width: 0%">0%</div>
                </div>
              </div>
            `,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false
          });

          // Track operation progress and results
          let completedOperations = 0;
          let successCount = 0;
          let failedIds = [];
          const totalOperations = selectedIds.length;

          // Function to update progress
          const updateProgress = () => {
            completedOperations++;
            const progressPercent = Math.round((completedOperations / totalOperations) * 100);

            this.$swal.update({
              html: `
                <div>
                  <p>Processing ${completedOperations} of ${selectedIds.length} appointments...</p>
                  <div class="progress mt-3" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-info"
                      role="progressbar" aria-valuenow="${progressPercent}" aria-valuemin="0" aria-valuemax="100"
                      style="width: ${progressPercent}%">${progressPercent}%</div>
                  </div>
                </div>
              `
            });

            // If all operations completed, show summary
            if (completedOperations === totalOperations) {
              this.$swal.close();

              setTimeout(() => {
                // Display detailed results
                this.$swal.fire({
                  icon: successCount > 0 ? 'success' : 'error',
                  title: 'Bulk Operation Complete',
                  html: `
                    <div class="text-left">
                      <p><strong>${successCount}</strong> of ${totalOperations} appointments successfully deleted.</p>
                      ${failedIds.length > 0 ? `<p>${failedIds.length} appointments could not be deleted.</p>` : ''}
                    </div>
                  `,
                  confirmButtonText: 'Done',
                });

                // Clear selected appointments
                this.patientNumber = [];

                // Reset selection state
                this.selectAll = 0;

                // Trigger parent component to reload appointment list
                this.$emit("reloadAppointment");
                this.$emit("enabledDeleteBtn", this.patientNumber);
              }, 500);
            }
          };

          // Process each selected appointment with individual API calls in parallel
          // We're using a specific endpoint for appointment deletion
          selectedIds.forEach(id => {
            get("appointment_delete", { id })
              .then(response => {
                if (response.data?.status === true) {
                  successCount++;
                } else {
                  failedIds.push(id);
                }
                updateProgress();
              })
              .catch(error => {
                console.error('Error deleting appointment ID', id, ':', error);
                failedIds.push(id);
                updateProgress();
              });
          });
        } else {
          // If canceled
          this.$emit("disableMultiDeleteButton");
        }
      } catch (error) {
        console.error(error);

        // Show error message
        await this.$swal.fire({
          icon: "error",
          title: "Error",
          text: this.formTranslation.common.internal_server_error,
        });
      }
    },
    handleAppointmentPrint(appointmentId) {
      let but = $("#appointment_print_" + appointmentId);
      but.prop("disabled", true);
      but.html(`<i class='fa fa-sync fa-spin'> </i>`);
      get("get_appointment_print", { id: appointmentId })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            $(response.data.data).printArea({});
          }
          setTimeout(() => {
            but.prop("disabled", false);
            but.html(`<i class='fa fa-print'> </i>`);
          }, 1000);
        })
        .catch((error) => {
          but.prop("disabled", false);
          but.html(`<i class='fa fa-print'> </i>`);
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getInitials(name) {
      if (name !== undefined && name !== "" && name !== null) {
        const patient_name = name.split(" ");
        const initials = patient_name.map((patient_name) =>
          patient_name.charAt(0).toUpperCase()
        );
        return initials.join("");
      } else {
        return " - ";
      }
    },
    customFormOpen(custom_form, id, editAble) {
      this.appointmentCustomFormData = custom_form;
      this.appointmentCustomFormData.module_id = id;
      this.appointmentCustomFormModal = true;
      this.appointmentCustomFormViewMode = !editAble;
    },
  },
  computed: {
    clinic_id() {
      const clinic = this.$store.state.userDataModule.clinic;
      return {
        id: clinic.id,
        label: clinic.name,
      };
    },
    user_data() {
      return this.$store.state.userDataModule.user;
    },
    doctor_id() {
      if (this.getUserRole() === "doctor") {
        return {
          id: this.user_data.ID,
          label: this.user_data.display_name,
          enableTeleMed: this.user_data.enableTeleMed,
          timeSlot: this.user_data.timeSlot,
        };
      } else {
        return {};
      }
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    currentDateTime() {
      return moment().format("YYYY-MM-DD HH:mm:ss");
    },
    // Get today's appointments
    todayAppointments() {
      // Apply filters consistently with the main list
      let filteredAppointments = this.appointmentList.filter(appointment => {
        return appointment.appointment_start_date === this.currentDate;
      });

      // If status filter is applied (and not "all"), respect it
      if (this.filter_status !== "all" && this.filter_status !== "") {
        filteredAppointments = filteredAppointments.filter(appointment => {
          return appointment.status === this.filter_status;
        });
      }

      return filteredAppointments;
    },
    // Get upcoming appointments
    upcomingAppointments() {
      // Apply filters consistently with the main list
      let filteredAppointments = this.appointmentList.filter(appointment => {
        return appointment.appointment_start_date > this.currentDate;
      });

      // If status filter is applied (and not "all"), respect it
      if (this.filter_status !== "all" && this.filter_status !== "") {
        filteredAppointments = filteredAppointments.filter(appointment => {
          return appointment.status === this.filter_status;
        });
      }

      return filteredAppointments;
    },
    // Get past appointments
    pastAppointments() {
      // Apply filters consistently with the main list
      let filteredAppointments = this.appointmentList.filter(appointment => {
        return appointment.appointment_start_date < this.currentDate;
      });

      // If status filter is applied (and not "all"), respect it
      if (this.filter_status !== "all" && this.filter_status !== "") {
        filteredAppointments = filteredAppointments.filter(appointment => {
          return appointment.status === this.filter_status;
        });
      }

      return filteredAppointments;
    },
    // Check if we have any today's appointments
    hasTodayAppointments() {
      return this.todayAppointments.length > 0;
    },
    // Check if we have any upcoming appointments
    hasUpcomingAppointments() {
      return this.upcomingAppointments.length > 0;
    },
    // Check if we have any past appointments
    hasPastAppointments() {
      return this.pastAppointments.length > 0;
    },
  },
  watch: {
    appointmentData(val, oldVal) {
      this.appointmentList = [];
      this.appointmentList = this.appointmentData;
      this.isLoading = false;
      this.$emit("updateAppointmentList", this.appointmentList);
      // for (let i=0; i <= val.length; i++) {
      //     let delay = i * 200;
      //     setTimeout(() => {
      //         if (i === (val.length)) {
      //             this.addAppointment = true;
      //         } else {
      //             let checkDuplicate = this.appointmentList.filter((item) => this.appointmentData[i] !== undefined && item.id === this.appointmentData[i].id);
      //             if(checkDuplicate.length === 0) {
      //                 if(this.appointmentData[i] !== undefined) {
      //                     this.appointmentList.push(this.appointmentData[i]);
      //                 }
      //             }
      //         }
      //       if(val.length === i){
      //         this.isLoading = false ;
      //         this.$emit('updateAppointmentList',this.appointmentList);
      //       }
      //     }, delay);
      // }
    },
    enable_delete_multiple() {
      this.selectAll = this.enable_delete_multiple;
    },
    delete_multiple_appointment() {
      this.selectAll = this.delete_multiple_appointment != true;
      if (this.delete_multiple_appointment == true) {
        this.deleteMultipleAppointment();
      }
    },
    filter_status() {
      this.isLoading = true;
      this.patientNumber = [];
      this.$emit("enabledDeleteBtn", this.patientNumber);
    },
  },
  filters: {
    serviceFiler: function (value) { },
  },
};
</script>
<style scoped>
.cpt {
  cursor: pointer;
}

.custom-control {
  padding-left: 3.75rem !important;
}

@media (max-width: 767px) {
  .text-heading {
    display: none;
  }
}
</style>