<template>
  <div class="bg-white rounded-lg shadow-sm mb-6">
    <!-- Header -->
    <div class="flex items-center p-6 border-b border-gray-200">
      <div class="bg-black p-2 rounded-lg">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
      <div class="ml-4">
        <h2 class="text-lg font-semibold text-gray-900">Schedule Management</h2>
        <p class="text-sm text-gray-500">
          Configure clinic and doctor schedules
        </p>
      </div>
    </div>

    <!-- Form Content -->
    <form
      id="clinic-schedule-form"
      @submit.prevent="handleSubmit"
      :novalidate="true"
      class="p-6"
    >
      <div class="bg-gray-50 rounded-lg p-6">
        <!-- Form Title -->
        <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule Absence</h3>
        
        <!-- Form Layout -->
        <div class="space-y-6">
          <!-- First Row: Holiday Of Selection and Doctor/Clinic Selection -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Holiday Of Selection -->
            <div v-if="getUserRole() !== 'doctor'" class="space-y-2">
              <label
                for="module_type"
                class="block text-sm font-medium text-gray-700"
              >
                {{ formTranslation.clinic_schedule.holiday_of }}
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <multi-select
                  deselect-label=""
                  select-label=""
                  v-model="clinicScheduleRequest.module_type"
                  @select="handleModuleChange"
                  id="module_type"
                  :tag-placeholder="
                    formTranslation.clinic_schedule.tag_module_type_plh
                  "
                  :placeholder="formTranslation.clinic_schedule.select_modulr_plh"
                  label="label"
                  track-by="id"
                  :allow-empty="false"
                  :options="clinicModule"
                  @input="changeType"
                  class="border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                />
                <button
                  type="button"
                  @click="clinicScheduleRequest.module_type = {}"
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              <p
                v-if="submitted && !$v.clinicScheduleRequest.module_type.required"
                class="text-sm text-red-600"
              >
                {{ formTranslation.clinic_schedule.module_type_required }}
              </p>
            </div>

            <!-- Doctor/Clinic Selection -->
            <div v-if="getUserRole() !== 'doctor'" class="space-y-2">
              <!-- Doctor Selection -->
              <div v-if="isDoctorModule">
                <label
                  for="specialties"
                  class="block text-sm font-medium text-gray-700"
                >
                  {{ formTranslation.common.doctor }}
                  <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <multi-select
                    deselect-label=""
                    select-label=""
                    v-model="clinicScheduleRequest.module_id"
                    id="select-module-type-id1"
                    :tag-placeholder="
                      formTranslation.clinic_schedule.tag_doctors_plh
                    "
                    :placeholder="formTranslation.clinic_schedule.select_doc_plh"
                    label="label"
                    track-by="id"
                    :loading="doctorMultiselectLoader"
                    :disabled="doctorMultiselectLoader"
                    :options="doctors"
                    :searchable="true"
                    class="border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                  />
                  <button
                    type="button"
                    @click="clinicScheduleRequest.module_id = ''"
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
                <p
                  v-if="
                    submitted && !$v.clinicScheduleRequest.module_id.id.required
                  "
                  class="text-sm text-red-600"
                >
                  {{ formTranslation.appointments.doc_required }}
                </p>
              </div>

              <!-- Clinic Selection -->
              <div v-if="isClinicModule">
                <label
                  for="specialties"
                  class="block text-sm font-medium text-gray-700"
                >
                  {{ formTranslation.common.clinic }}
                  <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <multi-select
                    deselect-label=""
                    select-label=""
                    v-model="clinicScheduleRequest.module_id"
                    id="select-module-type-id"
                    :tag-placeholder="
                      formTranslation.clinic_schedule.plh_tag_clinic
                    "
                    :placeholder="formTranslation.clinic.select_clinic"
                    label="label"
                    track-by="id"
                    :loading="
                      userData.addOns.kiviPro != true && clinicMultiselectLoader
                    "
                    :disabled="
                      userData.addOns.kiviPro != true && clinicMultiselectLoader
                    "
                    :options="clinics"
                    :searchable="true"
                    class="border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                  />
                  <button
                    type="button"
                    @click="clinicScheduleRequest.module_id = ''"
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
                <p
                  v-if="
                    submitted && !$v.clinicScheduleRequest.module_id.id.required
                  "
                  class="text-sm text-red-600"
                >
                  {{ formTranslation.reports.tag_plh_select_clinic }}
                </p>
              </div>
            </div>
          </div>
          
          <!-- Second Row: Date Selection -->
          <div class="border-t border-gray-200 pt-4">
            <h4 class="text-md font-medium text-gray-800 mb-3">Absence Period</h4>
            <div class="grid grid-cols-1 gap-4">
              <!-- Schedule Date Picker -->
              <div class="space-y-2">
                <label
                  for="from_date"
                  class="block text-sm font-medium text-gray-700"
                >
                  {{ formTranslation.clinic_schedule.schedule_date }}
                  <span class="text-red-500">*</span>
                </label>
                <vc-date-picker
                  id="schedule-date"
                  title-position="left"
                  v-model="clinicScheduleRequest.scheduleDate"
                  mode="range"
                  ref="holidayDatePicker"
                  :min-date="minDate"
                  :update-on-input="false"
                  :popover="{ placement: 'bottom', visibility: 'click' }"
                  :class="{
                    'border-red-500':
                      submitted && $v.clinicScheduleRequest.scheduleDate.$error,
                  }"
                >
                  <template v-slot="{ inputValue }">
                    <input
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                      readonly
                      :value="inputValue"
                      :placeholder="
                        formTranslation.clinic_schedule.select_schedule_date_plh
                      "
                    />
                  </template>
                </vc-date-picker>
                <p
                  v-if="
                    submitted &&
                    (!$v.clinicScheduleRequest.scheduleDate.start.required ||
                      !$v.clinicScheduleRequest.scheduleDate.end.required)
                  "
                  class="text-sm text-red-600"
                >
                  {{ formTranslation.clinic_schedule.schedule_date_required }}
                </p>
              </div>
            </div>
          </div>
          
          <!-- Third Row: Time Selection -->
          <div class="border-t border-gray-200 pt-4">
            <h4 class="text-md font-medium text-gray-800 mb-3">Time Settings</h4>
            
            <!-- All Day Toggle -->
            <div class="mb-4">
              <div class="flex items-center bg-blue-50 p-3 rounded-md">
                <input 
                  type="checkbox" 
                  id="all-day-toggle" 
                  v-model="clinicScheduleRequest.allDay" 
                  class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  @change="toggleAllDay"
                />
                <label for="all-day-toggle" class="ml-2 block text-sm font-medium text-gray-700">
                  All Day Absence
                </label>
              </div>
            </div>
            
            <!-- Time Selection (visible only when All Day is unchecked) -->
            <div v-if="!clinicScheduleRequest.allDay" class="bg-gray-100 p-4 rounded-md">
              <p class="text-sm text-gray-600 mb-3">Select specific hours for the absence period:</p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <label for="start_time" class="block text-sm font-medium text-gray-700">
                    Start Time
                    <span class="text-red-500">*</span>
                  </label>
                  <input 
                    type="time" 
                    id="start_time" 
                    v-model="clinicScheduleRequest.startTime" 
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'border-red-500': submitted && !clinicScheduleRequest.startTime && !clinicScheduleRequest.allDay }"
                  />
                  <p v-if="submitted && !clinicScheduleRequest.startTime && !clinicScheduleRequest.allDay" class="text-sm text-red-600">
                    Start time is required
                  </p>
                </div>
                
                <div class="space-y-2">
                  <label for="end_time" class="block text-sm font-medium text-gray-700">
                    End Time
                    <span class="text-red-500">*</span>
                  </label>
                  <input 
                    type="time" 
                    id="end_time" 
                    v-model="clinicScheduleRequest.endTime" 
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'border-red-500': submitted && !clinicScheduleRequest.endTime && !clinicScheduleRequest.allDay }"
                  />
                  <p v-if="submitted && !clinicScheduleRequest.endTime && !clinicScheduleRequest.allDay" class="text-sm text-red-600">
                    End time is required
                  </p>
                </div>
              </div>
              <p v-if="timeError" class="mt-2 text-sm text-red-600">
                {{ timeError }}
              </p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            @click="closeForm"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
          >
            <svg
              class="mr-2 h-4 w-4 text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
            {{ formTranslation.common.cancel }}
          </button>

          <button
            type="submit"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
            :disabled="loading"
          >
            <span v-if="!loading">
              <svg
                class="mr-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                />
              </svg>
              {{ formTranslation.common.save }}
            </span>
            <span v-else class="flex items-center">
              <svg
                class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                />
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              {{ formTranslation.common.loading }}
            </span>
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import { get, post } from "../../config/request";
import { required, numeric } from "vuelidate/lib/validators";
import {
  validateForm,
  minTime,
  maxTime,
  validateTimeSlot,
  objToTime,
} from "../../config/helper";

export default {
  data: () => {
    return {
      minDate: new Date(),
      isDoctorModule: true,
      isClinicModule: false,
      scheduleDate: {
        start: new Date(),
        end: new Date(),
      },
      defaultClinic: {},
      submitted: false,
      loading: false,
      selectModuleID: {},
      selectModuleType: {
        id: "doctor",
        label: "Doctor",
      },
      clinicScheduleRequest: {},
      clinicScheduleTitle: "Create holiday",
      clinicScheduleButtonText: '<i class="fa fa-save"></i> Save',
      clinicModule: [
        {
          id: "clinic",
          label: "Clinic",
        },
        {
          id: "doctor",
          label: "Doctor",
        },
      ],
      doctors: [],
      clinics: [],
      clinicMultiselectLoader: true,
      doctorMultiselectLoader: true,
      timeError: null,
    };
  },
  props: ["holidayId", "holidayDetail"],
  validations: {
    clinicScheduleRequest: {
      module_type: { required },
      scheduleDate: {
        start: { required },
        end: { required },
      },
      module_id: {
        id: { required },
      },
      // We'll validate time fields manually in the handleSubmit method
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init: function () {
      setTimeout(() => {
        this.clinicScheduleRequest = this.defaultClinicScheduleRequest();
        this.clinicRequest = this.defaultClinicRequest();
        this.getDoctorsData();
        //this.getClinicData();
      }, 1000);
    },
    handleSubmit: function () {
      this.submitted = true;
      this.$v.$touch();

      // Validate time fields if all day is not checked
      if (!this.clinicScheduleRequest.allDay && !this.validateTimeFields()) {
        this.loading = false;
        return;
      }

      if (this.$v.clinicScheduleRequest.$invalid) {
        this.loading = false;
        return;
      }

      // Create a deep copy of the request data to avoid modifying the original
      let clinicScheduleRequest = JSON.parse(JSON.stringify(this.clinicScheduleRequest));
      
      // Format dates properly
      try {
        clinicScheduleRequest.scheduleDate.start = moment(
          this.clinicScheduleRequest.scheduleDate.start
        ).format("YYYY-MM-DD");
        
        clinicScheduleRequest.scheduleDate.end = moment(
          this.clinicScheduleRequest.scheduleDate.end
        ).format("YYYY-MM-DD");
        
        // Add time fields
        clinicScheduleRequest.allDay = this.clinicScheduleRequest.allDay;
        
        if (!clinicScheduleRequest.allDay) {
          clinicScheduleRequest.startTime = this.clinicScheduleRequest.startTime;
          clinicScheduleRequest.endTime = this.clinicScheduleRequest.endTime;
        } else {
          clinicScheduleRequest.startTime = null;
          clinicScheduleRequest.endTime = null;
        }
      } catch (e) {
        console.error("Date formatting error:", e);
        this.$swal.fire({
          icon: "error",
          title: "Error",
          text: "Invalid date format. Please select valid dates.",
          showConfirmButton: true,
        });
        return;
      }

      // Confirm before saving
      this.$swal.fire({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        text: this.holidayId && this.holidayId !== -1 
          ? this.formTranslation.common.update_confirmation 
          : this.formTranslation.common.cancel_date,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: this.formTranslation.common.yes,
        cancelButtonText: this.formTranslation.common.cancel,
      }).then((result) => {
        if (result.isConfirmed) {
          this.submitted = true;
          this.loading = true;

          // Show loading state
          this.$swal.fire({
            title: this.holidayId && this.holidayId !== -1 ? "Updating..." : "Saving...",
            text: "Please wait while we process your request",
            allowOutsideClick: false,
            didOpen: () => {
              this.$swal.showLoading();
            },
          });

          // If we have an ID, include it in the request for update
          if (this.holidayId && this.holidayId !== -1) {
            clinicScheduleRequest.id = this.holidayId;
          }

          post("clinic_schedule_save", clinicScheduleRequest)
            .then((response) => {
              if (response.data.status !== undefined && response.data.status === true) {
                // Success scenario
                this.$swal.fire({
                  icon: "success",
                  title: "Success",
                  text: response.data.message,
                  showConfirmButton: false,
                  timer: 1500,
                });

                // Close the form and refresh the list
                this.isCollapse(false);
                this.getHoliday();
                this.closeForm();
              } else {
                // Error scenario
                this.$swal.fire({
                  icon: "error",
                  title: "Error",
                  text: response.data.message || "Failed to save schedule",
                  showConfirmButton: true,
                });
              }

              this.loading = false;
              this.submitted = false;
            })
            .catch((error) => {
              // Network or other errors
              console.error("Save error:", error);
              
              this.$swal.fire({
                icon: "error",
                title: "Error",
                text: this.formTranslation.common.internal_server_error,
                showConfirmButton: true,
              });

              this.submitted = false;
              this.loading = false;
            });
        }
      });
    },
    changeType: function (data, index) {
      this.clinicScheduleRequest.module_id = "";
      if (
        this.clinicScheduleRequest.module_type !== undefined &&
        this.clinicScheduleRequest.module_type.id === "doctor"
      ) {
        this.isDoctorModule = true;
        this.isClinicModule = false;
      } else if (
        this.clinicScheduleRequest.module_type !== undefined &&
        this.clinicScheduleRequest.module_type.id === "clinic"
      ) {
        this.isClinicModule = true;
        this.isDoctorModule = false;
      } else {
        this.isDoctorModule = false;
        this.isClinicModule = false;
        let module_id = {
          module_id: {
            id: this.defaultClinicData.id,
          },
        };
        this.clinicScheduleRequest = module_id;
      }
      // this.clinicScheduleRequest = {}
    },
    toggleAllDay: function() {
      // Clear time fields if all day is checked
      if (this.clinicScheduleRequest.allDay) {
        this.clinicScheduleRequest.startTime = null;
        this.clinicScheduleRequest.endTime = null;
        this.timeError = null;
      }
    },
    
    validateTimeFields: function() {
      // Skip validation if all day is checked
      if (this.clinicScheduleRequest.allDay) {
        return true;
      }
      
      // Check if both time fields are filled
      if (!this.clinicScheduleRequest.startTime || !this.clinicScheduleRequest.endTime) {
        return false;
      }
      
      // Check if end time is after start time
      if (this.clinicScheduleRequest.startTime >= this.clinicScheduleRequest.endTime) {
        this.timeError = "End time must be after start time";
        return false;
      }
      
      this.timeError = null;
      return true;
    },
    
    defaultClinicScheduleRequest: function () {
      let module_id = {
        id: "",
        label: "",
      };
      if (this.getUserRole() === "doctor") {
        let userData = this.loginUserData;
        module_id = {
          id: userData.ID,
          label: userData.display_name,
        };
      }
      return {
        id: "",
        module_type: {
          id: "doctor",
          label: "Doctor",
        },
        scheduleDate: {
          start: new Date(),
          end: new Date(),
        },
        module_id: module_id,
        description: "",
        status: 1,
        allDay: true,
        startTime: null,
        endTime: null
      };
    },
    getDoctorsData: function () {
      this.doctorMultiselectLoader = true;
      get("get_static_data", {
        data_type: "clinic_doctors",
        clinic_id: this.defaultClinicData.id,
      })
        .then((data) => {
          this.doctorMultiselectLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            this.doctors = data.data.data;
          }
        })
        .catch((error) => {
          this.doctorMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getClinicData: function () {
      this.clinicMultiselectLoader = true;
      get("get_static_data", {
        data_type: "clinics",
      })
        .then((data) => {
          this.clinicMultiselectLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            this.clinics = data.data.data;
            if (this.clinics.length > 0) {
              this.clinicScheduleRequest.module_id = [];

              this.clinicScheduleRequest.module_id = Object.assign(
                {},
                this.clinics[0]
              );
            }
          }
        })
        .catch((error) => {
          this.clinicMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    defaultClinicRequest: function () {
      return {
        searchKey: "",
        searchValue: "",
        offset: 0,
        limit: 10,
      };
    },
    editScheduleData: function (id) {
      if (id !== undefined && id !== null && id !== "") {
        // Show loading state
        this.$swal.fire({
          title: 'Loading...',
          text: 'Please wait while we fetch the data',
          allowOutsideClick: false,
          didOpen: () => {
            this.$swal.showLoading();
          }
        });
        
        // Clear the date field to avoid any conflicts
        this.clinicScheduleRequest.scheduleDate = "";
        
        get("clinic_schedule_edit", { id: id })
          .then((data) => {
            this.$swal.close();
            
            if (data.data.status !== undefined && data.data.status === true) {
              // Store the response data
              this.clinicScheduleRequest = data.data.data;
              
              // Set the module type flags
              if (this.clinicScheduleRequest.module_type.id === "clinic") {
                this.isDoctorModule = false;
                this.isClinicModule = true;
              } else {
                this.isDoctorModule = true;
                this.isClinicModule = false;
              }

              // Ensure dates are properly formatted as Date objects
              try {
                this.clinicScheduleRequest.scheduleDate = {
                  start: new Date(
                    this.clinicScheduleRequest.scheduleDate.start + " 00:00"
                  ),
                  end: new Date(
                    this.clinicScheduleRequest.scheduleDate.end + " 00:00"
                  ),
                };
              } catch (e) {
                console.error("Date parsing error:", e);
                // Fallback to current date if there's an error
                this.clinicScheduleRequest.scheduleDate = {
                  start: new Date(),
                  end: new Date()
                };
              }
              
              // Force update to ensure UI reflects the changes
              this.$forceUpdate();
            } else {
              this.$swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.data.message || this.formTranslation.clinic_schedule.schedule_not_found,
                showConfirmButton: true
              });
              
              // Navigate back if there's an error
              this.$router.push({ name: "clinic-schedule" });
            }
          })
          .catch((error) => {
            this.$swal.close();
            console.error("Failed to fetch schedule data:", error);
            
            this.$swal.fire({
              icon: 'error',
              title: 'Error',
              text: this.formTranslation.common.internal_server_error,
              showConfirmButton: true
            });
          });
      } else {
        this.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: this.formTranslation.clinic_schedule.schedule_not_found,
          showConfirmButton: true
        });
      }
    },
    getHoliday() {
      this.$emit("getClinicScheduleList");
    },
    closeForm() {
      this.$emit("closeForm");
      this.clinicScheduleRequest = this.defaultClinicScheduleRequest();
    },
    isCollapse(value) {
      this.$emit("closeForm", value);
    },
    handleModuleChange: function (selected) {
      if (selected.id !== undefined && selected.id === "clinic") {
        this.getClinicData();
      }
    },
  },
  computed: {
    defaultClinicData() {
      return this.$store.state.userDataModule.clinic;
    },
    loginUserData() {
      return this.$store.state.userDataModule.user;
    },
    // formTranslation: function () {
    //   return this.$store.state.staticDataModule.langTranslateData ;
    // },
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
  watch: {
    holidayId(holidayId, oldVal) {
      if (holidayId !== undefined && holidayId !== -1) {
        this.clinicScheduleTitle =
          this.formTranslation.clinic_schedule.editholiday;
        this.clinicScheduleButtonText =
          '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
        this.editScheduleData(holidayId);
      } else {
        this.clinicScheduleRequest = this.defaultClinicScheduleRequest();
      }
    },
    holidayDetail: function (newVal) {
      if (!newVal || Object.keys(newVal).length === 0) {
        return;
      }
      
      // Create a deep copy to avoid reference issues
      const holidayData = JSON.parse(JSON.stringify(newVal));
      
      // Set module type
      if (holidayData.module_type) {
        // Handle both string and object formats
        if (typeof holidayData.module_type === 'string') {
          this.clinicScheduleRequest.module_type = {
            id: holidayData.module_type,
            label: holidayData.module_type === 'doctor' ? 'Doctor' : 'Clinic'
          };
        } else {
          this.clinicScheduleRequest.module_type = holidayData.module_type;
        }
        
        // Set module flags based on type
        if (
          (typeof holidayData.module_type === 'string' && holidayData.module_type === 'doctor') ||
          (holidayData.module_type.id && holidayData.module_type.id === 'doctor')
        ) {
          this.isDoctorModule = true;
          this.isClinicModule = false;
        } else if (
          (typeof holidayData.module_type === 'string' && holidayData.module_type === 'clinic') ||
          (holidayData.module_type.id && holidayData.module_type.id === 'clinic')
        ) {
          this.isClinicModule = true;
          this.isDoctorModule = false;
        } else {
          this.isDoctorModule = false;
          this.isClinicModule = false;
        }
      }
      
      // Set module ID if available
      if (holidayData.module_id) {
        this.clinicScheduleRequest.module_id = holidayData.module_id;
      }
      
      // Set dates if available
      if (holidayData.start_date && holidayData.end_date) {
        try {
          this.clinicScheduleRequest.scheduleDate = {
            start: new Date(holidayData.start_date),
            end: new Date(holidayData.end_date)
          };
        } catch (e) {
          console.error("Date parsing error:", e);
        }
      }
      
      // Set time fields if available
      this.clinicScheduleRequest.allDay = holidayData.all_day === undefined ? true : !!holidayData.all_day;
      
      if (!this.clinicScheduleRequest.allDay) {
        this.clinicScheduleRequest.startTime = holidayData.start_time || null;
        this.clinicScheduleRequest.endTime = holidayData.end_time || null;
      } else {
        this.clinicScheduleRequest.startTime = null;
        this.clinicScheduleRequest.endTime = null;
      }
      
      // Force update to ensure UI reflects the changes
      this.$forceUpdate();
    },
  },
};
</script>
