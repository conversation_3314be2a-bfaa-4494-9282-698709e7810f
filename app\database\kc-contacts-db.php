<?php
require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

global $wpdb;
$charset_collate = $wpdb->get_charset_collate();

// Table name with WordPress prefix
$table_name = $wpdb->prefix . 'kc_contacts';

$sql = "CREATE TABLE `{$table_name}` (
    id bigint(20) NOT NULL AUTO_INCREMENT,    
    name varchar(191) NOT NULL,
    email varchar(191) NULL,
    phone varchar(30) NULL,
    type varchar(50) NOT NULL DEFAULT 'general',
    address text NULL,
    city varchar(100) NULL,
    state varchar(100) NULL,
    country varchar(100) NULL,
    postal_code varchar(20) NULL,
    notes text NULL,
    clinic_id bigint(20) UNSIGNED NULL,
    added_by bigint(20) UNSIGNED NOT NULL,
    status tinyint(1) UNSIGNED NOT NULL DEFAULT 1,
    created_at datetime NOT NULL,
    updated_at datetime NULL,
    PRIMARY KEY (id)
) $charset_collate;";

maybe_create_table($table_name, $sql);