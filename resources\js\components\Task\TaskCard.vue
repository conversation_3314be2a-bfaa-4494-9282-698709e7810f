<template>
  <div class="bg-white rounded-lg shadow-md p-4 transition-transform hover:scale-[1.02] border">
    <!-- Header -->
    <div class="flex justify-between items-start">
      <span class="text-xs font-medium px-2 py-1 rounded-md" :class="{
        'bg-green-100 text-green-600': task.priority === 'low',
        'bg-yellow-100 text-yellow-600': task.priority === 'medium',
        'bg-red-100 text-red-600': task.priority === 'high'
      }">
        {{ getPriorityLabel(task.priority) }}
      </span>

      <div class="relative">
        <button class="text-gray-500 hover:text-gray-700 focus:outline-none" @click.stop="toggleDropdown">
          <i class="ri-more-2-fill"></i>
        </button>

        <ul v-if="dropdownOpen" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border p-2">
          <li>
            <button @click.stop.prevent="$emit('view-task', task.id)" class="dropdown-item">
              <i class="ri-eye-line me-2"></i> {{ formTranslation.common.view || 'View' }}
            </button>
          </li>
          <li>
            <button @click.stop.prevent="$emit('edit-task', task.id)" class="dropdown-item">
              <i class="ri-edit-line me-2"></i> {{ formTranslation.common.edit || 'Edit' }}
            </button>
          </li>
          <li class="border-t my-2"></li>
          <li v-for="status in statusOptions" :key="status.key">
            <button v-if="task.status !== status.key" @click.stop.prevent="changeStatus(task.id, status.key)"
              class="dropdown-item">
              <i :class="status.icon" class="me-2"></i> {{ getStatusLabel(status.label) }}
            </button>
          </li>
        </ul>
      </div>
    </div>

    <!-- Title -->
    <h6 class="mt-2 text-lg font-semibold" :class="{ 'line-through text-gray-500': completed || cancelled }">
      {{ task.title }}
    </h6>

    <!-- Description -->
    <p v-if="task.description" class="text-sm text-gray-600 line-clamp-2 mt-1">
      {{ truncateDescription(task.description) }}
    </p>

    <!-- Footer -->
    <div class="flex justify-between items-center mt-3 text-sm">
      <!-- Due Date -->
      <div v-if="task.due_date" :class="{ 'text-red-500 font-bold': isTaskOverdue(task) }">
        <i class="ri-calendar-2-line me-1"></i>
        {{ formatDate(task.due_date) }}
      </div>

      <!-- Assignees -->
      <div class="flex items-center">
        <template v-if="task.assignees && task.assignees.length">
          <div v-for="(assignee, index) in limitedAssignees(task.assignees)" :key="index"
            class="w-7 h-7 rounded-full text-white text-xs font-bold flex items-center justify-center border-2 border-white -ml-2"
            :style="{ backgroundColor: getAvatarColor(assignee.name) }" :title="assignee.name">
            {{ getInitials(assignee.name) }}
          </div>
          <div v-if="task.assignees.length > 3"
            class="w-7 h-7 bg-gray-300 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white -ml-2">
            +{{ task.assignees.length - 3 }}
          </div>
        </template>

        <span v-else class="text-gray-400 text-sm flex items-center">
          <i class="ri-user-line mr-1"></i> {{ formTranslation.task.unassigned || 'Unassigned' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskCard',
  props: {
    task: Object,
    completed: Boolean,
    cancelled: Boolean,
  },
  data() {
    return {
      dropdownOpen: false,
      statusOptions: [
        { key: 'pending', label: 'task.move_to_pending', icon: 'ri-time-line' },
        { key: 'in-progress', label: 'task.move_to_in_progress', icon: 'ri-loader-line' },
        { key: 'completed', label: 'task.move_to_completed', icon: 'ri-check-line' },
        { key: 'cancelled', label: 'task.move_to_cancelled', icon: 'ri-close-line' }
      ]
    };
  },
  methods: {
    toggleDropdown() {
      this.dropdownOpen = !this.dropdownOpen;
    },
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        const date = new Date(dateString);

        // Check if the date is valid
        if (isNaN(date.getTime())) {
          console.warn('Invalid date value in TaskCard:', dateString);
          return '-';
        }

        const locale = this.$i18n && this.$i18n.locale ? this.$i18n.locale : 'en-US';
        return new Intl.DateTimeFormat(locale, {
          month: 'short',
          day: 'numeric'
        }).format(date);
      } catch (error) {
        console.error('Error formatting date in TaskCard:', error, 'Date string:', dateString);
        return dateString || '-';
      }
    },
    isTaskOverdue(task) {
      if (['completed', 'cancelled'].includes(task.status) || !task.due_date) return false;
      try {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const dueDate = new Date(task.due_date);

        // Check if the due date is valid
        if (isNaN(dueDate.getTime())) {
          console.warn('Invalid due_date value in TaskCard:', task.due_date);
          return false;
        }

        dueDate.setHours(0, 0, 0, 0);
        return dueDate < today;
      } catch (error) {
        console.error('Error checking if task is overdue in TaskCard:', error, 'Due date:', task.due_date);
        return false;
      }
    },
    getPriorityLabel(priority) {
      const labels = {
        'low': formTranslation.task ? formTranslation.task.priority_low || 'Low' : 'Low',
        'medium': formTranslation.task ? formTranslation.task.priority_medium || 'Medium' : 'Medium',
        'high': formTranslation.task ? formTranslation.task.priority_high || 'High' : 'High'
      };
      return labels[priority] || priority;
    },
    getStatusLabel(key) {
      // Map the status keys to their formTranslation equivalent with fallbacks
      const labels = {
        'task.move_to_pending': formTranslation.task ? formTranslation.task.move_to_pending || 'Move to Pending' : 'Move to Pending',
        'task.move_to_in_progress': formTranslation.task ? formTranslation.task.move_to_in_progress || 'Move to In Progress' : 'Move to In Progress',
        'task.move_to_completed': formTranslation.task ? formTranslation.task.move_to_completed || 'Move to Completed' : 'Move to Completed',
        'task.move_to_cancelled': formTranslation.task ? formTranslation.task.move_to_cancelled || 'Move to Cancelled' : 'Move to Cancelled'
      };
      return labels[key] || key;
    },
    limitedAssignees(assignees) {
      return assignees?.slice(0, 3) || [];
    },
    getInitials(name) {
      if (!name) return '?';
      return name.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('');
    },
    getAvatarColor(name) {
      if (!name) return '#6c757d';
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }
      const colors = [
        '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
        '#1abc9c', '#d35400', '#c0392b', '#16a085', '#8e44ad',
        '#27ae60', '#2980b9', '#f1c40f', '#e67e22', '#ecf0f1'
      ];
      return colors[Math.abs(hash) % colors.length];
    },
    truncateDescription(description, length = 80) {
      return description.length <= length ? description : description.substring(0, length) + '...';
    },
    changeStatus(taskId, status) {
      this.$emit('status-change', { taskId, status });
      // If marking as completed, emit a complete-task event as well
      if (status === 'completed') {
        this.$emit('complete-task', taskId);
      }
    }
  }
};
</script>