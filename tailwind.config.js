module.exports = {
  // Using a more compatible PostCSS 7 config
  future: {
    removeDeprecatedGapUtilities: true,
    purgeLayersByDefault: true,
  },
  purge: {
    enabled: process.env.NODE_ENV === 'production',
    content: [
      "./resources/**/*.{vue,js,ts,jsx,tsx}",
      "./resources/**/*.php",
      "./app/**/*.php",
      "./resources/js/**/*.vue", // Specific path for Vue files
      "./resources/js/components/**/*.vue", // If you have a components folder
      "./resources/views/**/*.php",
      "./resources/js/**/*.js",
    ],
  },
  corePlugins: {
    preflight: true,
  },
  theme: {
    extend: {},
  },
  plugins: [],
  variants: {
    extend: {
      backgroundColor: ["disabled"],
      textColor: ["disabled"],
    },
  },
};
