<template>
  <div>
    <!-- Loading State -->
    <div
      v-if="formLoader"
      class="flex items-center justify-center min-h-screen"
    >
      <loader-component-2></loader-component-2>
    </div>

    <!-- Main Content -->
    <div
      v-if="formTranslation.common !== undefined && !formLoader"
      class="flex flex-col lg:flex-row gap-6"
    >
      <!-- Form Section -->
      <div class="lg:w-3/4">
        <form
          id="clinicDataForm"
          @submit.prevent="handleSubmit"
          :novalidate="true"
        >
          <div class="bg-white rounded-lg shadow">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-xl font-semibold">
                {{ formTranslation.doctor.edit_profile }}
              </h3>
            </div>

            <!-- Form Content -->
            <div class="p-6">
              <!-- Clinic Info Section -->
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.clinic.clinic_info }}
              </h6>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Clinic Name -->
                <div>
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.clinic.clinic_name }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="name"
                    v-model="clinicData.name"
                    :class="{
                      'border-red-500': submitted && $v.clinicData.name.$error,
                    }"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.clinic.plh_clinic_name"
                    type="text"
                  />
                  <p
                    v-if="submitted && $v.clinicData.name.$error"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.name_required }}
                  </p>
                </div>

                <!-- Email -->
                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.email_address }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    v-model="clinicData.email"
                    :class="{
                      'border-red-500': submitted && $v.clinicData.email.$error,
                    }"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.clinic.plh_email"
                    type="email"
                  />
                  <p
                    v-if="submitted && $v.clinicData.email.$error && !$v.clinicData.email.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.email_required }}
                  </p>
                  <p
                    v-else-if="submitted && $v.clinicData.email.$error && !$v.clinicData.email.emailValidate"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.invalid_email }}
                  </p>
                </div>

                <!-- Phone Number -->
                <div>
                  <label
                    for="telephone_no"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <VuePhoneNumberInput
                    v-model="clinicData.telephone_no"
                    id="telephone_no"
                    clearable
                    :default-country-code="defaultCountryCode"
                    @update="contactUpdateHandaler"
                    no-example
                    class="phone-input"
                  />
                  <p
                    v-if="submitted && $v.clinicData.telephone_no.$error"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.contact_num_required }}
                  </p>
                </div>

                <!-- Specialties -->
                <!-- Specialties -->
                <div>
                  <label
                    for="specialties"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.clinic.specialities }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <multi-select
                      deselect-label=""
                      select-label=""
                      v-model="clinicData.specialties"
                      id="specialties"
                      :tag-placeholder="
                        formTranslation.clinic.plh_clinic_specialization
                      "
                      :placeholder="formTranslation.clinic.search_placeholder"
                      label="label"
                      track-by="id"
                      :options="specialization"
                      :multiple="true"
                      :loading="specializationMultiselectLoader"
                      @tag="addNewSpecialization"
                      :taggable="true"
                    ></multi-select>
                  </div>
                  <span class="text-sm text-blue-600 mt-1">
                    {{ formTranslation.clinic.note_specialization }}
                  </span>
                  <p
                    v-if="submitted && $v.clinicData.specialties.$error"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.clinic.clinic_specialities_required }}
                  </p>
                </div>
              </div>

              <!-- Additional sections like Address, Admin Details, etc. follow the same pattern -->
              <!-- Contact Information Section -->
              <hr class="my-6 border-gray-200" />
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.common.contact_info }}
              </h6>
              <div class="space-y-6">
                <!-- Address -->
                <div>
                  <label
                    for="address"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.address }}
                    <span class="text-red-500">*</span>
                  </label>
                  <textarea
                    id="address"
                    v-model="clinicData.address"
                    rows="3"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.clinic.plh_address"
                  ></textarea>
                  <p
                    v-if="submitted && $v.clinicData.address.$error"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.address_required }}
                  </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <!-- Country -->
                  <div>
                    <label
                      for="country"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.country }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="country"
                      v-model="clinicData.country"
                      type="text"
                      :placeholder="formTranslation.clinic.plh_country"
                      class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :class="{
                        'border-red-500':
                          submitted && $v.clinicData.country.$error,
                      }"
                    />
                    <p
                      v-if="submitted && $v.clinicData.country.$error"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.country_required }}
                    </p>
                  </div>

                  <!-- City -->
                  <div>
                    <label
                      for="city"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.city }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="city"
                      v-model="clinicData.city"
                      type="text"
                      :placeholder="formTranslation.clinic.plh_city"
                      class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :class="{
                        'border-red-500':
                          submitted && $v.clinicData.city.$error,
                      }"
                    />
                    <p
                      v-if="submitted && $v.clinicData.city.$error"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.city_required }}
                    </p>
                  </div>

                  <!-- Postal Code -->
                  <div>
                    <label
                      for="postal_code"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.postal_code }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="postal_code"
                      v-model="clinicData.postal_code"
                      type="text"
                      :placeholder="formTranslation.clinic.plh_pcode"
                      class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :class="{
                        'border-red-500':
                          submitted && $v.clinicData.postal_code.$error,
                      }"
                    />
                    <p
                      v-if="submitted && $v.clinicData.postal_code.$error"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.postal_code_required }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Clinic Admin Details Section -->
              <hr class="my-6 border-gray-200" />
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.clinic.clinic_admin_detail }}
              </h6>
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- First Name -->
                    <div>
                      <label
                        for="first_name"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.fname }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        id="first_name"
                        v-model="clinicData.first_name"
                        type="text"
                        :placeholder="formTranslation.clinic.fname_plh"
                        class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        :class="{
                          'border-red-500':
                            submitted && $v.clinicData.first_name.$error,
                        }"
                      />
                      <p
                        v-if="submitted && $v.clinicData.first_name.$error"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.fname_required }}
                      </p>
                    </div>

                    <!-- Last Name -->
                    <div>
                      <label
                        for="last_name"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.lname }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        id="last_name"
                        v-model="clinicData.last_name"
                        type="text"
                        :placeholder="formTranslation.receptionist.lname_plh"
                        class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        :class="{
                          'border-red-500':
                            submitted && $v.clinicData.last_name.$error,
                        }"
                      />
                      <p
                        v-if="submitted && $v.clinicData.last_name.$error"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.lname_required }}
                      </p>
                    </div>

                    <!-- Admin Email -->
                    <div>
                      <label
                        for="user_email"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.email }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        id="user_email"
                        v-model="clinicData.user_email"
                        type="email"
                        :placeholder="formTranslation.clinic.email_plh"
                        class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        :class="{
                          'border-red-500':
                            submitted && $v.clinicData.user_email.$error,
                        }"
                      />
                      <p
                        v-if="submitted && $v.clinicData.user_email.$error"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.email_required }}
                      </p>
                    </div>

                    <!-- Admin Phone -->
                    <div>
                      <label
                        for="mobile_number"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.contact_no }}
                        <span class="text-red-500">*</span>
                      </label>
                      <VuePhoneNumberInput
                        v-model="clinicData.mobile_number"
                        id="mobile_number"
                        clearable
                        :default-country-code="defaultCountryCode_admin"
                        @update="contactUpdateHandaler_admin"
                        class="phone-input"
                        no-example
                      />
                      <p
                        v-if="submitted && $v.clinicData.mobile_number.$error"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.contact_num_required }}
                      </p>
                    </div>

                    <!-- Date of Birth -->
                    <div>
                      <label
                        for="dob"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.dob }}
                      </label>
                      <input
                        type="date"
                        id="doc_birthdate"
                        v-model="clinicData.dob"
                        :max="new Date().toISOString().slice(0, 10)"
                        class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <!-- Gender -->
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {{ formTranslation.common.gender }}
                        <span class="text-red-500">*</span>
                      </label>
                      <div class="space-y-2">
                        <label class="inline-flex items-center mr-4">
                          <input
                            type="radio"
                            v-model="clinicData.gender"
                            value="male"
                            class="form-radio text-blue-600"
                          />
                          <span class="ml-2">{{
                            formTranslation.common.male
                          }}</span>
                        </label>
                        <label class="inline-flex items-center mr-4">
                          <input
                            type="radio"
                            v-model="clinicData.gender"
                            value="female"
                            class="form-radio text-blue-600"
                          />
                          <span class="ml-2">{{
                            formTranslation.common.female
                          }}</span>
                        </label>
                        <label
                          v-if="defaultUserRegistrationFormSettingData === 'on'"
                          class="inline-flex items-center"
                        >
                          <input
                            type="radio"
                            v-model="clinicData.gender"
                            value="other"
                            class="form-radio text-blue-600"
                          />
                          <span class="ml-2">{{
                            formTranslation.common.other
                          }}</span>
                        </label>
                      </div>
                      <p
                        v-if="submitted && $v.clinicData.gender.$error"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.gender_required }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Admin Profile Image -->
                <div class="lg:col-span-1">
                  <div class="flex justify-center">
                    <div class="relative">
                      <div
                        class="w-32 h-32 rounded-full bg-cover bg-center border-4 border-white shadow"
                        :style="'background-image: url(' + adminPreview + ');'"
                      ></div>
                      <button
                        @click="uploadAdmin"
                        class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50"
                      >
                        <i class="fas fa-pencil-alt"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Submit Button -->
              <div class="mt-6 flex justify-end">
                <button
                  type="submit"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  :disabled="loading"
                >
                  <template v-if="!loading">
                    <i class="fa fa-save mr-2"></i>
                    {{ formTranslation.clinic.save_btn }}
                  </template>
                  <template v-else>
                    <i class="fa fa-sync fa-spin mr-2"></i>
                    {{ formTranslation.common.loading }}
                  </template>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Profile Card -->
      <div class="lg:w-1/4">
        <div class="bg-white rounded-lg shadow">
          <!-- Profile Image Upload -->
          <div class="flex justify-center pt-6">
            <div class="relative">
              <!-- Image Preview -->
              <div
                class="w-32 h-32 rounded-full bg-cover bg-center"
                :style="'background-image: url(' + profileImage + ');'"
              ></div>

              <!-- Edit Button -->
              <button
                @click="uploadProfile"
                class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50"
              >
                <i class="fas fa-pencil-alt"></i>
              </button>
            </div>
          </div>

          <!-- Profile Info -->
          <div class="p-6 text-center">
            <h5 class="text-xl font-semibold">{{ clinicData.name }}</h5>
            <div class="text-gray-600 mt-2">{{ clinicData.email }}</div>
            <div class="mt-4" v-if="clinicData.address">
              {{ clinicData.address }}
            </div>
            <div class="mt-2 text-gray-600">
              {{ clinicData.specialties | clinicSpecialityFormat }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { maxLength, minLength, required } from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import {
  alphaSpace,
  postalCode,
  validateForm,
  emailValidate,
} from "../../config/helper";

export default {
  components: {
    VuePhoneNumberInput,
  },
  data: () => {
    return {
      cardTitle: "Edit clinic profile",
      clinicData: {},
      loading: false,
      submitted: false,
      editProfileText: '<i class="fa fa-pen-fancy"></i> Edit Profile',
      buttonText: '<i class="fa fa-plus"></i> Add',
      profileImage: "",
      adminPreview:
        window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png",
      formLoader: true,
      defaultCountryCode: null,
      defaultCountryCode_admin: null,
      defaultUserRegistrationFormSettingData: "on",
      subscription: {
        membership_level: "",
        status: "",
        created: "",
        next_payment_date: "",
        fee: "",
      },
    };
  },
  validations: {
    clinicData: {
      name: {
        required,
      },
      email: {
        required,
        emailValidate,
      },
      telephone_no: {
        required,
        // phoneNumber,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      address: {
        required,
      },
      first_name: {
        required,
      },
      last_name: {
        required,
      },
      mobile_number: {
        required,
        // phoneNumber,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      user_email: {
        required,
        emailValidate,
      },
      // dob: {required},
      specialties: {
        // Custom validator that accepts an empty array or a non-empty array
        required: function(value) {
          // Either it's an array (even if empty) or it has a value
          return Array.isArray(value);
        }
      },
      gender: {
        required,
      },
      city: {
        required,
        alphaSpace,
        maxLength: maxLength(30),
      },
      country: {
        required,
        alphaSpace,
        maxLength: maxLength(30),
      },
      postal_code: {
        required,
        postalCode,
        maxLength: maxLength(12),
      },
      status: { required },
    },
  },
  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();
    this.state = this.defaultClinicData();
    this.profileImage =
      window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png";
    this.init();
  },
  filters: {
    clinicSpecialityFormat: function (Speciality) {
      let result = [];
      let typeOfData = typeof Speciality;
      if (
        (typeOfData === "object" || typeOfData === "array") &&
        Speciality !== undefined &&
        Speciality !== null &&
        Speciality.length > 0
      ) {
        Speciality.map((speciality) => {
          result.push(speciality.label);
        });
        return result.join(" ,");
      } else {
        return "-";
      }
    },
  },
  methods: {
    contactUpdateHandaler: function (val) {
      this.clinicData.country_code = val.countryCode;
      this.clinicData.country_calling_code = val.countryCallingCode;
    },
    contactUpdateHandaler_admin: function (val) {
      this.clinicData.country_code_admin = val.countryCode;
      this.clinicData.country_calling_code_admin = val.countryCallingCode;
    },
    init: function () {
      this.editProfile();
    },

    uploadProfile() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.profileImage = attachment.url;
        _this.clinicData.clinic_profile = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    uploadAdmin() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();

        console.log('Admin image selected:', attachment);
        _this.adminPreview = attachment.url;
        _this.clinicData.profile_image = attachment.id;

        // Log the updated clinicData for debugging
        console.log('Updated clinicData after image selection:', {
          profile_image: _this.clinicData.profile_image,
          adminPreview: _this.adminPreview
        });
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    handleSubmit: function () {
      this.loading = true;
      this.submitted = true;

      // Force validation on all fields
      this.$v.$touch();

      // Debug validation state
      console.log('Form validation state:', {
        isInvalid: this.$v.clinicData.$invalid,
        invalidFields: Object.keys(this.$v.clinicData).filter(key =>
          this.$v.clinicData[key] && this.$v.clinicData[key].$invalid
        )
      });

      // Enhanced error field detection
      this.$nextTick(() => {
        // First try to find elements with border-red-500 class (invalid fields)
        const invalidField = document.querySelector("[class*='border-red-500']");
        if (invalidField) {
          console.log('Found invalid field with border-red-500:', invalidField);
          invalidField.scrollIntoView({ block: "center", behavior: "smooth" });
          // Add a more visible indicator for debugging
          invalidField.style.boxShadow = '0 0 0 2px rgba(239, 68, 68, 0.5)';
        } else {
          // If no invalid field found, try to find error messages
          const errorMessage = document.querySelector(".text-red-500");
          if (errorMessage) {
            console.log('Found error message:', errorMessage.textContent);
            errorMessage.scrollIntoView({ block: "center", behavior: "smooth" });
          } else {
            console.log('No validation errors found in the DOM');
          }
        }
      });

      // Check if form is invalid according to vuelidate
      if (this.$v.clinicData.$invalid) {
        console.log('Form validation failed');

        // Log which fields are invalid for debugging
        const invalidFields = Object.keys(this.$v.clinicData).filter(key =>
          this.$v.clinicData[key] && this.$v.clinicData[key].$invalid
        );

        console.log('Invalid fields:', invalidFields);

        // If there are validation errors, stop submission
        this.loading = false;

        // Show the first invalid field to the user
        this.$nextTick(() => {
          const invalidField = document.querySelector("[class*='border-red-500']");
          if (invalidField) {
            invalidField.scrollIntoView({ block: "center", behavior: "smooth" });
            invalidField.focus();
          }
        });

        return;
      }

      // Additional form validation using DOM
      const isFormValid = validateForm("clinicDataForm");
      console.log('DOM-based form validation result:', isFormValid);

      // Only proceed if the form is valid
      if (isFormValid) {
        // Log the data being sent to the server
        console.log('Submitting clinic data:', JSON.parse(JSON.stringify(this.clinicData)));

        post("clinic_save", this.clinicData)
          .then((response) => {
            this.loading = false;
            console.log('Clinic save response:', response.data);
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              if (response.data.choose_language_updated) {
                this.$store.dispatch(
                  "staticDataModule/refreshDashboardLocale",
                  { self: this }
                );
              }
              this.$store.dispatch("fetchAllClinic", { self: this });
            } else {
              console.error('Clinic save error:', response.data);
              displayErrorMessage(response.data.message || 'Unknown error occurred');
            }
          })
          .catch((error) => {
            console.error('Clinic save exception:', error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      } else {
        console.error('Form validation failed in validateForm()');
        this.loading = false;
        displayErrorMessage(this.formTranslation.common.please_check_form_errors || 'Please check the form for errors');
      }
    },
    defaultClinicData: function () {
      return {
        id: "",
        name: "",
        email: "",
        country_code: "",
        country_calling_code: "",
        telephone_no: "",
        address: "",
        city: "",
        country: "",
        postal_code: "",
        currency_prefix: "$",
        currency_postfix: "",
        // decimal_point: {},
        status: 1,
        specialties: [],
        profile_image: "",
        country_code_admin: "",
        country_calling_code_admin: "",
        choose_language: "",
      };
    },
    editProfile: function () {
      this.formLoader = true;
      this.cardTitle = this.formTranslation.clinic.edit_clinic_Profile;
      this.buttonText =
        '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
      get("clinic_edit", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            console.log('Clinic data loaded:', response.data.data);
            this.clinicData = response.data.data;

            // Ensure all required fields are present
            const requiredFields = ['name', 'email', 'telephone_no', 'address', 'city', 'country',
                                   'postal_code', 'first_name', 'last_name', 'user_email',
                                   'mobile_number', 'gender', 'specialties', 'status'];

            // Set default values for missing fields - using generic defaults
            const defaultValues = {
              address: this.clinicData.address || '',
              city: this.clinicData.city || '',
              country: this.clinicData.country || '',
              postal_code: this.clinicData.postal_code || '',
              gender: this.clinicData.gender || 'male', // Default to male as it's a common default
              status: this.clinicData.status || 1, // Default to active
              specialties: this.clinicData.specialties || []
            };

            const missingFields = requiredFields.filter(field =>
              !this.clinicData[field] && this.clinicData[field] !== 0 && this.clinicData[field] !== false
            );

            if (missingFields.length > 0) {
              console.warn('Missing required fields:', missingFields);

              // Apply default values for missing fields
              missingFields.forEach(field => {
                if (defaultValues[field] !== undefined) {
                  console.log(`Setting default value for ${field}:`, defaultValues[field]);
                  this.clinicData[field] = defaultValues[field];
                }
              });
            }

            // Ensure specialties is an array
            if (!Array.isArray(this.clinicData.specialties)) {
              this.clinicData.specialties = [];
            }

            // We'll keep specialties as an empty array if none are provided
            // This is handled by our updated validation rule that accepts empty arrays

            if (
              response.data.data.country_calling_code !== "" &&
              response.data.data.country_calling_code !== undefined
            ) {
              this.defaultCountryCode = response.data.data.country_code;
            }
            if (
              response.data.data.country_calling_code_admin !== "" &&
              response.data.data.country_calling_code_admin !== undefined
            ) {
              this.defaultCountryCode_admin =
                response.data.data.country_code_admin;
            }
            if (this.clinicData.profile_image) {
              this.adminPreview = this.clinicData.profile_image;
            }
            if (this.clinicData.clinic_profile) {
              this.profileImage = this.clinicData.clinic_profile;
            }
            this.formLoader = false;
            this.clinicData.choose_language =
              this.kc_available_translations.find(
                (el) => el.lang === response.data.data.choose_language
              );
          }
        })
        .catch((error) => {
          this.formLoader = false;
          console.error('Error loading clinic data:', error);
          displayErrorMessage(this.formTranslation.clinic.plh_record_not_found);
        });
    },
    addNewSpecialization: function (value) {
      let specialitiesObj = {
        label: value,
        type: "specialization",
        value: value.replace(" ", "_"),
        status: 1,
      };
      let _this = this;
      post("static_data_save", specialitiesObj)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            _this.clinicData.specialties.push({
              id: response.data.insert_id,
              label: value,
            });
            _this.$store.commit("staticDataModule/ADD_OPTION_STATIC_DATA", {
              dataType: "specialization",
              option: { id: response.data.insert_id, label: value },
            });
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getCountryCodeData: function () {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultCountryCode = response.data.data.country_code;
            this.defaultCountryCode_admin = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getUserRegistrationFormData: function () {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
  },
  computed: {
    specialization() {
      return this.$store.state.staticDataModule.static_data.specialization;
    },
    specializationMultiselectLoader() {
      return this.$store.state.staticDataModule.static_data_loader;
    },
    kc_available_translations() {
      return this.$store.state.userDataModule.user.kc_available_translations;
    },
  },
  watch: {},
};
</script>
<style scoped>
[type="date"] {
  background: #fff
    url(https://cdn1.iconfinder.com/data/icons/cc_mono_icon_set/blacks/16x16/calendar_2.png)
    97% 50% no-repeat;
}
[type="date"]::-webkit-inner-spin-button {
  display: none;
}
[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}
label {
  display: block;
}
#doc_birthdate {
  border: 1px solid #c4c4c4;
  border-radius: 5px;
  background-color: #fff;
  padding: 3px 5px;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.1);
  /* width: 190px; */
  width: 100%;
  height: 45px;
  color: #8c9cad;
}
#doc_birthdate ::placeholder {
  color: #8c9cad;
}
</style>
