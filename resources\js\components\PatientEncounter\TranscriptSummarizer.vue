<!-- components/TemplateSummarizer.vue -->
<template>
    <div class="template-summarizer container-fluid p-4">
        <!-- Template Selection -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="templateSelect" class="form-label">Select Template</label>
                            <select 
                                id="templateSelect"
                                v-model="selectedTemplate"
                                style="width: 100%;"
                                class="form-select"
                                :disabled="isLoadingSummary"
                            >
                                <option value="">Choose a template format</option>
                                <option 
                                    v-for="(template, key) in templates" 
                                    :key="key" 
                                    :value="key"
                                >
                                    {{ formatTemplateName(key) }}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generate Button -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <button
                    @click="generateSummaryCall"
                    :disabled="!selectedTemplate || isLoadingSummary"
                    class="btn btn-primary"
                >
                    <span v-if="isLoadingSummary" class="d-flex align-items-center">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        Processing...
                    </span>
                    <span v-else>Generate Summary</span>
                </button>
            </div>
        </div>

    </div>
</template>

<script>
export default {
    name: 'TemplateSummarizer',

    data() {
        return {
            templates: {
                "consultation_notes_template": "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nNHS Number: [NHS/ID Number]\n...",
                "referral_letter_template": "Receiving Specialist Information:\nName: [Specialist's Name]\nSpecialty: [e.g., Cardiologist]\n...",
                "gp_letter_template": "Doctor Information:\nReferring Doctor: [Full Name]\nClinic: [Clinic Name]\n...",
                "sick_medical_leave_note_template": "Doctor Information:\nName: [Doctor's Full Name]\nClinic: [Clinic Name]\n...",
                "operation_notes_procedure_report_template": "Hospital Information:\nHospital Name: [Hospital Name]\n...",
                "clinic_follow_up_ward_rounds_notes_template": "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\n...",
                "patient_admission_clerking_template": "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\n...",
                "ward_rounds_notes_template": "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\n..."
            },
            selectedTemplate: '',
            summary: '',
            isLoadingSummary: false,
            editMode: false,
            mistralConfig: {
                apiUrl: 'https://api.mistral.ai/v1/chat/completions',
                model: 'open-mistral-nemo',
                systemMessage: `You are Symbi, an AI clinical assistant in the UK based on the SynapseX 1.0 model...` // Your full system message
            }
        }
    },
    watch:{
        isLoadingSummary(val){
            console.log(val);
        }
    },

    methods: {
        formatTemplateName(key) {
            return key
                .replace(/_/g, ' ')
                .replace('template', '')
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ')
                .trim();
        },

        getTemplatePreview(key) {
            const template = this.templates[key];
            const lines = template.split('\n').slice(0, 5);
            return lines.join('\n') + '\n...';
        },

        async generateSummaryCall() {
            if (!this.selectedTemplate) return;
            await this.$emit('generateSummary', this.selectedTemplate);
        },

        async getConsultationData() {
            // Implement this method to get consultation data from your system
            return {
                encounter_id: this.encounterId,
                problems: this.$refs.medical_history_problems?.medicalHistoryList || [],
                observations: this.$refs.medical_history_observation?.medicalHistoryList || [],
                notes: this.$refs.medical_history_note?.medicalHistoryList || [],
                prescription: this.$refs.prescription_ref ? {
                    medicines: this.$refs.prescription_ref.prescriptionList || [],
                    notes: this.$refs.prescription_ref.notes || ''
                } : null
            };
        }
    }
}
</script>

<style scoped>
.template-preview {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.summary-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 0.95rem;
    line-height: 1.5;
}

textarea {
    font-family: monospace;
    resize: vertical;
}

/* Custom styles for better spacing */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
}

/* Optional: Style improvements for the select dropdown */
.form-select {
    cursor: pointer;
}

.form-select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}
</style>