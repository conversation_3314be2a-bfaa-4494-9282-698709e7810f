<!-- FileManagement.vue -->
<template>
  <div class="bg-white border rounded p-3">
    <div class="flex justify-between items-center mb-3">
      <h2 class="font-medium">Files</h2>
      <div class="flex gap-2">
        <button 
          class="flex items-center gap-1.5 px-3 py-1.5 border rounded text-sm hover:bg-gray-50 relative group"
          @click="fetchPdfPassword"
          title="Show Password"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="30" height="30" class="w-5 h-5">
            <circle cx="8" cy="12" r="3.5" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M11 12H20" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M16 12V9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M19 12V10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <span class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">Show Password</span>
        </button>
        <button class="flex items-center gap-1.5 px-3 py-1.5 border rounded text-sm hover:bg-gray-50"
          @click="showUploadModal">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="17 8 12 3 7 8"></polyline>
            <line x1="12" x2="12" y1="3" y2="15"></line>
          </svg>
          Upload
        </button>
        <button class="flex items-center gap-1.5 px-3 py-1.5 border rounded text-sm hover:bg-gray-50"
          @click="showMobileUploadModal">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <rect x="7" y="4" width="10" height="16" rx="1" ry="1"></rect>
            <path d="M11 5h2"></path>
            <path d="M12 17v.01"></path>
          </svg>
          Mobile Upload
        </button>
        <button class="flex items-center gap-1.5 px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-black"
          @click="showTemplateModal">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M8 12h8"></path>
            <path d="M12 8v8"></path>
          </svg>
          Generate Letter
        </button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
    </div>

    <!-- Document List -->
    <div v-else-if="documents.length > 0" class="space-y-2">
      <DocumentItem v-for="doc in documents" :key="doc.id" :document="doc" @edit="handleEdit" @share="openShareModal"
        @download="handleDownload" @delete="handleDelete" />
    </div>

    <!-- No Documents -->
    <div v-else class="text-center py-8 text-gray-500">No documents found</div>

    <!-- Modals -->
    <UploadModal v-if="showUploadDialog" :show="showUploadDialog" :patient-details="patientDetails"
      :editing-document="selectedDocument" :encounter-id="encounterId" @close="closeUploadModal"
      @upload="handleUpload" />

    <ShareModal v-if="showShareDialog" :show="showShareDialog" :document="selectedDocument"
      :patient-details="patientDetails" :patient-meta-data="patientMetaData" @close="closeShareModal"
      @share="handleShare" />

    <TemplateModal :encounter-id="encounterId" v-if="showTemplateDialog" :show="showTemplateDialog"
      :consultation-data="consultationData" :active-form-types="activeFormTypes" :patient-details="patientDetails"
      @close="closeTemplateModal" @select="handleTemplateSelect" @reloadFiles="init" />

    <!-- Mobile Upload QR Code Modal -->
    <div v-if="showMobileUploadDialog"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">Upload from Mobile Device</h3>
          <button @click="closeMobileUploadModal" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="text-center mb-4">
          <p class="text-sm mb-4">Scan this QR code with your mobile device to upload photos or documents directly from
            your phone.</p>

          <div v-if="qrCodeUrl"
            class="mx-auto mb-4 p-2 border rounded-lg bg-white w-64 h-64 flex items-center justify-center">
            <img :src="qrCodeUrl" alt="QR Code for mobile upload" class="max-w-full max-h-full" />
          </div>
          <div v-else class="animate-pulse bg-gray-200 mx-auto w-64 h-64 rounded-lg flex items-center justify-center">
            <span class="text-gray-500">Generating QR code...</span>
          </div>
        </div>

        <div class="flex flex-col space-y-3">
          <div class="p-3 bg-blue-50 text-blue-700 rounded-lg text-sm">
            <strong>Instructions:</strong>
            <ol class="list-decimal pl-4 mt-1">
              <li>Scan the QR code with your phone's camera</li>
              <li>Follow the link to open the upload page</li>
              <li>Take a photo or select a file from your gallery</li>
              <li>Click upload to add the file to this patient's records</li>
            </ol>
          </div>

          <p class="text-center text-sm text-gray-500">
            Mobile upload session will expire in 5 minutes
          </p>

          <div v-if="mobileUploadStatus" class="p-2 rounded-lg text-sm" :class="{
            'bg-green-50 text-green-700': mobileUploadStatus.type === 'success',
            'bg-yellow-50 text-yellow-700': mobileUploadStatus.type === 'pending',
            'bg-red-50 text-red-700': mobileUploadStatus.type === 'error'
          }">
            {{ mobileUploadStatus.message }}
          </div>

          <div class="flex justify-end pt-2">
            <button @click="refreshQRCode"
              class="mr-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg">
              Refresh QR
            </button>
            <button @click="closeMobileUploadModal" class="px-4 py-2 bg-black text-white rounded-lg">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DocumentItem from "./DocumentItem.vue";
import UploadModal from "./UploadModal.vue";
import ShareModal from "./ShareModal.vue";
import TemplateModal from "./TemplateModal.vue";
import { post, get } from "../../config/request";
import { _axios_post } from "../../config/request";
import { displayMessage, displayErrorMessage } from "../../utils/message";

export default {
  name: "FileManagement",

  components: {
    DocumentItem,
    UploadModal,
    ShareModal,
    TemplateModal,
  },

  props: {
    patientDetails: {
      type: Object,
      required: true,
    },
    patientMetaData: {
      type: Object,
      required: true,
    },
    encounterId: {
      type: [String, Number],
      required: true,
    },
    canUpload: {
      type: Boolean,
      default: true,
    },
    canDelete: {
      type: Boolean,
      default: true,
    },
    consultationData: {
      type: Object,
      required: true,
    },
    activeFormTypes: {
      type: Array,
      required: true,
    },
    editDocument: {
      // Add new prop
      type: Object,
      default: null,
    },
  },

  data() {
    return {
      documents: [],
      loading: false,
      showUploadDialog: false,
      showShareDialog: false,
      showTemplateDialog: false,
      showMobileUploadDialog: false,
      selectedDocument: null,
      refreshInterval: null,
      qrCodeUrl: null,
      uploadSessionId: null,
      mobileUploadStatus: null,
      mobileUploadPollingInterval: null,
    };
  },

  mounted() {
    this.init();
  },

  beforeUnmount() {
    this.cleanup();
  },

  methods: {
    init() {
      this.loadDocuments();
      // this.startAutoRefresh();
    },

    cleanup() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
      }

      // Also clear the mobile upload polling interval
      if (this.mobileUploadPollingInterval) {
        clearInterval(this.mobileUploadPollingInterval);
        this.mobileUploadPollingInterval = null;
      }
    },

    async loadDocuments() {
      this.loading = true;
      try {
        const response = await get("get_patient_document_by_appt_id", {
          appointment_id: this.encounterId,
          patient_id: this.patientDetails.patient_id,
        });
        if (response?.data?.status) {
          this.documents = response.data.data || [];
        } else {
          throw new Error(
            response?.data?.message || "Failed to load documents"
          );
        }
      } catch (error) {
        displayErrorMessage("Failed to load documents");
      } finally {
        this.loading = false;
      }
    },

    // startAutoRefresh() {
    //   this.refreshInterval = setInterval(() => {
    //     this.loadDocuments();
    //   }, 5000);
    // },

    // Modal control methods
    showUploadModal() {
      this.showUploadDialog = true;
    },

    closeUploadModal() {
      this.showUploadDialog = false;
      this.selectedDocument = null;
    },

    showTemplateModal() {
      this.showTemplateDialog = true;
    },

    closeTemplateModal() {
      this.showTemplateDialog = false;
    },

    // Mobile Upload QR Code Methods
    showMobileUploadModal() {
      this.showMobileUploadDialog = true;
      this.generateQRCode();
    },

    closeMobileUploadModal() {
      this.showMobileUploadDialog = false;
      this.stopMobileUploadPolling();

      // Clear QR code data
      this.qrCodeUrl = null;
      this.uploadSessionId = null;
      this.mobileUploadStatus = null;
    },

    async fetchPdfPassword() {
      try {
        // Show loading state
        this.$swal.fire({
          title: 'Fetching password...',
          allowOutsideClick: false,
          didOpen: () => {
            this.$swal.showLoading();
          }
        });

        // Replace with your actual API endpoint
        let response = await post('get_document_access_key', {
          patient_id: this.patientDetails.patient_id,
        });

        if (response?.data?.status) {
          const password = response.data.data.password;

          // Close loading and show the password
          this.$swal.fire({
            title: 'Document Password',
            html: `
          <div class="flex items-center justify-center mb-4 mt-2">
            <div class="relative w-full max-w-xs">
              <input type="text" id="password-field" class="border rounded px-3 py-2 w-full pr-10 font-mono bg-gray-50" 
                value="${password}" readonly />
              <button id="copy-button" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-gray-900">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                  <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                </svg>
              </button>
            </div>
          </div>
          <p class="text-sm text-gray-500">This password is required to access the document</p>
        `,
            showConfirmButton: true,
            confirmButtonText: 'Close',
            didOpen: () => {
              // Add copy functionality
              const copyButton = document.getElementById('copy-button');
              const passwordField = document.getElementById('password-field');

              copyButton.addEventListener('click', () => {
                passwordField.select();
                document.execCommand('copy');

                // Show copied tooltip
                const originalInnerHTML = copyButton.innerHTML;
                copyButton.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            `;

                setTimeout(() => {
                  copyButton.innerHTML = originalInnerHTML;
                }, 1500);
              });
            }
          });
        } else {
          throw new Error(response?.data?.message || "Failed to fetch password");
        }
      } catch (error) {
        console.error("Error fetching PDF password:", error);
        this.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to fetch document password. Please try again.'
        });
      }
    },

    async generateQRCode() {
      try {
        this.qrCodeUrl = null;
        this.mobileUploadStatus = {
          type: 'pending',
          message: 'Generating secure upload link...'
        };

        // Generate a new upload session
        const response = await post("create_mobile_upload_session", {
          encounter_id: this.encounterId,
          patient_id: this.patientDetails.patient_id,
          expire_minutes: 5, // Session expires in 30 minutes
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || "Failed to create upload session");
        }

        console.log("Upload session response:", response.data);

        // Get session details
        this.uploadSessionId = response.data.data.session_id;
        this.qrCodeUrl = response.data.data.qr_code_url;

        // Start polling for mobile uploads using this session
        this.startMobileUploadPolling();

        this.mobileUploadStatus = {
          type: 'success',
          message: 'QR code ready. Scan with your mobile device to upload.'
        };
      } catch (error) {
        console.error("Error generating QR code:", error);
        this.mobileUploadStatus = {
          type: 'error',
          message: 'Failed to generate QR code. Please try again.'
        };
      }
    },

    refreshQRCode() {
      // Stop current polling
      this.stopMobileUploadPolling();

      // Generate a new QR code
      this.generateQRCode();
    },

    startMobileUploadPolling() {
      // Clear any existing polling interval
      this.stopMobileUploadPolling();

      // Start a new polling interval (check every 3 seconds)
      this.mobileUploadPollingInterval = setInterval(() => {
        this.checkForMobileUploads();
      }, 3000);
    },

    stopMobileUploadPolling() {
      if (this.mobileUploadPollingInterval) {
        clearInterval(this.mobileUploadPollingInterval);
        this.mobileUploadPollingInterval = null;
      }
    },

    async checkForMobileUploads() {
      if (!this.uploadSessionId) return;

      try {
        console.log("Checking for mobile uploads with session ID:", this.uploadSessionId);

        const response = await get("check_mobile_upload_session", {
          session_id: this.uploadSessionId
        });

        console.log("Check mobile uploads response:", response.data);

        if (response?.data?.status && response?.data?.data?.uploads) {
          // If new uploads detected
          const uploads = response.data.data.uploads;
          if (uploads.length > 0) {
            console.log("New uploads detected:", uploads);

            // Reload the document list
            this.loadDocuments();

            // Update status
            this.mobileUploadStatus = {
              type: 'success',
              message: `${uploads.length} new file(s) uploaded from mobile device!`
            };

            // Optional: Play a notification sound
            // this.playNotificationSound();
          } else {
            console.log("No new uploads found");
          }
        }
      } catch (error) {
        console.error("Error checking for mobile uploads:", error);
        // Don't update the UI for polling errors to avoid confusing the user
      }
    },

    openShareModal(doc) {
      this.selectedDocument = doc;
      this.showShareDialog = true;
    },

    closeShareModal() {
      this.selectedDocument = null;
      this.showShareDialog = false;
    },

    // Action handlers
    async handleUpload() {
      await this.loadDocuments();
      this.closeUploadModal();
    },

    async handleShare(emails, encryptDocument = true) {
      try {
        const response = await post("send_document_summery_to", {
          doc: this.selectedDocument,
          emails,
          encrypt_document: encryptDocument, // Pass the encryption preference to the server
        });
        if (response?.data?.status) {
          displayMessage(response.data.message);
          this.closeShareModal();
          this.$swal.fire({
            icon: "success",
            title: "Success",
            text: response.data.message,
          });
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (error) {
        displayErrorMessage("Failed to share document");
      }
    },

    async handleDelete(doc) {
      if (!confirm("Are you sure you want to delete this document?")) return;

      try {
        const response = await get("delete_patient_document", {
          id: doc.id,
          encounter_id: this.encounterId,
          type: doc.type
        });
        if (response?.data?.status) {
          displayMessage("Document deleted successfully");
          await this.loadDocuments();
        } else {
          this.$swal.fire({
            icon: "error",
            title: "Error",
            text: response?.data?.message,
          });
          // throw new Error(response?.data?.message);
        }
      } catch (error) {
        displayErrorMessage("Failed to delete document");
      }
    },

    async handleEdit(doc) {
      console.log("handleEdit called with document:", doc);
      // this.selectedDocument = { ...doc };
      // this.showUploadDialog = true;
    },

    handleTemplateSelect(template) {
      // Implement template selection handling
      console.log("Selected template:", template);
      this.closeTemplateModal();
    },

    async handleEdit_delete(doc) {
      try {
        const response = await post("edit_patient_document", {
          document_id: doc.id,
          encounter_id: this.encounterId,
          patient_id: this.patientDetails.patient_id,
          document_type: doc.type,
          document_name: doc.name,
        });

        if (response?.data?.status) {
          displayMessage("Document updated successfully");
          await this.loadDocuments();
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (error) {
        displayErrorMessage("Failed to edit document");
      }
    },

    async handleDownload(doc) {
      console.log("opening doc in new tab", doc);
      try {
        this.logDocumentDownload(doc);

        if (!doc.document_url) {
          // Try to fetch download URL if not available
          const response = await get("view_patient_document", {
            document_id: doc.id,
            encounter_id: this.encounterId,
          });

          if (response?.data?.status && response.data.document_url) {
            const url = response.data.document_url;
            window.open(url, '_blank');
          } else {
            throw new Error("Document URL not available");
          }
        } else {
          // Direct open in new tab if URL exists
          window.open(doc.document_url, '_blank');
        }
      } catch (error) {
        displayErrorMessage("Failed to open document");
      }
    },

    logDocumentDownload(document) {
      // Get the current user ID - assuming you have it in your authentication store
      // Replace this with your actual way of getting the current user ID
      const user_id = this.getUserId();
      
      if (!user_id) {
        console.warn('Unable to log activity: User ID not found');
        return;
      }

      // Prepare activity data
      const activity_type = 'document_download';
      const activity_description = `Downloaded document: ${document.name}`;
      const additional_data = {
        document_id: document.id,
        document_type: document.type,
        document_name: document.name,
        downloaded_at: new Date().toISOString()
      };

      // Log the activity using the logActivity endpoint
      post('log_activity', {
        user_id,
        activity_type,
        activity_description,
        additional_data
      }).catch(error => {
        console.error('Failed to log document download activity:', error);
      });
    },

    async handleTemplateSelect_delete(template) {
      try {
        const response = await post("generate_document_from_template", {
          template_id: template.id,
          encounter_id: this.encounterId,
          patient_id: this.patientDetails.patient_id,
          consultation_data: this.consultationData,
        });

        if (response?.data?.status) {
          displayMessage("Document generated successfully");
          await this.loadDocuments();
          this.closeTemplateModal();
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (error) {
        displayErrorMessage("Failed to generate document from template");
      }
    },
  },
};
</script>
