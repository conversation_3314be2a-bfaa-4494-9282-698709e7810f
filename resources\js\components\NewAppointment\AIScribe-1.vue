<template>
  <div class="flex items-center gap-2">
    <div class="flex items-center justify-center px-3 py-1 bg-blue-50 rounded-l-lg border-l-4 border-blue-500">
      <span class="text-blue-600 font-semibold mr-1">AI Scribe</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2">
        <path d="M12 18h.01"></path>
        <path d="M8 3h8a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2l-4 4v-4H8a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"></path>
      </svg>
    </div>

    <div class="flex items-center gap-3 border-t border-b border-r rounded-r-lg px-2 py-1.5">
      <!-- Record Audio Button -->
      <button @click="toggleRecording" :disabled="['processing', 'analyzing', 'populating'].includes(status)" :class="[
        'h-8 w-12 flex items-center justify-center rounded transition-all duration-300 relative overflow-hidden',
        status === 'recording' ? (isPaused ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-red-500 hover:bg-red-600') : 'bg-black hover:bg-gray-800'
      ]">
        <!-- Microphone Icon (when not recording) -->
        <svg v-if="status !== 'recording'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="w-6 h-6 text-white transition-all duration-300">
          <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
          <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
          <line x1="12" x2="12" y1="19" y2="22"></line>
        </svg>

        <!-- Stop Icon (when recording and not paused) -->
        <svg v-else-if="!isPaused" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="w-6 h-6 text-white transition-all duration-300">
          <rect x="6" y="6" width="12" height="12"></rect>
        </svg>

        <!-- Play Icon (when recording is paused) -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="w-6 h-6 text-white transition-all duration-300">
          <polygon points="5 3 19 12 5 21 5 3"></polygon>
        </svg>

        <!-- Pulsing Background Effect -->
        <div v-if="status === 'recording' && !isPaused" class="absolute inset-0 bg-red-400 opacity-30 animate-ping">
        </div>
      </button>

      <!-- Pause/Resume Button (only shown when recording) -->
      <button v-if="status === 'recording'" @click="togglePause"
        class="h-8 w-8 flex items-center justify-center rounded transition-all duration-300 bg-gray-700 hover:bg-gray-800 text-white">
        <!-- Pause Icon -->
        <svg v-if="!isPaused" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
          <line x1="6" y1="4" x2="6" y2="20"></line>
          <line x1="18" y1="4" x2="18" y2="20"></line>
        </svg>

        <!-- Resume Icon -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
          <polygon points="5 3 19 12 5 21 5 3"></polygon>
        </svg>
      </button>

      <!-- Recording Animation Waves -->
      <div v-if="status === 'recording' && !isPaused" class="flex items-end space-x-1 h-8">
        <div v-for="n in 4" :key="n" class="w-1 bg-red-500 rounded-full transform transition-all duration-200"
          :class="[`animate-wave-${n}`]" :style="`animation-delay: ${n * 0.1}s; height: ${(Math.random() * 20) + 5}px`">
        </div>
        <div class="ml-2 flex items-center space-x-1">
          <div class="w-1.5 h-1.5 rounded-full bg-red-500 animate-pulse"></div>
          <div class="w-1.5 h-1.5 rounded-full bg-red-400 animate-pulse" style="animation-delay: 0.2s"></div>
        </div>
      </div>

      <!-- Paused Indicator -->
      <div v-if="status === 'recording' && isPaused" class="text-yellow-500 text-sm font-medium">
        Paused
      </div>

      <!-- Recording Duration -->
      <div v-if="status === 'recording'" class="text-sm ml-2">
        {{ formatTime(recordingDuration) }}
      </div>

      <!-- Alternative Options -->
      <div v-if="status === 'idle'" class="flex items-center gap-2">
        <!-- File Upload Button -->
        <label
          class="flex items-center gap-1 cursor-pointer px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="17 8 12 3 7 8"></polyline>
            <line x1="12" x2="12" y1="3" y2="15"></line>
          </svg>
          Upload Audio
          <input type="file" accept="audio/*, .mp3, .wav, .ogg, .webm, .m4a, .mp4, .aac" class="hidden"
            @change="handleFileUpload" />
        </label>

        <!-- Manual Transcript Entry Button -->
        <button @click="showManualTranscriptDialog"
          class="px-3 py-1.5 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm flex items-center gap-2">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
          Enter Transcript
        </button>
      </div>

      <!-- Error Message Display -->
      <div v-if="mediaError" class="text-sm text-red-500">
        {{ mediaError }}
      </div>

      <!-- Processing Spinner with Progress -->
      <div v-if="status === 'processing'" class="flex flex-col gap-1">
        <div class="flex items-center gap-2 text-sm text-blue-500">
          <svg class="animate-spin h-4 w-4" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none">
            </circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
          <span>{{ processingStatus || 'Processing audio...' }}</span>
        </div>

        <!-- Progress bar for longer transcriptions -->
        <div v-if="processingProgress > 0" class="w-64 bg-gray-200 rounded-full h-1.5 mt-1">
          <div class="bg-blue-500 h-1.5 rounded-full" :style="`width: ${processingProgress}%`"></div>
        </div>
      </div>

      <!-- Analyzing State -->
      <div v-if="status === 'analyzing'" class="flex items-center gap-2 text-sm text-blue-500">
        <svg class="animate-spin h-4 w-4" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
        Analyzing conversation...
      </div>

      <!-- Review Button -->
      <button v-if="status === 'transcribed'" @click="showTranscriptDialog"
        class="px-3 py-1.5 bg-black hover:bg-gray-800 text-white rounded text-sm flex items-center gap-2">
        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
        </svg>
        Review Transcript
      </button>

      <!-- Populating State -->
      <div v-if="status === 'populating'" class="flex items-center gap-2 text-sm text-blue-500">
        <div class="flex space-x-1">
          <div class="w-1.5 h-1.5 rounded-full bg-blue-500 animate-bounce"></div>
          <div class="w-1.5 h-1.5 rounded-full bg-blue-500 animate-bounce" style="animation-delay: 0.2s"></div>
          <div class="w-1.5 h-1.5 rounded-full bg-blue-500 animate-bounce" style="animation-delay: 0.4s"></div>
        </div>
        Populating records...
      </div>

      <!-- Completed State -->
      <div v-if="status === 'completed'"
        class="text-sm text-green-600 flex items-center gap-2 px-2 py-1 bg-green-50 rounded-full">
        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
        <span>Records updated</span>
      </div>
    </div>

    <!-- Audio Controls Section -->
    <div v-if="audioUrl && status !== 'recording'"
      class="flex items-center gap-2 ml-1 border rounded-lg overflow-hidden">
      <!-- Audio Player -->
      <div class="flex items-center px-2 py-1 bg-gray-50">
        <button @click="toggleAudioPlayback"
          class="w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 hover:bg-blue-600 transition-colors text-white">
          <svg v-if="isAudioPlaying" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <rect x="6" y="5" width="4" height="14"></rect>
            <rect x="14" y="5" width="4" height="14"></rect>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
        </button>

        <!-- Time Display and Progress -->
        <div class="flex flex-col ml-2 w-32">
          <div class="w-full bg-gray-200 rounded-full h-1.5 mb-1">
            <div class="bg-blue-500 h-1.5 rounded-full" :style="`width: ${audioProgress}%`"></div>
          </div>
          <div class="text-xs text-gray-500">
            {{ formatTime(audioCurrentTime) }} / {{ formatTime(audioDuration) }}
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex border-l">
        <!-- Download Button -->
        <a :href="audioUrl" :download="getAudioFileName()"
          class="px-2 py-1.5 border-r text-sm text-gray-600 hover:bg-gray-100 flex items-center gap-1 transition-colors"
          title="Download audio recording">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          <span>Download</span>
        </a>

        <!-- Restart Button -->
        <button @click="restartRecording"
          class="px-2 py-1.5 text-sm text-gray-600 hover:bg-gray-100 flex items-center gap-1 transition-colors"
          title="Start a new recording">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
            <path d="M3 3v5h5"></path>
          </svg>
          <span>Restart</span>
        </button>
      </div>
    </div>

    <!-- Manual Transcript Modal -->
    <div v-if="showManualModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Enter Consultation Transcript</h2>
          <button @click="closeManualModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="mb-4 flex-grow overflow-y-auto">
          <p class="text-sm text-gray-600 mb-2">Enter the conversation transcript below. Prefix each speaker with
            "Doctor:" or "Patient:" to help with analysis.</p>
          <textarea v-model="transcript" class="w-full min-h-[300px] p-3 border rounded" placeholder="Doctor: Hello, how are you feeling today?
Patient: I've been having headaches for the past week.
Doctor: Can you tell me more about these headaches?" style="height: 402px;"></textarea>
        </div>

        <div class="flex justify-end gap-2 mt-2">
          <button @click="closeManualModal" class="px-4 py-2 border rounded hover:bg-gray-50">
            Cancel
          </button>
          <button @click="processManualTranscript" class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded"
            :disabled="!transcript || transcript.trim() === ''">
            Analyze Transcript
          </button>
        </div>
      </div>
    </div>

    <!-- Transcript Review Modal -->
    <div v-if="showTranscriptModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Review Transcript</h2>
          <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Content area with scrolling -->
        <div class="flex-grow overflow-y-auto pr-2">
          <!-- Diarized Transcript Display -->
          <div v-if="diarizedTranscript && diarizedTranscript.length > 0" class="mb-4">
            <h3 class="text-md font-medium mb-2">Speaker Diarization</h3>
            <div class="space-y-2 mb-4">
              <div v-for="(utterance, index) in diarizedTranscript" :key="index" class="p-3 rounded-lg"
                :class="utterance.speaker === 0 ? 'bg-blue-50' : 'bg-green-50'">
                <div class="font-semibold mb-1">
                  Speaker {{ utterance.speaker === 0 ? '1 (Doctor)' : '2 (Patient)' }}
                </div>
                <div>{{ utterance.text }}</div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h3 class="text-md font-medium mb-2">Complete Transcript</h3>
            <textarea v-model="transcript"
              class="w-full min-h-[200px] p-3 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
              placeholder="Transcript will appear here..."></textarea>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-2">
          <button @click="closeModal" class="px-4 py-2 border rounded hover:bg-gray-50">
            Cancel
          </button>
          <button @click="analyzeTranscript" class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded">
            Extract Medical Data
          </button>
        </div>
      </div>
    </div>

    <!-- Results Modal -->
    <div v-if="showResultsModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Extracted Medical Information</h2>
          <button @click="closeResultsModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="flex-grow overflow-y-auto pr-2">
          <div class="space-y-4">
            <div v-for="(content, key) in analysisResults" :key="key" class="border p-4 rounded-lg">
              <h3 class="font-medium capitalize mb-2">{{ formatKey(key) }}</h3>
              <div class="text-gray-700">{{ content }}</div>
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-4">
          <button @click="populateRecords" class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded"
            :disabled="status === 'populating'">
            Populate Medical Records
          </button>
        </div>
      </div>
    </div>

  </div>

</template>

<script>
import { post } from "../../config/request";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import Recorder from './recorder'; // Import our custom recorder implementation

export default {
  name: 'AIScribe',
  emits: ['records-updated'],
  props: {
    encounterId: {
      type: [Number, String],
      required: true,
      validator: function (value) {
        if (!value && value !== 0) {
          console.warn('AIScribe: encounterId prop is required and cannot be null or undefined');
          return false;
        }
        return true;
      }
    }
  },

  watch: {
    encounterId: {
      immediate: true,
      handler(newVal) {
        console.log('AIScribe encounterId updated:', newVal);
      }
    }
  },
  data() {
    return {
      status: 'idle',
      transcript: '',
      diarizedTranscript: [],
      showTranscriptModal: false,
      showResultsModal: false,
      showManualModal: false,
      recorder: null,
      audioChunks: [],
      audioBlob: null,
      audioUrl: null,
      audioElement: null, // HTML audio element for playback
      isAudioPlaying: false, // Track if audio is currently playing
      audioProgress: 0, // Progress percentage for audio playback
      audioCurrentTime: 0, // Current playback time in seconds
      audioDuration: 0, // Total duration in seconds
      audioUpdateTimer: null, // Timer for updating audio progress
      analysisResults: null,
      recordingStartTime: null,
      recordingTimer: null,
      recordingDuration: 0,
      isPaused: false,
      pauseStartTime: null,
      totalPausedDuration: 0,
      maxRecordingTime: 45 * 60, // 45 minutes in seconds to handle longer consultations
      mediaError: null,  // Property to store media-related errors
      canRecord: true, // Flag to track if recording is available
      processingStatus: '', // Status message during processing
      processingProgress: 0, // Progress percentage for processing
      isGoogleMeetActive: false, // Flag for Google Meet detection
      largeFileThresholdMB: 50, // Threshold in MB to determine large files
      maxFileSizeMB: 250, // Maximum file size in MB that we can reasonably process
      chunkSizeMB: 25, // Size of chunks when processing large files
      downloadCounter: 1 // Counter for unique file downloads
    }
  },

  mounted() {
    // Test if recording is available
    this.checkRecordingAvailability();

    // For testing only - uncomment in development if needed
    /*
    this.diarizedTranscript = [
      { speaker: 0, text: "Hello, how are you feeling today?", start: 0, end: 3 },
      { speaker: 1, text: "I've been having headaches for the past week.", start: 3, end: 6 }
    ];
    this.transcript = "Doctor: Hello, how are you feeling today?\n\nPatient: I've been having headaches for the past week.";
    */
  },

  beforeDestroy() {
    this.cleanup();
    this.cleanupAudioPlayer();
  },

  beforeUnmount() {
    // Delete any recording when the appointment is closed
    this.deleteRecording();
    this.cleanupAudioPlayer();
  },

  // Track the Google Meet status - detect if we're in a Google Meet session
  created() {
    this.detectGoogleMeet();

    // Listen for appointment close or navigation away to delete recordings
    window.addEventListener('beforeunload', this.deleteRecording);
  },

  methods: {
    async checkRecordingAvailability() {
      try {
        // Try to create a Recorder instance
        const testRecorder = new Recorder();
        await testRecorder.initialize();
        this.canRecord = true;
        console.log('Recording is available');
      } catch (error) {
        console.error('Recording is not available:', error);
        this.canRecord = false;
        this.mediaError = 'Recording not supported in this browser. Please use file upload or manual entry.';
      }
    },

    cleanup() {
      // Clean up recorder if it exists
      if (this.recorder) {
        if (this.status === 'recording') {
          this.recorder.stop();
        }
        this.recorder = null;
      }

      // Clear timers
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }

      // Clean up audio URL if it exists
      if (this.audioUrl) {
        URL.revokeObjectURL(this.audioUrl);
      }
    },

    // Delete recording data completely
    deleteRecording() {
      // Clean up recorder
      this.cleanup();

      // Reset all recording data
      this.audioChunks = [];
      this.audioBlob = null;

      // Clean up audio player
      this.cleanupAudioPlayer();

      if (this.audioUrl) {
        URL.revokeObjectURL(this.audioUrl);
        this.audioUrl = null;
      }

      console.log('Recording deleted');
    },

    // Format seconds to MM:SS display with clean formatting
    formatTime(seconds) {
      // Ensure we're working with a valid number and round to nearest whole second
      seconds = Math.round(parseFloat(seconds) || 0);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // Toggle pause/resume recording
    togglePause() {
      if (!this.recorder || this.status !== 'recording') return;

      if (this.isPaused) {
        // Resume recording
        this.resumeRecording();
      } else {
        // Pause recording
        this.pauseRecording();
      }
    },

    // Pause the recording
    pauseRecording() {
      if (!this.recorder || this.status !== 'recording' || this.isPaused) return;

      // Pause the recorder
      try {
        // If our custom recorder has pause support
        if (this.recorder.pause) {
          this.recorder.pause();
        } else {
          // Otherwise we'll need to stop media tracks temporarily
          if (this.recorder.stream) {
            this.recorder.stream.getTracks().forEach(track => {
              track.enabled = false; // This effectively pauses without stopping
            });
          }
        }

        // Mark as paused and store pause time
        this.isPaused = true;
        this.pauseStartTime = Date.now();

        // Pause the recording timer
        if (this.recordingTimer) {
          clearInterval(this.recordingTimer);
          this.recordingTimer = null;
        }

        console.log('Recording paused');
      } catch (error) {
        console.error('Error pausing recording:', error);
      }
    },

    // Resume the recording
    resumeRecording() {
      if (!this.recorder || this.status !== 'recording' || !this.isPaused) return;

      try {
        // If our custom recorder has resume support
        if (this.recorder.resume) {
          this.recorder.resume();
        } else {
          // Otherwise re-enable media tracks
          if (this.recorder.stream) {
            this.recorder.stream.getTracks().forEach(track => {
              track.enabled = true;
            });
          }
        }

        // Calculate total paused duration for accurate timing
        if (this.pauseStartTime) {
          this.totalPausedDuration += (Date.now() - this.pauseStartTime) / 1000;
          this.pauseStartTime = null;
        }

        // Resume the recording timer
        this.recordingTimer = setInterval(() => {
          this.recordingDuration = Math.floor((Date.now() - this.recordingStartTime) / 1000) - Math.floor(this.totalPausedDuration);

          // Check if we've reached the maximum recording time
          if (this.recordingDuration >= this.maxRecordingTime) {
            this.stopRecording();
          }
        }, 1000);

        // Mark as resumed
        this.isPaused = false;

        console.log('Recording resumed');
      } catch (error) {
        console.error('Error resuming recording:', error);
      }
    },

    // Restart recording from scratch
    restartRecording() {
      // Delete existing recording
      this.deleteRecording();

      // Reset state variables
      this.status = 'idle';
      this.transcript = '';
      this.diarizedTranscript = [];
      this.analysisResults = null;
      this.mediaError = null;
      this.processingProgress = 0;
      this.processingStatus = '';

      // Increment download counter for unique filenames
      this.downloadCounter++;

      // Don't automatically start recording
      // Just reset to idle state and show mic button
    },

    // Generate a unique filename for audio downloads
    getAudioFileName() {
      // Get current date for filename
      const now = new Date();
      const datePart = now.toISOString().split('T')[0]; // YYYY-MM-DD format

      // Format: patient_id_YYYY-MM-DD_counter.wav
      const patientId = this.getPatientId();
      const extension = this.getAudioExtension();

      return `patient_${patientId}_${datePart}_${this.downloadCounter}.${extension}`;
    },

    // Get patient ID from encounter data or use encounter ID as fallback
    getPatientId() {
      // This is a placeholder - in a real implementation, you would get the patient ID
      // from the encounter data or from a parent component
      return this.encounterId || 'unknown';
    },

    // Determine the appropriate file extension based on the current audio blob
    getAudioExtension() {
      if (!this.audioBlob) return 'wav';

      // Try to determine extension from MIME type
      if (this.audioBlob.type.includes('webm')) return 'webm';
      if (this.audioBlob.type.includes('wav')) return 'wav';
      if (this.audioBlob.type.includes('mp3')) return 'mp3';
      if (this.audioBlob.type.includes('ogg')) return 'ogg';
      if (this.audioBlob.type.includes('m4a')) return 'm4a';

      // Default to wav for better compatibility
      return 'wav';
    },

    // Get the duration of an audio file using the Audio API
    async getAudioDuration(audioFile) {
      return new Promise((resolve, reject) => {
        try {
          // For extremely large files (>200MB), skip media loading and just use file size estimation
          // This prevents potential browser memory issues with gigantic audio files
          const fileSizeMB = audioFile.size / (1024 * 1024);
          if (fileSizeMB > 200) {
            console.log(`Audio file is very large (${fileSizeMB.toFixed(1)} MB). Using size-based duration estimation.`);

            // More accurate estimation based on file type and size
            let estimatedDuration = 0;

            if (audioFile.type.includes('mp3')) {
              estimatedDuration = fileSizeMB * 60; // ~1 minute per MB for MP3
            } else if (audioFile.type.includes('wav')) {
              // WAV files are much larger per time unit
              if (fileSizeMB > 500) {
                // Very large WAV files are likely 44.1kHz stereo (10-12 MB per minute)
                estimatedDuration = fileSizeMB * 6; // ~6 seconds per MB for high-quality WAV
              } else {
                estimatedDuration = fileSizeMB * 5; // ~5 seconds per MB for standard WAV
              }
            } else if (audioFile.type.includes('webm')) {
              estimatedDuration = fileSizeMB * 120; // ~2 minutes per MB for WebM
            } else if (audioFile.type.includes('m4a') || audioFile.type.includes('aac')) {
              estimatedDuration = fileSizeMB * 90; // ~1.5 minutes per MB for M4A/AAC
            } else {
              estimatedDuration = fileSizeMB * 60; // Default estimate
            }

            console.log(`Audio duration estimation for large file: ${estimatedDuration.toFixed(0)} seconds (${(estimatedDuration / 60).toFixed(1)} minutes)`);
            return resolve(estimatedDuration);
          }

          // For smaller files, try to get actual duration via the Audio API
          const audioUrl = URL.createObjectURL(audioFile);
          const audio = new Audio(audioUrl);

          // Set up event listeners
          const onLoad = () => {
            const duration = audio.duration;
            // Clean up and return the duration
            audio.removeEventListener('loadedmetadata', onLoad);
            audio.removeEventListener('error', onError);
            URL.revokeObjectURL(audioUrl);
            console.log(`Actual audio duration: ${duration.toFixed(1)} seconds`);
            resolve(duration);
          };

          const onError = (error) => {
            audio.removeEventListener('loadedmetadata', onLoad);
            audio.removeEventListener('error', onError);
            URL.revokeObjectURL(audioUrl);
            reject(error);
          };

          // Wait for audio metadata to load
          audio.addEventListener('loadedmetadata', onLoad);
          audio.addEventListener('error', onError);

          // Set a timeout in case the audio takes too long to load
          setTimeout(() => {
            // Estimate duration based on file size if metadata loading takes too long
            const estimatedDuration = this.estimateDurationFromSize(audioFile);

            audio.removeEventListener('loadedmetadata', onLoad);
            audio.removeEventListener('error', onError);
            URL.revokeObjectURL(audioUrl);

            console.log(`Audio duration estimation (timeout): ${estimatedDuration.toFixed(0)} seconds`);
            resolve(estimatedDuration);
          }, 3000); // 3 second timeout
        } catch (error) {
          // If there's an error, fall back to size-based estimation
          try {
            const estimatedDuration = this.estimateDurationFromSize(audioFile);
            console.log(`Audio duration estimation (error fallback): ${estimatedDuration.toFixed(0)} seconds`);
            resolve(estimatedDuration);
          } catch (err) {
            reject(error); // If even estimation fails, reject
          }
        }
      });
    },

    // Helper method to estimate audio duration from file size
    estimateDurationFromSize(audioFile) {
      const fileSizeMB = audioFile.size / (1024 * 1024);
      let estimatedDuration = 0;

      // If the audio file has a specified duration property, use that as highest priority
      if (audioFile.duration) {
        return audioFile.duration;
      }

      // Check file type for more accurate estimation
      if (audioFile.type.includes('mp3')) {
        // MP3 estimation (128-192kbps typical)
        estimatedDuration = fileSizeMB * 60; // ~1 minute per MB for MP3
      } else if (audioFile.type.includes('wav') || audioFile.type === 'audio/wav') {
        // WAV is uncompressed so size correlates strongly with duration
        // Size depends heavily on sample rate, bit depth, and channels

        // Special handling for extremely large WAV files (likely 44.1kHz stereo)
        if (fileSizeMB > 800) {
          // 5000 seconds for 841MB is about 5.9 seconds per MB
          estimatedDuration = fileSizeMB * 5.9;

          // Log this special case
          console.log(`Extremely large WAV file detected (${fileSizeMB.toFixed(1)} MB). Using precise estimation ratio of 5.9 seconds/MB`);
        }
        // Large WAVs (likely high quality, 44.1kHz, stereo, 16-bit)
        else if (fileSizeMB > 100) {
          // For high quality WAV (44.1kHz, stereo, 16-bit)
          // ~10MB per minute = ~6 seconds per MB
          estimatedDuration = fileSizeMB * 6;
        }
        // Medium WAVs (likely 22-44kHz, could be mono or stereo)
        else if (fileSizeMB > 20) {
          estimatedDuration = fileSizeMB * 5.5;
        }
        // Smaller WAVs (likely compressed or lower quality)
        else {
          estimatedDuration = fileSizeMB * 5; // ~5 seconds per MB for standard/compressed WAV
        }
      } else if (audioFile.type.includes('webm')) {
        // WebM is typically very compressed
        estimatedDuration = fileSizeMB * 120; // ~2 minutes per MB for WebM
      } else if (audioFile.type.includes('m4a') || audioFile.type.includes('aac')) {
        // AAC/M4A are well compressed
        estimatedDuration = fileSizeMB * 90; // ~1.5 minutes per MB for M4A/AAC
      } else {
        // Default fallback estimate
        estimatedDuration = fileSizeMB * 60; // Default estimate ~1 minute per MB
      }

      // Check if estimation seems reasonable, correct if necessary
      // For the specific 841MB WAV that's 83 minutes (5000 seconds)
      if (audioFile.type.includes('wav') && Math.abs(fileSizeMB - 841) < 10 && Math.abs(estimatedDuration - 5000) > 500) {
        console.log(`Correcting duration estimate for known 841MB WAV file: setting to 5000 seconds (83.3 minutes)`);
        return 5000;
      }

      return estimatedDuration;
    },

    // Detect if we're in a Google Meet session
    detectGoogleMeet() {
      // Check if we're in a Google Meet session based on URL or DOM elements
      this.isGoogleMeetActive =
        window.location.href.includes('meet.google.com') ||
        document.querySelector('[data-meeting-code]') !== null ||
        document.querySelector('[data-unresolved="GoogleMeet"]') !== null;

      if (this.isGoogleMeetActive) {
        console.log('Google Meet detected - AI Scribe can capture this virtual consultation');
      }
    },

    toggleRecording() {
      console.log("Toggle recording clicked, current status:", this.status);

      if (this.status === 'idle') {
        this.startRecording();
      } else if (this.status === 'recording') {
        this.stopRecording();
      }
    },

    async startRecording() {
      try {
        // Clear any previous error messages
        this.mediaError = null;

        console.log("Starting recording");
        this.status = 'recording';
        this.audioChunks = [];

        // Start recording timer
        this.recordingStartTime = Date.now();
        this.recordingDuration = 0;
        this.recordingTimer = setInterval(() => {
          this.recordingDuration = Math.floor((Date.now() - this.recordingStartTime) / 1000);

          // Check if we've reached the maximum recording time
          if (this.recordingDuration >= this.maxRecordingTime) {
            this.stopRecording();
          }
        }, 1000);

        // Create and initialize recorder
        this.recorder = new Recorder();

        // Set up data handling
        this.recorder.onDataAvailable = (data) => {
          if (data && data.size > 0) {
            this.audioChunks.push(data);
          }
        };

        this.recorder.onStop = () => {
          this.processRecording();
        };

        // Initialize and start recording
        await this.recorder.initialize();
        this.recorder.start();

        console.log("Recording started successfully");
      } catch (error) {
        console.error('Error starting recording:', error);

        // Show error and fall back to alternatives
        this.mediaError = 'Failed to start recording: ' + (error.message || 'Unknown error');
        this.canRecord = false;
        this.status = 'idle';

        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError);
        }
      }
    },

    stopRecording() {
      if (this.recorder) {
        try {
          this.recorder.stop();

          // Clear the recording timer
          if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
          }

          this.status = 'processing';
        } catch (error) {
          console.error('Error stopping recording:', error);
          this.mediaError = 'Error stopping recording';
          this.status = 'idle';
        }
      }
    },

    async processRecording() {
      try {
        if (!this.audioChunks || this.audioChunks.length === 0) {
          throw new Error('No audio data captured');
        }

        // Create audio blob - use webm as default, with fallbacks
        let mimeType = 'audio/webm';
        if (this.recorder && this.recorder.mimeType) {
          mimeType = this.recorder.mimeType;
        }

        this.audioBlob = new Blob(this.audioChunks, { type: mimeType });

        if (this.audioBlob.size === 0) {
          throw new Error('Recorded audio is empty');
        }

        // Log the original audio size
        const originalSizeMB = this.audioBlob.size / (1024 * 1024);
        console.log(`Original audio size: ${originalSizeMB.toFixed(2)} MB`);

        // For very large recordings (over 100MB), try to compress before processing
        if (originalSizeMB > 100 && window.AudioContext) {
          try {
            this.processingStatus = 'Compressing large audio file...';
            console.log('Attempting to compress large audio file');

            // Try to compress by downsampling to mono and reducing bit depth
            const compressedBlob = await this.compressAudio(this.audioBlob);

            // If compression was successful and resulted in significant size reduction
            const compressedSizeMB = compressedBlob.size / (1024 * 1024);
            console.log(`Compressed audio size: ${compressedSizeMB.toFixed(2)} MB`);

            // Only use compressed version if it's significantly smaller (at least 30% reduction)
            if (compressedSizeMB < originalSizeMB * 0.7) {
              console.log(`Using compressed audio (${Math.round((1 - compressedSizeMB / originalSizeMB) * 100)}% smaller)`);
              this.audioBlob = compressedBlob;
            } else {
              console.log('Compression did not yield significant improvement, using original audio');
            }
          } catch (compressError) {
            console.warn('Audio compression failed, using original audio:', compressError);
          }
        }

        // Create URL for audio playback/download
        if (this.audioUrl) {
          URL.revokeObjectURL(this.audioUrl);
        }
        this.audioUrl = URL.createObjectURL(this.audioBlob);

        // Initialize audio player
        this.initAudioPlayer();

        // For larger recordings (over 30MB), use direct API
        if (originalSizeMB > 30) {
          console.log(`Large recording detected (${originalSizeMB.toFixed(1)} MB). Using direct Deepgram API approach.`);
          try {
            displayMessage(`Large recording detected (${originalSizeMB.toFixed(1)} MB). Using direct API approach for better reliability.`, { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }

          // Use the direct Deepgram API upload
          await this.uploadDirectToDeepgram(this.audioBlob);
          return;
        }

        // For smaller recordings, continue with the existing approach
        await this.diarizeAudio();

        this.status = 'transcribed';
      } catch (error) {
        console.error('Error processing recording:', error);
        this.mediaError = `Failed to process recording: ${error.message || 'Unknown error'}`;
        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError);
        }
        this.status = 'idle';
      }
    },

    // Direct upload to Deepgram for large files
    async uploadDirectToDeepgram(audioBlob) {
      try {
        // Update status
        this.status = 'processing';
        this.processingStatus = 'Preparing audio for direct upload to Deepgram...';
        this.processingProgress = 10;

        // Create FormData object for file upload
        const formData = new FormData();

        // Add file with appropriate extension and mime type
        let fileName = 'audio.wav';
        let mimeType = 'audio/wav';

        // If the blob has a type, use it
        if (audioBlob.type) {
          mimeType = audioBlob.type;

          // Set appropriate file extension based on MIME type
          if (audioBlob.type.includes('mpeg') || audioBlob.type.includes('mp3')) {
            fileName = 'audio.mp3';
          } else if (audioBlob.type.includes('webm')) {
            fileName = 'audio.webm';
          } else if (audioBlob.type.includes('ogg')) {
            fileName = 'audio.ogg';
          }
        }

        // Add file to FormData
        formData.append('audio_file', audioBlob, fileName);

        // Add encounter ID
        formData.append('encounter_id', this.encounterId);

        // Log what we're uploading
        const fileSizeMB = audioBlob.size / (1024 * 1024);
        console.log(`Uploading ${fileName} (${fileSizeMB.toFixed(2)} MB) directly to Deepgram via API`);
        this.processingStatus = `Uploading audio (${fileSizeMB.toFixed(1)} MB) directly to Deepgram...`;

        // Display a user-friendly message about the direct upload approach
        try {
          displayMessage(`Using direct Deepgram API for better handling of large audio. This may take several minutes for an ${fileSizeMB.toFixed(1)} MB file.`, { timeout: 8000 });
        } catch (e) {
          console.warn('Could not display message:', e);
        }

        // Show in-progress updates
        let progressInterval = setInterval(() => {
          if (this.processingProgress < 85) {
            this.processingProgress += 0.5;

            // Update status message occasionally
            if (this.processingProgress === 30) {
              this.processingStatus = 'Audio upload in progress...';
            } else if (this.processingProgress === 50) {
              this.processingStatus = 'Deepgram is processing your audio...';
            } else if (this.processingProgress === 70) {
              this.processingStatus = 'Almost there, finalizing transcription...';
            }
          }
        }, 1000);

        // Set up the POST request with appropriate headers
        // Add nonce for WordPress security
        formData.append('_wpnonce', request_data.nonce);

        const response = await post("ai_scribe_direct_transcribe", formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-WP-Nonce': request_data.nonce
          },
          timeout: 600000 // 10 minute timeout for very large files
        });

        // Clear the progress interval
        clearInterval(progressInterval);

        // Check for valid response
        if (!response || !response.data) {
          throw new Error('No response from server');
        }

        // Process successful response
        if (response.data.status === true) {
          // Success case
          this.processingProgress = 100;
          this.processingStatus = 'Transcription complete!';

          if (!response.data.data) {
            throw new Error('Response missing data property');
          }

          this.diarizedTranscript = response.data.data.diarized_transcript || [];
          this.transcript = response.data.data.full_transcript || '';

          console.log('Diarized transcript segments:', this.diarizedTranscript.length);
          console.log('Full transcript length:', this.transcript.length);

          // Check if this was a test response
          if (response.data.data.test_mode) {
            console.log('Test mode response received with file info:', response.data.data.file_info);
            displayMessage('Test mode: Successfully uploaded file but using test transcript (Deepgram API not called)', { timeout: 8000 });
          }

          // Verify if data is empty despite success status
          if (this.diarizedTranscript.length === 0) {
            console.warn('Received empty diarized transcript despite success status');
          }

          if (!this.transcript || this.transcript.trim() === '') {
            console.warn('Received empty transcript text despite success status');
            throw new Error('Server returned empty transcript. The audio may not contain speech.');
          }

          // Auto-open transcript review dialog when transcription is ready
          setTimeout(() => {
            this.showTranscriptDialog();
          }, 500);

          // Set status to transcribed
          this.status = 'transcribed';
        } else {
          // API error
          throw new Error(response.data.message || 'Failed to process audio');
        }
      } catch (error) {
        console.error('Error with direct Deepgram upload:', error);
        this.mediaError = `Error: ${error.message || 'Unknown error'}`;

        // Show manual entry for large files as fallback
        if (audioBlob.size > 50 * 1024 * 1024) { // 50MB
          try {
            displayMessage(`We had trouble processing this large audio file. You can try using manual transcription as an alternative.`, { timeout: 10000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }

          // Show manual transcription dialog as fallback
          setTimeout(() => {
            this.showManualTranscriptDialog();
          }, 1500);
        } else {
          try {
            displayErrorMessage(this.mediaError);
          } catch (e) {
            console.error('Failed to display error message:', e);
            alert(this.mediaError);
          }
        }

        this.status = 'idle';
      }
    },

    async handleFileUpload(event) {
      try {
        const file = event.target.files[0];
        if (!file) return;

        // Clear any previous error
        this.mediaError = null;
        this.status = 'processing';

        // Check if file is audio or video (WebM files may be detected as video)
        // More permissive check for audio files - include common audio formats 
        // even if MIME type detection fails
        const isAudioFile =
          file.type.startsWith('audio/') ||
          file.type.startsWith('video/webm') || // WebM can be detected as video
          file.name.toLowerCase().endsWith('.mp3') ||
          file.name.toLowerCase().endsWith('.wav') ||
          file.name.toLowerCase().endsWith('.ogg') ||
          file.name.toLowerCase().endsWith('.webm') ||
          file.name.toLowerCase().endsWith('.m4a') ||
          file.name.toLowerCase().endsWith('.mp4') || // Some audio files might be in mp4 containers
          file.name.toLowerCase().endsWith('.aac');

        if (!isAudioFile) {
          throw new Error('Please upload an audio file (.mp3, .wav, .webm, .ogg, .m4a, etc)');
        }

        // Check file size immediately
        const fileSizeMB = file.size / (1024 * 1024);
        console.log('Uploading audio file:', file.name, 'type:', file.type, 'size:', fileSizeMB.toFixed(2) + ' MB');

        // Show warning for very large files
        if (fileSizeMB > this.maxFileSizeMB) {
          try {
            this.processingStatus = `File is very large (${fileSizeMB.toFixed(1)} MB)`;
            displayMessage(`Warning: This audio file is very large (${fileSizeMB.toFixed(1)} MB). Processing may take a long time or fail. Consider using a shorter recording.`, { timeout: 10000 });
          } catch (e) {
            console.warn('Could not display warning message:', e);
          }
        } else if (fileSizeMB > this.largeFileThresholdMB) {
          try {
            displayMessage(`This is a large audio file (${fileSizeMB.toFixed(1)} MB). Processing may take several minutes.`, { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display warning message:', e);
          }
        }

        // Save the original audio blob
        this.audioBlob = file;

        // Create URL for audio playback
        if (this.audioUrl) {
          URL.revokeObjectURL(this.audioUrl);
        }
        this.audioUrl = URL.createObjectURL(file);

        // Initialize audio player
        this.initAudioPlayer();

        // Special handling for WebM files (which often cause server-side issues)
        const isWebmFile = file.type === 'video/webm' || file.type.includes('webm') ||
          file.name.toLowerCase().endsWith('.webm');

        if (isWebmFile) {
          this.processingStatus = 'Converting WebM file to WAV format...';
          console.log('WebM file detected. Converting to WAV format for better compatibility');

          try {
            // Always convert WebM to WAV first for better compatibility
            const wavBlob = await this.convertToWav(file);
            const wavSizeMB = wavBlob.size / (1024 * 1024);
            console.log(`Converted WebM to WAV: ${fileSizeMB.toFixed(2)} MB → ${wavSizeMB.toFixed(2)} MB`);

            // If WebM was larger than 10MB, compression is important
            if (fileSizeMB > 10) {
              // Try to further compress the WAV file
              this.processingStatus = 'Optimizing audio for processing...';
              const compressedBlob = await this.compressAudio(wavBlob);
              const compressedSizeMB = compressedBlob.size / (1024 * 1024);

              console.log(`Compressed WAV: ${wavSizeMB.toFixed(2)} MB → ${compressedSizeMB.toFixed(2)} MB`);

              // Use the compressed WAV if it's smaller
              if (compressedSizeMB < wavSizeMB * 0.8) {
                this.audioBlob = compressedBlob;
                console.log(`Using optimized WAV (${Math.round((1 - compressedSizeMB / wavSizeMB) * 100)}% smaller)`);
              } else {
                this.audioBlob = wavBlob;
                console.log('Using converted WAV (compression not effective)');
              }
            } else {
              // For smaller files, just use the WAV conversion without compression
              this.audioBlob = wavBlob;
            }

            // Success message
            try {
              displayMessage(`WebM file converted for compatibility. Processing audio...`, { timeout: 3000 });
            } catch (e) {
              console.warn('Could not display message:', e);
            }
          } catch (conversionError) {
            console.error('Error converting WebM file:', conversionError);

            // If WebM file is too large (>15MB) and conversion failed, show manual entry
            if (fileSizeMB > 15) {
              this.processingStatus = 'WebM file too large to process. Please use manual entry.';
              try {
                displayMessage(`This WebM file (${fileSizeMB.toFixed(1)} MB) is too large to process. Please use manual transcript entry instead.`, { timeout: 8000 });
              } catch (e) {
                console.warn('Could not display message:', e);
              }

              // Show manual transcription dialog after a brief delay
              setTimeout(() => {
                this.showManualTranscriptDialog();
              }, 1500);

              return;
            }

            // For smaller WebM files, continue with original file as fallback
            console.warn('Continuing with original WebM file as fallback');
          }
        }
        // Handle non-WebM large files
        else if (fileSizeMB > 50 && window.AudioContext) {
          try {
            this.processingStatus = `Optimizing large audio file (${fileSizeMB.toFixed(1)} MB)...`;
            console.log('Attempting to optimize large audio file');

            // Convert to WAV first for larger files
            const wavBlob = await this.convertToWav(file);
            const wavSizeMB = wavBlob.size / (1024 * 1024);

            // Then compress
            const compressedBlob = await this.compressAudio(wavBlob);
            const compressedSizeMB = compressedBlob.size / (1024 * 1024);

            console.log(`Original: ${fileSizeMB.toFixed(2)} MB → WAV: ${wavSizeMB.toFixed(2)} MB → Compressed: ${compressedSizeMB.toFixed(2)} MB`);

            // Use the most efficient format
            if (compressedSizeMB < Math.min(fileSizeMB, wavSizeMB) * 0.8) {
              this.audioBlob = compressedBlob;
              console.log(`Using compressed audio (${Math.round((1 - compressedSizeMB / fileSizeMB) * 100)}% smaller than original)`);

              try {
                displayMessage(`Optimized audio file to ${compressedSizeMB.toFixed(1)} MB for faster processing.`, { timeout: 5000 });
              } catch (e) {
                console.warn('Could not display message:', e);
              }
            } else if (wavSizeMB < fileSizeMB * 0.8) {
              this.audioBlob = wavBlob;
              console.log(`Using WAV conversion (${Math.round((1 - wavSizeMB / fileSizeMB) * 100)}% smaller than original)`);
            } else {
              console.log('Optimizations not effective, using original audio');
            }
          } catch (processError) {
            console.warn('Audio optimization failed, using original file:', processError);
          }
        }

        // Process the file
        await this.processFileUpload();
      } catch (error) {
        console.error('Error processing uploaded file:', error);
        this.mediaError = `Failed to process file: ${error.message || 'Unknown error'}`;
        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError);
        }
        this.status = 'idle';
      } finally {
        // Reset input field to allow selecting the same file again
        event.target.value = '';
      }
    },

    async processFileUpload() {
      try {
        // For large files (over 30MB), use the direct Deepgram API approach
        const fileSizeMB = this.audioBlob.size / (1024 * 1024);
        if (fileSizeMB > 30) {
          console.log(`Large file detected (${fileSizeMB.toFixed(1)} MB). Using direct Deepgram API approach.`);
          try {
            displayMessage(`Large audio file detected (${fileSizeMB.toFixed(1)} MB). Using direct API approach for better reliability.`, { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }

          // Use the direct Deepgram API upload
          await this.uploadDirectToDeepgram(this.audioBlob);
          return;
        }

        // For smaller files, use the standard transcription approach
        await this.diarizeAudio();
        this.status = 'transcribed';
      } catch (error) {
        console.error('Error processing file:', error);
        this.mediaError = `Failed to process file: ${error.message || 'Unknown error'}`;
        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError);
        }
        this.status = 'idle';
      }
    },

    async diarizeAudio() {
      try {
        // Validate encounterId before proceeding
        if (!this.encounterId && this.encounterId !== 0) {
          throw new Error('Valid encounter ID is required for transcription');
        }

        // Update processing status
        this.processingStatus = 'Preparing audio for transcription...';
        this.processingProgress = 5;

        // Check if the audio blob needs conversion for better compatibility
        let processedBlob = this.audioBlob;
        const audioSizeMB = processedBlob.size / (1024 * 1024);

        console.log(`Audio file size: ${audioSizeMB.toFixed(2)} MB`);
        console.log(`Audio file type: ${processedBlob.type}`);
        console.log(`Duration: ~${Math.round(this.recordingDuration)} seconds`);

        // For large files (>50MB), we'll need to process them differently
        const isLargeFile = audioSizeMB > this.largeFileThresholdMB;

        // Try to optimize the audio format for better processing if it's not too large
        // Only attempt conversion for files below a certain size to avoid browser crashes
        const canAttemptConversion = audioSizeMB < 100 && window.AudioContext;

        // Special handling for WebM files to ensure they're properly processed
        const isWebmFile = processedBlob.type === 'video/webm' ||
          processedBlob.type.includes('webm') ||
          (typeof this.audioBlob.name === 'string' && this.audioBlob.name.toLowerCase().endsWith('.webm'));

        // Always convert to WAV format for compatibility, with special handling for WebM
        if (isWebmFile) {
          try {
            this.processingStatus = 'Converting WebM to WAV format (required for server compatibility)...';
            this.processingProgress = 10;
            console.log('Converting WebM to WAV format for better server compatibility...');

            // Use specific WebM conversion approach
            processedBlob = await this.convertToWav(this.audioBlob);

            // Check converted size and apply compression if needed
            const convertedSizeMB = processedBlob.size / (1024 * 1024);
            console.log(`WebM converted to WAV: ${audioSizeMB.toFixed(2)} MB → ${convertedSizeMB.toFixed(2)} MB`);

            // For larger converted files, apply additional compression
            if (convertedSizeMB > 15 && window.AudioContext) {
              this.processingStatus = 'Optimizing converted WebM audio...';
              const compressedBlob = await this.compressAudio(processedBlob);
              const compressedSizeMB = compressedBlob.size / (1024 * 1024);

              if (compressedSizeMB < convertedSizeMB * 0.8) {
                processedBlob = compressedBlob;
                console.log(`WebM optimized: ${convertedSizeMB.toFixed(2)} MB → ${compressedSizeMB.toFixed(2)} MB`);
              }
            }

            console.log('WebM audio successfully converted to WAV format');
            this.processingProgress = 20;
          } catch (convError) {
            console.warn('WebM conversion failed:', convError);

            // If WebM is larger than 13MB (which appears to be a problematic threshold)
            if (audioSizeMB > 13) {
              this.processingStatus = 'WebM file too large to process directly';
              throw new Error(`This WebM file (${audioSizeMB.toFixed(1)} MB) is too large to process. Please try using a WAV or MP3 format, or use manual transcript entry.`);
            }

            // Show a user-friendly message for smaller files
            try {
              displayMessage('WebM conversion failed. Processing with original format which may have reduced compatibility.', { timeout: 8000 });
            } catch (e) {
              console.warn('Could not display message:', e);
            }
          }
        }
        // For non-WebM files, use standard conversion
        else if (!processedBlob.type.includes('wav') && canAttemptConversion) {
          try {
            this.processingStatus = 'Converting to WAV format (required for server compatibility)...';
            this.processingProgress = 10;
            console.log('Converting audio to WAV format for server compatibility...');
            processedBlob = await this.convertToWav(this.audioBlob);
            console.log('Audio successfully converted to WAV format');
            this.processingProgress = 20;
          } catch (convError) {
            console.warn('Audio conversion failed. This format may not be supported:', convError);
            // Show a user-friendly message
            try {
              displayMessage('This audio format may not be supported. Please try uploading a WAV or MP3 file instead.', { timeout: 8000 });
            } catch (e) {
              console.warn('Could not display message:', e);
            }
          }
        }

        // Update processing status
        this.processingStatus = 'Preparing audio data...';
        this.processingProgress = 25;

        // Check for known problematic file types/sizes
        const isProblematicWebm = processedBlob.type === 'video/webm' || processedBlob.type.includes('webm');

        // Specifically handle the 13-14MB WebM files that are causing issues
        if (isProblematicWebm && audioSizeMB > 12 && audioSizeMB < 15) {
          this.processingStatus = `WebM files around 13MB have compatibility issues. Opening manual entry...`;

          try {
            displayMessage(`We've detected a WebM file (${audioSizeMB.toFixed(1)} MB) in a size range that often causes processing issues. For more reliable processing, please try using a WAV or MP3 format, or use manual transcript entry.`, { timeout: 10000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }

          // Show manual transcription dialog
          setTimeout(() => {
            this.showManualTranscriptDialog();
          }, 1500);

          return;
        }

        // Check if file is extremely large (exceeds our defined very large file threshold)
        if (audioSizeMB > this.maxFileSizeMB) {
          this.processingStatus = `File too large (${audioSizeMB.toFixed(1)} MB). Opening manual entry...`;

          try {
            displayMessage(`This audio file is extremely large (${audioSizeMB.toFixed(1)} MB) and exceeds our processing limit of ${this.maxFileSizeMB} MB. Please use manual transcript entry instead.`, { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }

          // Show manual transcription dialog
          setTimeout(() => {
            this.showManualTranscriptDialog();
          }, 1000);

          return;
        }

        // For large files that are still under max threshold, use batch processing
        if (isLargeFile) {
          return await this.processBatchAudio(processedBlob);
        }

        // For regular sized files, use FormData for more efficient upload
        this.processingProgress = 30;

        console.log('Sending audio for transcription with encounter ID:', this.encounterId);
        console.log('Processed blob type:', processedBlob.type);
        console.log('Processed blob size:', processedBlob.size, 'bytes');

        // Calculate a reasonable estimate for processing time
        // Using a dynamic estimate based on file size
        let estimatedTimePerMB;
        if (audioSizeMB > 30) {
          estimatedTimePerMB = 20; // Very large files (slower)
        } else if (audioSizeMB > 10) {
          estimatedTimePerMB = 15; // Large files
        } else if (audioSizeMB > 5) {
          estimatedTimePerMB = 12; // Medium files
        } else {
          estimatedTimePerMB = 10; // Small files (faster)
        }

        const estimatedTime = Math.ceil(audioSizeMB * estimatedTimePerMB);

        // Update progress status with time estimate
        this.processingStatus = `Transcribing audio (${audioSizeMB.toFixed(1)} MB)...`;

        if (audioSizeMB > 1) {
          this.processingStatus = `Transcribing audio (${audioSizeMB.toFixed(1)} MB, ~${estimatedTime} seconds)...`;
          try {
            const minutes = Math.floor(estimatedTime / 60);
            const seconds = estimatedTime % 60;
            const timeString = minutes > 0 ?
              `${minutes} min${minutes !== 1 ? 's' : ''} ${seconds} sec` :
              `${seconds} seconds`;

            displayMessage(`Processing audio file (${audioSizeMB.toFixed(1)} MB). Estimated time: ${timeString}`, { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }
        }

        // If we're in a Google Meet session, update the status
        if (this.isGoogleMeetActive) {
          this.processingStatus = `Transcribing virtual consultation (${audioSizeMB.toFixed(1)} MB)...`;
        }

        // Start progress animation for longer files
        let progressInterval = null;
        if (audioSizeMB > 0.5) {  // Only show animated progress for files over 0.5MB
          const maxProgress = 90; // Max progress before completion
          const stepSize = Math.max(1, (maxProgress - this.processingProgress) / (estimatedTime * 2));
          progressInterval = setInterval(() => {
            // Gradually increase progress
            if (this.processingProgress < maxProgress) {
              this.processingProgress += stepSize;
              if (this.processingProgress > maxProgress) {
                this.processingProgress = maxProgress;
              }
            }
          }, 500);
        } else {
          // For small files, just set progress to 50%
          this.processingProgress = 50;
        }

        // Adjust timeout based on file size (5 minutes base + 45 seconds per MB)
        const timeoutMs = 300000 + (audioSizeMB * 45000);

        try {
          let response;

          // Since FormData endpoint doesn't exist, use base64 upload directly
          this.processingStatus = 'Converting audio for upload...';

          // Convert blob to base64
          const base64Audio = await this.blobToBase64(processedBlob);

          if (!base64Audio) {
            throw new Error('Failed to convert audio to base64');
          }

          // Set up AbortController for timeout handling
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

          try {
            // Send using base64 approach - making sure we have the right MIME type
            // We know the server works with WAV files, so we explicitly say it's a WAV
            this.processingStatus = 'Uploading audio...';

            // For longer recordings (>15 minutes), use Deepgram directly
            const estimatedDuration = Math.round(this.estimateDurationFromSize(processedBlob));
            const isLongRecording = estimatedDuration > 900; // 15 minutes

            if (isLongRecording) {
              this.processingStatus = 'Using Deepgram for longer recording...';
            }

            response = await post("ai_scribe_transcribe", {
              audio_file: base64Audio,
              encounter_id: this.encounterId,
              file_size_mb: audioSizeMB,
              estimated_duration: estimatedDuration,
              // For longer recordings, use Deepgram's API directly
              deepgram_direct: isLongRecording,
              // Always specify WAV for the server since we've converted it
              audio_mime_type: 'audio/wav',
              audio_format: 'mono-16khz-wav' // Tell server this is optimized for Deepgram
            }, {
              signal: controller.signal,
              timeout: timeoutMs
            });

            // Clear the timeout if the request completes
            clearTimeout(timeoutId);

            console.log('Audio upload successful');
          } catch (uploadError) {
            // Handle timeout errors
            if (uploadError.name === 'AbortError') {
              if (audioSizeMB > 20) {
                throw new Error(`Transcription timed out. Audio file (${audioSizeMB.toFixed(1)} MB) may be too large for automatic processing.`);
              } else {
                throw new Error('Transcription timed out. Processing this audio file is taking longer than expected.');
              }
            }
            // Re-throw other errors
            throw uploadError;
          }

          // Stop progress animation
          if (progressInterval) {
            clearInterval(progressInterval);
          }

          // Set progress to complete
          this.processingProgress = 100;
          this.processingStatus = 'Transcription complete!';

          console.log('Transcription API response:', response);
          console.log('Transcription API response status:', response?.data?.status);

          // Check for valid response
          if (!response || !response.data) {
            throw new Error('No response from server');
          }

          // Process successful response
          // First check if we have a response
          if (!response || !response.data) {
            throw new Error('No response from server');
          }

          console.log('Response from server:', response.data);

          // Now handle different response statuses
          if (response.data.status === true || response.data.status === 200) {
            // Success case
            if (!response.data.data) {
              throw new Error('Response missing data property');
            }

            this.diarizedTranscript = response.data.data.diarized_transcript || [];
            this.transcript = response.data.data.full_transcript || '';

            console.log('Diarized transcript segments:', this.diarizedTranscript.length);
            console.log('Full transcript length:', this.transcript.length);

            // Verify if data is empty despite success status
            if (this.diarizedTranscript.length === 0) {
              console.warn('Received empty diarized transcript despite success status');
            }

            if (!this.transcript || this.transcript.trim() === '') {
              console.warn('Received empty transcript text despite success status');

              // This usually means the file format is not properly recognized
              if (processedBlob.type.includes('wav')) {
                // If it's already WAV but didn't work, there might be an issue with the audio content
                throw new Error('Server returned empty transcript. The audio may not contain speech.');
              } else {
                // If it's another format, suggest trying WAV 
                throw new Error('File format issue. Please try converting to WAV format and uploading again.');
              }
            }

            // Auto-open transcript review dialog when transcription is ready
            setTimeout(() => {
              this.showTranscriptDialog();
            }, 500);
          } else if (response.data.status === 404) {
            // Route not found error
            console.error('API route not found:', response.data);
            throw new Error('Server endpoint not found. Please contact support.');
          } else {
            // Other API errors
            console.error('Transcription API error:', response.data);
            throw new Error(response.data.message || 'Failed to process audio');
          }
        } catch (error) {
          // Clear the progress interval if it exists
          if (progressInterval) {
            clearInterval(progressInterval);
          }

          console.error('Error processing audio:', error);

          if (error.name === 'AbortError') {
            // For timeout errors on larger files, provide more helpful messaging
            if (audioSizeMB > 20) {
              this.processingStatus = 'File too large - showing manual entry';

              try {
                displayMessage(`Transcription timed out. This audio file (${audioSizeMB.toFixed(1)} MB) appears to be too large for automatic processing. Please use manual transcript entry instead.`, { timeout: 10000 });
              } catch (e) {
                console.warn('Could not display message:', e);
              }

              // Show manual transcription dialog as fallback
              setTimeout(() => {
                this.showManualTranscriptDialog();
              }, 1500);

              return; // Don't throw, gracefully fall back to manual entry
            } else {
              throw new Error('Transcription timed out. Processing this audio file is taking longer than expected.');
            }
          } else if (error.message && (
            error.message.includes('too large') ||
            error.message.includes('batch processing failed') ||
            error.message.includes('Batch processing failed'))) {

            // For "too large" errors, also show manual entry
            this.processingStatus = 'File size issue - showing manual entry';

            try {
              displayMessage(`This audio file (${audioSizeMB.toFixed(1)} MB) cannot be automatically transcribed due to its size. Please use manual transcript entry instead.`, { timeout: 10000 });
            } catch (e) {
              console.warn('Could not display message:', e);
            }

            // Show manual transcription dialog as fallback
            setTimeout(() => {
              this.showManualTranscriptDialog();
            }, 1500);

            return; // Don't throw, gracefully fall back to manual entry
          } else {
            throw error;
          }
        }
      } catch (error) {
        console.error('Error processing audio:', error);
        if (error.response) {
          console.error('API Error Status:', error.response.status);
          console.error('API Error Details:', error.response.data);

          // Handle specific error status codes
          if (error.response.status === 500) {
            // Try to handle server-side errors gracefully
            this.mediaError = 'The server encountered an error processing this audio. Using manual transcription instead.';

            // Show the manual transcript dialog as a fallback
            setTimeout(() => {
              this.showManualTranscriptDialog();
            }, 500);

            return;
          }
        }

        // Add specific suggestions based on the error
        let errorMessage = error.message || 'Unknown error during audio processing';
        if (errorMessage.includes('timed out')) {
          errorMessage = 'Processing timed out. Please try the manual transcript option instead.';
        } else if (errorMessage.includes('too large')) {
          errorMessage = 'File is too large for automatic processing. Please try entering the transcript manually.';
        } else if (errorMessage.includes('500')) {
          errorMessage = 'Server error processing audio. Please try the manual transcript option instead.';
        }

        this.mediaError = errorMessage;

        // For most errors, fallback to manual transcription instead of throwing
        setTimeout(() => {
          this.showManualTranscriptDialog();
        }, 1000);

        // Don't throw, just return so we can continue with manual flow
        return;
      }
    },

    // Initialize the audio player
    initAudioPlayer() {
      // Clean up any existing audio player
      this.cleanupAudioPlayer();

      // Create new audio element
      this.audioElement = new Audio(this.audioUrl);

      // Add event listeners
      this.audioElement.addEventListener('loadedmetadata', () => {
        this.audioDuration = this.audioElement.duration;
        console.log(`Audio duration: ${this.audioDuration} seconds`);
      });

      this.audioElement.addEventListener('timeupdate', () => {
        this.audioCurrentTime = this.audioElement.currentTime;
        this.audioProgress = (this.audioCurrentTime / this.audioDuration) * 100;
      });

      this.audioElement.addEventListener('ended', () => {
        this.isAudioPlaying = false;
        this.audioElement.currentTime = 0;
        this.audioProgress = 0;
      });

      this.audioElement.addEventListener('error', (e) => {
        console.error('Audio player error:', e);
        this.isAudioPlaying = false;
      });
    },

    // Clean up audio player
    cleanupAudioPlayer() {
      if (this.audioElement) {
        this.audioElement.pause();
        this.audioElement.src = '';
        this.audioElement.removeAttribute('src');
        this.audioElement = null;
      }
      this.isAudioPlaying = false;
      this.audioProgress = 0;
      this.audioCurrentTime = 0;
      this.audioDuration = 0;
    },

    // Toggle audio playback between play and pause
    toggleAudioPlayback() {
      if (!this.audioElement) {
        this.initAudioPlayer();
      }

      if (this.isAudioPlaying) {
        this.audioElement.pause();
        this.isAudioPlaying = false;
      } else {
        this.audioElement.play().catch(error => {
          console.error('Error playing audio:', error);

          // Handle common error cases
          if (error.name === 'NotAllowedError') {
            displayErrorMessage('Browser requires user interaction before playing audio.');
          } else if (error.name === 'NotSupportedError') {
            displayErrorMessage('This audio format is not supported by your browser.');
          }
        });
        this.isAudioPlaying = true;
      }
    },

    // Method to handle large audio files with chunking
    async handleLargeAudioFile(audioBlob) {
      const fileSizeMB = audioBlob.size / (1024 * 1024);
      console.log(`Processing large audio file: ${fileSizeMB.toFixed(2)} MB`);

      // For all large files, we'll use a chunked upload approach instead of base64
      this.processingStatus = `Processing large audio file (${fileSizeMB.toFixed(1)} MB). This may take several minutes...`;
      try {
        displayMessage(`Processing large audio file (${fileSizeMB.toFixed(1)} MB). Preparing for chunked upload...`, { timeout: 8000 });
      } catch (e) {
        console.warn('Could not display message:', e);
      }

      // First, compress the audio if possible to reduce transmission size
      let processedBlob = audioBlob;
      try {
        // Try to optimize the audio before chunking - convert to mono with lower quality
        if (window.AudioContext) {
          this.processingStatus = 'Optimizing audio before upload...';
          const compressedBlob = await this.compressAudio(audioBlob);
          const compressedSizeMB = compressedBlob.size / (1024 * 1024);

          // Only use compressed version if significantly smaller
          if (compressedSizeMB < fileSizeMB * 0.7) {
            console.log(`Using compressed audio: ${fileSizeMB.toFixed(1)}MB → ${compressedSizeMB.toFixed(1)}MB`);
            processedBlob = compressedBlob;
            try {
              displayMessage(`Optimized audio file to ${compressedSizeMB.toFixed(1)} MB for faster processing.`, { timeout: 5000 });
            } catch (e) {
              console.warn('Could not display message:', e);
            }
          }
        }
      } catch (error) {
        console.warn('Failed to compress audio, continuing with original:', error);
      }

      // Calculate optimum chunk size and number of chunks
      // For very large files, use smaller chunks to avoid timeouts
      const actualFileSizeMB = processedBlob.size / (1024 * 1024);
      let chunkSizeMB = this.chunkSizeMB;
      if (actualFileSizeMB > 100) {
        chunkSizeMB = 10; // Smaller chunks for very large files
      } else if (actualFileSizeMB > 50) {
        chunkSizeMB = 15; // Medium chunks for large files
      }

      const chunkSize = chunkSizeMB * 1024 * 1024; // Convert MB to bytes
      const numChunks = Math.ceil(processedBlob.size / chunkSize);
      console.log(`Will process audio in ${numChunks} chunks of approximately ${chunkSizeMB} MB each`);

      this.processingStatus = `Uploading audio in ${numChunks} parts...`;
      this.processingProgress = 10;

      try {
        // Implementation for chunked upload using FormData instead of base64
        let transcriptResult = null;

        // If chunks are needed
        if (numChunks > 1) {
          let allChunksUploaded = true;

          // First, create session for chunked upload
          const sessionResponse = await post("ai_scribe_create_session", {
            encounter_id: this.encounterId,
            total_chunks: numChunks,
            file_size_mb: actualFileSizeMB,
            mime_type: processedBlob.type
          });

          if (!sessionResponse || !sessionResponse.data || !sessionResponse.data.status) {
            throw new Error('Failed to create upload session');
          }

          const sessionId = sessionResponse.data.data.session_id;
          console.log(`Created upload session: ${sessionId} for ${numChunks} chunks`);

          // Upload each chunk
          for (let i = 0; i < numChunks; i++) {
            const start = i * chunkSize;
            const end = Math.min(start + chunkSize, processedBlob.size);
            const chunk = processedBlob.slice(start, end);

            // Update progress
            this.processingProgress = 10 + Math.round((i / numChunks) * 60);
            this.processingStatus = `Uploading part ${i + 1} of ${numChunks}...`;

            try {
              // Create FormData to handle binary upload more efficiently than base64
              const formData = new FormData();
              formData.append('chunk_data', chunk, 'chunk.webm');
              formData.append('session_id', sessionId);
              formData.append('chunk_index', i);
              formData.append('total_chunks', numChunks);
              formData.append('encounter_id', this.encounterId);

              // Send the chunk
              const chunkResponse = await post("ai_scribe_upload_chunk", formData, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                },
                timeout: 120000 // 2 minutes per chunk
              });

              if (!chunkResponse || !chunkResponse.data || !chunkResponse.data.status) {
                console.error(`Failed to upload chunk ${i + 1}:`, chunkResponse);
                allChunksUploaded = false;
                break;
              }

              console.log(`Uploaded chunk ${i + 1} of ${numChunks}`);

              // If it's the last chunk, get the combined result
              if (i === numChunks - 1) {
                this.processingStatus = 'Processing uploaded audio...';
                this.processingProgress = 70;

                const finalizeResponse = await post("ai_scribe_process_chunks", {
                  session_id: sessionId,
                  encounter_id: this.encounterId
                }, {
                  timeout: 300000 // 5 minute timeout for processing the full file
                });

                if (!finalizeResponse || !finalizeResponse.data) {
                  throw new Error('Failed to process uploaded chunks');
                }

                if (finalizeResponse.data.status) {
                  transcriptResult = finalizeResponse.data;
                } else {
                  throw new Error(finalizeResponse.data?.message || 'Failed to process audio chunks');
                }
              }
            } catch (chunkError) {
              console.error(`Error uploading chunk ${i + 1}:`, chunkError);
              allChunksUploaded = false;
              break;
            }
          }

          if (!allChunksUploaded) {
            throw new Error('Failed to upload all audio chunks');
          }
        } else {
          // For files that don't need chunking but are still considered "large"
          // Use FormData for more efficient upload
          const formData = new FormData();
          formData.append('audio_file', processedBlob, 'audio.webm');
          formData.append('encounter_id', this.encounterId);
          formData.append('file_size_mb', actualFileSizeMB);

          this.processingStatus = 'Uploading audio file...';
          this.processingProgress = 30;

          const response = await post("ai_scribe_transcribe_formdata", formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            timeout: 300000 + (actualFileSizeMB * 6000) // 5 minutes base + extra time per MB
          });

          if (!response || !response.data) {
            throw new Error('No response from server');
          }

          if (response.data.status) {
            transcriptResult = response.data;
          } else {
            throw new Error(response.data?.message || 'Failed to process audio file');
          }
        }

        // Process the result
        this.processingProgress = 100;
        this.processingStatus = 'Processing complete!';

        if (transcriptResult) {
          this.diarizedTranscript = transcriptResult.data.diarized_transcript || [];
          this.transcript = transcriptResult.data.full_transcript || '';

          console.log('Diarized transcript segments from large file:', this.diarizedTranscript.length);
          console.log('Full transcript length from large file:', this.transcript.length);

          return; // Success!
        } else {
          throw new Error('No transcript result received from server');
        }
      } catch (error) {
        console.error('Error processing large audio file:', error);

        // If server-side processing fails, show detailed error
        this.processingStatus = 'Server processing failed.';
        this.mediaError = `Error: ${error.message || 'Unknown error'}`;

        // Try fallback to a plain base64 approach if FormData failed
        if (error.message.includes('formdata') || error.message.includes('chunk')) {
          try {
            displayMessage('Trying alternative upload method...', { timeout: 5000 });

            // Fallback to direct base64 upload for server that might not support FormData
            const base64Audio = await this.blobToBase64(processedBlob);

            if (!base64Audio) {
              throw new Error('Failed to convert audio to base64');
            }

            const response = await post("ai_scribe_transcribe", {
              audio_file: base64Audio,
              encounter_id: this.encounterId,
              large_file: true,
              file_size_mb: actualFileSizeMB
            }, {
              timeout: 300000 + (actualFileSizeMB * 6000)
            });

            if (!response || !response.data) {
              throw new Error('No response from server');
            }

            if (response.data && response.data.status) {
              this.diarizedTranscript = response.data.data.diarized_transcript || [];
              this.transcript = response.data.data.full_transcript || '';
              return; // Success with fallback!
            } else {
              throw new Error(response.data?.message || 'Failed to process with fallback method');
            }
          } catch (fallbackError) {
            console.error('Fallback upload also failed:', fallbackError);
            this.mediaError = `Error: ${fallbackError.message || 'Server unable to process this file'}`;
          }
        }

        // Show error message to user through toast
        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.warn('Could not display error message:', e);
        }

        throw error; // Let the parent handler know there was an error
      }
    },

    // Process large audio files using batch processing with base64 encoding
    async processBatchAudio(audioBlob) {
      const fileSizeMB = audioBlob.size / (1024 * 1024);
      console.log(`Processing large audio file in batches: ${fileSizeMB.toFixed(2)} MB`);

      // For extremely large files, adjust handling based on size
      // Files over 600MB are at high risk of causing browser memory issues
      if (fileSizeMB > 600) {
        this.processingStatus = `File extremely large for browser processing (${fileSizeMB.toFixed(1)} MB)`;

        try {
          displayMessage(`This audio file (${fileSizeMB.toFixed(1)} MB) is extremely large and may cause browser stability issues. For better reliability with files over 600MB, you can either:
      1. Try processing in smaller segments, or
      2. Use manual transcript entry.`, { timeout: 12000 });
        } catch (e) {
          console.warn('Could not display message:', e);
        }

        // For 900MB+ files, default to manual entry to prevent crashes
        if (fileSizeMB > 900) {
          try {
            displayMessage(`For files over 900MB, we recommend using manual transcript entry for better reliability.`, { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display second message:', e);
          }

          // Show manual transcription dialog after a brief delay
          setTimeout(() => {
            this.showManualTranscriptDialog();
          }, 2000);

          return;
        }

        // For files 600-900MB, continue with processing but show a warning
        console.log(`Attempting to process very large file (${fileSizeMB.toFixed(1)} MB). This will use chunking with very small chunks.`);
      }
      // For large but manageable files (80-600MB), show a warning but continue
      else if (fileSizeMB > 80) {
        try {
          displayMessage(`This audio file (${fileSizeMB.toFixed(1)} MB) is large and processing may take several minutes. For better reliability, you might consider manual transcript entry instead.`, { timeout: 10000 });
        } catch (e) {
          console.warn('Could not display message:', e);
        }
      }

      // For all large files, we'll use a batch processing approach with base64 encoding
      this.processingStatus = `Processing large audio file (${fileSizeMB.toFixed(1)} MB). This may take several minutes...`;
      try {
        displayMessage(`Processing large audio file (${fileSizeMB.toFixed(1)} MB). Preparing for batch processing...`, { timeout: 8000 });
      } catch (e) {
        console.warn('Could not display message:', e);
      }

      // First, always convert to WAV format for better server compatibility
      let processedBlob = audioBlob;
      try {
        if (!audioBlob.type.includes('wav') && window.AudioContext) {
          this.processingStatus = 'Converting to WAV format (required for server compatibility)...';
          console.log('Converting audio to WAV format for server compatibility...');

          // Get original file size for comparison
          const inputSizeMB = audioBlob.size / (1024 * 1024);

          // Convert to WAV format
          processedBlob = await this.convertToWav(audioBlob);

          const wavSizeMB = processedBlob.size / (1024 * 1024);
          console.log('Audio successfully converted to WAV format');

          // Check if the WAV file became significantly larger
          if (wavSizeMB > inputSizeMB * 2 && wavSizeMB > 200) {
            console.log(`Warning: WAV conversion significantly increased file size: ${inputSizeMB.toFixed(1)}MB → ${wavSizeMB.toFixed(1)}MB`);
            try {
              displayMessage(`Converting to WAV format has increased file size. Optimizing now...`, { timeout: 5000 });
            } catch (e) {
              console.warn('Could not display message:', e);
            }
          }

          // Once converted, try to compress it to reduce size
          this.processingStatus = 'Optimizing audio for Deepgram...';
          const compressedBlob = await this.compressAudio(processedBlob);
          const compressedSizeMB = compressedBlob.size / (1024 * 1024);

          // Log the complete transformation
          console.log(`Original: ${inputSizeMB.toFixed(1)} MB → WAV: ${wavSizeMB.toFixed(1)} MB → Compressed: ${compressedSizeMB.toFixed(1)} MB`);

          // Only use compressed version if significantly smaller than WAV
          if (compressedSizeMB < wavSizeMB * 0.7) {
            console.log(`Using compressed audio (${Math.round((1 - compressedSizeMB / wavSizeMB) * 100)}% smaller than WAV)`);
            processedBlob = compressedBlob;

            // For extreme size increases, add special message
            if (wavSizeMB > 500 && compressedSizeMB < wavSizeMB * 0.2) {
              try {
                displayMessage(`Successfully compressed audio from ${wavSizeMB.toFixed(1)} MB to ${compressedSizeMB.toFixed(1)} MB for faster processing.`, { timeout: 6000 });
              } catch (e) {
                console.warn('Could not display message:', e);
              }
            }
          } else {
            // If compression didn't help significantly, at least inform
            console.log(`Compression not effective enough. Using WAV format.`);
          }
        } else if (window.AudioContext) {
          // If already WAV, just try to compress
          this.processingStatus = 'Optimizing audio before processing...';
          const compressedBlob = await this.compressAudio(audioBlob);
          const compressedSizeMB = compressedBlob.size / (1024 * 1024);

          // Only use compressed version if significantly smaller
          if (compressedSizeMB < fileSizeMB * 0.7) {
            console.log(`Using compressed audio: ${fileSizeMB.toFixed(1)}MB → ${compressedSizeMB.toFixed(1)}MB`);
            processedBlob = compressedBlob;
            try {
              displayMessage(`Optimized audio file to ${compressedSizeMB.toFixed(1)} MB for faster processing.`, { timeout: 5000 });
            } catch (e) {
              console.warn('Could not display message:', e);
            }
          }
        }
      } catch (error) {
        console.warn('Failed to process audio format, continuing with original:', error);
        try {
          displayMessage('There was an issue processing this audio format. Please try converting to WAV manually.', { timeout: 8000 });
        } catch (e) {
          console.warn('Could not display message:', e);
        }
      }

      // Calculate optimum chunk size and number of chunks
      // Use smaller chunks for base64 encoding to avoid hitting server limits
      const actualFileSizeMB = processedBlob.size / (1024 * 1024);

      // For large files, we'll use a time-based segmentation approach
      // Deepgram works best with segments of 15-30 minutes each
      const MAX_SEGMENT_MINUTES = 20; // Maximum segment length in minutes

      // Initialize chunkSizeMB variable - defining it at the beginning to fix the reference error
      let chunkSizeMB = 20; // Default value that will be overridden

      // Estimate the total duration of the audio
      const totalDurationSeconds = this.estimateDurationFromSize(processedBlob);
      const totalDurationMinutes = totalDurationSeconds / 60;
      console.log(`Estimated total duration: ${totalDurationMinutes.toFixed(1)} minutes`);

      // For files over 30 minutes, use time-based segmentation
      if (totalDurationMinutes > 30) {
        console.log(`Long audio detected (${totalDurationMinutes.toFixed(1)} minutes). Using time-based segmentation.`);

        // Calculate number of segments needed
        const numSegments = Math.ceil(totalDurationMinutes / MAX_SEGMENT_MINUTES);
        console.log(`Processing audio in ${numSegments} time segments of approximately ${MAX_SEGMENT_MINUTES} minutes each`);

        // Configure chunk size to achieve the desired segment count
        // This is just an approximation since we're using file size as a proxy for time
        chunkSizeMB = actualFileSizeMB / numSegments;

        // Set minimum chunk size to avoid too many tiny chunks
        if (chunkSizeMB < 2 && actualFileSizeMB > 10) {
          chunkSizeMB = 2;
          console.log(`Setting minimum chunk size to ${chunkSizeMB} MB to avoid too many small chunks`);
        }

        // Log the segmentation strategy
        console.log(`Using chunk size of approximately ${chunkSizeMB.toFixed(1)} MB per segment`);

        // Extra logging for very large files
        if (actualFileSizeMB > 500) {
          console.log(`NOTICE: Processing large file (${actualFileSizeMB.toFixed(1)} MB). This approach sends smaller time segments to Deepgram.`);
          console.log('Each segment will be processed separately for better reliability.');
        }
      }
      // For shorter files, use standard size-based chunking
      else {
        // Set chunk size based on file size for more manageable processing
        if (actualFileSizeMB > 200) {
          chunkSizeMB = 15; // Larger chunks for big files (but still reasonable for Deepgram)
        } else if (actualFileSizeMB > 100) {
          chunkSizeMB = 20; // Standard chunk size for medium-large files
        } else if (actualFileSizeMB > 50) {
          chunkSizeMB = 25; // Larger chunks for medium files
        } else {
          chunkSizeMB = 30; // Default chunk size (can handle complete file in most cases)
        }

        console.log(`Using standard chunk size of ${chunkSizeMB} MB`);
      }

      const chunkSize = chunkSizeMB * 1024 * 1024; // Convert MB to bytes
      const numChunks = Math.ceil(processedBlob.size / chunkSize);
      console.log(`Will process audio in ${numChunks} chunks of approximately ${chunkSizeMB} MB each`);

      this.processingStatus = `Processing audio in ${numChunks} batches...`;
      this.processingProgress = 10;

      try {
        // Array to collect partial transcripts
        const partialTranscripts = [];
        let combinedDiarizedTranscript = [];
        let combinedFullTranscript = '';
        let batchSuccess = true;

        // Process each chunk with improved error handling and retry mechanism
        for (let i = 0; i < numChunks; i++) {
          const start = i * chunkSize;
          const end = Math.min(start + chunkSize, processedBlob.size);
          const chunk = processedBlob.slice(start, end);

          // Update progress
          this.processingProgress = 10 + Math.round((i / numChunks) * 75);
          this.processingStatus = `Processing batch ${i + 1} of ${numChunks}...`;

          // Implement retry logic for more resilience
          let retryCount = 0;
          const maxRetries = 2; // Maximum number of retries per chunk
          let chunkProcessed = false;

          while (retryCount <= maxRetries && !chunkProcessed) {
            try {
              // If this is a retry, update status
              if (retryCount > 0) {
                console.log(`Retrying batch ${i + 1} (attempt ${retryCount} of ${maxRetries})...`);
                this.processingStatus = `Retrying batch ${i + 1} (attempt ${retryCount} of ${maxRetries})...`;
              }

              // Convert chunk to base64
              const base64Chunk = await this.blobToBase64(chunk);

              if (!base64Chunk) {
                throw new Error(`Failed to convert batch ${i + 1} to base64`);
              }

              // Try to reduce chunk size if it's a retry and the chunk is large
              let processedChunk = base64Chunk;
              if (retryCount > 0 && chunk.size > 1.5 * 1024 * 1024) {
                // For retries, try to compress the audio further or use a smaller portion
                try {
                  const smallerChunk = chunk.slice(0, chunk.size * 0.8); // Use 80% of the chunk
                  const smallerBase64 = await this.blobToBase64(smallerChunk);
                  if (smallerBase64) {
                    processedChunk = smallerBase64;
                    console.log(`Using reduced chunk size for retry: ${(smallerChunk.size / (1024 * 1024)).toFixed(2)} MB`);
                  }
                } catch (compressionError) {
                  console.warn(`Failed to compress chunk for retry, using original: ${compressionError.message}`);
                }
              }

              // Send the chunk for processing with increased timeout for retries
              // Using time-based segments optimized for Deepgram
              const timeoutMs = 180000 + (retryCount * 60000); // Add 1 minute per retry
              const chunkSizeMB = chunk.size / (1024 * 1024);
              const estimatedDurationSec = this.estimateDurationFromSize(chunk);

              const chunkResponse = await post("ai_scribe_transcribe", {
                audio_file: processedChunk,
                encounter_id: this.encounterId,
                chunk_index: i,
                total_chunks: numChunks,
                is_batch: true,
                time_segment: true, // Indicate this is a time segment for Deepgram
                segment_duration: Math.round(estimatedDurationSec),
                file_size_mb: chunkSizeMB,
                retry_count: retryCount,
                deepgram_direct: estimatedDurationSec > 3600, // Use Deepgram directly for recordings over 1 hour
                // Always specify WAV for the server since we've converted it
                audio_mime_type: 'audio/wav',
                audio_format: 'mono-16khz-wav' // Tell server this is optimized for Deepgram
              }, {
                timeout: timeoutMs
              });

              if (!chunkResponse || !chunkResponse.data) {
                console.error(`No response from server for batch ${i + 1}`);
                if (retryCount < maxRetries) {
                  retryCount++;
                  continue;
                } else {
                  batchSuccess = false;
                  break;
                }
              }

              if (chunkResponse.data && chunkResponse.data.status) {
                // If the server returned partial results for this chunk, collect them
                const chunkDiarized = chunkResponse.data.data.diarized_transcript || [];
                const chunkTranscript = chunkResponse.data.data.full_transcript || '';

                // Add to our collection
                partialTranscripts.push({
                  index: i,
                  diarized: chunkDiarized,
                  transcript: chunkTranscript
                });

                console.log(`Successfully processed batch ${i + 1} of ${numChunks}`);
                console.log(`Batch ${i + 1} transcript length: ${chunkTranscript.length} characters`);

                // Mark as successfully processed
                chunkProcessed = true;
              } else {
                console.error(`Failed to process batch ${i + 1}:`, chunkResponse);

                // Retry if possible
                if (retryCount < maxRetries) {
                  retryCount++;
                  // Short delay before retry
                  await new Promise(resolve => setTimeout(resolve, 2000));
                  continue;
                } else {
                  batchSuccess = false;
                  break;
                }
              }
            } catch (chunkError) {
              console.error(`Error processing batch ${i + 1}:`, chunkError);

              // Retry if possible
              if (retryCount < maxRetries) {
                retryCount++;
                // Longer delay for network errors
                await new Promise(resolve => setTimeout(resolve, 3000));
                continue;
              } else {
                batchSuccess = false;
                break;
              }
            }
          }

          // If this chunk failed after all retries, break the loop
          if (!chunkProcessed) {
            break;
          }
        }

        // If all batches were processed successfully, combine the results
        if (batchSuccess && partialTranscripts.length === numChunks) {
          this.processingStatus = 'Combining results from all batches...';
          this.processingProgress = 90;

          // Sort by index to ensure correct order
          partialTranscripts.sort((a, b) => a.index - b.index);

          // Combine the transcript text
          combinedFullTranscript = partialTranscripts.map(part => part.transcript).join(' ');

          // Combine the diarized segments 
          // Each batch/chunk has its own diarized segments, but timestamps need adjustment
          let timeOffset = 0;
          partialTranscripts.forEach(part => {
            // Adjust timestamps for this chunk's diarized segments
            const adjustedDiarized = part.diarized.map(segment => {
              // If segment has timestamps, adjust them
              if (segment.start !== undefined && segment.end !== undefined) {
                return {
                  ...segment,
                  start: segment.start + timeOffset,
                  end: segment.end + timeOffset
                };
              }
              return segment;
            });

            // Add to combined diarized transcript
            combinedDiarizedTranscript = combinedDiarizedTranscript.concat(adjustedDiarized);

            // Update time offset for next chunk
            // If this chunk has diarized segments with timestamps, use the last end time
            if (part.diarized.length > 0 && part.diarized[part.diarized.length - 1].end) {
              const lastSegment = part.diarized[part.diarized.length - 1];
              timeOffset += (lastSegment.end || 0);
            }
          });

          // Now we have the combined results!
          this.diarizedTranscript = combinedDiarizedTranscript;
          this.transcript = combinedFullTranscript;

          console.log('Diarized transcript segments from large file:', combinedDiarizedTranscript.length);
          console.log('Full transcript length from large file:', combinedFullTranscript.length);

          // If we have all the data processed, let's finish
          this.processingProgress = 100;
          this.processingStatus = 'Processing complete!';

          return; // Success with batch processing!
        } else {
          // If any batch failed, try sending the entire file as one piece
          // This is a fallback for when batching fails
          if (actualFileSizeMB < 20) { // Only try for files under 20MB
            this.processingStatus = 'Batch processing failed. Trying single-file processing...';
            this.processingProgress = 30;

            // Convert to base64
            const base64Audio = await this.blobToBase64(processedBlob);

            if (!base64Audio) {
              throw new Error('Failed to convert audio to base64');
            }

            // Try direct Deepgram processing for large files
            const response = await post("ai_scribe_transcribe", {
              audio_file: base64Audio,
              encounter_id: this.encounterId,
              large_file: true,
              deepgram_direct: true, // Signal server to use Deepgram API directly
              file_size_mb: actualFileSizeMB,
              estimated_duration: Math.round(this.estimateDurationFromSize(processedBlob)),
              // Always specify WAV for the server since we've converted it
              audio_mime_type: 'audio/wav',
              audio_format: 'mono-16khz-wav' // Tell server this is optimized for Deepgram
            }, {
              timeout: 300000 + (actualFileSizeMB * 6000)
            });

            if (response && response.data && response.data.status) {
              this.diarizedTranscript = response.data.data.diarized_transcript || [];
              this.transcript = response.data.data.full_transcript || '';

              console.log('Diarized transcript segments (fallback):', this.diarizedTranscript.length);
              console.log('Full transcript length (fallback):', this.transcript.length);

              this.processingProgress = 100;
              this.processingStatus = 'Processing complete!';

              return; // Success with fallback!
            } else {
              throw new Error('Failed with both batch processing and single-file processing');
            }
          } else {
            throw new Error('Batch processing failed. File too large for fallback method.');
          }
        }
      } catch (error) {
        console.error('Error processing large audio file:', error);

        // If processing fails, show detailed error
        this.processingStatus = 'Processing failed.';
        this.mediaError = `Error: ${error.message || 'Unknown error'}`;

        // Special handling for the large file case - offer manual transcription instead of just error
        const blobSizeMB = processedBlob.size / (1024 * 1024);
        if (blobSizeMB > 100 ||
          error.message.includes('too large') ||
          error.message.includes('batch processing') ||
          error.message.includes('Batch processing')) {

          try {
            displayMessage(`We couldn't process this large audio file (${blobSizeMB.toFixed(1)} MB). For files this size, manual transcript entry is more reliable.`, { timeout: 10000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }

          // Show manual transcription dialog after a brief delay
          setTimeout(() => {
            this.showManualTranscriptDialog();
          }, 1500);

          return; // Don't throw, gracefully fall back to manual entry
        } else {
          // For other types of errors, show error message
          try {
            displayErrorMessage(this.mediaError);
          } catch (e) {
            console.warn('Could not display error message:', e);
          }

          throw error; // Let the parent handler know there was an error
        }
      }
    },

    // Convert audio blob to WAV format for better compatibility with transcription services
    async convertToWav(audioBlob) {
      return new Promise(async (resolve, reject) => {
        try {
          // Create audio context
          const AudioContext = window.AudioContext || window.webkitAudioContext;
          if (!AudioContext) {
            return reject(new Error('AudioContext not supported'));
          }

          const audioContext = new AudioContext();

          // Special handling for WebM files which can sometimes cause issues
          const isWebmFile = audioBlob.type === 'video/webm' || audioBlob.type.includes('webm');

          // Log detailed info for debugging
          console.log(`Converting ${audioBlob.type} file to WAV format`);
          console.log(`File size: ${(audioBlob.size / (1024 * 1024)).toFixed(2)} MB`);

          // Get audio data from blob
          let arrayBuffer;
          try {
            arrayBuffer = await audioBlob.arrayBuffer();
            console.log(`Successfully got arrayBuffer of size: ${(arrayBuffer.byteLength / (1024 * 1024)).toFixed(2)} MB`);
          } catch (bufferError) {
            console.error('Error getting array buffer:', bufferError);
            return reject(new Error('Failed to read audio data: ' + bufferError.message));
          }

          // For WebM files > 10MB, we use a more conservative approach
          const blobSizeMB = audioBlob.size / (1024 * 1024);
          if (isWebmFile && blobSizeMB > 10) {
            try {
              console.log('Using specialized WebM conversion for large file');

              // For very large files, reduce the sample rate during conversion
              const targetSampleRate = blobSizeMB > 20 ? 16000 : 22050; // Lower for very large files

              // Create a MediaElement as an alternative way to decode WebM
              const audio = new Audio();
              audio.src = URL.createObjectURL(audioBlob);

              // Wait for the audio to be loaded
              await new Promise(resolve => {
                audio.addEventListener('loadedmetadata', resolve);
                audio.addEventListener('error', (e) => reject(new Error('MediaElement load error: ' + e)));
                // Set a timeout in case loading hangs
                setTimeout(resolve, 3000);
              });

              console.log(`Audio duration: ${audio.duration} seconds`);

              // Create an offline context with target sample rate
              const offlineContext = new OfflineAudioContext(
                1, // mono for better compression
                audio.duration * targetSampleRate,
                targetSampleRate
              );

              // Create a media element source
              const source = offlineContext.createMediaElementSource(audio);
              source.connect(offlineContext.destination);

              // Start rendering
              audio.currentTime = 0;
              audio.play();
              const renderedBuffer = await offlineContext.startRendering();

              // Stop the audio element
              audio.pause();
              URL.revokeObjectURL(audio.src);

              // Convert to WAV
              const wavBuffer = this.audioBufferToWav(renderedBuffer);
              const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });

              console.log(`Successfully converted large WebM to WAV: ${(wavBlob.size / (1024 * 1024)).toFixed(2)} MB`);
              resolve(wavBlob);
              return;
            } catch (specialConversionError) {
              console.warn('Specialized WebM conversion failed, falling back to standard method:', specialConversionError);
              // Continue with standard method
            }
          }

          // Standard method: Decode the audio data using AudioContext
          try {
            audioContext.decodeAudioData(arrayBuffer, (audioBuffer) => {
              try {
                // Log audio properties for debugging
                console.log(`Audio decoded successfully: ${audioBuffer.numberOfChannels} channels, ${audioBuffer.sampleRate}Hz, ${audioBuffer.duration}s`);

                // Prepare WAV file format
                const wavBuffer = this.audioBufferToWav(audioBuffer);

                // Create new blob with WAV format
                const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
                console.log(`WAV conversion complete: ${(wavBlob.size / (1024 * 1024)).toFixed(2)} MB`);

                resolve(wavBlob);
              } catch (encodeError) {
                console.error('Error encoding WAV:', encodeError);
                reject(encodeError);
              }
            }, (decodeError) => {
              console.error('Error decoding audio data:', decodeError);

              // For WebM files, try an alternative approach if standard decoding fails
              if (isWebmFile) {
                console.log('Attempting alternative WebM conversion approach');
                this.convertWebmAlternative(audioBlob)
                  .then(resolve)
                  .catch(reject);
              } else {
                reject(decodeError);
              }
            });
          } catch (contextError) {
            console.error('AudioContext error:', contextError);
            reject(contextError);
          }
        } catch (error) {
          console.error('Error in WAV conversion:', error);
          reject(error);
        }
      });
    },

    // Alternative method for WebM conversion when standard approach fails
    async convertWebmAlternative(webmBlob) {
      return new Promise(async (resolve, reject) => {
        try {
          console.log('Using alternative WebM conversion');

          // Create audio element to play the WebM
          const audio = new Audio();
          audio.src = URL.createObjectURL(webmBlob);

          // Create MediaRecorder to capture the output
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          const dest = audioContext.createMediaStreamDestination();

          // Wait for audio to be loaded
          await new Promise(resolve => {
            audio.addEventListener('canplaythrough', resolve);
            audio.load();
            // Set timeout to prevent hanging
            setTimeout(resolve, 5000);
          });

          console.log('WebM loaded in alternative approach, duration:', audio.duration);

          // If we can't get the duration, estimate based on file size
          const durationEstimate = audio.duration || (webmBlob.size / 16000); // rough estimate

          // Create a new buffer at 22kHz mono for good compression and compatibility
          const sampleRate = 22050;
          const offlineContext = new OfflineAudioContext(1, durationEstimate * sampleRate, sampleRate);

          // Play through the audio element and record
          const source = offlineContext.createMediaElementSource(audio);
          source.connect(offlineContext.destination);

          // Start playing
          audio.currentTime = 0;
          await audio.play();

          // Start rendering
          const renderedBuffer = await offlineContext.startRendering();

          // Stop and clean up
          audio.pause();
          URL.revokeObjectURL(audio.src);

          // Convert to WAV
          const wavBuffer = this.audioBufferToWav(renderedBuffer);
          const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });

          console.log(`Alternative WebM conversion successful: ${(wavBlob.size / (1024 * 1024)).toFixed(2)} MB`);
          resolve(wavBlob);
        } catch (error) {
          console.error('Alternative WebM conversion failed:', error);
          reject(error);
        }
      });
    },

    // Convert AudioBuffer to WAV format
    audioBufferToWav(audioBuffer) {
      const numberOfChannels = audioBuffer.numberOfChannels;
      const length = audioBuffer.length * numberOfChannels;
      const sampleRate = audioBuffer.sampleRate;
      const bitsPerSample = 16;
      const bytesPerSample = bitsPerSample / 8;
      const blockAlign = numberOfChannels * bytesPerSample;
      const byteRate = sampleRate * blockAlign;
      const dataSize = length * bytesPerSample;

      // Create buffer for WAV file
      const buffer = new ArrayBuffer(44 + dataSize);
      const view = new DataView(buffer);

      // Write WAV header
      // "RIFF" chunk descriptor
      this.writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + dataSize, true);
      this.writeString(view, 8, 'WAVE');

      // "fmt " sub-chunk
      this.writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true); // fmt chunk size
      view.setUint16(20, 1, true); // audio format (1 for PCM)
      view.setUint16(22, numberOfChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, byteRate, true);
      view.setUint16(32, blockAlign, true);
      view.setUint16(34, bitsPerSample, true);

      // "data" sub-chunk
      this.writeString(view, 36, 'data');
      view.setUint32(40, dataSize, true);

      // Write audio data
      let offset = 44;
      for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
        const channelData = audioBuffer.getChannelData(i);
        for (let j = 0; j < channelData.length; j++) {
          const sample = Math.max(-1, Math.min(1, channelData[j]));
          view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
          offset += 2;
        }
      }

      return buffer;
    },

    // Helper for audioBufferToWav
    writeString(view, offset, string) {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    },

    // Compress audio optimized for Deepgram speech recognition
    async compressAudio(audioBlob) {
      return new Promise(async (resolve, reject) => {
        try {
          const AudioContext = window.AudioContext || window.webkitAudioContext;
          if (!AudioContext) {
            return reject(new Error('AudioContext not supported'));
          }

          const audioContext = new AudioContext();
          console.log('Optimizing audio for Deepgram processing...');

          // Get audio data from blob
          const arrayBuffer = await audioBlob.arrayBuffer();

          // Decode the audio data
          audioContext.decodeAudioData(arrayBuffer, (audioBuffer) => {
            try {
              console.log('Original audio properties:', {
                sampleRate: audioBuffer.sampleRate,
                numberOfChannels: audioBuffer.numberOfChannels,
                duration: audioBuffer.duration
              });

              // Specifically optimize for Deepgram
              // Use mono 16kHz as recommended by Deepgram for best speech recognition
              const deepgramOptimalSampleRate = 16000; // 16kHz is optimal for Deepgram
              const offlineContext = new OfflineAudioContext(
                1, // Force mono (1 channel)
                Math.ceil(audioBuffer.duration * deepgramOptimalSampleRate),
                deepgramOptimalSampleRate
              );

              // Create a buffer source
              const source = offlineContext.createBufferSource();
              source.buffer = audioBuffer;

              // Optional: Add a low-pass filter to remove high frequencies 
              // This can improve compression without affecting speech quality
              if (audioBuffer.sampleRate > 22050) {
                const lowPassFilter = offlineContext.createBiquadFilter();
                lowPassFilter.type = 'lowpass';
                lowPassFilter.frequency.value = 8000; // Filter frequencies above 8kHz

                source.connect(lowPassFilter);
                lowPassFilter.connect(offlineContext.destination);
              } else {
                // For lower sample rate audio, just connect directly
                source.connect(offlineContext.destination);
              }

              // Start the source
              source.start(0);

              // Render and get the downsampled buffer
              offlineContext.startRendering().then(renderedBuffer => {
                console.log('Deepgram-optimized audio properties:', {
                  sampleRate: renderedBuffer.sampleRate,
                  numberOfChannels: renderedBuffer.numberOfChannels,
                  duration: renderedBuffer.duration
                });

                // Convert to WAV format optimized for speech
                // For Deepgram, 16-bit depth provides sufficient quality while reducing file size
                const wavBuffer = this.audioBufferToCompressedWav(renderedBuffer);

                // Create blob
                const compressedBlob = new Blob([wavBuffer], { type: 'audio/wav' });

                // Log compression ratio
                const originalSizeMB = audioBlob.size / (1024 * 1024);
                const compressedSizeMB = compressedBlob.size / (1024 * 1024);
                const compressionRatio = (1 - (compressedSizeMB / originalSizeMB)) * 100;
                console.log(`Original: ${originalSizeMB.toFixed(2)} MB → Compressed: ${compressedSizeMB.toFixed(2)} MB (${compressionRatio.toFixed(1)}% reduction)`);

                resolve(compressedBlob);
              }).catch(error => {
                console.error('Error rendering audio:', error);
                reject(error);
              });
            } catch (error) {
              console.error('Error compressing audio:', error);
              reject(error);
            }
          }, error => {
            console.error('Error decoding audio:', error);
            reject(error);
          });
        } catch (error) {
          console.error('Error in audio compression:', error);
          reject(error);
        }
      });
    },

    // Creates a more aggressively compressed WAV with reduced bit depth
    audioBufferToCompressedWav(audioBuffer) {
      const numberOfChannels = audioBuffer.numberOfChannels;
      const length = audioBuffer.length * numberOfChannels;
      const sampleRate = audioBuffer.sampleRate;
      const bitsPerSample = 8; // Use 8-bit instead of 16-bit for more compression
      const bytesPerSample = bitsPerSample / 8;
      const blockAlign = numberOfChannels * bytesPerSample;
      const byteRate = sampleRate * blockAlign;
      const dataSize = length * bytesPerSample;

      // Create buffer for WAV file
      const buffer = new ArrayBuffer(44 + dataSize);
      const view = new DataView(buffer);

      // Write WAV header (same as regular WAV but with 8-bit PCM)
      this.writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + dataSize, true);
      this.writeString(view, 8, 'WAVE');

      // "fmt " sub-chunk
      this.writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true); // fmt chunk size
      view.setUint16(20, 1, true); // audio format (1 for PCM)
      view.setUint16(22, numberOfChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, byteRate, true);
      view.setUint16(32, blockAlign, true);
      view.setUint16(34, bitsPerSample, true);

      // "data" sub-chunk
      this.writeString(view, 36, 'data');
      view.setUint32(40, dataSize, true);

      // Write audio data with 8-bit depth
      let offset = 44;
      for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
        const channelData = audioBuffer.getChannelData(i);
        for (let j = 0; j < channelData.length; j++) {
          // Convert float to 8-bit unsigned integer (0-255)
          const sample = Math.max(-1, Math.min(1, channelData[j]));
          // Scale from -1.0 - 1.0 to 0 - 255
          const scaled = Math.floor((sample + 1) * 127.5);
          view.setUint8(offset, scaled);
          offset += 1;
        }
      }

      return buffer;
    },

    blobToBase64(blob) {
      return new Promise((resolve, reject) => {
        if (!blob) {
          reject(new Error('No blob provided'));
          return;
        }

        // Check if the blob is too large for direct base64 conversion (> 50MB)
        const blobSizeMB = blob.size / (1024 * 1024);
        if (blobSizeMB > 50) {
          reject(new Error('File too large for base64 conversion. Try using a shorter recording or different format.'));
          return;
        }

        // For WebM files specifically, we need to handle them differently
        if (blob.type === 'video/webm' || blob.type.includes('webm')) {
          // First try to convert to WAV format for better server compatibility
          this.convertToWav(blob)
            .then(wavBlob => {
              // Now convert the WAV blob to base64
              const reader = new FileReader();
              reader.onloadend = () => resolve(reader.result);
              reader.onerror = (e) => {
                console.error('FileReader error for converted WebM:', e);
                reject(new Error('Failed to read converted audio data'));
              };
              reader.readAsDataURL(wavBlob);
            })
            .catch(error => {
              console.error('Error converting WebM to WAV:', error);

              // Fallback to direct base64 conversion with size check
              if (blobSizeMB > 10) {
                reject(new Error('WebM file is too large. Please use a shorter recording or different format.'));
                return;
              }

              const reader = new FileReader();
              reader.onloadend = () => resolve(reader.result);
              reader.onerror = (e) => {
                console.error('FileReader error for fallback:', e);
                reject(new Error('Failed to read audio data'));
              };
              reader.readAsDataURL(blob);
            });
        } else {
          // For non-WebM files, proceed with normal base64 conversion
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result);
          reader.onerror = (e) => {
            console.error('FileReader error:', e);
            reject(new Error('Failed to read audio data'));
          };
          reader.readAsDataURL(blob);
        }
      });
    },

    showManualTranscriptDialog() {
      this.transcript = '';
      this.showManualModal = true;
    },

    closeManualModal() {
      this.showManualModal = false;
    },

    async processManualTranscript() {
      try {
        if (!this.transcript || this.transcript.trim() === '') {
          throw new Error('Please enter a transcript');
        }

        this.closeManualModal();
        this.status = 'analyzing';

        // Get transcript length for estimation purposes
        const wordCount = this.transcript.split(/\s+/).length;
        console.log(`Manual transcript contains approximately ${wordCount} words`);

        // Show appropriate message based on length
        if (wordCount > 1000) {
          try {
            displayMessage(`This is a long transcript (${wordCount} words). Analysis may take several minutes.`, { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }
        }

        // Check if transcript is extremely large (could cause timeouts)
        if (wordCount > 5000) {
          // For very large transcripts, warn the user
          try {
            displayMessage('This transcript is very long. For better results, consider splitting it into multiple shorter consultations.', { timeout: 10000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }
        }

        // Process the manually entered transcript
        // First attempt to extract speakers if they're marked in the text
        this.diarizedTranscript = this.extractSpeakersFromText(this.transcript);

        // Check if speaker extraction was successful
        if (this.diarizedTranscript.length === 0 && wordCount > 20) {
          // If no speakers were detected in a substantial transcript, provide a hint
          try {
            displayMessage('Tip: For better analysis results, prefix each speaker with "Doctor:" or "Patient:" in your transcript.', { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }
        }

        await this.analyzeTranscript();
      } catch (error) {
        console.error('Error processing manual transcript:', error);
        this.mediaError = error.message || 'Failed to process transcript';
        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError);
        }
        this.status = 'idle';
      }
    },

    // Helper method to extract speakers from manually entered text
    extractSpeakersFromText(text) {
      if (!text) return [];

      const lines = text.split('\n');
      const diarized = [];
      let currentSpeaker = null;
      let currentText = '';

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        const doctorMatch = trimmedLine.match(/^Doctor:(.+)/i);
        const patientMatch = trimmedLine.match(/^Patient:(.+)/i);

        if (doctorMatch) {
          // If we had a previous speaker, add their text
          if (currentSpeaker !== null && currentText) {
            diarized.push({
              speaker: currentSpeaker,
              text: currentText.trim()
            });
          }

          // Start new doctor text
          currentSpeaker = 0;
          currentText = doctorMatch[1];
        } else if (patientMatch) {
          // If we had a previous speaker, add their text
          if (currentSpeaker !== null && currentText) {
            diarized.push({
              speaker: currentSpeaker,
              text: currentText.trim()
            });
          }

          // Start new patient text
          currentSpeaker = 1;
          currentText = patientMatch[1];
        } else if (currentSpeaker !== null) {
          // Continue text for current speaker
          currentText += ' ' + trimmedLine;
        } else {
          // If no speaker detected yet, assume doctor starts
          currentSpeaker = 0;
          currentText = trimmedLine;
        }
      }

      // Add the last speaker's text
      if (currentSpeaker !== null && currentText) {
        diarized.push({
          speaker: currentSpeaker,
          text: currentText.trim()
        });
      }

      return diarized;
    },

    // Show transcript dialog with diarized content
    showTranscriptDialog() {
      console.log('Opening transcript dialog with data:', {
        diarizedTranscript: this.diarizedTranscript,
        transcript: this.transcript
      });

      // Make sure diarized transcript is populated in the modal
      // Sometimes the diarized transcript might be empty even though full transcript exists
      if ((!this.diarizedTranscript || this.diarizedTranscript.length === 0) && this.transcript) {
        // Create a simple diarized version based on the full transcript
        this.diarizedTranscript = this.extractSpeakersFromText(this.transcript);
        console.log('Created diarized transcript from full text:', this.diarizedTranscript);
      }

      this.showTranscriptModal = true;
    },

    closeModal() {
      this.showTranscriptModal = false;
    },

    closeResultsModal() {
      this.showResultsModal = false;
    },

    async analyzeTranscript() {
      try {
        if (!this.transcript || this.transcript.trim() === '') {
          throw new Error('Transcript is empty');
        }

        // Close transcript dialog if it's open
        if (this.showTranscriptModal) {
          this.closeModal();
        }

        this.status = 'analyzing';

        // Display a message about longer wait times for lengthy transcripts
        const wordCount = this.transcript.split(/\s+/).length;
        if (wordCount > 200) {
          try {
            displayMessage('This is a longer transcript. Analysis may take up to 3 minutes...', { timeout: 8000 });
          } catch (e) {
            console.warn('Could not display message:', e);
          }
        }

        console.log('Analyzing transcript...');

        // Set up timeout handling with a longer timeout for larger transcripts
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 240000); // 4 minute timeout

        try {
          const response = await post("ai_scribe_analyze", {
            transcript: this.transcript,
            encounter_id: this.encounterId
          }, {
            signal: controller.signal
          });

          clearTimeout(timeoutId); // Clear the timeout if request completes

          console.log('Analysis response:', response);

          if (!response || !response.data) {
            throw new Error('No response from server');
          }

          if (response.data && response.data.status) {
            this.analysisResults = response.data.data;
            console.log('Analysis results:', this.analysisResults);
            this.showResultsModal = true;
            this.status = 'analyzed';
          } else {
            throw new Error(response.data?.message || 'Failed to analyze transcript');
          }
        } catch (abortError) {
          if (abortError.name === 'AbortError') {
            throw new Error('Analysis timed out. The transcript may be too long to process.');
          } else {
            throw abortError;
          }
        }
      } catch (error) {
        console.error('Error analyzing transcript:', error);
        this.mediaError = `Failed to analyze transcript: ${error.message || 'Unknown error'}`;

        // Suggest solutions based on the error
        let suggestion = '';
        if (error.message.includes('timed out')) {
          suggestion = ' Try shortening the transcript or breaking it into smaller sections.';
        }

        try {
          displayErrorMessage(this.mediaError + suggestion);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError + suggestion);
        }
        this.status = 'transcribed'; // Go back to transcribed state
      }
    },

    formatKey(key) {
      // Convert keys like 'present_concerns' to 'Present Concerns'
      if (!key) return '';
      return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    },

    async populateRecords() {
      try {
        if (!this.analysisResults) {
          throw new Error('No analysis results to populate');
        }

        this.closeResultsModal();
        this.status = 'populating';

        console.log('Populating records with data:', this.analysisResults);

        // Map the analysis results to the appropriate tab types
        // Include all possible tab types from the new system prompt
        const mappedData = {
          vitals: this.analysisResults.vitals || '',
          concerns: this.analysisResults.concerns || '',
          history: this.analysisResults.history || '',
          examination: this.analysisResults.examination || '',
          systems_review: this.analysisResults.systems_review || '',
          allergies: this.analysisResults.allergies || '',
          family_history: this.analysisResults.family_history || '',
          medical_history: this.analysisResults.medical_history || '',
          medications: this.analysisResults.medications || '',
          social_history: this.analysisResults.social_history || '',
          mental_health: this.analysisResults.mental_health || '',
          lifestyle: this.analysisResults.lifestyle || '',
          safeguarding: this.analysisResults.safeguarding || '',
          notes: this.analysisResults.notes || '',
          comments: this.analysisResults.comments || '',
          safety_netting: this.analysisResults.safety_netting || '',
          preventative_care: this.analysisResults.preventative_care || '',
          plan: this.analysisResults.plan || ''
        };

        // First, emit an event to the parent with the extracted data
        // This allows the parent to manage the AI population process
        this.$emit('ai-populate', {
          extractedData: mappedData,
          rawData: this.analysisResults
        });

        // Also send to the server for saving
        const response = await post("ai_scribe_populate", {
          data: JSON.stringify(this.analysisResults),
          encounter_id: this.encounterId
        });

        console.log('Population response:', response);

        if (!response || !response.data) {
          throw new Error('No response from server');
        }

        if (response.data && response.data.status) {
          try {
            displayMessage('Medical records populated by AI');
          } catch (e) {
            console.error('Failed to display success message:', e);
            alert('Medical records populated by AI');
          }

          this.status = 'completed';

          // After 3 seconds, reset to idle
          setTimeout(() => {
            this.status = 'idle';
          }, 3000);
        } else {
          throw new Error(response.data?.message || 'Failed to populate records');
        }
      } catch (error) {
        console.error('Error populating records:', error);
        this.mediaError = `Failed to populate records: ${error.message || 'Unknown error'}`;
        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError);
        }
        this.status = 'analyzed'; // Go back to analyzed state
      }
    }
  }
};
</script>

<style scoped>
@keyframes wave-1 {

  0%,
  100% {
    height: 0.5rem;
  }

  50% {
    height: 1.5rem;
  }
}

@keyframes wave-2 {

  0%,
  100% {
    height: 0.75rem;
  }

  50% {
    height: 2rem;
  }
}

@keyframes wave-3 {

  0%,
  100% {
    height: 1rem;
  }

  50% {
    height: 1.75rem;
  }
}

@keyframes wave-4 {

  0%,
  100% {
    height: 0.5rem;
  }

  50% {
    height: 1.25rem;
  }
}

.animate-wave-1 {
  animation: wave-1 1s ease-in-out infinite;
}

.animate-wave-2 {
  animation: wave-2 1s ease-in-out infinite;
}

.animate-wave-3 {
  animation: wave-3 1s ease-in-out infinite;
}

.animate-wave-4 {
  animation: wave-4 1s ease-in-out infinite;
}
</style>