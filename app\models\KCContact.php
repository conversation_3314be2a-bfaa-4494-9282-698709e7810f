<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCContact extends KCModel
{
    public function __construct()
    {
        parent::__construct('contacts');
    }

    /**
     * Get contacts that are globally visible (not clinic-specific)
     * 
     * @return array|object|null
     */
    public function getGlobalContacts()
    {
        return $this->get_by(['clinic_id' => null]);
    }

    /**
     * Get contacts for a specific clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return array|object|null
     */
    public function getClinicContacts($clinic_id)
    {
        return $this->get_by(['clinic_id' => (int)$clinic_id]);
    }

    /**
     * Get all contacts that should be visible to a specific clinic
     * This includes global contacts plus clinic-specific contacts
     * 
     * @param int $clinic_id Clinic ID
     * @return array|object|null
     */
    public function getVisibleContactsForClinic($clinic_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'kc_contacts';
        
        $query = "SELECT * FROM {$table_name} 
                  WHERE clinic_id IS NULL 
                  OR clinic_id = %d 
                  ORDER BY name ASC";
                  
        return $wpdb->get_results($wpdb->prepare($query, $clinic_id));
    }

    /**
     * Get contacts by type
     * 
     * @param string $type Contact type (general, doctor, clinic, pharmacy)
     * @return array|object|null
     */
    public function getContactsByType($type)
    {
        return $this->get_by(['type' => $type]);
    }
}