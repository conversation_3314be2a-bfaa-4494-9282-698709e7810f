import { post } from "../config/request";

export const helperDefaultGlobalCheckboxApplyData = (module) => {
  return {
    action_perform: "delete",
    module: module,
    data: [],
  };
};

export const helperDefaultGlobalCheckboxApplyDataActions = () => {
  return [
    {
      value: "active",
      label: window?.__kivicarelang?.service?.dt_active || "",
    },
    {
      value: "inactive",
      label: window?.__kivicarelang?.service?.dt_inactive || "",
    },
    {
      value: "delete",
      label: window?.__kivicarelang?.clinic_schedule?.dt_lbl_dlt || "",
    },
  ];
};

const helperGlobalCheckboxApply = (
  globalCheckboxApplyData,
  successCallback = null
) => {
  post("module_wise_multiple_data_update", globalCheckboxApplyData)
    .then((response) => {
      if (response?.data?.status && response.data.status === true) {
        displayMessage(response.data.message);
        if (successCallback) {
          successCallback(response.data);
        }
      } else {
        displayErrorMessage(response.data.message);
      }
    })
    .catch((error) => {
      console.log(error);
    });
};

export const helperGlobalAction = (
  globalCheckboxApplyData,
  successCallback = null
) => {
  let content = "";
  if (globalCheckboxApplyData?.action_perform === "delete") {
    content = window?.__kivicarelang?.common?.py_delete_clinic;
  } else if (
    globalCheckboxApplyData?.action_perform === "active" ||
    globalCheckboxApplyData?.action_perform === "inactive"
  ) {
    content = window?.__kivicarelang?.common?.py_status;
  }

  Swal.fire({
    title: window?.__kivicarelang?.clinic_schedule?.dt_are_you_sure,
    text: content,
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#dc3545", // Bootstrap danger color
    cancelButtonColor: "#6c757d", // Bootstrap secondary color
    confirmButtonText: window?.__kivicarelang?.common?.yes,
    cancelButtonText: window?.__kivicarelang?.common?.cancel,
    focusConfirm: false,
  }).then((result) => {
    if (result.isConfirmed) {
      helperGlobalCheckboxApply(globalCheckboxApplyData, successCallback);
    }
  });
};
