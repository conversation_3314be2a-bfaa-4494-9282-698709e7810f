<template>
  <div>
    <div class="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
      <!-- Sidebar z-index reduced to 30 -->
      <SideBar mode="false" class="z-30"></SideBar>

      <!-- Modified main content area to ensure proper spacing with sidebar -->
      <div class="transition-all duration-300" :class="!fullSideBar ? 'ml-20' : 'ml-64'">
        <!-- Reduced z-index for header to 20 to ensure modals can overlay properly -->
        <div class="sticky top-0 bg-white/80 backdrop-blur-sm border-b border-purple-100 z-20">
          <Header></Header>
        </div>
        <div class="p-2 container mx-auto">
          <transition name="fade" mode="out-in">
            <router-view></router-view>
          </transition>
        </div>
      </div>

      <!-- Fixed Action Buttons Container with higher z-index than sidebar and header -->
      <div class="fixed bottom-6 right-6 flex items-center space-x-4 z-40">
        <!-- AI Copilot (Commented out for now) -->
        <!-- <div v-if="getUserRole() != 'patient'" class="transition-all duration-300 w-auto group">
          <div class="absolute bottom-full right-0 mb-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div class="bg-white rounded-2xl shadow-lg py-3 px-4 mb-2 whitespace-nowrap">
              <p class="text-gray-700 text-base">Your AI Copilot is arriving soon ✨</p>
            </div>
          </div>
          <button class="bg-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="28"
              height="28"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-7 h-7 text-gray-700"
            >
              <path d="M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z" />
              <path d="M16 8V5c0-1.1.9-2 2-2" />
              <path d="M12 13h4" />
              <path d="M12 18h6a2 2 0 0 1 2 2v1" />
              <path d="M12 8h8" />
              <path d="M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z" />
              <path d="M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z" />
              <path d="M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z" />
            </svg>
          </button>
        </div> -->
      </div>
      
      <!-- Chat Widget - Positioned at bottom left -->
      <ChatInterface v-if="getUserRole() !== 'patient'"/>
      </div>
  </div>
</template>

<script>
import Header from "./components/Partials/Header";
import SideBar from "./components/Partials/SideBar";
import Body from "./components/Partials/Body";
import ChatInterface from "./ChatInterface";

export default {
  name: "App",
  components: {
    Header,
    SideBar,
    Body,
    ChatInterface,
  },
  data: () => {
    return {
      tempColor: localStorage.getItem("temp_color"),
      backgroundImage:
        "url(" +
        pluginBASEURL +
        "images/vendor/vue-phone-number-input/dist/flags.9c96e0ed.png)",
    };
  },
  computed: {
    fullSideBar() {
      return this.$store.state.fullSideBar;
    },
    userData() {
      if (this.$store.state !== undefined) {
        return this.$store.state.userDataModule.user;
      }
    },
    getColor() {
      if (this.tempColor == "" || this.tempColor == null) {
        return this.userData.theme_color;
      }
      return this.tempColor;
    },
    getLang() {
      return this.userData.get_lang;
    },
    getMode() {
      return this.userData.theme_mode;
    },
    formTranslationData: function () {
      if (this.$store.state.staticDataModule.langTranslateData !== undefined) {
        return this.$store.state.staticDataModule.langTranslateData;
      }
    },
  },
  watch: {
    getMode: function (value) {
      if (value == "true") {
        document.body.classList.add("rtl");
        var h1 = document.getElementsByTagName("html")[0];
        var att = document.createAttribute("dir");
        att.value = "rtl";
        h1.setAttributeNode(att);
      } else {
        document.body.classList.remove("rtl");
        document.getElementsByTagName("html")[0].removeAttribute("dir");
      }
    },
    getLang: function (value) {
      this.$i18n.locale = value.id;
    },
  },
  mounted() {
    this.init();
    if (
      window.request_data.current_user_role !== "" &&
      window.request_data.current_user_role !== "kiviCare_patient"
    ) {
      this.$store.dispatch("staticDataModule/fetchStaticData", {
        type: "static_data",
        static_data_type: "specialization",
      });
    }
    this.$store.dispatch("userDataModule/fetchDefaultClinic");
    this.$store.dispatch("logout_redirect_url", {
      data: window.request_data.logout_redirect_url,
    });
    this.$store.commit("appointmentModule/FILE_UPLOAD_STATUS", {
      data: window.request_data.file_upload_status,
    });
    this.$store.commit("appointmentModule/DESCRIPTION_STATUS", {
      data: window.request_data.description_status,
    });
    this.$store.commit("appointmentModule/PATIENT_INFO_STATUS", {
      data: window.request_data.patient_detail_info_status,
    });
    this.$store.commit("FETCH_DATE_FORMAT", {
      data: window.request_data.date_format,
    });
    this.$store.commit("FETCH_FOOTER_COPYRIGHT_TEXT", {
      data: window.request_data.copyrightText,
    });
    this.$store.commit("FETCH_WORDPRESS_LOGO", {
      data: {
        logo: window.request_data.wordpress_logo,
        status: window.request_data.wordpress_logo_status,
      },
    });
  },
  methods: {
    init: function () {
      $(".notice.notice-warning").remove();
      $(".notice.e-notice").remove();
      this.getDynamicTranslation();
      if (
        this.formTranslation.months_short !== undefined &&
        this.formTranslation.months !== undefined
      ) {
        window.moment.updateLocale("en", {
          monthsShort: Object.values(this.formTranslation.months_short),
          months: Object.values(this.formTranslation.months),
        });
      }
    },
    getUserRole() {
      const user = this.$store.state.userDataModule.user;
      if (user && user.roles) {
        if (user.roles.includes('administrator')) return 'administrator';
        if (user.roles.includes('clinic_admin')) return 'clinic_admin';
        if (user.roles.includes('doctor')) return 'doctor';
        if (user.roles.includes('patient')) return 'patient';
        if (user.roles.includes('receptionist')) return 'receptionist';
      }
      return '';
    },
    getDynamicTranslation: function () {
      this.$store.dispatch("staticDataModule/fetchLangTranslateData", {
        filePath: window.request_data.kiviCarePluginURL,
        current: "temp",
        module: "",
      });
    },
  },
};
</script>

<style>
.iti-flag {
  background-image: v-bind(backgroundImage) !important;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>