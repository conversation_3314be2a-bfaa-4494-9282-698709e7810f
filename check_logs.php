<?php
// This is a temporary file for debugging - remove after use
// Find the WordPress installation path
$path = __DIR__;
while (!file_exists($path . '/wp-config.php')) {
    $path = dirname($path);
    if ($path === '/' || empty($path)) {
        die('WordPress installation not found.');
    }
}
require_once($path . '/wp-load.php');
global $wpdb;

echo "Checking if activity logs table exists...\n";
$table_name = $wpdb->prefix . 'kc_activity_logs';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
echo "Table {$table_name} exists: " . ($table_exists ? 'Yes' : 'No') . "\n";

if ($table_exists) {
    echo "Getting logs from table...\n";
    $rows = $wpdb->get_results("SELECT * FROM {$table_name} LIMIT 10");
    echo "Found " . count($rows) . " logs\n";
    print_r($rows);
} else {
    echo "Creating table...\n";
    require_once('app/database/kc-activity-log-db.php');
    kivicareCreateActivityLogTable();
    echo "Table creation attempted.\n";
    
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    echo "Table exists after creation attempt: " . ($table_exists ? 'Yes' : 'No') . "\n";
}

// Create a test log entry to verify logging works
echo "Creating test log entry...\n";
$user_id = get_current_user_id();
$user_data = get_userdata($user_id);
$user_role = reset($user_data->roles);

$insert_result = $wpdb->insert(
    $table_name,
    [
        'user_id' => $user_id,
        'user_type' => $user_role,
        'activity_type' => 'test_activity',
        'activity_description' => 'Test activity log entry',
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'created_at' => current_time('mysql')
    ]
);
echo "Test entry created: " . ($insert_result ? 'Yes' : 'No') . "\n";
if (!$insert_result) {
    echo "Database error: " . $wpdb->last_error . "\n";
}

// Check if the entry was created
echo "Verifying test entry...\n";
$test_entry = $wpdb->get_row("SELECT * FROM {$table_name} WHERE activity_type = 'test_activity' ORDER BY id DESC LIMIT 1");
echo "Test entry found: " . ($test_entry ? 'Yes' : 'No') . "\n";
if ($test_entry) {
    print_r($test_entry);
}