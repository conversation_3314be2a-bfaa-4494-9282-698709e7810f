<template>
  <div>
    <form :id="appointmentFormObj.id ? `appointmentDataForm${appointmentFormObj.id}` : 'appointmentDataForm'"
      :appointment-form-id="appointmentFormObj.id || 0" enctype="multipart/form-data" @submit.prevent="handleFormSubmit"
      :novalidate="true">
      <div class="px-4 py-4">
        <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
          <!-- Left Column -->
          <div class="md:col-span-5">
            <!-- Clinic Selection -->
            <div class="mb-4" v-if="
              userData.addOns.kiviPro === true &&
              (getUserRole() === 'administrator' ||
                getUserRole() === 'doctor' ||
                getUserRole() === 'patient')">
              <label for="clinic_id" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.select_clinic }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select v-model="appointmentFormObj.clinic_id" :options="clinics" :loading="clinicMultiselectLoader"
                :disabled="disabledClinicField" @select="clinicChange" @remove="clinicChange" id="clinic_id"
                label="label" track-by="id" :placeholder="formTranslation.appointments.search_plh"
                :tag-placeholder="formTranslation.appointments.select_clinic_plh" deselect-label="" select-label=""
                class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.clinic_id.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.clinic_is_required }}
              </p>
            </div>

            <!-- Doctor Selection -->
            <div v-if="getUserRole() !== 'doctor'" class="mb-4">
              <label for="doctor_id" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.doctor }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select v-model="appointmentFormObj.doctor_id" :options="doctors" :loading="doctorMultiselectLoader"
                :disabled="disabledDoctorField" @select="handleDoctorChange" @remove="handleDoctorUnselect"
                id="doctor_id" label="label" track-by="id" :placeholder="formTranslation.appointments.search_plh"
                :tag-placeholder="formTranslation.appointments.doctor_plh" deselect-label="" select-label=""
                class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.doctor_id.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.doc_required }}
              </p>
            </div>

            <!-- Service Selection -->
            <div class="mb-4">
              <div class="flex justify-between items-center mb-1">
                <label for="visit_type" class="block text-sm font-medium text-gray-700">
                  {{ formTranslation.common.service }}
                  <span class="text-red-500">*</span>
                </label>
                <router-link v-if="canAddService" :to="{ name: 'service' }" class="text-sm text-black hover:text-black">
                  <i class="fa fa-plus"></i>
                  {{ formTranslation.common.service_add }}
                </router-link>
              </div>
              <multi-select v-model="appointmentFormObj.visit_type" :options="appointmentTypes"
                :loading="serviceMultiselectLoader" :disabled="disabledServiceField"
                @select="appointmentTypeChangeSelect" @remove="appointmentTypeChangeUnselect" id="visit_type"
                label="name" track-by="id" :placeholder="formTranslation.appointments.search_plh"
                :tag-placeholder="formTranslation.appointments.tag_visit_type_plh"
                :close-on-select="!appointmentTypeMultiselect" class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.visit_type.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.patient_bill.service_required }}
              </p>
            </div>

            <!-- Date Picker -->
            <div class="mb-4">
              <label for="appointment_start_date" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.appointment_date }}
                <span class="text-red-500">*</span>
              </label>
              <vc-date-picker v-model="appointmentFormObj.appointment_start_date" id="appointmentDate" is-expanded
                title-position="left" :available-dates="{ weekdays: getAvailableDays() }" :min-date="minDate"
                :max-date="maxDate" :masks="masks" :popover="{ placement: 'bottom', visibility: 'click' }"
                @input="handleDateChange" class="w-full">
                <template v-slot="{ inputValue, inputEvents }">
                  <input
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                    readonly :value="inputValue" v-on="inputEvents"
                    :placeholder="formTranslation.appointments.appointment_date_plh" />
                </template>
              </vc-date-picker>
              <p v-if="submitted && !$v.appointmentFormObj.appointment_start_date.required"
                class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.appointment_date_required }}
              </p>
            </div>

            <!-- Patient Selection -->
            <div v-if="showPatientSelection" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.patient }}
                <span class="text-red-500">*</span>
                <span v-if="canAddPatient" class="float-right">
                  <router-link :to="{ name: 'patient.create' }" class="text-black text-sm">
                    <i class="fa fa-plus"></i>
                    {{ formTranslation.patient.add_patient }}
                  </router-link>
                </span>
              </label>
              <multi-select v-model="appointmentFormObj.patient_id" :options="patients"
                :loading="patientMultiselectLoader" :disabled="disabledPatientField" id="patient_id" label="label"
                track-by="id" :placeholder="formTranslation.appointments.search_plh"
                :tag-placeholder="formTranslation.appointments.tag_patient_type_plh" class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.patient_id.required" class="text-sm text-red-500 mt-1">
                {{ formTranslation.appointments.patient_requires }}
              </p>
            </div>

            <!-- Status Selection -->
            <div v-if="getUserRole() !== 'patient'" class="mb-4">
              <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.status }}
                <span class="text-red-500">*</span>
              </label>
              <select v-model="appointmentFormObj.status" id="status" name="status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black">
                <option value="">{{ formTranslation.appointments.select_status }}</option>
                <option value="1">{{ formTranslation.appointments.booked }}</option>
                <option value="2">{{ formTranslation.appointments.pending }}</option>
                <option value="3">{{ formTranslation.appointments.check_out }}</option>
                <option value="4">{{ formTranslation.appointments.check_in }}</option>
                <option value="0">{{ formTranslation.appointments.cancelled }}</option>
              </select>
              <p v-if="submitted && !$v.appointmentFormObj.status.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.status_required }}
              </p>
            </div>

            <!-- Reason for Visit -->
            <div class="mb-4">
              <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.description || 'Reason for Visit' }}
              </label>
              <textarea
                v-model="appointmentFormObj.description"
                id="description"
                name="description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                :placeholder="formTranslation.appointments.appointment_desc_plh || 'Enter reason for visit'"
              ></textarea>
            </div>

            <!-- File Upload Section -->
            <div v-if="fileUploadEnable === 'on' && !appointmentFormObj.id" class="mb-4">
              <h5 class="text-lg font-medium mb-3">
                {{ formTranslation.patient.add_medical_report }}
              </h5>
              <div class="flex">
                <button type="button" id="appointmentreport"
                  class="px-4 py-2 bg-black text-white rounded-md hover:bg-black focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
                  @click.prevent="multiUploadProfile">
                  {{ formTranslation.common.choose_file }}
                </button>
                <span class="ml-3 text-sm text-gray-600 self-center">
                  {{
                    upload_appointment_report.length > 0
                      ? upload_appointment_report.length + " File selected"
                      : formTranslation.common.no_file_chosen
                  }}
                </span>
              </div>
            </div>
          </div>

          <!-- Right Column -->
          <div class="md:col-span-7">
            <!-- Time Slots -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.available_slot }}
                <span class="text-red-500">*</span>
              </label>
              <div class="border border-gray-200 rounded-md p-4 overflow-y-auto"
                :class="getUserRole() !== 'doctor' && getUserRole() !== 'patient' ? 'h-96' : 'h-64'">
                <div v-if="timeSlots.length && appointmentFormObj.appointment_start_date">
                  <div v-for="(timeSlot, index) in timeSlots" :key="index" class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ formTranslation.appointments.session }} {{ index + 1 }}
                    </label>
                    <div class="flex flex-wrap gap-2">
                      <template v-for="(slot, subIndex) in timeSlot">
                        <span v-if="!slot.available" :key="subIndex"
                          class="px-3 py-1 text-red-600 bg-red-100 rounded line-through">
                          {{ slot.time }}
                        </span>
                        <button v-else :key="'timeSlot' + subIndex" type="button"
                          class="px-3 py-1 rounded transition-colors" :class="appointmentFormObj.appointment_start_time === slot.time
                            ? 'bg-black text-white'
                            : 'border border-black text-black hover:bg-black hover:text-white'"
                          @click="handleTimeChange(slot.time)">
                          {{ slot.time }}
                        </button>
                      </template>
                    </div>
                  </div>
                </div>
                <div v-else class="flex items-center justify-center h-full">
                  <p class="text-black text-sm font-medium">
                    {{ formTranslation.appointments.no_time_slots_found }}
                  </p>
                </div>
              </div>
              <p v-if="submitted && !$v.appointmentFormObj.appointment_start_time.required"
                class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.time_slot_required }}
              </p>
            </div>

            <!-- Service Details -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.widgets.service_detail }}
              </label>
              <div class="border border-gray-200 rounded-md p-4 min-h-32 flex items-center justify-center">
                <div v-if="appointmentFormObj.visit_type && appointmentFormObj.visit_type.length > 0">
                  <div v-for="(service, index) in appointmentFormObj.visit_type" :key="index" class="text-center">
                    <span class="font-medium">{{ service.name }}</span>
                    <span class="ml-2">
                      {{ " - " + (appointmentFormObj.id ? prefix : "") + service.charges +
                        (appointmentFormObj.id ? postfix : "") }}
                    </span>
                  </div>
                </div>
                <div v-else>
                  <p class="text-black text-sm font-medium">
                    {{ formTranslation.widgets.no_service_detail_found }}.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Method -->
        <div v-if="!appointmentFormObj.id" class="w-full py-2 bg-white">
          <h6 class="block text-sm font-medium text-gray-700 mb-1">Payment Method</h6>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Stripe Payment Option -->
            <label :class="[
              'flex items-center px-2 p-4 border rounded-lg cursor-pointer transition-colors',
              appointmentFormObj.payment_mode === 'paymentStripepay'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:bg-gray-50'
            ]">
              <input type="radio" name="paymentMethod" value="paymentStripepay"
                v-model="appointmentFormObj.payment_mode"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
              <div class="ml-3 flex items-center">
                <div class="flex-shrink-0 mr-3">
                  <!-- Credit Card SVG Icon -->
                  <svg width="24" height="24" class="text-blue-800" xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor" viewBox="0 0 384 512">
                    <path
                      d="M155.3 154.6c0-22.3 18.6-30.9 48.4-30.9 43.4 0 98.5 13.3 141.9 36.7V26.1C298.3 7.2 251.1 0 203.8 0 88.1 0 11 60.4 11 161.4c0 157.9 216.8 132.3 216.8 200.4 0 26.4-22.9 34.9-54.7 34.9-47.2 0-108.2-19.5-156.1-45.5v128.5a396.1 396.1 0 0 0 156 32.4c118.6 0 200.3-51 200.3-153.6 0-170.2-218-139.7-218-203.9z" />
                  </svg>
                </div>
                <div>
                  <p class="font-medium text-gray-900">Stripe</p>
                  <p class="text-sm text-gray-500">Pay securely online with credit card</p>
                </div>
              </div>
            </label>

            <!-- Offline Payment Option -->
            <label :class="[
              'flex items-center px-2 p-4 border rounded-lg cursor-pointer transition-colors',
              appointmentFormObj.payment_mode === 'paymentOffline'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:bg-gray-50'
            ]">
              <input type="radio" name="paymentMethod" value="paymentOffline" v-model="appointmentFormObj.payment_mode"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
              <div class="ml-3 flex items-center">
                <div class="flex-shrink-0 mr-3">
                  <!-- Dollar Sign SVG Icon -->
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-green-600">
                    <line x1="12" y1="2" x2="12" y2="22"></line>
                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-medium text-gray-900">Pay Offline</p>
                  <p class="text-sm text-gray-500">Pay with cash, bank transfer or Cheque</p>
                </div>
              </div>
            </label>
            <!-- Pay Later Payment Option -->
            <label :class="[
              'flex items-center px-2 p-4 border rounded-lg cursor-pointer transition-colors',
              appointmentFormObj.payment_mode === 'paymentLater'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:bg-gray-50'
            ]">
              <input type="radio" name="paymentMethod" value="paymentLater" v-model="appointmentFormObj.payment_mode"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
              <div class="ml-3 flex items-center">
                <div class="flex-shrink-0 mr-3">
                  <!-- Clock SVG Icon -->
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-purple-600">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                </div>
                <div>
                  <p class="font-medium text-gray-900">Pay Later</p>
                  <p class="text-sm text-gray-500">Pay at a later date after your appointment</p>
                </div>
              </div>
            </label>
          </div>
          <p v-if="submitted && !$v.appointmentFormObj.payment_mode.required" class="text-sm text-red-500 mt-1">
            Please select a payment method to continue
          </p>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 mt-6">
          <button v-if="!loading" type="submit"
            class="px-4 py-2 bg-black text-white rounded-md hover:bg-black focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
            :disabled="!disabledButton">
            <i class="fa fa-save mr-2"></i>
            {{ appointmentFormObj.payment_mode === "paymentStripepay"
              ? 'Send payment link to patient'
              : formTranslation.appointments.save_btn
            }}
          </button>
          <button v-else type="submit" class="px-4 py-2 bg-black text-white rounded-md opacity-75 cursor-not-allowed"
            disabled>
            <i class="fa fa-sync fa-spin mr-2"></i>{{ formTranslation.common.loading }}
          </button>
          <button type="button"
            class="px-4 py-2 border border-black text-black rounded-md hover:bg-black hover:text-white focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
            @click="appointmentCloseForm">
            {{ formTranslation.common.cancel }}
          </button>
        </div>
      </div>
    </form>

    <!-- Appointment Summary Modal -->
    <div v-if="appointmentModel" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-auto">
          <!-- Modal Header -->
          <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              {{ formTranslation.widgets.summary }}
            </h3>
            <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none"
              @click="closeAppointmentModal">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="p-4">
            <input type="hidden" value="" id="payment_status_child" />
            <!-- Loader -->
            <div v-if="overlaySpinner" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
              <loader-component-2></loader-component-2>
            </div>
            <!-- Appointment Detail Component -->
            <AppointmentDetail ref="appointment_detail" :appointment-data="appointmentFormObj" :user-data="userData"
              :prefix="prefix" :postfix="postfix" @bookAppointment="bookAppointmentHandle"
              @cancelAppointment="closeAppointmentModal" :lazy="true" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { required } from "vuelidate/lib/validators";
import { validateForm } from "../../config/helper";
import { post, get } from "../../config/request";
import AppointmentDetail from "./AppointmentDetail";
import moment from "moment";

export default {
  name: "AppointmentForm",
  components: {
    AppointmentDetail
  },
  props: {
    appointmentData: {
      type: [Object, Array, Date],
      default: () => ({})
    },
    patient_profile_id: {
      type: [Number, String],
      default: ""
    }
  },
  validations: {
    appointmentFormObj: {
      appointment_start_date: { required },
      appointment_start_time: { required },
      visit_type: { required },
      clinic_id: { required },
      doctor_id: { required },
      patient_id: { required },
      status: { required },
      payment_mode: { required }
    }
  },
  data() {
    return {
      appointmentModel: false,
      overlaySpinner: false,
      loading: false,
      prefix: "",
      postfix: "",
      appointmentFormObj: {
        clinic_id: null,
        doctor_id: null,
        visit_type: [], // Initialize as empty array
        appointment_start_date: null,
        appointment_start_time: null,
        patient_id: null,
        status: "1",
        description: "",
        custom_fields: [],
        payment_mode: ""
      },
      submitted: false,
      doctors: [],
      timer: "",
      appointmentTypes: [],
      showCustomField: false,
      componentKey: 0,
      disabledDoctorField: false,
      disabledServiceField: false,
      disabledPatientField: false,
      disabledClinicField: false,
      DoctorWorkdays: [],
      holiday: {},
      restrictAppointment: {
        pre_book: "0",
        post_book: "365",
        only_same_day_book: "on"
      },
      requiredFields: [],
      patientRoleName: "patient",
      minDate: new Date(),
      maxDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365),
      disabledButton: true,
      patients: [],
      clinicMultiselectLoader: true,
      doctorMultiselectLoader: true,
      patientMultiselectLoader: true,
      serviceMultiselectLoader: false,
      appointmentTypeMultiselect: true,
      upload_appointment_report: [],
      taxes: [],
      masks: {
        input: "DD/MM/YYYY"
      },
      lastTimeSlotRequest: null
    };
  },
  computed: {
    showClinicSelection() {
      return (
        this.userData.addOns?.kiviPro === true &&
        ["administrator", "doctor", "patient"].includes(this.getUserRole())
      );
    },

    canAddPatient() {
      return (
        ["administrator", "clinic_admin", "receptionist", "doctor"].includes(
          this.getUserRole()
        ) &&
        !this.appointmentFormObj.id &&
        this.kcCheckPermission("patient_add")
      );
    },

    canAddService() {
      return (
        ["administrator", "clinic_admin", "receptionist", "doctor"].includes(
          this.getUserRole()
        ) &&
        !this.appointmentFormObj.id &&
        this.kcCheckPermission("service_add")
      );
    },

    showPatientSelection() {
      return (
        this.getUserRole() !== "patient" &&
        this.$route.params.patient_id === undefined &&
        !this.patient_profile_id
      );
    },

    timeSlots() {
      return this.$store.state.appointmentModule.timeSlots || [];
    },

    clinics() {
      if (this.$store.state.clinic.length > 0 && !this.appointmentFormObj.id && !this.appointmentFormObj.clinic_id) {
        this.$nextTick(() => {
          this.appointmentFormObj.clinic_id = this.$store.state.clinic[0];
          if (this.getUserRole() !== "doctor") {
            this.clinicChange(this.$store.state.clinic[0]);
          }
        });
      }

      this.clinicMultiselectLoader = false;
      return this.$store.state.clinic || [];
    },

    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },

    fileUploadEnable() {
      return this.$store.state.appointmentModule.file_upload_status;
    },

    enableDisableAppointmentDescriptionStatus() {
      return this.$store.state.appointmentModule.description_status;
    }
  },
  watch: {
    appointmentFormObj: {
      handler(newData) {
        this.appointmentFormObj.is_dashboard = newData.payment_mode === 'paymentStripepay';
      },
      deep: true
    }
  },
  async mounted() {
    // Get appointment restriction settings
    await this.getRestrictAppointmentDay();

    this.$nextTick(async () => {
      // Initialize form based on mode (edit or new)
      if (this.appointmentData.id) {
        await this.initFormData();
        this.setupEditMode();
      } else {
        await this.init();
      }

      // Special case for doctor role with no KiviPro
      if (this.getUserRole() === "doctor" && this.userData.addOns?.kiviPro !== true) {
        await this.getDoctorActiveDays(this.userData.default_clinic, this.userData.ID);
      }
    });
  },
  methods: {
    // Available days and holiday-related methods
    getAvailableDays() {
      // If DoctorWorkdays is empty, return all days
      if (!this.DoctorWorkdays || this.DoctorWorkdays.length === 0) {
        return [1, 2, 3, 4, 5, 6, 7]; // All days available (1=Monday, 7=Sunday)
      }

      // Return days that are NOT in DoctorWorkdays (those are the available days)
      return [1, 2, 3, 4, 5, 6, 7].filter(day => !this.DoctorWorkdays.includes(day));
    },

    isHoliday(date) {
      if (!this.holiday || !this.holiday.length) {
        return false;
      }

      return this.holiday.some(holiday => {
        const fromDate = new Date(holiday.start_date);
        const toDate = new Date(holiday.end_date);
        return date >= fromDate && date <= toDate;
      });
    },

    // Find next available date (not a holiday or disabled day)
    findNextAvailableDate() {
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);

      // Try the next 30 days
      for (let i = 0; i < 30; i++) {
        const testDate = new Date(currentDate);
        testDate.setDate(currentDate.getDate() + i);

        const dayOfWeek = testDate.getDay() + 1; // Convert to 1-7 format
        if (!this.DoctorWorkdays.includes(dayOfWeek) && !this.isHoliday(testDate)) {
          return testDate;
        }
      }

      return currentDate; // Default to today if no available days found
    },

    // Initialization methods
    async init() {
      this.appointmentFormObj.payment_mode = "";

      // Set up form based on edit or add mode
      if (this.appointmentData.id) {
        this.setupEditMode();
      } else {
        this.setupAddMode();
      }

      // Load initial data based on user role
      await this.setupInitialDataByRole();

      // Load patients list if not patient role
      if (this.getUserRole() !== "patient") {
        await this.loadPatientsList();
      }
    },

    setupEditMode() {
      this.taxes = this.appointmentFormObj.tax || [];
      this.showCustomField = true;

      // Disable fields in edit mode
      this.disabledDoctorField = true;
      this.disabledServiceField = true;
      this.disabledClinicField = true;
      this.disabledPatientField = true;

      // Reset loaders
      this.clinicMultiselectLoader = false;
      this.doctorMultiselectLoader = false;
      this.patientMultiselectLoader = false;
      this.serviceMultiselectLoader = false;

      // If we have clinic_id and doctor_id, load their data
      if (this.appointmentFormObj.clinic_id && this.appointmentFormObj.doctor_id) {
        this.getDoctorActiveDays(
          this.appointmentFormObj.clinic_id.id,
          this.appointmentFormObj.doctor_id.id
        );
        this.dispatchTimeSlot();
      }
    },

    setupAddMode() {
      this.appointmentFormObj.status = "1";
      const isOnlySameDay = this.restrictAppointment.only_same_day_book === "on";

      // Today's date (beginning of day)
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Set minimum date to today
      this.minDate = today;

      // Calculate maximum date based on settings
      this.maxDate = isOnlySameDay
        ? today // Same day only
        : new Date(
          today.getTime() +
          1000 * 60 * 60 * 24 *
          parseInt(this.restrictAppointment.post_book || 365)
        );

      // Don't set initial date - let user select it
      this.appointmentFormObj.appointment_start_date = null;
    },

    // Role-specific setup
    async setupInitialDataByRole() {
      if (this.getUserRole() === "doctor") {
        await this.setupDoctorRole();
      } else if (this.getUserRole() === "patient") {
        this.setupPatientRole();
      } else {
        await this.setupDefaultRole();
      }
    },

    async setupDoctorRole() {
      const doctorId = this.userData;
      this.showCustomField = true;

      // Set doctor in form object if not already set
      if (!this.appointmentFormObj.doctor_id) {
        this.appointmentFormObj.doctor_id = {
          id: doctorId.ID,
          label: doctorId.display_name
        };
      }

      // Only load services once
      if (this.appointmentTypes.length === 0) {
        await this.getDoctorsServices(doctorId.ID);
      }

      // Initialize clinic if needed
      if (this.clinics && this.clinics.id && !this.appointmentFormObj.clinic_id) {
        await this.handleInitialClinicChange({ id: this.clinics.id });
      }
    },

    setupPatientRole() {
      if (!this.appointmentFormObj.patient_id) {
        this.appointmentFormObj.patient_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
      }
    },

    async setupDefaultRole() {
      if (
        this.$store.state.clinic.length > 0 &&
        this.$store.state.clinic[0].id !== undefined
      ) {
        // Only trigger clinic change if no appointment data exists
        if (!Object.keys(this.appointmentData).length) {
          await this.handleInitialClinicChange({
            id: this.$store.state.clinic[0].id,
          });
        }
      }
    },

    // Data loading methods
    async handleInitialClinicChange(selectedClinic) {
      // This is a special version of clinicChange that doesn't clear form data
      if (this.getUserRole() !== "patient") {
        await this.loadPatientsList(selectedClinic.id);
      }

      if (this.getUserRole() === "doctor") {
        const doctorId = this.userData;
        await this.getDoctorsServices(doctorId.ID);
        await this.getDoctorActiveDays(selectedClinic.id, 1);
      } else {
        await this.loadDoctorsList(selectedClinic.id);
      }
    },

    async loadPatientsList(clinicId) {
      // Use cached data if available
      if (this.$store.state.userDataModule?.userDropDownData?.patients?.length > 0) {
        this.patientMultiselectLoader = false;
        this.patients = this.$store.state.userDataModule.userDropDownData.patients;
        return;
      }

      try {
        this.patientMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: clinicId,
        });

        if (response.data.status === true) {
          this.patients = response.data.data;
        }
      } finally {
        this.patientMultiselectLoader = false;
      }
    },

    async loadDoctorsList(clinicId) {
      try {
        this.doctorMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "get_users_by_clinic",
          clinic_id: clinicId,
          module_type: "appointment",
        });

        if (response.data.status === true) {
          this.doctors = response.data.data;
          this.postfix = response.data.postfix;
          this.prefix = response.data.prefix;
        }
      } finally {
        this.doctorMultiselectLoader = false;
      }
    },

    async getRestrictAppointmentDay() {
      try {
        const response = await get("restrict_appointment_edit", {});
        if (response.data.status === true) {
          this.restrictAppointment = response.data.data;
        }
      } catch (error) {
        console.log(error);
        displayErrorMessage(this.formTranslation.widgets.record_not_found);
      }
    },

    // Clearing and resetting methods
    clearAppointmentData() {
      this.upload_appointment_report = [];
      this.appointmentFormObj.doctor_id = "";
      this.appointmentFormObj.visit_type = [];

      // Handle role-specific defaults
      if (this.getUserRole() === "doctor") {
        this.appointmentFormObj.doctor_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
      } else if (this.getUserRole() === "patient") {
        this.appointmentFormObj.patient_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
      }

      // Handle patient ID from route or props
      let patient_id = "";
      if (this.$route.params.patient_id !== undefined) {
        patient_id = this.$route.params.patient_id;
      } else if (this.patient_profile_id) {
        patient_id = this.patient_profile_id;
      }

      if (patient_id) {
        this.appointmentFormObj.patient_id = patient_id;
      }

      this.$store.commit("appointmentModule/RESET_TIME_SLOT");
    },

    // Event handlers
    clinicChange(selectedOption) {
      // Load patients for this clinic if needed
      if (this.getUserRole() !== "patient") {
        this.loadPatientsList(selectedOption.id);
      }

      // Reset appointment form data on clinic change
      this.clearAppointmentData();

      // Load doctor-specific data
      if (this.getUserRole() === "doctor") {
        const doctor_id = this.userData;
        this.getDoctorsServices(doctor_id.ID);
        this.getDoctorActiveDays(selectedOption.id, 1);
      } else {
        this.loadDoctorsList(selectedOption.id);
      }

      // Update tax data
      this.appointment_tax_data();
    },

    dispatchTimeSlot() {
      // Only proceed if we have all required data
      if (!this.appointmentFormObj.doctor_id || !this.appointmentFormObj.appointment_start_date) {
        return;
      }

      // Get clinic ID based on role
      let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole())
        ? this.userData.user_clinic_id
        : (this.appointmentFormObj?.clinic_id?.id || this.userData.default_clinic_id);

      // Prepare service list for time slot request
      let services = [];
      // Ensure visit_type is properly handled
      if (Array.isArray(this.appointmentFormObj.visit_type) && this.appointmentFormObj.visit_type.length > 0) {
        services = this.appointmentFormObj.visit_type.filter(service => service != null);
      }

      // Get time slots from API
      this.getTimeSlot({
        date: moment(this.appointmentFormObj.appointment_start_date).format("YYYY-MM-DD"),
        appointment_id: this.appointmentFormObj.id || "",
        clinic_id: clinic_id,
        doctor_id: this.appointmentFormObj.doctor_id.id,
        service: services
      });
    },

    getTimeSlot(data) {
      // Ensure we have all required data
      if (!data.date || !data.doctor_id || !data.clinic_id) {
        return;
      }

      // Add service_id parameter - extract IDs from service objects
      let serviceIds = '';
      if (Array.isArray(data.service) && data.service.length > 0) {
        serviceIds = data.service.map(service => {
          if (!service) return null;
          // Handle both service objects with id and service_id
          return service.service_id || service.id;
        }).filter(Boolean).join(',');
      }
      data.service_id = serviceIds;

      // Generate a cache key based on parameters
      const cacheKey = `${data.date}_${data.clinic_id}_${data.doctor_id}_${serviceIds}`;

      // Only make API call if we don't have cached data or if required data is new
      if (this.lastTimeSlotRequest !== cacheKey) {
        this.lastTimeSlotRequest = cacheKey;

        // Dispatch the API call
        this.$store.dispatch("appointmentModule/fetchAppointmentSlots", data);
      }
    },

    handleTimeChange(time) {
      this.appointmentFormObj.appointment_start_time = time;
    },

    handleDoctorChange(selectedOption) {
      this.DoctorWorkdays = [];

      // Get clinic ID based on role
      let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole())
        ? this.userData.user_clinic_id
        : (this.appointmentFormObj?.clinic_id?.id || this.userData.default_clinic_id);

      // Reset time slots and selections
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");
      this.appointmentFormObj.appointment_start_time = "";
      this.lastTimeSlotRequest = null;

      // Get doctor workdays and services first
      this.getDoctorActiveDays(clinic_id, selectedOption.id);

      // Clear existing services to avoid mixing with previous doctor's services
      this.appointmentFormObj.visit_type = [];

      // Get services for the new doctor - this will trigger time slots fetch if a date is selected
      this.getDoctorsServices(selectedOption.id);

      // Update UI
      this.componentKey += 1;
      this.showCustomField = true;
      this.appointment_tax_data();
    },

    handleDoctorUnselect() {
      this.clearAppointmentData();
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");
      this.appointment_tax_data();
    },

    handleDateChange(selectedOption) {
      // Reset time selection when date changes
      this.appointmentFormObj.appointment_start_time = "";

      // Update the date in the form
      this.appointmentFormObj.appointment_start_date = selectedOption;

      // Reset cache key to ensure we get fresh data on date change
      this.lastTimeSlotRequest = null;

      // Reset time slots in store to avoid showing old slots during loading
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");

      // Dispatch time slot request if we have both doctor and services
      if (this.appointmentFormObj.doctor_id &&
          this.appointmentFormObj.visit_type &&
          this.appointmentFormObj.visit_type.length > 0) {
        // Add a slight delay to ensure store reset completes
        this.$nextTick(() => {
          this.dispatchTimeSlot();
        });
      }
    },

    // Form submission and related methods
    handleFormSubmit() {
      this.loading = true;
      this.submitted = true;

      // Set clinic ID if kiviPro is not enabled
      if (this.userData.addOns?.kiviPro !== true) {
        let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole())
          ? this.userData.user_clinic_id
          : (this.appointmentFormObj?.clinic_id?.id || this.userData.default_clinic_id);

        this.appointmentFormObj.clinic_id = clinic_id;
      }

      // Handle patient ID from route or props
      let patient_id = false;
      if (this.$route.params.patient_id !== undefined) {
        patient_id = this.$route.params.patient_id;
      } else if (this.patient_profile_id) {
        patient_id = this.patient_profile_id;
      }

      if (patient_id) {
        this.appointmentFormObj.patient_id = {
          id: patient_id,
        };
      }

      // Handle patient ID for edit mode
      if (this.appointmentFormObj.id) {
        this.appointmentFormObj.patient_id = {
          id: this.appointmentData.patient_id,
        };
      }

      // Validate form
      this.$v.$touch();
      if (this.$v.appointmentFormObj.$invalid) {
        this.loading = false;
        return;
      }

      // Check required fields
      if (this.requiredFields?.length > 0) {
        this.loading = false;
        displayErrorMessage(this.formTranslation.common.all_required_field_validation);
        return;
      }

      // Check payment method for new appointments
      if (
        Object.keys(this.userData.all_payment_method || {}).length > 0 &&
        !this.appointmentFormObj.id &&
        !this.appointmentFormObj.payment_mode
      ) {
        this.loading = false;
        return;
      }

      // Submit form if valid
      if (validateForm("appointmentDataForm")) {
        this.bookAppointmentHandle();
      }
    },

    bookAppointmentHandle() {
      let appointmentData = { ...this.appointmentFormObj };

      // Format date for API
      appointmentData.appointment_start_date = moment(appointmentData.appointment_start_date).format("YYYY-MM-DD");
      appointmentData.tax = this.taxes;

      // Submit to API
      post("appointment_save", appointmentData)
        .then((response) => {
          this.loading = false;

          if (response.data.status === true) {
            // Handle WooCommerce integration if needed
            if (response.data.woocommerce_cart_data) {
              const cartData = response.data.woocommerce_cart_data;

              if (cartData.woocommerce_redirect) {
                if (this.appointmentFormObj.payment_mode === "paymentPaypal") {
                  kiviOpenPaymentWindow(cartData.woocommerce_redirect);
                  this.overlaySpinner = true;
                  this.timer = setInterval(this.checkChildWindow, 500);
                  return;
                } else {
                  location.href = cartData.woocommerce_redirect;
                  return;
                }
              }
            } else if (this.appointmentFormObj.payment_mode === "paymentRazorpay") {
              // Handle Razorpay checkout
              if (response.data.checkout_detail) {
                kivicareCreateRazorpayCheckoutPage(response.data.checkout_detail);
                this.overlaySpinner = true;
                this.timer = setInterval(this.checkChildWindow, 500);
              } else {
                displayErrorMessage(response.data.message);
              }
            } else {
              // Standard success path
              displayMessage(response.data.message);
              this.$store.commit("appointmentModule/RESET_TIME_SLOT");
              this.$emit("appointmentSaved", response.data.data);

              // Update patient profile if needed
              if (this.patient_profile_id) {
                this.$store.dispatch(
                  "appointmentModule/fetchAppointmentEncounterCount",
                  { id: this.patient_profile_id }
                );
              }

              this.closeAppointmentModal();
            }
          } else {
            displayErrorMessage(response.data.message);
            this.closeAppointmentModal();
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
          this.closeAppointmentModal();
        });
    },

    // UI helpers
    appointmentCloseForm() {
      this.upload_appointment_report = [];
      this.$emit("closeAppointmentForm");
      this.closeAppointmentModal();
    },

    closeAppointmentModal() {
      this.appointmentModel = false;
      this.loading = false;
      this.overlaySpinner = false;
    },

    checkChildWindow() {
      let ele = document.getElementById("payment_status_child");
      if (ele !== null && ele.value !== "") {
        clearInterval(this.timer);

        if (ele.value === "failed") {
          displayErrorMessage(this.formTranslation.common.payment_transaction_failed);
          this.$refs.appointment_detail.loading = false;
          ele.value = "";
        } else if (ele.value === "approved") {
          ele.value = "";
          displayMessage(this.formTranslation.common.payment_transaction_saved);
          this.appointmentModel = false;
          this.$store.commit("appointmentModule/RESET_TIME_SLOT");
          this.$emit("appointmentSaved", {});
        } else {
          ele.value = "";
        }

        // Update patient profile if needed
        if (this.patient_profile_id) {
          this.$store.dispatch(
            "appointmentModule/fetchAppointmentEncounterCount",
            { id: this.patient_profile_id }
          );
        }

        this.overlaySpinner = false;
        this.loading = false;
      }
    },

    // Doctor data methods
    async getDoctorActiveDays(clinic_id, id) {
      // Handle object IDs
      if (clinic_id?.id) {
        clinic_id = clinic_id.id;
      }
      if (id?.id) {
        id = id.id;
      }

      try {
        const response = await get("get_doctor_workdays", {
          clinic_id: clinic_id,
          doctor_id: id,
        });

        if (response.data.status === true) {
          this.holiday = response.data.holiday;

          // Store the doctor's workdays (these are days the doctor DOESN'T work)
          this.DoctorWorkdays = response.data.data;

          // Force the date picker to refresh
          this.$nextTick(() => {
            // If there's a selected date but it's not valid anymore, reset it
            if (this.appointmentFormObj.appointment_start_date) {
              const day = this.appointmentFormObj.appointment_start_date.getDay() + 1;
              if (this.DoctorWorkdays.includes(day)) {
                // Current selected day is not available, reset it to the first available day
                this.appointmentFormObj.appointment_start_date = this.findNextAvailableDate();
              }
            }
          });
        }
      } catch (error) {
        console.log(error);
      }
    },

    async getDoctorsServices(doctorId) {
      // Skip if services are already loading
      if (this.serviceMultiselectLoader) {
        return;
      }

      this.serviceMultiselectLoader = true;
      this.appointmentTypes = [];

      // Get clinic ID based on role
      let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole())
        ? this.userData.user_clinic_id
        : (this.appointmentFormObj?.clinic_id?.id || this.userData.default_clinic_id);

      try {
        const response = await get("get_clinic_service", {
          module_type: "appointment_form",
          limit: 0,
          doctor_id: doctorId,
          clinic_id: clinic_id,
        });

        this.appointmentTypes = JSON.parse(JSON.stringify(response.data.data || []));

        // Set default service for new appointments
        if (
          this.appointmentTypes.length > 0 &&
          !this.appointmentFormObj.id &&
          this.appointmentFormObj.visit_type.length === 0
        ) {
          this.appointmentFormObj.visit_type.push(this.appointmentTypes[0]);

          if (
            this.appointmentTypes[0].multiple !== undefined &&
            this.appointmentTypes[0].multiple === "no"
          ) {
            this.appointmentTypeMultiselect = false;
          }

          // Reset cache key since service has changed
          this.lastTimeSlotRequest = null;
        }

        // Update time slots if we have all required data (only once)
        if (
          this.appointmentFormObj.appointment_start_date &&
          this.appointmentFormObj.doctor_id &&
          this.appointmentFormObj.visit_type &&
          this.appointmentFormObj.visit_type.length > 0
        ) {
          // Reset cache key first to ensure fresh data
          this.lastTimeSlotRequest = null;
          this.dispatchTimeSlot();
        }

        // Update tax data
        this.appointment_tax_data();
      } catch (error) {
        console.log(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.serviceMultiselectLoader = false;
      }
    },

    appointmentTypeChangeSelect(selected) {
      // Ensure visit_type is always an array
      if (!Array.isArray(this.appointmentFormObj.visit_type)) {
        this.appointmentFormObj.visit_type = [];
      }

      // Check if service is already selected to prevent duplicates
      const isAlreadySelected = Array.isArray(this.appointmentFormObj.visit_type) &&
        this.appointmentFormObj.visit_type.some(
          (service) => service && service.id === selected.id
        );

      if (isAlreadySelected) {
        return; // Don't add if already selected
      }

      if (
        selected.multiple !== undefined &&
        selected.multiple !== "" &&
        selected.multiple === "no"
      ) {
        // For single selection services, replace the entire array
        this.appointmentFormObj.visit_type = [selected];
        this.appointmentTypeMultiselect = false;
      } else {
        // For multiple selection, add to existing array
        this.appointmentTypeMultiselect = true;
        this.appointmentFormObj.visit_type.push(selected);
      }

      // Reset cache key to ensure we get fresh time slots
      this.lastTimeSlotRequest = null;

      // Clear any previously selected time to avoid conflicts
      this.appointmentFormObj.appointment_start_time = '';

      // Reset time slots in store to avoid showing old slots during loading
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");

      // Trigger updates with a small delay to ensure store reset completes
      this.$nextTick(() => {
        // Only dispatch if we have date and doctor selected
        if (this.appointmentFormObj.appointment_start_date && this.appointmentFormObj.doctor_id) {
          this.dispatchTimeSlot();
        }
        this.appointment_tax_data();
      });
    },

    appointmentTypeChangeUnselect(selected) {
      // Ensure visit_type is always an array
      if (!Array.isArray(this.appointmentFormObj.visit_type)) {
        this.appointmentFormObj.visit_type = [];
        return; // No need to filter an empty array
      }

      // Remove the unselected service
      this.appointmentFormObj.visit_type = this.appointmentFormObj.visit_type.filter(
        (service) => service && service.id !== selected.id
      );

      if (
        selected.multiple !== undefined &&
        selected.multiple !== "" &&
        selected.multiple === "no"
      ) {
        this.appointmentTypeMultiselect = true;
      }

      // Reset cache key to ensure we get fresh time slots
      this.lastTimeSlotRequest = null;

      // Clear any previously selected time to avoid conflicts
      this.appointmentFormObj.appointment_start_time = '';

      // Reset time slots in store to avoid showing old slots during loading
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");

      // If no services remain, don't show any time slots
      if (this.appointmentFormObj.visit_type.length === 0) {
        return;
      }

      // Trigger updates with a slight delay to ensure store reset completes
      this.$nextTick(() => {
        // Only dispatch if we have date and doctor selected
        if (this.appointmentFormObj.appointment_start_date && this.appointmentFormObj.doctor_id) {
          this.dispatchTimeSlot();
        }
        this.appointment_tax_data();
      });
    },

    // File upload
    multiUploadProfile() {
      let _this = this;
      var custom_uploader = kivicareCustomImageUploader(
        this.formTranslation,
        "report",
        this.userData.addOns?.kiviPro === true
      );

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .map((item) => {
            item.toJSON();
            return item;
          });

        // Initialize file array if needed
        if (!_this.appointmentFormObj.file) {
          _this.appointmentFormObj.file = [];
        }

        attachment.forEach((report) => {
          _this.upload_appointment_report.push({
            name: report.attributes.filename,
            url: report.attributes.url,
          });
          _this.appointmentFormObj.file.push(report.attributes.id);
        });
      });

      custom_uploader.open();
    },

    appointment_tax_data() {
      if (this.userData.addOns?.kiviPro === false) {
        return;
      }

      this.taxes = [];

      post("tax_calculated_data", this.appointmentFormObj)
        .then((response) => {
          if (response.data.status === true) {
            this.taxes = response.data.data;
            this.appointmentFormObj.tax = response.data.tax_total;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage("Internal server error");
        });
    },

    // Form data initialization for edit mode
    async initFormData() {
      if (Object.keys(this.appointmentData).length > 0) {
        try {
          // Set basic form data first
          this.appointmentFormObj = {
            ...this.appointmentFormObj,
            id: this.appointmentData.id,
            status: this.appointmentData.status?.toString(),
            description: this.appointmentData.description || "",
            custom_fields: this.appointmentData.custom_fields || [],
            payment_mode: this.appointmentData.payment_mode || "",
          };

          // Handle clinic selection first
          if (this.appointmentData.clinic_id) {
            // Set initial clinic data
            this.appointmentFormObj.clinic_id = {
              id: this.appointmentData.clinic_id,
              label: this.appointmentData.clinic_name,
            };
          }

          // Set doctor data
          if (this.appointmentData.doctor_id) {
            this.appointmentFormObj.doctor_id = {
              id: this.appointmentData.doctor_id,
              label: this.appointmentData.doctor_name,
            };

            // After doctor is set, get their services (once)
            if (this.appointmentTypes.length === 0) {
              await this.getDoctorsServices(this.appointmentData.doctor_id);
            }

            // Get doctor's working days
            await this.getDoctorActiveDays(
              this.appointmentData.clinic_id,
              this.appointmentData.doctor_id
            );
          }

          // Set patient data
          if (this.appointmentData.patient_id) {
            this.appointmentFormObj.patient_id = {
              id: this.appointmentData.patient_id,
              label: this.appointmentData.patient_name,
            };
          }

          // Set appointment date and time
          if (this.appointmentData.appointment_start_date) {
            this.appointmentFormObj.appointment_start_date = new Date(
              this.appointmentData.appointment_start_date
            );
          }

          if (this.appointmentData.appointment_start_time) {
            this.appointmentFormObj.appointment_start_time =
              this.appointmentData.appointment_start_time;
          }

          // Set visit type/service
          if (this.appointmentData.visit_type && Array.isArray(this.appointmentData.visit_type)) {
            this.appointmentFormObj.visit_type = this.appointmentData.visit_type.map((service) => ({
              id: service.service_id,
              service_id: service.service_id,
              name: service.name,
              charges: service.charges,
              multiple: service.multiple,
            }));
          }

          // Set other data
          if (this.appointmentData.tax) {
            this.taxes = this.appointmentData.tax;
          }

          if (this.appointmentData.file && Array.isArray(this.appointmentData.file)) {
            this.upload_appointment_report = this.appointmentData.file.map(
              (file) => ({
                name: file.name,
                url: file.url,
              })
            );
          }

          // Update UI and fetch time slots
          this.$nextTick(() => {
            this.dispatchTimeSlot();
            this.appointment_tax_data();
          });
        } catch (error) {
          console.error("Error initializing form data:", error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        }
      }
    },

    // Custom fields handling
    getCustomFieldsValues(fieldsObj) {
      if (!fieldsObj || fieldsObj === undefined) {
        return false;
      }

      for (let index = 0; index < this.appointmentFormObj.custom_fields.length; index++) {
        const customField = { ...this.appointmentFormObj.custom_fields[index] };
        const customFieldValue = { ...fieldsObj };
        customField.field_data = customFieldValue["custom_field_" + customField.id];
        this.appointmentFormObj.custom_fields[index] = customField;
      }

      this.appointmentFormObj.custom_fields_data = fieldsObj;
    },

    getRequireFields(validateRequired) {
      this.requiredFields = validateRequired;
    },
  }
};
</script>