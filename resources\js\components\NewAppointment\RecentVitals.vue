<template>
  <div class="bg-white border rounded p-3 mb-3">
    <div class="flex justify-between items-center mb-3">
      <h2 class="font-medium flex items-center gap-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="lucide lucide-history w-4 h-4"
        >
          <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
          <path d="M3 3v5h5"></path>
          <path d="M12 7v5l4 2"></path>
        </svg>
        Recent Vitals History
      </h2>
    </div>
    <div class="grid grid-cols-2 gap-3">
      <div 
        v-for="field in vitalFields" 
        :key="field.key"
        class="p-2 bg-gray-50 rounded"
      >
        <div class="text-gray-500 mb-1">{{ field.label }}</div>
        <div class="font-medium">
          {{ loading ? 'Loading...' : (getVitalValue(field.vital_type) || '--') }}
        </div>
        <div class="text-xs text-gray-400">
          {{ loading ? '' : (getVitalTimestamp(field.vital_type) || 'No recent records') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";
import { formatDistanceToNow } from 'date-fns';

export default {
  name: "RecentVitals",
  
  props: {
    encounterId: {
      type: [String, Number],
      default: null
    },
    patientId: {
      type: [String, Number],
      default: null
    }
  },

  data() {
    return {
      loading: false,
      vitals: {},
      vitalFields: [
        { label: "Temperature", vital_type: "temperature", key: "temp" },
        { label: "Pulse", vital_type: "pulse", key: "pulse" },
        { label: "Blood Pressure", vital_type: "blood_pressure", key: "bp" },
        { label: "Respiratory Rate", vital_type: "respiratory_rate", key: "resp" },
        { label: "Saturation", vital_type: "saturation", key: "sat" }
      ]
    };
  },

  watch: {
    patientId: {
      immediate: true,
      handler(newId) {
        if (newId) {
          this.fetchLastRecordedVitals();
        } else {
          // Reset vitals when patient id is not available
          this.vitals = {};
        }
      }
    }
  },

  methods: {
    async fetchLastRecordedVitals() {
      if (!this.patientId) return;

      try {
        this.loading = true;
        const response = await get('get_last_recorded_vitals', {
          patient_id: this.patientId,
          encounter_id: this.encounterId,
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to fetch vitals');
        }

        this.vitals = response.data.data;
      } catch (error) {
        console.error('Error fetching vitals:', error);
        if (this.$toast) {
          this.$toast.error(error.message || 'Failed to fetch vital signs');
        }
      } finally {
        this.loading = false;
      }
    },

    getVitalValue(type) {
      return this.vitals[type]?.value || '--';
    },

    getVitalTimestamp(type) {
      if (!this.vitals[type]?.recorded_at) {
        return 'No recent records';
      }

      try {
        const date = new Date(this.vitals[type].recorded_at);
        return `Recorded ${formatDistanceToNow(date)} ago`;
      } catch (error) {
        return 'Invalid date';
      }
    }
  }
};
</script>