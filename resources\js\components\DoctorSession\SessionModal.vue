<!-- SessionModal.vue -->
<template>
  <div>
    <!-- <PERSON>dal Backdrop -->
    <div
      v-if="showAddEditSessionModal"
      class="fixed inset-0 bg-black backdrop-blur-sm transition-opacity opacity-50 duration-300 z-40"
      @click="closeModal"
    ></div>

    <!-- Modal Container -->
    <div
      v-if="showAddEditSessionModal"
      class="fixed inset-0 z-50 overflow-y-auto"
      role="dialog"
      aria-modal="true"
    >
      <div class="flex min-h-screen items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl">
          <div class="p-6">
            <!-- Header -->
            <div class="mb-8">
              <h2 class="text-2xl font-semibold mb-2">
                {{
                  isEdit
                    ? formTranslation.doctor_session.edit_session
                    : formTranslation.doctor_session.add_session_btn
                }}
              </h2>
              <p class="text-gray-600">
                Configure your weekly schedule with easy-to-use time slots
              </p>
              <div v-if="initialData.service_id" class="mt-2 px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full inline-flex items-center">
                <span class="mr-1">Service-specific session:</span>
                <span class="font-semibold">{{ initialData.service_name || 'Service #' + initialData.service_id }}</span>
              </div>
            </div>

            <form @submit.prevent="handleSubmit">
              <!-- Form Controls -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Clinic Selection -->
                <div
                  v-if="
                    userData.addOns.kiviPro === true &&
                    (getUserRole() === 'administrator' ||
                      getUserRole() === 'doctor')
                  "
                >
                  <label class="block text-sm font-medium mb-2">
                    {{ formTranslation.clinic.select_clinic }}
                    <span class="text-red-500">*</span>
                  </label>
                  <select
                    v-model="formData.clinic_id"
                    @change="handleClinicChange"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 text-left relative"
                    :class="{
                      'border-red-500 focus:ring-red-400':
                        submitted && !$v.formData.clinic_id.required,
                    }"
                  >
                    <option value="">
                      {{ formTranslation.doctor_session.plh_search }}
                    </option>
                    <option
                      v-for="clinic in allClinics"
                      :key="clinic.id"
                      :value="clinic.id"
                    >
                      {{ clinic.label }}
                    </option>
                  </select>

                  <div
                    v-if="submitted && !$v.formData.clinic_id.required"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formTranslation.common.clinic_is_required }}
                  </div>
                </div>

                <!-- Doctor Selection -->
                <div v-if="getUserRole() !== 'doctor'">
                  <label class="block text-sm font-medium mb-2">
                    {{ formTranslation.common.doctors }}
                    <span class="text-red-500">*</span>
                  </label>
                  <select
                    v-model="formData.doctors"
                    @change="handleDoctorChange"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 text-left relative"
                    :class="{
                      'border-red-500 focus:ring-red-400':
                        submitted && !$v.formData.doctors.required,
                    }"
                  >
                    <option value="">
                      {{ formTranslation.doctor_session.plh_search }}
                    </option>
                    <option
                      v-for="doctor in doctors"
                      :key="doctor.id"
                      :value="doctor"
                    >
                      {{ doctor.label }}
                    </option>
                  </select>
                  <div
                    v-if="submitted && !$v.formData.doctors.required"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formTranslation.appointments.doc_required }}
                  </div>
                </div>

                <!-- Buffer Time -->
                <div>
                  <label class="block text-sm font-medium mb-2">
                    {{ formTranslation.setup_wizard.time_slot_minute }}
                    <span class="text-red-500">*</span>
                  </label>
                  <select
                    v-model="formData.buffertime"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 text-left relative"
                    :class="{
                      'border-red-500':
                        submitted && !$v.formData.buffertime.required,
                    }"
                  >
                    <option
                      v-for="(slot, index) in timeSlots"
                      :key="index"
                      :value="slot"
                    >
                      {{ slot }} minutes
                    </option>
                  </select>
                  <div
                    v-if="submitted && !$v.formData.buffertime.required"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formTranslation.appointments.time_slot_required }}
                  </div>
                </div>
              </div>

              <!-- Quick Time Slots -->
              <div class="mb-8">
                <h3 class="text-sm font-medium mb-3">Quick Time Slots</h3>
                <div class="flex flex-wrap gap-3">
                  <button
                    type="button"
                    @click="applyQuickTimeSlot('morning')"
                    class="px-4 py-2 bg-gray-100 rounded-full text-sm hover:bg-gray-200 transition-colors"
                  >
                    Morning (9 AM - 1 PM)
                  </button>
                  <button
                    type="button"
                    @click="applyQuickTimeSlot('afternoon')"
                    class="px-4 py-2 bg-gray-100 rounded-full text-sm hover:bg-gray-200 transition-colors"
                  >
                    Afternoon (2 PM - 5 PM)
                  </button>
                  <button
                    type="button"
                    @click="applyQuickTimeSlot('fullDay')"
                    class="px-4 py-2 bg-gray-100 rounded-full text-sm hover:bg-gray-200 transition-colors"
                  >
                    Full Day (9 AM - 5 PM)
                  </button>
                </div>
              </div>

              <!-- Day Tabs -->
              <div class="mb-6 border-b">
                <div class="flex space-x-1">
                  <button
                    v-for="(day, index) in formData.days"
                    :key="day.name"
                    type="button"
                    @click="activeDay = index"
                    class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors relative"
                    :class="[
                      activeDay === index
                        ? 'bg-pink-50 text-pink-600 border-b-2 border-pink-600'
                        : 'text-gray-600 hover:bg-gray-50',
                    ]"
                  >
                    {{ day.name.toUpperCase() }}
                  </button>
                </div>
              </div>

              <!-- Time Slots -->
              <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-lg font-medium">
                    {{ formData.days[activeDay].label }} Schedule
                  </h3>
                  <div class="flex gap-2">
                    <button
                      type="button"
                      @click="copyToAllDays"
                      class="flex items-center px-3 py-2 text-sm bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      <i class="far fa-copy mr-2"></i>
                      Copy to All Days
                    </button>
                    <button
                      type="button"
                      @click="addSlot(activeDay)"
                      class="px-3 py-2 text-sm bg-black text-white rounded-lg hover:bg-black transition-colors"
                    >
                      Add Time Slot
                    </button>
                  </div>
                </div>

                <!-- Time Slot List -->
                <div
                  v-for="(slot, slotIndex) in formData.days[activeDay].slots"
                  :key="slotIndex"
                  class="mb-4 p-4 bg-gray-50 rounded-lg"
                >
                  <div class="flex items-center gap-4">
                    <i class="far fa-clock text-gray-400"></i>
                    <vue-timepicker
                      v-model="slot.start"
                      :minute-interval="5"
                      format="HH:mm"
                      class="flex-1"
                      @change="validateTime(activeDay)"
                    />
                    <span class="text-gray-500">to</span>
                    <vue-timepicker
                      v-model="slot.end"
                      :minute-interval="5"
                      format="HH:mm"
                      class="flex-1"
                      @change="validateTime(activeDay)"
                    />
                    <button
                      type="button"
                      @click="removeSlot(activeDay, slotIndex)"
                      class="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <i class="fas fa-trash-alt"></i>
                    </button>
                  </div>
                </div>

                <!-- Error Message -->
                <div
                  v-if="formData.days[activeDay].error"
                  class="text-red-500 text-sm p-2 bg-red-50 rounded-lg flex items-center gap-2"
                >
                  <i class="fas fa-exclamation-circle"></i>
                  {{ formData.days[activeDay].error }}
                </div>
              </div>

              <!-- Footer -->
              <div class="flex justify-end gap-4">
                <button
                  type="button"
                  @click="closeModal"
                  class="px-6 py-2 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {{ formTranslation.common.cancel }}
                </button>
                <button
                  type="submit"
                  :disabled="loading"
                  class="px-6 py-2 bg-black text-white rounded-lg hover:bg-black transition-colors disabled:opacity-50 disabled:hover:bg-black flex items-center gap-2"
                >
                  <i
                    :class="
                      loading ? 'fas fa-circle-notch fa-spin' : 'fas fa-save'
                    "
                  ></i>
                  {{
                    loading
                      ? formTranslation.common.loading
                      : formTranslation.doctor_session.save_btn
                  }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  maxLength,
  minLength,
  required,
  requiredIf,
} from "vuelidate/lib/validators";
import VueTimepicker from "vue2-timepicker";
import "vue2-timepicker/dist/VueTimepicker.css";
import SearchableSelect from "./../../components/Common/SearchableSelect.vue";

export default {
  name: "SessionModal",
  components: {
    VueTimepicker,
    SearchableSelect,
  },
  props: {
    showAddEditSessionModal: {
      type: Boolean,
      required: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    allClinics: {
      type: Array,
      default: () => [],
    },
    doctors: {
      type: Array,
      default: () => [],
    },
    userData: {
      type: Object,
      required: true,
    },
  },
  data() {
    const days = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"];
    const timeSlots = Array.from({ length: 12 }, (_, i) => i * 5).filter(
      (slot) => slot <= 60
    );

    return {
      formData: {
        doctors: null,
        clinic_id: "",
        buffertime: 0,
        days: days.map((day) => ({
          name: day,
          label: (this.formTranslation?.days || {})[day] || day,
          slots: [],
          error: null,
        })),
      },
      loading: false,
      submitted: false,
      timeSlots,
      days,
      activeDay: 0,
      quickTimeSlots: {
        morning: [{ start: "09:00", end: "13:00" }],
        afternoon: [{ start: "14:00", end: "17:00" }],
        fullDay: [{ start: "09:00", end: "17:00" }],
      },
    };
  },
  validations() {
    return {
      formData: {
        clinic_id: {
          required: requiredIf(() => {
            return (
              this.userData?.addOns?.kiviPro === true &&
              (this.getUserRole() === "administrator" ||
                this.getUserRole() === "doctor")
            );
          }),
        },
        doctors: {
          required: requiredIf(() => this.getUserRole() !== "doctor"),
        },
        buffertime: { required },
      },
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (Object.keys(newVal).length) {
          // Set form data with the new values
          this.formData = {
            ...this.formData,
            clinic_id: newVal.clinic_id?.toString() || "", // Ensure clinic_id is a string
            doctors: newVal.doctors || null,
            buffertime: parseInt(newVal.buffertime) || 0,
            days: newVal.days || this.formData.days,
          };

          // If we have a clinic_id, trigger the clinic change to load doctors
          if (this.formData.clinic_id) {
            this.handleClinicChange({
              target: { value: this.formData.clinic_id },
            });
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    defaultFormData() {
      const currentClinicId = this.formData?.clinic_id || "";
      const currentDoctors = this.formData?.doctors || {};
      const translations = this.formTranslation?.days || {};
      const days = this.days || [
        "mon",
        "tue",
        "wed",
        "thu",
        "fri",
        "sat",
        "sun",
      ];

      return {
        doctors: currentDoctors,
        clinic_id: currentClinicId,
        buffertime: this.formData?.buffertime || 0,
        days: days.map((day) => ({
          name: day,
          label: translations[day] || day,
          slots: [],
          error: null,
        })),
      };
    },
    closeModal() {
      this.$emit("close");
      this.formData = this.defaultFormData();
      this.submitted = false;
      this.activeDay = 0;
    },
    parseTime(timeStr) {
      const [hours, minutes] = timeStr.split(":").map(Number);
      const date = new Date();
      date.setHours(hours, minutes, 0, 0);
      return date;
    },

    // updated addSlot method to include default times
    addSlot(dayIndex) {
      const slots = this.formData.days[dayIndex].slots;
      let startTime = "09:00";
      let endTime = "17:00";

      if (slots.length > 0) {
        // Set new slot after the last existing slot
        const lastSlot = slots[slots.length - 1];
        startTime = this.incrementTime(lastSlot.end, this.formData.buffertime);
        endTime = this.incrementTime(startTime, 60); // Default 1-hour duration
      }

      this.formData.days[dayIndex].slots.push({
        start: startTime,
        end: endTime,
      });

      this.validateTime(dayIndex);
    },
    incrementTime(time, minutes) {
      const date = this.parseTime(time);
      date.setMinutes(date.getMinutes() + minutes);
      return `${String(date.getHours()).padStart(2, "0")}:${String(
        date.getMinutes()
      ).padStart(2, "0")}`;
    },
    removeSlot(dayIndex, slotIndex) {
      this.formData.days[dayIndex].slots.splice(slotIndex, 1);
      this.validateTime(dayIndex);
    },
    copyFromPreviousDay(dayIndex) {
      const previousDay = this.formData.days[dayIndex - 1];
      if (previousDay?.slots.length > 0) {
        this.formData.days[dayIndex].slots = previousDay.slots.map((slot) => ({
          start: slot.start,
          end: slot.end,
        }));
        this.validateTime(dayIndex);
      }
    },
    applyQuickTimeSlot(type) {
      if (this.quickTimeSlots && this.quickTimeSlots[type]) {
        // Create a copy of the current form data
        const updatedDays = [...this.formData.days];
        updatedDays[this.activeDay] = {
          ...updatedDays[this.activeDay],
          slots: this.quickTimeSlots[type].map((slot) => ({
            start: slot.start,
            end: slot.end,
          })),
        };

        // Update only the days array while preserving other form data
        this.formData = {
          ...this.formData,
          days: updatedDays,
        };

        this.validateTime(this.activeDay);
      }
    },

    copyToAllDays() {
      const sourceSlots = this.formData.days[this.activeDay].slots;
      const updatedDays = this.formData.days.map((day, index) => {
        if (index !== this.activeDay) {
          return {
            ...day,
            slots: sourceSlots.map((slot) => ({
              start: slot.start,
              end: slot.end,
            })),
          };
        }
        return day;
      });

      // Update only the days array while preserving other form data
      this.formData = {
        ...this.formData,
        days: updatedDays,
      };
    },

    validateTime(dayIndex) {
      const slots = this.formData.days[dayIndex].slots;
      let hasOverlap = false;

      // Reset errors
      this.formData.days[dayIndex].error = null;

      // Sort slots by start time for better validation
      slots.sort((a, b) => a.start.localeCompare(b.start));

      // Validate slots
      for (let i = 0; i < slots.length; i++) {
        const current = slots[i];

        // Check empty slots
        if (!current.start || !current.end) {
          this.formData.days[dayIndex].error =
            this.formTranslation.common.emptyTimeSlots;
          return;
        }

        // Check invalid time range
        if (current.start >= current.end) {
          this.formData.days[dayIndex].error =
            this.formTranslation.common.invalidTimeRange;
          return;
        }

        // Check for minimum duration (if specified in buffertime)
        const startTime = this.parseTime(current.start);
        const endTime = this.parseTime(current.end);
        const durationMinutes = (endTime - startTime) / (1000 * 60);

        if (durationMinutes < this.formData.buffertime) {
          this.formData.days[
            dayIndex
          ].error = `Time slot must be at least ${this.formData.buffertime} minutes long`;
          return;
        }

        // Check overlaps with next slot
        if (i < slots.length - 1) {
          const next = slots[i + 1];
          if (current.end > next.start) {
            hasOverlap = true;
            break;
          }
        }
      }

      if (hasOverlap) {
        this.formData.days[dayIndex].error =
          this.formTranslation.common.overlappingSlots;
      }
    },
    handleClinicChange(event) {
      const selectedClinicId = event.target.value;
      this.$emit("clinic-change", selectedClinicId);
    },
    handleDoctorChange(event) {
      // const doctorId = event.target.value;
      // if (doctorId) {
      //   this.$emit("doctor-change", doctorId);
      // }
    },
    async handleSubmit() {
      this.submitted = true;
      this.loading = true;

      try {
        this.$v.$touch();
        if (this.$v.$invalid) {
          throw new Error("Form validation failed");
        }

        let isValid = true;

        // Validate current day's time slots
        this.validateTime(this.activeDay);
        if (this.formData.days[this.activeDay].error) {
          isValid = false;
        }

        // Check if any slots are defined in the current day
        const hasSlots = this.formData.days[this.activeDay].slots.length > 0;
        if (!hasSlots) {
          this.$swal.fire({
            title: "Error!",
            text: this.formTranslation.doctor_session.no_slots_error,
            icon: "error",
          });
          throw new Error("No time slots defined");
        }

        if (!isValid) {
          throw new Error("Time slot validation failed");
        }

        // Create a copy of the form data for submission
        const submissionData = { ...this.formData };

        // Preserve the ID if this is an edit operation
        if (this.isEdit && this.initialData.id) {
          submissionData.id = this.initialData.id;
        }

        // If we're using clinic IDs, find the matching clinic object
        if (
          typeof submissionData.clinic_id === "string" ||
          typeof submissionData.clinic_id === "number"
        ) {
          const selectedClinic = this.allClinics.find(
            (clinic) => clinic.id == submissionData.clinic_id
          );
          submissionData.clinic_id = selectedClinic || {};
        }

        if (this.getUserRole() === "doctor") {
          submissionData.doctors = {
            id: this.$store.state.userDataModule.user.ID,
            label: this.$store.state.userDataModule.user.display_name,
          };
        }

        // Submit the form data
        this.$emit("submit", submissionData);
      } catch (error) {
        console.error("Submit error:", error);
        if (!error.message.includes("validation")) {
          this.$swal.fire({
            title: "Error!",
            text:
              error.message ||
              this.formTranslation.common.internal_server_error,
            icon: "error",
          });
        }
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped>
.vue__time-picker input.display-time {
  @apply w-full px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-colors duration-200;
}

.vue__time-picker .dropdown {
  @apply border border-gray-200 rounded-lg shadow-lg bg-white;
}

.vue__time-picker .dropdown ul li:not([disabled]).active,
.vue__time-picker .dropdown ul li:not([disabled]).active:hover {
  @apply bg-purple-500 text-white;
}

.vue__time-picker .dropdown ul li:not([disabled]):hover {
  @apply bg-purple-100;
}

/* Transition classes for modal */
.modal-enter-active,
.modal-leave-active {
  @apply transition-all duration-300;
}

.modal-enter-from,
.modal-leave-to {
  @apply opacity-0 scale-95;
}

.modal-enter-to,
.modal-leave-from {
  @apply opacity-100 scale-100;
}
</style>