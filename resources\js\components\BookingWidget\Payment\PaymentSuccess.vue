<template>
  <div class="kivi-payment-result kivi-payment-success">
    <div class="kivi-payment-result-icon">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
        <polyline points="22 4 12 14.01 9 11.01"></polyline>
      </svg>
    </div>
    <h2 class="kivi-payment-result-title">Payment Successful!</h2>
    <p class="kivi-payment-result-message">Your appointment has been confirmed and payment has been processed successfully.</p>
    <div class="kivi-payment-result-details">
      <p><strong>Appointment ID:</strong> {{ appointmentId }}</p>
      <p><strong>Date:</strong> {{ formatDate(bookingData.date) }}</p>
      <p><strong>Time:</strong> {{ formatTime(bookingData.time) }}</p>
      <p v-if="servicesTotal > 0"><strong>Amount Paid:</strong> {{ formatPrice(servicesTotal) }}</p>
    </div>
    <div class="kivi-payment-result-actions">
      <button @click="goHome" class="kivi-btn kivi-btn-primary kivi-btn-large">
        Return to Home
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PaymentSuccess',
  props: {
    appointmentId: {
      type: String,
      required: true
    },
    bookingData: {
      type: Object,
      required: true
    },
    servicesTotal: {
      type: Number,
      default: 0
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return '';
      // Use the same date formatting logic as in the parent component
      const options = { year: 'numeric', month: 'long', day: 'numeric' };
      return new Date(date).toLocaleDateString(undefined, options);
    },
    formatTime(time) {
      if (!time) return '';
      // Return the time as is, assuming it's already formatted
      return time;
    },
    formatPrice(price) {
      if (!price) return '';
      // Format price with currency symbol
      return new Intl.NumberFormat(undefined, {
        style: 'currency',
        currency: 'USD' // This should ideally come from a configuration
      }).format(price);
    },
    goHome() {
      // Navigate back to home page
      window.location.href = '/';
    }
  }
}
</script>