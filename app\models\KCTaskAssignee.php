<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTaskAssignee extends KCModel
{
    public function __construct()
    {
        parent::__construct('task_assignees');
    }

    /**
     * Assign a task to a user
     *
     * @param int $task_id - Task ID
     * @param int $assignee_id - User ID
     * @return int|bool - Assignee ID or false
     */
    public function assignTask($task_id, $assignee_id)
    {
        // Check if this assignment already exists
        $existing = $this->get_by(
            [
                'task_id' => $task_id,
                'assignee_id' => $assignee_id
            ],
            '=',
            true
        );

        if ($existing) {
            // Assignment already exists
            return $existing->id;
        }

        // Create new assignment
        return $this->insert([
            'task_id' => (int)$task_id,
            'assignee_id' => (int)$assignee_id,
            'assigned_at' => current_time('mysql')
        ]);
    }

    /**
     * Unassign a task from a user
     *
     * @param int $task_id - Task ID
     * @param int $assignee_id - User ID
     * @return bool - Success
     */
    public function unassignTask($task_id, $assignee_id)
    {
        return $this->delete([
            'task_id' => (int)$task_id,
            'assignee_id' => (int)$assignee_id
        ]);
    }

    /**
     * Mark a task as completed by an assignee
     *
     * @param int $task_id - Task ID
     * @param int $assignee_id - User ID
     * @return bool - Success
     */
    public function completeTask($task_id, $assignee_id)
    {
        return $this->update(
            ['completed_at' => current_time('mysql')],
            [
                'task_id' => (int)$task_id,
                'assignee_id' => (int)$assignee_id
            ]
        );
    }

    /**
     * Get all tasks assigned to a user
     *
     * @param int $user_id - User ID
     * @param array $filters - Additional filters
     * @return array - Tasks data
     */
    public function getAssignedTasks($user_id, $filters = [])
    {
        global $wpdb;

        $table_name = $this->get_table_name();
        $tasks_table = $wpdb->prefix . 'kc_tasks';
        $users_table = $wpdb->base_prefix . 'users';
        $clinics_table = $wpdb->prefix . 'kc_clinics';

        $query = "
            SELECT $tasks_table.*,
                   creator.display_name AS creator_name,
                   patient.display_name AS patient_name,
                   $clinics_table.name AS clinic_name,
                   $table_name.assigned_at,
                   $table_name.completed_at
            FROM $table_name
            JOIN $tasks_table ON $table_name.task_id = $tasks_table.id
            LEFT JOIN $users_table creator ON $tasks_table.creator_id = creator.ID
            LEFT JOIN $users_table patient ON $tasks_table.patient_id = patient.ID
            LEFT JOIN $clinics_table ON $tasks_table.clinic_id = $clinics_table.id
            WHERE $table_name.assignee_id = %d
        ";

        $params = [(int)$user_id];

        // Add filters
        if (!empty($filters['status']) && $filters['status'] !== 'all') {
            $query .= " AND $tasks_table.status = %s";
            $params[] = $filters['status'];
        }

        if (!empty($filters['priority']) && $filters['priority'] !== 'all') {
            $query .= " AND $tasks_table.priority = %s";
            $params[] = $filters['priority'];
        }

        if (isset($filters['is_archived'])) {
            $query .= " AND $tasks_table.is_archived = %d";
            $params[] = (int)$filters['is_archived'];
        } else {
            // By default, don't show archived tasks
            $query .= " AND $tasks_table.is_archived = 0";
        }

        if (!empty($filters['clinic_id'])) {
            $query .= " AND $tasks_table.clinic_id = %d";
            $params[] = (int)$filters['clinic_id'];
        }
        
        // Support for multiple clinics filter
        if (!empty($filters['clinic_ids']) && is_array($filters['clinic_ids'])) {
            $clinic_ids = array_map('intval', $filters['clinic_ids']);
            $clinic_ids_str = implode(',', $clinic_ids);
            $query .= " AND $tasks_table.clinic_id IN ($clinic_ids_str)";
        }
        
        // Support for filtering by category
        if (!empty($filters['category'])) {
            $query .= " AND $tasks_table.category = %s";
            $params[] = $filters['category'];
        }
        
        // Support for date range filtering
        if (!empty($filters['due_date_start']) && !empty($filters['due_date_end'])) {
            $query .= " AND $tasks_table.due_date BETWEEN %s AND %s";
            $params[] = $filters['due_date_start'];
            $params[] = $filters['due_date_end'];
        } else if (!empty($filters['due_date_start'])) {
            $query .= " AND $tasks_table.due_date >= %s";
            $params[] = $filters['due_date_start'];
        } else if (!empty($filters['due_date_end'])) {
            $query .= " AND $tasks_table.due_date <= %s";
            $params[] = $filters['due_date_end'];
        }

        // Add ordering
        if (!empty($filters['orderby'])) {
            $orderby = esc_sql($filters['orderby']);
            $order = !empty($filters['order']) ? esc_sql($filters['order']) : 'ASC';
            $query .= " ORDER BY $tasks_table.$orderby $order";
        } else {
            // Default ordering by due date
            $query .= " ORDER BY $tasks_table.due_date ASC";
        }

        $prepared_query = $wpdb->prepare($query, $params);
        return $wpdb->get_results($prepared_query);
    }

    /**
     * Get all assignees for a task
     *
     * @param int $task_id - Task ID
     * @return array - Assignees data
     */
    public function getTaskAssignees($task_id)
    {
        global $wpdb;

        $table_name = $this->get_table_name();
        $users_table = $wpdb->base_prefix . 'users';

        $query = "
            SELECT $table_name.*, $users_table.display_name AS assignee_name
            FROM $table_name
            JOIN $users_table ON $table_name.assignee_id = $users_table.ID
            WHERE $table_name.task_id = %d
        ";

        $prepared_query = $wpdb->prepare($query, (int)$task_id);
        return $wpdb->get_results($prepared_query);
    }
}