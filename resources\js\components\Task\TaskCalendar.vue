<template>
  <div class="bg-white rounded-lg shadow">
    <!-- Calendar View -->
    <div class="calendar-container">
      <!-- Days of week headers -->
      <div class="grid grid-cols-7 gap-px bg-gray-200">
        <div v-for="day in daysOfWeek" :key="day" class="bg-white p-2 text-center text-sm font-medium text-gray-700">
          {{ day }}
        </div>
      </div>

      <!-- Calendar grid -->
      <div class="grid grid-cols-7 gap-px bg-gray-200">
        <!-- Calendar days -->
        <div v-for="(day, index) in calendarDays" :key="index"
          :class="[
            'min-h-32 bg-white p-1 relative',
            day.isCurrentMonth ? 'bg-white' : 'bg-gray-50 text-gray-400',
            day.isToday ? 'bg-blue-50' : '',
            day.date.getDay() === 0 || day.date.getDay() === 6 ? 'bg-gray-50' : ''
          ]"
          @click="handleDayClick(day.date)">

          <!-- Day number -->
          <div :class="[
            'absolute top-1 right-1 flex h-6 w-6 items-center justify-center rounded-full text-sm font-semibold',
            day.isToday ? 'bg-black text-white' : ''
          ]">
            {{ day.date.getDate() }}
          </div>

          <!-- Tasks for this day -->
          <div class="mt-8 space-y-1 max-h-28 overflow-y-auto">
            <div v-for="task in day.tasks" :key="task.id"
              :class="[
                'px-2 py-1 text-xs rounded-lg cursor-pointer truncate',
                task.priority === 'high' ? 'bg-red-100 text-red-800' :
                task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-blue-100 text-blue-800',
                task.status === 'completed' ? 'line-through opacity-60' : ''
              ]"
              @click.stop="$emit('task-click', task.id)">
              {{ task.title }}
            </div>
          </div>

          <!-- Add task button (always visible) -->
          <div class="absolute bottom-1 right-1">
            <button class="p-1 bg-black text-white rounded-full shadow hover:bg-black"
              @click.stop="$emit('day-click', formatDate(day.date))">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskCalendar',
  props: {
    tasks: {
      type: Array,
      required: true
    },
    currentMonth: {
      type: Number,
      required: true
    },
    currentYear: {
      type: Number,
      required: true
    }
  },
  computed: {
    daysOfWeek() {
      return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    },

    calendarDays() {
      const days = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get first day of the month
      const firstDayOfMonth = new Date(this.currentYear, this.currentMonth, 1);

      // Get the day of week the month starts on (0-6)
      const startDayOfWeek = firstDayOfMonth.getDay();

      // Get the last day of the month
      const lastDayOfMonth = new Date(this.currentYear, this.currentMonth + 1, 0);
      const daysInMonth = lastDayOfMonth.getDate();

      // Get the last day of the previous month
      const prevMonthLastDay = new Date(this.currentYear, this.currentMonth, 0);
      const prevMonthDays = prevMonthLastDay.getDate();

      // Fill in days from previous month
      for (let i = startDayOfWeek - 1; i >= 0; i--) {
        const date = new Date(this.currentYear, this.currentMonth - 1, prevMonthDays - i);
        days.push({
          date,
          isCurrentMonth: false,
          isToday: this.isSameDay(date, today),
          tasks: this.getTasksForDate(date)
        });
      }

      // Fill in days of current month
      for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(this.currentYear, this.currentMonth, i);
        days.push({
          date,
          isCurrentMonth: true,
          isToday: this.isSameDay(date, today),
          tasks: this.getTasksForDate(date)
        });
      }

      // Calculate how many days we need from next month to complete the calendar grid
      // (We always show 6 rows of 7 days = 42 cells)
      const remainingDays = 42 - days.length;

      // Fill in days from next month
      for (let i = 1; i <= remainingDays; i++) {
        const date = new Date(this.currentYear, this.currentMonth + 1, i);
        days.push({
          date,
          isCurrentMonth: false,
          isToday: this.isSameDay(date, today),
          tasks: this.getTasksForDate(date)
        });
      }

      return days;
    }
  },
  methods: {
    getTasksForDate(date) {
      return this.tasks.filter(task => {
        if (!task.due_date) return false;

        try {
          const taskDate = new Date(task.due_date);

          // Check if the task date is valid
          if (isNaN(taskDate.getTime())) {
            console.warn('Invalid due_date value in TaskCalendar:', task.due_date);
            return false;
          }

          return this.isSameDay(taskDate, date);
        } catch (error) {
          console.error('Error parsing task date in TaskCalendar:', error, 'Due date:', task.due_date);
          return false;
        }
      });
    },

    isSameDay(date1, date2) {
      return date1.getDate() === date2.getDate() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getFullYear() === date2.getFullYear();
    },

    handleDayClick(date) {
      this.$emit('day-click', this.formatDate(date));
    },

    formatDate(date) {
      // Format date as YYYY-MM-DD for task creation
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
};
</script>

<style scoped>
.calendar-container {
  user-select: none;
}
</style>