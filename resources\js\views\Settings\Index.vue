<template>
  <div class="w-full min-h-screen">
    <div class="max-w-[1600px] mx-auto px-4 py-6">
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- Sidebar Navigation -->
        <div class="w-full lg:w-1/4">
          <div
            class="bg-white rounded-3xl shadow-sm border border-gray-100 sticky top-6"
          >
            <!-- Sidebar Header -->
            <div class="p-6 border-b border-gray-100">
              <div class="flex items-center gap-4">
                <div
                  class="w-12 h-12 bg-violet-100 rounded-2xl flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-violet-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h2 class="text-xl font-semibold text-gray-900">Settings</h2>
                  <p class="text-sm text-gray-500">
                    Manage your application settings
                  </p>
                </div>
              </div>
            </div>

            <!-- Navigation Menu -->
            <div class="p-4">
              <nav class="space-y-1">

                <router-link
                  v-if="getUserRole() === 'clinic_admin'"
                  :to="{ name: 'setting.subscription' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'subscription'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'subscription'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                    <svg class="w-5 h-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"
                    :class="[
                          currentRouteModule === 'subscription'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        >
                      <path d="M64 64C28.7 64 0 92.7 0 128L0 384c0 35.3 28.7 64 64 64l448 0c35.3 0 64-28.7 64-64l0-256c0-35.3-28.7-64-64-64L64 64zm64 320l-64 0 0-64c35.3 0 64 28.7 64 64zM64 192l0-64 64 0c0 35.3-28.7 64-64 64zM448 384c0-35.3 28.7-64 64-64l0 64-64 0zm64-192c-35.3 0-64-28.7-64-64l64 0 0 64zM288 160a96 96 0 1 1 0 192 96 96 0 1 1 0-192z"/>
                    </svg>
                    </div>
                    <span> Subscription</span>
                  </div>
                </router-link>

                <!-- General Settings -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.general-setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'general_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'general_setting'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'general_setting'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <span>{{
                      formTranslation.settings.general +
                      " " +
                      formTranslation.common.settings
                    }}</span>
                  </div>
                </router-link>

                <!-- Holidays -->
                <router-link
                  v-if="getUserRole() !== 'patient'"
                  :to="{ name: 'clinic.schedule' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'clinic_schedule'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'clinic_schedule'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'clinic_schedule'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.settings.holidays }}</span>
                  </div>
                </router-link>

                <!-- Subscription -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'subscription' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'subscription'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'subscription'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'subscription'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </div>
                    <span>Subscription</span>
                  </div>
                </router-link>

                <!-- Doctor Sessions -->
                <router-link
                  v-if="
                    getUserRole() === 'doctor' ||
                    (getUserRole() === 'receptionist' &&
                      kcCheckPermission('doctor_session_list'))
                  "
                  :to="{ name: 'doctor.session.setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'doctor_session'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'doctor_session'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'doctor_session'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <span>{{
                      formTranslation.doctor_session.doc_sessions
                    }}</span>
                  </div>
                </router-link>

                <!-- Telemedicine -->
                <router-link
                  v-if="getUserRole() === 'doctor'"
                  :to="{ name: 'doctor.telemed.setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'telemed'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    !userData.addOns.telemed && !userData.addOns.googlemeet
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :disabled="
                    !userData.addOns.telemed && !userData.addOns.googlemeet
                  "
                  :title="
                    !userData.addOns.telemed &&
                    !userData.addOns.googlemeet &&
                    AddonsList?.googlemeetAndtelemed?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'telemed'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'telemed'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.common.telemed }}</span>
                    </div>
                    <span
                      v-if="
                        !userData.addOns.telemed && !userData.addOns.googlemeet
                      "
                      v-html="kivicareProFeatureIcon('googlemeetAndtelemed')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Continue with the pattern for other menu items -->

                <!-- Example of a Pro Feature Menu Item -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.comman_settings' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'comman_settings'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'comman_settings'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'comman_settings'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.settings.pro_settings }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Configurations -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'clinic.configuration' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'clinic_configuration'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'clinic_configuration'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'clinic_configuration'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.settings.configurations }}</span>
                  </div>
                </router-link>

                <!-- Template Manager -->
                <router-link
                  v-if="getUserRole() === 'administrator' || getUserRole() === 'clinic_admin' || getUserRole() === 'doctor'"
                  :to="{ name: 'templates' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'templates'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'templates'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'templates'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <span>Template Manager</span>
                  </div>
                </router-link>

                <!-- App Config -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'app-config' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'app_config'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.api != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.api != true && AddonsList?.app?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'app_config'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'app_config'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.settings.app_config }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.api != true"
                      v-html="kivicareProFeatureIcon('app')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Email Template -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'email.template' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'email_template'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'email_template'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'email_template'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.settings.email_template }}</span>
                  </div>
                </router-link>

                <!-- SMS Template -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'sms.template' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'sms_template'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'sms_template'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'sms_template'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.settings.sms_template }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Webhooks -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'webhooks' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    [
                      'webhooks',
                      'webhooks.create',
                      'webhooks.edit',
                      'webhooks.log',
                    ].includes($route.name)
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    !userData.addOns.webhooks
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true &&
                    AddonsList?.webhooks?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          [
                            'webhooks',
                            'webhooks.create',
                            'webhooks.edit',
                            'webhooks.log',
                          ].includes($route.name)
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            [
                              'webhooks',
                              'webhooks.create',
                              'webhooks.edit',
                              'webhooks.log',
                            ].includes($route.name)
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.webhooks.webhooks }}</span>
                    </div>
                    <span
                      v-if="!userData.addOns.webhooks"
                      v-html="kivicareProFeatureIcon('webhooks')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>
                <!-- Custom Notification -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'custom_notification' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'custom_notification'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'custom_notification'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'custom_notification'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                          />
                        </svg>
                      </div>
                      <span>{{
                        formTranslation.common.custom_notification
                      }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Custom Form -->
                <router-link
                  v-if="
                    ['administrator', 'clinic_admin'].includes(getUserRole())
                  "
                  :to="{ name: 'custom_form' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'custom_form'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'custom_form'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'custom_form'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.common.custom_form }}</span>
                  </div>
                </router-link>

                <!-- Body Chart Setting -->
                <router-link
                  v-if="['administrator'].includes(getUserRole())"
                  :to="{ name: 'body_chart_setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'body_chart_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.bodyChart != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.bodyChart != true &&
                    AddonsList?.bodyChart?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'body_chart_setting'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'body_chart_setting'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                          />
                        </svg>
                      </div>
                      <span>{{
                        formTranslation.common.encounter_body_chart
                      }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.bodyChart != true"
                      v-html="kivicareProFeatureIcon('bodyChart')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Static Data -->
                <router-link
                  v-if="
                    getUserRole() !== 'patient' &&
                    kcCheckPermission('static_data_list')
                  "
                  :to="{ name: 'static.data' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'static_data'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'static_data'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'static_data'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.settings.listings }}</span>
                  </div>
                </router-link>

                <!-- Custom Fields -->
                <router-link
                  v-if="
                    checkEnableModule('custom_fields') &&
                    (getUserRole() === 'administrator' ||
                      getUserRole() === 'clinic_admin') &&
                    kcCheckPermission('custom_field_list')
                  "
                  :to="{ name: 'custom.field' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'custom_field'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'custom_field'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'custom_field'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.settings.custom_field }}</span>
                  </div>
                </router-link>
                <!-- Patient Setting -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.patient_setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'patient_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'patient_setting'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'patient_setting'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.settings.patient_setting }}</span>
                  </div>
                </router-link>

                <!-- Widget Setting -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.widget_setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'widget_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'widget_setting'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'widget_setting'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
                        />
                      </svg>
                    </div>
                    <span>{{
                      formTranslation.widget_setting.widget_setting
                    }}</span>
                  </div>
                </router-link>

                <!-- Google Event Template -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'google.event.template' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'google_event_template'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'google_event_template'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'google_event_template'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.google_event.template }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Google Meet -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.googlemeet' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'googlemeet'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.googlemeet != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.googlemeet != true &&
                    AddonsList?.googlemeet?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'googlemeet'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'googlemeet'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.googlemeet.googlemeet }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.googlemeet != true"
                      v-html="kivicareProFeatureIcon('googlemeet')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Payment Settings -->
                <router-link
                  v-if="
                    getUserRole() === 'administrator' ||
                    getUserRole() === 'clinic_admin'
                  "
                  :to="{ name: 'payment.setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'payment_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'payment_setting'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'payment_setting'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </div>
                    <span>{{ formTranslation.settings.payment }}</span>
                  </div>
                </router-link>

                <!-- Language Settings -->
                <router-link
                  v-if="getUserRole() !== 'doctor' && newUser == 'false'"
                  :to="{ name: 'setting.language_settings' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'language_settings'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'language_settings'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'language_settings'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
                          />
                        </svg>
                      </div>
                      <span>{{
                        formTranslation.settings.language_settings
                      }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Google Calendar Integration -->
                <router-link
                  v-if="
                    userData.is_enable_google_cal == 'on' &&
                    (getUserRole() === 'doctor' ||
                      getUserRole() === 'receptionist')
                  "
                  :to="{ name: 'setting.google_calender' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'google_calender'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'google_calender'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'google_calender'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <span>{{
                        formTranslation.common.google_calendar_integration
                      }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Google Meet Integration -->
                <router-link
                  v-if="
                    getUserRole() === 'doctor' &&
                    userData.is_enable_googleMeet === 'on'
                  "
                  :to="{ name: 'setting.google_meet_config' }"
                  class="group flex items-center px-4 py-3 rounded-xl transition-all duration-200"
                  :class="[
                    currentRouteModule === 'google_meet_config'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.googlemeet !== true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.googlemeet != true &&
                    AddonsList?.googlemeet?.message
                      ? AddonsList.googlemeet.message
                      : ''
                  "
                  :disabled="userData.addOns.googlemeet != true"
                >
                  <div
                    class="w-9 h-9 rounded-lg flex items-center justify-center mr-3"
                    :class="[
                      currentRouteModule === 'google_meet_config'
                        ? 'bg-violet-100'
                        : 'bg-gray-100',
                    ]"
                  >
                    <i
                      class="fa fa-video"
                      :class="[
                        currentRouteModule === 'google_meet_config'
                          ? 'text-violet-600'
                          : 'text-gray-600',
                      ]"
                      v-b-tooltip.hover
                      :title="
                        formTranslation.googlemeet.google_meet_intergration
                      "
                    ></i>
                  </div>
                  <span class="text-sm font-medium">
                    {{ formTranslation.googlemeet.google_meet_intergration }}
                  </span>
                  <span
                    v-if="userData.addOns.googlemeet != true"
                    v-html="kivicareProFeatureIcon('googlemeet')"
                    class="ml-2"
                  ></span>
                </router-link>

                <!-- Consultation Template Setting -->
                <router-link
                  v-if="getUserRole() === 'clinic_admin'"
                  :to="{ name: 'setting.encounter_template_setting' }"
                  class="group flex items-center px-4 py-3 rounded-xl transition-all duration-200"
                  :class="[
                    currentRouteModule === 'encounter_template_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div
                    class="w-9 h-9 rounded-lg flex items-center justify-center mr-3"
                    :class="[
                      currentRouteModule === 'encounter_template_setting'
                        ? 'bg-violet-100'
                        : 'bg-gray-100',
                    ]"
                  >
                    <i
                      class="far fa-calendar"
                      :class="[
                        currentRouteModule === 'encounter_template_setting'
                          ? 'text-violet-600'
                          : 'text-gray-600',
                      ]"
                      v-b-tooltip.hover
                      :title="formTranslation.common.encounter_template_setting"
                    ></i>
                  </div>
                  <span class="text-sm font-medium">
                    {{ formTranslation.common.encounter_template_setting }}
                  </span>
                </router-link>

                <!-- Appointment Restriction -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.appointment-setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'appointment_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                        currentRouteModule === 'appointment_setting'
                          ? 'bg-violet-100'
                          : 'bg-gray-100',
                      ]"
                    >
                      <svg
                        class="w-5 h-5"
                        :class="[
                          currentRouteModule === 'appointment_setting'
                            ? 'text-violet-600'
                            : 'text-gray-600',
                        ]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2zM12 12v4m0 0l-2-2m2 2l2-2"
                        />
                      </svg>
                    </div>
                    <span>{{
                      formTranslation.appointments.restrict_appointment
                    }}</span>
                  </div>
                </router-link>

                <!-- Permission Setting -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.permission-setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'permission_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'permission_setting'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'permission_setting'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
                          />
                        </svg>
                      </div>
                      <span>{{
                        formTranslation.settings.permission_setting
                      }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>

                <!-- Sidebar Setting -->
                <router-link
                  v-if="getUserRole() === 'administrator'"
                  :to="{ name: 'setting.dashboard-sidebar-setting' }"
                  :class="[
                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200',
                    currentRouteModule === 'dashboard_sidebar_setting'
                      ? 'bg-violet-50 text-violet-600'
                      : 'text-gray-700 hover:bg-gray-50',
                    userData.addOns.kiviPro != true
                      ? 'opacity-50 cursor-not-allowed'
                      : '',
                  ]"
                  :title="
                    userData.addOns.kiviPro != true && AddonsList?.pro?.message
                  "
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-9 h-9 rounded-lg flex items-center justify-center mr-3',
                          currentRouteModule === 'dashboard_sidebar_setting'
                            ? 'bg-violet-100'
                            : 'bg-gray-100',
                        ]"
                      >
                        <svg
                          class="w-5 h-5"
                          :class="[
                            currentRouteModule === 'dashboard_sidebar_setting'
                              ? 'text-violet-600'
                              : 'text-gray-600',
                          ]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M4 6h16M4 12h16M4 18h7"
                          />
                        </svg>
                      </div>
                      <span>{{ formTranslation.common.sidebar_setting }}</span>
                    </div>
                    <span
                      v-if="userData.addOns.kiviPro != true"
                      v-html="kivicareProFeatureIcon('pro')"
                      class="ml-2"
                    ></span>
                  </div>
                </router-link>
              </nav>
            </div>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="w-full lg:w-3/4">
          <div class="bg-white rounded-3xl shadow-sm">
            <transition name="fade" mode="out-in">
              <router-view></router-view>
            </transition>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// Script remains the same
import { AddonsList } from "../../const/addons";
export default {
  data: () => {
    return {
      newUser: window.request_data.new_user,
      showHardtLink: "off",
      AddonsList,
    };
  },
  mounted() {
    this.getRequestHelper();
  },
  methods: {
    getRequestHelper: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.showHardtLink = window.request_data.link_show_hide;
      }
    },
  },
  computed: {
    activeRouteClass() {
      return "bg-violet-50 text-violet-600";
    },
    currentRouteModule: function () {
      if (window.innerWidth < 1500) {
        let wrapperMenu = document.querySelector(
          ".main-content .sidenav-toggler"
        );
        if (wrapperMenu) {
          wrapperMenu.classList.remove("active");
          document
            .querySelector(".main-content .sidenav-toggler")
            .classList.remove("active");
          document.querySelector("body").classList.add("g-sidenav-hidden");
          localStorage.setItem("sidebarToggle", false);
        }
      }
      return this.$route.meta.module;
    },
    isWoocommerceEnabled() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user
      ) {
        return this.$store.state.userDataModule.user.woocommercePayment;
      }
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
};
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
