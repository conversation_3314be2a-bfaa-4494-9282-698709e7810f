<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCTDLClinicSetting extends KCModel {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct('tdl_clinic_settings');
    }

    /**
     * Get settings for a specific clinic
     *
     * @param int $clinic_id Clinic ID
     * @return array|false Settings or false if not found
     */
    public function getClinicSettings($clinic_id) {
        global $wpdb;
        
        $clinic_id = (int)$clinic_id;
        $table_name = $this->get_table_name();
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE clinic_id = %d",
            $clinic_id
        );
        
        return $wpdb->get_row($query, ARRAY_A);
    }

    /**
     * Save clinic TDL settings
     *
     * @param array $data Settings data
     * @return int|bool ID of inserted/updated record or false on failure
     */
    public function saveClinicSettings($data) {
        // Ensure we have required fields
        if (empty($data['clinic_id'])) {
            return false;
        }
        
        $clinic_id = (int)$data['clinic_id'];
        $current_settings = $this->getClinicSettings($clinic_id);
        
        // Sanitize data
        $save_data = [
            'clinic_id' => $clinic_id,
            'account_id' => isset($data['account_id']) ? sanitize_text_field($data['account_id']) : '',
            'api_key' => isset($data['api_key']) ? sanitize_text_field($data['api_key']) : '',
            'sender_id' => isset($data['sender_id']) ? sanitize_text_field($data['sender_id']) : '',
            'azure_storage_connection' => isset($data['azure_storage_connection']) ? sanitize_text_field($data['azure_storage_connection']) : '',
            'azure_storage_container' => isset($data['azure_storage_container']) ? sanitize_text_field($data['azure_storage_container']) : '',
            'updated_at' => current_time('mysql')
        ];
        
        // Handle additional settings fields if present
        if (isset($data['api_endpoint'])) {
            $save_data['api_endpoint'] = sanitize_text_field($data['api_endpoint']);
        }
        
        if (isset($data['is_active'])) {
            $save_data['is_active'] = (int)$data['is_active'];
        }
        
        if (isset($data['config_json']) && is_array($data['config_json'])) {
            $save_data['config_json'] = wp_json_encode($data['config_json']);
        } else if (isset($data['config_json']) && is_string($data['config_json'])) {
            $save_data['config_json'] = sanitize_text_field($data['config_json']);
        }
        
        if ($current_settings) {
            // Update existing settings
            return $this->update($save_data, ['clinic_id' => $clinic_id]);
        } else {
            // Insert new settings
            $save_data['created_at'] = current_time('mysql');
            return $this->insert($save_data);
        }
    }
    
    /**
     * Get all clinic settings
     *
     * @param array $filters Optional filters
     * @return array Array of settings
     */
    public function getAllClinicSettings($filters = []) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        $clinics_table = $wpdb->prefix . 'kc_clinics';
        
        $query = "
            SELECT s.*, c.name as clinic_name 
            FROM {$table_name} s
            LEFT JOIN {$clinics_table} c ON s.clinic_id = c.id
            WHERE 1=1
        ";
        
        // Apply filters
        if (!empty($filters['clinic_id'])) {
            $clinic_id = (int)$filters['clinic_id'];
            $query .= $wpdb->prepare(" AND s.clinic_id = %d", $clinic_id);
        }
        
        if (isset($filters['is_active'])) {
            $is_active = (int)$filters['is_active'];
            $query .= $wpdb->prepare(" AND s.is_active = %d", $is_active);
        }
        
        // Add ordering
        if (!empty($filters['orderby'])) {
            $orderby = esc_sql($filters['orderby']);
            $order = !empty($filters['order']) ? esc_sql($filters['order']) : 'ASC';
            $query .= " ORDER BY s.{$orderby} {$order}";
        } else {
            // Default ordering by clinic_id
            $query .= " ORDER BY s.clinic_id ASC";
        }
        
        // Apply pagination
        if (!empty($filters['per_page']) && !empty($filters['page'])) {
            $per_page = (int)$filters['per_page'];
            $page = (int)$filters['page'];
            $offset = ($page - 1) * $per_page;
            $query .= $wpdb->prepare(" LIMIT %d, %d", $offset, $per_page);
        }
        
        return $wpdb->get_results($query, ARRAY_A);
    }
    
    /**
     * Count all clinic settings
     *
     * @param array $filters Optional filters
     * @return int Count of settings
     */
    public function countClinicSettings($filters = []) {
        global $wpdb;
        
        $table_name = $this->get_table_name();
        
        $query = "SELECT COUNT(*) FROM {$table_name} WHERE 1=1";
        
        // Apply filters
        if (!empty($filters['clinic_id'])) {
            $clinic_id = (int)$filters['clinic_id'];
            $query .= $wpdb->prepare(" AND clinic_id = %d", $clinic_id);
        }
        
        if (isset($filters['is_active'])) {
            $is_active = (int)$filters['is_active'];
            $query .= $wpdb->prepare(" AND is_active = %d", $is_active);
        }
        
        return (int)$wpdb->get_var($query);
    }
    
    /**
     * Delete clinic settings
     *
     * @param int $clinic_id Clinic ID
     * @return bool Success or failure
     */
    public function deleteClinicSettings($clinic_id) {
        return $this->delete(['clinic_id' => (int)$clinic_id]);
    }
}