<?php
/**
 * Fix for service import CSV validation
 * 
 * This filter removes duplicate entries from the required_field array
 * to prevent validation errors during CSV import
 */

add_filter('kcpro_import_module_wise_data', function($response) {
    // Only apply this fix to service imports
    if (isset($response['data']['module_type']) && $response['data']['module_type'] === 'service') {
        // Remove duplicate entries from required_field array
        if (isset($response['data']['required_field']) && is_array($response['data']['required_field'])) {
            $response['data']['required_field'] = array_unique($response['data']['required_field']);
        }
    }
    
    return $response;
}, 9, 1); // Priority 9 to run before the main import handler
