<template>
  <div> <!-- Single root element wrapper -->
    <!-- AI Scribe Control Bar -->
    <div class="flex items-center gap-2">
      <div class="flex items-center justify-center px-3 py-1 bg-blue-50 rounded-l-lg border-l-4 border-blue-500">
        <span class="text-blue-600 font-semibold mr-1">AI Scribe</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2">
          <path d="M12 18h.01"></path>
          <path d="M8 3h8a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2l-4 4v-4H8a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"></path>
        </svg>
      </div>

      <div class="flex items-center gap-3 border-t border-b border-r rounded-r-lg px-2 py-1.5">
        <!-- Record Audio Button -->
        <button @click="toggleRecording" :disabled="['processing', 'analyzing', 'populating'].includes(status)" :class="[
          'h-8 w-12 flex items-center justify-center rounded transition-all duration-300 relative overflow-hidden',
          status === 'recording' ? (isPaused ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-red-500 hover:bg-red-600') : 'bg-black hover:bg-gray-800'
        ]">
          <!-- Microphone Icon (when not recording) -->
          <svg v-if="status !== 'recording'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="w-6 h-6 text-white transition-all duration-300">
            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
            <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
            <line x1="12" x2="12" y1="19" y2="22"></line>
          </svg>

          <!-- Stop Icon (when recording and not paused) -->
          <svg v-else-if="!isPaused" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="w-6 h-6 text-white transition-all duration-300">
            <rect x="6" y="6" width="12" height="12"></rect>
          </svg>

          <!-- Play Icon (when recording is paused) -->
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="w-6 h-6 text-white transition-all duration-300">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>

          <!-- Pulsing Background Effect -->
          <div v-if="status === 'recording' && !isPaused" class="absolute inset-0 bg-red-400 opacity-30 animate-ping">
          </div>
        </button>

        <!-- Pause/Resume Button (only shown when recording) -->
        <button v-if="status === 'recording'" @click="togglePause"
          class="h-8 w-8 flex items-center justify-center rounded transition-all duration-300 bg-gray-700 hover:bg-gray-800 text-white">
          <!-- Pause Icon -->
          <svg v-if="!isPaused" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
            <line x1="6" y1="4" x2="6" y2="20"></line>
            <line x1="18" y1="4" x2="18" y2="20"></line>
          </svg>

          <!-- Resume Icon -->
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
        </button>

        <!-- Recording Animation Waves -->
        <div v-if="status === 'recording' && !isPaused" class="flex items-end space-x-1 h-8">
          <div v-for="n in 4" :key="n" class="w-1 bg-red-500 rounded-full transform transition-all duration-200"
            :class="[`animate-wave-${n}`]"
            :style="`animation-delay: ${n * 0.1}s; height: ${(Math.random() * 20) + 5}px`"></div>
          <div class="ml-2 flex items-center space-x-1">
            <div class="w-1.5 h-1.5 rounded-full bg-red-500 animate-pulse"></div>
            <div class="w-1.5 h-1.5 rounded-full bg-red-400 animate-pulse" style="animation-delay: 0.2s"></div>
          </div>
        </div>

        <!-- Paused Indicator -->
        <div v-if="status === 'recording' && isPaused" class="text-yellow-500 text-sm font-medium">
          Paused
        </div>

        <!-- Recording Duration -->
        <div v-if="status === 'recording'" class="text-sm ml-2">
          {{ formatTime(recordingDuration) }}
        </div>

        <!-- Alternative Options -->
        <div v-if="status === 'idle'" class="flex items-center gap-2">
          <!-- File Upload Button -->
          <label
            class="flex items-center gap-1 cursor-pointer px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="17 8 12 3 7 8"></polyline>
              <line x1="12" x2="12" y1="3" y2="15"></line>
            </svg>
            Upload Audio
            <input type="file" accept="audio/*, .mp3, .wav, .ogg, .webm, .m4a, .mp4, .aac" class="hidden"
              @change="handleFileUpload" />
          </label>

          <!-- Mobile Recording Button -->
          <button @click="showMobileRecordingModal"
            class="flex items-center gap-1 px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="22"></line>
            </svg>
            Mobile Recording
          </button>

          <!-- Manual Transcript Entry Button -->
          <button @click="showManualTranscriptDialog"
            class="px-3 py-1.5 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm flex items-center gap-2">
            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            Enter Manually
          </button>
        </div>

        <!-- Processing Indicator -->
        <div v-if="['processing', 'analyzing', 'populating'].includes(status)" class="flex flex-col justify-center">
          <div class="text-sm font-semibold mb-1">
            {{ status === 'processing' ? 'Processing audio' : status === 'analyzing' ? 'Analyzing transcript' :
            'Populating records' }}...
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="bg-blue-600 h-2.5 rounded-full" :style="`width: ${processingProgress}%`"></div>
          </div>
        </div>

        <!-- Video Tab Navigation For Processing Status -->
        <div v-if="status === 'transcribed' || status === 'analyzed'" class="flex flex-col gap-1">
          <button @click="showTranscriptModal = true"
            class="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
            View Transcript
          </button>

          <button v-if="status === 'analyzed'" @click="populateRecords"
            class="px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded text-sm flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            Use Analysis
          </button>
        </div>

        <!-- Display Errors -->
        <div v-if="mediaError" class="text-red-500 text-sm">
          {{ mediaError }}
        </div>
      </div>
    </div>

    <!-- Manual Transcript Dialog -->
    <div v-if="showManualModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Enter Transcript Manually</h2>
          <button @click="closeManualModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="mb-4 flex-grow overflow-y-auto">
          <p class="text-sm text-gray-600 mb-2">Enter the conversation transcript below. Prefix each speaker with
            "Doctor:" or "Patient:" to help with analysis.</p>
          <textarea v-model="transcript" class="w-full min-h-[300px] p-3 border rounded" placeholder="Doctor: Hello, how are you feeling today?
Patient: I've been having headaches for the past week.
Doctor: Can you tell me more about these headaches?" style="height: 402px;"></textarea>
        </div>

        <div class="flex justify-end gap-2 mt-2">
          <button @click="closeManualModal" class="px-4 py-2 border rounded hover:bg-gray-50">
            Cancel
          </button>
          <button @click="processManualTranscript" class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded"
            :disabled="!transcript || transcript.trim() === ''">
            Analyze Transcript
          </button>
        </div>
      </div>
    </div>

    <!-- Transcript Review Modal -->
    <div v-if="showTranscriptModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl my-4 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Review Transcript</h2>
          <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Content area with scrolling -->
        <div class="flex-grow overflow-y-auto pr-2">
          <!-- Diarized Transcript Display -->
          <div v-if="diarizedTranscript && diarizedTranscript.length > 0" class="mb-4">
            <h3 class="text-md font-medium mb-2">Speaker Diarization</h3>
            <div class="space-y-2 mb-4">
              <div v-for="(utterance, index) in diarizedTranscript" :key="index" class="p-3 rounded-lg"
                :class="utterance.speaker === 0 ? 'bg-blue-50' : 'bg-green-50'">
                <div class="font-semibold mb-1">
                  Speaker {{ utterance.speaker === 0 ? '1 (Doctor)' : '2 (Patient)' }}
                </div>
                <div>{{ utterance.text }}</div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <h3 class="text-md font-medium mb-2">Complete Transcript</h3>
            <textarea v-model="transcript"
              class="w-full min-h-[200px] p-3 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
              style="height: 280px;"></textarea>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <button @click="closeModal" class="px-4 py-2 border rounded hover:bg-gray-50">
            Close
          </button>
          <button v-if="status === 'transcribed'" @click="analyzeTranscript"
            class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded">
            Analyze Transcript
          </button>
          <button v-else-if="status === 'analyzed'" @click="populateRecords"
            class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded" :disabled="status === 'populating'">
            Populate Medical Records
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Recording Modal -->
    <div v-if="showMobileDialog"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 overflow-y-auto pt-10 pb-10">
      <div class="bg-white rounded-lg p-6 w-full max-w-md my-4 flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Record With Mobile Device</h2>
          <button @click="showMobileDialog = false" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="flex justify-center mb-6">
          <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="QR Code" class="w-48 h-48" />
          <div v-else class="w-48 h-48 flex items-center justify-center bg-gray-100 rounded-lg">
            <svg class="animate-spin h-8 w-8 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
          </div>
        </div>

        <div class="text-center mb-4">
          <p class="text-sm text-gray-600 mb-2">Scan this QR code with your mobile device to record audio</p>
          <div v-if="sessionStatus" class="text-xs text-gray-500">
            Session expires in {{ sessionTimeRemaining }}
          </div>
        </div>

        <div v-if="mobileRecordingError" class="bg-red-50 p-3 rounded-md mb-4">
          <p class="text-sm text-red-600">{{ mobileRecordingError }}</p>
        </div>

        <div v-if="mobileRecordingSuccess" class="bg-green-50 p-3 rounded-md mb-4">
          <p class="text-sm text-green-600">{{ mobileRecordingSuccess }}</p>
        </div>

        <div class="flex justify-between">
          <button @click="showMobileDialog = false" class="px-4 py-2 border rounded hover:bg-gray-50">
            Cancel
          </button>

          <div>
            <button v-if="sessionProcessed" @click="retrieveTranscript"
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
              Load Transcript
            </button>

            <button v-else-if="sessionId && !sessionProcessed" @click="checkSessionStatus"
              class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded">
              Check Status
            </button>

            <button v-else @click="createMobileRecordingSession" :disabled="isCreatingSession"
              class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed">
              <span v-if="isCreatingSession" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                  viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                Generating...
              </span>
              <span v-else>Generate QR Code</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post } from "../../config/request";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import Recorder from './recorder'; // Import our custom recorder implementation

export default {
  name: 'AIScribe',
  emits: ['records-updated'],
  props: {
    encounterId: {
      type: [Number, String],
      required: true,
      validator: function (value) {
        if (!value && value !== 0) {
          console.warn('AIScribe: encounterId prop is required and cannot be null or undefined');
          return false;
        }
        return true;
      }
    },
    patientId: {
      type: [String, Number],
      default: null
    },
    patientDetails: {
      type: Object,
      required: true,
    },
  },

  watch: {
    encounterId: {
      immediate: true,
      handler(newVal) {
        console.log('AIScribe encounterId updated:', newVal);
      }
    }
  },
  data() {
    return {
      status: 'idle',
      transcript: '',
      diarizedTranscript: [],
      showTranscriptModal: false,
      showResultsModal: false,
      showManualModal: false,
      recorder: null,
      audioChunks: [],
      audioBlob: null,
      audioUrl: null,
      audioElement: null, // HTML audio element for playback
      isAudioPlaying: false, // Track if audio is currently playing
      audioProgress: 0, // Progress percentage for audio playback
      audioCurrentTime: 0, // Current playback time in seconds
      audioDuration: 0, // Total duration in seconds
      audioUpdateTimer: null, // Timer for updating audio progress
      analysisResults: null,
      recordingStartTime: null,
      recordingTimer: null,
      recordingDuration: 0,
      isPaused: false,
      pauseStartTime: null,
      totalPausedDuration: 0,
      maxRecordingTime: 45 * 60, // 45 minutes in seconds to handle longer consultations
      mediaError: null,  // Property to store media-related errors
      canRecord: true, // Flag to track if recording is available
      processingStatus: '', // Status message during processing
      processingProgress: 0, // Progress percentage for processing
      isGoogleMeetActive: false, // Flag for Google Meet detection
      largeFileThresholdMB: 50, // Threshold in MB to determine large files
      maxFileSizeMB: 250, // Maximum file size in MB that we can reasonably process
      chunkSizeMB: 25, // Size of chunks when processing large files
      downloadCounter: 1, // Counter for unique file downloads

      // Mobile recording related data properties
      showMobileDialog: false,
      isCreatingSession: false,
      sessionId: null,
      qrCodeUrl: null,
      recordUrl: null,
      sessionStatus: null,
      sessionTimeRemaining: '',
      sessionProcessed: false,
      mobileRecordingError: null,
      mobileRecordingSuccess: null,
      sessionCheckTimer: null,
      recordingDocumentId: null,
    }
  },

  mounted() {
    // Test if recording is available
    this.checkRecordingAvailability();

    // For testing only - uncomment in development if needed
    /*
    this.diarizedTranscript = [
      { speaker: 0, text: "Hello, how are you feeling today?", start: 0, end: 3 },
      { speaker: 1, text: "I've been having headaches for the past week.", start: 3, end: 6 }
    ];
    this.transcript = "Doctor: Hello, how are you feeling today?\n\nPatient: I've been having headaches for the past week.";
    */
  },

  beforeDestroy() {
    this.cleanup();
    this.cleanupAudioPlayer();
  },

  beforeUnmount() {
    // Delete any recording when the appointment is closed
    this.deleteRecording();
    this.cleanupAudioPlayer();
  },

  // Track the Google Meet status - detect if we're in a Google Meet session
  created() {
    this.detectGoogleMeet();

    // Listen for appointment close or navigation away to delete recordings
    window.addEventListener('beforeunload', this.deleteRecording);
  },

  methods: {
    async checkRecordingAvailability() {
      try {
        // Try to create a Recorder instance
        const testRecorder = new Recorder();
        await testRecorder.initialize();
        this.canRecord = true;
        console.log('Recording is available');
      } catch (error) {
        console.error('Recording is not available:', error);
        this.canRecord = false;
        this.mediaError = 'Recording not supported in this browser. Please use file upload or manual entry.';
      }
    },

    cleanup() {
      // Clean up recorder if it exists
      if (this.recorder) {
        if (this.status === 'recording') {
          this.recorder.stop();
        }
        this.recorder = null;
      }

      // Clear timers
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }

      // Clean up audio URL if it exists
      if (this.audioUrl) {
        URL.revokeObjectURL(this.audioUrl);
      }
    },

    // Delete recording data completely
    deleteRecording() {
      // Clean up recorder
      this.cleanup();

      // Reset all recording data
      this.audioChunks = [];
      this.audioBlob = null;

      // Clean up audio player
      this.cleanupAudioPlayer();

      if (this.audioUrl) {
        URL.revokeObjectURL(this.audioUrl);
        this.audioUrl = null;
      }

      console.log('Recording deleted');
    },

    // Format seconds to MM:SS display with clean formatting
    formatTime(seconds) {
      // Ensure we're working with a valid number and round to nearest whole second
      seconds = Math.round(parseFloat(seconds) || 0);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // Toggle pause/resume recording
    togglePause() {
      if (!this.recorder || this.status !== 'recording') return;

      if (this.isPaused) {
        // Resume recording
        this.resumeRecording();
      } else {
        // Pause recording
        this.pauseRecording();
      }
    },

    // Pause the recording
    pauseRecording() {
      if (!this.recorder || this.status !== 'recording' || this.isPaused) return;

      // Pause the recorder
      try {
        // If our custom recorder has pause support
        if (this.recorder.pause) {
          this.recorder.pause();
        } else {
          // Otherwise we'll need to stop media tracks temporarily
          if (this.recorder.stream) {
            this.recorder.stream.getTracks().forEach(track => {
              track.enabled = false; // This effectively pauses without stopping
            });
          }
        }

        // Mark as paused and store pause time
        this.isPaused = true;
        this.pauseStartTime = Date.now();

        // Pause the recording timer
        if (this.recordingTimer) {
          clearInterval(this.recordingTimer);
        }
      } catch (error) {
        console.error('Error pausing recording:', error);
      }
    },

    // Resume the recording
    resumeRecording() {
      if (!this.recorder || this.status !== 'recording' || !this.isPaused) return;

      try {
        // If our custom recorder has resume support
        if (this.recorder.resume) {
          this.recorder.resume();
        } else {
          // Otherwise re-enable media tracks
          if (this.recorder.stream) {
            this.recorder.stream.getTracks().forEach(track => {
              track.enabled = true;
            });
          }
        }

        // Update the total paused duration
        if (this.pauseStartTime) {
          this.totalPausedDuration += (Date.now() - this.pauseStartTime) / 1000;
        }

        // Reset the recording start time to now
        this.recordingStartTime = Date.now();

        // Mark as not paused
        this.isPaused = false;
        this.pauseStartTime = null;

        // Resume the recording timer
        this.recordingTimer = setInterval(() => {
          if (!this.isPaused) {
            this.recordingDuration = this.totalPausedDuration +
              (Date.now() - this.recordingStartTime) / 1000;
          }
        }, 1000);
      } catch (error) {
        console.error('Error resuming recording:', error);
      }
    },

    // Initialize recorder and start recording
    async toggleRecording() {
      // If already recording, stop it
      if (this.status === 'recording') {
        this.stopRecording();
        return;
      }

      // Reset error message
      this.mediaError = null;

      // Validate recording capability
      if (!this.canRecord) {
        this.mediaError = 'Recording not supported in this browser. Please use file upload or manual entry.';
        return;
      }

      try {
        // Initialize recorder
        this.recorder = new Recorder();
        await this.recorder.initialize();

        // Start recording
        this.recorder.start();

        // Set up data handlers
        this.recorder.onDataAvailable = (chunk) => {
          this.audioChunks.push(chunk);
        };

        this.recorder.onStop = () => {
          this.completeRecording();
        };

        this.recorder.onError = (err) => {
          console.error('Recording error:', err);
          this.mediaError = `Recording error: ${err.message || 'Unknown error'}`;
          this.status = 'idle';
        };

        // Initialize recording state
        this.audioChunks = [];
        this.status = 'recording';
        this.recordingStartTime = Date.now();
        this.recordingDuration = 0;
        this.totalPausedDuration = 0;
        this.isPaused = false;

        // Set up interval to update recording duration
        this.recordingTimer = setInterval(() => {
          if (!this.isPaused) {
            this.recordingDuration = this.totalPausedDuration +
              (Date.now() - this.recordingStartTime) / 1000;

            // Auto-stop if max recording time is reached
            if (this.recordingDuration >= this.maxRecordingTime) {
              this.stopRecording();
            }
          }
        }, 1000);

      } catch (err) {
        console.error('Failed to start recording:', err);
        this.mediaError = `Failed to start recording: ${err.message || 'Could not access microphone'}`;
        this.status = 'idle';
      }
    },

    // Stop the current recording
    stopRecording() {
      if (this.status !== 'recording' || !this.recorder) return;

      try {
        // Stop the recorder
        this.recorder.stop();

        // Clear the recording timer
        if (this.recordingTimer) {
          clearInterval(this.recordingTimer);
          this.recordingTimer = null;
        }

        // The actual processing happens in completeRecording() which is called via the onStop callback
      } catch (err) {
        console.error('Error stopping recording:', err);
        this.mediaError = `Error stopping recording: ${err.message || 'Unknown error'}`;
        this.status = 'idle';
      }
    },

    // Process the completed recording
    completeRecording() {
      // If no audio chunks were recorded, show an error
      if (!this.audioChunks.length) {
        this.mediaError = 'No audio was recorded. Please try again.';
        this.status = 'idle';
        return;
      }

      try {
        // Create final audio blob from chunks
        const mimeType = this.recorder.mimeType || 'audio/webm';
        this.audioBlob = new Blob(this.audioChunks, { type: mimeType });

        // Create URL for the audio
        this.audioUrl = URL.createObjectURL(this.audioBlob);

        // Show the transcript review modal and start processing
        this.processRecording();
      } catch (err) {
        console.error('Error completing recording:', err);
        this.mediaError = `Error processing recording: ${err.message || 'Unknown error'}`;
        this.status = 'idle';
      }
    },

    // Set up audio playback elements
    setupAudioPlayer() {
      // Clean up any existing audio element first
      this.cleanupAudioPlayer();

      // Create new audio element
      this.audioElement = new Audio(this.audioUrl);

      // Set up audio event listeners
      this.audioElement.addEventListener('loadedmetadata', () => {
        this.audioDuration = this.audioElement.duration;
      });

      this.audioElement.addEventListener('timeupdate', () => {
        this.audioCurrentTime = this.audioElement.currentTime;
        this.audioProgress = (this.audioCurrentTime / this.audioDuration) * 100;
      });

      this.audioElement.addEventListener('ended', () => {
        this.isAudioPlaying = false;
      });

      // Start updating audio progress periodically
      this.audioUpdateTimer = setInterval(() => {
        if (this.isAudioPlaying) {
          this.audioCurrentTime = this.audioElement.currentTime;
          this.audioProgress = (this.audioCurrentTime / this.audioDuration) * 100;
        }
      }, 200);
    },

    // Clean up audio player resources
    cleanupAudioPlayer() {
      if (this.audioElement) {
        this.audioElement.pause();
        this.audioElement.src = '';
        this.audioElement.remove();
        this.audioElement = null;
      }

      if (this.audioUpdateTimer) {
        clearInterval(this.audioUpdateTimer);
        this.audioUpdateTimer = null;
      }

      this.isAudioPlaying = false;
      this.audioProgress = 0;
      this.audioCurrentTime = 0;
      this.audioDuration = 0;
    },

    // Close the transcript modal
    closeModal() {
      this.showTranscriptModal = false;
    },

    // Close the manual transcript modal
    closeManualModal() {
      this.showManualModal = false;
    },

    // Show the manual transcript dialog
    showManualTranscriptDialog() {
      this.showManualModal = true;
      this.transcript = '';
    },

    // Process a manually entered transcript
    async processManualTranscript() {
      if (!this.transcript || this.transcript.trim() === '') {
        return;
      }

      this.showManualModal = false;
      this.status = 'transcribed';

      // Create a fake diarized transcript based on text prefixes
      this.diarizedTranscript = [];
      const lines = this.transcript.split('\n').filter(line => line.trim() !== '');

      let speakerIndex = 0;
      let currentTime = 0;

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed.startsWith('Doctor:')) {
          speakerIndex = 0;
        } else if (trimmed.startsWith('Patient:')) {
          speakerIndex = 1;
        }

        // Extract text after the speaker prefix
        let text = trimmed;
        if (trimmed.includes(':')) {
          text = trimmed.substring(trimmed.indexOf(':') + 1).trim();
        }

        if (text) {
          this.diarizedTranscript.push({
            speaker: speakerIndex,
            text: text,
            start: currentTime,
            end: currentTime + 5, // Fake 5 second duration per utterance
            confidence: 0.95
          });

          currentTime += 5;
        }
      }

      // Show the transcript review modal
      this.showTranscriptModal = true;
    },

    // Process the recorded audio
    async processRecording() {
      if (!this.audioBlob) {
        this.mediaError = 'No audio was recorded';
        this.status = 'idle';
        return;
      }

      this.status = 'processing';
      this.processingStatus = 'Transcribing audio...';
      this.processingProgress = 10;

      try {
        // Set up audio player for preview
        this.setupAudioPlayer();

        // Convert Blob to base64 for API submission
        const reader = new FileReader();
        reader.readAsDataURL(this.audioBlob);

        reader.onloadend = async () => {
          const base64Audio = reader.result;

          this.processingProgress = 30;

          // Submit to transcription API
          try {
            const response = await post("ai_scribe_transcribe", {
              audio_file: base64Audio,
              encounter_id: this.encounterId
            });

            this.processingProgress = 80;

            if (response?.data?.status && response?.data?.data) {
              this.diarizedTranscript = response.data.data.diarized_transcript || [];
              this.transcript = response.data.data.full_transcript || '';

              this.status = 'transcribed';
              this.processingProgress = 100;

              // Show the transcript for review
              this.showTranscriptModal = true;
            } else {
              throw new Error(response?.data?.message || 'Transcription failed');
            }
          } catch (error) {
            console.error('Transcription error:', error);
            this.mediaError = `Transcription error: ${error.message || 'Service unavailable'}`;
            this.status = 'idle';
          }
        };

        reader.onerror = (error) => {
          console.error('Error reading audio file:', error);
          this.mediaError = 'Error reading audio file';
          this.status = 'idle';
        };
      } catch (error) {
        console.error('Error processing recording:', error);
        this.mediaError = `Error processing recording: ${error.message || 'Unknown error'}`;
        this.status = 'idle';
      }
    },

    // Handle file upload
    async handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Reset state
      this.mediaError = null;
      this.status = 'processing';
      this.processingStatus = 'Transcribing audio...';
      this.processingProgress = 10;

      try {
        // Create a blob from the file
        this.audioBlob = file;

        // Create URL for the audio and set up player
        this.audioUrl = URL.createObjectURL(this.audioBlob);
        this.setupAudioPlayer();

        // Check file size
        const fileSizeMB = file.size / (1024 * 1024);
        if (fileSizeMB > this.maxFileSizeMB) {
          throw new Error(`File too large (${fileSizeMB.toFixed(1)} MB). Maximum size is ${this.maxFileSizeMB} MB`);
        }

        this.processingProgress = 20;

        // Read file as base64
        const reader = new FileReader();
        reader.readAsDataURL(file);

        reader.onloadend = async () => {
          const base64Audio = reader.result;

          this.processingProgress = 40;

          try {
            // Submit to transcription API
            const response = await post("ai_scribe_transcribe", {
              audio_file: base64Audio,
              encounter_id: this.encounterId
            });

            this.processingProgress = 90;

            if (response?.data?.status && response?.data?.data) {
              this.diarizedTranscript = response.data.data.diarized_transcript || [];
              this.transcript = response.data.data.full_transcript || '';

              this.status = 'transcribed';
              this.processingProgress = 100;

              // Show the transcript for review
              this.showTranscriptModal = true;
            } else {
              throw new Error(response?.data?.message || 'Transcription failed');
            }
          } catch (error) {
            console.error('API Error:', error);
            this.mediaError = `Transcription error: ${error.message || 'Service unavailable'}`;
            this.status = 'idle';
          }
        };

        reader.onerror = (error) => {
          console.error('Error reading file:', error);
          this.mediaError = 'Error reading audio file';
          this.status = 'idle';
        };

      } catch (error) {
        console.error('File upload error:', error);
        this.mediaError = `Error processing file: ${error.message || 'Unknown error'}`;
        this.status = 'idle';
      }

      // Reset the file input so the same file can be selected again
      event.target.value = '';
    },

    // Analyze the transcript with AI
    async analyzeTranscript() {
      if (this.status !== 'transcribed' || !this.transcript) return;

      this.status = 'analyzing';
      this.processingStatus = 'Analyzing transcript...';
      this.processingProgress = 20;

      try {
        // Submit transcript for analysis
        const response = await post("ai_scribe_analyze", {
          transcript: this.transcript,
          encounter_id: this.encounterId
        });

        this.processingProgress = 80;

        if (!response?.data?.status || !response?.data?.data) {
          throw new Error(response?.data?.message || 'Analysis failed');
        }

        // Store the results
        this.analysisResults = response.data.data;
        this.status = 'analyzed';
        this.processingProgress = 100;

        // Close modal if user wants to go straight to "Use Analysis"
        this.showTranscriptModal = true;

      } catch (error) {
        console.error('Analysis error:', error);
        this.mediaError = `Analysis error: ${error.message || 'Failed to analyze transcript'}`;
        this.status = 'transcribed'; // Go back to transcribed state
      }
    },

    // Detect if we're in a Google Meet session
    detectGoogleMeet() {
      // Look for Google Meet in the URL
      this.isGoogleMeetActive = window.location.href.includes('meet.google.com');

      // Or check for Meet elements in the DOM
      if (!this.isGoogleMeetActive) {
        this.isGoogleMeetActive = document.querySelector('[data-meetingcode], .u6vdEc, .g3VIld') !== null;
      }

      return this.isGoogleMeetActive;
    },

    // Populate encounter tabs with analyzed data
    async populateRecords() {
      if (this.status !== 'analyzed') return;

      this.status = 'populating';
      this.processingStatus = 'Populating encounter tabs with AI analyzed data...';

      try {
        // Filter out empty transcript or analysis results
        if (!this.analysisResults) {
          throw new Error('No analysis results to populate');
        }

        // Populate the encounter tabs with the analyzed data
        const response = await post("ai_scribe_populate", {
          encounter_id: this.encounterId,
          data: JSON.stringify(this.analysisResults)
        });

        console.log('Population response:', response);

        if (!response || !response.data) {
          throw new Error('No response from server');
        }

        if (response.data && response.data.status) {
          try {
            displayMessage('Medical records populated by AI');
          } catch (e) {
            console.error('Failed to display success message:', e);
            alert('Medical records populated by AI');
          }

          this.status = 'completed';

          // After 3 seconds, reset to idle
          setTimeout(() => {
            this.status = 'idle';
          }, 3000);
        } else {
          throw new Error(response.data?.message || 'Failed to populate records');
        }
      } catch (error) {
        console.error('Error populating records:', error);
        this.mediaError = `Failed to populate records: ${error.message || 'Unknown error'}`;
        try {
          displayErrorMessage(this.mediaError);
        } catch (e) {
          console.error('Failed to display error message:', e);
          alert(this.mediaError);
        }
        this.status = 'analyzed'; // Go back to analyzed state
      }
    },

    // Show the mobile recording modal
    showMobileRecordingModal() {
      this.showMobileDialog = true;
      this.mobileRecordingError = null;
      this.mobileRecordingSuccess = null;

      // // If we already have a session, check its status
      // if (this.sessionId) {
      //   this.checkSessionStatus();
      // }
      this.createMobileRecordingSession();
    },

    // Create a new mobile recording session
    async createMobileRecordingSession() {
      this.isCreatingSession = true;
      this.mobileRecordingError = null;
      this.mobileRecordingSuccess = null;

      try {
        // Create a new recording session
        const response = await post("ai_scribe_create_mobile_recording", {
          encounter_id: this.encounterId,
          patient_id: this.patientId,
          expire_minutes: 30 // Session expires in 30 minutes
        });

        console.log('Mobile recording session response:', response);

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to create mobile recording session');
        }

        // Store session data
        const sessionData = response.data.data;
        this.sessionId = sessionData.session_id;
        this.qrCodeUrl = sessionData.qr_code_url;
        this.recordUrl = sessionData.record_url;

        // Start checking session status
        this.startSessionStatusCheck();

        this.mobileRecordingSuccess = 'QR code generated successfully. Scan with your mobile device to record audio.';
      } catch (error) {
        console.error('Error creating mobile recording session:', error);
        this.mobileRecordingError = `Error: ${error.message || 'Failed to create recording session'}`;
      } finally {
        this.isCreatingSession = false;
      }
    },

    // Start periodic checking of session status
    startSessionStatusCheck() {
      // Clear any existing timer
      if (this.sessionCheckTimer) {
        clearInterval(this.sessionCheckTimer);
      }

      // Set up status check every 10 seconds
      this.sessionCheckTimer = setInterval(() => {
        this.checkSessionStatus();
      }, 10000); // Check every 10 seconds
    },

    // Check the status of the mobile recording session
    async checkSessionStatus() {
      if (!this.sessionId) return;

      try {
        const response = await post("ai_scribe_check_mobile_recording", {
          session_id: this.sessionId
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to check session status');
        }

        // Update session status
        const statusData = response.data.data;
        this.sessionStatus = statusData;
        this.sessionTimeRemaining = this.formatTimeRemaining(statusData.time_remaining);
        this.sessionProcessed = statusData.processed;

        // If session has been processed and is ready for transcription
        if (statusData.processed && statusData.transcription_ready && statusData.document_id) {
          this.mobileRecordingSuccess = 'Audio recording has been uploaded! Click "Load Transcript" to continue.';

          // Stop checking status as we have everything we need
          if (this.sessionCheckTimer) {
            clearInterval(this.sessionCheckTimer);
          }
          
          console.log('Audio recording ready for transcription', statusData);
          
          // Store document ID for reference
          this.recordingDocumentId = statusData.document_id;
        } else if (statusData.processed && !statusData.transcription_ready) {
          // Processed but missing required data
          console.warn('Session processed but not ready for transcription', statusData);
          this.mobileRecordingError = 'Audio processed but not ready for transcription. Try again or contact support.';
        }
      } catch (error) {
        console.error('Error checking session status:', error);
        // Show a generic error message if checking fails repeatedly
        if (!this.mobileRecordingSuccess) {
          this.mobileRecordingError = 'Could not verify recording status. Try refreshing the page.';
        }
      }
    },

    // Format time remaining in human-readable format
    formatTimeRemaining(seconds) {
      if (!seconds) return 'Expired';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);

      if (minutes === 0) {
        return `${remainingSeconds} seconds`;
      }

      return `${minutes} minute${minutes !== 1 ? 's' : ''} ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
    },

    // Retrieve and load the transcript from a processed mobile recording
    async retrieveTranscript() {
      if (!this.sessionId) {
        this.mobileRecordingError = 'Missing session ID. Please try again.';
        return;
      }
      
      if (!this.sessionProcessed) {
        this.mobileRecordingError = 'Recording not yet processed. Please wait and try again.';
        return;
      }

      // Clear any existing errors
      this.mobileRecordingError = null;
      this.mediaError = null;
      
      try {
        // First get the session status to confirm it's ready
        this.mobileRecordingSuccess = 'Checking recording status...';
        
        const response = await post("ai_scribe_check_mobile_recording", {
          session_id: this.sessionId
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to retrieve session status');
        }

        // Check if the session is ready for transcription
        const sessionData = response.data.data;
        if (!sessionData.transcription_ready) {
          throw new Error('Audio recording not ready for transcription. Try again in a moment.');
        }
        
        if (!sessionData.document_id) {
          throw new Error('Document ID missing. Please try again or record a new audio file.');
        }

        // Close the mobile dialog and update UI
        this.showMobileDialog = false;
        this.status = 'processing';
        this.processingStatus = 'Processing mobile recording transcript...';
        this.processingProgress = 20;
        
        // Provide feedback
        try {
          displayMessage('Processing mobile recording...');
        } catch (e) {
          console.log('Processing mobile recording...');
        }

        // Short delay to ensure UI updates
        await new Promise(resolve => setTimeout(resolve, 500));
        this.processingProgress = 30;

        // Call the specific mobile recording processing endpoint
        console.log('Calling process endpoint with session ID:', this.sessionId);
        const transcriptResponse = await post("ai_scribe_process_mobile_recording", {
          session_id: this.sessionId,
          encounter_id: this.encounterId
        });

        this.processingProgress = 80;

        if (!transcriptResponse?.data?.status) {
          throw new Error(transcriptResponse?.data?.message || 'Failed to process mobile recording');
        }

        const transcriptData = transcriptResponse.data.data;
        if (!transcriptData || !transcriptData.diarized_transcript || !transcriptData.full_transcript) {
          throw new Error('Invalid transcript data received from server');
        }
        
        // Store the data
        this.diarizedTranscript = transcriptData.diarized_transcript;
        this.transcript = transcriptData.full_transcript;
        this.processingProgress = 100;

        // Show the transcript review modal
        this.showTranscriptModal = true;
        this.status = 'transcribed';
        
        try {
          displayMessage('Transcript processed successfully');
        } catch (e) {
          console.log('Transcript processed successfully');
        }
      } catch (error) {
        console.error('Error retrieving transcript:', error);
        this.mobileRecordingError = `Error: ${error.message || 'Failed to retrieve transcript'}`;
        this.status = 'idle';
        this.processingProgress = 0;
        
        try {
          displayErrorMessage(`Failed to process recording: ${error.message}`);
        } catch (e) {
          console.error('Failed to display error message');
        }
      }
    }
  }
};
</script>

<style scoped>
@keyframes wave-1 {

  0%,
  100% {
    height: 0.5rem;
  }

  50% {
    height: 1.5rem;
  }
}

@keyframes wave-2 {

  0%,
  100% {
    height: 0.75rem;
  }

  50% {
    height: 2rem;
  }
}

@keyframes wave-3 {

  0%,
  100% {
    height: 1rem;
  }

  50% {
    height: 1.75rem;
  }
}

@keyframes wave-4 {

  0%,
  100% {
    height: 0.5rem;
  }

  50% {
    height: 1.25rem;
  }
}

.animate-wave-1 {
  animation: wave-1 1s ease-in-out infinite;
}

.animate-wave-2 {
  animation: wave-2 1s ease-in-out infinite;
}

.animate-wave-3 {
  animation: wave-3 1s ease-in-out infinite;
}

.animate-wave-4 {
  animation: wave-4 1s ease-in-out infinite;
}
</style>