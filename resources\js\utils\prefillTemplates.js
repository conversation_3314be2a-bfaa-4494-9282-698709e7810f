export const prefillTemplates = {
  concerns: `* Main reason for consultation:
* Duration of symptoms:
* Severity:
* Associated symptoms:
* Aggravating factors:
* Relieving factors:`,

  history: `* Past consultations:
* Previous treatments:
* Response to treatments:
* Recent changes:
* Impact on daily activities:`,

  examination: `General Appearance:
* Level of distress:
* Consciousness:
* Position:

Vital Signs:
* BP:
* HR:
* RR:
* Temp:
* O2 Sat:`,

  safeguarding: `* Vulnerability assessment:
* Risk factors identified:
* Support systems in place:
* Previous safeguarding concerns:
* Actions taken:
* Follow-up plan:`,

  allergies: `* Known allergies:
* Type of reaction:
* Severity:
* Date identified:
* Management plan:`,

  family_history: `* Relevant family conditions:
* First-degree relatives:
* Age of onset:
* Current status:
* Genetic testing:`,

  medical_history: `* Chronic conditions:
* Previous surgeries:
* Hospitalizations:
* Significant illnesses:
* Current status:`,

  medications: `* Current medications:
* Dosage:
* Frequency:
* Start date:
* Side effects:
* Compliance:`,

  social_history: `* Living situation:
* Occupation:
* Lifestyle habits:
* Support system:
* Substance use:
* Exercise:`,

  mental_health: `* Current mental state:
* Previous diagnoses:
* Treatment history:
* Risk assessment:
* Support systems:
* Coping strategies:`,

  lifestyle: `* Diet:
* Exercise routine:
* Sleep pattern:
* Stress levels:
* Work-life balance:
* Habits/Dependencies:`,

  systems_review: `Cardiovascular:
* Chest pain:
* Palpitations:
* Dyspnea:

Respiratory:
* Cough:
* Wheezing:
* Sputum:

Gastrointestinal:
* Appetite:
* Bowel habits:
* Abdominal pain:`,

  safety_netting: `* Warning signs to watch for:
* When to seek urgent care:
* Follow-up plan:
* Contact information:
* Resources provided:`,

  preventative_care: `* Immunizations:
* Screenings due:
* Health promotion:
* Risk factors:
* Prevention strategies:`,

  notes: `* Key observations:
* Special considerations:
* Follow-up requirements:
* Communication needs:
* Care coordination:`
}