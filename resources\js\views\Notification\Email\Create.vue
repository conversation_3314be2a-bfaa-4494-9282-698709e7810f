<template>
  <div class="space-y-4">
    <form id="doctorDataForm" novalidate>
      <div class="bg-white shadow rounded-xl">
        <div class="flex justify-between items-center p-4 border-b">
          <h2 class="text-xl font-semibold">{{ formTranslation.settings.email_template }}
            <a v-if="request_status == 'off'" href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#email-template" target="_blank" class="text-blue-500 ml-2">
              <i class="fa fa-question-circle"></i>
            </a>
          </h2>
          <button type="button" @click="$refs.NotificationTestModal.modalOpen = true; $refs.NotificationTestModal.notificationType='email'" class="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded hover:bg-blue-600">
            <i class="fas fa-paper-plane"></i> {{formTranslation.widgets.send_test_email}}
          </button>
        </div>

        <div v-if="isLoading" class="p-4">
          <loader-component-2></loader-component-2>
        </div>

        <div v-else>
          <div v-for="(head, headIndex) in emailTypeList" :key="headIndex" class="border-b">
            <button type="button" @click="mainAccordian(headIndex)" :class="{'font-bold': selectedMainAccrodionId === headIndex}" class="w-full text-left p-4 bg-gray-50 hover:bg-gray-100">
              {{ labels[headIndex] || headIndex }}
            </button>

            <div v-if="selectedMainAccrodionId === headIndex" class="p-4 space-y-2">
              <div v-for="(item, index) in head" :key="index" class="border rounded-lg">
                <div class="flex items-center justify-between p-4 bg-gray-100">
                  <input type="checkbox" v-model="item.post_status" true-value="publish" false-value="draft" class="mr-2">
                  <button type="button" @click="selectedEmailTemplate(item.ID)" :class="{'font-bold': activeEmailTemplateIndex === item.ID}" class="text-left w-full">
                    {{ item.post_status === 'publish' ? formTranslation.common['enabled_' + item.post_name] : formTranslation.common['disabled_' + item.post_name] }}
                  </button>
                </div>

                <div v-if="item.ID === activeEmailTemplateIndex" class="p-4 space-y-2">
                  <label class="block font-medium">{{ formTranslation.settings.lbl_email_subject }}</label>
                  <input type="text" v-model="item.post_title" :placeholder="formTranslation.settings.lbl_email_subject" class="w-full p-2 border rounded">

                  <div v-if="emailDynamicKey[item.post_name]" class="space-y-1">
                    <label class="block font-medium">{{ formTranslation.settings.dynamic_keys_list }}</label>
                    <div class="flex flex-wrap gap-2">
                      <button type="button" v-for="(emailValue, emailIndex) in emailDynamicKey[item.post_name]" :key="emailIndex" @click.prevent="copyDynamicKey(emailValue)" class="px-2 py-1 text-sm text-white bg-blue-500 rounded hover:bg-blue-600">
                        {{ emailValue }}
                      </button>
                    </div>
                  </div>

                  <vue-editor :editorToolbar="customToolbar" v-model="item.post_content"></vue-editor>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end p-4">
          <button v-if="!loading" @click="saveEmailTemplates" type="button" class="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600">
            <i class="fa fa-save mr-1"></i> {{ formTranslation.common.save }}
          </button>
          <button v-else disabled type="button" class="px-4 py-2 text-white bg-blue-400 rounded">
            <i class="fa fa-sync fa-spin"></i> {{ formTranslation.common.loading }}
          </button>
        </div>
      </div>
    </form>
    <NotificationTestModal ref="NotificationTestModal"></NotificationTestModal>
  </div>
</template>

<script>
import {post,get} from "../../../config/request";
import NotificationTestModal from "../NotificationTestModal";
export default {
  components:{NotificationTestModal},
  data: () => {
    return {
      loading: false,
      request_status:'off',
      state: '',
      checked: '',
      activeTab: 0,
      emailTemplateTitle: 'Patient Registration Template',
      emailRequest: {
        to: '<EMAIL>',
        subject: 'Test Email',
        message: 'This is Test email'
      },
      emailTemplateSaveRequest: {
        content: '',
        type: '',
      },
      templateSaveRequest: {
        ID: 0,
        post_content: '',
      },
      emailTemplateGetRequest: {
        type: 'patient_register',
      },
      testEmailRequest: {
        content: ''
      },
      emailTypeList: [],
      activeEmailTemplateIndex: -1,
      selectedMainAccrodionId:-1,
      copyToolTipText:'',
      emailDynamicKey: [],
      isLoading:true,
      labels:[],
      customToolbar: [
        [{
          header: [false, 1, 2, 3, 4, 5, 6]
        }], ["bold", "italic", "underline", "strike"], // toggled buttons
        [{
          align: ""
        }, {
          align: "center"
        }, {
          align: "right"
        }, {
          align: "justify"
        }], ["blockquote", "code-block"], [{
          list: "ordered"
        }, {
          list: "bullet"
        }, {
          list: "check"
        }], [{
          indent: "-1"
        }, {
          indent: "+1"
        }], // outdent/indent
        [{
          color: []
        }, {
          background: []
        },
        ],
        ['clean'], // remove formatting button
        ['code-block'], // code block
      ]

    }
  },
  mounted() {
    if(!['administrator'].includes(this.getUserRole())) {
      this.$router.push({ name: "403"})
    }
    this.init();
    this.copyToolTipText = this.formTranslation.settings.click_to_copy;
    this.getModule();
  },
  methods: {
    init: function () {
      this.getEmailTemplate();
    },
    mainAccordian(value){
      this.selectedMainAccrodionId = this.selectedMainAccrodionId === value ? -1 : value;
    },
    saveEmailTemplates: function () {
      this.loading = true;
      post('save_email_template', { data : this.emailTypeList } )
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.getEmailTemplate();
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
          displayErrorMessage(this.formTranslation.common.server_error);
        })
    },
    getEmailTemplate: function () {
      get('get_email_template', {})
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.isLoading = false;
            let data = response.data.data
            this.emailTypeList = data;
            if(response.data.dynamicKey !== undefined ){
              this.emailDynamicKey = response.data.dynamicKey;
            }
            this.labels = response.data.labels;
          }
        })
        .catch((error) => {
          console.log(error);
        })
    },
    selectedEmailTemplate: function (value) {
      this.activeEmailTemplateIndex = this.activeEmailTemplateIndex === value ? ' ' : value;
    },
    onTemplateCheckBoxChecked: function (value) {
    },
    copyDynamicKey: function(id) {
      const elem = document.createElement("input");
      document.querySelector("body").appendChild(elem);
      elem.value = id;
      elem.select();
      document.execCommand("copy");
      elem.remove();
      this.copyToolTipText =  id + ' ' + this.formTranslation.settings.copied
    },
    getModule:function (){
        if(window.request_data.link_show_hide !== undefined && window.request_data.link_show_hide !== ''){
        this.request_status = window.request_data.link_show_hide;
        }
    }
  },
  watch: {}
}
</script>
<style scoped>
.accordion .card-header:has(button.not-collapsed):after {
  content: "\F077" !important;
}
</style>