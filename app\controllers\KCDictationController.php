<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCPatient;
use App\models\KCDictation;
use Exception;

class KCDictationController extends KCBase
{
    public $db;

    /**
     * @var KCRequest
     */
    private $request;

    public function __construct()
    {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        
        parent::__construct();
    }

    /**
     * Get dictation letters list
     */
    public function index()
    {
        try {
            // Check permission for dictation list
            if (!kcCheckPermission('dictation_list') && !kcCheckPermission('patient_encounter_list')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();

            // Default query conditions
            $search_condition = $patient_condition = $paginationCondition = "";
            $orderByCondition = " ORDER BY d.created_at DESC ";
            $dictation_table = $this->db->prefix . 'kc_dictations';
            $patient_table = $this->db->prefix . 'kc_patients';

            // Handle pagination
            if (isset($request_data['perPage']) && (int) $request_data['perPage'] > 0) {
                $perPage = (int) $request_data['perPage'];
                $offset = ((int) $request_data['page'] - 1) * $perPage;
                $paginationCondition = " LIMIT {$perPage} OFFSET {$offset} ";
            }
            
            // Handle search
            if (isset($request_data['searchTerm']) && trim($request_data['searchTerm']) !== '') {
                $searchTerm = esc_sql(strtolower(trim($request_data['searchTerm'])));
                $search_condition = " AND (
                    d.title LIKE '%{$searchTerm}%' OR 
                    d.content LIKE '%{$searchTerm}%' OR 
                    p.first_name LIKE '%{$searchTerm}%' OR 
                    p.last_name LIKE '%{$searchTerm}%' OR
                    CONCAT(p.first_name, ' ', p.last_name) LIKE '%{$searchTerm}%'
                ) ";
            }

            // Filter by patient if specified
            if (!empty($request_data['patient_id'])) {
                $patient_id = (int) $request_data['patient_id'];
                $patient_condition = " AND d.patient_id = {$patient_id} ";
            }

            // Role-specific conditions
            $dictations_query = "";
            
            switch ($current_login_user_role) {
                case 'administrator':
                    // Admin can see all dictations
                    $dictations_query = "SELECT d.*, 
                            CASE WHEN d.doctor_id > 0 THEN 
                                (SELECT display_name FROM {$this->db->users} WHERE ID = d.doctor_id) 
                                ELSE 'System' 
                            END as doctor_name,
                            CASE WHEN d.patient_id > 0 THEN 
                                CONCAT(p.first_name, ' ', p.last_name)
                                ELSE '' 
                            END as patient_name
                        FROM {$dictation_table} d
                        LEFT JOIN {$patient_table} p ON d.patient_id = p.id
                        WHERE d.status = 1 {$search_condition} {$patient_condition}
                        {$orderByCondition}";
                    break;
                    
                case $this->getClinicAdminRole():
                    // Clinic admin sees clinic dictations
                    $clinic_id = kcGetClinicIdOfClinicAdmin();

                    if (empty($clinic_id)) {
                        $dictations_query = "SELECT * FROM {$dictation_table} WHERE 1=0"; // Empty result
                    } else {
                        $dictations_query = "SELECT d.*, 
                                CASE WHEN d.doctor_id > 0 THEN 
                                    (SELECT display_name FROM {$this->db->users} WHERE ID = d.doctor_id) 
                                    ELSE 'System' 
                                END as doctor_name,
                                CASE WHEN d.patient_id > 0 THEN 
                                    CONCAT(p.first_name, ' ', p.last_name)
                                    ELSE '' 
                                END as patient_name
                            FROM {$dictation_table} d
                            LEFT JOIN {$patient_table} p ON d.patient_id = p.id
                            WHERE d.status = 1 AND d.clinic_id = {$clinic_id}
                            {$search_condition} {$patient_condition}
                            {$orderByCondition}";
                    }
                    break;
                    
                case $this->getDoctorRole():
                    // Doctor sees their own dictations
                    $dictations_query = "SELECT d.*, 
                            CASE WHEN d.doctor_id > 0 THEN 
                                (SELECT display_name FROM {$this->db->users} WHERE ID = d.doctor_id) 
                                ELSE 'System' 
                            END as doctor_name,
                            CASE WHEN d.patient_id > 0 THEN 
                                CONCAT(p.first_name, ' ', p.last_name)
                                ELSE '' 
                            END as patient_name
                        FROM {$dictation_table} d
                        LEFT JOIN {$patient_table} p ON d.patient_id = p.id
                        WHERE d.status = 1 AND d.doctor_id = {$user_id}
                        {$search_condition} {$patient_condition}
                        {$orderByCondition}";
                    break;
                    
                default:
                    // Other roles should not see dictations
                    $dictations_query = "SELECT * FROM {$dictation_table} WHERE 1=0"; // Empty result
                    break;
            }
            
            // Count total records for pagination
            $total = 0;
            if (!empty($dictations_query)) {
                $count_query = preg_replace('/SELECT.*?FROM/is', 'SELECT COUNT(*) FROM', $dictations_query);
                $count_query = preg_replace('/ORDER BY.*$/is', '', $count_query);
                $total = $this->db->get_var($count_query);
                
                // Add pagination to the main query
                $dictations_query .= $paginationCondition;
            }
            
            // Execute the query
            $dictations = $this->db->get_results($dictations_query);
            
            // Format data
            if (!empty($dictations)) {
                foreach ($dictations as $key => $dictation) {
                    // Format dates
                    $dictations[$key]->created_at_formatted = date('d M Y H:i', strtotime($dictation->created_at));
                    
                    // Check if current user is the owner
                    $dictations[$key]->is_owner = ($dictation->doctor_id == $user_id);
                    
                    // Truncate content for preview
                    $dictations[$key]->content_preview = mb_substr(strip_tags($dictation->content), 0, 100) . (mb_strlen(strip_tags($dictation->content)) > 100 ? '...' : '');
                }
            }
            
            // Send the response
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Dictations retrieved successfully', 'kc-lang'),
                'data' => $dictations,
                'total' => $total
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Save a dictation letter
     */
    public function save()
    {
        try {
            // Check permission for dictation save
            if (!kcCheckPermission('dictation_add') && !kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            
            // Validate required fields
            if (empty($request_data['title']) || empty($request_data['content'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Title and content are required', 'kc-lang')
                ]);
            }
            
            // Prepare dictation data
            $dictation_data = [
                'title' => sanitize_text_field($request_data['title']),
                'content' => wp_kses_post($request_data['content']),
                'doctor_id' => $user_id,
                'status' => 1
            ];
            
            // Set patient_id if provided
            if (!empty($request_data['patient_id'])) {
                $dictation_data['patient_id'] = (int) $request_data['patient_id'];
                
                // Verify patient exists
                $patient = (new KCPatient())->get_by(['id' => $dictation_data['patient_id']], '=', true);
                if (empty($patient)) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Patient not found', 'kc-lang')
                    ]);
                }
            } else {
                $dictation_data['patient_id'] = null;
            }
            
            // Set clinic_id based on user role
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                    $dictation_data['clinic_id'] = kcGetDefaultClinicId();
                    break;
                    
                case $this->getClinicAdminRole():
                    $dictation_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    break;
                    
                case 'administrator':
                    if (!empty($request_data['clinic_id'])) {
                        $dictation_data['clinic_id'] = (int) $request_data['clinic_id'];
                    } else {
                        $dictation_data['clinic_id'] = null;
                    }
                    break;
                    
                default:
                    $dictation_data['clinic_id'] = null;
                    break;
            }
            
            // Check if we're updating an existing dictation
            if (!empty($request_data['id'])) {
                $dictation_id = (int) $request_data['id'];
                
                // Get the existing dictation
                $existing_dictation = $this->db->get_row(
                    $this->db->prepare(
                        "SELECT * FROM {$this->db->prefix}kc_dictations WHERE id = %d",
                        $dictation_id
                    )
                );
                
                if (!$existing_dictation) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Dictation not found', 'kc-lang')
                    ]);
                }
                
                // Check if user has permission to edit this dictation
                $can_edit = false;
                
                if ($current_login_user_role === 'administrator') {
                    $can_edit = true;
                } else if ($current_login_user_role === $this->getClinicAdminRole() && 
                          $existing_dictation->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                    $can_edit = true;
                } else if ($existing_dictation->doctor_id == $user_id) {
                    $can_edit = true;
                }
                
                if (!$can_edit) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('You do not have permission to edit this dictation', 'kc-lang')
                    ]);
                }
                
                // Update the dictation
                $dictation_data['updated_at'] = current_time('Y-m-d H:i:s');
                
                $result = $this->db->update(
                    $this->db->prefix . 'kc_dictations',
                    $dictation_data,
                    ['id' => $dictation_id]
                );
                
                if ($result) {
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Dictation updated successfully', 'kc-lang'),
                        'data' => [
                            'id' => $dictation_id
                        ]
                    ]);
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to update dictation', 'kc-lang')
                    ]);
                }
            } else {
                // Insert new dictation
                $dictation_data['created_at'] = current_time('Y-m-d H:i:s');
                
                $result = $this->db->insert(
                    $this->db->prefix . 'kc_dictations',
                    $dictation_data
                );
                
                if ($result) {
                    $dictation_id = $this->db->insert_id;
                    
                    // Log activity
                    kcLogActivity(
                        'dictation_created',
                        sprintf(esc_html__('Dictation created: %s', 'kc-lang'), $dictation_data['title']),
                        [
                            'patient_id' => $dictation_data['patient_id'],
                            'resource_id' => $dictation_id,
                            'resource_type' => 'dictation',
                            'clinic_id' => $dictation_data['clinic_id']
                        ]
                    );
                    
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Dictation saved successfully', 'kc-lang'),
                        'data' => [
                            'id' => $dictation_id
                        ]
                    ]);
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to save dictation', 'kc-lang')
                    ]);
                }
            }
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get details of a single dictation
     */
    public function getDetails()
    {
        try {
            // Check permission for dictation details
            if (!kcCheckPermission('dictation_view') && !kcCheckPermission('patient_encounter_view')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Dictation ID is required', 'kc-lang')
                ]);
            }
            
            $dictation_id = (int) $request_data['id'];
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            
            // Get dictation with associated data
            $dictation = $this->db->get_row(
                $this->db->prepare(
                    "SELECT d.*, 
                        CASE WHEN d.doctor_id > 0 THEN 
                            (SELECT display_name FROM {$this->db->users} WHERE ID = d.doctor_id) 
                            ELSE 'System' 
                        END as doctor_name,
                        CASE WHEN d.patient_id > 0 THEN 
                            CONCAT(p.first_name, ' ', p.last_name)
                            ELSE '' 
                        END as patient_name,
                        CASE WHEN d.clinic_id > 0 THEN 
                            c.name
                            ELSE '' 
                        END as clinic_name
                    FROM {$this->db->prefix}kc_dictations d
                    LEFT JOIN {$this->db->prefix}kc_patients p ON d.patient_id = p.id
                    LEFT JOIN {$this->db->prefix}kc_clinics c ON d.clinic_id = c.id
                    WHERE d.id = %d AND d.status = 1",
                    $dictation_id
                )
            );
            
            if (!$dictation) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Dictation not found', 'kc-lang')
                ]);
            }
            
            // Check if user has access to this dictation
            $has_access = false;
            
            if ($current_login_user_role === 'administrator') {
                $has_access = true;
            } else if ($current_login_user_role === $this->getClinicAdminRole() && 
                      $dictation->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $has_access = true;
            } else if ($dictation->doctor_id == $user_id) {
                $has_access = true;
            }
            
            if (!$has_access) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have access to this dictation', 'kc-lang')
                ]);
            }
            
            // Format dates
            $dictation->created_at_formatted = date('d M Y H:i', strtotime($dictation->created_at));
            if (!empty($dictation->updated_at)) {
                $dictation->updated_at_formatted = date('d M Y H:i', strtotime($dictation->updated_at));
            }
            
            // Check if current user is the owner
            $dictation->is_owner = ($dictation->doctor_id == $user_id);
            
            // Get patient details if available
            if (!empty($dictation->patient_id)) {
                $patient = (new KCPatient())->get_by(['id' => $dictation->patient_id], '=', true);
                if ($patient) {
                    $dictation->patient_details = $patient;
                }
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Dictation details retrieved successfully', 'kc-lang'),
                'data' => $dictation
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Delete a dictation (soft delete by setting status = 0)
     */
    public function delete()
    {
        try {
            // Check permission for dictation delete
            if (!kcCheckPermission('dictation_delete') && !kcCheckPermission('patient_encounter_delete')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Dictation ID is required', 'kc-lang')
                ]);
            }
            
            $dictation_id = (int) $request_data['id'];
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            
            // Get the dictation
            $dictation = $this->db->get_row(
                $this->db->prepare(
                    "SELECT * FROM {$this->db->prefix}kc_dictations WHERE id = %d",
                    $dictation_id
                )
            );
            
            if (!$dictation) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Dictation not found', 'kc-lang')
                ]);
            }
            
            // Check if user has permission to delete this dictation
            $can_delete = false;
            
            if ($current_login_user_role === 'administrator') {
                $can_delete = true;
            } else if ($current_login_user_role === $this->getClinicAdminRole() && 
                      $dictation->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $can_delete = true;
            } else if ($dictation->doctor_id == $user_id) {
                $can_delete = true;
            }
            
            if (!$can_delete) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to delete this dictation', 'kc-lang')
                ]);
            }
            
            // Soft delete the dictation
            $result = $this->db->update(
                $this->db->prefix . 'kc_dictations',
                ['status' => 0],
                ['id' => $dictation_id]
            );
            
            if ($result) {
                // Log activity
                kcLogActivity(
                    'dictation_deleted',
                    sprintf(esc_html__('Dictation deleted: %s', 'kc-lang'), $dictation->title),
                    [
                        'patient_id' => $dictation->patient_id,
                        'resource_id' => $dictation_id,
                        'resource_type' => 'dictation',
                        'clinic_id' => $dictation->clinic_id
                    ]
                );
                
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Dictation deleted successfully', 'kc-lang')
                ]);
            } else {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to delete dictation', 'kc-lang')
                ]);
            }
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Process audio dictation using Deepgram and save as a letter
     */
    public function processAudioDictation()
    {
        try {
            // Check permission
            if (!kcCheckPermission('dictation_add') && !kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate request
            if (!isset($request_data['audio_file']) || empty($request_data['audio_file'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Audio file is required', 'kc-lang')
                ]);
            }
            
            // Get base64 encoded audio data
            $audio_data = $request_data['audio_file'];
            
            // Decode base64 to binary
            $decoded_audio = base64_decode(preg_replace('#^data:audio/\w+;base64,#i', '', $audio_data));
            
            // Debug info
            error_log('Dictation: Audio file size: ' . strlen($decoded_audio) . ' bytes');
            
            // Create a temporary file
            $temp_file = wp_tempnam('dictation_audio_');
            file_put_contents($temp_file, $decoded_audio);
            
            // Check if DEEPGRAM_API_KEY is defined
            if (!defined('DEEPGRAM_API_KEY') || empty(DEEPGRAM_API_KEY)) {
                error_log('DEEPGRAM_API_KEY not defined or empty in wp-config.php');
                throw new Exception("Deepgram API key is not configured. Please contact your administrator.");
            }
            
            // Prepare request to Deepgram API
            $curl = curl_init();
            
            // Build Deepgram URL with parameters for Nova 3 model
            $deepgram_url = 'https://api.deepgram.com/v1/listen?' . implode('&', [
                'model=nova-3',
                'smart_format=true',
                'diarize=true',
                'punctuate=true',
                'language=en-US'
            ]);
            
            // Set cURL options
            curl_setopt($curl, CURLOPT_URL, $deepgram_url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, file_get_contents($temp_file));
            curl_setopt($curl, CURLOPT_HTTPHEADER, [
                'Authorization: Token ' . DEEPGRAM_API_KEY,
                'Content-Type: audio/wav'
            ]);
            curl_setopt($curl, CURLOPT_TIMEOUT, 300); // 5 minute timeout
            
            $response = curl_exec($curl);
            $err = curl_error($curl);
            $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            
            curl_close($curl);
            unlink($temp_file); // Clean up temp file
            
            if ($err) {
                error_log('Dictation: cURL Error: ' . $err);
                throw new Exception("cURL Error: " . $err);
            }
            
            // Handle API errors
            if ($http_code !== 200) {
                error_log('Dictation: API Error: HTTP ' . $http_code . ': ' . $response);
                throw new Exception("API Error: HTTP " . $http_code);
            }
            
            $transcription_result = json_decode($response, true);
            
            // Handle JSON decode errors
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('Dictation: Error decoding response: ' . json_last_error_msg());
                throw new Exception("Error decoding API response: " . json_last_error_msg());
            }
            
            // Check for results in the response
            if (!isset($transcription_result['results'])) {
                error_log('Dictation: No results in response: ' . json_encode($transcription_result));
                throw new Exception("Failed to transcribe audio");
            }
            
            // Get the transcribed text (full transcript)
            $full_transcript = '';
            
            if (isset($transcription_result['results']['channels'][0]['alternatives'][0]['transcript'])) {
                $full_transcript = $transcription_result['results']['channels'][0]['alternatives'][0]['transcript'];
            } else {
                // Try to extract from utterances if available
                if (isset($transcription_result['results']['utterances'])) {
                    foreach ($transcription_result['results']['utterances'] as $utterance) {
                        $full_transcript .= $utterance['transcript'] . ' ';
                    }
                }
            }
            
            // Return success response with transcribed text
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Audio transcribed successfully', 'kc-lang'),
                'data' => [
                    'full_transcript' => trim($full_transcript)
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Register dictation database table
     */
    public static function createDictationTable()
    {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        $table_name = $wpdb->prefix . 'kc_dictations';
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            content longtext NOT NULL,
            doctor_id bigint(20) NOT NULL,
            patient_id bigint(20) DEFAULT NULL,
            clinic_id bigint(20) DEFAULT NULL,
            status tinyint(1) NOT NULL DEFAULT 1,
            created_at datetime NOT NULL,
            updated_at datetime DEFAULT NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}