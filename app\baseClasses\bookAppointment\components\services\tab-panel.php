<div class="p-6">
    <!-- Header with title and search -->
    <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
        <h3 class="text-xl font-semibold text-gray-900">Select Service</h3>
        <div class="relative w-full">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input type="text" id="serviceSearch" placeholder="Search services..."
             style="padding-left: 2.5rem;"
                class="pl-10 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:outline-none text-sm w-full" />
        </div>
    </div>

    <!-- Service type filters -->
    <div class="md:flex space-x-2 mb-6">
        <button type="button" class="filter-button px-4 py-2 rounded-md text-sm font-medium bg-indigo-600 text-white"
            data-filter="all">All</button>
        <button type="button"
            class="filter-button px-4 py-2 rounded-md text-sm font-medium transition-colors inline-flex items-center bg-gray-100 text-gray-600 hover:bg-gray-200"
            data-filter="virtual">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-video w-4 h-4 mr-2">
                <path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path>
                <rect x="2" y="6" width="14" height="12" rx="2"></rect>
            </svg>
            Virtual
        </button>
        <button type="button"
            class="filter-button px-4 py-2 rounded-md text-sm font-medium transition-colors inline-flex items-center bg-gray-100 text-gray-600 hover:bg-gray-200"
            data-filter="clinic">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-users w-4 h-4 mr-2">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
            In-Clinic
        </button>
    </div>

    <!-- Selected category display -->
    <div id="selectedCategoryDisplay" class="mb-4 bg-gray-50 p-3 rounded-lg border border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <span class="text-sm text-gray-500">Selected Category:</span>
                <span id="displayCategoryName" class="ml-2 font-medium text-indigo-600">Loading...</span>
            </div>
            <button id="changeCategoryBtn" type="button" class="text-sm text-indigo-600 hover:text-indigo-800">
                Change
            </button>
        </div>
    </div>

    <!-- Service list -->
    <div id="serviceLists" class="space-y-4">
        <span class="loader-class hidden">
            <!-- Loader placeholder -->
            <div class="flex justify-center items-center py-6">
                <div class="double-lines-spinner"></div>
            </div>
        </span>

        <!-- Services will be loaded dynamically -->
    </div>
</div>

<script>
    // Get the filter buttons and service list container
    const filterButtons = document.querySelectorAll('.filter-button');
    const serviceList = document.getElementById('serviceLists');
    const selectedCategory = document.getElementById('selected_category');
    const displayCategoryName = document.getElementById('displayCategoryName');
    
    // Load services for the selected category when the tab is shown
    function loadServicesForCategory() {
        if (selectedCategory) {
            const categoryValue = selectedCategory.value;
            const formattedCategoryName = categoryValue.replace(/_/g, ' ');
            
            // Show the selected category name
            if (displayCategoryName) {
                displayCategoryName.textContent = formattedCategoryName;
            }
            
            // Show loading state
            serviceList.innerHTML = '<div class="flex justify-center items-center py-6"><div class="double-lines-spinner"></div></div>';
            
            // In a real implementation, AJAX would be used here to load services from the server
            // For now, we're relying on the existing getClinicServices call on the backend
        }
    }
    
    // When the tab becomes visible, load services
    const servicesTabObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'class' && 
                !document.getElementById('services').classList.contains('hidden')) {
                loadServicesForCategory();
            }
        });
    });
    
    // Start observing the services tab for visibility changes
    if (document.getElementById('services')) {
        servicesTabObserver.observe(document.getElementById('services'), {
            attributes: true
        });
    }
    
    // Change Category button handler
    document.getElementById('changeCategoryBtn').addEventListener('click', function() {
        // Navigate back to the category tab
        const categoryTab = document.querySelector('[href="#category"]');
        if (categoryTab) {
            categoryTab.click();
        }
    });
    
    // Service search functionality
    document.getElementById('serviceSearch').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const serviceItems = serviceList.querySelectorAll('.service-item');
        
        serviceItems.forEach(item => {
            const serviceName = item.querySelector('h3').textContent.toLowerCase();
            const serviceDesc = item.querySelector('.text-sm')?.textContent.toLowerCase() || '';
            
            if (serviceName.includes(searchTerm) || serviceDesc.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Add click event listeners to the filter buttons
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filterType = button.getAttribute('data-filter');

            // Remove 'active' class from all buttons
            filterButtons.forEach(btn => {
                btn.classList.remove('bg-indigo-600', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            // Add 'active' class to the clicked button
            button.classList.remove('bg-gray-100', 'text-gray-600');
            button.classList.add('bg-indigo-600', 'text-white');

            // Filter the service items based on the selected type
            filterServices(filterType);
        });
    });

    // Function to filter the service items
    function filterServices(type) {
        const serviceItems = serviceList.querySelectorAll('.service-item');

        serviceItems.forEach(item => {
            if (type === 'all') {
                item.style.display = 'block';
            } else if (type === 'virtual' && item.classList.contains('virtual')) {
                item.style.display = 'block';
            } else if (type === 'clinic' && item.classList.contains('clinic')) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    // When multiple services can be selected
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('service-checkbox')) {
            const selectedServices = [];
            const checkboxes = document.querySelectorAll('.service-checkbox:checked');
            
            checkboxes.forEach(checkbox => {
                selectedServices.push({
                    id: checkbox.value,
                    service_id: checkbox.getAttribute('service_id'),
                    name: checkbox.getAttribute('service_name'),
                    charges: checkbox.getAttribute('service_price'),
                    doctor_id: checkbox.getAttribute('doctor_id'),
                    clinic_id: checkbox.getAttribute('clinic_id')
                });
            });
            
            // Store selected services in a hidden field
            const hiddenServicesField = document.getElementById('selected_services');
            if (!hiddenServicesField) {
                const hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.id = 'selected_services';
                hiddenField.name = 'selected_services';
                hiddenField.value = JSON.stringify(selectedServices);
                document.getElementById('services').appendChild(hiddenField);
            } else {
                hiddenServicesField.value = JSON.stringify(selectedServices);
            }
            
            // Update visual styling for the selected item
            const serviceItems = document.querySelectorAll('.service-item label');
            serviceItems.forEach(label => {
                const checkbox = document.getElementById(label.getAttribute('for'));
                if (checkbox && checkbox.checked) {
                    label.classList.remove('border-gray-200', 'hover:border-purple-200');
                    label.classList.add('border-purple-500', 'bg-purple-50');
                } else {
                    label.classList.add('border-gray-200', 'hover:border-purple-200');
                    label.classList.remove('border-purple-500', 'bg-purple-50');
                }
            });
        }
    });
</script>