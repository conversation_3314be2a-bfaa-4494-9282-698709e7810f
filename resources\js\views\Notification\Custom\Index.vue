<template>
  <div class="grid grid-cols-1">
    <div class="col-span-1">
      <!-- Display overlay only when userModuleData.addOns.kiviPro is not true -->
      <div class="relative" v-if="!userModuleData.addOns.kiviPro">
        <div
          class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"
        >
          <overlay-message addon_type="pro"></overlay-message>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-lg">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold">
              {{ formTranslation.common.custom_notification }}
              <a
                v-if="requestStatus == 'off'"
                href="https://apps.medroid.ai/docs/product/kivicare/pro-version/custom-notification/"
                target="_blank"
                class="ml-2 text-gray-500 hover:text-gray-700"
              >
                <i class="fa fa-question-circle"></i>
              </a>
            </h2>
          </div>
        </div>

        <!-- Main Content -->
        <div id="api-builder" class="p-6">
          <div class="w-full">
            <!-- Tabs -->
            <div class="border-b border-gray-200">
              <nav class="flex -mb-px">
                <button
                  @click="viewMode = 1"
                  :class="[
                    viewMode === 1
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                    'px-4 py-2 font-medium text-sm border-b-2',
                  ]"
                >
                  {{ formTranslation.common.form }}
                </button>
                <button
                  @click="viewMode = 2"
                  :class="[
                    viewMode === 2
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                    'px-4 py-2 font-medium text-sm border-b-2',
                  ]"
                >
                  {{ formTranslation.common.response }}
                </button>
                <button
                  @click="viewMode = 3"
                  :class="[
                    viewMode === 3
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                    'px-4 py-2 font-medium text-sm border-b-2',
                  ]"
                >
                  {{ formTranslation.common.dynamic_keys }}
                </button>
                <button
                  @click="viewMode = 4"
                  :class="[
                    viewMode === 4
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                    'px-4 py-2 font-medium text-sm border-b-2',
                  ]"
                >
                  {{ formTranslation.common.collections }}
                </button>
              </nav>
            </div>
            <!-- Form Tab Content -->
            <div v-show="viewMode === 1" class="mt-4">
              <form @submit.prevent="executeApiRequest">
                <!-- URL and Method selection -->
                <div class="mb-4">
                  <label
                    class="block text-sm font-medium text-gray-700"
                    for="url"
                  >
                    {{ formTranslation.common.url
                    }}<span class="text-red-500">*</span>
                  </label>
                  <div class="mt-1 flex rounded-md shadow-sm">
                    <select
                      v-model="apiRequest.method"
                      class="inline-flex items-center px-3 py-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm"
                    >
                      <option value="GET">
                        {{ formTranslation.common.get }}
                      </option>
                      <option value="POST">
                        {{ formTranslation.common.post }}
                      </option>
                    </select>
                    <input
                      v-model="apiRequest.url"
                      type="url"
                      class="flex-1 min-w-0 block w-full px-3 py-2 rounded-r-md border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      autocomplete="on"
                    />
                  </div>
                  <div
                    v-if="isTestSubmitted && !$v.apiRequest.url.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{
                      formTranslation.common.url +
                      " " +
                      formTranslation.common.required
                    }}
                  </div>
                  <div
                    v-if="isTestSubmitted && !$v.apiRequest.method.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{
                      formTranslation.Method +
                      " " +
                      formTranslation.common.required
                    }}
                  </div>
                </div>

                <!-- Header data input -->
                <div class="mb-4">
                  <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                      {{ formTranslation.common.headers }}
                    </label>
                    <button
                      type="button"
                      v-if="!apiRequest.headers.length"
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      @click="addHeaderData"
                    >
                      {{ formTranslation.common.add_header }}
                    </button>
                  </div>

                  <div
                    v-for="(header, index) in apiRequest.headers"
                    :key="index"
                    class="flex gap-2 mb-2"
                  >
                    <input
                      v-model="header.key"
                      type="text"
                      :placeholder="formTranslation.common.enter_key"
                      class="flex-1 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm px-3 py-2"
                    />
                    <input
                      list="header-list-suggestion"
                      v-model="header.value"
                      type="text"
                      :placeholder="formTranslation.common.enter_value"
                      class="flex-1 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm px-3 py-2"
                    />
                    <datalist id="header-list-suggestion">
                      <option
                        v-for="(dynamicKey, key) in dynamicKeys"
                        :value="dynamicKey.key"
                        :key="key"
                      >
                        {{ dynamicKey.key }}
                      </option>
                    </datalist>
                    <button
                      type="button"
                      class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      @click="removeHeaderData(index)"
                    >
                      {{ formTranslation.common.remove }}
                    </button>
                  </div>

                  <button
                    type="button"
                    v-if="apiRequest.headers.length"
                    class="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    @click="addHeaderData"
                  >
                    {{ formTranslation.common.add_header }}
                  </button>
                </div>
                <!-- Query parameters input -->
                <div v-if="apiRequest.method === 'GET'" class="mb-4">
                  <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                      {{ formTranslation.common.query_parameters }}
                    </label>
                    <button
                      type="button"
                      v-if="!queryParams"
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      @click="addQueryParam"
                    >
                      {{ formTranslation.common.add_query_parameter }}
                    </button>
                  </div>

                  <div
                    v-for="(param, index) in queryParams"
                    :key="index"
                    class="flex gap-2 mb-2"
                  >
                    <input
                      v-model="param.key"
                      type="text"
                      :placeholder="formTranslation.common.enter_key"
                      class="flex-1 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm px-3 py-2"
                    />
                    <input
                      v-model="param.value"
                      list="query-list-suggestion"
                      type="text"
                      :placeholder="formTranslation.common.enter_value"
                      class="flex-1 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm px-3 py-2"
                    />
                    <datalist id="query-list-suggestion">
                      <option
                        v-for="(dynamicKey, key) in dynamicKeys"
                        :value="dynamicKey.key"
                        :key="key"
                      >
                        {{ dynamicKey.key }}
                      </option>
                    </datalist>
                    <button
                      type="button"
                      class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      @click="removeQueryParam(index)"
                    >
                      {{ formTranslation.common.remove }}
                    </button>
                  </div>

                  <button
                    type="button"
                    v-if="queryParams"
                    class="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    @click="addQueryParam"
                  >
                    {{ formTranslation.common.add_query_parameter }}
                  </button>
                </div>

                <!-- Body input for non-GET requests -->
                <div v-else class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-1"
                    >Body</label
                  >
                  <textarea
                    v-model="apiRequest.body"
                    rows="5"
                    class="w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm px-3 py-2"
                  >
                  </textarea>
                </div>

                <!-- Action buttons -->
                <div class="flex justify-end gap-2">
                  <button
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    @click="clearForm"
                  >
                    {{ formTranslation.common.clear }}
                  </button>
                  <button
                    type="submit"
                    v-if="isLoading"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 opacity-50 cursor-not-allowed"
                    disabled
                  >
                    {{ formTranslation.common.loading }}
                  </button>
                  <button
                    type="submit"
                    v-else
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {{ formTranslation.common.send_request }}
                  </button>
                  <button
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    @click="saveApiList(true)"
                  >
                    {{ formTranslation.common.save }}
                  </button>
                </div>
              </form>

              <!-- Modal Component -->
              <div
                v-if="isModalOpen"
                class="fixed inset-0 z-50 overflow-y-auto"
                aria-labelledby="modal-title"
                role="dialog"
                aria-modal="true"
              >
                <div
                  class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
                >
                  <!-- Background overlay -->
                  <div
                    class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                    aria-hidden="true"
                  ></div>

                  <!-- Modal panel -->
                  <div
                    class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
                  >
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                      <div class="sm:flex sm:items-start">
                        <div
                          class="mt-3 text-center sm:mt-0 sm:text-left w-full"
                        >
                          <h3
                            class="text-lg leading-6 font-medium text-gray-900"
                            id="modal-title"
                          >
                            {{
                              formTranslation.common.save_custom_notification
                            }}
                          </h3>
                          <div class="mt-4 space-y-4">
                            <!-- Name Input -->
                            <div>
                              <label
                                for="name"
                                class="block text-sm font-medium text-gray-700"
                              >
                                {{ formTranslation.common.name }}
                              </label>
                              <input
                                type="text"
                                id="name"
                                v-model="modalData.name"
                                :placeholder="formTranslation.common.enter_name"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              />
                            </div>
                            <!-- Notification Type Select -->
                            <div>
                              <label
                                for="type"
                                class="block text-sm font-medium text-gray-700"
                              >
                                {{ formTranslation.common.notification_type }}
                              </label>
                              <select
                                id="type"
                                v-model="modalData.type"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              >
                                <option value="sms">
                                  {{ formTranslation.common.sms }}
                                </option>
                                <option value="whatsapp">
                                  {{ formTranslation.common.whatsapp }}
                                </option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- Modal footer -->
                    <div
                      class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"
                    >
                      <button
                        type="button"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                        @click="saveApiList(false)"
                        :disabled="modalLoading"
                      >
                        {{
                          modalLoading
                            ? formTranslation.common.loading
                            : formTranslation.common.save
                        }}
                      </button>
                      <button
                        type="button"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        @click="isModalOpen = false"
                      >
                        {{ formTranslation.common.cancel }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Response Tab Content -->
              <div v-show="viewMode === 2" class="mt-4">
                <!-- Status Code Alert -->
                <div
                  v-if="showStatusCodeAlert"
                  :class="[
                    'rounded-md p-4',
                    responseClass === 'alert-success'
                      ? 'bg-green-50 text-green-700'
                      : 'bg-red-50 text-red-700',
                  ]"
                >
                  <p class="text-sm">
                    {{ formTranslation.common.status_code }}:
                    {{ apiResponse ? apiResponse.status : "" }}
                  </p>
                </div>

                <!-- Response Content -->
                <div class="mt-4 max-h-96 overflow-y-auto">
                  <pre
                    class="bg-gray-50 p-4 rounded-lg whitespace-pre-wrap break-words"
                    >{{ apiResponse }}</pre
                  >
                </div>
              </div>
              <!-- Dynamic Keys Tab Content -->
              <div v-show="viewMode === 3" class="mt-4">
                <form @submit.prevent="saveDynamicKeys">
                  <div class="space-y-4">
                    <div class="grid grid-cols-12 gap-4">
                      <div class="col-span-5">
                        <label class="block text-sm font-medium text-gray-700">
                          {{ formTranslation.common.key }}
                        </label>
                      </div>
                      <div class="col-span-5">
                        <label class="block text-sm font-medium text-gray-700">
                          {{ formTranslation.common.value }}
                        </label>
                      </div>
                      <div class="col-span-2">
                        <label class="block text-sm font-medium text-gray-700">
                          {{ formTranslation.common.action }}
                        </label>
                      </div>
                    </div>

                    <hr class="border-gray-200" />

                    <div
                      v-for="(dynamicKey, index) in dynamicKeys"
                      :key="index"
                      class="grid grid-cols-12 gap-4"
                    >
                      <div class="col-span-5">
                        <input
                          v-model="dynamicKey.key"
                          type="text"
                          :placeholder="formTranslation.common.enter_key"
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>
                      <div class="col-span-5">
                        <input
                          list="dynamic-key-list-suggestion"
                          v-model="dynamicKey.value"
                          type="text"
                          :placeholder="formTranslation.common.enter_value"
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                        <datalist id="dynamic-key-list-suggestion">
                          <option
                            v-for="(value, key) in [
                              '[dynamic_reciever_number]',
                              '[dynamic_sms_body]',
                            ]"
                            :key="key"
                            :value="value"
                          >
                            {{ value }}
                          </option>
                        </datalist>
                      </div>
                      <div class="col-span-2">
                        <button
                          type="button"
                          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          @click="removeDynamicKey(index)"
                        >
                          {{ formTranslation.common.remove }}
                        </button>
                      </div>
                    </div>

                    <div class="flex gap-2">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        @click="addDynamicKey"
                      >
                        {{ formTranslation.common.add_dynamic_key }}
                      </button>
                      <button
                        v-if="dynamicKeys.length"
                        type="submit"
                        :disabled="dynamicsKeysloading"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {{
                          dynamicsKeysloading
                            ? formTranslation.common.loading
                            : "Save dynamic keys"
                        }}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
              <!-- Collections Tab Content -->
              <div v-show="viewMode === 4" class="mt-4">
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.common.id }}
                        </th>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.common.name }}
                        </th>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.doctor.type }}
                        </th>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.common.action }}
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr
                        v-for="(collection, key) in collections"
                        :key="collection.id"
                      >
                        <td
                          class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                        >
                          {{ key + 1 }}
                        </td>
                        <td
                          class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                        >
                          {{ collection.name ? collection.name : "-" }}
                        </td>
                        <td
                          class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                        >
                          {{
                            collection.type === "sms"
                              ? formTranslation.common.sms
                              : formTranslation.common.whatsapp
                          }}
                        </td>
                        <td
                          class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2"
                        >
                          <button
                            class="text-blue-600 hover:text-blue-900"
                            @click="editApiData(collection, key)"
                          >
                            {{ formTranslation.clinic_schedule.dt_lbl_edit }}
                          </button>
                          <button
                            class="text-red-600 hover:text-red-900"
                            @click="deleteApiData(key)"
                          >
                            {{ formTranslation.clinic_schedule.dt_lbl_dlt }}
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import ModalPopup from "../../../components/Modal/Index";
import { get, post } from "../../../config/request";
import { required } from "vuelidate/lib/validators";

export default {
  components: {
    ModalPopup,
  },
  data() {
    return {
      apiRequest: {
        method: "GET",
        url: "",
        body: JSON.stringify({ apikey: "" }),
        headers: [{ key: "Content-Type", value: "application/json" }],
      },
      apiResponse: null,
      showStatusCodeAlert: false,
      isModalOpen: false,
      isTestSubmitted: false,
      isLoading: false,
      viewMode: 1,
      queryParams: [],
      requestStatus: "off",
      isDynamicKeyTabActive: false,
      dynamicKeys: [],
      dynamicsKeysloading: false,
      collections: [],
      modalLoading: false,
      modalData: {
        name: "",
        type: "sms",
      },
      iseditIndex: "",
    };
  },
  validations: {
    apiRequest: {
      method: { required },
      url: { required },
    },
  },
  mounted() {
    this.initializeModule();
    this.getDynamicKeys();
    this.getApiList();
  },

  methods: {
    async executeApiRequest() {
      try {
        this.isTestSubmitted = true;
        this.isLoading = true;
        this.$v.$touch();

        if (this.$v.apiRequest.$invalid) {
          this.isLoading = false;
          return;
        }

        this.isTestSubmitted = false;
        this.sendRequest();
        // const requestOptions = {
        //   method: this.apiRequest.method,
        //   url: this.replaceDynamicKeys(this.apiRequest.url)
        // };

        // if (this.apiRequest.headers && this.apiRequest.headers.length > 0) {
        //   requestOptions.headers = this.apiRequest.headers.map(header => ({
        //     [header.key]: this.replaceDynamicKeys(header.value)
        //   }));
        // }

        // if (this.apiRequest.method === 'POST') {
        //   requestOptions.data = this.parseJsonInput(this.apiRequest.body);
        // }

        // const response = await axios(requestOptions);
        // this.handleApiResponse(response);
      } catch (error) {
        this.handleApiResponse(error.response);
      } finally {
        this.isLoading = false;
      }
    },
    parseJsonInput(jsonInput) {
      try {
        return JSON.parse(this.replaceDynamicKeys(jsonInput) || "{}");
      } catch (error) {
        console.error("Invalid JSON input:", error);
        return {};
      }
    },
    handleApiResponse(response) {
      this.viewMode = 2;
      this.apiResponse = {
        status: response ? response.status : "error",
        data: response ? response.body : null,
      };
      this.showStatusCodeAlert = true;
    },
    parseQueryParams(url) {
      const queryParams = [];
      const queryString = url.split("?")[1];
      if (queryString) {
        const paramsArray = queryString.split("&");
        paramsArray.forEach((paramString) => {
          const [key, value] = paramString.split("=");
          queryParams.push({ key, value });
        });
      }
      return queryParams;
    },
    addQueryParam() {
      this.queryParams.push({ key: "", value: "" });
    },
    removeQueryParam(index) {
      this.queryParams.splice(index, 1);
    },
    addHeaderData() {
      this.apiRequest.headers.push({ key: "", value: "" });
    },
    removeHeaderData(index) {
      this.apiRequest.headers.splice(index, 1);
    },
    addDynamicKey() {
      this.dynamicKeys.push({ key: "", value: "" });
    },
    removeDynamicKey(index) {
      this.dynamicKeys.splice(index, 1);
    },
    editApiData(data, index) {
      this.apiRequest = {
        method: data.method,
        url: data.url,
        body: data.body,
        headers: data.headers,
      };
      this.modalData = {
        name: data.name,
        type: data.type,
      };
      this.viewMode = 1;
      this.iseditIndex = index + 1;
    },
    deleteApiData(index) {
      this.collections.splice(index, 1);
      this.saveApiListCall(false);
    },
    saveDynamicKeys() {
      this.dynamicsKeysloading = true;
      post("custom_notification_save_dynamic_keys", { data: this.dynamicKeys })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
          this.dynamicsKeysloading = false;
        })
        .catch((error) => {
          this.dynamicsKeysloading = false;
          displayErrorMessage(this.formTranslation.common.server_error + ".");
        });
    },
    saveApiList(openModel = false) {
      if (this.iseditIndex) {
        // Replace the existing object's value with the new one
        this.collections.splice(this.iseditIndex - 1, 1, {
          name: this.modalData.name,
          type: this.modalData.type,
          method: this.apiRequest.method,
          url: this.apiRequest.url,
          headers: this.apiRequest.headers,
          body: this.apiRequest.body,
        });
        this.saveApiListCall(true);
      } else if (openModel) {
        this.isTestSubmitted = true;
        this.$v.$touch();
        if (this.$v.apiRequest.$invalid) {
          return;
        }
        this.isTestSubmitted = false;
        this.isModalOpen = true;
      } else {
        // Check if an object with the same name exists
        const isNameAlreadyExists = this.collections.some(
          (collection) => collection.name === this.modalData.name
        );
        const isTypeAlreadyExists = this.collections.some(
          (collection) => collection.type === this.modalData.type
        );
        if (isNameAlreadyExists) {
          // Handle the case where the name already exists, e.g., show an error message
          displayErrorMessage("Name already exists. Cannot add the same name.");
          return;
        }
        if (isTypeAlreadyExists) {
          // Handle the case where the name already exists, e.g., show an error message
          displayErrorMessage(
            "Notification type already exists. Cannot add the same notification type."
          );
          return;
        }
        this.modalLoading = true;
        // Push the new object only if the name doesn't already exist
        this.collections.push({
          name: this.modalData.name,
          type: this.modalData.type,
          method: this.apiRequest.method,
          url: this.apiRequest.url,
          headers: this.apiRequest.headers,
          body: this.apiRequest.body,
        });

        this.saveApiListCall(true);
      }
    },
    saveApiListCall(modalSave = true) {
      post("custom_notification_save_api_configuration_list", {
        data: this.collections,
      })
        .then((response) => {
          if (modalSave) {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              this.isModalOpen = false;
              this.modalData = {
                name: "",
                type: "sms",
              };
              this.apiRequest = {
                method: "GET",
                url: "",
                body: JSON.stringify({ apikey: "" }),
                headers: [{ key: "Content-Type", value: "application/json" }],
              };
              this.iseditIndex = "";
            } else {
              displayErrorMessage(response.data.message);
            }
            this.modalLoading = false;
          }
        })
        .catch((error) => {
          if (modalSave) {
            this.modalLoading = false;
            displayErrorMessage(this.formTranslation.common.server_error + ".");
          }
        });
    },
    getApiList() {
      get("custom_notification_get_api_configuration_list", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.collections = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getDynamicKeys() {
      get("custom_notification_get_dynamic_keys", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.dynamicKeys = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    replaceDynamicKeys(data) {
      this.dynamicKeys.forEach((keyValuePair) => {
        data = data.replace(keyValuePair.key, keyValuePair.value);
      });
      return data;
    },
    initializeModule() {
      const { link_show_hide } = window.request_data;
      if (link_show_hide !== undefined && link_show_hide !== "") {
        this.requestStatus = link_show_hide;
      }
    },
    clearForm() {
      this.apiRequest = {
        method: "GET",
        url: "",
        body: JSON.stringify({ apikey: "" }),
        headers: [{ key: "Content-Type", value: "application/json" }],
      };
      this.openModal = {
        name: "",
        type: "sms",
      };
      this.queryParams = [];
      this.iseditIndex = "";
    },
    sendRequest() {
      post("send_custom_notification_request", this.apiRequest)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === "success"
          ) {
            this.handleApiResponse(response);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
  computed: {
    responseClass() {
      if (this.apiResponse) {
        return this.apiResponse.status >= 200 && this.apiResponse.status < 300
          ? "alert-success"
          : "alert-danger";
      }
      return "";
    },
    userModuleData() {
      return this.$store.state.userDataModule.user;
    },
  },
  watch: {
    "apiRequest.url"(newUrl) {
      this.queryParams = this.parseQueryParams(newUrl);
    },
    queryParams: {
      deep: true,
      handler(newQueryParams) {
        if (this.apiRequest.method === "GET") {
          const queryParamsString = newQueryParams
            .filter(
              (param) => param.key.trim() !== "" || param.value.trim() !== ""
            )
            .map((param) => `${param.key}=${param.value}`)
            .join("&");
          this.apiRequest.url =
            this.apiRequest.url.split("?")[0] +
            (queryParamsString ? `?${queryParamsString}` : "");
        }
      },
    },
  },
};
</script>

<style>
#api-builder .nav.nav-tabs {
  margin-bottom: 1rem;
}
#api-builder .nav-tabs .nav-link.active {
  color: var(--primary);
  /* border-top-color: var(--primary);
    border-left-color: var(--primary);
    border-right-color: var(--primary); */
}
#api-builder .nav-tabs .nav-link {
  font-size: 1rem;
  font-weight: 400;
  background-color: #fff;
  border-bottom-color: #dee2e6;
}
</style>
