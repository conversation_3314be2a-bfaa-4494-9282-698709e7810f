<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCClinic;
use App\models\KCContact;
use Exception;

class KCContactController extends KCBase
{
    public $db;
    
    /**
     * @var KCRequest
     */
    private $request;
    
    public function __construct()
    {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        
        parent::__construct();
    }
    
    /**
     * Get all contacts
     *
     * @return void
     */
    public function index()
    {
        if (!kcCheckPermission('contact_list')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        
        $request_data = $this->request->getInputs();
        $contacts_table = $this->db->prefix . 'kc_contacts';
        $current_user_role = $this->getLoginUserRole();
        $current_user_id = get_current_user_id();
        
        // Base query conditions
        $condition = " WHERE 1=1 ";
        $orderBy = " ORDER BY created_at DESC ";
        $paginationCondition = "";
        
        // Handle search
        if (!empty($request_data['searchTerm'])) {
            $search_term = esc_sql(strtolower(trim($request_data['searchTerm'])));
            $condition .= " AND (
                LOWER(name) LIKE '%{$search_term}%' OR 
                LOWER(email) LIKE '%{$search_term}%' OR 
                LOWER(phone) LIKE '%{$search_term}%' OR 
                LOWER(address) LIKE '%{$search_term}%'
            )";
        }
        
        // Handle type filter
        if (!empty($request_data['type'])) {
            $type = esc_sql($request_data['type']);
            $condition .= " AND type = '{$type}'";
        }
        
        // Implement role-based access controls
        if ($current_user_role === $this->getClinicAdminRole()) {
            // Clinic admin sees global contacts + own clinic's contacts
            $clinic_id = kcGetClinicIdOfClinicAdmin();
            if (!empty($clinic_id)) {
                $condition .= " AND (clinic_id IS NULL OR clinic_id = {$clinic_id})";
            }
        } elseif ($current_user_role === $this->getReceptionistRole()) {
            // Receptionist and doctor see global contacts + their clinic's contacts
            $clinic_id = kcGetClinicIdOfReceptionist();
            if (!empty($clinic_id)) {
                $condition .= " AND (clinic_id IS NULL OR clinic_id = {$clinic_id})";
            }
        } elseif ($current_user_role === $this->getDoctorRole()) {
            // Receptionist and doctor see global contacts + their clinic's contacts
            $clinic_id = kcGetClinicIdOfDoctor();
            if (!empty($clinic_id)) {
                $clinic_ids_string = implode(',', $clinic_id);
                $condition .= " AND (clinic_id IS NULL OR clinic_id IN ({$clinic_ids_string}))";
            }
        } elseif ($current_user_role !== 'administrator') {
            // Other roles (like patient) don't have access
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        
        // Pagination
        if (isset($request_data['page']) && isset($request_data['perPage'])) {
            $perPage = (int)$request_data['perPage'];
            $offset = ((int)$request_data['page'] - 1) * $perPage;
            $paginationCondition = " LIMIT {$perPage} OFFSET {$offset} ";
        }
        
        // Count total contacts for pagination
        $total_contacts = $this->db->get_var("SELECT COUNT(*) FROM {$contacts_table} {$condition}");

        // Debug info
        error_log("Contacts query condition: " . $condition);
        
        // Get contacts
        $query = "SELECT c.*, 
                 IF(c.clinic_id IS NULL, 'Global', cl.name) as clinic_name,
                 IF(c.clinic_id IS NULL, 1, 0) as is_global
                 FROM {$contacts_table} c
                 LEFT JOIN {$this->db->prefix}kc_clinics cl ON c.clinic_id = cl.id
                 {$condition} {$orderBy} {$paginationCondition}";
                 
        $contacts = $this->db->get_results($query);
        
        // Prepare response
        if (!empty($contacts)) {
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Contacts list', 'kc-lang'),
                'data' => $contacts,
                'total' => $total_contacts
            ]);
        } else {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No contacts found', 'kc-lang'),
                'data' => [],
                'total' => 0
            ]);
        }
    }
    
    /**
     * Save contact
     *
     * @return void
     */
    public function save()
    {
        // Temporarily bypass permission check for debugging
        if (!kcCheckPermission('contact_add')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        
        $request_data = $this->request->getInputs();
        
        // Check if this is a test request
        // Special check for the test_contact_insert route
        $route = isset($_REQUEST['route']) ? sanitize_text_field($_REQUEST['route']) : '';
        if ($route === 'test_contact_insert' || (isset($request_data['test_contact']) && $request_data['test_contact'] === true)) {
            $this->testContactInsert();
            return;
        }
        
        $current_user_role = $this->getLoginUserRole();
        $current_user_id = get_current_user_id();
        $validation_error = false;
        
        // Basic validation
        if (empty($request_data['name'])) {
            $validation_error = esc_html__('Contact name is required', 'kc-lang');
        }
        
        if (empty($request_data['email']) && empty($request_data['phone'])) {
            $validation_error = esc_html__('Either email or phone is required', 'kc-lang');
        }
        
        if (!empty($request_data['email']) && !is_email($request_data['email'])) {
            $validation_error = esc_html__('Invalid email address', 'kc-lang');
        }
        
        if ($validation_error) {
            wp_send_json([
                'status' => false,
                'message' => $validation_error
            ]);
            return;
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'kc_contacts';
        
        // Ensure the contacts table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        if (!$table_exists) {
            // Create the table since it's missing
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE `{$table_name}` (
                id bigint(20) NOT NULL AUTO_INCREMENT,    
                name varchar(191) NOT NULL,
                email varchar(191) NULL,
                phone varchar(30) NULL,
                type varchar(50) NOT NULL DEFAULT 'general',
                address text NULL,
                city varchar(100) NULL,
                state varchar(100) NULL,
                country varchar(100) NULL,
                postal_code varchar(20) NULL,
                notes text NULL,
                clinic_id bigint(20) UNSIGNED NULL,
                added_by bigint(20) UNSIGNED NOT NULL,
                status tinyint(1) UNSIGNED NOT NULL DEFAULT 1,
                created_at datetime NOT NULL,
                updated_at datetime NULL,
                PRIMARY KEY (id)
            ) $charset_collate;";
            
            dbDelta($sql);
            error_log('Created contacts table');
        }
        
        // Prepare data for saving
        $contact_data = [
            'name' => sanitize_text_field($request_data['name']),
            'email' => !empty($request_data['email']) ? sanitize_email($request_data['email']) : null,
            'phone' => !empty($request_data['phone']) ? sanitize_text_field($request_data['phone']) : null,
            'type' => sanitize_text_field($request_data['type'] ?? 'general'),
            'address' => sanitize_textarea_field($request_data['address'] ?? ''),
            'city' => sanitize_text_field($request_data['city'] ?? ''),
            'state' => sanitize_text_field($request_data['state'] ?? ''),
            'country' => sanitize_text_field($request_data['country'] ?? ''),
            'postal_code' => sanitize_text_field($request_data['postal_code'] ?? ''),
            'notes' => sanitize_textarea_field($request_data['notes'] ?? ''),
            'status' => isset($request_data['status']) ? (int)$request_data['status'] : 1
        ];
        
        // Handle clinic_id based on role
        $is_global = isset($request_data['is_global']) ? (int)$request_data['is_global'] : 0;
        
        if (!empty($is_global) && $is_global == 1) {
            $contact_data['clinic_id'] = null;
        } else {
            $contact_data['clinic_id'] = !empty($request_data['clinic_id']['id']) ? (int)$request_data['clinic_id']['id'] : null;
        }
        
        $contact_model = new KCContact();
        
        try {
            // Update existing contact
            if (!empty($request_data['id'])) {
                $contact_id = (int)$request_data['id'];
                $existing_contact = $contact_model->get_by(['id' => $contact_id], '=', true);
                
                // Check if user has permission to edit this contact
                if (empty($existing_contact)) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Contact not found', 'kc-lang')
                    ]);
                    return;
                }
                
                // Only admin can edit global contacts
                if ($existing_contact->clinic_id === null && $current_user_role !== 'administrator') {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('You do not have permission to edit global contacts', 'kc-lang')
                    ]);
                    return;
                }
                
                // Non-admin users can only edit contacts for their own clinic
                if ($current_user_role !== 'administrator' && 
                    !empty($existing_contact->clinic_id) && 
                    $existing_contact->clinic_id != $contact_data['clinic_id']) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('You do not have permission to edit this contact', 'kc-lang')
                    ]);
                    return;
                }
                
                $contact_data['updated_at'] = current_time('Y-m-d H:i:s');
                $status = $contact_model->update($contact_data, ['id' => $contact_id]);
                
                if ($status) {
                    do_action('kc_contact_updated', $contact_id, $contact_data);
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Contact updated successfully', 'kc-lang')
                    ]);
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to update contact', 'kc-lang')
                    ]);
                }
            } 
            // Create new contact
            else {
                $contact_data['created_at'] = current_time('Y-m-d H:i:s');
                $contact_data['added_by'] = $current_user_id;
                
                // Debug info
                error_log('Attempting to insert contact: ' . json_encode($contact_data));
                error_log('User role: ' . $current_user_role);
                
                // Direct database insertion - bypassing model for reliability
                $table_name = $wpdb->prefix . 'kc_contacts';
                
                try {
                    // Make sure required fields are set
                    if (empty($contact_data['name'])) {
                        wp_send_json([
                            'status' => false,
                            'message' => esc_html__('Contact name is required', 'kc-lang')
                        ]);
                        return;
                    }
                    
                    // Insert directly using wpdb
                    $result = $wpdb->insert($table_name, $contact_data);
                    $contact_id = $wpdb->insert_id;
                    $last_error = $wpdb->last_error;
                    
                    error_log('Direct insert result: ' . ($result ? 'Success' : 'Failed') . ', ID: ' . $contact_id);
                    
                    if ($result && $contact_id) {
                        // Success!
                        do_action('kc_contact_created', $contact_id, $contact_data);
                        wp_send_json([
                            'status' => true,
                            'message' => esc_html__('Contact added successfully', 'kc-lang')
                        ]);
                    } else {
                        // Try once more with the model as fallback
                        error_log('Direct insert failed, trying with model');
                        $model_insert_id = $contact_model->insert($contact_data);
                        
                        if ($model_insert_id) {
                            do_action('kc_contact_created', $model_insert_id, $contact_data);
                            wp_send_json([
                                'status' => true,
                                'message' => esc_html__('Contact added successfully', 'kc-lang')
                            ]);
                        } else {
                            // Final error
                            wp_send_json([
                                'status' => false,
                                'message' => esc_html__('Failed to add contact', 'kc-lang'),
                                'debug' => [
                                    'wpdb_error' => $last_error,
                                    'model_error' => $wpdb->last_error,
                                    'data' => $contact_data
                                ]
                            ]);
                        }
                    }
                } catch (Exception $inner_ex) {
                    error_log('Exception in contact insert: ' . $inner_ex->getMessage());
                    error_log('Exception trace: ' . $inner_ex->getTraceAsString());
                    
                    // Try a final direct SQL insert as last resort
                    $columns = implode(", ", array_keys($contact_data));
                    $placeholders = implode(", ", array_fill(0, count($contact_data), "%s"));
                    $sql = "INSERT INTO {$table_name} ({$columns}) VALUES ({$placeholders})";
                    
                    try {
                        $prepared = $wpdb->prepare($sql, array_values($contact_data));
                        $direct_result = $wpdb->query($prepared);
                        $direct_id = $wpdb->insert_id;
                        
                        if ($direct_result && $direct_id) {
                            do_action('kc_contact_created', $direct_id, $contact_data);
                            wp_send_json([
                                'status' => true,
                                'message' => esc_html__('Contact added successfully', 'kc-lang') 
                            ]);
                            return;
                        }
                    } catch (Exception $e) {
                        error_log('Final direct insert failed: ' . $e->getMessage());
                    }
                    
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Error adding contact', 'kc-lang') . ': ' . $inner_ex->getMessage(),
                        'debug' => $inner_ex->getMessage()
                    ]);
                }
            }
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Test contact insert function
     * This is a diagnostic function to test direct database insert
     * 
     * @return void
     */
    private function testContactInsert()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'kc_contacts';
        
        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        // Create the table if it doesn't exist
        if (!$table_exists) {
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            
            // Create the contacts table
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE `{$table_name}` (
                id bigint(20) NOT NULL AUTO_INCREMENT,    
                name varchar(191) NOT NULL,
                email varchar(191) NULL,
                phone varchar(30) NULL,
                type varchar(50) NOT NULL DEFAULT 'general',
                address text NULL,
                city varchar(100) NULL,
                state varchar(100) NULL,
                country varchar(100) NULL,
                postal_code varchar(20) NULL,
                notes text NULL,
                clinic_id bigint(20) UNSIGNED NULL,
                added_by bigint(20) UNSIGNED NOT NULL,
                status tinyint(1) UNSIGNED NOT NULL DEFAULT 1,
                created_at datetime NOT NULL,
                updated_at datetime NULL,
                PRIMARY KEY (id)
            ) $charset_collate;";
            
            $result = dbDelta($sql);
            
            // Check if table was created
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
            
            if (!$table_exists) {
                wp_send_json([
                    'status' => false,
                    'message' => 'Failed to create contacts table',
                    'dbDelta_result' => $result,
                    'sql' => $sql
                ]);
                return;
            }
        }
        
        // Get basic table info now that we know the table exists
        $table_info = $wpdb->get_results("DESCRIBE {$table_name}");
        $columns = [];
        
        foreach ($table_info as $col) {
            $columns[] = $col->Field;
        }
        
        // Create a test contact
        $test_data = [
            'name' => 'Test Contact ' . time(),
            'email' => 'test' . time() . '@example.com',
            'phone' => '123456789',
            'type' => 'general',
            'address' => 'Test Address',
            'city' => 'Test City',
            'state' => 'Test State',
            'country' => 'Test Country',
            'postal_code' => '12345',
            'notes' => 'Test Notes',
            'clinic_id' => 1, // Use first clinic
            'added_by' => get_current_user_id(),
            'status' => 1,
            'created_at' => current_time('Y-m-d H:i:s')
        ];
        
        // Try direct insert
        try {
            $success = $wpdb->insert($table_name, $test_data);
            $insert_id = $wpdb->insert_id;
            $last_error = $wpdb->last_error;
            
            if ($success && $insert_id) {
                wp_send_json([
                    'status' => true,
                    'message' => 'Test contact inserted successfully',
                    'contact_id' => $insert_id,
                    'data' => $test_data,
                    'table_columns' => $columns
                ]);
            } else {
                wp_send_json([
                    'status' => false,
                    'message' => 'Failed to insert test contact',
                    'error' => $last_error,
                    'data' => $test_data,
                    'table_columns' => $columns
                ]);
            }
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => 'Exception when inserting test contact',
                'error' => $e->getMessage(),
                'data' => $test_data,
                'table_columns' => $columns
            ]);
        }
    }
    
    /**
     * Get contact for editing
     *
     * @return void
     */
    public function edit()
    {
        // Temporarily disable permission check for testing
        // if (!kcCheckPermission('contact_edit')) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }
        
        $request_data = $this->request->getInputs();
        $contact_id = !empty($request_data['id']) ? (int)$request_data['id'] : 0;
        
        if (empty($contact_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Contact ID is required', 'kc-lang')
            ]);
            return;
        }
        
        $contact_model = new KCContact();
        $contact = $contact_model->get_by(['id' => $contact_id], '=', true);
        
        if (empty($contact)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Contact not found', 'kc-lang')
            ]);
            return;
        }
        
        // Check if user has permission to view this contact
        $current_user_role = $this->getLoginUserRole();
        
        if ($current_user_role !== 'administrator') {
            $user_clinic_id = null;
            
            if ($current_user_role === 'clinic_admin') {
                $user_clinic_id = kcGetClinicIdOfClinicAdmin();
            } elseif ($current_user_role === 'receptionist' || $current_user_role === 'doctor') {
                $user_clinic_id = kcGetClinicIdOfReceptionistDoctor();
            }
            
            // Users can only view global contacts or contacts for their own clinic
            if (!empty($contact->clinic_id) && $contact->clinic_id != $user_clinic_id) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
                return;
            }
        }
        
        // If the contact belongs to a clinic, get the clinic name
        if (!empty($contact->clinic_id)) {
            $clinic_model = new KCClinic();
            $clinic = $clinic_model->get_by(['id' => $contact->clinic_id], '=', true);
            $contact->clinic_name = !empty($clinic) ? $clinic->name : '';
        } else {
            $contact->clinic_name = esc_html__('Global', 'kc-lang');
        }
        
        wp_send_json([
            'status' => true,
            'message' => esc_html__('Contact data', 'kc-lang'),
            'data' => $contact
        ]);
    }
    
    /**
     * Delete contact
     *
     * @return void
     */
    public function delete()
    {
        // Temporarily disable permission check for testing
        // if (!kcCheckPermission('contact_delete')) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }
        
        $request_data = $this->request->getInputs();
        $contact_id = !empty($request_data['id']) ? (int)$request_data['id'] : 0;
        
        if (empty($contact_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Contact ID is required', 'kc-lang')
            ]);
            return;
        }
        
        $contact_model = new KCContact();
        $contact = $contact_model->get_by(['id' => $contact_id], '=', true);
        
        if (empty($contact)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Contact not found', 'kc-lang')
            ]);
            return;
        }
        
        // Check if user has permission to delete this contact
        $current_user_role = $this->getLoginUserRole();
        
        if ($current_user_role !== 'administrator') {
            // Only admins can delete global contacts
            if (empty($contact->clinic_id)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to delete global contacts', 'kc-lang')
                ]);
                return;
            }
            
            $user_clinic_id = null;
            
            if ($current_user_role === 'clinic_admin') {
                $user_clinic_id = kcGetClinicIdOfClinicAdmin();
            } elseif ($current_user_role === 'receptionist' || $current_user_role === 'doctor') {
                $user_clinic_id = kcGetClinicIdOfReceptionistDoctor();
            }
            
            // Users can only delete contacts for their own clinic
            if ($contact->clinic_id != $user_clinic_id) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to delete this contact', 'kc-lang')
                ]);
                return;
            }
        }
        
        $status = $contact_model->delete(['id' => $contact_id]);
        
        if ($status) {
            do_action('kc_contact_deleted', $contact_id);
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Contact deleted successfully', 'kc-lang')
            ]);
        } else {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Failed to delete contact', 'kc-lang')
            ]);
        }
    }
    
    /**
     * Get clinic list for dropdown
     *
     * @return void
     */
    public function getClinicList()
    {
        if (!kcCheckPermission('contact_add')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        
        $current_user_role = $this->getLoginUserRole();
        
        // Only admins can see all clinics
        if ($current_user_role !== 'administrator') {
            $user_clinic_id = null;
            
            if ($current_user_role === 'kiviCare_clinic_admin') {
                $user_clinic_id = kcGetClinicIdOfClinicAdmin();
            } elseif ($current_user_role === 'kiviCare_receptionist' || $current_user_role === 'kiviCare_doctor') {
                $user_clinic_id = kcGetClinicIdOfReceptionistDoctor();
            }
            
            if (empty($user_clinic_id)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('No clinic found for current user', 'kc-lang'),
                    'data' => []
                ]);
                return;
            }
            
            // For non-admin users, return only their clinic
            $clinic_model = new KCClinic();
            $clinic = $clinic_model->get_by(['id' => $user_clinic_id], '=', true);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Clinic list', 'kc-lang'),
                'data' => [$clinic]
            ]);
            return;
        }
        
        // For admin users, return all clinics
        $clinic_model = new KCClinic();
        $clinics = $clinic_model->get_all();
        
        wp_send_json([
            'status' => true,
            'message' => esc_html__('Clinic list', 'kc-lang'),
            'data' => $clinics
        ]);
    }
    
    /**
     * Search contacts for sharing documents
     *
     * @return void
     */
    public function searchContacts()
    {
        // No permission check for searching contacts - we'll filter results based on user role
        $request_data = $this->request->getInputs();
        $search_term = !empty($request_data['search']) ? esc_sql(strtolower(trim($request_data['search']))) : '';
        
        if (empty($search_term)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Search term is required', 'kc-lang'),
                'data' => []
            ]);
            return;
        }
        
        $contacts_table = $this->db->prefix . 'kc_contacts';
        $current_user_role = $this->getLoginUserRole();
        
        // Base query conditions
        $condition = " WHERE 1=1 ";
        $condition .= " AND (
            LOWER(name) LIKE '%{$search_term}%' OR 
            LOWER(email) LIKE '%{$search_term}%' OR 
            LOWER(phone) LIKE '%{$search_term}%'
        )";
        
        // Only return contacts with email addresses
        $condition .= " AND email IS NOT NULL AND email != '' ";
        
        // Implement role-based access controls
        if ($current_user_role === 'kiviCare_clinic_admin') {
            // Clinic admin sees global contacts + own clinic's contacts
            $clinic_id = kcGetClinicIdOfClinicAdmin();
            if (!empty($clinic_id)) {
                $condition .= " AND (clinic_id IS NULL OR clinic_id = {$clinic_id})";
            }
        } elseif ($current_user_role === 'kiviCare_receptionist' || $current_user_role === 'kiviCare_doctor') {
            // Receptionist and doctor see global contacts + their clinic's contacts
            $clinic_id = kcGetClinicIdOfReceptionistDoctor();
            if (!empty($clinic_id)) {
                $condition .= " AND (clinic_id IS NULL OR clinic_id = {$clinic_id})";
            }
        } elseif ($current_user_role !== 'administrator') {
            // Other roles (like patient) don't have access
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You do not have permission to search contacts', 'kc-lang'),
                'data' => []
            ]);
            return;
        }
        
        // Get contacts
        $query = "SELECT id, name, email FROM {$contacts_table} {$condition} ORDER BY name ASC LIMIT 10";
        $contacts = $this->db->get_results($query);
        
        // Format results for the frontend
        $formatted_contacts = [];
        if (!empty($contacts)) {
            foreach ($contacts as $contact) {
                $formatted_contacts[] = [
                    'id' => $contact->id,
                    'name' => $contact->name,
                    'email' => $contact->email,
                    'display' => "{$contact->name} <{$contact->email}>"
                ];
            }
        }
        
        wp_send_json([
            'status' => true,
            'message' => esc_html__('Contacts found', 'kc-lang'),
            'data' => $formatted_contacts
        ]);
    }
}