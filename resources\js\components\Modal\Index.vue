<!-- ModalPopup component -->
<template>
  <div class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <!-- Modal Content -->
      <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full z-60">
        <!-- Close button -->
        <button 
          @click="$emit('close')"
          class="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
        >
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <!-- Modal content slot -->
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModalPopup',
  
  mounted() {
    document.body.style.overflow = 'hidden';
  },
  
  beforeDestroy() {
    document.body.style.overflow = '';
  }
}
</script>