<template>
  <div
    v-if="showModal"
    class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div class="bg-white rounded-lg w-full max-w-2xl my-8">
      <!-- Header -->
      <div class="flex justify-between items-center p-6 border-b">
        <h3 class="text-lg font-semibold text-gray-900">
          Reschedule Appointment
        </h3>
        <button
          @click="closeModal"
          type="button"
          aria-label="Close modal"
          class="text-gray-500 hover:text-gray-700"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-5 h-5"
          >
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <form @submit.prevent="handleSubmit">
        <!-- Scrollable Content -->
        <div class="overflow-y-auto p-6">
          <!-- Important Notice -->
          <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-800">
            <p class="text-sm font-medium">
              <span class="font-bold">Important:</span> Rescheduling will preserve all appointment details including the selected service.
            </p>
          </div>

          <div class="space-y-6">
            <!-- Date Selection -->
            <div>
              <label
                for="appointment-date"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                Date
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  id="appointment-date"
                  type="date"
                  v-model="selectedDate"
                  :min="minDate"
                  :max="maxDate"
                  class="w-full rounded-lg border border-gray-300 px-3 py-2 bg-white"
                  required
                />
              </div>
            </div>

            <!-- Time Slots -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Available Time Slots
                <span class="text-red-500">*</span>
              </label>
              <div class="border border-gray-200 rounded-lg p-4">
                <div v-if="loadingSlots" class="flex justify-center py-4">
                  <div
                    class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"
                  ></div>
                  <p class="mt-2 text-sm text-gray-600 ml-2">
                    Loading available slots...
                  </p>
                </div>
                <div
                  v-else-if="timeSlots.length === 0"
                  class="text-center py-4"
                >
                  <p class="text-sm text-gray-600">
                    {{
                      selectedDate
                        ? "No available slots for selected date"
                        : "Please select a date to view available slots"
                    }}
                  </p>
                </div>
                <div v-else class="grid grid-cols-4 gap-2" role="radiogroup">
                  <button
                    v-for="slot in timeSlots"
                    :key="slot.time"
                    type="button"
                    @click="selectedSlot = slot"
                    :class="[
                      'px-3 py-2 text-sm border rounded',
                      'hover:bg-blue-50 hover:border-blue-200',
                      selectedSlot === slot ? 'bg-blue-50 border-blue-200' : '',
                      'transition-colors duration-150',
                    ]"
                    role="radio"
                    :aria-checked="selectedSlot === slot"
                  >
                    {{ slot.time }}
                  </button>
                </div>
              </div>
              <p
                v-if="submitted && !selectedSlot"
                class="text-sm text-red-500 mt-1"
              >
                Please select a time slot
              </p>
            </div>

            <!-- Reason -->
            <div>
              <label
                for="reschedule-reason"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                Reason for Rescheduling
                <span class="text-red-500">*</span>
              </label>
              <textarea
                id="reschedule-reason"
                v-model="reason"
                class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows="3"
                :placeholder="
                  formTranslation?.appointments?.appointment_desc_plh ||
                  'Enter reason for rescheduling...'
                "
                required
              ></textarea>
              <p
                v-if="submitted && !reason.trim()"
                class="text-sm text-red-500 mt-1"
              >
                Please provide a reason for rescheduling
              </p>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            type="button"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="!isValid || loading"
          >
            <span v-if="loading" class="flex items-center">
              <svg
                class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Saving...
            </span>
            <span v-else>Reschedule Appointment</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import moment from "moment";

export default {
  name: "RescheduleModal",
  props: {
    showModal: {
      type: Boolean,
      default: false,
    },
    appointment: {
      type: Object,
      required: false,
      default: null
    },
  },
  data() {
    return {
      loading: false,
      submitted: false,
      loadingSlots: false,
      selectedDate: "",
      selectedSlot: null,
      reason: "",
      timeSlots: [],
      isClosing: false,
    };
  },
  computed: {
    minDate() {
      return new Date().toISOString().split("T")[0];
    },
    maxDate() {
      const date = new Date();
      date.setFullYear(date.getFullYear() + 1);
      return date.toISOString().split("T")[0];
    },
    hasValidAppointment() {
      return this.appointment &&
             typeof this.appointment === 'object' &&
             this.appointment.id;
    },
    isValid() {
      return (
        this.selectedDate &&
        this.selectedSlot &&
        this.reason.trim() &&
        !this.loading
      );
    },
  },
  watch: {
    showModal: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.hasValidAppointment) {
          this.$nextTick(() => {
            this.initializeForm();
            document.addEventListener("keydown", this.handleEscapeKey);
          });
        } else {
          document.removeEventListener("keydown", this.handleEscapeKey);
        }
      },
    },
    selectedDate(newDate) {
      if (newDate) {
        this.fetchTimeSlots();
      } else {
        this.timeSlots = [];
        this.selectedSlot = null; // Reset selected slot when date is cleared
      }
    },
  },
  beforeUnmount() {
    // Changed from beforeDestroy to beforeUnmount
    document.removeEventListener("keydown", this.handleEscapeKey);
  },
  methods: {
    initializeForm() {
      try {
        if (this.appointment?.appointment_start_time) {
          const appointmentDate = moment(
            this.appointment.appointment_start_time
          );
          this.selectedDate = appointmentDate.format("YYYY-MM-DD");
          this.fetchTimeSlots();
        }
        this.selectedSlot = null;
        this.reason = "";
      } catch (error) {
        console.error("Error initializing form:", error);
        this.$toast?.error("Error initializing form");
      }
    },

    async fetchTimeSlots() {
      if (!this.selectedDate) return;

      this.selectedSlot = null;
      this.loadingSlots = true;

      try {
        const doctorId = this.appointment?.doctor_id?.id;
        const clinicId = this.appointment?.clinic_id?.id;

        if (!doctorId || !clinicId) {
          throw new Error("Missing required IDs");
        }

        const response = await get("get_time_slots", {
          doctor_id: doctorId,
          clinic_id: clinicId,
          date: this.selectedDate,
        });

        if (!response?.data?.data) {
          throw new Error("Invalid response format");
        }

        this.timeSlots = response.data.data[0] || [];
      } catch (error) {
        console.error("Error fetching time slots:", error);
        this.$toast?.error(error.message || "Error loading time slots");
        this.timeSlots = [];
      } finally {
        this.loadingSlots = false;
      }
    },

    handleBackdropClick(event) {
      if (event.target === event.currentTarget && !this.loading) {
        this.closeModal();
      }
    },

    handleEscapeKey(event) {
      if (event.key === "Escape" && !this.loading) {
        this.closeModal();
      }
    },

    closeModal() {
      if (this.isClosing || this.loading) return;

      this.isClosing = true;
      this.$emit("update:showModal", false);
      this.$emit("close");
      this.resetForm();
      this.isClosing = false;
    },

    resetForm() {
      this.selectedDate = "";
      this.selectedSlot = null;
      this.reason = "";
      this.submitted = false;
      this.timeSlots = [];
    },

    async handleSubmit() {
      this.submitted = true;

      if (!this.isValid) return;

      try {
        this.loading = true;

        const payload = {
          appointment_id: this.appointment?.id,
          appointment_start_date: this.selectedDate,
          appointment_start_time: this.selectedSlot.time,
          description: this.reason.trim(),
          status: this.appointment?.status,
          // Include visit_type if available to ensure service preservation
          visit_type: this.appointment?.visit_type || null,
          _ajax_nonce:
            document.querySelector('meta[name="kivicare-ajax-nonce"]')
              ?.content || "",
        };

        if (!payload.appointment_id) {
          throw new Error("Invalid appointment ID");
        }

        // Log the payload for debugging
        console.log("Rescheduling appointment with payload:", payload);

        const response = await post("appointment_reschedule", payload);

        if (!response?.data?.status) {
          throw new Error(
            response?.data?.message || "Failed to reschedule appointment"
          );
        }

        this.$emit("rescheduled", response.data);
        this.closeModal();
        this.$toast?.success(
          response.data.message || "Appointment rescheduled successfully"
        );
      } catch (error) {
        console.error("Rescheduling error:", error);
        this.$toast?.error(error.message || "Failed to reschedule appointment");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped>
input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  opacity: 0;
  cursor: pointer;
}
</style>
