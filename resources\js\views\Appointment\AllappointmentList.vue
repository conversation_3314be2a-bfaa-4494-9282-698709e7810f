<template>
  <div>
    <!-- Admin Warning Alert -->
    <div
      v-if="getUserRole() === 'administrator' && clinicSessionNotice.status"
      class="mb-4 bg-yellow-50 border-l-4 border-yellow-400 p-4"
    >
      <div class="flex items-center justify-between">
        <p class="text-yellow-700 font-medium">{{ clinicSessionNotice.msg }}</p>
        <router-link
          v-if="kcCheckPermission('doctor_session_list')"
          :to="{ name: 'doctor-session.create' }"
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          {{ formTranslation.common.add_session }}
        </router-link>
      </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white rounded-xl shadow-sm min-h-screen">
      <!-- Header Section -->
      <div class="px-6 py-4 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">
              {{ formTranslation.appointments.appointments }}
            </h1>
            <p class="text-sm text-gray-500 mt-1">
              Manage and track your appointments
            </p>
          </div>
          <div class="flex items-center gap-3">
            <!-- Bulk mode actions -->
            <div v-if="enableMultiDelete" class="flex items-center gap-2">
              <!-- Delete button -->
              <button
                v-if="kcCheckPermission('appointment_delete')"
                :disabled="!enableMultiBtnDelete"
                @click="performBulkDelete"
                class="items-center justify-center whitespace-nowrap rounded-md text-sm font-medium flex gap-1.5 px-4 py-2
                      bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 disabled:pointer-events-none"
              >
                <i class="fa fa-trash"></i> Delete Selected
              </button>

              <!-- Cancel selection button -->
              <button
                @click="cancelBulkOperation"
                class="items-center justify-center whitespace-nowrap rounded-md text-sm font-medium flex gap-1.5 px-4 py-2
                      bg-gray-200 text-gray-800 hover:bg-gray-300"
              >
                <i class="fa fa-times"></i> Cancel Selection
              </button>

              <!-- Exit bulk mode button -->
              <button
                @click="exitBulkMode"
                class="items-center justify-center whitespace-nowrap rounded-md text-sm font-medium flex gap-1.5 px-4 py-2
                      bg-gray-700 text-white hover:bg-gray-900"
              >
                <i class="fa fa-arrow-left"></i> Exit Bulk Mode
              </button>
            </div>

            <!-- Normal mode actions - Bulk actions toggle button -->
            <div v-else class="mr-2">
              <button
                v-if="kcCheckPermission('appointment_delete')"
                @click="enterBulkMode"
                class="items-center justify-center whitespace-nowrap rounded-md text-sm font-medium flex gap-1.5 px-4 py-2
                      bg-gray-200 text-gray-800 hover:bg-gray-300"
              >
                <i class="fa fa-tasks"></i> Bulk Actions
              </button>
            </div>

            <!-- data import module -->
            <module-data-import
              v-if="
                userData.addOns.kiviPro &&
                kcCheckPermission('appointment_add') &&
                kivicareCompareVersion(requireProVersion, userData.pro_version)
              "
              ref="module_data_import"
              :required-data="[
                {
                  label: formTranslation.common.lbl_date_validation,
                  value: 'date',
                },
                {
                  label: formTranslation.doctor_session.plh_start_time,
                  value: 'start_time',
                },
                {
                  label: formTranslation.doctor_session.plh_end_time,
                  value: 'end_time',
                },
                { label: formTranslation.common.service, value: 'service' },
                {
                  label: formTranslation.clinic.clinic_name,
                  value: 'clinic_name',
                },
                {
                  label: formTranslation.doctor.doctor_name,
                  value: 'doctor_name',
                },
                {
                  label: formTranslation.patient.patient_name,
                  value: 'patient_name',
                },
                { label: formTranslation.common.status, value: 'status' },
              ]"
              :module-name="formTranslation.appointments.appointments"
              :module-type="'appointment'"
            ></module-data-import>

            <!-- data export module -->
            <module-data-export
              v-if="kcCheckPermission('appointment_export')"
              :module-data="csvAppointmentData"
              :module-name="formTranslation.appointments.appointments"
              :module-type="'appointment'"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" x2="12" y1="15" y2="3"></line>
              </svg>
              Export
            </module-data-export>

            <button
              v-if="addAppointmentButton && kcCheckPermission('appointment_add')"
              @click="handleAppointmentForm({})"
              class="items-center justify-center whitespace-nowrap rounded-md text-sm font-medium bg-black text-white px-4 py-2 hover:bg-gray-800"
            >
              <i class="fa fa-plus"></i> Add Appointment
            </button>
          </div>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="border-b border-gray-100 bg-gray-50/50">
        <!-- Filters Section -->
        <div class="border-b border-gray-100 bg-gray-50/50 px-6 py-2">
          <div class="flex items-center">
            <!-- Left side with filter icon and title -->
            <div class="flex items-center gap-2 mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4 text-gray-500"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polygon
                  points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"
                ></polygon>
              </svg>
              <h2 class="text-lg font-medium">
                {{ formTranslation.common.filters }}
              </h2>
            </div>

            <!-- Right side with clear filter button -->
            <button
              @click="clearFilters"
              class="flex items-center gap-1.5 text-sm text-gray-600 border border-2 p-1 border-black hover:text-white hover:bg-black"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              Clear Filters
            </button>
          </div>
          <!-- Filter Controls -->
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4 my-2">
            <div v-for="filter in activeFilters" :key="filter.key">
              <SearchableSelect
                v-model="filterData[filter.key]"
                :options="filter.options"
                :placeholder="filter.placeholder"
                @change="() => handleFilterChange(filter.type)"
              />
            </div>

            <div>
              <select
                v-model="filterData.status"
                @change="handleFilterChange"
                class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              >
                <option
                  v-for="status in allStatus"
                  :key="status.value"
                  :value="status.value"
                >
                  {{ formTranslation.common[status.label] || status.label }}
                </option>
              </select>
            </div>
            <div>
              <div class="flex items-center space-x-2">
                <input
                  v-model="filterData.startDate"
                  type="date"
                  class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  @change="handleDateRangeChange"
                />
                <span class="text-gray-500">to</span>
                <input
                  v-model="filterData.endDate"
                  type="date"
                  class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  @change="handleDateRangeChange"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Appointments List -->
      <div class="overflow-x-auto min-h-screen">
        <AppointmentCard
          :appointment-data="appointmentList"
          :appointmentDate="filterData.date"
          :filter_status="filterData.status"
          :enable_delete_multiple="enableMultiDelete"
          :delete_multiple_appointment="deleteMultipleAppointment"
          @enabledDeleteBtn="deleteBtnEnabled"
          @reloadAppointment="handleFilterChange"
          @closeFilterForm="closeFilterForm"
          @updateAppointmentList="updateAppointmentList"
          @rescheduleAppointment="handleRescheduleAppointment"
          :isLoading="isLoading"
          :patient_profile_id="patient_profile_id"
          ref="appointmentCardRef"
        />
      </div>

      <!-- Pagination -->
      <div
        class="px-6 py-4 border-t border-gray-100 flex items-center justify-between"
      >
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600">Rows per page:</span>
          <select
            @click="currentPage = 1"
            v-model.number="perPage"
            class="border border-gray-200 rounded px-2 py-1 text-sm"
          >
            <option v-for="n in [10, 20, 50]" :key="n" :value="n">
              {{ n }}
            </option>
            <option :value="-1">ALL</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-600">
            <template v-if="perPage === -1">
              Showing all {{ appointmentTotalRows }} records
            </template>
            <template v-else>
              Page {{ currentPage }} of
              {{ Math.ceil(appointmentTotalRows / perPage) }}
            </template>
          </span>
          <div class="flex gap-1" v-if="perPage !== -1">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="inline-flex items-center justify-center rounded-md border border-gray-200 w-8 h-8 hover:bg-gray-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </button>
            <button
              @click="currentPage++"
              :disabled="
                currentPage >= Math.ceil(appointmentTotalRows / perPage)
              "
              class="inline-flex items-center justify-center rounded-md border border-gray-200 w-8 h-8 hover:bg-gray-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      v-if="isAppointmentModalOpen"
      class="fixed inset-0 bg-black opacity-50"
      @click="isAppointmentModalOpen = false"
    ></div>

    <!-- Modal Component -->
    <div
      v-if="isAppointmentModalOpen"
      class="fixed inset-0 z-50 overflow-y-auto"
    >
      <div class="min-h-screen px-4 py-6 flex items-center justify-center">
        <div class="w-full max-w-5xl bg-white rounded-xl shadow-xl">
          <!-- Modal Header -->
          <div
            class="sticky top-0 z-10 flex items-center justify-between p-6 border-b border-gray-100 bg-white rounded-t-xl"
          >
            <h3 class="text-xl font-semibold text-gray-900">
              {{
                isRescheduling
                  ? formTranslation.appointments.reschedule
                  : formTranslation.appointments.add_appointment_btn
              }}
            </h3>
            <button
              @click="closeAppointmentModal"
              class="text-gray-400 hover:text-gray-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                class="w-6 h-6"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="p-6">
            <AppointmentForm
              :appointment-data="appointmentFormObj"
              :is-rescheduling="isRescheduling"
              :patient_profile_id="patient_profile_id"
              @appointmentSaved="handleAppointmentSave"
              @closeAppointmentForm="closeAppointmentModal"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AppointmentCard from "../../components/appointment/AppointmentCard";
import AppointmentForm from "../../components/appointment/AppointmentForm";
import SearchableSelect from "../../components/Common/SearchableSelect";

import { get } from "../../config/request";
import moment from "moment";

export default {
  components: {
    AppointmentCard,
    AppointmentForm,
    SearchableSelect,
  },

  props: {
    patient_profile_id: {
      type: [Number, String],
      default: "",
    },
  },

  data() {
    return {
      showImportModal: false,
      isAppointmentModalOpen: false,
      filterOpen: false,
      enableMultiDelete: false,
      enableMultiBtnDelete: false, // Track if any appointments are selected
      isRescheduling: false,
      appointment: {},
      deleteMultipleAppointment: false,
      isLoading: false,
      filterData: this.getDefaultFilterData(),
      perPage: 10, // This is a number, not a string
      currentPage: 1,
      totalPages: 1,
      appointmentFormObj: {},
      appBtnText: '<i class="fa fa-plus"></i> Add Appointment',
      addAppointmentButton: true,
      clinicSessionNotice: {
        status: false,
        msg: "",
      },
      doctors: [],
      patients: [],
      csvAppointmentData: [],
      allStatus: [
        { label: "all", value: "all" },
        { label: "booked", value: "1" },
        { label: "completed", value: "3" },
        { label: "cancelled", value: "0" },
        { label: "checkin", value: "4" },
        { label: "pending", value: "2" },
      ],
    };
  },

  computed: {
    appointmentList() {
      return this.$store.state.appointmentModule.appointmentList || [];
    },

    appointmentTotalRows() {
      return this.$store.state.appointmentModule.totalrows || 0;
    },

    showClinicFilter() {
      return (
        this.userData?.addOns?.kiviPro &&
        ["administrator", "doctor"].includes(this.getUserRole())
      );
    },

    showDoctorFilter() {
      return this.getUserRole() !== "doctor";
    },

    showPatientFilter() {
      return (
        this.getUserRole() !== "patient" &&
        !this.$route.params.patient_id &&
        !this.patient_profile_id
      );
    },

    formattedDate: {
      get() {
        return this.getFormattedDate(this.filterData.date);
      },
      set(value) {
        this.filterData.date = this.setFormattedDate(value);
      },
    },

    defaultClinicData() {
      return this.$store.state.userDataModule.clinic;
    },

    activeFilters() {
      const filters = [];

      if (this.showClinicFilter) {
        filters.push({
          key: "clinic_id",
          type: "clinic",
          options: this.clinics(),
          placeholder:
            this.formTranslation?.appointments?.select_clinic ||
            "Select Clinic",
        });
      }

      if (this.showDoctorFilter) {
        filters.push({
          key: "doctor_id",
          type: "doctor",
          options: this.doctors,
          placeholder:
            this.formTranslation?.patient_encounter?.tag_select_doctor ||
            "Select Doctor",
        });
      }

      if (this.showPatientFilter) {
        filters.push({
          key: "patient_id",
          type: "patient",
          options: this.patients,
          placeholder:
            this.formTranslation?.appointments?.select_patient ||
            "Select Patient",
        });
      }

      return filters;
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },
  },

  methods: {
    onDateInput(event) {
      this.filterData.date = this.handleDateInput(event);
    },
    getDefaultFilterData() {
      const patient_id =
        this.$route.params.patient_id || this.patient_profile_id || "";
      return {
        date: "",
        startDate: "", // Only set when user selects
        endDate: "", // Only set when user selects
        patient_id: patient_id,
        status: "all",
        clinic_id: "",
        doctor_id: "",
      };
    },

    async init() {
      try {
        await this.checkIfClinicHaveSession();
        await this.loadAppointments();

        if (this.getUserRole() !== "patient") {
          await this.getClinicPatients("");
        }

        if (this.getUserRole() !== "doctor") {
          await this.doctorListDropDown("");
        }
      } catch (error) {
        console.error("Initialization error:", error);
      }
    },

    async handleFilterChange(type) {
      this.isLoading = true;

      try {
        if (type === "clinic") {
          await Promise.all([
            this.getClinicPatients(this.filterData.clinic_id),
            this.doctorListDropDown(this.filterData.clinic_id),
          ]);
        }
        await this.loadAppointments();
      } catch (error) {
        console.error("Filter change error:", error);
      } finally {
        this.isLoading = false;
      }
    },

    handleDateRangeChange() {
      if (this.filterData.startDate && this.filterData.endDate) {
        this.loadAppointments();
      }
    },

    clearFilters() {
      // Reset all filters to default values
      this.filterData = this.getDefaultFilterData();

      // Trigger reload of appointments with cleared filters
      this.loadAppointments();
    },

    async loadAppointments() {
      let activeFilters = {};

      // Only add filters that have been explicitly set
      Object.entries(this.filterData).forEach(([key, value]) => {
        // Check if the filter has a value and it's not empty
        if (value && value !== "") {
          // Special handling for date range
          if (key === "startDate" && this.filterData.endDate) {
            activeFilters.date = {
              start: moment(this.filterData.startDate).toISOString(),
              end: moment(this.filterData.endDate).toISOString(),
            };
          }
          // Skip endDate as it's handled with startDate
          else if (key === "endDate") {
            return;
          }
          // Your existing filter logic
          else if (key === "status" && value !== "all") {
            // activeFilters[key] = value;
            activeFilters[key] = this.transformStatusFilter(
              this.filterData.status
            );
          } else if (["clinic_id", "doctor_id", "patient_id"].includes(key)) {
            activeFilters[key] = { id: value };
          } else if (value !== this.getDefaultFilterData()[key]) {
            activeFilters[key] = value;
          }
        }
      });

      // If no filters are applied, only use today's date
      if (Object.keys(activeFilters).length === 1 && activeFilters.pagination) {
        activeFilters.date = moment().format("YYYY-MM-DD");
      }

      // Add pagination
      activeFilters.pagination = this.currentPage;

      // Handle the "ALL" option (value -1)
      if (this.perPage === -1 || this.perPage === "-1") {
        // For "ALL" option, set perPage to 0 to indicate we want all records
        activeFilters.perPage = 0;
      } else {
        // Make sure perPage is a number, not a string
        activeFilters.perPage = parseInt(this.perPage, 10);
      }

      // If date range is selected, add the additional required parameters
      await this.$store.dispatch("appointmentModule/fetchAppointmentData", {
        filterData: activeFilters,
      });
    },

    // Add this method to transform status
    transformStatusFilter(status) {
      // Find the matching status object from allStatus
      const matchedStatus = this.allStatus.find(
        (statusObj) => statusObj.value === status
      );

      // If found, return an object with label and value
      // Otherwise, return a default status object
      return matchedStatus
        ? {
            label:
              this.formTranslation.common[matchedStatus.label] ||
              matchedStatus.label,
            value: status,
          }
        : { label: "All", value: "all" }; // Default fallback
    },

    handleAppointmentForm(appointment = {}) {
      this.isRescheduling = false;
      this.appointmentFormObj = appointment;
      this.isAppointmentModalOpen = true;
    },

    // handleRescheduleAppointment(appointment) {
    //   this.isRescheduling = true;
    //   this.appointmentFormObj = { ...appointment };
    //   this.isAppointmentModalOpen = true;
    // },

    handleRescheduleAppointment(appointment) {
      this.isRescheduling = true;
      // appointment.clinic_id=appointment.clinic_id.id;
      // appointment.doctor_id=appointment.doctor_id.id;
      this.appointmentFormObj = appointment;

      this.appointmentFormObj.clinic_id = appointment.clinic_id.id;
      this.appointmentFormObj.doctor_id = appointment.doctor_id.id;
      this.appointmentFormObj.patient_id = appointment.patient_id.id;
      // console.log(this.appointmentFormObj.visit_type,'ritesh-2');
      this.isAppointmentModalOpen = true;
    },

    closeAppointmentModal() {
      this.isAppointmentModalOpen = false;
      this.appointmentFormObj = {};
      this.isRescheduling = false;
    },

    handleAppointmentSave() {
      this.closeAppointmentModal();
      this.loadAppointments();
    },

    async checkIfClinicHaveSession() {
      this.clinicSessionNotice.status =
        window.request_data.allClinicHaveSession.status;
      this.clinicSessionNotice.msg =
        window.request_data.allClinicHaveSession.message;
    },

    clinic() {
      return this.$store.state.userDataModule.clinic;
    },
    clinics() {
      this.clinicMultiselectLoader = false;
      return this.$store.state.clinic;
    },

    filterOpenClose() {
      this.filterOpen = !this.filterOpen;
    },

    closeFilterForm() {
      this.filterOpen = false;
    },

    deleteBtnEnabled(selectedIds) {
      this.enableMultiBtnDelete = selectedIds && selectedIds.length > 0;
    },

    updateAppointmentList(data) {
      this.csvAppointmentData = data;
    },

    // Enter bulk mode
    enterBulkMode() {
      this.enableMultiDelete = true;
      // Reset any previous selection
      if (this.$refs.appointmentCardRef) {
        this.$refs.appointmentCardRef.patientNumber = [];
        this.$refs.appointmentCardRef.selectAll = false;
      }
      this.enableMultiBtnDelete = false;
    },

    // Exit bulk mode
    exitBulkMode() {
      // First clear any selections
      this.cancelBulkOperation();
      // Then disable bulk mode
      this.enableMultiDelete = false;
    },

    // Cancel all selections but keep bulk mode enabled
    cancelBulkOperation() {
      this.deleteMultipleAppointment = false;

      // Reset selections
      if (this.$refs.appointmentCardRef) {
        // Clear selected appointments
        this.$refs.appointmentCardRef.patientNumber = [];

        // Uncheck all checkboxes
        this.$refs.appointmentCardRef.selectAll = false;

        // Find all checkboxes and uncheck them
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
          if (checkbox) {
            checkbox.checked = false;

            // Remove highlighting from rows
            if (checkbox.id && checkbox.id !== "appointment_id_select_all") {
              const row = checkbox.closest('tr');
              if (row) {
                row.classList.remove('bg-blue-50', 'border-blue-300');
              }
            }
          }
        });

        // Update button state
        this.enableMultiBtnDelete = false;
      }
    },

    performBulkDelete() {
      if (!this.enableMultiBtnDelete) return;

      // Set this flag to true, which will trigger the watch in AppointmentCard
      this.deleteMultipleAppointment = true;

      // Reset the flag after a short delay to allow the handler to execute
      setTimeout(() => {
        this.deleteMultipleAppointment = false;
      }, 500);
    },

    async getClinicPatients(clinic_id) {
      console.log("clinic_id", clinic_id);
      try {
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: clinic_id,
        });

        if (response.data?.status) {
          this.patients = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinic patients:", error);
      }
    },

    async doctorListDropDown(clinic_id) {
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          clinic_id: clinic_id,
          module_type: "appointment_filter",
        });

        if (response.data?.status) {
          this.doctors = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching doctors:", error);
      }
    },

    handleClickOutside(event) {
      // Get all dropdown buttons and menus
      const dropdowns = document.querySelectorAll(".dropdown-menu");
      const buttons = document.querySelectorAll(".dropdown-button");

      // If click is outside dropdown and buttons, close all dropdowns
      if (
        !event.target.closest(".dropdown-menu") &&
        !event.target.closest(".dropdown-button")
      ) {
        this.closeAllDropdowns();
      }
    },

    closeAllDropdowns() {
      // Add your logic to close dropdowns here
      // For example:
      this.openDropdownId = null; // if you're tracking open dropdown with an ID
      // or
      this.isDropdownOpen = false; // if you're using a boolean
    },
  },

  mounted() {
    this.init();
  },

  beforeDestroy() {
    // Clean up listener
    document.removeEventListener("click", this.handleClickOutside);
  },

  watch: {
    currentPage() {
      this.loadAppointments();
    },

    perPage() {
      this.loadAppointments();
    },

    "filterData.date"() {
      this.loadAppointments();
    },
  },
};
</script>
<style>
.kivicare-pagination .page-item.active button {
  border-color: var(--primary) !important;
  background-color: var(--primary) !important;
}
.kivicare-pagination .page-link {
  border-color: var(--primary) !important;
}
.font-size-10 {
  font-size: 10px !important;
}
.kc-row-gap {
  row-gap: 1em;
}
</style>