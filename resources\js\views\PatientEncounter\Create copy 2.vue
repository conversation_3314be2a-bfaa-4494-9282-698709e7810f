<!-- Create.vue -->
<template>
  <div class="flex flex-col space-y-6">
    <form id="encounterDataForm" @submit.prevent="handleSubmit" novalidate>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Consultation Date -->
          <div>
            <label for="encounter_date" class="block text-sm font-medium text-gray-700">
              {{ formTranslation.patient_encounter.encounter_date }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <input
                type="date"
                id="encounter_date"
                v-model="formData.date"
                :min="minDate"
                :max="maxDate"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-500': submitted && $v.formData.date.$error }"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <i class="fa fa-calendar text-gray-400"></i>
              </div>
            </div>
            <div v-if="submitted && !$v.formData.date.required" class="text-red-500 text-sm mt-1">
              {{ formTranslation.patient_encounter.encounter_date_required }}
            </div>
          </div>

          <!-- Clinic Selection -->
          <div v-if="showClinicSelection">
            <label for="clinic_id" class="block text-sm font-medium text-gray-700">
              {{ formTranslation.clinic.select_clinic }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <select
                id="clinic_id"
                v-model="formData.clinic_id"
                @change="handleClinicChange"
                :disabled="clinicMultiselectLoader"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
                :class="{ 'border-red-500': submitted && !formData.clinic_id }"
              >
                <option value="">{{ formTranslation.patient_encounter.tag_select_clinic_plh }}</option>
                <option v-for="option in clinics" :key="option.id" :value="option.id">
                  {{ option.label }}
                </option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <i class="fa fa-chevron-down text-gray-400"></i>
              </div>
            </div>
            <div v-if="submitted && !formData.clinic_id" class="text-red-500 text-sm mt-1">
              {{ formTranslation.common.clinic_is_required }}
            </div>
          </div>

          <!-- Doctor Selection -->
          <div v-if="!isDoctorSelectionEnabled" class="mt-4 md:mt-0">
            <label for="doctor_id" class="block text-sm font-medium text-gray-700">
              {{ formTranslation.common.doctor }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <select
                id="doctor_id"
                v-model="formData.doctor_id"
                :disabled="doctorMultiselectLoader || !formData.clinic_id"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
                :class="{ 'border-red-500': submitted && !formData.doctor_id }"
              >
                <option value="">{{ formTranslation.patient_encounter.tag_select_doctor }}</option>
                <option v-for="doctor in doctors" :key="doctor.id" :value="doctor.id">
                  {{ doctor.label }}
                </option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <i class="fa fa-chevron-down text-gray-400"></i>
              </div>
            </div>
            <div v-if="submitted && !formData.doctor_id" class="text-red-500 text-sm mt-1">
              {{ formTranslation.appointments.doc_required }}
            </div>
          </div>

          <!-- Patient Selection -->
          <div v-if="patientField" class="mt-4 md:mt-0">
            <label for="patient_id" class="block text-sm font-medium text-gray-700">
              {{ formTranslation.common.patient }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <select
                id="patient_id"
                v-model="formData.patient_id"
                :disabled="patientMultiselectLoader || !formData.clinic_id"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
                :class="{ 'border-red-500': submitted && !formData.patient_id }"
              >
                <option value="">{{ formTranslation.patient_encounter.tag_patient_type_plh }}</option>
                <option v-for="patient in patients" :key="patient.id" :value="patient.id">
                  {{ patient.label }}
                </option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <i class="fa fa-chevron-down text-gray-400"></i>
              </div>
            </div>
            <div v-if="submitted && !formData.patient_id" class="text-red-500 text-sm mt-1">
              {{ formTranslation.patient_bill.patient_required }}
            </div>
          </div>

          <!-- Description -->
          <div class="col-span-1 md:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700">
              {{ formTranslation.appointments.description }}
            </label>
            <textarea
              id="description"
              v-model="formData.description"
              rows="3"
              maxlength="1000"
              class="block w-full mt-1 px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 resize-none"
              :placeholder="formTranslation.appointments.description"
            ></textarea>
            <div class="text-sm text-gray-500 mt-1">
              {{ formData.description.length }}/1000
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            @click="handleCancel"
            class="px-4 py-2 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            {{ formTranslation.common.cancel }}
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2"
            :class="[
              loading 
                ? 'bg-indigo-400 cursor-not-allowed' 
                : 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
            ]"
          >
            <i :class="loading ? 'fa fa-sync fa-spin mr-2' : 'fa fa-save mr-2'"></i>
            {{ loading ? formTranslation.common.loading : formTranslation.patient_encounter.save_btn }}
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import { required, requiredIf } from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import { validateForm } from "../../config/helper";
import moment from "moment";

export default {
  name: "CreateEncounter",

  props: {
    encounterId: {
      type: [Number, String],
      default: -1,
    },
    encounterData: {
      type: Object,
      default: () => null,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    patientField: {
      type: Boolean,
      default: true,
    },
    clinicField: {
      type: Boolean,
      default: true,
    },
    // New props for date constraints
    maxDaysInFuture: {
      type: Number,
      default: 30, // Allow bookings up to 30 days in future
    },
    maxDaysInPast: {
      type: Number,
      default: 7, // Allow bookings up to 7 days in past
    }
  },

  data() {
    return {
      formData: this.getDefaultFormData(),
      doctors: [],
      patients: [],
      clinics: [],
      loading: false,
      submitted: false,
      clinicMultiselectLoader: false,
      doctorMultiselectLoader: false,
      patientMultiselectLoader: false,
      patientRoleName: "patient",
      errorMessage: "",
      hasUnsavedChanges: false,
    };
  },

  validations() {
    return {
      formData: {
        date: { 
          required,
          validDate: (value) => {
            const date = moment(value);
            return date.isValid() && 
                   date.isBetween(this.minDate, this.maxDate, 'day', '[]');
          }
        },
        patient_id: {
          required: requiredIf(() => this.patientField),
        },
        doctor_id: {
          required: requiredIf(() => !this.isDoctorSelectionEnabled),
        },
        clinic_id: {
          required: requiredIf(() => this.showClinicSelection),
        },
        description: {
          maxLength: (value) => !value || value.length <= 1000
        }
      },
    };
  },

  computed: {
    showClinicSelection() {
      return (
        this.userData?.addOns?.kiviPro &&
        ["administrator", "doctor"].includes(this.getUserRole()) &&
        this.clinicField
      );
    },

    isDoctorSelectionEnabled() {
      return this.getUserRole() === "doctor";
    },

    userId() {
      return this.$store.state.userDataModule.user.ID;
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },

    activeClinicId() {
      return this.$store.state.activeClinicId || this.userData?.default_clinic_id;
    },

    minDate() {
      return moment().subtract(this.maxDaysInPast, 'days').format('YYYY-MM-DD');
    },

    maxDate() {
      return moment().add(this.maxDaysInFuture, 'days').format('YYYY-MM-DD');
    },

    hasFormErrors() {
      return this.$v.formData.$invalid;
    }
  },

  watch: {
    formData: {
      handler(newVal, oldVal) {
        if (oldVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.hasUnsavedChanges = true;
        }
      },
      deep: true
    }
  },

  async created() {
    // Add beforeunload event listener
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  },

  async mounted() {
    try {
      await this.initializeForm();
    } catch (error) {
      console.error('Mounting error:', error);
      this.handleError(error);
    }
  },

  beforeDestroy() {
    // Remove beforeunload event listener
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
  },

  methods: {
    getDefaultFormData() {
      return {
        date: moment().format('YYYY-MM-DD'),
        clinic_id: this.activeClinicId || "",
        doctor_id: this.getUserRole() === "doctor" ? this.userId : "",
        patient_id: "",
        description: "",
        added_by: this.userId,
        status: 1,
      };
    },

    handleBeforeUnload(e) {
      if (this.hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    },

    async initializeForm() {
      try {
        this.formData = this.getDefaultFormData();

        if (this.isEdit && this.encounterData) {
          await this.loadEditData();
        } else {
          await this.loadInitialData();
        }

        // Reset unsaved changes flag after successful initialization
        this.hasUnsavedChanges = false;
      } catch (error) {
        throw new Error('Failed to initialize form: ' + error.message);
      }
    },

    async loadEditData() {
      const { doctor_id, patient_id, clinic_id, encounter_date, description } = this.encounterData;

      this.formData = {
        ...this.formData,
        doctor_id: this.extractId(doctor_id),
        patient_id: this.extractId(patient_id),
        clinic_id: this.extractId(clinic_id),
        date: encounter_date ? moment(encounter_date).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD'),
        description: description || '',
      };

      if (this.formData.clinic_id) {
        await Promise.all([
          this.getUserRole() !== "doctor" && this.loadDoctorsForClinic(this.formData.clinic_id),
          this.getUserRole() !== "patient" && this.loadClinicPatients(this.formData.clinic_id)
        ]);
      }
    },

    async loadInitialData() {
      const loadTasks = [];
      
      if (this.getUserRole() !== "doctor") {
        loadTasks.push(this.loadDoctors());
      }
      
      if (this.getUserRole() !== "patient") {
        loadTasks.push(this.loadClinicPatients(this.activeClinicId));
      }

      await Promise.all(loadTasks);

      if (this.$route.params.patient_id) {
        this.formData.patient_id = this.$route.params.patient_id;
      }
    },

    extractId(value) {
      return typeof value === "object" ? value.id : value;
    },

    async loadDoctors() {
      try {
        this.doctorMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          clinic_id: this.activeClinicId
        });

        if (response.data.status) {
          this.doctors = response.data.data || [];
        }
      } catch (error) {
        throw new Error('Failed to load doctors: ' + error.message);
      } finally {
        this.doctorMultiselectLoader = false;
      }
    },

    async loadClinicPatients(clinicId) {
      try {
        this.patientMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: clinicId
        });

        if (response.data.status) {
          this.patients = response.data.data || [];
        }
      } catch (error) {
        throw new Error('Failed to load patients: ' + error.message);
      } finally {
        this.patientMultiselectLoader = false;
      }
    },

    async handleClinicChange(event) {
      try {
        const clinicId = event?.target?.value || event;
        if (!clinicId) return;

        this.formData.doctor_id = "";
        this.formData.patient_id = "";

        await Promise.all([
          this.getUserRole() !== "doctor" && this.loadDoctorsForClinic(clinicId),
          this.getUserRole() !== "patient" && this.loadClinicPatients(clinicId)
        ]);
      } catch (error) {
        this.handleError(error);
      }
    },

    async handleSubmit() {
      try {
        this.loading = true;
        this.submitted = true;
        this.errorMessage = "";

        this.$v.$touch();
        if (this.hasFormErrors) {
          return;
        }

        const submitData = this.prepareSubmitData();
        const response = await post(
          "patient_encounter_save", 
          submitData
        );

        if (response.data.status) {
          this.hasUnsavedChanges = false;
          displayMessage(response.data.message);
          this.$emit("encounterSaved", response.data.data);
          this.$emit("closeEncounterForm");
        } else {
          throw new Error(response.data.message);
        }
      } catch (error) {
        this.handleError(error);
      } finally {
        this.loading = false;
      }
    },

    prepareSubmitData() {
      const selectedClinic = this.clinics.find(c => c.id === this.formData.clinic_id);
      const selectedDoctor = this.doctors.find(d => d.id === this.formData.doctor_id);
      const selectedPatient = this.patients.find(p => p.id === this.formData.patient_id);

      return {
        ...this.formData,
        id: this.isEdit ? this.encounterId : undefined,
        encounter_date: moment(this.formData.date).format("YYYY-MM-DD"),
        clinic_id: this.formatSelection(selectedClinic),
        doctor_id: this.getUserRole() === "doctor" 
          ? this.formatSelection({ id: this.userId, label: this.userData?.display_name })
          : this.formatSelection(selectedDoctor),
        patient_id: this.formatSelection(selectedPatient)
      };
    },

    formatSelection(item) {
      return item ? {
        id: item.id,
        label: item.label || ""
      } : null;
    },

    handleCancel() {
      if (this.hasUnsavedChanges) {
        if (confirm(this.formTranslation.common.unsaved_changes)) {
          this.$emit("closeEncounterForm");
        }
      } else {
        this.$emit("closeEncounterForm");
      }
    },

    handleError(error) {
      console.error('Error:', error);
      this.errorMessage = error.message || this.formTranslation.common.internal_server_error;
      displayErrorMessage(this.errorMessage);
    }
  },
};
</script>
