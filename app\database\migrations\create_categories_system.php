<?php
/**
 * Migration: Create Categories System
 * This migration creates the new category system and updates the services table
 */

if (!defined('ABSPATH')) {
    exit;
}

class CreateCategoriesSystem {
    /**
     * Run the migration
     */
    public function up() {
        global $wpdb;
        
        // Create categories table
        $this->createCategoriesTable();
        
        // Add category_id to services table
        $this->addCategoryIdToServices();
        
        // Add visibility and sort_order to services table
        $this->addVisibilityToServices();
        
        // Migrate existing service categories from static_data
        $this->migrateServiceCategories();
    }
    
    /**
     * Create the categories table
     */
    private function createCategoriesTable() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'kc_categories';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            name varchar(191) NOT NULL,
            slug varchar(191) NOT NULL,
            description text NULL,
            module_type varchar(50) NOT NULL,
            parent_id bigint(20) NULL,
            visibility enum('public', 'backend_only', 'disabled') DEFAULT 'public',
            sort_order int(11) DEFAULT 0,
            status tinyint(1) DEFAULT 1,
            created_at datetime NOT NULL,
            updated_at datetime NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_slug_module (slug, module_type),
            INDEX module_type (module_type),
            INDEX visibility (visibility),
            INDEX status (status),
            INDEX parent_id (parent_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Add category_id column to services table
     */
    private function addCategoryIdToServices() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'kc_services';
        
        // Check if column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'category_id'");
        
        if (empty($column_exists)) {
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN `category_id` bigint(20) NULL AFTER `type`");
        }
    }
    
    /**
     * Add visibility and sort_order columns to services table
     */
    private function addVisibilityToServices() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'kc_services';
        
        // Check if visibility column exists
        $visibility_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'visibility'");
        if (empty($visibility_exists)) {
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN `visibility` enum('public', 'backend_only', 'disabled') DEFAULT 'public' AFTER `status`");
        }
        
        // Check if sort_order column exists
        $sort_order_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'sort_order'");
        if (empty($sort_order_exists)) {
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN `sort_order` int(11) DEFAULT 0 AFTER `visibility`");
        }
        
        // Check if updated_at column exists
        $updated_at_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'updated_at'");
        if (empty($updated_at_exists)) {
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN `updated_at` datetime NULL AFTER `created_at`");
        }
    }
    
    /**
     * Migrate existing service categories from static_data to new categories table
     */
    private function migrateServiceCategories() {
        global $wpdb;
        
        $static_data_table = $wpdb->prefix . 'kc_static_data';
        $categories_table = $wpdb->prefix . 'kc_categories';
        $services_table = $wpdb->prefix . 'kc_services';
        
        // Get all service types from static_data
        $service_types = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$static_data_table} WHERE type = %s AND status = 1",
            'service_type'
        ));
        
        $category_mapping = [];
        
        foreach ($service_types as $service_type) {
            // Generate slug
            $slug = sanitize_title($service_type->label);
            
            // Check if category already exists
            $existing_category = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$categories_table} WHERE slug = %s AND module_type = %s",
                $slug,
                'service'
            ));
            
            if (!$existing_category) {
                // Insert new category
                $category_data = [
                    'name' => $service_type->label,
                    'slug' => $slug,
                    'description' => '',
                    'module_type' => 'service',
                    'parent_id' => null,
                    'visibility' => 'public',
                    'sort_order' => 0,
                    'status' => $service_type->status,
                    'created_at' => $service_type->created_at,
                    'updated_at' => current_time('Y-m-d H:i:s')
                ];
                
                $wpdb->insert($categories_table, $category_data);
                $category_id = $wpdb->insert_id;
            } else {
                $category_id = $existing_category;
            }
            
            // Map old value to new category ID
            $category_mapping[$service_type->value] = $category_id;
        }
        
        // Update services table with category_id
        foreach ($category_mapping as $old_type => $category_id) {
            $wpdb->update(
                $services_table,
                ['category_id' => $category_id],
                ['type' => $old_type]
            );
        }
        
        // Handle services with unmapped types
        $unmapped_services = $wpdb->get_results("
            SELECT DISTINCT type FROM {$services_table} 
            WHERE category_id IS NULL AND type IS NOT NULL AND type != ''
        ");
        
        foreach ($unmapped_services as $service) {
            if ($service->type === 'system_service') {
                continue; // Skip system services
            }
            
            // Create category for unmapped service type
            $slug = sanitize_title($service->type);
            $name = ucwords(str_replace('_', ' ', $service->type));
            
            $category_data = [
                'name' => $name,
                'slug' => $slug,
                'description' => '',
                'module_type' => 'service',
                'parent_id' => null,
                'visibility' => 'public',
                'sort_order' => 0,
                'status' => 1,
                'created_at' => current_time('Y-m-d H:i:s'),
                'updated_at' => current_time('Y-m-d H:i:s')
            ];
            
            $wpdb->insert($categories_table, $category_data);
            $category_id = $wpdb->insert_id;
            
            // Update services
            $wpdb->update(
                $services_table,
                ['category_id' => $category_id],
                ['type' => $service->type]
            );
        }
    }
    
    /**
     * Reverse the migration (optional)
     */
    public function down() {
        global $wpdb;
        
        $categories_table = $wpdb->prefix . 'kc_categories';
        $services_table = $wpdb->prefix . 'kc_services';
        
        // Remove columns from services table
        $wpdb->query("ALTER TABLE {$services_table} DROP COLUMN IF EXISTS `category_id`");
        $wpdb->query("ALTER TABLE {$services_table} DROP COLUMN IF EXISTS `visibility`");
        $wpdb->query("ALTER TABLE {$services_table} DROP COLUMN IF EXISTS `sort_order`");
        $wpdb->query("ALTER TABLE {$services_table} DROP COLUMN IF EXISTS `updated_at`");
        
        // Drop categories table
        $wpdb->query("DROP TABLE IF EXISTS {$categories_table}");
    }
}