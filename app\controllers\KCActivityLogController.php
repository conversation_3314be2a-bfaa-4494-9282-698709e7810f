<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCActivityLog;

class KCActivityLogController extends KCBase {

    public $db;
    private $request;
    private $activityLogModel;
    private $table_name;

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        $this->activityLogModel = new KCActivityLog();
        $this->table_name = $wpdb->prefix . 'kc_activity_logs';
        
        parent::__construct();
    }

    /**
     * Get activity logs with pagination and filters
     * 
     * @return void JSON response
     */
    public function getActivityLogs() {
        global $wpdb;
        $request_data = $this->request->getInputs();
        
        // Ensure user is logged in
        if (!is_user_logged_in()) {
            $this->sendEmptyResponse('Not authorized');
            return;
        }
        
        // Setup pagination
        $page = isset($request_data['page']) ? max(1, (int)$request_data['page']) : 1;
        $per_page = isset($request_data['per_page']) ? max(1, (int)$request_data['per_page']) : 10;
        $offset = ($page - 1) * $per_page;
        
        // Get filters from request
        $filters = !empty($request_data['filters']) ? $request_data['filters'] : [];
        
        try {
            // Build query conditions based on user role and filters
            list($where_clause, $where_params, $join_clause) = $this->buildQueryConditions($filters);
            
            // Get total count for pagination
            $count_query = "SELECT COUNT(*) FROM {$this->table_name} AS l {$join_clause} {$where_clause}";
            if (!empty($where_params)) {
                $count_query = $wpdb->prepare($count_query, $where_params);
            }
            
            $total = (int)$wpdb->get_var($count_query);
            
            // If no results, return empty set
            if ($total === 0) {
                $this->sendSuccessResponse([], 0, $page, $per_page, 'No records found');
                return;
            }
            
            // Build query with pagination
            $query = "SELECT l.* FROM {$this->table_name} AS l {$join_clause} {$where_clause} ORDER BY l.created_at DESC LIMIT %d, %d";
            $all_params = array_merge($where_params, [$offset, $per_page]);
            
            // Prepare and execute query
            $query = $wpdb->prepare($query, $all_params);
            $logs = $wpdb->get_results($query);
            
            // Process logs to add user details
            $processed_logs = $this->processLogs($logs);
            
            // Return response
            $this->sendSuccessResponse(
                $processed_logs, 
                $total, 
                $page, 
                $per_page, 
                'Activity logs fetched successfully'
            );
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('KCActivityLogController Error: ' . $e->getMessage());
            $this->sendEmptyResponse('An error occurred while fetching logs: ' . $e->getMessage());
            return;
        }
    }
    
    /**
     * Build query conditions based on user role and filters
     * 
     * @param array $filters Request filters
     * @return array [where_clause, where_params, join_clause]
     */
    private function buildQueryConditions($filters = []) {
        global $wpdb;
        
        $where_conditions = [];
        $where_params = [];
        $join_clause = '';
        
        $user_role = $this->getLoginUserRole();
        
        // Add role-based filtering
        if ($user_role == $this->getReceptionistRole()) {
            $clinic_id = kcGetClinicIdOfReceptionist();
            if (!empty($clinic_id)) {
                $where_conditions[] = "l.clinic_id IS NOT NULL AND l.clinic_id = %d";
                $where_params[] = $clinic_id;
            }
        } 
        elseif ($user_role == $this->getDoctorRole()) {
            $clinic_id = kcGetClinicIdOfDoctor(true);
            if (!empty($clinic_id)) {
                $where_conditions[] = "l.clinic_id IS NOT NULL AND l.clinic_id = %d";
                $where_params[] = $clinic_id;
            }
        } 
        elseif ($user_role == $this->getPatientRole()) {
            $clinic_id = kcGetClinicIdOfPatient();
            if (!empty($clinic_id)) {
                $where_conditions[] = "l.clinic_id IS NOT NULL AND l.clinic_id = %d";
                $where_params[] = $clinic_id;
            }
        } 
        elseif ($user_role == $this->getClinicAdminRole()) {
            $clinic_id = kcGetClinicIdOfClinicAdmin();
            if (!empty($clinic_id)) {
                $where_conditions[] = "l.clinic_id IS NOT NULL AND l.clinic_id = %d";
                $where_params[] = $clinic_id;
            }
        }
        elseif ($user_role == 'administrator') {
            // Admin can see all logs, but filter by clinic name if provided
            if (!empty($filters['clinic_name'])) {
                $join_clause = "LEFT JOIN {$wpdb->prefix}kc_clinics c ON l.clinic_id = c.id";
                $where_conditions[] = "c.name LIKE %s";
                $where_params[] = '%' . sanitize_text_field($filters['clinic_name']) . '%';
            }
        }
        
        // Add activity type filter
        if (!empty($filters['activity_type'])) {
            $where_conditions[] = 'l.activity_type = %s';
            $where_params[] = sanitize_text_field($filters['activity_type']);
        }
        
        // Add user type filter
        if (!empty($filters['user_type'])) {
            $where_conditions[] = 'l.user_type = %s';
            $where_params[] = sanitize_text_field($filters['user_type']);
        }
        
        // Add user name filter
        if (!empty($filters['user_name'])) {
            $join_clause .= " LEFT JOIN {$wpdb->users} u ON l.user_id = u.ID";
            $where_conditions[] = "u.display_name LIKE %s";
            $where_params[] = '%' . sanitize_text_field($filters['user_name']) . '%';
        }
        
        // Add clinic name filter for non-admin roles
        if (!empty($filters['clinic_name']) && $user_role !== 'administrator') {
            if (strpos($join_clause, 'kc_clinics') === false) {
                $join_clause .= " LEFT JOIN {$wpdb->prefix}kc_clinics c ON l.clinic_id = c.id";
            }
            $where_conditions[] = "c.name LIKE %s";
            $where_params[] = '%' . sanitize_text_field($filters['clinic_name']) . '%';
        }
        
        // Add date filters
        if (!empty($filters['date_from'])) {
            $where_conditions[] = 'l.created_at >= %s';
            $where_params[] = sanitize_text_field($filters['date_from']) . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $where_conditions[] = 'l.created_at <= %s';
            $where_params[] = sanitize_text_field($filters['date_to']) . ' 23:59:59';
        }
        
        // Build WHERE clause
        $where_clause = !empty($where_conditions) 
            ? 'WHERE ' . implode(' AND ', $where_conditions) 
            : 'WHERE 1=1';
        
        return [$where_clause, $where_params, $join_clause];
    }
    
    /**
     * Process logs to add user and clinic details
     * 
     * @param array $logs Raw logs from database
     * @return array Processed logs with additional details
     */
    private function processLogs($logs) {
        global $wpdb;
        
        // Role name mapping for display
        $role_map = [
            'kivicare_clinic_admin' => 'Clinic Admin',
            'kivicare_doctor' => 'Doctor',
            'kivicare_patient' => 'Patient',
            'kivicare_receptionist' => 'Admin Staff',
            'clinic_admin' => 'Clinic Admin',
            'doctor' => 'Doctor',
            'patient' => 'Patient',
            'receptionist' => 'Admin Staff',
            'administrator' => 'Administrator'
        ];
        
        // Cache for users and clinics to avoid repeated DB calls
        $user_cache = [];
        $clinic_cache = [];
        
        $processed_logs = [];
        foreach ($logs as $log) {
            if (empty($log->user_id)) {
                $log->user_name = 'System';
                $log->display_role = 'System';
            } else {
                // Get user details (with caching)
                if (!isset($user_cache[$log->user_id])) {
                    $user_data = get_userdata($log->user_id);
                    $user_cache[$log->user_id] = $user_data ? $user_data->display_name : 'Unknown User';
                }
                $log->user_name = $user_cache[$log->user_id];
                
                // Map role names for display
                $log->display_role = isset($role_map[$log->user_type]) 
                    ? $role_map[$log->user_type] 
                    : $this->formatRoleName($log->user_type);
            }
            
            // Get clinic name (with caching)
            if (!empty($log->clinic_id)) {
                if (!isset($clinic_cache[$log->clinic_id])) {
                    $clinic = $wpdb->get_row($wpdb->prepare(
                        "SELECT name FROM {$wpdb->prefix}kc_clinics WHERE id = %d",
                        $log->clinic_id
                    ));
                    $clinic_cache[$log->clinic_id] = $clinic ? $clinic->name : 'Unknown Clinic';
                }
                $log->clinic_name = $clinic_cache[$log->clinic_id];
            } else {
                $log->clinic_name = 'System';
            }
            
            $processed_logs[] = $log;
        }
        
        return $processed_logs;
    }
    
    /**
     * Format role name for display
     * 
     * @param string $role Raw role name
     * @return string Formatted role name
     */
    private function formatRoleName($role) {
        if (empty($role)) {
            return 'System';
        }
        
        if (strpos($role, 'kivicare_') === 0) {
            $role = substr($role, 9);
        }
        return ucwords(str_replace('_', ' ', $role));
    }
    
    /**
     * Send empty response for errors
     * 
     * @param string $message Error message
     * @return void
     */
    private function sendEmptyResponse($message) {
        wp_send_json([
            'status' => false,
            'message' => $message,
            'data' => [
                'logs' => [],
                'total' => 0,
                'current_page' => 1,
                'total_pages' => 0,
                'per_page' => 10
            ]
        ]);
    }
    
    /**
     * Send success response with data
     * 
     * @param array $logs Processed logs
     * @param int $total Total count
     * @param int $page Current page
     * @param int $per_page Items per page
     * @param string $message Success message
     * @return void
     */
    private function sendSuccessResponse($logs, $total, $page, $per_page, $message) {
        wp_send_json([
            'status' => true,
            'message' => $message,
            'data' => [
                'logs' => $logs,
                'total' => (int) $total,
                'per_page' => (int) $per_page,
                'current_page' => (int) $page,
                'total_pages' => ceil($total / $per_page)
            ]
        ]);
    }
    
    /**
     * Log activity
     * 
     * @param int $user_id User ID
     * @param string $activity_type Type of activity
     * @param string $activity_description Description of activity
     * @param array $additional_data Additional data to log
     * 
     * @return int|bool Activity log ID or false on failure
     */
    public function logActivity($user_id, $activity_type, $activity_description, $additional_data = []) {
        return $this->activityLogModel->logActivity(
            $user_id, 
            sanitize_text_field($activity_type), 
            sanitize_text_field($activity_description), 
            $additional_data
        );
    }

    /**
     * Log activity from UI
     * 
     * @return int|bool Activity log ID or false on failure
     */
    public function logActivityFromUi() {
        $request_data = $this->request->getInputs();
        $user_id = isset($request_data['user_id']) ? (int)$request_data['user_id'] : 0;
        $activity_type = isset($request_data['activity_type']) ? sanitize_text_field($request_data['activity_type']) : '';
        $activity_description = isset($request_data['activity_description']) ? sanitize_text_field($request_data['activity_description']) : '';
        $additional_data = isset($request_data['additional_data']) ? $request_data['additional_data'] : '';
        
        return $this->activityLogModel->logActivity($user_id, $activity_type, $activity_description, $additional_data);
    }
    
    /**
     * Get activity types (for filtering)
     * 
     * @return void JSON response
     */
    public function getActivityTypes() {
        $activity_types = [
            'login' => esc_html__('Login', 'kc-lang'),
            'logout' => esc_html__('Logout', 'kc-lang'),
            'appointment_created' => esc_html__('Appointment Created', 'kc-lang'),
            'appointment_updated' => esc_html__('Appointment Updated', 'kc-lang'),
            'appointment_deleted' => esc_html__('Appointment Deleted', 'kc-lang'),
            'appointment_status_changed' => esc_html__('Appointment Status Changed', 'kc-lang'),
            'encounter_created' => esc_html__('Encounter Created', 'kc-lang'),
            'encounter_updated' => esc_html__('Encounter Updated', 'kc-lang'),
            'file_uploaded' => esc_html__('File Uploaded', 'kc-lang'),
            'file_downloaded' => esc_html__('File Downloaded', 'kc-lang'),
            'file_deleted' => esc_html__('File Deleted', 'kc-lang'),
            'prescription_created' => esc_html__('Prescription Created', 'kc-lang'),
            'prescription_updated' => esc_html__('Prescription Updated', 'kc-lang'),
            'prescription_sent' => esc_html__('Prescription Sent', 'kc-lang'),
            'patient_created' => esc_html__('Patient Created', 'kc-lang'),
            'patient_updated' => esc_html__('Patient Updated', 'kc-lang'),
            'summary_generated' => esc_html__('Summary Generated', 'kc-lang'),
            'report_generated' => esc_html__('Report Generated', 'kc-lang'),
            'data_shared' => esc_html__('Data Shared', 'kc-lang'),
            'bill_generated' => esc_html__('Bill Generated', 'kc-lang'),
            'payment_completed' => esc_html__('Payment Completed', 'kc-lang'),
            'communication_sent' => esc_html__('Communication Sent', 'kc-lang'),
            'profile_updated' => esc_html__('Profile Updated', 'kc-lang'),
            'mobile_upload_created' => esc_html__('Mobile Upload Session Created', 'kc-lang'),
            'ai_transcription_completed' => esc_html__('AI Transcription Completed', 'kc-lang'),
            'ai_analysis_completed' => esc_html__('AI Analysis Completed', 'kc-lang')
        ];
        
        // Allow plugins to add more activity types
        $activity_types = apply_filters('kc_activity_types', $activity_types);
        
        wp_send_json([
            'status' => true,
            'message' => esc_html__('Activity types fetched successfully', 'kc-lang'),
            'data' => $activity_types
        ]);
    }
}