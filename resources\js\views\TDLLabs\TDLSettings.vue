<template>
    <div class="w-full px-4">
        <div class="w-full">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-xl font-medium text-gray-800">{{ $t('TDL Labs Settings') }}</h4>
                    </div>
                    <div class="p-6">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <form v-else @submit.prevent="saveSettings">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="account_id" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('TDL Account ID') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="account_id"
                                        v-model="settings.account_id"
                                        required
                                    />
                                    <p class="mt-1 text-sm text-gray-500">{{ $t('Your TDL account identifier') }}</p>
                                </div>
                                <div>
                                    <label for="sender_id" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('Sender ID') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="sender_id"
                                        v-model="settings.sender_id"
                                        required
                                        maxlength="50"
                                    />
                                    <p class="mt-1 text-sm text-gray-500">{{ $t('Sender identification for TDL communications') }}</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                <div>
                                    <label for="api_key" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('API Key') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="password"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="api_key"
                                        v-model="settings.api_key"
                                        required
                                    />
                                    <p class="mt-1 text-sm text-gray-500">{{ $t('Your TDL API key for authentication') }}</p>
                                </div>
                                <div>
                                    <label for="auto_process_results" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('Auto Process Results') }}
                                    </label>
                                    <div class="mt-2 flex items-center">
                                        <button
                                            type="button"
                                            @click="settings.auto_process_results = !settings.auto_process_results"
                                            class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                            :class="[settings.auto_process_results ? 'bg-blue-600' : 'bg-gray-200']"
                                        >
                                            <span
                                                class="inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
                                                :class="[settings.auto_process_results ? 'translate-x-5' : 'translate-x-0']"
                                            ></span>
                                        </button>
                                        <span class="ml-3 text-sm font-medium text-gray-700">
                                            {{ settings.auto_process_results ? $t('Enabled') : $t('Disabled') }}
                                        </span>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">{{ $t('Automatically process new results from Azure') }}</p>
                                </div>
                            </div>

                            <div class="mt-8 border-t border-gray-200 pt-6">
                                <h5 class="text-lg font-medium text-gray-800">{{ $t('Azure Storage Configuration') }}</h5>
                                <p class="mt-1 text-sm text-gray-500">{{ $t('Configure Azure Blob Storage for HL7 message exchange') }}</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                <div>
                                    <label for="azure_storage_connection" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('Azure Connection String') }}
                                    </label>
                                    <textarea
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="azure_storage_connection"
                                        v-model="settings.azure_storage_connection"
                                        rows="3"
                                    ></textarea>
                                    <p class="mt-1 text-sm text-gray-500">{{ $t('Connection string for Azure Blob Storage') }}</p>
                                </div>
                                <div>
                                    <label for="azure_storage_container" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('Azure Container Name') }}
                                    </label>
                                    <input
                                        type="text"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="azure_storage_container"
                                        v-model="settings.azure_storage_container"
                                    />
                                    <p class="mt-1 text-sm text-gray-500">{{ $t('Blob storage container name') }}</p>
                                </div>
                            </div>

                            <div class="mt-8 border-t border-gray-200 pt-6">
                                <button
                                    type="submit"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    :disabled="saving"
                                >
                                    <i v-if="saving" class="fa fa-sync fa-spin mr-2"></i>
                                    <i v-else class="fa fa-save mr-2"></i>
                                    {{ saving ? $t('Saving...') : $t('Save Settings') }}
                                </button>
                                <button
                                    type="button"
                                    class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    :disabled="saving"
                                    @click="testConnection"
                                >
                                    <i v-if="testing" class="fa fa-sync fa-spin mr-2"></i>
                                    <i v-else class="fa fa-check-circle mr-2"></i>
                                    {{ testing ? $t('Testing...') : $t('Test Connection') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";
import { displayMessage, displayErrorMessage } from "../../utils/message";

export default {
    name: 'TDLSettings',
    data() {
        return {
            loading: true,
            saving: false,
            testing: false,
            settings: {
                clinic_id: 0,
                account_id: '',
                api_key: '',
                sender_id: '',
                azure_storage_connection: '',
                azure_storage_container: '',
                auto_process_results: true
            },
            currentClinic: null
        };
    },
    created() {
        this.getCurrentClinic();
    },
    methods: {
        getCurrentClinic() {
            // Get current clinic for the logged-in user
            this.$store.dispatch('getClinicList')
                .then(response => {
                    if (response.data && response.data.status && response.data.data && response.data.data.length > 0) {
                        this.currentClinic = response.data.data[0];
                        this.settings.clinic_id = this.currentClinic.id;
                        this.getSettings();
                    } else {
                        this.loading = false;
                        displayErrorMessage(this.$t('No clinic found for the current user.'));
                    }
                })
                .catch(error => {
                    this.loading = false;
                    console.error('Error getting clinic:', error);
                    displayErrorMessage(this.$t('Failed to load clinic data.'));
                });
        },
        getSettings() {
            this.loading = true;

            get('tdl_get_clinic_settings', {
                clinic_id: this.settings.clinic_id
            })
                .then(response => {
                    this.loading = false;

                    if (response.data.status === true && response.data.data) {
                        // Merge received settings with default settings
                        const receivedSettings = response.data.data;
                        this.settings = {
                            ...this.settings,
                            ...receivedSettings,
                            // Ensure clinic_id is set correctly
                            clinic_id: this.settings.clinic_id
                        };

                        // Convert auto_process_results from integer to boolean
                        this.settings.auto_process_results = !!parseInt(this.settings.auto_process_results);
                    }
                })
                .catch(error => {
                    this.loading = false;
                    console.error('Error getting settings:', error);

                    // Only show error if it's not a 404 (settings not found is expected for new setups)
                    if (error.response && error.response.status !== 404) {
                        displayErrorMessage(this.$t('Failed to load settings.'));
                    }
                });
        },
        saveSettings() {
            this.saving = true;

            // Convert boolean to integer for database storage
            const settingsToSave = {
                ...this.settings,
                auto_process_results: this.settings.auto_process_results ? 1 : 0
            };

            post('tdl_save_clinic_settings', settingsToSave)
                .then(response => {
                    this.saving = false;

                    if (response.data.status === true) {
                        displayMessage(this.$t('Settings saved successfully.'));
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to save settings.'));
                    }
                })
                .catch(error => {
                    this.saving = false;
                    console.error('Error saving settings:', error);
                    displayErrorMessage(this.$t('Failed to save settings. Please try again.'));
                });
        },
        testConnection() {
            if (!this.settings.account_id || !this.settings.api_key) {
                displayErrorMessage(this.$t('Account ID and API Key are required to test the connection.'));
                return;
            }

            this.testing = true;

            // Create test payload
            const testData = {
                account_id: this.settings.account_id,
                api_key: this.settings.api_key,
                sender_id: this.settings.sender_id
            };

            post('tdl_test_connection', testData)
                .then(response => {
                    this.testing = false;

                    if (response.data.status === true) {
                        displayMessage(this.$t('Connection successful! TDL API credentials are valid.'));
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Connection failed. Please check your credentials.'));
                    }
                })
                .catch(error => {
                    this.testing = false;
                    console.error('Error testing connection:', error);

                    let errorMessage = this.$t('Connection failed. Please check your credentials.');
                    if (error.response && error.response.data && error.response.data.message) {
                        errorMessage = error.response.data.message;
                    }

                    displayErrorMessage(errorMessage);
                });
        }
    }
};
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>