<?php
include_once dirname(dirname(dirname(dirname(dirname(__FILE__))))) . '/wp-load.php';
global $wpdb;
$table_name = $wpdb->prefix . 'kc_clinic_sessions';
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'service_id'");
echo 'Column exists: ' . (\!empty($column_exists) ? 'Yes' : 'No') . PHP_EOL;
if (empty($column_exists)) {
    echo 'Adding column...' . PHP_EOL;
    $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN  bigint(20) DEFAULT NULL AFTER ");
    echo 'Column added.' . PHP_EOL;
}
?>
