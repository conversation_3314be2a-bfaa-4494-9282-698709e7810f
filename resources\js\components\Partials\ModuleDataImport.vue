<!-- ModuleDataImport.vue -->
<template>
  <div>
    <button @click="openDataImportModal"
      class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 ease-in-out group">
      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
        <polyline points="17 8 12 3 7 8"></polyline>
        <line x1="12" x2="12" y1="3" y2="15"></line>
      </svg>
      Import
    </button>
    <div v-if="isImportModalOpen">
      <!-- Modal Backdrop -->
      <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40" @click="handleDataImportModalClose"></div>

      <!-- Modal Content -->
      <div class="fixed inset-0 z-50 overflow-y-auto" role="dialog" aria-modal="true">
        <div class="flex min-h-screen items-center justify-center p-4">
          <div class="w-full max-w-2xl bg-white rounded-xl shadow-xl">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 class="text-xl font-semibold text-gray-900">
                {{ moduleName }} {{ formTranslation.common.import }}
              </h3>
              <button @click="handleDataImportModalClose" class="text-gray-400 hover:text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
              <!-- Import Form -->
              <form v-if="!isImportDone" @submit.prevent="handleSubmit" class="space-y-6">
                <!-- File Type and Upload Section -->
                <div class="grid grid-cols-3 gap-6">
                  <!-- File Type Selection -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">
                      {{ formTranslation.static_data.tag_select_type_plh }}
                    </label>
                    <select v-model="uploadFileDetail.type" @change="handleTypeChange"
                      class="w-full rounded-lg px-4 py-2 border border-full border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                      <option value="csv">CSV</option>
                      <option value="xls">XLS</option>
                    </select>
                  </div>

                  <!-- File Upload -->
                  <div class="space-y-2 col-span-2">
                    <label class="block text-sm font-medium text-gray-700">
                      {{ formTranslation.common.upload_file }}
                    </label>
                    <div class="flex">
                      <button type="button" @click="handleFileUpload" :disabled="isFileUploading"
                        class="flex-none px-4 py-2 bg-black text-white rounded-l-lg hover:bg-black disabled:opacity-50">
                        {{
                          isFileUploading
                            ? formTranslation.common.loading
                            : formTranslation.common.choose_file
                        }}
                      </button>
                      <div
                        class="flex-1 px-3 py-2 bg-gray-50 border border-l-0 border-gray-300 rounded-r-lg text-sm text-gray-500 truncate">
                        {{
                          uploadFileDetail.name ||
                          formTranslation.common.no_file_chosen
                        }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Sample File Link -->
                <div v-if="sampleFiles[uploadFileDetail.type]" class="text-sm">
                  <a :href="sampleFiles[uploadFileDetail.type]" class="text-blue-600 hover:text-blue-700"
                    target="_blank" :download="moduleType + '.' + uploadFileDetail.type">
                    {{ formTranslation.common.lbl_download_sample_file }}
                  </a>
                </div>

                <!-- Required Fields -->
                <div v-if="requiredData.length" class="space-y-2">
                  <h4 class="font-medium text-gray-900">
                    {{ formTranslation.common.lbl_required_field }}
                  </h4>
                  <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
                    <li v-for="field in requiredData" :key="field.value">
                      {{ field.label }}
                    </li>
                  </ul>
                </div>

                <!-- Notification Options -->
                <div v-if="showNotificationOptions" class="space-y-3">
                  <h4 class="font-medium text-gray-900">
                    {{
                      formTranslation.common.send_notification_when_user_register
                    }}
                  </h4>
                  <div class="flex gap-4">
                    <label class="inline-flex items-center">
                      <input type="checkbox" v-model="uploadFileDetail.email"
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span class="ml-2 text-sm text-gray-700">{{
                        formTranslation.common.email
                        }}</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input type="checkbox" v-model="uploadFileDetail.sms"
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span class="ml-2 text-sm text-gray-700">{{
                        formTranslation.common.message
                        }}</span>
                    </label>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end gap-3 pt-4 border-t">
                  <button type="submit" :disabled="isSubmitting || !uploadFileDetail.url"
                    class="px-4 py-2 bg-black text-white rounded-lg hover:bg-black focus:ring-2 focus:ring-black focus:ring-offset-2 disabled:opacity-50 flex items-center gap-2">
                    <i :class="[
                      'fa',
                      isSubmitting ? 'fa-spinner fa-spin' : 'fa-save',
                    ]"></i>
                    {{
                      isSubmitting
                        ? formTranslation.common.loading
                        : formTranslation.common.save
                    }}
                  </button>
                  <button type="button" @click="handleDataImportModalClose"
                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                    {{ formTranslation.common.cancel }}
                  </button>
                </div>
              </form>

              <!-- Import Results -->
              <div v-else class="space-y-6">
                <div class="grid grid-cols-2 gap-4">
                  <div class="p-4 bg-gray-50 rounded-lg">
                    <p class="flex justify-between text-sm">
                      <span class="font-medium">{{ formTranslation.common.total_rows }}:</span>
                      <span>{{ totalRows }}</span>
                    </p>
                  </div>
                  <div class="p-4 bg-gray-50 rounded-lg">
                    <p class="flex justify-between text-sm">
                      <span class="font-medium">{{ formTranslation.common.total_rows_inserted }}:</span>
                      <span>{{ totalRowsInserted }}</span>
                    </p>
                  </div>
                </div>

                <div class="flex justify-center gap-3">
                  <button @click="resetForm" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    {{ formTranslation.common.import_more_file }}
                  </button>
                  <button @click="handleDataImportModalClose"
                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                    {{ formTranslation.common.close }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";

export default {
  name: "ModuleDataImport",

  props: {
    moduleType: {
      type: String,
      required: true,
    },
    moduleName: {
      type: String,
      required: true,
    },
    requiredData: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      isImportModalOpen: false,
      uploadFileDetail: {
        name: "",
        type: "csv",
        url: "",
        id: "",
        module_type: "",
        sms: false,
        email: false,
      },
      isFileUploading: false,
      isSubmitting: false,
      sampleFiles: {
        csv: "",
        xls: "",
      },
      totalRows: 0,
      totalRowsInserted: 0,
      isImportDone: false,
      detailReport: {},
    };
  },

  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    showNotificationOptions() {
      return ["clinic", "doctor", "patient", "receptionist"].includes(
        this.moduleType
      );
    },
  },

  created() {
  },

  mounted() {
    this.uploadFileDetail.module_type = this.moduleType;
    if (this.formTranslation?.common?.no_file_chosen) {
      this.uploadFileDetail.name = this.formTranslation.common.no_file_chosen;
    }
    this.fetchSampleFiles();
  },

  methods: {
    openDataImportModal() {
      this.isImportModalOpen = true;
    },

    async handleFileUpload() {
      if (!this.userData.addOns?.kiviPro) {
        return;
      }

      this.isFileUploading = true;

      try {
        // Check if kivicareCustomImageUploader is defined globally
        if (typeof kivicareCustomImageUploader !== 'function') {
          console.error("kivicareCustomImageUploader is not defined");
          this.isFileUploading = false;
          this.displayErrorMessage(this.formTranslation?.common?.internal_server_error || "Internal server error");
          return;
        }

        const uploader = await kivicareCustomImageUploader(
          this.formTranslation,
          this.uploadFileDetail.type
        );

        uploader.on("select", () => {
          const attachment = uploader.state().get("selection").first().toJSON();
          this.uploadFileDetail.name = attachment.filename;
          this.uploadFileDetail.url = attachment.url;
          this.uploadFileDetail.id = attachment.id;
          this.isFileUploading = false;
        });

        uploader.on("close", () => {
          this.isFileUploading = false;
        });

        uploader.open();
      } catch (error) {
        console.error("File upload error:", error);
        this.isFileUploading = false;
        this.displayErrorMessage(this.formTranslation?.common?.internal_server_error || "Internal server error");
      }
    },

    handleTypeChange() {
      this.uploadFileDetail.id = "";
      this.uploadFileDetail.url = "";
      this.uploadFileDetail.name = this.formTranslation?.common?.no_file_chosen || "No file chosen";
    },

    handleDataImportModalClose() {
      this.isImportModalOpen = false;
      this.resetForm();
    },

    resetForm() {
      this.uploadFileDetail = {
        name: this.formTranslation?.common?.no_file_chosen || "No file chosen",
        type: "csv",
        url: "",
        id: "",
        module_type: this.moduleType,
        sms: false,
        email: false,
      };
      this.isImportDone = false;
      this.isFileUploading = false;
      this.detailReport = {};
    },

    async handleSubmit() {
      if (!this.uploadFileDetail.url) {
        this.displayErrorMessage(this.formTranslation?.common?.no_file_chosen || "No file chosen");
        return;
      }

      try {
        this.isSubmitting = true;
        const response = await get("import_module_data", {
          ...this.uploadFileDetail,
          required_field: this.requiredData,
        });

        if (response?.data?.status) {
          this.totalRows = response.data.total_row || 0;
          this.totalRowsInserted = response.data.total_data_insert || 0;
          this.detailReport = response.data.detail_report || {};
          this.isImportDone = true;
          this.$emit("reloadList");
          this.displayMessage(response.data.message);
        } else {
          this.displayErrorMessage(response?.data?.message || this.formTranslation?.common?.error_message || "Error occurred");
        }
      } catch (error) {
        console.error("Submit error:", error);
        this.displayErrorMessage(this.formTranslation?.common?.internal_server_error || "Internal server error");
      } finally {
        this.isSubmitting = false;
      }
    },

    async fetchSampleFiles() {
      try {
        const response = await get("import_demo_files", {
          module_type: this.moduleType,
        });

        if (response?.data?.status) {
          this.sampleFiles = response.data.data || { csv: "", xls: "" };
        }
      } catch (error) {
        console.error("Error fetching sample files:", error);
      }
    },

    // Helper methods for displaying messages
    displayMessage(message) {
      // Check if global function exists
      if (typeof displayMessage === 'function') {
        displayMessage(message);
      } else if (this.$toast) {
        this.$toast.success(message);
      } else {
        console.log('Success:', message);
      }
    },

    displayErrorMessage(message) {
      // Check if global function exists
      if (typeof displayErrorMessage === 'function') {
        displayErrorMessage(message);
      } else if (this.$toast) {
        this.$toast.error(message);
      } else {
        console.error('Error:', message);
      }
    }
  },
};
</script>