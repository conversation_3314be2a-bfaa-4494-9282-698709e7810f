<?php

function kivicare_add_time_to_clinic_schedule() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'kc_clinic_schedule';
    
    // Check if columns already exist
    $check_start_time = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'start_time'");
    $check_end_time = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'end_time'");
    $check_all_day = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'all_day'");
    
    // Add start_time column if it doesn't exist
    if (empty($check_start_time)) {
        $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN start_time time NULL AFTER start_date");
    }
    
    // Add end_time column if it doesn't exist
    if (empty($check_end_time)) {
        $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN end_time time NULL AFTER end_date");
    }
    
    // Add all_day column if it doesn't exist
    if (empty($check_all_day)) {
        $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN all_day tinyint(1) DEFAULT 1 AFTER end_time");
    }
}

// Run the migration
kivicare_add_time_to_clinic_schedule();