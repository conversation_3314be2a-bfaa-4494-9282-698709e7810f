<template>
  <div
    v-if="show"
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true"
  >
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
      >
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-12">
          <svg
            class="animate-spin h-8 w-8 text-gray-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>

        <div v-else>
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="w-full">
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                    {{ template.name }}
                  </h3>
                  <div class="flex items-center space-x-2">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="{
                        'bg-blue-100 text-blue-800': template.category === 'general',
                        'bg-green-100 text-green-800': template.category === 'referral',
                        'bg-yellow-100 text-yellow-800': template.category === 'sick_note',
                        'bg-purple-100 text-purple-800': template.category === 'consultation',
                        'bg-pink-100 text-pink-800': template.category === 'procedure'
                      }"
                    >
                      {{ template.category_label }}
                    </span>
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="{
                        'bg-gray-100 text-gray-800': template.share_status === 'private',
                        'bg-indigo-100 text-indigo-800': template.share_status === 'clinic',
                        'bg-green-100 text-green-800': template.share_status === 'public'
                      }"
                    >
                      {{ template.share_status === 'private' ? $t('Private') : 
                         template.share_status === 'clinic' ? $t('Clinic') : $t('Public') }}
                    </span>
                    <span
                      v-if="template.is_system"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {{ $t("System") }}
                    </span>
                  </div>
                </div>

                <div class="text-sm text-gray-500 mb-4">
                  <span v-if="template.doctor_name">
                    {{ $t("Created by") }}: {{ template.doctor_name }}
                  </span>
                  <span v-if="template.clinic_name">
                    {{ template.doctor_name ? ' | ' : '' }}{{ $t("Clinic") }}: {{ template.clinic_name }}
                  </span>
                  <span v-if="template.created_at">
                    {{ (template.doctor_name || template.clinic_name) ? ' | ' : '' }}
                    {{ $t("Created") }}: {{ formatDate(template.created_at) }}
                  </span>
                </div>

                <div class="bg-gray-100 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <div v-html="template.content"></div>
                </div>

                <div class="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-gray-700 mb-2">{{ $t("Template Variables") }}</h4>
                  
                  <div v-if="!detectVariables(template.content).length" class="py-3 text-center">
                    <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="mt-1 text-sm text-gray-500">{{ $t("No variables detected in this template") }}</p>
                  </div>
                  
                  <div v-else>
                    <p class="text-sm text-gray-600 mb-3">
                      {{ $t("This template uses the following variables that will be replaced with actual data:") }}
                    </p>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                      <div 
                        v-for="(variable, index) in detectVariables(template.content)" 
                        :key="index"
                        class="flex items-center px-2 py-1.5 bg-blue-50 border border-blue-100 rounded-md"
                      >
                        <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                        <code class="text-xs font-medium text-blue-700">${{ variable }}</code>
                      </div>
                    </div>
                    
                    <div class="mt-3 text-sm text-gray-500 flex items-start">
                      <svg class="flex-shrink-0 h-5 w-5 text-gray-400 mt-0.5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>
                        {{ $t("These variables will be automatically replaced with patient, doctor, clinic and encounter information when the template is used.") }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-500 text-base font-medium text-white hover:bg-blue-600 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
              @click="cloneTemplate"
            >
              {{ $t("Clone") }}
            </button>
            <button
              v-if="template.is_editable && !template.is_system"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="editTemplate"
            >
              {{ $t("Edit") }}
            </button>
            <button
              v-if="template.is_editable && !template.is_system"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="confirmDelete"
            >
              {{ $t("Delete") }}
            </button>
            <button
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="$emit('close')"
            >
              {{ $t("Close") }}
            </button>
          </div>
          
          <!-- Delete Confirmation Modal -->
          <div v-if="showDeleteModal" class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>
              <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
              <div
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
              >
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div class="sm:flex sm:items-start">
                    <div
                      class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10"
                    >
                      <svg
                        class="h-6 w-6 text-red-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                        />
                      </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                      <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $t("Delete Template") }}</h3>
                      <div class="mt-2">
                        <p class="text-sm text-gray-500">
                          {{ $t("Are you sure you want to delete this template? This action cannot be undone.") }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                    @click="deleteTemplate"
                  >
                    {{ $t("Delete") }}
                  </button>
                  <button
                    type="button"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    @click="cancelDelete"
                  >
                    {{ $t("Cancel") }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import { get, post } from '../../config/request';

export default {
  props: {
    show: {
      type: Boolean,
      required: true
    },
    templateId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      isLoading: true,
      showDeleteModal: false,
      template: {
        id: null,
        name: "",
        category: "",
        content: "",
        share_status: "",
        doctor_name: "",
        clinic_name: "",
        created_at: "",
        is_system: false,
        is_editable: false
      }
    };
  },
  watch: {
    templateId: {
      immediate: true,
      handler() {
        if (this.templateId) {
          this.loadTemplate();
        }
      }
    }
  },
  methods: {
    async loadTemplate() {
      this.isLoading = true;

      get('get_template_details', {id: this.templateId})
        .then(response => {
          console.log('md_template_details:', response);
          if (response.data.status) {
            this.template = response.data.data;
          }
        })
        .catch(error => {
          console.error('Error fetching template details:', error);
          displayErrorMessage(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    formatDate(dateString) {
      const options = { year: "numeric", month: "short", day: "numeric" };
      return new Date(dateString).toLocaleDateString(undefined, options);
    },

    detectVariables(content) {
      if (!content) return [];
      
      // Find all ${variable} patterns in the content
      const variableRegex = /\${([^}]+)}/g;
      const matches = content.match(variableRegex) || [];
      
      // Extract variable names and remove duplicates
      return [...new Set(matches.map(match => match.replace(/\${|}/, "")))];
    },

    editTemplate() {
      this.$emit("edit", this.template);
    },

    cloneTemplate() {
      this.$emit("clone", this.template.id);
    },
    
    confirmDelete() {
      this.showDeleteModal = true;
    },
    
    cancelDelete() {
      this.showDeleteModal = false;
    },
    
    async deleteTemplate() {
      try {
        // Get the nonce if available
        let ajaxNonce = '';
        try {
          ajaxNonce = window.kivicare?.ajax_nonce || window.wp_rest?.nonce || '';
        } catch (e) {
          console.warn("Failed to get nonce:", e);
        }
        
        // Use fetch with more control over the request
        const fetchOptions = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'same-origin',
          body: JSON.stringify({
            id: this.template.id,
            _ajax_nonce: ajaxNonce
          })
        };
        
        // Add nonce header if available
        if (ajaxNonce) {
          fetchOptions.headers['X-WP-Nonce'] = ajaxNonce;
        }
        
        const response = await fetch("/wp-json/kivicare/api/v1/delete_md_template", fetchOptions);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.status) {
          displayMessage(data.message || "Template deleted successfully");
          this.cancelDelete();
          this.$emit('delete', this.template.id);
          this.$emit('close');
        } else {
          throw new Error(data.message || "Failed to delete template");
        }
      } catch (error) {
        console.error("Error deleting template:", error);
        displayErrorMessage(error.message || "Error deleting template");
        this.cancelDelete();
      }
    }
  }
};
</script>