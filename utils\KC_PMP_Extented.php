<?php

use App\models\KCClinic;

class KC_PMP_Extented extends \App\baseClasses\KCBase
{
    public function __construct()
    {
        add_action('pmpro_after_checkout', array($this, 'assign_clinic_admin_role'));
        add_action('pmpro_after_change_membership_level', array($this, 'update_dashboard_capability'), 10, 2);
        add_action('admin_page_access_denied', array($this, 'show_msg_to_unsubscribe_user'), 10, 2);

        add_action('kc_doctor_assign_to_clinic', array($this, 'update_subscribe_price'));
        add_action('kc_doctor_delete_after', array($this, 'exlude_doctor_subscribe_price'),10,2);

        add_filter('pmpro_pages_custom_template_path', array($this, 'pmpro_pages_custom_template_path'), 10, 2);

        add_filter('pmpro_confirmation_url', array($this, 'pmpro_confirmation_url'), 10, 2);
        add_action('kcpro_clinic_save', array($this, 'clinic_save'), 10, 2);

        add_filter('pmpro_checkout_level', array($this, 'add_extra_price_at_checkout'), 10, 1);

        add_action('wp_enqueue_scripts', array($this, 'wp_enqueue_scripts'));

        add_action('wp', fn() => remove_filter('pmpro_include_payment_information_fields', array(
            'PMProGateway_stripe',
            'pmpro_include_payment_information_fields'
        )));

        add_filter('pmpro_include_payment_information_fields', array($this, 'pmpro_include_payment_information_fields'));
    }

    /**
     * Use our own payment fields at checkout. (Remove the name attributes.)
     * @since 1.8
     */
    public  function pmpro_include_payment_information_fields($include)
    {
        //global vars
        global $pmpro_requirebilling, $pmpro_show_discount_code, $discount_code, $CardType, $AccountNumber, $ExpirationMonth, $ExpirationYear;

        //include ours
?>
        <fieldset id="pmpro_payment_information_fields" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_fieldset', 'pmpro_payment_information_fields')); ?>" <?php if (! $pmpro_requirebilling || apply_filters("pmpro_hide_payment_information_fields", false)) { ?>style="display: none;" <?php } ?>>
            <div class="">
                <div class="border rounded-lg p-4">
                    <?php
                    if (get_option('pmpro_stripe_payment_request_button')) { ?>
                        <div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_payment-request-button', 'pmpro_payment-request-button')); ?>">
                            <div id="payment-request-button"><!-- Alternate payment method will be inserted here. --></div>
                            <h3 class="font-medium mb-4 flex items-center m-0 p-0 ">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-credit-card w-5 h-5 mr-2">
                                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                    <line x1="2" x2="22" y1="10" y2="10"></line>
                                </svg>
                                <span>
                                    <?php
                                    echo esc_html(pmpro_is_checkout() ? __('Pay with Credit Card', 'paid-memberships-pro') : __('Credit Card', 'paid-memberships-pro'));
                                    ?>
                                </span>

                            </h3>
                        </div>
                    <?php
                    }
                    ?>
                    <div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_fields')); ?>">
                        <input type="hidden" id="CardType" name="CardType"
                            value="<?php echo esc_attr($CardType); ?>" />
                        <div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_payment-account-number', 'pmpro_payment-account-number')); ?>">
                            <label for="AccountNumber" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Card Number', 'paid-memberships-pro'); ?></label>
                            <div id="AccountNumber"></div>
                        </div>
                        <div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_cols-2')); ?>">
                            <div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_payment-expiration', 'pmpro_payment-expiration')); ?>">
                                <label for="Expiry" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('Expiration Date', 'paid-memberships-pro'); ?></label>
                                <div id="Expiry"></div>
                            </div>
                            <?php
                            $pmpro_show_cvv = apply_filters("pmpro_show_cvv", true);
                            if ($pmpro_show_cvv) { ?>
                                <div class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_field pmpro_payment-cvv', 'pmpro_payment-cvv')); ?>">
                                    <label for="CVV" class="<?php echo esc_attr(pmpro_get_element_class('pmpro_form_label')); ?>"><?php esc_html_e('CVC', 'paid-memberships-pro'); ?></label>
                                    <div id="CVV"></div>
                                </div>
                            <?php } ?>
                        </div> <!-- end pmpro_cols-2 -->

                    </div> <!-- end pmpro_form_fields -->
                </div> <!-- end pmpro_card_content -->
            </div> <!-- end pmpro_card -->
        </fieldset> <!-- end pmpro_payment_information_fields -->
<?php

        //don't include the default
        return false;
    }
    public function wp_enqueue_scripts()
    {
        if (function_exists('pmpro_is_checkout') && pmpro_is_checkout()) {
            wp_enqueue_script('kc-tailwind', 'https://cdn.tailwindcss.com');
        }
    }
    public function add_extra_price_at_checkout($level)
    {
        if ($this->getLoginUserRole() != $this->getClinicAdminRole()) return $level;

        // Define your additional price (e.g., $10 extra)
        $KCClinic = (new KCClinic)->get_by(['clinic_admin_id' => (int)get_current_user_id()], '=', true);

        // Ensure Stripe is the active gateway
        $subscription_settings = get_option(KIVI_CARE_PREFIX . 'subscription_settings');

        $extra_price = $KCClinic->allow_no_of_doc * ($subscription_settings['doctorPrice'] ?? 10); // Add $10;

        // Add the extra price to the current billing amount
        $level->billing_amount += $extra_price;
        $level->initial_payment += $extra_price;

        // Optionally, display a message or label for the additional cost
        add_action('pmpro_checkout_after_level_cost', function () use ($extra_price) {
            echo "<p><strong>Note:</strong> An additional charge of $$extra_price has been added for extra services.</p>";
        });

        return $level;
    }
    public function clinic_save($clinic_id)
    {
        $KCClinic = (new KCClinic)->get_by(['id' => (int)$clinic_id], '=', true);
        $KCClinic->clinic_admin_id;
        if ($KCClinic->clinic_admin_id) {
            if ($clinic_admin = get_user_to_edit($KCClinic->clinic_admin_id)) {
                $clinic_admin->remove_cap(KIVI_CARE_PREFIX . 'dashboard');
            }
        }
    }
    public function exlude_doctor_subscribe_price($id,$clinics_id)
    {
        foreach ($clinics_id as $key => $clinic_id) {
            $this->update_subscribe_price(['clinic_id' => $clinic_id]);
        }
    }
    public function pmpro_confirmation_url($url)
    {

        global $wpdb;

        // Prepare the SQL query to search for the shortcode in page content
        $query = $wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts}
            WHERE post_content LIKE %s
            AND post_status = 'publish'
            AND post_type = 'page'
            ",
            '%' . $wpdb->esc_like("[kivicareClinicRegister]") . '%'
        );

        // Execute the query and get the results
        $page_id = $wpdb->get_var($query);

        if (!$page_id) return $url;

        return add_query_arg(['payment-success' => 1], get_permalink($page_id));
    }
    public function pmpro_pages_custom_template_path($templates, $page_name)
    {
        if ($page_name == 'checkout') {
            $templates[] = KIVI_CARE_DIR . 'templates/pmp/pages/checkout.php';
        }
        return $templates;
    }
    public function update_subscribe_price(array $data): void
    {
        $clinic_all_doctors = count((new \App\models\KCDoctorClinicMapping())->get_by(['clinic_id' => $data['clinic_id']]));
        if($clinic_all_doctors>1){
            $clinic_all_doctors=$clinic_all_doctors-1;
        }
        $KCClinic = (new KCClinic)->get_by(['id' => (int)$data['clinic_id']], '=', true);

        // Ensure Stripe is the active gateway
        $active_plan = pmpro_getMembershipLevelForUser($KCClinic->clinic_admin_id);
      
        $subscription_settings = get_option(KIVI_CARE_PREFIX . 'subscription_settings');
        if (pmpro_getOption('gateway') !== 'stripe' || !$active_plan) {
            return;
        }

        // Load the Stripe PHP SDK (Ensure it's properly included in your project)
        require_once PMPRO_DIR . '/includes/lib/Stripe/init.php';

        if (PMProGateway_stripe::using_api_keys()) {
            $secret_key = get_option("pmpro_stripe_secretkey");
        } elseif ($livemode) {
            $secret_key = get_option('pmpro_live_stripe_connect_secretkey');
        } else {
            $secret_key = get_option('pmpro_sandbox_stripe_connect_secretkey');
        }
        // Set Stripe API Key
        \Stripe\Stripe::setApiKey($secret_key);

        // Get the subscription ID from the PMPro database
        global $wpdb;
        $subscription_id = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT subscription_transaction_id 
             FROM {$wpdb->prefix}pmpro_subscriptions 
             WHERE user_id = %d AND status = 'active'",
                $KCClinic->clinic_admin_id
            )
        );

        if (empty($subscription_id)) {
            return; // No active subscription found
        }

        try {
            // Retrieve the subscription from Stripe
            $subscription = \Stripe\Subscription::retrieve($subscription_id);

            $extra_price = 0;
            // Update the subscription price for the next cycle
            if ($clinic_all_doctors >= $KCClinic->allow_no_of_doc) {
                $extra_price = ($clinic_all_doctors * ($subscription_settings['doctorPrice'] ?? 10));
            }

            $new_price_amount = pmpro_getLevel($active_plan->id)->billing_amount + $extra_price;



            // Create a new price in Stripe for the updated amount
            $new_price = \Stripe\Price::create([
                'unit_amount' => $new_price_amount * 100, // In cents
                'currency' => $subscription->items->data[0]->price->currency,
                'recurring' => [
                    'interval' => $subscription->items->data[0]->price->recurring->interval,
                ],
                'product' => $subscription->items->data[0]->price->product,
            ]);

            // Update the subscription with the new price
            $subscription->items->data[0]->id;
            \Stripe\Subscription::update($subscription_id, [
                'items' => [
                    [
                        'id' => $subscription->items->data[0]->id,
                        'price' => $new_price->id,
                    ],
                ],
            ]);



            if (!is_null($KCClinic->clinic_admin_id)) {
                $where_conditions['user_id'] = $KCClinic->clinic_admin_id;
                $where_format[] = '%d';
            }
            
            // Update the billing amount
            $result = $wpdb->update(
                $wpdb->prefix . 'pmpro_memberships_users',
                array('billing_amount' => $new_price_amount),
                $where_conditions,
                array('%f'),
                $where_format
            );

            // Log or notify on success
            error_log("Updated subscription for user {$KCClinic->clinic_admin_id}: New price is $new_price_amount.");
        } catch (Exception $e) {
            // Handle errors
            error_log("Error updating subscription for user {$KCClinic->clinic_admin_id}: " . $e->getMessage());
        }
    }

    public function assign_clinic_admin_role($user_id): void
    {
        // Get the user object
        $user = get_user_by('id', $user_id);
        if ($user) {
            // Add the clinic_admin role And Base On Subcription Add Capability of Dashbord
            if (pmpro_hasMembershipLevel(null, $user_id)) {
                $user->add_cap(KIVI_CARE_PREFIX . 'dashboard', true);
            }
        }
    }
    public function update_dashboard_capability($level_id, $user_id): void
    {
        $user = get_user_by('id', $user_id);
        if (in_array($this->getClinicAdminRole(), $user->roles)) {
            if (!get_user_meta($user_id, 'send_new_register_email', true)) {

                $user_email_param =  kcCommonNotificationUserData($user->ID, get_user_meta($user_id, 'pmphelper', true));

                kcSendEmail($user_email_param);
                delete_user_meta($user_id, 'pmphelper');
                update_user_meta($user_id, 'send_new_register_email', true);
            }

            $user->remove_cap(KIVI_CARE_PREFIX . 'dashboard', true);
            // $user->remove_cap(KIVI_CARE_PREFIX.'doctor_add ', true);
            $clinic_admin_has_plan = pmpro_hasMembershipLevel(null, $user_id);
            $user->add_cap(KIVI_CARE_PREFIX . 'dashboard', $clinic_admin_has_plan);


            $KCClinic = (new KCClinic)->get_by(['clinic_admin_id' => (int)$user_id], '=', true);
            $clinic_all_doctors = (new \App\models\KCDoctorClinicMapping())->get_by(['clinic_id' => $KCClinic->clinic_id]);

            // $user->add_cap(KIVI_CARE_PREFIX.'doctor_add', $KCClinic->allow_no_of_doc > count($clinic_all_doctors??[]));
            foreach ($clinic_all_doctors as $key => $doctor) {
                $doctor_user_instance = get_user_by('id', $doctor->doctor_id);

                $doctor_user_instance->remove_cap(KIVI_CARE_PREFIX . 'dashboard', true);
                $doctor_user_instance->add_cap(KIVI_CARE_PREFIX . 'dashboard', $clinic_admin_has_plan);
            }
        }
    }
    public function show_msg_to_unsubscribe_user(): void
    {
        if (isset($_GET['page']) && 'dashboard' == $_GET['page']) {
            $user = new WP_User(get_current_user_id());
            $hasMembershipLevel = pmpro_hasMembershipLevel(null, $user->ID);
            if ($this->getClinicAdminRole() == $this->getLoginUserRole()  && !$hasMembershipLevel) {
                require KIVI_CARE_DIR . './templates/planExpire.php';
                die;
            }
        }
    }
    public static function get_user_subscription_details($user_id)
    {

    if (!$user_id) {
        return array(
            'success' => false,
            'message' => 'User not logged in'
        );
    }

    // Get user's membership level
    $membership = pmpro_getMembershipLevelForUser($user_id);

    if (empty($membership)) {
        return array(
            'success' => false,
            'message' => 'No active subscription found'
        );
    }

    global $wpdb;

    // Get user's membership record
   
    $membership_data = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}pmpro_subscriptions WHERE user_id = %d AND membership_level_id = %d ORDER BY id DESC LIMIT 1",
            $user_id,
            $membership->id
        )
    );
    if (!$membership_data) {
        return array(
            'success' => false,
            'message' => 'No membership data found'
        );
    }

    // Get subscription details using PMPro_Subscription
    $subscription = PMPro_Subscription::get_subscription($membership_data->id);

    if ( empty($subscription)) {
        return array(
            'success' => false,
            'message' => 'No subscription details found'
        );
    }

    // Format dates
    $created_date = date("F j, Y \\a\\t g:i a", strtotime($membership_data->startdate));

    // Get next payment date from the subscription object
    $next_payment_date =sprintf(// translators: %1$s is the date and %2$s is the time.
									__( '%1$s at %2$s', 'paid-memberships-pro' ),
									esc_html( $subscription->get_next_payment_date( get_option( 'date_format' ) ) ),
									esc_html( $subscription->get_next_payment_date( get_option( 'time_format' ) ) )
								);

    // Get fee using PMPro pricing functions
    $formatted_price = (!empty($membership->billing_amount)) 
        ? pmpro_formatPrice($membership->billing_amount) . ' per ' . ucfirst($membership->cycle_period) 
        : "N/A";

    return array(
        'success' => true,
        'membership_level' => $membership->name,
        'status' => ucfirst($membership_data->status), // Active, Cancelled, etc.
        'created' => $created_date,
        'next_payment_date' => $next_payment_date,
        'fee' => $formatted_price
    );
    }
}
