<?php

namespace App\baseClasses\Shortcodes;

use App\baseClasses\KCBase;

if(class_exists('ClinicRegisterNew')) return;
class ClinicRegisterNew
{
    protected KCBase $kcBase;

    function __construct()
    {
        $this->kcBase = KCBase::get_instance();
//        wp_enqueue_style('register-clinic-tailwindcss', KIVI_CARE_DIR_URI . 'assets/css/registerClinic.css', [], KIVI_CARE_VERSION, false);
     
        wp_register_script('jquery-validate',KIVI_CARE_DIR_URI . 'assets/js/jquery.validate.js', array('jquery'), KIVI_CARE_VERSION, false);

        wp_enqueue_script('register-clinic', KIVI_CARE_DIR_URI . 'assets/js/registerClinic.js',['kc_axios','jquery-validate'] ,KIVI_CARE_VERSION . '-' . time(), false);
        wp_localize_script('register-clinic', 'KC_CLINIC_REGISTER', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                "ajax_post_nonce" => esc_js(wp_create_nonce('ajax_post')),
                "ajax_get_nonce" => esc_js(wp_create_nonce('ajax_get')),
            )
        );

    }

    public function pmpro_getAllLevels()
    {
        $levels = pmpro_getAllLevels(); // Get all PMP levels as an associative array
        if (empty($levels)) return false;
        $pmp_levels = array();
        foreach ($levels as $level) {
            // Extract details for each plan\
            $pmp_levels[] = [
                'id'=>$level->id,
                'plan_name' => $level->name,
                'price' => $level->billing_amount,
                'billing_cycle' => $level->cycle_period,
                'trial_period' => $level->trial_limit,
                'billing_label' => ($level->cycle_number == 1) ? strtolower($level->cycle_period) : strtolower($level->cycle_period . 's'),
                'trial_text' => $level->trial_limit > 0 ? "{$level->trial_limit}-day free trial included" : "7 Days free trial",

            ];
        }
//        return $pmp_levels;

        return  apply_filters('pmpro_get_all_levels',$pmp_levels);
        
    }

    public function render()
    {
        if(!empty($this->kcBase->getLoginUserRole()) &&  $this->kcBase->getLoginUserRole() !== $this->kcBase->getClinicAdminRole() ){
            return "Your Are Not Allowed to access this page.";
        }
        ob_start();
        require KIVI_CARE_DIR . 'app/baseClasses/clinicRegister/clinicRegisterStep.php';
        return ob_get_clean();
    }
}