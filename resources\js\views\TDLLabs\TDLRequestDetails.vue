<template>
    <div class="w-full px-4">
        <div class="grid grid-cols-1 gap-6">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                        <h4 class="font-medium text-lg text-gray-800">{{ $t('TDL Lab Test Request Details') }}</h4>
                        <div class="flex space-x-2">
                            <button 
                                v-if="request.status === 'sent'" 
                                @click="checkResults" 
                                class="inline-flex items-center px-3 py-1 text-sm border border-blue-600 rounded-md text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                :disabled="checkingResults"
                            >
                                <i :class="{'fa fa-sync mr-1': true, 'fa-spin': checkingResults}"></i>
                                {{ checkingResults ? $t('Checking...') : $t('Check Results') }}
                            </button>
                            <button 
                                v-if="request.status === 'pending'" 
                                @click="submitRequest" 
                                class="inline-flex items-center px-3 py-1 text-sm border border-green-600 rounded-md text-green-600 hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                :disabled="submitting"
                            >
                                <i :class="{'fa fa-paper-plane mr-1': !submitting, 'fa fa-sync fa-spin mr-1': submitting}"></i>
                                {{ submitting ? $t('Submitting...') : $t('Submit to TDL') }}
                            </button>
                            <router-link to="/tdl-requests" class="inline-flex items-center px-3 py-1 text-sm border border-blue-600 rounded-md text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fa fa-arrow-left mr-1"></i>
                                {{ $t('Back to Requests') }}
                            </router-link>
                        </div>
                    </div>
                    <div class="p-6">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <div v-else>
                            <!-- Request Status -->
                            <div class="mb-6">
                                <span class="px-3 py-1 text-sm font-medium rounded-full" :class="{
                                    'bg-blue-100 text-blue-800': request.status === 'pending',
                                    'bg-indigo-100 text-indigo-800': request.status === 'sent',
                                    'bg-green-100 text-green-800': request.status === 'completed',
                                    'bg-red-100 text-red-800': request.status === 'error'
                                }">
                                    {{ statusLabel(request.status) }}
                                </span>
                            </div>
                            
                            <!-- Request Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                                        <div class="p-5">
                                            <h5 class="text-base font-medium text-gray-800 mb-4">{{ $t('Request Information') }}</h5>
                                            <div class="divide-y divide-gray-200">
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Order Number') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ request.order_number || 'N/A' }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Date Created') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ formatDate(request.created_at) }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Collection Date') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ formatDate(request.collection_date) }}</div>
                                                </div>
                                                <div v-if="request.status === 'sent' || request.status === 'completed'" class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Date Sent') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ formatDate(request.sent_date) }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                                        <div class="p-5">
                                            <h5 class="text-base font-medium text-gray-800 mb-4">{{ $t('Patient Information') }}</h5>
                                            <div class="divide-y divide-gray-200">
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Patient') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ request.patient ? request.patient.name : 'N/A' }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Doctor') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ request.doctor ? request.doctor.name : 'N/A' }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Clinic') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ request.clinic ? request.clinic.name : 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Clinical Notes -->
                            <div v-if="request.clinical_notes" class="mb-6">
                                <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                                    <div class="p-5">
                                        <h5 class="text-base font-medium text-gray-800 mb-3">{{ $t('Clinical Notes') }}</h5>
                                        <p class="text-sm text-gray-700">{{ request.clinical_notes }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Tests Requested -->
                            <div class="mb-6">
                                <h5 class="text-base font-medium text-gray-800 mb-3">{{ $t('Tests Requested') }}</h5>
                                <div v-if="!request.items || request.items.length === 0" class="p-4 rounded-md bg-blue-50 border border-blue-200 text-blue-700 text-sm">
                                    {{ $t('No tests have been requested.') }}
                                </div>
                                <div v-else class="overflow-x-auto bg-white border rounded-lg">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-44">{{ $t('Test Code') }}</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Test Name') }}</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Sample Type') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="(item, index) in request.items" :key="index">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ item.test_code || 'N/A' }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ item.test_name || 'N/A' }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ item.sample_type || 'N/A' }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- HL7 Message -->
                            <div v-if="request.hl7_message" class="mb-6">
                                <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                                    <div class="px-5 py-3 border-b border-gray-200 flex justify-between items-center">
                                        <h5 class="font-medium text-gray-800">{{ $t('HL7 Message') }}</h5>
                                        <button @click="copyHL7Message" class="text-blue-600 hover:text-blue-800 text-sm">
                                            <i :class="{'fa mr-1': true, 'fa-copy': !hl7Copied, 'fa-check': hl7Copied}"></i>
                                            {{ hl7Copied ? $t('Copied') : $t('Copy') }}
                                        </button>
                                    </div>
                                    <div class="p-5">
                                        <pre class="bg-gray-50 p-4 border border-gray-200 rounded-md text-xs text-gray-700 overflow-x-auto"><code>{{ request.hl7_message }}</code></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Results Section -->
                            <div v-if="request.status === 'completed'">
                                <div>
                                    <div class="flex justify-between items-center mb-3">
                                        <h5 class="text-base font-medium text-gray-800">{{ $t('Results') }}</h5>
                                        <router-link 
                                            v-if="results.length > 0"
                                            :to="'/tdl-result/' + results[0].id" 
                                            class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                        >
                                            <i class="fa fa-chart-bar mr-1"></i>
                                            {{ $t('View Detailed Results') }}
                                        </router-link>
                                    </div>
                                    <div v-if="results.length > 0" class="p-4 rounded-md bg-green-50 border border-green-200 text-green-700 text-sm">
                                        {{ $t('Results are available for this request. Click "View Detailed Results" to see complete test results.') }}
                                    </div>
                                    <div v-else class="p-4 rounded-md bg-yellow-50 border border-yellow-200 text-yellow-700 text-sm">
                                        {{ $t('This request is marked as completed, but no results are currently available. Try refreshing the page.') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Error State -->
                            <div v-if="request.status === 'error'" class="mt-6">
                                <div class="p-4 rounded-md bg-red-50 border border-red-200">
                                    <div class="flex">
                                        <i class="fa fa-exclamation-triangle text-red-600 mr-3 mt-0.5"></i>
                                        <div>
                                            <h5 class="text-base font-medium text-red-800 mb-1">{{ $t('Error Information') }}</h5>
                                            <p class="text-sm text-red-700">{{ request.error_message || $t('An error occurred while processing this request. Please check the details and try again.') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
    name: 'TDLRequestDetails',
    data() {
        return {
            loading: true,
            checkingResults: false,
            submitting: false,
            hl7Copied: false,
            request: {
                id: 0,
                patient_id: 0,
                doctor_id: 0,
                clinic_id: 0,
                order_number: '',
                status: 'pending',
                collection_date: '',
                sent_date: null,
                clinical_notes: '',
                hl7_message: '',
                error_message: null,
                created_at: '',
                updated_at: '',
                patient: null,
                doctor: null,
                clinic: null,
                items: []
            },
            results: []
        };
    },
    computed: {
        currentLocale() {
            return this.$i18n.locale || 'en';
        },
        requestId() {
            return this.$route.params.id;
        }
    },
    created() {
        this.fetchRequestDetails();
    },
    methods: {
        fetchRequestDetails() {
            if (!this.requestId) {
                this.redirectToRequests('No request ID provided');
                return;
            }
            
            this.loading = true;
            
            get('tdl_get_test_request_details', { id: this.requestId })
                .then(response => {
                    this.loading = false;
                    
                    if (response.data.status === true) {
                        this.request = response.data.data;
                        
                        // Initialize items array if it's null
                        if (!this.request.items) {
                            this.request.items = [];
                        }
                        
                        // If the status is completed, fetch associated results
                        if (this.request.status === 'completed') {
                            this.fetchResults();
                        }
                    } else {
                        this.redirectToRequests(response.data.message || this.$t('Failed to load request details.'));
                    }
                })
                .catch(error => {
                    this.loading = false;
                    console.error('Error fetching request details:', error);
                    this.redirectToRequests(this.$t('Failed to load request details.'));
                });
        },
        redirectToRequests(errorMessage) {
            displayErrorMessage(errorMessage);
            this.$router.push('/tdl-requests');
        },
        fetchResults() {
            get('tdl_get_test_results', { request_id: this.request.id })
                .then(response => {
                    if (response.data.status === true) {
                        this.results = response.data.data.results || [];
                    } else {
                        console.error('Error fetching results:', response.data.message);
                    }
                })
                .catch(error => {
                    console.error('Error fetching results:', error);
                });
        },
        submitRequest() {
            if (this.submitting) return;
            
            this.submitting = true;
            
            post('tdl_submit_test_request', { request_id: this.request.id })
                .then(response => {
                    this.submitting = false;
                    
                    if (response.data.status === true) {
                        displayMessage(this.$t('Request has been submitted to TDL.'));
                        // Refresh the request details to get updated status
                        this.fetchRequestDetails();
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to submit request to TDL.'));
                    }
                })
                .catch(error => {
                    this.submitting = false;
                    console.error('Error submitting request:', error);
                    displayErrorMessage(this.$t('Failed to submit request to TDL.'));
                });
        },
        checkResults() {
            if (this.checkingResults) return;
            
            this.checkingResults = true;
            
            post('tdl_check_test_results', { request_id: this.request.id })
                .then(response => {
                    this.checkingResults = false;
                    
                    if (response.data.status === true) {
                        if (response.data.data && response.data.data.results_found) {
                            displayMessage(this.$t('New results found for this request. Updating status...'));
                            // Refresh the request details to get updated status and results
                            this.fetchRequestDetails();
                        } else {
                            displayMessage(this.$t('No new results found for this request.'));
                        }
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to check for results.'));
                    }
                })
                .catch(error => {
                    this.checkingResults = false;
                    console.error('Error checking results:', error);
                    displayErrorMessage(this.$t('Failed to check for results.'));
                });
        },
        copyHL7Message() {
            if (!this.request.hl7_message) return;
            
            // Create a temporary textarea to copy the text
            const textarea = document.createElement('textarea');
            textarea.value = this.request.hl7_message;
            textarea.setAttribute('readonly', '');
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);
            
            // Select and copy the text
            textarea.select();
            document.execCommand('copy');
            
            // Remove the textarea
            document.body.removeChild(textarea);
            
            // Show "Copied" status for 2 seconds
            this.hl7Copied = true;
            setTimeout(() => {
                this.hl7Copied = false;
            }, 2000);
        },
        statusLabel(status) {
            switch (status) {
                case 'pending':
                    return this.$t('Pending');
                case 'sent':
                    return this.$t('Sent to TDL');
                case 'completed':
                    return this.$t('Completed');
                case 'error':
                    return this.$t('Error');
                default:
                    return status || 'N/A';
            }
        },
        formatDate(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const options = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                
                return new Date(dateString).toLocaleDateString(this.currentLocale, options);
            } catch (error) {
                console.error('Error formatting date:', error);
                return dateString || 'N/A';
            }
        }
    }
};
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>