<template>
    <div class="w-full px-4">
        <div class="w-full">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="px-6 py-4 flex justify-between items-center border-b border-gray-200">
                        <h4 class="text-xl font-medium text-gray-800">{{ $t('TDL Lab Test Requests') }}</h4>
                        <div class="flex space-x-2">
                            <button 
                                @click="refreshRequests" 
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                :disabled="refreshing"
                            >
                                <i :class="{'fa fa-sync mr-1': true, 'fa-spin': refreshing}"></i>
                                {{ $t('Refresh') }}
                            </button>
                            <router-link to="/tdl-new-request" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fa fa-plus mr-1"></i>
                                {{ $t('New Request') }}
                            </router-link>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Filters -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                            <div>
                                <label for="patient_filter" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Patient') }}</label>
                                <select
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    id="patient_filter"
                                    v-model="filters.patient_id"
                                    @change="applyFilters"
                                >
                                    <option value="">{{ $t('All Patients') }}</option>
                                    <option v-for="patient in patients" :key="patient.id" :value="patient.id">
                                        {{ patient.first_name }} {{ patient.last_name }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <label for="status_filter" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Status') }}</label>
                                <select
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    id="status_filter"
                                    v-model="filters.status"
                                    @change="applyFilters"
                                >
                                    <option value="">{{ $t('All Statuses') }}</option>
                                    <option value="pending">{{ $t('Pending') }}</option>
                                    <option value="sent">{{ $t('Sent') }}</option>
                                    <option value="completed">{{ $t('Completed') }}</option>
                                    <option value="error">{{ $t('Error') }}</option>
                                </select>
                            </div>
                            <div>
                                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Date From') }}</label>
                                <input
                                    type="date"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    id="date_from"
                                    v-model="filters.date_from"
                                    @change="applyFilters"
                                />
                            </div>
                            <div>
                                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Date To') }}</label>
                                <input
                                    type="date"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    id="date_to"
                                    v-model="filters.date_to"
                                    @change="applyFilters"
                                />
                            </div>
                        </div>
                        
                        <!-- Requests Table -->
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <div v-else-if="requests.length === 0" class="bg-blue-50 border-l-4 border-blue-500 p-4 text-blue-700">
                            {{ $t('No test requests found matching your criteria.') }}
                        </div>
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Order Number') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Date') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Patient') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Doctor') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Tests') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {{ $t('Status') }}
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                                            {{ $t('Actions') }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="request in requests" :key="request.id" class="hover:bg-gray-50">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ request.order_number || 'N/A' }}</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ formatDate(request.created_at) }}</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ request.patient_name || 'N/A' }}</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ request.doctor_name || 'N/A' }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">
                                            <span 
                                                v-for="(item, index) in request.items" 
                                                :key="index" 
                                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1 mb-1"
                                            >
                                                {{ item.test_code }}
                                            </span>
                                            <span v-if="!request.items || request.items.length === 0">N/A</span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm">
                                            <span
                                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                :class="{
                                                    'bg-blue-100 text-blue-800': request.status === 'pending',
                                                    'bg-indigo-100 text-indigo-800': request.status === 'sent',
                                                    'bg-green-100 text-green-800': request.status === 'completed',
                                                    'bg-red-100 text-red-800': request.status === 'error'
                                                }"
                                            >
                                                {{ statusLabel(request.status) }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <router-link
                                                    :to="'/tdl-request/' + request.id"
                                                    class="text-blue-600 hover:text-blue-800"
                                                    title="View Details"
                                                >
                                                    <span class="inline-flex items-center justify-center h-8 w-8 rounded-md bg-blue-50 hover:bg-blue-100">
                                                        <i class="fa fa-eye"></i>
                                                    </span>
                                                </router-link>
                                                <button
                                                    v-if="request.status === 'pending'"
                                                    @click="submitRequest(request.id)"
                                                    class="text-green-600 hover:text-green-800"
                                                    title="Submit to TDL"
                                                    :disabled="processingIds.includes(request.id)"
                                                >
                                                    <span class="inline-flex items-center justify-center h-8 w-8 rounded-md bg-green-50 hover:bg-green-100">
                                                        <i :class="['fa', processingIds.includes(request.id) ? 'fa-sync fa-spin' : 'fa-paper-plane']"></i>
                                                    </span>
                                                </button>
                                                <button
                                                    v-if="request.status === 'sent'"
                                                    @click="checkResults(request.id)"
                                                    class="text-indigo-600 hover:text-indigo-800"
                                                    title="Check Results"
                                                    :disabled="processingIds.includes(request.id)"
                                                >
                                                    <span class="inline-flex items-center justify-center h-8 w-8 rounded-md bg-indigo-50 hover:bg-indigo-100">
                                                        <i :class="['fa', processingIds.includes(request.id) ? 'fa-sync fa-spin' : 'fa-sync']"></i>
                                                    </span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div v-if="totalPages > 1" class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-700">
                                {{ $t('Showing {start} to {end} of {total} requests', {
                                    start: (currentPage - 1) * perPage + 1,
                                    end: Math.min(currentPage * perPage, total),
                                    total: total
                                }) }}
                            </div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a
                                    href="#"
                                    @click.prevent="changePage(currentPage - 1)"
                                    :class="[
                                        'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                                        currentPage === 1 
                                            ? 'text-gray-300 cursor-not-allowed' 
                                            : 'text-gray-500 hover:bg-gray-50'
                                    ]"
                                >
                                    <span class="sr-only">{{ $t('Previous') }}</span>
                                    <i class="fa fa-chevron-left h-5 w-5"></i>
                                </a>
                                <a
                                    v-for="page in pageNumbers"
                                    :key="page"
                                    href="#"
                                    @click.prevent="changePage(page)"
                                    :class="[
                                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                                        page === currentPage
                                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                    ]"
                                >
                                    {{ page }}
                                </a>
                                <a
                                    href="#"
                                    @click.prevent="changePage(currentPage + 1)"
                                    :class="[
                                        'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                                        currentPage === totalPages 
                                            ? 'text-gray-300 cursor-not-allowed' 
                                            : 'text-gray-500 hover:bg-gray-50'
                                    ]"
                                >
                                    <span class="sr-only">{{ $t('Next') }}</span>
                                    <i class="fa fa-chevron-right h-5 w-5"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
    name: 'TDLRequests',
    data() {
        return {
            loading: true,
            refreshing: false,
            requests: [],
            patients: [],
            currentPage: 1,
            perPage: 25,
            total: 0,
            totalPages: 0,
            processingIds: [], // Track which requests are being processed
            filters: {
                patient_id: '',
                status: '',
                date_from: '',
                date_to: ''
            }
        };
    },
    computed: {
        pageNumbers() {
            const range = [];
            const showPages = 5;
            const halfShow = Math.floor(showPages / 2);
            
            let start = this.currentPage - halfShow;
            if (start < 1) start = 1;
            
            let end = start + showPages - 1;
            if (end > this.totalPages) {
                end = this.totalPages;
                start = Math.max(1, end - showPages + 1);
            }
            
            for (let i = start; i <= end; i++) {
                range.push(i);
            }
            
            return range;
        },
        currentLocale() {
            return this.$i18n.locale || 'en';
        }
    },
    created() {
        this.loadPatients();
        this.fetchRequests();
    },
    methods: {
        loadPatients() {
            get('patient_list')
                .then(response => {
                    if (response.data.status === true) {
                        this.patients = response.data.data || [];
                    } else {
                        console.error('Error loading patients:', response.data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading patients:', error);
                });
        },
        fetchRequests() {
            this.loading = true;
            
            const params = {
                page: this.currentPage,
                per_page: this.perPage,
                patient_id: this.filters.patient_id || '',
                status: this.filters.status || '',
                date_from: this.filters.date_from ? `${this.filters.date_from} 00:00:00` : '',
                date_to: this.filters.date_to ? `${this.filters.date_to} 23:59:59` : ''
            };
            
            get('tdl_get_test_requests', params)
                .then(response => {
                    this.loading = false;
                    
                    if (response.data.status === true) {
                        this.requests = response.data.data.requests || [];
                        this.total = response.data.data.total || 0;
                        this.totalPages = response.data.data.total_pages || 0;
                        this.currentPage = response.data.data.current_page || 1;
                        
                        // Initialize items array if null
                        this.requests.forEach(request => {
                            if (!request.items) {
                                request.items = [];
                            }
                        });
                    } else {
                        this.requests = [];
                        this.total = 0;
                        this.totalPages = 0;
                        
                        displayErrorMessage(response.data.message || this.$t('Failed to load test requests.'));
                    }
                })
                .catch(error => {
                    this.loading = false;
                    console.error('Error fetching requests:', error);
                    
                    displayErrorMessage(this.$t('Failed to load test requests.'));
                });
        },
        refreshRequests() {
            if (this.refreshing) return;
            
            this.refreshing = true;
            
            get('tdl_refresh_test_requests')
                .then(response => {
                    this.refreshing = false;
                    
                    if (response.data.status === true) {
                        displayMessage(response.data.message || this.$t('Test requests refreshed successfully.'));
                        this.fetchRequests();
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to refresh test requests.'));
                    }
                })
                .catch(error => {
                    this.refreshing = false;
                    console.error('Error refreshing requests:', error);
                    
                    displayErrorMessage(this.$t('Failed to refresh test requests.'));
                });
        },
        formatDate(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const options = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                
                return new Date(dateString).toLocaleDateString(this.currentLocale, options);
            } catch (error) {
                console.error('Error formatting date:', error);
                return dateString || 'N/A';
            }
        },
        statusLabel(status) {
            switch (status) {
                case 'pending':
                    return this.$t('Pending');
                case 'sent':
                    return this.$t('Sent to TDL');
                case 'completed':
                    return this.$t('Completed');
                case 'error':
                    return this.$t('Error');
                default:
                    return status || 'N/A';
            }
        },
        applyFilters() {
            this.currentPage = 1;
            this.fetchRequests();
        },
        changePage(page) {
            if (page < 1 || page > this.totalPages) return;
            
            this.currentPage = page;
            this.fetchRequests();
        },
        submitRequest(requestId) {
            if (this.processingIds.includes(requestId)) return;
            
            // Add requestId to processing list
            this.processingIds.push(requestId);
            
            post('tdl_submit_test_request', { request_id: requestId })
                .then(response => {
                    // Remove requestId from processing list
                    this.processingIds = this.processingIds.filter(id => id !== requestId);
                    
                    if (response.data.status === true) {
                        displayMessage(this.$t('Request has been submitted to TDL.'));
                        
                        // Update the request status in the local data
                        const requestIndex = this.requests.findIndex(r => r.id === requestId);
                        if (requestIndex !== -1) {
                            this.requests[requestIndex].status = 'sent';
                            
                            // Create a new array to trigger reactivity
                            this.requests = [...this.requests];
                        }
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to submit request to TDL.'));
                    }
                })
                .catch(error => {
                    // Remove requestId from processing list
                    this.processingIds = this.processingIds.filter(id => id !== requestId);
                    
                    console.error('Error submitting request:', error);
                    displayErrorMessage(this.$t('Failed to submit request to TDL.'));
                });
        },
        checkResults(requestId) {
            if (this.processingIds.includes(requestId)) return;
            
            // Add requestId to processing list
            this.processingIds.push(requestId);
            
            post('tdl_check_test_results', { request_id: requestId })
                .then(response => {
                    // Remove requestId from processing list
                    this.processingIds = this.processingIds.filter(id => id !== requestId);
                    
                    if (response.data.status === true) {
                        if (response.data.data && response.data.data.results_found) {
                            displayMessage(this.$t('New results found for this request. Updating status...'));
                            
                            // Update the request status in the local data if results were found
                            const requestIndex = this.requests.findIndex(r => r.id === requestId);
                            if (requestIndex !== -1) {
                                this.requests[requestIndex].status = 'completed';
                                
                                // Create a new array to trigger reactivity
                                this.requests = [...this.requests];
                            }
                        } else {
                            displayMessage(this.$t('No new results found for this request.'));
                        }
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to check for results.'));
                    }
                })
                .catch(error => {
                    // Remove requestId from processing list
                    this.processingIds = this.processingIds.filter(id => id !== requestId);
                    
                    console.error('Error checking results:', error);
                    displayErrorMessage(this.$t('Failed to check for results.'));
                });
        }
    }
};
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>