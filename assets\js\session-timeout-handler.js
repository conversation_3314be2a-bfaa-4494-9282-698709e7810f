/**
 * Kivicare Session Timeout Handler
 * Replaces WordPress default auth check modal with custom handling
 */

(function($) {
    'use strict';

    // Session timeout handler object
    window.KivicareSessionHandler = {
        checkInterval: null,
        warningShown: false,
        sessionCheckUrl: null,
        refreshUrl: null,

        // Configuration
        config: {
            checkIntervalMinutes: 5,
            warningTimeoutSeconds: 10,
            maxRetries: 3,
            retryDelay: 2000
        },

        // Initialize the session handler
        init: function(options) {
            // Merge options with defaults and localized config
            if (window.kivicareSessionConfig) {
                $.extend(this.config, window.kivicareSessionConfig);
            }
            if (options) {
                $.extend(this.config, options);
            }

            // Set URLs
            this.sessionCheckUrl = this.config.ajaxurl || window.ajaxurl || '/wp-admin/admin-ajax.php';
            this.refreshUrl = window.location.href;

            // Start session monitoring
            this.startSessionMonitoring();

            // Listen for heartbeat events
            this.bindHeartbeatEvents();

            // Listen for AJAX errors that might indicate session expiry
            this.bindAjaxErrorHandling();

            console.log('Kivicare Session Handler initialized');
        },

        // Start monitoring session status
        startSessionMonitoring: function() {
            var self = this;

            // Check session every configured interval
            this.checkInterval = setInterval(function() {
                self.checkSessionStatus();
            }, this.config.checkIntervalMinutes * 60 * 1000);

            // Initial check after 30 seconds
            setTimeout(function() {
                self.checkSessionStatus();
            }, 30000);
        },

        // Check session status via AJAX
        checkSessionStatus: function() {
            var self = this;

            $.ajax({
                url: this.sessionCheckUrl,
                type: 'POST',
                data: {
                    action: 'heartbeat',
                    _nonce: this.config.nonce || (window.request_data ? window.request_data.nonce : ''),
                    data: { 'wp-auth-check': true }
                },
                timeout: 10000,
                success: function(response) {
                    self.handleSessionResponse(response);
                },
                error: function(xhr, status, error) {
                    // Only handle session expiry on specific error codes
                    if (xhr.status === 401 || xhr.status === 403) {
                        self.handleSessionExpired();
                    }
                }
            });
        },

        // Handle session response
        handleSessionResponse: function(response) {
            if (response && response['wp-auth-check'] === false) {
                this.handleSessionExpired();
            }
        },

        // Bind to WordPress heartbeat events
        bindHeartbeatEvents: function() {
            var self = this;

            $(document).on('heartbeat-tick', function(e, data) {
                if (data && data['wp-auth-check'] === false) {
                    self.handleSessionExpired();
                }
            });

            $(document).on('heartbeat-connection-lost', function() {
                // Don't immediately assume session expired on connection loss
                // Wait for explicit session check
                setTimeout(function() {
                    self.checkSessionStatus();
                }, 5000);
            });
        },

        // Bind AJAX error handling for session expiry detection
        bindAjaxErrorHandling: function() {
            var self = this;

            // Override jQuery AJAX to catch 401/403 errors
            $(document).ajaxError(function(event, xhr, settings) {
                if (xhr.status === 401 || xhr.status === 403) {
                    // Check if this is a session-related error
                    var responseText = xhr.responseText || '';
                    if (responseText.includes('login') || responseText.includes('session') || responseText.includes('expired')) {
                        self.handleSessionExpired();
                    }
                }
            });
        },

        // Handle session expiry
        handleSessionExpired: function() {
            if (this.warningShown) {
                return;
            }

            this.warningShown = true;

            // Clear the check interval
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
                this.checkInterval = null;
            }

            // Show session expired notification
            this.showSessionExpiredNotification();
        },

        // Show session expired notification
        showSessionExpiredNotification: function() {
            var self = this;

            // Remove any existing notifications
            $('#kivicare-session-expired').remove();

            // Create notification HTML
            var notification = $(`
                <div id="kivicare-session-expired">
                    <div class="notification-content">
                        <strong>⚠️ Session Expired</strong>
                        <p>
                            Your session has expired for security reasons. Please refresh the page to continue working.
                        </p>
                        <button id="kivicare-refresh-page">
                            Refresh Page Now
                        </button>
                        <span class="countdown-text">
                            Auto-refresh in <span id="kivicare-countdown">${this.config.warningTimeoutSeconds}</span> seconds
                        </span>
                    </div>
                </div>
            `);

            // Prepend to body
            $('body').prepend(notification);

            // Handle refresh button click
            $('#kivicare-refresh-page').on('click', function() {
                self.refreshPage();
            });

            // Start countdown
            this.startCountdown();
        },

        // Start countdown timer
        startCountdown: function() {
            var self = this;
            var countdown = this.config.warningTimeoutSeconds;
            var countdownElement = $('#kivicare-countdown');

            var timer = setInterval(function() {
                countdown--;
                countdownElement.text(countdown);

                if (countdown <= 0) {
                    clearInterval(timer);
                    self.refreshPage();
                }
            }, 1000);
        },

        // Refresh the page
        refreshPage: function() {
            // Show loading state
            $('#kivicare-session-expired').addClass('loading').html(`
                <div class="notification-content">
                    <strong><span class="loading-spinner"></span>🔄 Refreshing...</strong>
                    <p>
                        Please wait while we refresh the page.
                    </p>
                </div>
            `);

            // Refresh after a short delay
            setTimeout(function() {
                window.location.reload();
            }, 1000);
        },

        // Destroy the session handler
        destroy: function() {
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
                this.checkInterval = null;
            }

            $(document).off('heartbeat-tick heartbeat-connection-lost');
            $('#kivicare-session-expired').remove();

            this.warningShown = false;
        }
    };

    // Auto-initialize when document is ready
    $(document).ready(function() {
        // Only initialize on Kivicare pages
        if (window.request_data && (
            window.location.href.includes('page=dashboard') ||
            $('[data-kivicare-page]').length > 0 ||
            $('.kivicare-shortcode').length > 0
        )) {
            window.KivicareSessionHandler.init();
        }
    });

})(jQuery);
