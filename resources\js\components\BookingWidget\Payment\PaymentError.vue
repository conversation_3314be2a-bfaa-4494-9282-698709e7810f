<template>
  <div class="kivi-payment-result kivi-payment-error">
    <div class="kivi-payment-result-icon">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="15" y1="9" x2="9" y2="15"></line>
        <line x1="9" y1="9" x2="15" y2="15"></line>
      </svg>
    </div>
    <h2 class="kivi-payment-result-title">Payment Failed</h2>
    <p class="kivi-payment-result-message">{{ errorMessage || 'We were unable to process your payment. Please try again or choose another payment method.' }}</p>
    <div class="kivi-payment-result-details">
      <h3>What to do next?</h3>
      <ul class="kivi-payment-error-steps">
        <li>Check if you have sufficient funds in your account</li>
        <li>Verify your payment details and try again</li>
        <li>Try a different payment method</li>
        <li>Contact support if the problem persists</li>
      </ul>
    </div>
    <div class="kivi-payment-result-actions">
      <button @click="retryPayment" class="kivi-btn kivi-btn-primary kivi-btn-large">
        Try Again
      </button>
      <button @click="changePaymentMethod" class="kivi-btn kivi-btn-secondary kivi-btn-large">
        Change Payment Method
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PaymentError',
  props: {
    errorMessage: {
      type: String,
      default: ''
    },
    appointmentId: {
      type: String,
      default: null
    }
  },
  methods: {
    retryPayment() {
      this.$emit('retry-payment');
    },
    changePaymentMethod() {
      this.$emit('change-payment-method');
    }
  }
}
</script>