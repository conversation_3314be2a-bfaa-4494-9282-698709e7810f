<template>
  <div>
    <div class="p-6 max-w-7xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Invoices &amp; Payments</h1>
        <button
          class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-credit-card w-4 h-4 mr-2"
          >
            <rect width="20" height="14" x="2" y="5" rx="2"></rect>
            <line x1="2" x2="22" y1="10" y2="10"></line></svg
          >Make Payment
        </button>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white text-card-foreground rounded-xl border shadow">
          <div class="p-6">
            <p class="text-gray-600 text-sm">Total Outstanding</p>
            <p class="text-2xl font-bold text-purple-600">$230.00</p>
          </div>
        </div>
        <div class="bg-white text-card-foreground rounded-xl border shadow">
          <div class="p-6">
            <p class="text-gray-600 text-sm">Paid this Month</p>
            <p class="text-2xl font-bold text-purple-600">$450.00</p>
          </div>
        </div>
        <div class="bg-white text-card-foreground rounded-xl border shadow">
          <div class="p-6">
            <p class="text-gray-600 text-sm">Pending Claims</p>
            <p class="text-2xl font-bold text-purple-600">2</p>
          </div>
        </div>
      </div>
      <div
        role="alert"
        class="relative w-full rounded-lg border px-4 py-3 text-sm [&amp;>svg+div]:translate-y-[-3px] [&amp;>svg]:absolute [&amp;>svg]:left-4 [&amp;>svg]:top-4 [&amp;>svg]:text-foreground [&amp;>svg~*]:pl-7 bg-background text-foreground mb-6 bg-purple-50 border-purple-100"
      >
        <div class="flex items-start space-x-4">
          <div
            class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-message-square w-4 h-4 text-purple-600"
            >
              <path
                d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
              ></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-purple-900">Insurance Update</h3>
            <div class="text-sm [&amp;_p]:leading-relaxed mt-1 text-purple-800">
              Your insurance claim for Lab Tests (CLM-2025-002) has been
              processed. Your out-of-pocket expense is $70.00. Would you like to
              pay this now?
            </div>
            <div class="mt-3 flex space-x-4">
              <button
                class="text-sm px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Pay Now</button
              ><button
                class="text-sm px-3 py-1 text-purple-600 hover:bg-purple-100 rounded-md"
              >
                View Details
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex space-x-2">
          <button class="px-4 py-2 rounded-lg text-sm bg-purple-600 text-white">
            All</button
          ><button
            class="px-4 py-2 rounded-lg text-sm bg-gray-100 text-gray-600 hover:bg-gray-200"
          >
            Pending</button
          ><button
            class="px-4 py-2 rounded-lg text-sm bg-gray-100 text-gray-600 hover:bg-gray-200"
          >
            Paid</button
          ><button
            class="px-4 py-2 rounded-lg text-sm bg-gray-100 text-gray-600 hover:bg-gray-200"
          >
            Overdue
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <div class="relative">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-search w-4 h-4 absolute left-3 top-3 text-gray-400"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path></svg
            ><input
              placeholder="Search invoices..."
              class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              type="text"
            />
          </div>
        </div>
      </div>
      <div class="space-y-4">
        <div
          class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden"
        >
          <div class="p-6">
            <div
              class="flex flex-col md:flex-row md:items-center md:justify-between"
            >
              <div>
                <div class="flex items-center space-x-3">
                  <h3 class="font-semibold text-lg">Consultation</h3>
                  <span
                    class="px-2 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800"
                    >Pending</span
                  >
                </div>
                <p class="text-gray-600 mt-1">INV-2025-001</p>
                <div
                  class="flex items-center space-x-4 text-sm text-gray-600 mt-2"
                >
                  <span>Due: February 15, 2025</span><span>•</span
                  ><span>$150.00</span>
                </div>
              </div>
              <div class="mt-4 md:mt-0 flex items-center space-x-3">
                <button
                  class="px-4 py-2 text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100"
                >
                  Download PDF</button
                ><button
                  class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  View Details
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden"
        >
          <div class="p-6">
            <div
              class="flex flex-col md:flex-row md:items-center md:justify-between"
            >
              <div>
                <div class="flex items-center space-x-3">
                  <h3 class="font-semibold text-lg">Lab Tests</h3>
                  <span
                    class="px-2 py-1 rounded-full text-sm bg-green-100 text-green-800"
                    >Paid</span
                  >
                </div>
                <p class="text-gray-600 mt-1">INV-2025-002</p>
                <div
                  class="flex items-center space-x-4 text-sm text-gray-600 mt-2"
                >
                  <span>Due: January 25, 2025</span><span>•</span
                  ><span>$350.00</span>
                </div>
              </div>
              <div class="mt-4 md:mt-0 flex items-center space-x-3">
                <button
                  class="px-4 py-2 text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100"
                >
                  Download PDF</button
                ><button
                  class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  View Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get, post } from "../../config/request";

export default {
  components: {
  },
  data: () => {
    return {
      isLoading: false,
      dashboardData: {},
      isAppointmentReload: false,
      appointmentRequest: {},
      reloadCalender: true,

      loading: false,
      vitals: {},
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init: function () {
      console.log(this.getUserRole());
      this.getDashboardData();
      this.dashboardData = this.defaultDashboardData();
      this.$nextTick(() => {
        // Add the component back in
        this.reloadCalender = true;
      });
    },
    defaultDashboardData: function () {
      return {
        appointment_count: 0,
        doctor_count: 0,
        patient_count: 0,
        revenue: 0,
        change_log: true,
        telemed_log: false,
      };
    },
  },
};
</script>
