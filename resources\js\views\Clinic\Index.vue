<!-- ClinicList.vue -->
<template>
  <div>
    <div :show="userData.addOns.kiviPro != true" variant="white"
      class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
      <!-- Header Section -->
      <div class="mb-8 flex justify-between items-center">
        <div class="flex items-center gap-4">
          <button @click="$router.go(-1)"
            class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
            <i class="fas fa-arrow-left"></i>
            <span>Back</span>
          </button>
          <h1 class="text-2xl font-semibold text-gray-800">
            {{ formTranslation.clinic.clinic_list }}
          </h1>
        </div>
        <div class="flex items-center gap-2">
          <!-- data import module -->
          <module-data-import v-if="
            userData.addOns.kiviPro &&
            kcCheckPermission('clinic_add') &&
            kivicareCompareVersion(requireProVersion, userData.pro_version)
          " ref="module_data_import" :required-data="[
            { label: formTranslation.clinic.clinic_name, value: 'clinic_name' },
            { label: formTranslation.clinic.email, value: 'email' },
            {
              label: formTranslation.common.country_calling_code,
              value: 'country_calling_code',
            },
            { label: formTranslation.common.country_code, value: 'country_code' },
            { label: formTranslation.clinic.clinic_contact, value: 'contact' },
            {
              label: formTranslation.clinic.dt_lbl_specialties,
              value: 'specialization',
            },
            { label: formTranslation.common.address, value: 'address' },
            { label: formTranslation.common.city, value: 'city' },
            { label: formTranslation.common.country, value: 'country' },
            { label: formTranslation.common.postal_code, value: 'postal_code' },
            {
              label:
                formTranslation.common.clinic_admin +
                ' ' +
                formTranslation.common.fname,
              value: 'clinic_admin_first_name',
            },
            {
              label:
                formTranslation.common.clinic_admin +
                ' ' +
                formTranslation.common.lname,
              value: 'clinic_admin_last_name',
            },
            {
              label: formTranslation.common.clinic_admin_email,
              value: 'clinic_admin_email',
            },
            {
              label: formTranslation.common.clinic_admin_country_calling_code,
              value: 'clinic_admin_country_calling_code',
            },
            {
              label: formTranslation.common.clinic_admin_country_code,
              value: 'clinic_admin_country_code',
            },
            {
              label:
                formTranslation.common.clinic_admin +
                ' ' +
                formTranslation.doctor.doctor_contact,
              value: 'clinic_admin_contact',
            },
            {
              label:
                formTranslation.common.clinic_admin +
                ' ' +
                formTranslation.doctor.gender,
              value: 'clinic_admin_gender',
            },
          ]" :module-name="formTranslation.common.clinic" module-type="clinic"
            @reloadList="getClinicData"></module-data-import>

          <!-- data export module -->
          <module-data-export v-if="kcCheckPermission('clinic_export')" :module-data="clinicsList.data"
            :module-name="formTranslation.clinic.clinic_list" module-type="clinic">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" x2="12" y1="15" y2="3"></line>
            </svg>
            {{ formTranslation.common.import }}
          </module-data-export>

          <!-- Add Clinic button -->
          <button v-if="kcCheckPermission('clinic_add')" @click="handleClinicForm({})"
            class="items-center justify-center whitespace-nowrap rounded-md text-sm font-medium bg-black text-white px-4 py-2 hover:bg-gray-800">
            <i class="fa fa-plus"></i> {{ formTranslation.clinic.add_clinic }}
          </button>
        </div>
      </div>

      <!-- Global Search -->
      <div class="relative mb-6">
        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        <input v-model="serverParams.searchTerm" @input="globalFilter({ searchTerm: serverParams.searchTerm })"
          placeholder="Search clinic data..."
          class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text" />
      </div>

      <!-- Column Filters -->
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 pb-2">
        <!-- ID Filter -->
        <div class="relative">
          <input v-model="serverParams.columnFilters.id" @input="debouncedColumnFilter" placeholder="Filter by ID"
            class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
            type="text" />
          <button v-if="serverParams.columnFilters.id" @click="clearFilter('id')"
            class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Name Filter -->
        <div class="relative">
          <input v-model="serverParams.columnFilters.name" @input="debouncedColumnFilter" placeholder="Filter by name"
            class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
            type="text" />
          <button v-if="serverParams.columnFilters.name" @click="clearFilter('name')"
            class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Email Filter -->
        <div class="relative">
          <input v-model="serverParams.columnFilters.email" @input="debouncedColumnFilter" placeholder="Filter by email"
            class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
            type="text" />
          <button v-if="serverParams.columnFilters.email" @click="clearFilter('email')"
            class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Specialties Filter -->
        <div class="relative">
          <select v-model="serverParams.columnFilters.specialties" @change="debouncedColumnFilter"
            class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 appearance-none">
            <option value="">All Specialties</option>
            <option v-for="spec in specialization" :key="spec.value" :value="spec.label">
              {{ spec.label }}
            </option>
          </select>
          <button v-if="serverParams.columnFilters.specialties" @click="clearFilter('specialties')"
            class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Status Filter -->
        <div class="relative">
          <select v-model="serverParams.columnFilters.status" @change="debouncedColumnFilter"
            class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 appearance-none">
            <option value="">All Status</option>
            <option value="1">Active</option>
            <option value="0">Inactive</option>
          </select>
          <button v-if="serverParams.columnFilters.status" @click="clearFilter('status')"
            class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- Bulk Actions Section -->
      <div v-if="selectedRows.length > 0" class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <span class="text-sm font-medium text-gray-700">
              {{ selectedRows.length }} clinic{{ selectedRows.length > 1 ? 's' : '' }} selected
            </span>
            <div class="flex gap-2">
              <select v-model="globalCheckboxApplyData.action_perform" class="border border-gray-300 rounded-md text-sm p-1">
                <option value="" disabled selected>-- Select Action --</option>
                <option v-for="action in globalCheckboxApplyDataActions" :key="action.value" :value="action.value">
                  {{ action.label }}
                </option>
              </select>
              <button @click="confirmDelete" 
                class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="!globalCheckboxApplyData.action_perform || selectedRows.length === 0">
                Apply
              </button>
            </div>
          </div>
          <button @click="selectedRows = []; updateGlobalCheckboxData();" class="text-sm text-gray-500 hover:text-gray-700">
            Clear selection
          </button>
        </div>
      </div>

      <!-- Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input type="checkbox" @change="selectAll" :checked="isAllSelected" class="rounded border-gray-300" />
              </th>
              <th v-for="column in clinicsList.column.filter(
                (col) =>
                  col.field !== 'clinic_full_address' &&
                  col.field !== 'actions'
              )" :key="column.field" @click="handleSort(column.field)"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none">
                <div class="flex items-center space-x-1">
                  <span>{{ column.label }}</span>
                  <span class="flex flex-col" v-if="column.sortable !== false">
                    <i class="fas fa-chevron-up transform -mb-1" :class="{
                      'text-blue-600':
                        serverParams.sort[0].field === column.field &&
                        serverParams.sort[0].type === 'asc',
                      'text-gray-400':
                        serverParams.sort[0].field !== column.field ||
                        serverParams.sort[0].type !== 'asc',
                    }"></i>
                    <i class="fas fa-chevron-down transform" :class="{
                      'text-blue-600':
                        serverParams.sort[0].field === column.field &&
                        serverParams.sort[0].type === 'desc',
                      'text-gray-400':
                        serverParams.sort[0].field !== column.field ||
                        serverParams.sort[0].type !== 'desc',
                    }"></i>
                  </span>
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="(row, index) in clinicsList.data" :key="row.id" class="hover:bg-gray-50" :class="{'bg-blue-50': selectedRows.some(selected => selected.id === row.id)}">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" v-model="selectedRows" :value="row" class="rounded border-gray-300" @change="updateGlobalCheckboxData" />
              </td>
              <!-- Row data -->
              <td class="px-6 py-4 whitespace-nowrap">{{ row.id }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img v-if="row.profile_image" :src="row.profile_image" class="h-10 w-10 rounded-full mr-3" />
                  <b-avatar v-else :text="getInitials(row.name)" class="mr-3">
                  </b-avatar>
                  {{ row.name }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.email }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ row.clinic_admin_email }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ row.telephone_no }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ row.specialties }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center justify-center space-x-2">
                  <!-- Toggle button -->
                  <toggle-switch v-if="kcCheckPermission('clinic_edit')" :value="row.status === '1' ? 'on' : 'off'"
                    @input="
                      (value) => {
                        // Update the local state immediately for the toggle animation
                        row.status = value === 'on' ? '1' : '0';
                        // Then call the status change function
                        changeModuleValueStatus({
                          module_type: 'clinics',
                          id: row.id,
                          value: value === 'on' ? '1' : '0',
                        });
                      }
                    " on-value="on" off-value="off" />
                  <span :class="[
                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                    row.status === '1'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800',
                  ]">
                    {{ row.status === "1" ? "Active" : "Inactive" }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex gap-2">
                  <button v-if="kcCheckPermission('clinic_edit')" @click="handleClinicForm(row)"
                    class="p-1 hover:bg-gray-100 rounded">
                    <i class="fa fa-pen-alt text-gray-600"></i>
                  </button>
                  <button v-if="kcCheckPermission('clinic_edit')" @click="resendRequest(row.clinic_admin_id)"
                    class="p-1 hover:bg-gray-100 rounded">
                    <i class="fa fa-paper-plane text-gray-600"></i>
                  </button>
                  <button v-if="kcCheckPermission('clinic_delete')" @click="deleteClinicData(index + 1, row)"
                    class="p-1 hover:bg-gray-100 rounded">
                    <i class="fa fa-trash text-red-500"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-700">Rows per page:</span>
            <select v-model="serverParams.perPage" @change="
              onPerPageChange({ currentPerPage: serverParams.perPage, currentPage: serverParams.page })
              " class="border border-gray-300 rounded-md text-sm p-1">
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
            </select>
          </div>
          <div class="flex items-center gap-4">
            <span class="text-sm text-gray-700">
              Page {{ serverParams.page }} of
              {{ Math.ceil(totalRows / serverParams.perPage) }}
            </span>
            <div class="flex gap-2">
              <button @click="onPageChange({ currentPage: serverParams.page - 1 })" :disabled="serverParams.page === 1"
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button @click="onPageChange({ currentPage: serverParams.page + 1 })" :disabled="serverParams.page >=
                Math.ceil(totalRows / serverParams.perPage)
                " class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Clinic Modal -->
    <ClinicForm v-if="isAddClinicModalOpen" :initial-data="clinicFormData" :is-edit-mode="!!editingClinicId"
      @submit-success="handleClinicSave" @cancel="closeClinicModal" />

  </div>
</template>

<script>
import { post, get } from "../../config/request";
import ClinicForm from "../../components/Clinic/ClinicForm";

export default {
  components: {
    ClinicForm,
  },

  data: () => {
    return {
      clinicsList: {
        column: [],
        data: [],
      },
      showImportModal: false,
      isAddClinicModalOpen: false,
      clinicFormObj: {},
      editingClinicId: null,
      clinicFormData: {},
      isLoading: true,
      selectedRows: [],
      isAllSelected: false,
      searchName: "",
      searchEmail: "",
      serverParams: {
        columnFilters: {
          id: "",
          name: "",
          email: "",
          telephone_no: "",
          specialties: "",
          status: "",
        },
        sort: [
          {
            field: "id",
            type: "asc",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
        type: "list",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      globalCheckboxApplyData: {},
      globalCheckboxApplyDataActions: [],
    };
  },
  created() {
    this.debouncedGlobalSearch = _.debounce(this.handleGlobalSearch, 300);
    this.debouncedColumnFilter = _.debounce(this.handleColumnFilter, 300);
  },
  mounted() {
    this.init();
  },
  methods: {
    init: function () {
      this.clinicsList = this.defaultClinicList();
      this.getClinicData();
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData();
      this.globalCheckboxApplyDataActions =
        this.defaultGlobalCheckboxApplyDataActions();
    },
    getClinicData: function () {
      get("clinic_list", this.serverParams, false)
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.isLoading = false;
            this.clinicsList.data = data.data.data;
            this.totalRows = data.data.total;
          } else {
            this.clinicsList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          this.isLoading = false;
          console.log(error);
          displayErrorMessage(this.formTranlation.common.internal_server_error);
        });
    },

    handleSort(field) {
      // Skip sorting for non-sortable columns
      const column = this.clinicsList.column.find((col) => col.field === field);
      if (column && column.sortable === false) return;

      // Determine sort direction
      let type = "asc";
      if (this.serverParams.sort?.[0]?.field === field) {
        type = this.serverParams.sort[0].type === "asc" ? "desc" : "asc";
      }

      // Update sort params and fetch data
      this.updateParams({
        sort: [
          {
            field,
            type,
          },
        ],
      });

      // // Format sort parameter according to API requirements
      // const sortParams = {
      //   "sort[]": {
      //     field: field,
      //     type: type,
      //   },
      // };

      // Update params and fetch data
      // this.updateParams(sortParams);
    },

    handleColumnFilter() {
      const hasFilters = Object.values(this.serverParams.columnFilters).some(
        (value) => !!value
      );
      const hadFilters = Object.values(this.oldServerParams.columnFilters).some(
        (value) => !!value
      );

      if (hasFilters || hadFilters) {
        this.oldServerParams.columnFilters = {
          ...this.serverParams.columnFilters,
        };
        this.updateParams({
          columnFilters: this.serverParams.columnFilters,
          page: 1,
        });
      }
    },

    clearFilter(filterName) {
      // Clear the specific filter
      this.serverParams.columnFilters[filterName] = "";

      // // Update old params to trigger the filter
      // this.oldServerParams.columnFilters = {
      //   ...this.serverParams.columnFilters
      // };

      // // Trigger the filter update
      // this.updateParams({
      //   columnFilters: this.serverParams.columnFilters,
      //   page: 1
      // });
    },

    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: "", // Start with no action selected
        module: "clinics",
        data: [],
      };
    },

    defaultGlobalCheckboxApplyDataActions: function () {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active,
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive,
        },
        {
          value: "resend_credential",
          label: this.formTranslation.receptionist.resend_credential,
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt,
        },
      ];
    },

    confirmDelete() {
      let content = "";
      if (this.globalCheckboxApplyData.action_perform === "delete") {
        content = this.formTranslation.common.py_delete_clinic;
      } else if (
        this.globalCheckboxApplyData.action_perform === "resend_credential"
      ) {
        content = this.formTranslation.common.py_resend_credential;
      } else if (
        this.globalCheckboxApplyData.action_perform === "active" ||
        this.globalCheckboxApplyData.action_perform === "inactive"
      ) {
        content = this.formTranslation.common.py_status;
      }

      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: content,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.globalCheckboxApply();
          }
        });
    },

    globalCheckboxApply() {
      // Show loading indicator
      this.pageLoader = true;
      
      const action = this.globalCheckboxApplyData.action_perform;
      const selectedIds = this.globalCheckboxApplyData.data;
      
      console.log('Bulk action:', action);
      console.log('Selected clinic IDs:', selectedIds);
      
      // Track completion of all operations
      let completedOperations = 0;
      let successCount = 0;
      let errorCount = 0;
      const totalOperations = selectedIds.length;
      
      // Function to handle when all operations are completed
      const checkCompletion = () => {
        completedOperations++;
        
        if (completedOperations === totalOperations) {
          this.pageLoader = false;
          
          // Show success message
          this.$swal.fire({
            icon: successCount > 0 ? 'success' : 'error',
            title: successCount > 0 
              ? `Successfully processed ${successCount} clinic${successCount > 1 ? 's' : ''}${errorCount > 0 ? ` (${errorCount} failed)` : ''}`
              : 'Failed to process clinics',
            showConfirmButton: false,
            timer: 2000
          });
          
          // Clear selection
          this.selectedRows = [];
          this.updateGlobalCheckboxData();
          
          // Refresh data
          this.getClinicData();
          
          // Update clinic list in the store if needed
          if (action === 'delete' || action === 'active' || action === 'inactive') {
            this.$store.dispatch("fetchAllClinic", { self: this });
          }
        }
      };
      
      // Process each selected item with the appropriate API endpoint
      selectedIds.forEach(id => {
        // Different API endpoints for different actions
        if (action === 'delete') {
          // Use the same API as single delete
          post("clinic_delete", { id })
            .then(response => {
              console.log('Delete response for ID', id, ':', response.data);
              if (response.data.status === true) {
                successCount++;
              } else {
                errorCount++;
              }
              checkCompletion();
            })
            .catch(error => {
              console.error('Error deleting clinic ID', id, ':', error);
              errorCount++;
              checkCompletion();
            });
        } 
        else if (action === 'active' || action === 'inactive') {
          // Use the same API as individual status change
          post("change_module_value_status", {
            module_type: 'clinics',
            id: id,
            value: action === 'active' ? '1' : '0'
          })
            .then(response => {
              console.log('Status change response for ID', id, ':', response.data);
              if (response.data.status === true) {
                successCount++;
              } else {
                errorCount++;
              }
              checkCompletion();
            })
            .catch(error => {
              console.error('Error changing status for clinic ID', id, ':', error);
              errorCount++;
              checkCompletion();
            });
        }
        else if (action === 'resend_credential') {
          // Use the same API as individual resend credentials
          post("resend_credential", { id: id })
            .then(response => {
              console.log('Resend credentials response for ID', id, ':', response.data);
              if (response.data.status === true) {
                successCount++;
              } else {
                errorCount++;
              }
              checkCompletion();
            })
            .catch(error => {
              console.error('Error resending credentials for clinic ID', id, ':', error);
              errorCount++;
              checkCompletion();
            });
        }
      });
    },

    updateParams: function (newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getClinicData();
    },

    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 300),
    onColumnFilter: _.debounce(function (params) {
      var emptyValue = true;
      var emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value, index, array) {
        if (value) {
          emptyValue = false;
        }
      });
      Object.values(this.oldServerParams.columnFilters).map(function (
        value,
        index,
        array
      ) {
        if (value) {
          emptyValue2 = false;
        }
      });
      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
          {},
          params.columnFilters
        );
        this.updateParams({
          columnFilters: params.columnFilters,
          perPage: this.serverParams.perPage,
          page: 1,
        });
      }
    }, 300),
    defaultClinicList: function () {
      return {
        column: [
          // {
          //     field: 'profile',
          //     // label: this.formTranslation.clinic.profile_img,
          // },
          {
            field: "id",
            label: this.formTranslation.common.id,
            width: "80px",
            filterOptions: {
              enabled: true, // enable filter for this column
              placeholder: this.formTranslation.common.id,
              filterValue: "",
            },
          },
          {
            field: "name",
            label: this.formTranslation.clinic.dt_lbl_name,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.clinic.dt_plh_name_filter,
              filterValue: "",
            },
          },
          {
            field: "email",
            label: this.formTranslation.clinic.dt_lbl_email,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.clinic.dt_lbl_email,
              filterValue: "",
            },
          },
          {
            field: "clinic_admin_email",
            label: this.formTranslation.common.clinic_admin_email,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.common.clinic_admin_email,
              filterValue: "",
            },
          },
          {
            field: "telephone_no",
            label: this.formTranslation.clinic.dt_lbl_contect,
            width: "160px",
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.clinic.dt_plh_fltr_contact,
              filterValue: "",
            },
          },
          {
            field: "specialties",
            label: this.formTranslation.clinic.dt_lbl_specialties,
            width: "150px",
            sortable: false,
            filterOptions: {
              enabled: true, // enable filter for this column
              placeholder: this.formTranslation.clinic.dt_plh_fltr_specialitiy,
              filterValue: "",
            },
            html: true,
          },
          {
            field: "clinic_full_address",
            label: this.formTranslation.clinic.plh_address,
            sortable: false,
            filterOptions: {
              enabled: true, // enable filter for this column
              placeholder: this.formTranslation.clinic.plh_address,
              filterValue: "",
            },
          },
          {
            field: "status",
            label: this.formTranslation.clinic.dt_lbl_status,
            filterOptions: {
              enabled: true, // enable filter for this column
              placeholder:
                this.formTranslation.static_data.dt_lbl_plh_sr_fltr_status,
              filterDropdownItems: [
                { value: "1", text: this.formTranslation.common.active },
                { value: "0", text: this.formTranslation.common.inactive },
              ],
              filterValue: "",
            },
          },
          {
            field: "actions",
            sortable: false,
            label: this.formTranslation.clinic.dt_lbl_action,
          },
        ],
        data: [],
      };
    },

    deleteClinicData: function (index, props) {
      if (this.clinicsList.data[index - 1] !== undefined) {
        let ele = $("#user_delete_" + index);

        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.py_delete_clinic,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              // Disable button and show loading state
              ele.prop("disabled", true);
              $(ele).find("i").removeClass("fa fa-trash");
              $(ele).find("i").addClass("fa fa-sync fa-spin");

              // Perform delete operation
              post("clinic_delete", {
                id: this.clinicsList.data[index - 1].id,
              })
                .then((data) => {
                  // Restore button state
                  ele.prop("disabled", false);
                  $(ele).find("i").removeClass("fa fa-sync fa-spin");
                  $(ele).find("i").addClass("fa fa-trash");

                  // Check response status
                  if (
                    data.data.status !== undefined &&
                    data.data.status === true
                  ) {
                    // Remove item from data if not default clinic
                    if (this.userData.default_clinic != props.id) {
                      this.clinicsList.data.splice(index - 1, 1);
                    }

                    // Refresh clinics
                    this.$store.dispatch("fetchAllClinic", { self: this });

                    // Show success message
                    this.$swal.fire({
                      icon: "success",
                      title: data.data.message,
                      showConfirmButton: false,
                      timer: 1500,
                    });
                  } else {
                    // Show error message
                    this.$swal.fire({
                      icon: "error",
                      title: data.data.message,
                      showConfirmButton: false,
                      timer: 1500,
                    });
                  }
                })
                .catch((error) => {
                  // Restore button state
                  ele.prop("disabled", false);
                  $(ele).find("i").removeClass("fa fa-sync fa-spin");
                  $(ele).find("i").addClass("fa fa-trash");

                  // Log error and show generic error message
                  console.log(error);
                  this.$swal.fire({
                    icon: "error",
                    title: this.formTranslation.common.internal_server_error,
                    showConfirmButton: false,
                    timer: 1500,
                  });
                });
            }
          });
      }
    },
    resendRequest: function (id) {
      var element = $("#resend_" + id).find("i");
      element.removeClass("fa fa-paper-plane ");
      element.addClass("fa fa-spinner fa-spin");
      post("resend_credential", { id: id })
        .then((data) => {
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-paper-plane");
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranlation.common.internal_server_error);
        });
    },
    getInitials(name) {
      if (name !== undefined && name !== "" && name !== null) {
        const patient_name = name.split(" ");
        const initials = patient_name.map((patient_name) =>
          patient_name.charAt(0).toUpperCase()
        );
        return initials.join("");
      } else {
        return " - ";
      }
    },
    selectAll() {
      this.isAllSelected = !this.isAllSelected;
      this.selectedRows = this.isAllSelected ? [...this.clinicsList.data] : [];
      this.updateGlobalCheckboxData();
    },
    
    updateGlobalCheckboxData() {
      // Format data for API - Include only the IDs as a simple array
      // This format is what the API endpoint expects based on the server's response
      this.globalCheckboxApplyData.data = this.selectedRows.map(row => row.id);
    },

    onSelectedRowsChange(params) {
      this.selectedRows = params.selectedRows;
      this.isAllSelected =
        this.selectedRows.length === this.clinicsList.data.length;
      this.updateGlobalCheckboxData();
    },

    // Update this method to handle both checkbox and status changes
    handleStatusChange(value, id) {
      this.changeModuleValueStatus({
        module_type: "clinics",
        id: id,
        value: value,
      });
    },

    // Add this if you don't have it
    changeModuleValueStatus(params) {
      post("change_module_value_status", params)
        .then((data) => {
          if (data.data.status === true) {
            displayMessage(data.data.message);
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          console.error(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleClinicForm(clinic = {}) {
      this.editingClinicId = clinic.id || null;
      this.clinicFormData = { ...clinic };
      this.isAddClinicModalOpen = true;

      // If editing, fetch the full clinic data
      if (this.editingClinicId) {
        this.fetchClinicData(this.editingClinicId);
      }
    },

    async fetchClinicData(id) {
      try {
        const response = await get("clinic_edit", { id });
        if (response.data?.status) {
          this.clinicFormData = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinic data:", error);
        displayErrorMessage(
          this.formTranslation.clinic.doctor_record_not_found
        );
      }
    },

    closeClinicModal() {
      this.isAddClinicModalOpen = false;
      this.editingClinicId = null;
      this.clinicFormData = {};
    },

    handleClinicSave() {
      this.closeClinicModal();
      this.getClinicData(); // Refresh the list
    },
  },
  computed: {
    specialization() {
      if (
        this.$store.state.staticDataModule.static_data.specialization !==
        undefined &&
        this.$store.state.staticDataModule.static_data.specialization.length > 0
      ) {
        return this.$store.state.staticDataModule.static_data.specialization;
      }
      return [];
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
  watch: {
    selectedRows(newVal) {
      this.isAllSelected = newVal.length === this.clinicsList.data.length;
    },
  },
};
</script>
