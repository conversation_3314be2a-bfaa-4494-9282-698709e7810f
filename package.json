{"name": "kivi-care", "version": "1.0.0", "description": "Medroid - medical dashboard", "repository": "", "scripts": {"dev": "cross-env NODE_ENV=development webpack --config=node_modules/laravel-mix/setup/webpack.config.js", "development": "npm run dev", "watch": "npm run dev -- --watch", "prod": "cross-env NODE_ENV=production webpack --config=webpack.prod.config.js", "production": "npm run prod", "prod-mix": "cross-env NODE_ENV=production webpack --config=node_modules/laravel-mix/setup/webpack.config.js"}, "author": "medroid", "license": "ISC", "dependencies": {"@fortawesome/fontawesome-free": "^6.6.0", "@fullcalendar/core": "^4.4.2", "@fullcalendar/daygrid": "^4.4.2", "@fullcalendar/interaction": "^4.4.2", "@fullcalendar/resource-timeline": "^4.4.2", "@fullcalendar/timegrid": "^4.4.2", "@fullcalendar/vue": "^4.4.2", "@staaky/tipped": "^4.7.0", "@toast-ui/vue-image-editor": "^3.15.2", "animate.css": "^3.7.2", "apexcharts": "^3.22.0", "axios": "^0.19.2", "bootstrap": "^4.5.2", "bootstrap-vue": "^2.17.3", "crypto-js": "^4.2.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "jquery": "^3.5.1", "jquery-confirm": "^3.3.4", "jspdf": "^2.5.2", "jspdf-autotable": "^3.5.25", "laravel-mix": "^5.0.6", "lodash": "^4.17.20", "moment": "^2.29.1", "moment-timezone": "^0.5.43", "node-snackbar": "^0.1.16", "pdfjs-dist": "^4.10.38", "perfect-scrollbar": "^1.5.0", "popper.js": "^1.16.1", "print-area-js": "^1.0.2", "quill": "^1.3.7", "sass": "^1.26.12", "sweetalert2": "^11.15.10", "v-calendar": "^1.0.8", "vue": "^2.6.12", "vue-apexcharts": "^1.6.0", "vue-carousel": "^0.18.0", "vue-color": "^2.8.1", "vue-excel-xlsx": "^1.2.2", "vue-fragment": "^1.6.0", "vue-good-table": "^2.21.9", "vue-i18n": "^8.22.4", "vue-json-csv": "^1.2.12", "vue-json-editor": "^1.4.3", "vue-loading-overlay": "^3.4.2", "vue-multiselect": "^2.1.9", "vue-phone-number-input": "^1.12.13", "vue-print-nb": "^1.5.0", "vue-qrcode-component": "^2.1.1", "vue-router": "^3.4.6", "vue-signature": "^2.5.6", "vue-sweetalert": "^0.1.18", "vue2-editor": "^2.10.2", "vue2-timepicker": "^1.1.5", "vuedraggable": "^2.24.3", "vuelidate": "^0.7.5", "vuex": "^3.5.1"}, "devDependencies": {"autoprefixer": "^9.8.8", "cross-env": "^7.0.3", "postcss": "^7.0.39", "resolve-url-loader": "^3.1.0", "sass-loader": "^8.0.2", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue-template-compiler": "^2.7.16"}}