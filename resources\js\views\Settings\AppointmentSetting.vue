<template>
  <div>
    <div
      v-if="formLoader"
      class="fixed inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50"
    >
      <loader-component-2></loader-component-2>
    </div>

    <div class="space-y-6">
      <!-- Appointment Restriction Card -->
      <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <h2 class="text-xl font-semibold">
                {{ formTranslation.appointments.restrict_appointment_detail }}
              </h2>
              <a
                v-if="request_status == 'off'"
                href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#appointment-setting"
                target="_blank"
                class="text-gray-500 hover:text-gray-700"
              >
                <i class="fa fa-question-circle"></i>
              </a>
            </div>
          </div>
        </div>

        <form @submit.prevent="handleSubmit" class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Post Appointment -->
            <div>
              <label
                for="post_appointment"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                {{ formTranslation.appointments.post_appointment }}
                <span class="text-red-500">*</span>
              </label>
              <input
                id="post_appointment"
                v-model="restrictAppointment.post_book"
                type="number"
                min="0"
                max="100000000000"
                :disabled="isDisable"
                class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{
                  'border-red-500':
                    submitted && $v.restrictAppointment.post_book.$error,
                }"
              />
              <div
                v-if="submitted && !$v.restrictAppointment.post_book.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.appointments.post_appointment_required }}
              </div>
            </div>

            <!-- Pre Appointment -->
            <div>
              <label
                for="pre_appointment"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                {{ formTranslation.appointments.pre_appointment }}
                <span class="text-red-500">*</span>
              </label>
              <input
                id="pre_appointment"
                v-model="restrictAppointment.pre_book"
                type="number"
                min="0"
                max="100000000000"
                :disabled="isDisable"
                class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{
                  'border-red-500':
                    submitted && $v.restrictAppointment.pre_book.$error,
                }"
              />
              <div
                v-if="submitted && !$v.restrictAppointment.pre_book.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.appointments.pre_appointment_required }}
              </div>
            </div>
          </div>

          <!-- Pre Post Note -->
          <div class="mt-4">
            <p class="text-gray-600">
              {{ formTranslation.appointments.pre_post_note }}
            </p>
          </div>

          <!-- Same Day Booking -->
          <div class="mt-6">
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="restrictAppointment.only_same_day_book"
                class="form-checkbox h-5 w-5 text-blue-600 rounded"
                :true-value="'on'"
                :false-value="'off'"
                @change="onOnlySameDayBookChange"
              />
              <span class="ml-2 text-gray-700 font-medium">{{
                formTranslation.appointments.same_day_booking_only_lbl
              }}</span>
            </label>
          </div>

          <!-- Submit Button -->
          <div class="mt-6 flex justify-end">
            <button
              type="submit"
              :disabled="restrictAppointmentLoading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              <i
                :class="
                  restrictAppointmentLoading
                    ? 'fa fa-spinner fa-spin'
                    : 'fa fa-save'
                "
                class="mr-2"
              ></i>
              {{
                restrictAppointmentLoading
                  ? formTranslation.common.loading
                  : formTranslation.common.save
              }}
            </button>
          </div>
        </form>
      </div>

      <!-- Multi File Upload Card -->
      <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b">
          <h2 class="text-xl font-semibold">
            {{ formTranslation.appointments.multi_file_upload }}
          </h2>
        </div>
        <div class="p-6">
          <label class="inline-flex items-center">
            <input
              type="checkbox"
              v-model="status"
              class="form-checkbox h-5 w-5 text-blue-600 rounded"
              :true-value="'on'"
              :false-value="'off'"
              @change="onFileUploadStatusChange"
            />
            <span class="ml-2 text-gray-700 font-medium">{{
              formTranslation.appointments.appointment_multi_file_upload
            }}</span>
          </label>
        </div>
      </div>

      <!-- Appointment Daily Reminder Card -->
      <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b">
          <h2 class="text-xl font-semibold">
            {{ formTranslation.appointments.appointment_daily_reminder }}
          </h2>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Email Reminder -->
            <div>
              <label class="inline-flex items-center">
                <input
                  type="checkbox"
                  v-model="reminder.status"
                  class="form-checkbox h-5 w-5 text-blue-600 rounded"
                  :true-value="'on'"
                  :false-value="'off'"
                />
                <span class="ml-2 text-gray-700 font-medium">{{
                  formTranslation.appointments.appointment_email_reminder
                }}</span>
              </label>
            </div>

            <!-- SMS Reminder -->
            <div v-if="userData.addOns.kiviPro">
              <label class="inline-flex items-center">
                <input
                  type="checkbox"
                  v-model="reminder.sms_status"
                  class="form-checkbox h-5 w-5 text-blue-600 rounded"
                  :true-value="'on'"
                  :false-value="'off'"
                />
                <span class="ml-2 text-gray-700 font-medium">{{
                  formTranslation.appointments.appointment_sms_reminder
                }}</span>
              </label>
            </div>

            <!-- WhatsApp Reminder -->
            <div
              v-if="userData.addOns.kiviPro && userData.pro_version >= '1.2.0'"
            >
              <label class="inline-flex items-center">
                <input
                  type="checkbox"
                  v-model="reminder.whatapp_status"
                  class="form-checkbox h-5 w-5 text-blue-600 rounded"
                  :true-value="'on'"
                  :false-value="'off'"
                />
                <span class="ml-2 text-gray-700 font-medium">{{
                  formTranslation.appointments.appointment_whatsapp_reminder
                }}</span>
              </label>
            </div>
          </div>

          <!-- Reminder Time Selection -->
          <div
            v-if="
              reminder.status == 'on' ||
              reminder.whatapp_status == 'on' ||
              reminder.sms_status == 'on'
            "
            class="mt-6"
          >
            <div class="max-w-xs">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{
                  formTranslation.appointments.notice_of_appointment_reminder
                }}
              </label>
              <select
                v-model="reminder.time"
                class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option v-for="i in 48" :key="i" :value="i < 10 ? '0' + i : i">
                  {{ i < 10 ? "0" + i + ":00" : i + ":00" }}
                </option>
              </select>
            </div>
          </div>

          <!-- Save Button -->
          <div class="mt-6 flex justify-end">
            <button
              @click="appointmentReminder"
              :disabled="loading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              <i
                :class="loading ? 'fa fa-spinner fa-spin' : 'fa fa-save'"
                class="mr-2"
              ></i>
              {{
                loading
                  ? formTranslation.common.loading
                  : formTranslation.common.save
              }}
            </button>
          </div>
        </div>
      </div>

      <!-- Time Format Card -->
      <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center space-x-2">
            <h2 class="text-xl font-semibold">
              {{
                formTranslation.appointments.appointment_time_format +
                " " +
                formTranslation.common.deprecated
              }}
            </h2>
            <a
              href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#appointment-setting"
              target="_blank"
              class="text-gray-500 hover:text-gray-700"
            >
              <i class="fa fa-question-circle"></i>
            </a>
          </div>
        </div>

        <div class="p-6">
          <label class="inline-flex items-center">
            <input
              type="checkbox"
              :disabled="true"
              class="form-checkbox h-5 w-5 text-blue-600 rounded"
              :true-value="'on'"
              :false-value="'off'"
              @change="onAppointmentTimeFormat"
            />
            <span class="ml-2 text-gray-700 font-medium">{{
              formTranslation.appointments.appointment_time_24_format
            }}</span>
          </label>
        </div>
      </div>

      <!-- Description Notes Card -->
      <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b">
          <h2 class="text-xl font-semibold">
            {{ formTranslation.appointments.appointment_description_notes }}
          </h2>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="onAppointmentDescription"
                class="form-checkbox h-5 w-5 text-blue-600 rounded"
                :true-value="'on'"
                :false-value="'off'"
                @change="onAppointmentDescriptionChange"
              />
              <span class="ml-2 text-gray-700 font-medium">{{
                formTranslation.appointments.appointment_description
              }}</span>
            </label>

            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="onAppointmentPatientInfo"
                class="form-checkbox h-5 w-5 text-blue-600 rounded"
                :true-value="'on'"
                :false-value="'off'"
                @change="onAppointmentPatientInfoChange"
              />
              <span class="ml-2 text-gray-700 font-medium">{{
                formTranslation.appointments.show_patient_information
              }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Cancellation Buffer Card -->
      <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b">
          <h2 class="text-xl font-semibold">
            {{ formTranslation.appointments.appointment_cancellation_buffer }}
          </h2>
        </div>

        <div class="p-6">
          <p class="text-gray-600 mb-4">
            {{
              formTranslation.appointments.appointment_cancellation_buffer_note
            }}
          </p>

          <label class="inline-flex items-center">
            <input
              type="checkbox"
              v-model="cancellation_buffer.status"
              class="form-checkbox h-5 w-5 text-blue-600 rounded"
              :true-value="'on'"
              :false-value="'off'"
              @change="cancellationBufferStatus"
            />
            <span class="ml-2 text-gray-700 font-medium">{{
              formTranslation.appointments.enable_cancellation_buffer_lbl
            }}</span>
          </label>

          <div v-if="cancellation_buffer.status == 'on'" class="mt-6">
            <div class="max-w-xs">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.notice_of_cancellation_buffer }}
              </label>
              <select
                v-model="cancellation_buffer.time"
                class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option
                  v-for="(value, key) in intervalOptions"
                  :key="key"
                  :value="value.value"
                >
                  {{ value.label }}
                </option>
              </select>
            </div>
          </div>

          <!-- Save Button -->
          <div class="mt-6 flex justify-end">
            <button
              @click="appointmentCancellationBuffer"
              :disabled="loading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              <i
                :class="loading ? 'fa fa-spinner fa-spin' : 'fa fa-save'"
                class="mr-2"
              ></i>
              {{
                loading
                  ? formTranslation.common.loading
                  : formTranslation.common.save
              }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import {
  maxLength,
  maxValue,
  minLength,
  minValue,
  required,
} from "vuelidate/lib/validators";
export default {
  name: "UniqueIdSetting",
  components: {},
  data: () => {
    return {
      onAppointmentDescription: "on",
      onAppointmentPatientInfo: "on",
      restrictAppointment: {},
      restrictAppointmentLoading: false,
      loading: false,
      cancellation_buffer_loading: false,
      submitted: false,
      status: "off",
      onAppointmentTimeFormatStatus: "off",
      reminder: {
        time: "24",
        status: "off",
        sms_status: "off",
        whatapp_status: "off",
      },
      storePrevios: {
        pre_book: "",
        post_book: "",
      },
      formLoader: true,
      request_status: "off",
      isDisable: false,
      isCancellationBufferDisable: false,
      cancellation_buffer: {
        status: "off",
        time: "", // This will hold the selected time value
      },
      intervalOptions: [
        { value: "0.25", label: "15 minutes" },
        { value: "0.50", label: "30 minutes" },
        { value: "0.75", label: "45 minutes" },
        { value: "1", label: "1 hour" },
        { value: "2", label: "2 hours" },
        { value: "3", label: "3 hours" },
        { value: "4", label: "4 hours" },
        { value: "5", label: "5 hours" },
        { value: "6", label: "6 hours" },
        { value: "12", label: "12 hours" },
        { value: "24", label: "1 day" },
        { value: "48", label: "2 days" },
        { value: "72", label: "3 days" },
        { value: "96", label: "5 days" },
        { value: "144", label: "6 days" },
        { value: "168", label: "1 week" },
        { value: "336", label: "2 weeks" },
        { value: "504", label: "3 weeks" },
        { value: "672", label: "4 weeks" },
        { value: "840", label: "5 weeks" },
        { value: "1008", label: "6 weeks" },
        { value: "1176", label: "7 weeks" },
        { value: "1344", label: "8 weeks" },
      ],
    };
  },
  validations: {
    restrictAppointment: {
      pre_book: {
        required,
        minValue: minValue(0),
        maxValue: maxValue(365),
      },
      post_book: {
        required,
        minValue: minValue(0),
        maxValue: maxValue(365),
      },
    },
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.onAppointmentTimeFormatStatus =
      window.request_data !== undefined &&
      window.request_data.appointment_time_format === "on"
        ? "on"
        : "off";
    this.restrictAppointment = this.defaultData();
    this.edit();
    this.editAppointmentReminder();
    this.editAppointmentCancellationBuffer();
    this.getFileUploadStatus();
    this.getAppointmentDescription();
    this.getAppointmentPatientInfo();
    this.getModule();
  },
  methods: {
    defaultData() {
      return {
        pre_book: "0",
        post_book: "365",
        only_same_day_book: "off",
      };
    },
    handleSubmit() {
      this.submitted = true;
      // stop here if form is invalid
      this.$v.$touch();

      if (this.$v.restrictAppointment.$invalid) {
        return;
      }

      let pre_book_message = "";
      let alert_message =
        "Pre booking open before " +
        this.restrictAppointment.post_book +
        " days " +
        pre_book_message;

      if (this.restrictAppointment.pre_book != 0) {
        pre_book_message =
          " and close before " +
          this.restrictAppointment.pre_book +
          " days of booking date.";
      } else {
        alert_message += "before appointment booking date.";
      }

      alert_message =
        "Appointment pre booking open from " +
        this.restrictAppointment.post_book +
        " days " +
        pre_book_message +
        " before appointment booking date. ";

      this.$swal
        .fire({
          title: this.formTranslation.appointments.booking_restriction,
          text: alert_message,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#dc3545", // Bootstrap danger color
          cancelButtonColor: "#6c757d", // Bootstrap secondary color
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.restrictAppointmentLoading = true;
            post("restrict_appointment_save", this.restrictAppointment)
              .then((response) => {
                this.restrictAppointmentLoading = false;
                if (
                  response.data.status !== undefined &&
                  response.data.status === true
                ) {
                  this.submitted = false;
                  this.storePrevios = Object.assign(
                    {},
                    this.storePrevios,
                    this.restrictAppointment
                  );

                  this.$swal.fire({
                    icon: "success",
                    title: "Success",
                    text: response.data.message,
                    timer: 1500,
                    showConfirmButton: false,
                  });
                } else {
                  this.restrictAppointmentLoading = false;
                  this.submitted = false;

                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: response.data.message,
                    timer: 1500,
                    showConfirmButton: false,
                  });
                }
              })
              .catch((error) => {
                this.submitted = false;
                this.restrictAppointmentLoading = false;
                console.log(error);

                this.$swal.fire({
                  icon: "error",
                  title: "Error",
                  text: this.formTranslation.common.internal_server_error,
                  timer: 1500,
                  showConfirmButton: false,
                });
              });
          }
        });
    },
    getAppointmentDescription: function () {
      this.formLoader = true;
      get("get_appointment_description_status", {}).then((res) => {
        this.formLoader = false;
        this.onAppointmentDescription = res.data.data == "on" ? "on" : "off";
      });
    },
    getAppointmentPatientInfo: function () {
      this.formLoader = true;
      get("get_appointment_patient_info_status", {}).then((res) => {
        this.formLoader = false;
        this.onAppointmentPatientInfo = res.data.data == "on" ? "on" : "off";
      });
    },
    getFileUploadStatus: function () {
      this.formLoader = true;
      get("get_multifile_upload_status", {})
        .then((response) => {
          this.status = response.data.data;
          this.formLoader = false;
        })
        .catch((error) => {
          this.formLoader = false;
          console.log("error", error);
        });
    },
    onFileUploadStatusChange: function (value) {
      get("change_multifile_upload_status", { status: value })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit("appointmentModule/FILE_UPLOAD_STATUS", {
              data: value,
            });
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log("error", error);
        });
    },
    onOnlySameDayBookChange: function (value) {
      if (value == "on") {
        this.isDisable = true;
      } else {
        this.isDisable = false;
      }
    },
    cancellationBufferStatus: function (value) {
      if (value == "on") {
        this.isCancellationBufferDisable = true;
      } else {
        this.isCancellationBufferDisable = false;
      }
    },
    edit() {
      this.formLoader = true;
      get("restrict_appointment_edit", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            setTimeout(() => {
              this.restrictAppointment = response.data.data;
              if (this.restrictAppointment.only_same_day_book === "on") {
                this.isDisable = true;
              } else {
                this.isDisable = false;
              }
              this.storePrevios = Object.assign(
                {},
                this.storePrevios,
                response.data.data
              );
            }, 200);
            this.formLoader = false;
          }
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    appointmentReminder: function () {
      this.loading = true;
      post("appointment_reminder_notificatio_save", this.reminder)
        .then((response) => {
          this.loading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("error", error);
        });
    },
    appointmentCancellationBuffer: function () {
      this.cancellation_buffer_loading = true;
      post("appointment_cancellation_buffer_save", this.cancellation_buffer)
        .then((response) => {
          this.cancellation_buffer_loading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.cancellation_buffer_loading = false;
          console.log("error", error);
        });
    },
    editAppointmentCancellationBuffer: function () {
      this.formLoader = true;
      get("get_appointment_cancellation_buffer", { type: "setting" })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.cancellation_buffer = response.data.data;
          }
          this.formLoader = false;
        })
        .catch((error) => {
          console.log(error);
          this.formLoader = false;
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    editAppointmentReminder: function () {
      this.formLoader = true;
      get("get_appointment_reminder_notification", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.reminder = response.data.data;
          }
          this.formLoader = false;
        })
        .catch((error) => {
          console.log(error);
          this.formLoader = false;
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    onAppointmentTimeFormat: function () {
      post("update_appointment_time_format", {
        timeFormat: this.onAppointmentTimeFormatStatus,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    onAppointmentDescriptionChange: function (value) {
      post("appointment_description_status_change", {
        status: this.onAppointmentDescription,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit("appointmentModule/DESCRIPTION_STATUS", {
              data: value,
            });
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    onAppointmentPatientInfoChange: function (value) {
      post("appointment_patient_info_status_change", {
        status: this.onAppointmentPatientInfo,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            this.$store.commit("appointmentModule/PATIENT_INFO_STATUS", {
              data: value,
            });
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    prevValCheck() {
      if (
        parseInt(this.restrictAppointment.pre_book) >
          parseInt(this.restrictAppointment.post_book) ||
        parseInt(this.restrictAppointment.pre_book) ===
          parseInt(this.restrictAppointment.post_book)
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
