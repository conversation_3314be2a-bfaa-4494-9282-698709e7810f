<?php
/**
 * Migration: Create TDL Labs Database Tables
 * File: kc_tdl_create_database_tables.php
 */

if (!defined('ABSPATH')) {
    exit;
}

class CreateTdlLabsTables {
    /**
     * Run the migration - creates all tables for TDL Labs
     */
    public function up() {
        global $wpdb;

        error_log("[Migration] Starting TDL Labs tables creation");

        $charset_collate = $wpdb->get_charset_collate();

        // Define table names
        $clinic_settings_table = $wpdb->prefix . 'kc_tdl_clinic_settings';
        $test_catalog_table = $wpdb->prefix . 'kc_tdl_test_catalog';
        $test_requests_table = $wpdb->prefix . 'kc_tdl_test_requests';
        $test_request_items_table = $wpdb->prefix . 'kc_tdl_test_request_items';
        $test_results_table = $wpdb->prefix . 'kc_tdl_test_results';
        $test_result_items_table = $wpdb->prefix . 'kc_tdl_test_result_items';

        // 1. TDL Clinic Settings Table
        $clinic_settings_sql = "CREATE TABLE IF NOT EXISTS {$clinic_settings_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            clinic_id bigint(20) UNSIGNED NOT NULL,
            account_id varchar(255) NOT NULL,
            api_key varchar(255) NOT NULL,
            sender_id varchar(50) NOT NULL,
            azure_storage_connection varchar(255) DEFAULT NULL,
            azure_storage_container varchar(255) DEFAULT NULL,
            auto_process_results TINYINT(1) DEFAULT 1,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY clinic_id (clinic_id)
        ) {$charset_collate};";

        // 2. TDL Test Catalog Table
        $test_catalog_sql = "CREATE TABLE IF NOT EXISTS {$test_catalog_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            test_code varchar(50) NOT NULL,
            test_name varchar(255) NOT NULL,
            test_description text DEFAULT NULL,
            test_category varchar(100) DEFAULT NULL,
            sample_type varchar(100) DEFAULT NULL,
            normal_range text DEFAULT NULL,
            price decimal(10,2) DEFAULT NULL,
            last_updated datetime DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY test_code (test_code),
            INDEX test_category (test_category)
        ) {$charset_collate};";

        // 3. TDL Test Requests Table
        $test_requests_sql = "CREATE TABLE IF NOT EXISTS {$test_requests_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            patient_id bigint(20) UNSIGNED NOT NULL,
            doctor_id bigint(20) UNSIGNED NOT NULL,
            clinic_id bigint(20) UNSIGNED NOT NULL,
            order_number varchar(20) NOT NULL,
            status ENUM('pending', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
            collection_date datetime DEFAULT NULL,
            clinical_notes text DEFAULT NULL,
            hl7_message longtext DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY order_number (order_number),
            INDEX patient_id (patient_id),
            INDEX doctor_id (doctor_id),
            INDEX clinic_id (clinic_id),
            INDEX status (status)
        ) {$charset_collate};";

        // 4. TDL Test Request Items Table
        $test_request_items_sql = "CREATE TABLE IF NOT EXISTS {$test_request_items_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            request_id bigint(20) UNSIGNED NOT NULL,
            test_code varchar(50) NOT NULL,
            test_name varchar(255) NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX request_id (request_id)
        ) {$charset_collate};";

        // 5. TDL Test Results Table
        $test_results_sql = "CREATE TABLE IF NOT EXISTS {$test_results_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            request_id bigint(20) UNSIGNED DEFAULT NULL,
            patient_id bigint(20) UNSIGNED NOT NULL,
            doctor_id bigint(20) UNSIGNED DEFAULT NULL,
            clinic_id bigint(20) UNSIGNED NOT NULL,
            order_number varchar(20) DEFAULT NULL,
            result_id varchar(50) NOT NULL,
            result_date datetime NOT NULL,
            result_status ENUM('received', 'in_review', 'completed', 'rejected') NOT NULL DEFAULT 'received',
            result_data longtext NOT NULL,
            physician_notes text DEFAULT NULL,
            reviewed_by bigint(20) UNSIGNED DEFAULT NULL,
            reviewed_at datetime DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY result_id (result_id),
            INDEX request_id (request_id),
            INDEX patient_id (patient_id),
            INDEX order_number (order_number),
            INDEX result_status (result_status)
        ) {$charset_collate};";

        // 6. TDL Test Result Items Table
        $test_result_items_sql = "CREATE TABLE IF NOT EXISTS {$test_result_items_table} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            result_id bigint(20) UNSIGNED NOT NULL,
            test_code varchar(50) NOT NULL,
            test_name varchar(255) NOT NULL,
            biomarker_name varchar(255) NOT NULL,
            value varchar(255) NOT NULL,
            units varchar(50) DEFAULT NULL,
            reference_range varchar(255) DEFAULT NULL,
            abnormal_flag varchar(10) DEFAULT NULL,
            observation_datetime datetime DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX result_id (result_id),
            INDEX test_code (test_code),
            INDEX biomarker_name (biomarker_name)
        ) {$charset_collate};";

        // Execute all CREATE TABLE queries
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($clinic_settings_sql);
        dbDelta($test_catalog_sql);
        dbDelta($test_requests_sql);
        dbDelta($test_request_items_sql);
        dbDelta($test_results_sql);
        dbDelta($test_result_items_sql);

        error_log("[Migration] TDL Labs tables created successfully");
        return true;
    }

    /**
     * Reverse the migration - drops all TDL Labs tables
     */
    public function down() {
        global $wpdb;

        error_log("[Migration] Dropping TDL Labs tables");

        // Drop tables in reverse order to handle dependencies
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_tdl_test_result_items");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_tdl_test_results");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_tdl_test_request_items");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_tdl_test_requests");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_tdl_test_catalog");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}kc_tdl_clinic_settings");

        error_log("[Migration] TDL Labs tables dropped successfully");
        return true;
    }
}