<template>
  <div class="w-full">
    <div class="bg-white rounded-lg shadow-lg">
      <!-- Header -->
      <div class="p-4 border-b">
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">
            {{ formTranslation.settings.permission_setting }}
            <a
              v-if="request_status == 'off'"
              href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#patient-setting"
              target="_blank"
              class="ml-2 text-gray-500 hover:text-gray-700"
            >
              <i class="fa fa-question-circle"></i>
            </a>
          </h2>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="formLoader" class="flex justify-center items-center p-8">
        <div
          class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"
        ></div>
      </div>

      <!-- Pro Version Overlay -->
      <div v-if="!userData.addOns.kiviPro" class="relative">
        <div
          class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center"
        >
          <div class="text-center p-4">
            <h3 class="text-lg font-semibold mb-2">Pro Feature</h3>
            <p class="text-gray-600">
              This feature is only available in the Pro version
            </p>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div v-if="userData.addOns.kiviPro" class="divide-y divide-gray-200">
        <!-- Role Permissions -->
        <div
          v-for="(index, key) in allPermissionListData"
          :key="key"
          class="border-b border-gray-200"
        >
          <!-- Role Header -->
          <div
            class="p-4 cursor-pointer hover:bg-gray-50"
            @click="toggleAccordion(key)"
          >
            <div class="flex items-center justify-between">
              <span class="font-medium">{{ role_list[key] + " role" }}</span>
              <svg
                :class="{ 'rotate-180': activeAccordions[key] }"
                class="w-5 h-5 transition-transform duration-200"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>

          <!-- Role Content -->
          <div v-show="activeAccordions[key]" class="pl-6 pr-4 pb-4">
            <!-- Modules -->
            <div
              v-for="(index2, key2) in index.capabilities"
              :key="key2"
              v-if="kivicareCheckModule(key, key2)"
              class="mt-4"
            >
              <!-- Module Header -->
              <div class="flex items-center mb-2">
                <h4 class="font-medium text-gray-900">
                  {{ formTranslation.common[key2] }}
                </h4>
              </div>

              <!-- Capabilities Grid -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div
                  v-for="(capVal, capKey) in index2"
                  :key="capKey"
                  v-if="kivicareCapability(key, capKey)"
                  class="flex items-center space-x-2"
                >
                  <label class="inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      :id="'checkbox-' + key + capKey"
                      v-model="index.capabilities[key2][capKey]"
                      class="form-checkbox h-5 w-5 text-blue-600 transition duration-150 ease-in-out"
                      true-value="true"
                      false-value="false"
                    />
                    <span class="ml-2 text-sm text-gray-700">
                      {{ formatCapabilityName(capKey) }}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4 mt-6">
              <button
                @click="handleSubmit(key)"
                :id="key"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {{ formTranslation.doctor.save_btn }}
              </button>
              <button
                @click="toggleAccordion(key)"
                :disabled="isSubmited"
                class="px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {{ formTranslation.common.cancel }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Non-Pro Version Content -->
      <div v-else class="p-4">
        <div class="space-y-2">
          <div
            v-for="(index, key) in role_list"
            :key="key"
            class="p-4 bg-gray-50 rounded-md"
          >
            {{ role_list[key] + " role" }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
  name: "PermissionSetting",

  data() {
    return {
      allPermissionListData: {},
      formLoader: true,
      request_status: "off",
      check_value: "on",
      plugin_prefix: window.pluginPREFIX,
      isSubmited: false,
      role_list: {},
      activeAccordions: {},
    };
  },

  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }

    this.role_list = {
      administrator: "Administrator",
      kiviCare_clinic_admin: this.formTranslation.common.clinic_admin,
      kiviCare_receptionist: this.formTranslation.common.receptionist,
      kiviCare_doctor: this.formTranslation.doctor_session.dt_lbl_doc,
      kiviCare_patient: this.formTranslation.common.patient,
    };

    this.allPermissionList();
    this.getModule();
  },

  methods: {
    toggleAccordion(key) {
      this.$set(this.activeAccordions, key, !this.activeAccordions[key]);
    },

    formatCapabilityName(capKey) {
      return capKey
        .split("_")
        .map((v) => {
          if (
            v
              .toLowerCase()
              .includes(this.plugin_prefix.replace("_", "").toLowerCase())
          ) {
            return "";
          }
          v = v.split("");
          if (v[0]) {
            v[0] = v[0].toUpperCase();
          }
          return v.join("");
        })
        .filter((v) => v)
        .join(" ");
    },

    allPermissionList() {
      this.formLoader = true;
      get("all_permission_list", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.allPermissionListData = response.data.data;
          }
          this.formLoader = false;
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
          this.displayErrorMessage(
            this.formTranslation.widgets.record_not_found
          );
        });
    },

    handleSubmit(key) {
      const button = document.getElementById(key);
      button.textContent = this.formTranslation.common.loading;
      button.disabled = true;

      post("save_permission_list", {
        data: this.allPermissionListData,
        type: key,
      })
        .then((response) => {
          button.textContent = this.formTranslation.common.save;
          button.disabled = false;

          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.displayMessage(response.data.message);
            this.$store.dispatch("userDataModule/fetchUserData", {});
          } else {
            this.displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          button.textContent = this.formTranslation.common.save;
          button.disabled = false;
          console.log(error);
          this.displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    kivicareCapability(role, capability) {
      // Existing capability check logic remains the same
      capability = capability.toLowerCase();
      role = role.toLowerCase();
      let prefix = this.plugin_prefix.toLowerCase();
      let status = capability.includes(prefix);

      if (role === "administrator") {
        return (
          status &&
          ![
            prefix + "settings",
            prefix + "dashboard",
            prefix + "common_settings",
          ].includes(capability)
        );
      } else if (role === prefix + "clinic_admin") {
        return (
          status &&
          ![prefix + "settings", prefix + "common_settings"].includes(
            capability
          )
        );
      } else if (role === prefix + "doctor") {
        return (
          status &&
          ![prefix + "settings", prefix + "patient_encounters"].includes(
            capability
          )
        );
      } else if (role === prefix + "receptionist") {
        return (
          status &&
          ![prefix + "settings", prefix + "doctor_dashboard"].includes(
            capability
          )
        );
      }
      if (role === prefix + "patient") {
        return (
          status &&
          ![prefix + "service_list", prefix + "patient_encounters"].includes(
            capability
          )
        );
      }
    },

    kivicareCheckModule(role, module) {
      // Existing module check logic remains the same
      let status = true;
      switch (role) {
        case "administrator":
          break;
        case "kiviCare_clinic_admin":
          status = !["clinic_module"].includes(module);
          break;
        case "kiviCare_doctor":
          status = !["doctor_module", "custom_field_module"].includes(module);
          break;
        case "kiviCare_patient":
          status = !["doctor_module", "service_module"].includes(module);
          break;
        case "kiviCare_receptionist":
          status = !["receptionist_module", "clinic_module"].includes(module);
          break;
      }
      return status;
    },

    getModule() {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },

  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    formTranslation() {
      return this.$store.state.staticDataModule.langTranslateData;
    },
  },
};
</script>
