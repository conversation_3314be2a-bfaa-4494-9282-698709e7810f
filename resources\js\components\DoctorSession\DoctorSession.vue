<!-- DoctorSession.vue -->
<template>
  <div class="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-4">
    <!-- Timezone Alert -->
    <div v-if="getUserRole() == 'administrator' && !timezone_status" class="mb-6">
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex justify-between items-center">
          <p class="font-medium text-yellow-700">{{ timezone_msg }}</p>
          <button @click="iUnderstandTimezone"
            class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200">
            Got it!
          </button>
        </div>
      </div>
    </div>

    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button @click="$router.back()"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="m12 19-7-7 7-7"></path>
            <path d="M19 12H5"></path>
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.doctor_session.doc_sessions }}
        </h1>
      </div>
      <div class="flex gap-3">
        <module-data-export v-if="kcCheckPermission('doctor_session_export')" :module-data="clinicData.clinic_sessions"
          :module-name="formTranslation.doctor_session.doc_sessions" module-type="session">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          {{ formTranslation.common.import }}
        </module-data-export>
        <button v-if="kcCheckPermission('doctor_session_add')" @click="showAddAvailabilityModal"
          class="px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800 flex items-center gap-2">
          <i class="fas fa-plus"></i>
          {{ formTranslation.doctor_session.add_session_btn }}
        </button>
      </div>
    </div>

    <!-- Sessions List Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- Search Bar -->
      <div class="p-4 border-b border-gray-200">
        <div class="relative">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input type="text" v-model="searchQuery"
            class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent"
            :placeholder="formTranslation.datatable.search_placeholder" />
        </div>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th v-for="col in column" :key="col.field"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ col.label }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="(session, index) in filteredSessions" :key="index"
              class="hover:bg-gray-50"
              :class="{
                'bg-green-50 hover:bg-green-100': session.service_id,
                'bg-blue-50 hover:bg-blue-100': !session.service_id,
                'border-t-2 border-t-green-200': session.service_id && (index === 0 || filteredSessions[index-1]?.service_id !== session.service_id)
              }"
            >
              <td v-for="col in column" :key="col.field" class="px-6 py-4 whitespace-nowrap">
                <template v-if="col.field === 'action'">
                  <div class="flex gap-2">
                    <button v-if="kcCheckPermission('doctor_session_edit')"
                      @click="editSessionData({ row: session, index })" class="p-1 hover:bg-gray-100 rounded">
                      <i class="fa fa-pen-alt text-gray-600"></i>
                    </button>
                    <button v-if="kcCheckPermission('doctor_session_delete')"
                      @click="deleteSessionData({ row: session, index })" class="p-1 hover:bg-gray-100 rounded">
                      <i class="fa fa-trash text-red-500"></i>
                    </button>
                  </div>
                </template>
                <template v-else-if="col.field === 'index'">
                  {{ (currentPage - 1) * perPage + index + 1 }}
                </template>
                <template v-else-if="col.field === 'session_type'">
                  <span
                    :class="{
                      'px-2 py-1 rounded-full text-xs font-medium': true,
                      'bg-blue-100 text-blue-800': !session.service_id,
                      'bg-green-100 text-green-800': session.service_id
                    }"
                  >
                    {{ !session.service_id ? 'Doctor' :
                       (session.service_name ? session.service_name : 'Service #' + session.service_id) }}
                  </span>
                </template>
                <template v-else-if="col.field === 'days'">
                  {{ tableDaysTranslation(session.days) }}
                </template>
                <template v-else>
                  {{ session[col.field] }}
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select v-model="perPage" class="border border-gray-300 rounded-md text-sm p-1">
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <div class="flex gap-2">
            <button @click="previousPage" :disabled="currentPage === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Session Modal -->
    <SessionModal :showAddEditSessionModal="showAvailabilityModal" :is-edit="isEdit" :initial-data="modalData"
      :all-clinics="allClinics" :doctors="doctors" :get-user-role="getUserRole" :user-data="userData"
      @update:showAddEditSessionModal="showAvailabilityModal = $event" @close="handleShowAvailabilityModalClose"
      @clinic-change="handleClinicChange" @doctor-change="handleDoctorChange" @submit="handleSessionSubmit" />
  </div>
</template>

<script>
import SessionModal from "./SessionModal.vue";
import { post, get } from "../../config/request";

export default {
  name: "DoctorSession",
  components: {
    SessionModal,
  },
  data: () => ({
    showAvailabilityModal: false,
    isEdit: false,
    modalData: {},
    pageLoader: true,
    clinicData: { clinic_sessions: [] },
    doctors: [],
    timezone_status: true,
    timezone_msg: "",
    doctorMultiselectLoader: true,
    clinicMultiselectLoader: true,
    searchQuery: "",
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    column: [],
    dropDownWeekDays: [],
    activeClinicId: 0,
    days: ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]
  }),

  computed: {
    filteredSessions() {
      let filtered = this.clinicData.clinic_sessions || [];

      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter((session) => {
          // Add service_name to the searchable fields
          const serviceName = session.service_name || '';
          const sessionType = session.service_id ? 'service' : 'doctor';

          return (
            session.doctor_name.toLowerCase().includes(query) ||
            session.clinic_name.toLowerCase().includes(query) ||
            serviceName.toLowerCase().includes(query) ||
            sessionType.includes(query) ||
            this.tableDaysTranslation(session.days)
              .toLowerCase()
              .includes(query)
          );
        });
      }

      this.totalPages = Math.ceil(filtered.length / this.perPage);

      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return filtered.slice(start, end);
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },

    allClinics() {
      this.clinicMultiselectLoader = false;
      return this.$store.state.clinic || [];
    },
  },

  watch: {
    perPage() {
      this.currentPage = 1;
    },
    searchQuery() {
      this.currentPage = 1;
    },
  },

  mounted() {
    if (["patient"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }

    this.setupInitialData();
    this.init();

    if (this.$route.params.id !== undefined) {
      this.handleRouteWithDoctorId();
    } else {
      this.getUserClinic(1);
    }
  },

  methods: {
    init() {
      this.getClinicSessionsList();
      if (this.userData.addOns.kiviPro !== true) {
        this.activeClinicId = this.userData.default_clinic_id;
      }
      this.getDoctorsData();
    },

    showAddAvailabilityModal() {
      this.isEdit = false;
      this.modalData = this.defaultClinicSessionData(); // Use the default data structure
      this.showAvailabilityModal = true;
    },

    async editSessionData(data) {
      this.isEdit = true;

      await this.handleClinicChange(data.row.clinic_id?.id);

      const selectedDoctor = this.doctors.find(
        (d) => d.id === data.row.doctor_id
      );

      this.modalData = {
        id: data.row.id,
        clinic_id: data.row.clinic_id?.id.toString(),
        doctors: selectedDoctor || {},
        buffertime: data.row.buffertime || 0,
        // Include service information if this is a service-specific session
        service_id: data.row.service_id || null,
        service_name: data.row.service_name || null,
        days: data.row.days.map((day) => ({
          name: day.name,
          label: this.formTranslation.days[day.name],
          slots: day.slots.map((slot) => ({
            start: slot.start,
            end: slot.end,
          })),
          error: null,
        })),
      };

      this.showAvailabilityModal = true;
    },

    defaultClinicSessionData() {
      return {
        doctors: {},
        clinic_id: "",
        buffertime: 0,
        days: this.days.map((day) => ({
          name: day,
          label: this.formTranslation.days[day],
          slots: [],
          error: null,
        })),
      };
    },

    async handleClinicChange(clinicId) {
      try {
        const response = await get("get_static_data", {
          data_type: "get_users_by_clinic",
          clinic_id: clinicId,
        });

        if (response.data.status) {
          this.doctors = response.data.data;
        }
      } catch (error) {
        console.error(error);
        this.$swal.fire({
          title: "Error!",
          text:
            error.message || this.formTranslation.common.internal_server_error,
          icon: "error",
        });
      }
    },

    async handleDoctorChange(doctorId) {
      if (doctorId) {
        await this.getUserClinic(doctorId);
      }
    },

    async handleSessionSubmit(formData) {
      try {
        // Ensure we're sending the ID if this is an edit operation
        if (this.isEdit && this.modalData.id) {
          formData.id = this.modalData.id;
        }

        const response = await post("clinic_session_save", formData);

        if (response.data.status) {
          this.handleShowAvailabilityModalClose();

          await this.getClinicSessionsList();

          this.$swal.fire({
            title: "Success!",
            text: response.data.message,
            icon: "success",
          });

          return true;
        } else {
          throw new Error(response.data.message);
        }
      } catch (error) {
        console.error(error);
        this.$swal.fire({
          title: "Error!",
          text:
            error.message || this.formTranslation.common.internal_server_error,
          icon: "error",
        });
        throw error;
      }
    },

    setupInitialData() {
      this.days.forEach((item) => {
        this.dropDownWeekDays.push({
          value: item,
          text: this.formTranslation.days[item],
        });
      });

      this.getTimezoneSetting();
      this.setupColumns();
    },

    getTimezoneSetting() {
      this.timezone_status = window.request_data.time_zone_data.data;
      this.timezone_msg = window.request_data.time_zone_data.message;
    },

    setupColumns() {
      this.column = [
        {
          field: "index",
          label: this.formTranslation.doctor_session.dt_lbl_sr,
          width: "50px",
        },
        {
          label: this.formTranslation.doctor_session.dt_lbl_doc,
          field: "doctor_name",
          width: "150px",
        },
        {
          field: "clinic_name",
          label: this.formTranslation.doctor_session.dt_lbl_clinic,
          width: "150px",
        },
        {
          label: "Session Type",
          field: "session_type",
          width: "130px",
          sortable: false,
        },
        {
          label: this.formTranslation.doctor_session.dt_lbl_days,
          field: "days",
          width: "200px",
          sortable: false,
        },
        {
          label: this.formTranslation.doctor_session.dt_lblaction,
          field: "action",
          width: "50px",
          sortable: false,
          html: true,
        },
      ];
    },

    // Existing methods remain the same...
    tableDaysTranslation(days) {
      return days
        .filter((day) => day.slots.length)
        .map((day) => this.formTranslation.days[day.name])
        .join(", ");
    },

    previousPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    },

    handleShowAvailabilityModalClose() {
      this.showAvailabilityModal = false;
      this.modalData = {};
      this.isEdit = false;
    },

    // API methods...
    async getUserClinic(doctor_id) {
      try {
        const response = await get("clinic_doctor_wise_list", {
          data_type: "doctor",
          doctor_id: doctor_id,
        });

        if (response.data.status) {
          this.clinics = response.data.data;
        }
      } catch (error) {
        console.error(error);
        this.$swal.fire({
          title: "Error!",
          text: this.formTranslation.common.internal_server_error,
          icon: "error",
        });
      }
    },

    async getDoctorsData() {
      this.doctorMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          clinic_id: this.activeClinicId,
        });

        if (response.data.status) {
          this.doctors = response.data.data;
        }
      } catch (error) {
        console.error(error);
        this.$swal.fire({
          title: "Error!",
          text: this.formTranslation.common.internal_server_error,
          icon: "error",
        });
      } finally {
        this.doctorMultiselectLoader = false;
      }
    },

    async getClinicSessionsList() {
      this.pageLoader = true;
      try {
        // First, prefetch all services to ensure we have their names
        let serviceMap = {};
        try {
          const serviceResponse = await get("get_service_list", {});
          if (serviceResponse.data.status && Array.isArray(serviceResponse.data.data)) {
            // Create a map of service_id to service_name
            serviceResponse.data.data.forEach(service => {
              serviceMap[service.id] = service.name;
            });
            console.log('Service map loaded with', Object.keys(serviceMap).length, 'services');
          }
        } catch (serviceError) {
          console.error('Error prefetching service names:', serviceError);
        }

        // Now fetch the clinic sessions
        const response = await get("clinic_session_list", {});
        if (response.data.status) {
          // Process session data to add service names
          let clinicSessions = response.data.data.clinic_sessions || [];
          // Add service_name to all sessions with service_id
          clinicSessions = clinicSessions.map(session => {
            if (session.service_id) {
              return {
                ...session,
                service_name: serviceMap[session.service_id] || `Service #${session.service_id}`
              };
            }
            return session;
          });

          // Sort sessions to group by service_id
          clinicSessions.sort((a, b) => {
            // First sort by service_id presence (non-service sessions first)
            if (!a.service_id && b.service_id) return -1;
            if (a.service_id && !b.service_id) return 1;

            // Then by service_id or other criteria
            if (a.service_id && b.service_id) {
              return a.service_id - b.service_id;
            }

            // If both are regular sessions or same service, sort by doctor name
            return a.doctor_name.localeCompare(b.doctor_name);
          });
          this.clinicData = {
            ...response.data.data,
            clinic_sessions: clinicSessions
          };
        } else {
          this.clinicData = { clinic_sessions: [] };
        }
      } catch (error) {
        console.error(error);
        this.$swal.fire({
          title: "Error!",
          text: this.formTranslation.common.internal_server_error,
          icon: "error",
        });
      } finally {
        this.pageLoader = false;
      }
    },

    deleteSessionData(data) {
      if (!this.clinicData.clinic_sessions[data.index]) return;

      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: this.formTranslation.common.reset_appointment_slot,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#dc3545",
          cancelButtonColor: "#6c757d",
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            try {
              const response = await post("clinic_session_delete", {
                session_id: data.row.id,
              });

              this.getClinicSessionsList();

              if (response.data.status) {
                this.clinicData.clinic_sessions.splice(data.index, 1);
                this.$swal.fire({
                  title: "Success!",
                  text: response.data.message,
                  icon: "success",
                });
              } else {
                throw new Error(response.data.message);
              }
            } catch (error) {
              console.error(error);
              this.$swal.fire({
                title: "Error!",
                text:
                  error.message ||
                  this.formTranslation.common.internal_server_error,
                icon: "error",
              });
            }
          }
        });
    },

    handleRouteWithDoctorId() {
      const doctor_id = this.$route.params.id;
      if (doctor_id) {
        this.getDoctorsData();
        setTimeout(() => {
          const doctor = this.doctors.find((d) => d.id == doctor_id);
          if (doctor) {
            this.modalData = {
              doctors: doctor,
              buffertime: 0,
              days: this.days.map((day) => ({
                name: day,
                label: this.formTranslation.days[day],
                slots: [],
                error: null,
              })),
            };
            this.showAvailabilityModal = true;
            this.getUserClinic(doctor_id);
          }
        }, 500);
      }
    },
  },
};
</script>