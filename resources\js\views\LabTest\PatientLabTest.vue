<template>
  <div>
    <div class="p-6 max-w-7xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Lab Tests</h1>
        <button
          class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-plus w-4 h-4 mr-2"
          >
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path></svg
          >Schedule New Test
        </button>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div
          class="bg-white text-card-foreground rounded-xl border shadow hover:shadow-md transition-shadow cursor-pointer"
        >
          <div class="p-4">
            <h3 class="font-semibold mb-2">Complete Blood Count</h3>
            <div class="flex items-center text-sm text-gray-600 mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-clock w-4 h-4 mr-1"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline></svg
              ><span>15 mins</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-purple-600 font-semibold">$35</span
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-arrow-right w-4 h-4 text-gray-400"
              >
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>
        <div
          class="bg-white text-card-foreground rounded-xl border shadow hover:shadow-md transition-shadow cursor-pointer"
        >
          <div class="p-4">
            <h3 class="font-semibold mb-2">Lipid Panel</h3>
            <div class="flex items-center text-sm text-gray-600 mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-clock w-4 h-4 mr-1"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline></svg
              ><span>20 mins</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-purple-600 font-semibold">$45</span
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-arrow-right w-4 h-4 text-gray-400"
              >
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>
        <div
          class="bg-white text-card-foreground rounded-xl border shadow hover:shadow-md transition-shadow cursor-pointer"
        >
          <div class="p-4">
            <h3 class="font-semibold mb-2">Diabetes Screening</h3>
            <div class="flex items-center text-sm text-gray-600 mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-clock w-4 h-4 mr-1"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline></svg
              ><span>30 mins</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-purple-600 font-semibold">$55</span
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-arrow-right w-4 h-4 text-gray-400"
              >
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>
        <div
          class="bg-white text-card-foreground rounded-xl border shadow hover:shadow-md transition-shadow cursor-pointer"
        >
          <div class="p-4">
            <h3 class="font-semibold mb-2">Thyroid Function</h3>
            <div class="flex items-center text-sm text-gray-600 mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-clock w-4 h-4 mr-1"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline></svg
              ><span>25 mins</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-purple-600 font-semibold">$65</span
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-arrow-right w-4 h-4 text-gray-400"
              >
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        role="alert"
        class="relative w-full rounded-lg border px-4 py-3 text-sm [&amp;>svg+div]:translate-y-[-3px] [&amp;>svg]:absolute [&amp;>svg]:left-4 [&amp;>svg]:top-4 [&amp;>svg]:text-foreground [&amp;>svg~*]:pl-7 bg-background text-foreground mb-6 bg-purple-50 border-purple-100"
      >
        <div class="flex items-start space-x-4">
          <div
            class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-message-square w-4 h-4 text-purple-600"
            >
              <path
                d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
              ></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-purple-900">AI Health Assistant</h3>
            <div class="text-sm [&amp;_p]:leading-relaxed mt-1 text-purple-800">
              Based on your age and family history, it's recommended to schedule
              a lipid panel test. Would you like to schedule it now?
            </div>
            <div class="mt-3 flex space-x-4">
              <button
                class="text-sm px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Schedule Test</button
              ><button
                class="text-sm px-3 py-1 text-purple-600 hover:bg-purple-100 rounded-md"
              >
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="flex space-x-2 mb-6">
        <button class="px-4 py-2 rounded-lg text-sm bg-purple-600 text-white">
          Upcoming</button
        ><button
          class="px-4 py-2 rounded-lg text-sm bg-gray-100 text-gray-600 hover:bg-gray-200"
        >
          Completed</button
        ><button
          class="px-4 py-2 rounded-lg text-sm bg-gray-100 text-gray-600 hover:bg-gray-200"
        >
          All
        </button>
      </div>
      <div class="space-y-4">
        <div
          class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden"
        >
          <div class="p-4">
            <div
              class="flex flex-col md:flex-row md:items-center md:justify-between"
            >
              <div>
                <h3 class="font-semibold text-lg mb-1">
                  Comprehensive Metabolic Panel
                </h3>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-calendar w-4 h-4 mr-1"
                    >
                      <path d="M8 2v4"></path>
                      <path d="M16 2v4"></path>
                      <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                      <path d="M3 10h18"></path></svg
                    ><span>Tomorrow, 9:00 AM</span>
                  </div>
                  <span>•</span><span>Main Lab Center</span><span>•</span
                  ><span class="text-blue-600">Scheduled</span>
                </div>
              </div>
              <div class="mt-4 md:mt-0 flex items-center space-x-3">
                <button
                  class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  Check In
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden"
        >
          <div class="p-4">
            <div
              class="flex flex-col md:flex-row md:items-center md:justify-between"
            >
              <div>
                <h3 class="font-semibold text-lg mb-1">
                  Complete Blood Count (CBC)
                </h3>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-calendar w-4 h-4 mr-1"
                    >
                      <path d="M8 2v4"></path>
                      <path d="M16 2v4"></path>
                      <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                      <path d="M3 10h18"></path></svg
                    ><span>January 10, 2025</span>
                  </div>
                  <span>•</span><span>Main Lab Center</span><span>•</span
                  ><span class="text-green-600">Completed</span>
                </div>
              </div>
              <div class="mt-4 md:mt-0 flex items-center space-x-3">
                <button
                  class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  View Results
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get, post } from "../../config/request";

export default {
  components: {
  },
  data: () => {
    return {
      isLoading: false,
      dashboardData: {},
      isAppointmentReload: false,
      appointmentRequest: {},
      reloadCalender: true,

      loading: false,
      vitals: {},
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init: function () {
      console.log(this.getUserRole());
      this.getDashboardData();
      this.dashboardData = this.defaultDashboardData();
      this.$nextTick(() => {
        // Add the component back in
        this.reloadCalender = true;
      });
    },
    defaultDashboardData: function () {
      return {
        appointment_count: 0,
        doctor_count: 0,
        patient_count: 0,
        revenue: 0,
        change_log: true,
        telemed_log: false,
      };
    },
  },
};
</script>
