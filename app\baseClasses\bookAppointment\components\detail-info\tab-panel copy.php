<div class="max-w-2xl mx-auto p-6">
    <div class="flex items-center justify-between mb-6">
        <div class="iq-kivi-tab-panel-title-animation">
            <h3 class="text-xl font-semibold text-gray-800"><?php echo esc_html__('Enter Details', 'kc-lang'); ?></h3>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="flex space-x-4 mb-6">
        <a href="#kc_register" 
           id="register-tab"
           data-iq-toggle="tab" 
           class="flex-1 py-2 px-4 rounded-lg font-medium transition-colors bg-purple-600 text-white text-center active">
            <?php echo esc_html__('Register', 'kc-lang'); ?>
        </a>
        <a href="#kc_login" 
           id="login-tab"
           data-iq-toggle="tab" 
           class="flex-1 py-2 px-4 rounded-lg font-medium transition-colors bg-gray-100 text-gray-600 hover:bg-gray-200 text-center">
            <?php echo esc_html__('Login', 'kc-lang'); ?>
        </a>
    </div>

    <div class="widget-content">
        <div id="login-register-panel" class="card-list-data">
            <!-- Register Form -->
            <div id="kc_register" class="iq-tab-pannel kivicare-register-form-data iq-fade active authActive">
                <div id="kivicare-register-form">
                    <div class="space-y-6" id="kivicare-register">
                        <?php if(kcGoogleCaptchaData('status') === 'on'){ ?>
                            <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response">
                            <input type="hidden" name="captcha_action" value="validate_captcha">
                        <?php } ?>
                        <input type="hidden" name="widgettype" value="new_appointment_widget" id="widgettype">
                        <input type="hidden" id="registerClinicId">

                        <!-- Name Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1" for="firstName">
                                    <?php echo esc_html__('First Name', 'kc-lang'); ?><span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       name="first_name" 
                                       id="firstName"
                                       class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                       placeholder="<?php echo esc_html__('Enter your first name', 'kc-lang'); ?>" 
                                       required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1" for="lastName">
                                    <?php echo esc_html__('Last Name', 'kc-lang'); ?><span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       name="last_name" 
                                       id="lastName"
                                       class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                       placeholder="<?php echo esc_html__('Enter your last name', 'kc-lang'); ?>" 
                                       required>
                            </div>
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" for="userEmail">
                                <?php echo esc_html__('Email', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <input type="email" 
                                   name="user_email" 
                                   id="userEmail"
                                   class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                   placeholder="<?php echo esc_html__('Enter your email', 'kc-lang'); ?>" 
                                   required>
                        </div>

                        <!-- Contact Field -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" for="userContact">
                                <?php echo esc_html__('Contact', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <div class="flex space-x-2">
                                <?php if (file_exists(KIVI_CARE_DIR . 'assets/helper_assets/CountryCodes.json')) {
                                    $json_country_code = file_get_contents(KIVI_CARE_DIR . 'assets/helper_assets/CountryCodes.json');
                                    $country_code = json_decode($json_country_code, true);
                                ?>
                                    <select name="country_code" 
                                            id="CountryCode"
                                            class="w-32 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                        <?php foreach ($country_code as $id => $code) {
                                            $valueString = '{"countryCallingCode":"' . $code['dial_code'] . '","countryCode":"' . $code['code'] . '"}';
                                        ?>
                                            <option value="<?php echo esc_html__($valueString, 'kc-lang'); ?>" 
                                                    <?php echo esc_html__(($code['code'] == 'US') ? "selected" : "", 'kc-lang') ?>>
                                                <?php echo esc_html__($code['dial_code'] . " - " . $code['name'], 'kc-lang'); ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                <?php } else { ?>
                                    <input type="text" 
                                           name="country_code" 
                                           id="txt_CountryCode"
                                           class="w-32 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" 
                                           placeholder="<?php echo esc_html__('Country code', 'kc-lang'); ?>" 
                                           required>
                                <?php } ?>
                                <input type="tel" 
                                       name="mobile_number" 
                                       id="userContact"
                                       class="flex-1 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" 
                                       placeholder="<?php echo esc_html__('Enter your contact number', 'kc-lang'); ?>" 
                                       required>
                            </div>
                        </div>

                        <!-- Address Field -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" for="address">
                                <?php echo esc_html__('Address', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <textarea name="address" 
                                      id="address" 
                                      rows="3"
                                      class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                      placeholder="<?php echo esc_html__('Enter your address', 'kc-lang'); ?>" 
                                      required></textarea>
                        </div>

                        <!-- Date of Birth Field -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" for="dob">
                                <?php echo esc_html__('Date of Birth', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <input type="date" 
                                   name="dob" 
                                   id="dob"
                                   class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                   required>
                        </div>

                        <!-- NHS Number Field -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" for="nhs">
                                <?php echo esc_html__('NHS No.', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   name="nhs" 
                                   id="nhs"
                                   class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                   placeholder="<?php echo esc_html__('Enter NHS No.', 'kc-lang'); ?>" 
                                   required>
                        </div>

                        <!-- Gender Field -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo esc_html__('Gender', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <div class="flex space-x-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" 
                                           name="gender" 
                                           value="male"
                                           class="form-radio text-purple-600 focus:ring-purple-500" 
                                           required>
                                    <span class="ml-2"><?php echo esc_html__('Male', 'kc-lang'); ?></span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" 
                                           name="gender" 
                                           value="female"
                                           class="form-radio text-purple-600 focus:ring-purple-500">
                                    <span class="ml-2"><?php echo esc_html__('Female', 'kc-lang'); ?></span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" 
                                           name="gender" 
                                           value="other"
                                           class="form-radio text-purple-600 focus:ring-purple-500">
                                    <span class="ml-2"><?php echo esc_html__('Other', 'kc-lang'); ?></span>
                                </label>
                            </div>
                        </div>

                        <?php do_action('kivicare_widget_register_form_field_add'); ?>
                    </div>
                    <div id="customFieldsList">
                        <?php kcGetCustomFieldsList('patient_module',0); ?>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <div id="kc_login" class="iq-tab-pannel kivicare-login-form-data iq-fade authActive hidden">
                <div id="kivicare-login-form">
                    <div class="space-y-4" id="kivicare-login">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" for="loginUsername">
                                <?php echo esc_html__('Username or Email', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   name="username" 
                                   id="loginUsername"
                                   class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                   placeholder="<?php echo esc_html__('Enter your username or email', 'kc-lang'); ?>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1" for="loginPassword">
                                <?php echo esc_html__('Password', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password" 
                                       name="password" 
                                       id="loginPassword"
                                       class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black 
                                       placeholder="***********">
                                <button type="button" 
                                        id="togglePassword" 
                                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-right">
                            <a href="<?php echo esc_url(wp_lostpassword_url()); ?>" 
                               target="_blank"
                               class="text-sm text-purple-600 hover:text-purple-700">
                                <?php echo esc_html__('Forgot Password ?', 'kc-lang'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add this JavaScript at the bottom of your page or in your scripts file -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabs = document.querySelectorAll('[data-iq-toggle="tab"]');
    const panels = document.querySelectorAll('.iq-tab-pannel');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Remove active classes
            tabs.forEach(t => {
                t.classList.remove('bg-purple-600', 'text-white');
                t.classList.add('bg-gray-100', 'text-gray-600');
            });
            panels.forEach(p => {
                p.classList.add('hidden');
            });
            
            // Add active classes
            tab.classList.remove('bg-gray-100', 'text-gray-600');
            tab.classList.add('bg-purple-600', 'text-white');
            
            const targetId = tab.getAttribute('href');
            document.querySelector(targetId).classList.remove('hidden');
        });
    });

    // Show default tab on page load
    const defaultTab = document.querySelector('[data-iq-toggle="tab"].active');
    if (defaultTab) {
        const defaultTargetId = defaultTab.getAttribute('href');
        document.querySelector(defaultTargetId).classList.remove('hidden');
    }

    // Password visibility toggle
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('loginPassword');

    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle eye icon
            togglePassword.querySelector('i').classList.toggle('fa-eye');
            togglePassword.querySelector('i').classList.toggle('fa-eye-slash');
        });
    }

    // Form validation
    const registerForm = document.getElementById('kivicare-register-form');
    const loginForm = document.getElementById('kivicare-login-form');

    function validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
                // Add error message
                let errorMsg = field.nextElementSibling;
                if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                    errorMsg = document.createElement('span');
                    errorMsg.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
                    field.parentNode.insertBefore(errorMsg, field.nextSibling);
                }
                errorMsg.textContent = 'This field is required';
            }
        });

        return isValid;
    }

    // Add input listeners for validation
    const inputs = document.querySelectorAll('input[required], textarea[required], select[required]');
    inputs.forEach(input => {
        input.addEventListener('input', () => {
            input.classList.remove('border-red-500');
            const errorMsg = input.nextElementSibling;
            if (errorMsg && errorMsg.classList.contains('error-message')) {
                errorMsg.remove();
            }
        });
    });

    // Handle form submissions
    if (registerForm) {
        registerForm.addEventListener('submit', (e) => {
            e.preventDefault();
            if (validateForm(registerForm)) {
                // Add your register form submission logic here
                console.log('Register form is valid, submitting...');
            }
        });
    }

    if (loginForm) {
        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            if (validateForm(loginForm)) {
                // Add your login form submission logic here
                console.log('Login form is valid, submitting...');
            }
        });
    }

    // Handle country code selection if present
    const countryCodeSelect = document.getElementById('CountryCode');
    if (countryCodeSelect) {
        countryCodeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            try {
                const countryData = JSON.parse(selectedOption.value);
                console.log('Selected country code:', countryData.countryCallingCode);
            } catch (error) {
                console.error('Error parsing country code data:', error);
            }
        });
    }
});
</script>