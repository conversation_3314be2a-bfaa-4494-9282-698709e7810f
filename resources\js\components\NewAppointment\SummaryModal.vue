<!-- SummaryModal.vue -->
<template>
  <ModalPopup
    v-if="show"
    modalId="summary-modal"
    modalSize="lg"
    :openModal="show"
    :modalTitle="'Generated Summary'"
    @closeModal="$emit('close')"
  >
    <div class="summary-modal mt-6">
      <!-- Header -->
      <div class="flex justify-between items-center mb-3">
        <!-- <button
            class="flex items-center gap-1.5 px-3 py-1.5 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            @click="downloadPDF"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-4 h-4"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" x2="12" y1="15" y2="3"></line>
            </svg>
            Download PDF
          </button> -->
      </div>

      <!-- Editor -->
      <vue-editor
        :editor-toolbar="customToolbar"
        class="custom-editor"
        :key="index"
        v-model="editedSummary"
      ></vue-editor>

      <!-- Footer -->
      <div class="flex justify-end gap-2 mt-3">
        <button
          type="button"
          class="px-4 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200"
          @click="$emit('close')"
          :disabled="isLoading"
        >
          {{ $t("Cancel") }}
        </button>
        <button
          type="button"
          class="px-4 py-2 text-sm bg-black text-white rounded hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black disabled:opacity-50"
          @click="$emit('next')"
          :disabled="isLoading"
        >
          <template v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ $t("Saving...") }}
          </template>
          <template v-else>
            {{ $t("Save Document") }}
          </template>
        </button>
      </div>
    </div>
  </ModalPopup>
</template>

<script>
import ModalPopup from "../Modal/Index.vue";

export default {
  name: "SummaryModal",

  components: {
    ModalPopup,
  },

  props: {
    show: {
      type: Boolean,
      required: true,
    },
    summary: {
      type: String,
      required: true,
    },
    template: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      editedSummary: this.summary,
      isLoading: false,
      index: "summary-editor",
      customToolbar: [
        [
          {
            header: [false, 1, 2, 3, 4, 5, 6],
          },
        ],
        ["bold", "italic", "underline", "strike"],
        [
          {
            align: "",
          },
          {
            align: "center",
          },
          {
            align: "right",
          },
          {
            align: "justify",
          },
        ],
        ["blockquote", "code-block"],
        [
          {
            list: "ordered",
          },
          {
            list: "bullet",
          },
          {
            list: "check",
          },
        ],
        [
          {
            indent: "-1",
          },
          {
            indent: "+1",
          },
        ],
        [
          {
            color: [],
          },
          {
            background: [],
          },
        ],
      ],
    };
  },

  watch: {
    summary: {
      immediate: true,
      handler(newVal) {
        this.editedSummary = this.convertAsterisksToBold(newVal);
      },
    },
  },

  methods: {
    downloadPDF() {
      console.log(this.summary, "ritesh");
      const plainText = this.editedSummary.replace(/<\/?strong>/g, "**");
      this.$emit("download", plainText);
    },
    convertAsterisksToBold(text) {
      if (!text) return "";
      // Replace **text** with <strong>text</strong>
      return text.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
    },
  },
};
</script>

<style>
.summary-modal {
  padding: 1rem;
}

.editor-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

#quill-container {
  height: 300px !important;
  overflow-y: auto;
}

/* Target the editable content area */
:deep(.ql-container) {
  height: calc(100% - 42px) !important; /* 42px is toolbar height */
  min-height: 300px;
}

:deep(.ql-editor) {
  min-height: 400px;
  max-height: 100%;
  overflow-y: auto;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-size: 14px;
  line-height: 1.5;
}

/* Additional styling for the Vue Editor */
.custom-editor {
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.ql-toolbar.ql-snow {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  background-color: #f8fafc;
  border-color: #e2e8f0;
  padding: 8px;
}

.ql-container.ql-snow {
  border-bottom-left-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-color: #e2e8f0;
}

/* Improve readability of the content */
:deep(.ql-editor h1) {
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

:deep(.ql-editor h2) {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

:deep(.ql-editor p) {
  margin-bottom: 0.75rem;
}

:deep(.ql-editor ul, .ql-editor ol) {
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
}
</style>
