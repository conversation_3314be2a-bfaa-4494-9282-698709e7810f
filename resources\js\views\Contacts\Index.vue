<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header -->
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-semibold text-gray-800">Contacts</h1>
      </div>
      <div>
        <button
          class="flex items-center gap-2 px-4 py-2 text-sm text-white bg-black rounded-lg hover:bg-gray-800 transition"
          @click="isContactModalOpen = true">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>
          Add Contact
        </button>
      </div>
    </div>

    <!-- Global Search -->
    <div class="relative mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
      <input v-model="searchTerm" @input="getContactList()" placeholder="Search contacts..."
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
    </div>

    <!-- Filters -->
    <div class="grid grid-cols-6 gap-4 mb-6">
      <select v-model="contactType" @change="getContactList()"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400">
        <option value="">All Types</option>
        <option value="general">General</option>
        <option value="clinic">Clinic</option>
        <option value="doctor">Doctor</option>
        <option value="pharmacy">Pharmacy</option>
      </select>
    </div>

    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clinic</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr v-if="isLoading">
            <td colspan="7" class="px-6 py-4 whitespace-nowrap text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </td>
          </tr>
          <tr v-else-if="contactList.length === 0">
            <td colspan="7" class="px-6 py-4 whitespace-nowrap text-center">No contacts found</td>
          </tr>
          <tr v-else v-for="contact in contactList" :key="contact.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">{{ contact.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" :class="{
                'bg-blue-100 text-blue-800': contact.type === 'general',
                'bg-indigo-100 text-indigo-800': contact.type === 'clinic',
                'bg-yellow-100 text-yellow-800': contact.type === 'doctor',
                'bg-green-100 text-green-800': contact.type === 'pharmacy'
              }">
                {{ contact.type }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ contact.email || '-' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ contact.phone || '-' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ formatAddress(contact) }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ contact.clinic_name || '-' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2">
                <button @click="viewContact(contact)" class="p-1 hover:bg-gray-100 rounded">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="w-4 h-4 text-gray-600">
                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                </button>
                <button @click="editContact(contact)" class="p-1 hover:bg-gray-100 rounded">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="w-4 h-4 text-gray-600">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                  </svg>
                </button>
                <button @click="deleteContact(contact.id)" class="p-1 hover:bg-gray-100 rounded">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="w-4 h-4 text-red-500">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    <line x1="10" x2="10" y1="11" y2="17"></line>
                    <line x1="14" x2="14" y1="11" y2="17"></line>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select v-model="perPage" @change="getContactList" class="border border-gray-300 rounded-md text-sm p-1">
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4" v-if="totalRows > 0">
          <span class="text-sm text-gray-700">
            Page {{ currentPage }} of {{ Math.ceil(totalRows / perPage) }}
          </span>
          <div class="flex gap-2">
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50" :disabled="currentPage === 1"
              @click="currentPage--; getContactList()">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="w-5 h-5 text-gray-600">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </button>
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
              :disabled="currentPage >= Math.ceil(totalRows / perPage)" @click="currentPage++; getContactList()">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="w-5 h-5 text-gray-600">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Modal -->
    <div v-if="isContactModalOpen" class="fixed inset-0 z-[9999] overflow-auto">
      <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeContactModal"></div>
      <div class="flex items-start justify-center min-h-screen pt-40 pb-4 px-4">
        <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-auto">
          <div class="border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">
              {{ selectedContactId ? 'Edit Contact' : 'Add Contact' }}
            </h3>
            <button @click="closeContactModal" class="text-gray-400 hover:text-gray-500">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="p-6">
            <div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Name <span
                      class="text-red-500">*</span></label>
                  <input type="text" v-model="newContact.name"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    required />
                </div>

                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Type <span
                      class="text-red-500">*</span></label>
                  <select v-model="newContact.type"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    required>
                    <option value="general">General</option>
                    <option value="clinic">Clinic</option>
                    <option value="doctor">Doctor</option>
                    <option value="pharmacy">Pharmacy</option>
                  </select>
                </div>

                <!-- Clinic selection (admin only) -->
                <div class="space-y-1 md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700">Contact Visibility</label>
                  <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
                    <div v-if="isAdmin" class="flex items-center">
                      <button type="button" @click="newContact.is_global = !newContact.is_global"
                        class="relative inline-flex h-6 w-11 items-center rounded-full"
                        :class="newContact.is_global ? 'bg-black' : 'bg-gray-300'">
                        <span class="inline-block h-4 w-4 transform rounded-full bg-white transition"
                          :class="newContact.is_global ? 'translate-x-6' : 'translate-x-1'">
                        </span>
                      </button>
                      <span class="ml-3 text-sm font-medium text-gray-700">
                        {{ newContact.is_global ? 'Global (visible to all clinics)' : 'Clinic-specific' }}
                      </span>
                    </div>

                    <div
                      v-if="(getUserRole() === 'administrator' || getUserRole() === 'doctor') & !newContact.is_global && clinicList.length > 0"
                      class="flex-grow">
                      <select v-model="newContact.clinic_id"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                        required>
                        <option :value="null">-- Select a clinic --</option>
                        <option v-for="clinic in clinicList" :key="clinic.id" :value="clinic">{{ clinic.label }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Email</label>
                  <input type="email" v-model="newContact.email"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
                </div>

                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Phone</label>
                  <input type="text" v-model="newContact.phone"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
                </div>

                <div class="space-y-1 md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700">Address</label>
                  <textarea v-model="newContact.address"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    rows="2"></textarea>
                </div>

                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">City</label>
                  <input type="text" v-model="newContact.city"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
                </div>

                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">State</label>
                  <input type="text" v-model="newContact.state"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
                </div>

                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Postal Code</label>
                  <input type="text" v-model="newContact.postal_code"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
                </div>

                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Country</label>
                  <input type="text" v-model="newContact.country"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
                </div>

                <div class="space-y-1 md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700">Notes</label>
                  <textarea v-model="newContact.notes"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    rows="3"></textarea>
                </div>

                <div class="md:col-span-2">
                  <div class="flex items-center">
                    <button type="button" @click="newContact.status = !newContact.status"
                      class="relative inline-flex h-6 w-11 items-center rounded-full"
                      :class="newContact.status ? 'bg-black' : 'bg-gray-300'">
                      <span class="inline-block h-4 w-4 transform rounded-full bg-white transition"
                        :class="newContact.status ? 'translate-x-6' : 'translate-x-1'">
                      </span>
                    </button>
                    <span class="ml-3 text-sm font-medium text-gray-700">
                      {{ newContact.status ? 'Active' : 'Inactive' }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="mt-6 flex justify-end space-x-3">
                <button type="button" @click="closeContactModal"
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                  Cancel
                </button>
                <button type="button" :disabled="isSavingContact" @click="saveContact"
                  class="flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50">
                  <span v-if="isSavingContact" class="mr-2">
                    <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                      viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                  </span>
                  {{ selectedContactId ? 'Update' : 'Save' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- View Contact Modal -->
    <div v-if="isViewModalOpen" class="fixed inset-0 z-[9999] overflow-auto">
      <div class="fixed inset-0 bg-black bg-opacity-50" @click="isViewModalOpen = false"></div>
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-auto">
          <div class="border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Contact Details</h3>
            <button @click="isViewModalOpen = false" class="text-gray-400 hover:text-gray-500">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="p-6">
            <div v-if="selectedContactData">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p class="mb-3"><span class="font-medium">Name:</span> {{ selectedContactData.name }}</p>
                  <p class="mb-3"><span class="font-medium">Type:</span> {{ selectedContactData.type }}</p>
                  <p class="mb-3"><span class="font-medium">Email:</span> {{ selectedContactData.email || '-' }}</p>
                  <p class="mb-3"><span class="font-medium">Phone:</span> {{ selectedContactData.phone || '-' }}</p>
                </div>
                <div>
                  <p class="mb-3"><span class="font-medium">Address:</span> {{ formatAddress(selectedContactData) || '-'
                  }}</p>
                  <p class="mb-3"><span class="font-medium">Clinic:</span> {{ selectedContactData.clinic_name || '-' }}
                  </p>
                  <p class="mb-3" v-if="selectedContactData.notes"><span class="font-medium">Notes:</span> {{
                    selectedContactData.notes }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="border-t border-gray-200 px-6 py-4 flex justify-end">
            <button @click="isViewModalOpen = false"
              class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get, post } from '../../config/request';
import { displayMessage, displayErrorMessage } from '../../utils/message';
import Create from './Create.vue';

export default {
  name: 'ContactIndex',
  components: {
    Create
  },
  data() {
    return {
      contactList: [],
      clinicList: [],
      isLoading: true,
      isContactModalOpen: false,
      isViewModalOpen: false,
      selectedContactId: null,
      selectedContactData: null,
      searchTerm: '',
      contactType: '',
      totalRows: 0,
      currentPage: 1,
      perPage: 10,
      newContact: {
        name: '',
        type: 'general',
        email: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
        clinic_id: null,
        notes: '',
        status: true,
        is_global: false
      },
      isSavingContact: false,
      isLoadingClinics: false
    };
  },
  created() {
    this.getContactList();
  },
  mounted() {
    if (this.getUserRole() === 'administrator' || this.getUserRole() === 'doctor') {
      this.getClinicList();
    }
  },
  computed: {
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
    currentUserRole() {
      return this.$store.state?.userData?.user_role || '';
    },
    // Whether the current user is an admin
    isAdmin() {
      return this.getUserRole() == 'administrator';
    },
    // Whether the user is a clinic admin
    isClinicAdmin() {
      return this.getUserRole() == 'clinic_admin';
    },
    // Whether the user is a doctor
    isDoctor() {
      return this.getUserRole() == 'doctor';
    },
    // Current clinic ID if available
    currentClinicId() {
      let userData = this.$store.state?.userData?.user;
      return userData && userData.clinic_id ? userData.clinic_id : null;
    }
  },
  methods: {
    hasPermission(permission) {
      return true; // Always return true for testing
    },
    formatAddress(contact) {
      const parts = [];
      if (contact.address) parts.push(contact.address);
      if (contact.city) parts.push(contact.city);
      if (contact.state) parts.push(contact.state);
      if (contact.postal_code) parts.push(contact.postal_code);
      if (contact.country) parts.push(contact.country);

      return parts.length > 0 ? parts.join(', ') : '-';
    },
    getContactList() {
      this.isLoading = true;

      const queryParams = {
        page: this.currentPage,
        perPage: this.perPage
      };

      if (this.searchTerm) {
        queryParams.searchTerm = this.searchTerm;
      }

      if (this.contactType) {
        queryParams.type = this.contactType;
      }

      // If user is clinic admin or doctor, add clinic_id filter
      if ((this.isClinicAdmin || this.isDoctor) && this.currentClinicId) {
        queryParams.clinic_id = this.currentClinicId;
      }

      console.log('Fetching contacts with params:', queryParams);

      get('contact_list', queryParams)
        .then(response => {
          console.log('Contact list response:', response);
          if (response.data.status) {
            this.contactList = response.data.data;
            this.totalRows = response.data.total || 0;
          } else {
            this.contactList = [];
            this.totalRows = 0;
          }
        })
        .catch(error => {
          console.error('Error fetching contacts:', error);
          displayErrorMessage(error);
          this.contactList = [];
          this.totalRows = 0;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    viewContact(contact) {
      this.selectedContactData = contact;
      this.isViewModalOpen = true;
    },
    editContact(contact) {
      this.selectedContactId = contact.id;
      this.selectedContactData = contact;

      // Set the is_global flag based on the clinic_id
      const is_global = contact.clinic_id === null || contact.is_global === 1;

      this.newContact = {
        ...contact,
        is_global: is_global,
        status: contact.status == 1
      };

      this.isContactModalOpen = true;
    },
    closeContactModal() {
      this.isContactModalOpen = false;
      this.selectedContactId = null;
      this.selectedContactData = null;
      this.newContact = {
        name: '',
        type: 'general',
        email: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
        clinic_id: null,
        notes: '',
        status: true,
        is_global: false
      };
    },

    getClinicList() {
      this.isLoadingClinics = true;
      get("get_static_data", {
        data_type: "clinic_list",
      })
        .then((response) => {
          this.clinicMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.clinicList = response.data.data;
          }
        })
        .catch((error) => {
          this.clinicMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    saveContact() {
      // Basic validation
      if (!this.newContact.name.trim()) {
        displayErrorMessage('Contact name is required');
        return;
      }

      if (!this.newContact.email && !this.newContact.phone) {
        displayErrorMessage('Either email or phone is required');
        return;
      }

      // Admin validation for clinic selection
      if (this.isAdmin && !this.newContact.is_global && !this.newContact.clinic_id) {
        displayErrorMessage('Please select a clinic for this contact');
        return;
      }

      this.isSavingContact = true;

      // Convert status to 0/1 for API
      const contactData = {
        ...this.newContact,
        status: this.newContact.status ? 1 : 0,
        is_global: this.newContact.is_global ? 1 : 0
      };
      console.log("getUserRole", this.getUserRole());

      // Handle clinic_id based on user role and global flag
      if (this.isAdmin || ["doctor"].includes(this.getUserRole())) {
        // For admin users, use the selected clinic_id or null if it's a global contact
        if (contactData.is_global) {
          contactData.clinic_id = null;
        }
        // Otherwise keep the selected clinic_id
      } else {

        if (["clinic_admin", "receptionist"].includes(this.getUserRole())) {
          // Handle other roles that need clinic IDs
          if (Array.isArray(this.userData.user_clinic_id)) {
            contactData.clinic_id = this.userData.user_clinic_id.map(id => ({ id: id.toString() }));
          } else {
            contactData.clinic_id = { id: this.userData.user_clinic_id.toString() };
          }
        } else {
          // For other roles
          contactData.clinic_id = 0;
        }
        // Non-admin users can't create global contacts
        contactData.is_global = 0;
      }

      // If editing, add the id
      if (this.selectedContactId) {
        contactData.id = this.selectedContactId;
      }

      post('contact_save', contactData)
        .then(response => {
          if (response.data.status) {
            displayMessage(this.selectedContactId
              ? 'Contact updated successfully'
              : 'Contact added successfully'
            );
            this.getContactList();
            this.closeContactModal();
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch(error => {
          console.error('Contact save error:', error);
          displayErrorMessage(error);
        })
        .finally(() => {
          this.isSavingContact = false;
        });
    },
    deleteContact(id) {
      if (!confirm('Are you sure you want to delete this contact?')) {
        return;
      }

      get('contact_delete', { id: id })
        .then(response => {
          if (response.data.status) {
            displayMessage('Contact deleted successfully');
            this.getContactList();
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch(error => {
          displayErrorMessage(error);
        });
    }
  }
};
</script>