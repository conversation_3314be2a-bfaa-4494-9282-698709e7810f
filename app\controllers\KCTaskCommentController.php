<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCTask;
use App\models\KCTaskComment;
use Exception;

class KCTaskCommentController extends KCBase {

	public $db;

	/**
	 * @var KCRequest
	 */
	private $request;

	public function __construct() {
		global $wpdb;
		$this->db = $wpdb;
		$this->request = new KCRequest();
		parent::__construct();
	}

	public function index() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID is required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		
		// Verify task exists and user has access to it
		$task = (new KCTask())->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}
		
		// Get the comments for this task
		$comments = (new KCTaskComment())->getTaskComments($task_id);

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Task comments', 'kc-lang'),
			'data' => $comments
		]);
	}

	public function save() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id']) || !isset($request_data['comment']) || empty($request_data['comment'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID and comment text are required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		$comment_text = sanitize_textarea_field($request_data['comment']);
		$user_id = get_current_user_id();

		// Verify task exists
		$task = (new KCTask())->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}

		try {
			$comment_model = new KCTaskComment();
			
			// Handle editing existing comment if comment_id is provided
			if (!empty($request_data['id'])) {
				$comment_id = (int)$request_data['id'];
				$existing_comment = $comment_model->get_by(['id' => $comment_id], '=', true);
				
				// Verify the comment exists and belongs to this user
				if (empty($existing_comment) || $existing_comment->task_id != $task_id) {
					wp_send_json([
						'status' => false,
						'message' => esc_html__('Comment not found', 'kc-lang')
					]);
				}
				
				// Only the comment author can edit the comment
				if ($existing_comment->user_id != $user_id) {
					wp_send_json([
						'status' => false,
						'message' => esc_html__('You do not have permission to edit this comment', 'kc-lang')
					]);
				}
				
				// Update the comment
				$result = $comment_model->update(
					['comment' => $comment_text],
					['id' => $comment_id]
				);
				
				$message = esc_html__('Comment updated successfully', 'kc-lang');
			} else {
				// Add new comment
				$result = $comment_model->addComment($task_id, $comment_text, $user_id);
				$message = esc_html__('Comment added successfully', 'kc-lang');
			}

			if ($result) {
				// Get all comments for the task to return updated list
				$comments = $comment_model->getTaskComments($task_id);
				
				wp_send_json([
					'status' => true,
					'message' => $message,
					'data' => $comments
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to save comment', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function delete() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Comment ID is required', 'kc-lang')
			]);
		}

		$comment_id = (int)$request_data['id'];
		$user_id = get_current_user_id();
		$user_role = $this->getLoginUserRole();

		try {
			$comment_model = new KCTaskComment();
			$comment = $comment_model->get_by(['id' => $comment_id], '=', true);
			
			if (empty($comment)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Comment not found', 'kc-lang')
				]);
			}
			
			// Verify the user has permission to delete this comment
			// Only the comment author, admin, or clinic admin can delete comments
			if ($comment->user_id != $user_id && 
				$user_role !== $this->getAdminRole() && 
				$user_role !== $this->getClinicAdminRole()) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('You do not have permission to delete this comment', 'kc-lang')
				]);
			}
			
			$result = $comment_model->deleteComment($comment_id);

			if ($result) {
				// Get all comments for the task to return updated list
				$comments = $comment_model->getTaskComments($comment->task_id);
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Comment deleted successfully', 'kc-lang'),
					'data' => $comments
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to delete comment', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}
}