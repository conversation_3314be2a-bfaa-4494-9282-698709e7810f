<template>
  <b-row>
    <div class="page-loader-section" v-if="formLoader || isColumnLoading">
      <loader-component-2></loader-component-2>
    </div>
    <b-col v-else sm="12">
      <form :novalidate="true">
        <b-card class="p-0 shadow" header-tag="header" footer-tag="footer">
          <template v-slot:header>
            <b-row>
              <b-col sm="12" md="8" lg="8">
                <h3 class="mb-0">{{ cardTitle }}</h3>
              </b-col>
              <b-col sm="12" md="4" lg="4">
                <div class="d-flex justify-content-end">
                  <router-link class="btn btn-sm btn-primary" :to="{ name: `${name}` }">
                    <i class="fa fa-angle-double-left"></i>
                    {{ formTranslation.common.back }}
                  </router-link>
                </div>
              </b-col>
            </b-row>
          </template>
          <div class="row" v-if="formStep === 1">
            <div class="col-md-4">
              <div class="form-group">
                <label for="name" class="form-control-label">
                  {{ formTranslation.common.name }}
                  <span class="text-danger">*</span>
                </label>
                <input id="name" v-model="formData.step_1.name" name="name" type="text" class="form-control"
                  :placeholder="formTranslation.common.enter_name">
                <div v-if="submitted && !$v.formData.step_1.name.required"
                  class="invalid-feedback">
                  {{ formTranslation.common.name_required }}
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="module_name" class="form-control-label">
                  {{ formTranslation.webhooks.module_name }}
                  <span class="text-danger">*</span>
                </label>
                <multi-select
                  :placeholder="formTranslation.webhooks.select_module_name"
                  v-model="formData.step_1.module_name"
                  @select="moduleNameChange"
                  @remove="moduleNameChange"
                  id="module_name"
                  :customLabel="(value) => customLabel(value,moduleNameOption)"
                  :options="moduleNameOptionsValue"
                  :loading="!moduleNameOptionsValue.length"
                >
                </multi-select>
                <div v-if="submitted && !$v.formData.step_1.module_name.required"
                  class="invalid-feedback">
                  {{ formTranslation.webhooks.module_name_required }}
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="event_name" class="form-control-label">
                  {{ formTranslation.webhooks.event_name }}
                  <span class="text-danger">*</span>
                </label>
                <multi-select
                  :placeholder="!formData.step_1.module_name
                    ? formTranslation.webhooks.please_first_select_module : formTranslation.webhooks.select_event_name"
                  v-model="formData.step_1.event_name"
                  id="event_name"
                  :customLabel="(value) => customLabel(value,eventNameOption)"
                  :options="eventNameOptionsValue"

                >
                </multi-select>
                <div v-if="submitted && !$v.formData.step_1.event_name.required"
                  class="invalid-feedback">
                  {{ formTranslation.webhooks.event_name_required }}
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="status" class="form-control-label">
                  {{ formTranslation.common.status }}
                  <span class="text-danger">*</span>
                </label>
                <multi-select
                  v-model="formData.step_1.status"
                  id="status"
                  :customLabel="(value) => customLabel(value,statusOptions)"
                  :options="statusOptionsValue"
                  :preselectFirst="!editID"

                ></multi-select>
                <div v-if="submitted && !$v.formData.step_1.status.required"
                  class="invalid-feedback">
                  {{ formTranslation.appointments.status_required }}
                </div>
              </div>
            </div>
          </div>
          <div class="row" v-if="formStep === 2">
            <div class="col-md-12">
              <div class="form-group">
                <label for="methods" class="form-control-label">
                  {{ formTranslation.webhooks.webhooks_method }}
                  <span class="text-danger">*</span>
                </label>
                <b-input-group >
                  <template #prepend>
                    <multi-select
                      v-model="formData.step_2.methods"
                      :placeholder="formTranslation.webhooks.select_webhooks_method"
                      id="methods"
                      :customLabel="(value) => customLabel(value,webhooksMethodsOption)"
                      :options="webhooksMethodsOptionValue"
                      :loading="!webhooksMethodsOptionValue.length"

                    >
                    </multi-select>
                  </template>
                  <input
                      v-model="formData.step_2.webhook_data.url"
                      type="url"
                      class="form-control"
                      autocomplete="on"
                      :placeholder="formTranslation.webhooks.url_placeholder"
                    />
                </b-input-group>
                <div v-if="submitted && !$v.formData.step_2.webhook_data.url.required" class="invalid-feedback">
                  {{formTranslation.webhooks.url_required}}
                </div>
<!--                <div v-if="submitted && !$v.formData.step_2.webhook_data.url.url" class="invalid-feedback">-->
<!--                  {{formTranslation.webhooks.please_enter_valid_url}}-->
<!--                </div>-->
                <div v-if="submitted && !$v.formData.step_2.methods.required" class="invalid-feedback">
                  {{formTranslation.webhooks.webhooks_method_required}}
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-group">
                <label for="header-data">{{formTranslation.common.headers}}&nbsp;&nbsp;
                  <button type="button" v-if="!(formData.step_2.webhook_data?.headers?.length)" class="btn btn-primary btn-sm" @click="pushData('headers')">{{formTranslation.common.add_header}}</button>
                </label>
                <div v-for="(header, index) in formData.step_2.webhook_data.headers" :key="index" class="row mb-2" v-if="formData.step_2.webhook_data?.headers?.length">
                  <div class="col-5">
                    <input v-model="header.key" type="text" :placeholder="formTranslation.common.enter_key" class="form-control" />
                    <div v-if="submitted && !$v.formData.step_2.webhook_data.headers.$each[index].key.required"
                         class="invalid-feedback">{{$t('webhooks.key_required')}}
                    </div>
                  </div>
                  <div class="col-md-5">
                    <multi-select
                      v-model="header.value"
                      :placeholder="formTranslation.webhooks.select_or_enter_value"
                      :taggable="true"
                      :options="dynamicKeysOption"
                      :loading="formData.step_1.event_name && !dynamicKeysOption.length"
                      @tag="(newTag) => {
                        header.value = newTag;
                        dynamicKeysOption.push(newTag)
                      }"

                    >
                    <span slot="noOptions">
                      {{ formTranslation.webhooks.select_event_name_to_dynamic_key  }}
                    </span>
                    </multi-select>
                    <div v-if="submitted && !$v.formData.step_2.webhook_data.headers.$each[index].value.required"
                         class="invalid-feedback">{{$t('webhooks.value_required')}}
                    </div>
                  </div>
                  <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger" @click="popData('headers',index)">
                      <i :title="formTranslation.webhooks.delete_header_data" class="fa fa-trash"></i>
                    </button>
                  </div>
                </div>
                <button type="button" v-if="formData.step_2.webhook_data?.headers?.length" class="btn btn-primary btn-sm" @click="pushData('headers')">{{formTranslation.common.add_header}}</button>
              </div>
            </div>
            <div class="col-md-12" v-if="selectedMethodType === httpGetMethodName">
              <div class="form-group">
                <label for="query-params">{{formTranslation.common.query_parameters}}&nbsp;&nbsp;
                  <button type="button" v-if="!(formData?.step_2?.webhook_data?.query_parameters?.length)" class="btn btn-primary btn-sm" @click="pushData('query_parameters')">{{formTranslation.common.add_query_parameter}}</button>
                </label>
                <div v-for="(param, index) in formData.step_2.webhook_data.query_parameters" :key="index" class="row mb-2">
                  <div class="col-md-5">
                    <input v-model="param.key" type="text" :placeholder="formTranslation.common.enter_key" class="form-control" />
                    <div v-if="submitted && !$v.formData.step_2.webhook_data.query_parameters.$each[index].key.required"
                         class="invalid-feedback">{{$t('webhooks.key_required')}}
                    </div>
                  </div>
                  <div class="col-md-5">
                    <multi-select
                      v-model="param.value"
                      :placeholder="formTranslation.webhooks.select_or_enter_value"
                      :taggable="true"
                      :options="dynamicKeysOption"
                      :loading="formData.step_1.event_name && !dynamicKeysOption.length"
                      @tag="(newTag) => {
                      param.value = newTag;
                      dynamicKeysOption.push(newTag)
                    }"

                    >
                    <span slot="noOptions">
                      {{ formTranslation.webhooks.select_event_name_to_dynamic_key  }}
                    </span>
                    </multi-select>
                    <div v-if="submitted && !$v.formData.step_2.webhook_data.query_parameters.$each[index].value.required"
                         class="invalid-feedback">{{$t('webhooks.value_required')}}
                    </div>
                  </div>
                  <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger" @click="popData('query_parameters',index)">
                      <i :title="formTranslation.webhooks.delete_query_parameter" class="fa fa-trash"></i>
                    </button>
                  </div>
                </div>
                <button type="button" v-if="formData?.step_2?.webhook_data?.query_parameters?.length" class="btn btn-primary btn-sm" @click="pushData('query_parameters')">
                  {{formTranslation.common.add_query_parameter}}
                </button>
              </div>
            </div>
            <div class="col-md-12" v-else>
              <div class="form-group">
                <label for="content-type">{{$t('webhooks.content_type')}}</label>
                <multi-select
                  :placeholder="formTranslation.webhooks.select_content_type"
                  v-model="formData.step_2.webhook_data.content_type"
                  id="content-type"
                  :options="contentTypeOption"
                  trackBy="value"
                  label="text"

                >
                </multi-select>
              </div>
            </div>
            <div class="col-md-12" v-if="selectedMethodType !== httpGetMethodName && selectedContentType === 'json_data'">
              <div class="form-group">
                <label for="json_body">{{$t('webhooks.json_data')}}</label>
                <vue-json-editor v-model="formData.step_2.webhook_data.json_data" :showBtns="false" mode="code" @json-change="jsonEditorChange" @has-error="jsonError" :expandedOnStart="true" ></vue-json-editor>
              </div>
            </div>
            <div class="col-md-12" v-if="selectedMethodType !== httpGetMethodName && selectedContentType === 'form_data'">
              <div class="form-group">
                <label for="form_data">{{$t('webhooks.form_data')}}&nbsp;&nbsp;
                  <button type="button" v-if="!(formData.step_2.webhook_data.form_data.length)" class="btn btn-primary btn-sm"
                          @click="pushData('form_data')">
                    {{formTranslation.webhooks.add_form_data}}
                  </button>
                </label>
                <div v-for="(form_data, index) in formData.step_2.webhook_data.form_data" :key="index" class="row mb-2">
                  <div class="col-md-5">
                    <input v-model="form_data.key" type="text" :placeholder="formTranslation.common.enter_key" class="form-control" />
                    <div v-if="submitted && !$v.formData.step_2.webhook_data.form_data.$each[index].key.required"
                         class="invalid-feedback">{{$t('webhooks.key_required')}}
                    </div>
                  </div>
                  <div class="col-md-5">
                    <multi-select
                      v-model="form_data.value"
                      :placeholder="'Select or enter value'"
                      :taggable="true"
                      :options="dynamicKeysOption"
                      :loading="formData.step_1.event_name && !dynamicKeysOption.length"
                      @tag="(newTag) => {
                        form_data.value = newTag;
                        dynamicKeysOption.push(newTag)
                      }"

                    >
                      <span slot="noOptions">
                        {{ formTranslation.webhooks.select_event_name_to_dynamic_key  }}
                      </span>
                    </multi-select>
                    <div v-if="submitted && !$v.formData.step_2.webhook_data.form_data.$each[index].value.required"
                         class="invalid-feedback">{{$t('webhooks.value_required')}}
                    </div>
                  </div>
                  <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger" @click="popData('form_data',index)">
                      <i :title="formTranslation.webhooks.delete_form_data" class="fa fa-trash"></i>
                    </button>
                  </div>
                </div>
                <button type="button" v-if="formData.step_2.webhook_data.form_data.length" class="btn btn-primary btn-sm"
                        @click="pushData('form_data')">
                  {{formTranslation.webhooks.add_form_data}}
                </button>
              </div>
            </div>
            <div class="col-md-12">
              <p class="mb-0">
                {{formTranslation.settings.dynamic_keys_list}}
              </p>
              <button class="btn btn-sm  btn-primary mt-2" v-b-tooltip.hover :title="copyToolTipText" v-for="(dynamic_key, key) in dynamicKeysOption"
                      :key="key" @click.prevent="copyToClipboard(dynamic_key, formTranslation.webhooks.template_dynamic_key)" >
                {{dynamic_key}}
              </button>
            </div>
          </div>
          <template v-slot:footer>
            <div class="row">
              <div class="col-md-12 p-0">
                <div class="d-flex justify-content-end">
                  <div v-if="formStep === 1">
                    <router-link class="btn btn-outline-primary" :to="{ name: `${name}` }">
                      {{ formTranslation.common.cancel }}
                    </router-link>
                    <button class="btn btn-primary" @click.prevent="updateFormStep(2)">
                      <i class="fas fa-step-forward"></i>&nbsp; {{ formTranslation.datatable.next_text }}
                    </button>
                  </div>
                  <div v-else>
                    <button class="btn btn-outline-primary" @click.prevent="updateFormStep(1)" >
                      <i class="fas fa-step-backward"></i>&nbsp; {{ formTranslation.datatable.prev_text }}
                    </button>
                    <button v-if="!loading" class="btn btn-primary" @click.prevent="handleSubmit">
                      <i class="fa fa-save"></i>&nbsp;{{ formTranslation.patient.save_btn }}
                    </button>
                    <button v-else class="btn btn-primary" disabled>
                      <i class="fa fa-sync fa-spin"></i>&nbsp; {{ formTranslation.common.loading }}
                    </button>
                  
                  </div>
                </div>
              </div>
            </div>
          </template>
        </b-card>
      </form>
    </b-col>
  </b-row>
</template>

<script>
import { copyToClipboard } from "../../config/helper";
import {
  helperModuleGetEditData,
  helperModuleSaveData
} from "../../utils/create";
import {
  required
} from "vuelidate/lib/validators";
import {mapActions} from "vuex";
import vueJsonEditor from 'vue-json-editor'

export default {
  components:{
    vueJsonEditor
  },
  data: () => {
    return {
      isColumnLoading: false,
      formData: {},
      apiEndpoint: {
        edit: 'webhooks_edit',
        save: 'webhooks_save',
        column: 'webhooks_column'
      },
      name: 'webhooks',
      loading: false,
      submitted: false,
      cardTitle: '',
      formLoader: true,
      eventNameOption:[],
      formStep:1,
      statusOptions:[],
      dynamicKeysOption:[],
      contentTypeOption:[],
      moduleNameOption:[],
      allEventNameOption:[],
      webhooksMethodsOption:[],
      moduleNameOptionsValue:[],
      eventNameOptionsValue:[],
      statusOptionsValue:[],
      webhooksMethodsOptionValue:[],
      httpGetMethodName : 'GET',
      jsonEditorError:false,
      copyToolTipText:''
    }
  },
  validations: {
    formData: {
      step_1:{
        name: {
          required,
        },
        module_name: {
          required
        },
        event_name: {
          required
        },
        status: {
          required
        }
      },
      step_2:{
        methods: {
          required
        },
        webhook_data: {
          url: {
            required,
          },
          headers: {
            $each: {
              key: { required },
              value: { required },
            }
          },
          query_parameters:{
            $each: {
              key: { required },
              value: { required },
            }
          },
          form_data:{
            $each: {
              key: { required },
              value: { required },
            }
          },
        },
      }
    }
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole()) || !(this?.userData?.addOns?.webhooks)) {
      this.$router.push({ name: "403" });
    }
    this.copyToolTipText = this.formTranslation.settings.click_to_copy;
    this.init();
  },
  methods: {
    init() {
      this.getStatusOption();
      this.contentTypeOption = this.getContentTypeOption();
      this.getTableColumn();
      this.formData = this.defaultFormData();
      /// Code for the Edit functionality...
      if (this.$route.params.id) {
        this.formData.step_1.id = this.$route.params.id;
        this.cardTitle = this.formTranslation.webhooks.edit_webhook;
        this.getEditFormData();
      } else {
        this.cardTitle = this.formTranslation.webhooks.create_webhook;
        this.formLoader = false;
      }
    },
    async getEditFormData(){
      this.formLoader = true
      const formData = await helperModuleGetEditData(this.apiEndpoint.edit,{ id : this.editID});
      if(formData){
        this.formData = formData;
        this.generateEventNameOption();
      }else{
        this.navigateToListPage();
      }
      this.formLoader = false
    },
    getStatusOption(){
      this.statusOptions = {
        "yes": this.formTranslation.common.active,
        "no": this.formTranslation.common.inactive,
      };
      this.statusOptionsValue = Object.keys(this.statusOptions);
    },
    getContentTypeOption(){
      return  [
        { value:'form_data',text:this.formTranslation.webhooks.form_data },
        { value:'json_data',text:this.formTranslation.webhooks.json_data },
      ];
    },
    navigateToListPage() {
      this.$router.push({ name: this.name });
    },
    defaultFormData() {
      return {
        step_1:{
          id:'',
          name: '',
          module_name: '',
          event_name: '',
          status:'',
        },
        step_2:{
          methods:'POST',
          webhook_data: {
            url:'',
            headers:[],
            query_parameters:[],
            content_type:'',
            form_data:[],
            json_data: {
              "id" : "{{id}}"
            }
          },
        },
        user_id: '',
      }
    },
    handleSubmit() {
      if(this.jsonEditorError && this.formData.step_2.methods !== this.httpGetMethodName && this.formData.step_2?.webhook_data?.content_type?.value === 'json_data'){
        displayErrorMessage(this.formTranslation.webhooks.please_enter_valid_json);
        return;
      }
      this.loading = true;
      this.submitted = true;
      // stop here if form is invalid
      if(this.validateFormData('step_2')){
        this.loading = false;
        return;
      }
      this.submitted = false;
      this.saveFormData();
    },
    async saveFormData(){
      this.loading = true
      const response = await helperModuleSaveData(this.apiEndpoint.save,this.formData);
      if(response){
        this.navigateToListPage();
      }
      this.loading = false
    },
    validateFormData(step) {
      this.$v.$touch();
      this.$nextTick(() => {
        const invalidElement = document.querySelector('.is-invalid') || document.querySelector('.invalid-feedback');
        if (invalidElement) {
          invalidElement.scrollIntoView({ block: "center", behavior: "smooth" });
        }
      })
      return this.$v.formData[step].$invalid;
    },
    updateFormStep(step){
      if(step === 2){
        this.submitted = true;
        if(this.validateFormData('step_1')){
          return;
        }
        this.submitted = false;
      }
      this.formStep = step;
    },
    parseQueryParams(url) {
      const queryParams = [];
      const queryString = url.split('?')[1];
      if (queryString) {
        const paramsArray = queryString.split('&');
        paramsArray.forEach(paramString => {
          const [key, value] = paramString.split('=');
          queryParams.push({ key, value });
        });
      }
      return queryParams;
    },
    pushData(type){
      if(this.formData.step_2?.webhook_data?.[type]){
        this.formData.step_2.webhook_data[type].push({ key: '', value: '' });
      }
    },
    popData(type,index){
      if(this.formData.step_2?.webhook_data?.[type]){
        this.formData.step_2.webhook_data[type].splice(index, 1);
      }
    },
    customLabel(selected_value, fieldOptionData){
      if(fieldOptionData?.[selected_value]?.text){
        return fieldOptionData[selected_value].text;
      }
      if(fieldOptionData?.[selected_value]){
        return fieldOptionData[selected_value];
      }
      return selected_value;
    },
    generateModuleOption( column_data ){
      const options = [];
      const value = [];
      if(column_data?.filterOptions?.filterDropdownItems){
        column_data.filterOptions.filterDropdownItems.forEach( (index) => {
          options[index.value] = index.text;
          value.push(index.value);
        });
      }
      this.moduleNameOption = options;
      this.moduleNameOptionsValue = value;
    },
    generateAllEventOption( column_data ){
      if(column_data?.filterOptions?.filterDropdownItemsCopy){
        this.allEventNameOption = column_data.filterOptions.filterDropdownItemsCopy
      }
      if(this.editID && !this.eventNameOptionsValue.length){
        this.generateEventNameOption();
        this.generateDynamicKeysOptions();
      }
    },
    generateWebhookMethodOption( column_data ){
      const options = [];
      const value = [];
      if(column_data?.filterOptions?.filterDropdownItems){
        column_data.filterOptions.filterDropdownItems.forEach( (index) => {
          options[index.value] = index.text;
          value.push(index.value);
        });
      }
      this.webhooksMethodsOption = options;
      this.webhooksMethodsOptionValue = value;
    },
    generateEventNameOption( ){
      const module_name = this.formData.step_1.module_name;
      if(!this.allEventNameOption?.[module_name]){
        return;
      }
      this.eventNameOptionsValue = [];
      this.eventNameOption = [];
      const data = this.allEventNameOption[module_name];
      for( const property in data){
        const index = data[property].value;
        this.eventNameOptionsValue.push(index);
        this.eventNameOption[index] = data[property];
      }
    },
    generateDynamicKeysOptions(){
      const event_name = this.formData.step_1.event_name;
      this.dynamicKeysOption = this.eventNameOption?.[event_name]?.dynamic_keys ? this.eventNameOption[event_name].dynamic_keys : [];
    },
    formatColumnData( column_data ){
      if(column_data?.module_name ){
        this.generateModuleOption( column_data.module_name );
      }
      if(column_data?.event_name ){
        this.generateAllEventOption( column_data.event_name );
      }
      if(column_data?.methods ){
        this.generateWebhookMethodOption( column_data.methods );
      }
    },
    moduleNameChange(){
      this.formData.step_1.event_name = '';
      this.generateEventNameOption();
    },
    ...mapActions('tableModule', ["fetchTableColumns"]),
    async getTableColumn() {
      this.isColumnLoading = true
      const columns = await this.fetchTableColumns({
        endpoint: this.apiEndpoint.column,
        module: 'webhooks'
      });
      if(columns){
        this.formatColumnData(columns);
      }
      this.isColumnLoading = false
    },
    jsonEditorChange(data){
      this.jsonEditorError = this.isJsonString(data)
    },
    isJsonString(str){
      try {
        JSON.parse(str);
      } catch (e) {
        return false;
      }
      return true;
    },
    jsonError(){
      this.jsonEditorError = true
    },
    copyToClipboard
  },
  computed: {
    userData() {
      return this?.$store?.state?.userDataModule && this?.$store?.state?.userDataModule?.user
        ? this.$store.state.userDataModule.user
        : [];
    },
    selectedMethodType(){
      return this.formData.step_2.methods;
    },
    selectedContentType(){
      return this.formData.step_2.webhook_data?.content_type?.value;
    },
    editID(){
      return this.formData.step_1.id;
    },
  },
  watch:{
    'formData.step_1.event_name': function () {
      this.generateDynamicKeysOptions();
    },
    'formData.step_2.webhook_data.url':function (newUrl) {
      this.formData.step_2.webhook_data.query_parameters = this.parseQueryParams(newUrl);
    },
    'formData.step_2.webhook_data.query_parameters': {
      deep: true,
      handler(newQueryParams) {
        if (this.selectedMethodType === this.httpGetMethodName) {
          const queryParamsString = newQueryParams
              .filter(param => param.key.trim() !== '' || param.value.trim() !== '')
              .map(param => `${param.key}=${param.value}`)
              .join('&');
          this.formData.step_2.webhook_data.url =
              this.formData.step_2.webhook_data.url.split('?')[0] + (queryParamsString ? `?${queryParamsString}` : '');
        }
      }
    }
  }
}
</script>
<style>
.jsoneditor-menu{
  display: none;
}
div.jsoneditor{
  border: 1px solid var(--primary);
}
</style>