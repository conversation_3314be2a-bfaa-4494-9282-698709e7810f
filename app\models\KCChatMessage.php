<?php

namespace App\models;

use App\baseClasses\MDModel;

class KCChatMessage extends MDModel {
    public function __construct() {
        // The parent constructor will prefix this with 'md_'
        parent::__construct('chat_messages');
    }
    
    /**
     * Override the insert method to ensure tables exist and add debugging
     *
     * @param array $data Data to insert
     * @return int|false ID of new record or false on failure
     */
    public function insert($data) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'md_chat_messages';
        
        // First, let's log what table name our model thinks it should use
        error_log('Model tableName property: ' . $this->tableName);
        error_log('Expected table name: ' . $table_name);
        
        // Check if table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $table_name
            )
        );
        
        if (!$table_exists) {
            error_log('Chat messages table does not exist: ' . $table_name);
            return false;
        }
        
        // Add verbose debugging about the data we're inserting
        error_log('Attempting to insert chat message with data: ' . json_encode($data));
        
        // Check if the file_name column exists
        $file_name_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s",
                DB_NAME,
                $table_name,
                'file_name'
            )
        );
        
        // Log column existence
        error_log('file_name column exists: ' . ($file_name_exists ? 'Yes' : 'No'));
        
        // If the file_name column doesn't exist, try to add it
        if (!$file_name_exists && isset($data['file_name'])) {
            error_log('Adding missing file_name column to ' . $table_name . ' table');
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN file_name VARCHAR(255) DEFAULT NULL AFTER file_type");
            
            // Check if the column was added successfully
            $check_column = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*)
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = %s",
                    DB_NAME,
                    $table_name,
                    'file_name'
                )
            );
            
            error_log('file_name column added successfully: ' . ($check_column ? 'Yes' : 'No'));
            
            // If we failed to add the column, remove it from data to prevent insert failure
            if (!$check_column && isset($data['file_name'])) {
                error_log('Removing file_name from data since column could not be added');
                unset($data['file_name']);
            }
        }
        
        // Now insert the data - use direct table name to avoid any issues with parent class
        try {
            // Prepare format strings for wpdb->insert
            $formats = [];
            foreach ($data as $value) {
                if (is_int($value)) {
                    $formats[] = '%d';
                } elseif (is_float($value)) {
                    $formats[] = '%f';
                } else {
                    $formats[] = '%s';
                }
            }
            
            error_log('Inserting into ' . $table_name . ' with formats: ' . json_encode($formats));
            
            // We'll use direct wpdb->insert() to get more error details if it fails
            $result = $wpdb->insert($table_name, $data, $formats);
            
            if ($result === false) {
                error_log('Error inserting chat message: ' . $wpdb->last_error);
                
                // Try one more time after removing file_name if it exists and caused problems
                if (isset($data['file_name'])) {
                    error_log('Retrying insert without file_name field');
                    unset($data['file_name']);
                    // Update formats array
                    $formats = array_slice($formats, 0, count($data));
                    $result = $wpdb->insert($table_name, $data, $formats);
                    
                    if ($result === false) {
                        error_log('Second insert attempt also failed: ' . $wpdb->last_error);
                        return false;
                    }
                } else {
                    return false;
                }
            }
            
            $insert_id = $wpdb->insert_id;
            error_log('Chat message inserted successfully with ID: ' . $insert_id);
            return $insert_id;
        } catch (\Exception $e) {
            error_log('Exception creating chat message: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            return false;
        }
    }
    
    /**
     * Get messages for a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @return array Messages
     */
    public function getMessagesForConversation($conversation_id) {
        global $wpdb;
        
        // Validate conversation_id
        $conversation_id = intval($conversation_id);
        if ($conversation_id <= 0) {
            error_log('Invalid conversation_id passed to getMessagesForConversation: ' . $conversation_id);
            return [];
        }
        
        // Check if the messages table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $wpdb->prefix . 'md_chat_messages'
            )
        );
        
        try {
            // Get all messages for this conversation with sender info
            $messages = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT m.*, u.display_name as sender_name, 
                        u.user_email as sender_email,
                        um.meta_value as sender_avatar
                    FROM {$wpdb->prefix}md_chat_messages m 
                    JOIN {$wpdb->users} u ON m.user_id = u.ID 
                    LEFT JOIN {$wpdb->usermeta} um ON u.ID = um.user_id AND um.meta_key = %s
                    WHERE m.conversation_id = %d 
                    ORDER BY m.created_at ASC",
                    'kc_profile_image',
                    $conversation_id
                )
            );
            
            // Add debug information
            error_log('Retrieved ' . count($messages) . ' messages for conversation ID: ' . $conversation_id);
            
            return $messages ?: [];
        } catch (\Exception $e) {
            error_log('Error in getMessagesForConversation: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get new messages for a conversation since a specific message ID
     * 
     * @param int $conversation_id Conversation ID
     * @param int $last_message_id Last message ID
     * @return array New messages
     */
    public function getNewMessages($conversation_id, $last_message_id) {
        global $wpdb;
        
        $messages = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT m.*, u.display_name as sender_name 
                FROM {$wpdb->prefix}md_chat_messages m 
                JOIN {$wpdb->users} u ON m.user_id = u.ID 
                WHERE m.conversation_id = %d AND m.id > %d 
                ORDER BY m.created_at ASC",
                $conversation_id,
                $last_message_id
            )
        );
        
        return $messages;
    }

    /**
     * Get the last message for a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @return object|null Last message if exists, null otherwise
     */
    public function getLastMessageForConversation($conversation_id) {
        global $wpdb;
        
        $message = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT m.*, u.display_name as sender_name 
                FROM {$wpdb->prefix}md_chat_messages m 
                JOIN {$wpdb->users} u ON m.user_id = u.ID 
                WHERE m.conversation_id = %d 
                ORDER BY m.created_at DESC 
                LIMIT 1",
                $conversation_id
            )
        );
        
        return $message;
    }

    /**
     * Get unread message count for a user in a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @param int $user_id User ID
     * @return int Unread count
     */
    public function getUnreadCount($conversation_id, $user_id) {
        global $wpdb;
        
        // Validate inputs
        $conversation_id = intval($conversation_id);
        $user_id = intval($user_id);
        
        if ($conversation_id <= 0 || $user_id <= 0) {
            error_log("Invalid parameters in getUnreadCount: conversation_id=$conversation_id, user_id=$user_id");
            return 0;
        }
        
        try {
            // Make sure tables exist
            $messages_table = $wpdb->prefix . 'md_chat_messages';
            $read_table = $wpdb->prefix . 'md_chat_message_read';
            
            // Check if the first table exists
            $table_check = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = %s AND table_name = %s",
                    DB_NAME,
                    $messages_table
                )
            );
            
            if (!$table_check) {
                error_log("Table $messages_table doesn't exist!");
                return 0;
            }
            
            // Execute the count query
            $unread_count = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM $messages_table m 
                    LEFT JOIN $read_table r ON m.id = r.message_id AND r.user_id = %d 
                    WHERE m.conversation_id = %d AND m.user_id != %d AND r.id IS NULL",
                    $user_id,
                    $conversation_id,
                    $user_id
                )
            );
            
            // Log the result for debugging
            error_log("Unread count for conversation_id=$conversation_id, user_id=$user_id: " . (int)$unread_count);
            
            // Convert to integer and return
            return (int)$unread_count;
        } catch (\Exception $e) {
            error_log("Error in getUnreadCount: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get total unread message count for a user across all conversations
     * 
     * @param int $user_id User ID
     * @return int Total unread count
     */
    public function getTotalUnreadCount($user_id) {
        global $wpdb;
        
        // Validate input
        $user_id = intval($user_id);
        if ($user_id <= 0) {
            error_log("Invalid user_id in getTotalUnreadCount: $user_id");
            return 0;
        }
        
        try {
            // Make sure tables exist
            $messages_table = $wpdb->prefix . 'md_chat_messages';
            $members_table = $wpdb->prefix . 'md_chat_members';
            $read_table = $wpdb->prefix . 'md_chat_message_read';
            
            // Check if the tables exist
            $messages_exist = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = %s AND table_name = %s",
                    DB_NAME,
                    $messages_table
                )
            );
            
            if (!$messages_exist) {
                error_log("Table $messages_table doesn't exist!");
                return 0;
            }
            
            // Execute the count query with proper table references
            $total_unread = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM $messages_table m 
                    JOIN $members_table mem ON m.conversation_id = mem.conversation_id 
                    LEFT JOIN $read_table r ON m.id = r.message_id AND r.user_id = %d 
                    WHERE mem.user_id = %d AND m.user_id != %d AND r.id IS NULL",
                    $user_id,
                    $user_id,
                    $user_id
                )
            );
            
            // Log the query and result for debugging
            $query = $wpdb->last_query;
            error_log("Total unread query: $query");
            error_log("Total unread count for user_id=$user_id: " . (int)$total_unread);
            
            // Convert to integer and return
            return (int)$total_unread;
        } catch (\Exception $e) {
            error_log("Error in getTotalUnreadCount: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Mark messages as read for a user in a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @param int $user_id User ID
     * @return bool True on success, false on error
     */
    public function markMessagesAsRead($conversation_id, $user_id) {
        global $wpdb;
        
        // Validate inputs
        $conversation_id = intval($conversation_id);
        $user_id = intval($user_id);
        
        if ($conversation_id <= 0 || $user_id <= 0) {
            error_log("Invalid parameters in markMessagesAsRead: conversation_id=$conversation_id, user_id=$user_id");
            return false;
        }
        
        // Define the correct table name
        $read_table = $wpdb->prefix . 'md_chat_message_read';
        
        // Check if the read status table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $read_table
            )
        );
        
        if (!$table_exists) {
            error_log("The read messages table ($read_table) doesn't exist");
            // Try to create it when we can
            $this->createTables();
        }
        
        try {
            // Get all unread messages in this conversation (sent by others)
            // Use consistent table names
            $messages_table = $wpdb->prefix . 'md_chat_messages';
            $read_table = $wpdb->prefix . 'md_chat_message_read';
            
            $unread_messages = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT m.id FROM $messages_table m 
                    LEFT JOIN $read_table r ON m.id = r.message_id AND r.user_id = %d 
                    WHERE m.conversation_id = %d AND m.user_id != %d AND r.id IS NULL",
                    $user_id,
                    $conversation_id,
                    $user_id
                )
            );
            
            error_log("Found " . count($unread_messages) . " unread messages in conversation $conversation_id for user $user_id");
            
            if (empty($unread_messages)) {
                return true; // No unread messages
            }
            
            // Mark each message as read
            $now = current_time('mysql');
            $success = true;
            $marked_count = 0;
            
            foreach ($unread_messages as $message) {
                $result = $wpdb->insert(
                    $read_table,
                    [
                        'message_id' => $message->id,
                        'user_id' => $user_id,
                        'read_at' => $now
                    ],
                    ['%d', '%d', '%s']
                );
                
                if ($result) {
                    $marked_count++;
                } else {
                    error_log('Error marking message ' . $message->id . ' as read: ' . $wpdb->last_error);
                    $success = false;
                }
            }
            
            error_log("Marked $marked_count out of " . count($unread_messages) . " messages as read");
            
            // Return success flag
            return $success;
        } catch (\Exception $e) {
            error_log('Exception in markMessagesAsRead: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            return false;
        }
    }
}