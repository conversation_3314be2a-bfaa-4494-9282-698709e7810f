<template>
  <div class="task-attachments-container">
    <!-- Loading state -->
          <div v-if="loading" class="flex justify-center items-center py-5">
      <div class="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-black"></div>
      <span class="ml-3 text-gray-700">{{ formTranslation.common.loading || 'Loading...' }}</span>
    </div>
    
    <!-- Upload form -->
    <div v-else class="mb-6">
      <label for="file-upload" class="block text-sm font-medium text-gray-700 mb-2">
        {{ formTranslation.task.upload_attachment || 'Upload Attachment' }}
      </label>
      
      <!-- Drop zone -->
      <div 
        class="drop-zone border-2 border-dashed border-gray-300 rounded-lg transition-all duration-200"
        :class="{ 'border-black bg-black/5': isDragging }"
        @dragover.prevent="isDragging = true"
        @dragleave.prevent="isDragging = false"
        @drop.prevent="handleFileDrop"
      >
        <div class="flex flex-col items-center justify-center p-6">
          <!-- Upload icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <p class="mb-1 text-gray-600">{{ formTranslation.task.drop_files_here || 'Drop files here' }}</p>
          <p class="text-gray-500 text-sm mb-3">{{ formTranslation.task.or || 'or' }}</p>
          <label for="file-upload" class="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 cursor-pointer">
            {{ formTranslation.task.browse_files || 'Browse Files' }}
          </label>
          <input type="file" id="file-upload" class="hidden" @change="handleFileSelect" />
          <p class="text-gray-500 text-xs mt-3">{{ formTranslation.task.max_file_size || 'Maximum file size: 10MB' }}</p>
        </div>
      </div>
      
      <!-- Upload progress -->
      <div v-if="uploadingFile" class="mt-4 bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
        <div class="flex items-center mb-2">
          <!-- File icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <div class="file-info flex-grow">
            <div class="flex justify-between">
              <span class="file-name text-sm font-medium text-gray-700">{{ uploadingFile.name }}</span>
              <span class="file-size text-xs text-gray-500">{{ formatFileSize(uploadingFile.size) }}</span>
            </div>
          </div>
        </div>
        <!-- Progress bar -->
        <div class="w-full bg-gray-200 rounded-full h-2.5 mb-3">
          <div class="bg-black h-2.5 rounded-full" :style="{ width: uploadProgress + '%' }"></div>
        </div>
        <div class="flex justify-end">
          <button 
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            @click="cancelUpload"
          >
            {{ formTranslation.common.cancel || 'Cancel' }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- No attachments state -->
    <div v-if="!attachments.length && !uploadingFile" class="no-attachments text-center p-6 bg-gray-50 rounded-lg border border-gray-200">
      <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 mb-3 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <p class="text-gray-500">{{ formTranslation.task.no_attachments || 'No attachments yet' }}</p>
    </div>
    
    <!-- Attachments list -->
    <div v-else-if="attachments.length" class="attachments-list">
      <h3 class="text-sm font-medium text-gray-700 mb-3">{{ formTranslation.task.attachments || 'Attachments' }}</h3>
      <div class="space-y-2">
        <div 
          v-for="attachment in attachments" 
          :key="attachment.id" 
          class="attachment-item flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200"
        >
          <!-- File type icon -->
          <svg v-if="isImageFile(attachment.file_name)" xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-3 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
          </svg>
          <svg v-else-if="isPDFFile(attachment.file_name)" xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          
          <div class="file-details flex-grow">
            <div class="flex justify-between">
              <h6 class="text-sm font-medium text-gray-900 truncate max-w-xs" :title="attachment.file_name">{{ attachment.file_name }}</h6>
              <span class="file-date text-xs text-gray-500">{{ formatDate(attachment.uploaded_at) }}</span>
            </div>
            <div class="flex justify-between items-center">
              <small class="text-xs text-gray-500">
                {{ formTranslation.task.uploaded_by || 'Uploaded by' }}: {{ attachment.uploader_name }}
              </small>
              <div class="file-actions flex space-x-2">
                <!-- Download button -->
                <button 
                  class="text-xs text-blue-600 hover:text-blue-800" 
                  @click="downloadAttachment(attachment)"
                >
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                    <span>{{ formTranslation.common.download || 'Download' }}</span>
                  </div>
                </button>
                <!-- Delete button -->
                <button 
                  v-if="canDeleteAttachment(attachment)"
                  class="text-xs text-red-600 hover:text-red-800" 
                  @click="confirmDeleteAttachment(attachment)"
                >
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                    <span>{{ formTranslation.common.delete || 'Delete' }}</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from '../../config/request';

export default {
  name: 'TaskAttachments',
  props: {
    taskId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      attachments: [],
      isDragging: false,
      uploadingFile: null,
      uploadProgress: 0,
      attachmentToDelete: null
    }
  },
  computed: {
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
  },
  mounted() {
    this.fetchAttachments();
  },
  methods: {
    async fetchAttachments() {
      if (!this.taskId) return;
      
      try {
        this.loading = true;
        const response = await get("get_task_attachments", { task_id: this.taskId });
        
        if (response?.data?.status) {
          this.attachments = Array.isArray(response.data.data) ? response.data.data : [];
        } else {
          this.attachments = [];
          console.warn('API returned error status:', response?.data?.message);
        }
      } catch (error) {
        console.error('Error fetching attachments:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: error.message || formTranslation.task.attachments_load_failed || 'Failed to load attachments'
        });
        this.attachments = [];
      } finally {
        this.loading = false;
      }
    },
    
    handleFileDrop(event) {
      this.isDragging = false;
      if (event.dataTransfer.files.length > 0) {
        this.uploadFile(event.dataTransfer.files[0]);
      }
    },
    
    handleFileSelect(event) {
      if (event.target.files.length > 0) {
        this.uploadFile(event.target.files[0]);
      }
    },
    
    async uploadFile(file) {
      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.file_too_large || 'File is too large. Maximum size is 10MB.'
        });
        return;
      }
      
      this.uploadingFile = file;
      this.uploadProgress = 0;
      
      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('task_id', this.taskId);
      
      try {
        const response = await post(
          "upload_task_attachment",
          formData,
          {
            isFormData: true,
            onUploadProgress: (progressEvent) => {
              this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            }
          }
        );
        
        if (response?.data?.status) {
          await this.fetchAttachments();
          
          this.$swal.fire({
            icon: 'success',
            title: formTranslation.common.success || 'Success',
            text: formTranslation.task.file_uploaded_successfully || 'File uploaded successfully',
            showConfirmButton: false,
            timer: 1500
          });
          
          this.$emit('attachment-uploaded');
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response?.data?.message || formTranslation.task.file_upload_failed || 'Failed to upload file'
          });
        }
      } catch (error) {
        console.error('Error uploading file:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.file_upload_failed || 'Failed to upload file'
        });
      } finally {
        this.uploadingFile = null;
        this.uploadProgress = 0;
      }
    },
    
    cancelUpload() {
      if (this.uploadingFile) {
        this.uploadingFile = null;
        this.uploadProgress = 0;
        
        this.$swal.fire({
          icon: 'info',
          title: formTranslation.common.cancelled || 'Cancelled',
          text: formTranslation.task.upload_cancelled || 'File upload cancelled',
          showConfirmButton: false,
          timer: 1500
        });
      }
    },
    
    downloadAttachment(attachment) {
      const link = document.createElement('a');
      link.href = attachment.file_url;
      link.download = attachment.file_name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    
    confirmDeleteAttachment(attachment) {
      this.attachmentToDelete = attachment;
      
      this.$swal.fire({
        title: formTranslation.task.confirm_delete_attachment || 'Confirm Delete Attachment',
        text: `${formTranslation.task.delete_attachment_confirmation_text || 'Are you sure you want to delete this attachment? This action cannot be undone.'}\n${formTranslation.task.file || 'File'}: ${attachment.file_name}`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: formTranslation.common.delete || 'Delete',
        cancelButtonText: formTranslation.common.cancel || 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          this.executeDeleteAttachment();
        } else {
          this.attachmentToDelete = null;
        }
      });
    },
    
    async executeDeleteAttachment() {
      if (!this.attachmentToDelete) return;
      
      try {
        const response = await post("delete_task_attachment", {
          id: this.attachmentToDelete.id,
          task_id: this.taskId
        });
        
        if (response?.data?.status) {
          this.attachments = response.data.data || [];
          
          this.$swal.fire({
            icon: 'success',
            title: formTranslation.common.success || 'Success',
            text: formTranslation.task.attachment_deleted_successfully || 'Attachment deleted successfully',
            showConfirmButton: false,
            timer: 1500
          });
          
          this.$emit('attachment-deleted');
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response?.data?.message || formTranslation.task.attachment_delete_failed || 'Failed to delete attachment'
          });
        }
      } catch (error) {
        console.error('Error deleting attachment:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.attachment_delete_failed || 'Failed to delete attachment'
        });
      } finally {
        this.attachmentToDelete = null;
      }
    },
    
    canDeleteAttachment(attachment) {
      let isAdmin = ["clinic_admin", "administrator"].includes(this.getUserRole()) ? true : false;
      let isOwner = parseInt(attachment.uploaded_by) === parseInt(this.userData.ID);
      
      return isAdmin || isOwner;
    },
    
    formatDate(dateString) {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      // Format relative time for recent uploads
      if (diffMinutes < 1) {
        return formTranslation.common.just_now || 'Just now';
      } else if (diffMinutes < 60) {
        return formTranslation.common.minutes_ago?.replace('{count}', diffMinutes) || `${diffMinutes} minutes ago`;
      } else if (diffHours < 24) {
        return formTranslation.common.hours_ago?.replace('{count}', diffHours) || `${diffHours} hours ago`;
      } else if (diffDays < 7) {
        return formTranslation.common.days_ago?.replace('{count}', diffDays) || `${diffDays} days ago`;
      }
      
      // Format absolute date for older uploads
      return new Intl.DateTimeFormat(this.$i18n.locale || 'en', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }).format(date);
    },
    
    formatFileSize(bytes) {
      if (!bytes) return '0 Bytes';
      
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    isImageFile(fileName) {
      if (!fileName) return false;
      const extension = fileName.split('.').pop().toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension);
    },
    
    isPDFFile(fileName) {
      if (!fileName) return false;
      const extension = fileName.split('.').pop().toLowerCase();
      return extension === 'pdf';
    }
  }
}
</script>

<style scoped>
.task-attachments-container {
  max-height: 600px;
  overflow-y: auto;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>