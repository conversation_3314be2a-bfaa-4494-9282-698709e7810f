<?php

use App\models\KCPatientEncounter;

$input_html= str_replace("\n", "<br>",  preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>',rgbToHex( $request_data['plainText'])));
function rgbToHex($input) {
    // Regular expression to match rgb values like rgb(161, 0, 0)
    $pattern = '/rgb\((\d+),\s*(\d+),\s*(\d+)\)/';


    // Callback function to convert RGB to Hex
    $callback = function($matches) {
        $r = (int) $matches[1];
        $g = (int) $matches[2];
        $b = (int) $matches[3];
        // Convert RGB to Hex and return the result
        return sprintf('#%02x%02x%02x', $r, $g, $b);
    };
    // Perform the replacement using preg_replace_callback
    return preg_replace_callback($pattern, $callback,stripslashes( $input));
}
$encounter_detail = (new KCPatientEncounter())->get_by(['id' => (int) $request_data['encounter_id']], '=', true);

$imageHeaderPath = $header_template_id? get_attached_file($header_template_id):KIVI_CARE_DIR.'assets/images/template-header.png';
$headerImage = file_get_contents($imageHeaderPath);
$Headerbase64Image = base64_encode($headerImage);
$HeaderimageInfo = getimagesize($imageHeaderPath);
$HeaderimageType = $HeaderimageInfo['mime'];
$HeaderdataUri = 'data:' . $HeaderimageType . ';base64,' . $Headerbase64Image;


$imageFooterPath = $footer_template_id? get_attached_file($footer_template_id):KIVI_CARE_DIR.'assets/images/template-footer.png';
$FooterImage = file_get_contents($imageFooterPath);
$Footerbase64Image = base64_encode($FooterImage);
$FooterimageInfo = getimagesize($imageFooterPath);
$FooterimageType = $FooterimageInfo['mime'];
$FooterdataUri = 'data:' . $FooterimageType . ';base64,' . $Footerbase64Image;



$docSigndataUri='';
if($imagedocSignPath = get_attached_file(get_user_meta($encounter_detail->doctor_id ,'doctor_signature',true))){
    $docSignImage = file_get_contents($imagedocSignPath);
    $docSignbase64Image = base64_encode($docSignImage);
    $docSignimageInfo = getimagesize($imagedocSignPath);
    $docSignimageType = $docSignimageInfo['mime'];
    $docSigndataUri = 'data:' . $docSignimageType . ';base64,' . $docSignbase64Image;

}
$doctor_data= json_decode(get_user_meta($encounter_detail->doctor_id,'basic_data',true),true);

$clinic_detail = kcClinicDetail($encounter_detail->clinic_id);

$doctor= get_user_by( 'ID', $encounter_detail->doctor_id );

ob_start(); ?>
<html>
    <head>
        <style>
            main {
                margin: 0 auto;
                padding: 0 60pt ;
                width: 100%;
                font-family: Arial, sans-serif;
            }

        </style>
    </head>
    <body>
        <!-- Define header and footer blocks before your content -->
        <header>
            <img src="<?php echo $HeaderdataUri ?>" alt="">
        </header>

        <footer>
            <img src="<?php echo $FooterdataUri ?>" alt="">
            </footer>
            
            <!-- Wrap the content of your PDF inside a main tag -->
            <main>
                <h2 style="display: inline;"><?php echo $request_data['templateName']?></h2>
                <h4 style="float: right;display: inline;margin-top: 6px;" ><?php echo wp_date('Y-m-d')?></h4>
                <br>
                <br>
                <?php echo $input_html ?>
                <p>
                    <?php echo $doctor->display_name.', '.$clinic_detail->name;?>
                </p>
                <p>
                    <?php 
                    // Use registration_prefix if available, otherwise default to Set Prefix
                    $prefix = isset($doctor_data['registration_prefix']) && !empty($doctor_data['registration_prefix']) ? strtoupper($doctor_data['registration_prefix']) : 'SET PREFIX';
                    echo $prefix . ' Number: ' . $doctor_data['gmc_no'];
                    ?>
                </p>
                <?php
                if(!empty($docSigndataUri)):
                ?>
                <img style="width: 40%;height: 100px;" src="<?php echo $docSigndataUri ?>" alt="">
                <?php endif; 
                foreach ($doctor_data['qualifications'] as $key => $value) {
                    printf("<p>%s-%s-%s<p/>",$value['degree'],$value['university'],$value['year']);
                }
                	
                ?>
              
        </main>
    </body>
</html>
<?php return ob_get_clean() ?>