<template>
  <div class="">
    <div class="flex h-[calc(100vh-200px)]">
      <!-- Prescription List -->
      <div class="w-2/5 border-r p-4">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>

        <div v-else-if="groupedPrescriptions.length" class="space-y-3">
          <div v-for="prescription in groupedPrescriptions" :key="prescription.encounter_id"
            class="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer" :class="{
              'bg-gray-50': selectedEncounterId === prescription.encounter_id,
            }" @click="selectEncounter(prescription.encounter_id)">
            <h3 class="font-medium">
              Prescription #{{ prescription.encounter_id }}
            </h3>
            <div class="text-sm text-gray-500 mt-1">
              {{ formatDate(prescription.items[0].created_at) }}
            </div>
            <div class="text-sm text-gray-600 mt-2">
              {{ prescription.items.length }} medicines prescribed
            </div>
          </div>
        </div>

        <!-- Empty Details State -->
        <div v-else-if="!loading && !groupedPrescriptions.length" class="w-3/5 p-6 flex items-center justify-center">
          <div class="text-gray-500">Select a prescription to view details</div>
        </div>
      </div>

      <!-- Prescription Details -->
      <div class="w-3/5 p-6" v-if="selectedPrescription">
        <div class="flex justify-between items-center mb-6">
          <div>
            <h2 class="text-xl font-semibold">
              Prescription #{{ selectedPrescription.encounter_id }}
            </h2>
            <p class="text-sm text-gray-500">
              {{ formatDate(selectedPrescription.items[0].created_at) }}
            </p>
          </div>
          <div class="flex gap-2">
            <button @click="printPrescription(selectedPrescription.encounter_id)"
              class="p-2 text-gray-500 hover:bg-gray-100 rounded-lg" title="Print">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-printer w-5 h-5">
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                <path d="M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6"></path>
                <rect x="6" y="14" width="12" height="8" rx="1"></rect>
              </svg>
            </button>
          </div>
        </div>

        <div class="space-y-4">
          <div v-for="medicine in selectedPrescription.items" :key="medicine.id" class="p-4 border rounded-lg">
            <div class="flex justify-between items-start">
              <h3 class="font-medium text-lg">{{ medicine.name.label }}</h3>
              <span class="text-sm text-gray-500">{{
                formatDate(medicine.created_at)
              }}</span>
            </div>
            <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
              <div v-if="medicine.dose">
                <span class="text-gray-500">Dose:</span>
                <span class="ml-2">{{ medicine.dose }}</span>
              </div>
              <div v-if="medicine.route">
                <span class="text-gray-500">Route:</span>
                <span class="ml-2">{{ medicine.route }}</span>
              </div>
              <div v-if="medicine.frequency">
                <span class="text-gray-500">Frequency:</span>
                <span class="ml-2">{{ medicine.frequency }}</span>
              </div>
              <div v-if="medicine.duration">
                <span class="text-gray-500">Duration:</span>
                <span class="ml-2">{{ medicine.duration }} days</span>
              </div>
            </div>
            <div v-if="medicine.instruction" class="mt-2 text-sm text-gray-600">
              <span class="text-gray-500">Instructions:</span>
              <span class="ml-2">{{ medicine.instruction }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";
import { displayErrorMessage } from "../../utils/message";
import { formatDate } from "../../utils/helper";

export default {
  name: "PrescriptionsTab",
  props: {
    patientId: {
      type: [String, Number],
    },
  },

  data() {
    return {
      loading: false,
      prescriptions: [],
      selectedEncounterId: null,
    };
  },

  computed: {
    groupedPrescriptions() {
      return Object.values(
        this.prescriptions.reduce((acc, prescription) => {
          if (!acc[prescription.encounter_id]) {
            acc[prescription.encounter_id] = {
              encounter_id: prescription.encounter_id,
              items: [],
            };
          }
          acc[prescription.encounter_id].items.push(prescription);
          return acc;
        }, {})
      );
    },
    selectedPrescription() {
      return this.groupedPrescriptions.find(
        (p) => p.encounter_id === this.selectedEncounterId
      );
    },
  },

  methods: {
    formatDate,
    login_id() {
      return this.$store.state.userDataModule.user.ID;
    },
    async getPrescriptionTabDetails() {
      this.loading = true;
      this.$emit("loading", true);

      try {
        const response = await get("get_prescription_list_by_patient_id", {
          login_id: this.login_id(),
          patient_id: this.patientId,
        });

        if (response.data.status) {
          this.prescriptions = response.data.data;
          if (this.prescriptions.length > 0) {
            this.selectedEncounterId = this.prescriptions[0].encounter_id;
          }
        }

        this.loading = false;
        this.$emit("loading", false);
      } catch (error) {
        console.error("Error fetching prescriptions:", error);
        displayErrorMessage("Internal server error");
      } finally {
        this.loading = false;
        this.$emit("loading", false);
      }
    },
    selectEncounter(encounterId) {
      this.selectedEncounterId = encounterId;
    },
    printPrescription(encounterId) {
      let currentUrl = window.location.href.split('#')[0];
      window.open(`${currentUrl}#/appointment/prescription/${encounterId}`, '_blank');
    }
  },

  watch: {
    patientId: {
      immediate: true,
      handler() {
        this.getPrescriptionTabDetails();
      },
    },
  },
};
</script>
