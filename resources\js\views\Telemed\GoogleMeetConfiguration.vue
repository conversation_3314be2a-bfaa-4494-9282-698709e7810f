<template>
  <div>
    <div class="relative" v-if="userData.addOns.googlemeet != true">
      <div class="absolute inset-0 bg-white bg-opacity-50 z-10">
        <overlay-message addon_type="googlemeet"></overlay-message>
      </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md">
      <div class="p-4">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-semibold">
            {{ formTranslation.googlemeet.googlemeet }}
            <a v-if="request_status == 'off'" 
               href="https://apps.medroid.ai/docs/product/kivicare/google-meet-telemed-woocommerce-addon/admin/" 
               target="_blank"
               class="ml-2 text-gray-500 hover:text-gray-700">
              <i class="fa fa-question-circle"></i>
            </a>
          </h2>
        </div>
      </div>
      
      <hr class="border-gray-200" />
      
      <div class="p-4">
        <div class="grid md:grid-cols-12 gap-6">
          <div class="md:col-span-5">
            <form
              id="googleCalform"
              name="googleCalform"
              @submit.prevent="handleCalenderSubmit"
              :novalidate="true"
              enctype="multipart/form-data"
            >
              <div class="space-y-4">
                <div class="pl-4">
                  <label class="inline-flex items-center">
                    <input
                      type="checkbox"
                      class="form-checkbox h-5 w-5 text-blue-600"
                      v-model="googleCalData.enableCal"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @change="handleCalenderSubmit"
                    />
                    <span class="ml-2 font-semibold">
                      {{formTranslation.googlemeet.google_meet_configuration}}
                    </span>
                  </label>
                </div>

                <div v-if="googleCalData.enableCal == 'Yes'" class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">
                      {{formTranslation.googlemeet.google_meet_client_id}}
                    </label>
                    <input
                      type="text"
                      class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      v-model="googleCalData.client_id"
                    />
                    <p v-if="submitted && !$v.googleCalData.client_id.required" 
                       class="mt-1 text-sm text-red-600">
                      {{formTranslation.googlemeet.google_meet_client_id_required}}
                    </p>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">
                      {{formTranslation.googlemeet.google_meet_client_secret}}
                    </label>
                    <input
                      type="text"
                      class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      v-model="googleCalData.client_secret"
                    />
                    <p v-if="submitted && !$v.googleCalData.client_secret.required" 
                       class="mt-1 text-sm text-red-600">
                      {{formTranslation.googlemeet.google_meet_client_secret_required}}
                    </p>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">
                      {{formTranslation.common.app_name}}
                    </label>
                    <input
                      type="text"
                      class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      v-model="googleCalData.app_name"
                    />
                    <p v-if="submitted && !$v.googleCalData.app_name.required" 
                       class="mt-1 text-sm text-red-600">
                      {{formTranslation.common.app_name_required}}
                    </p>
                  </div>

                  <div class="flex justify-end">
                    <button 
                      type="submit"
                      class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <i class="fa fa-save mr-2"></i>
                      {{ formTranslation.common.save }}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>

          <div class="md:col-span-7" v-if="googleCalData.enableCal == 'Yes'">
            <div class="space-y-4">
              <h4 class="text-xl font-semibold text-blue-600">
                {{formTranslation.googlemeet.guide_to_setup_google_meet}}
              </h4>
              
              <div class="space-y-2 border rounded-lg divide-y">
                <div class="p-4">
                  <span class="font-semibold">Step 1:</span>
                  <a 
                    href="https://apps.medroid.ai/docs/product/kivicare/google-calendar/" 
                    target="_blank"
                    class="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    {{ formTranslation.pro_setting.please_refer_link}}
                  </a>
                </div>
                
                <div class="p-4">
                  <p>
                    <span class="font-semibold">Note:</span>
                    If you have already setup Google calendar then You can use same ClientID and Client Secret here.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="formLoader" class="fixed inset-0 flex items-center justify-center bg-white bg-opacity-75">
      <loader-component-2></loader-component-2>
    </div>

    <form v-else id="googleMeetForm" :novalidate="true" class="mt-6">
      <div class="bg-white rounded-lg shadow-md">
        <div class="p-4 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-2xl font-semibold">
              {{ formTranslation.googlemeet.google_event_template }}
            </h2>
          </div>
        </div>

        <div class="p-4 space-y-6">
          <div v-for="(item, index) in googleEventList" :key="index">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">
                  {{formTranslation.google_event.google_event_title}}
                </label>
                <input
                  type="text"
                  class="w-full rounded-md border-gray-300 px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  v-model="item.post_title"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">
                  {{formTranslation.google_event.google_event_desc}}
                </label>
                <vue-editor 
                  :editorToolbar="customToolbar" 
                  v-model="item.post_content"
                  class="mt-1"
                ></vue-editor>
              </div>
            </div>
          </div>

          <div class="flex justify-end">
            <button 
              v-if="!loading"
              @click="saveGoogleEventTemplate"
              type="button"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <i class="fa fa-save mr-2"></i>
              {{ formTranslation.common.save }}
            </button>
            <button 
              v-else
              disabled
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 opacity-75 cursor-not-allowed"
            >
              <i class="fa fa-sync fa-spin mr-2"></i>
              {{formTranslation.common.loading}}
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import { post,get } from "../../config/request";
import {maxValue, minValue, required} from "vuelidate/lib/validators";
import {alphaSpace, validateForm} from "../../config/helper";
export default {
  name: "generalSetting",
  components: {
  },
  data: () => {
    return {
      googleCalData: {},
      loading: false,
      googleEventList:[],
      eventTitle: '',
      request_status:'off',
      templateSaveRequest: {
        ID: 0,
        post_content: '',
      },
      formLoader:true.valueOf,
      submitted: false,
      customToolbar: [[{
        header: [false, 1, 2, 3, 4, 5, 6]
      }], ["bold", "italic", "underline", "strike"], // toggled buttons
        [{
          align: ""
        }, {
          align: "center"
        }, {
          align: "right"
        }, {
          align: "justify"
        }], ["blockquote", "code-block"], [{
          list: "ordered"
        }, {
          list: "bullet"
        }, {
          list: "check"
        }], [{
          indent: "-1"
        }, {
          indent: "+1"
        }], // outdent/indent
        [{
          color: []
        }, {
          background: []
        }], // dropdown with defaults from theme
      ]
    };
  },
  mounted() {
    if(!['administrator'].includes(this.getUserRole())) {
      this.$router.push({ name: "403"})
    }
    this.googleCalData = this.defaultGoogleData();
    this.getGoogleEventTemplate();
    this.getModule();
  },
  validations: {
    googleCalData: {
        client_id: { required },
        client_secret: { required },
        app_name:{required}
    }
  },
  methods: {
    handleCalenderSubmit() {
      if(this.userData.addOns.googlemeet != true){
        return
      }

      this.submitted = true;
      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.googleCalData.$invalid) {
        // this.submitted = false;
        return;
      }
      var element =$('#btn-google-submit').find("i");
      element.removeClass('fa fa-save ')
      element.addClass("fa fa-spinner fa-spin");

      post("google_meet_config",this.googleCalData)
          .then((response) => {
                this.submitted = false;
                element.removeClass("fa fa-spinner fa-spin");
                element.addClass("fa fa-save");
            if (
                response.data.status !== undefined &&
                response.data.status === true
            ) {
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.common.internal_server_error);
          });
    },
    defaultGoogleData() {
      return {
        client_id: "",
        client_secret: "",
        app_name: "",
        enableCal: false,
      };
    },
    getGoogleEventTemplate: function () {
      this.formLoader = true;
      get('get_google_meet_event_template_and_config', {})
          .then((response) => {
            if (response.data.status !== undefined && response.data.status === true) {
              this.googleEventList = response.data.data
            }
            if(response.data.config !== undefined && response.data.config.status !== undefined && response.data.config.status === true){
              this.googleCalData = response.data.config.data;
            }
            this.formLoader = false;
          })
          .catch((error) => {
            this.formLoader = false;
            console.log(error);
          })
    },
    saveGoogleEventTemplate: function () {
      if(this.userData.addOns.googlemeet != true){
        return
      }
      this.loading = true;
      post('save_google_meet_event_template', { data : this.googleEventList } )
          .then((response) => {
            if (response.data.status !== undefined && response.data.status === true) {
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.loading = false;
            displayErrorMessage(this.formTranslation.common.server_error);
          })
    },
    getModule:function (){
        if(window.request_data.link_show_hide !== undefined && window.request_data.link_show_hide !== ''){
        this.request_status = window.request_data.link_show_hide;
        }
    }
  },
  computed:{
    userData() {
      return this.$store.state.userDataModule.user;
    },
  }
};
</script>

<style scoped>

</style>