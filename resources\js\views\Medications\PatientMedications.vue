<template>
  <div>
    <div class="p-6 max-w-7xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Prescriptions</h1>
        <!-- <button
          class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-refresh-ccw w-4 h-4 mr-2"
          >
            <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
            <path d="M3 3v5h5"></path>
            <path
              d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"
            ></path>
            <path d="M16 16h5v5"></path></svg
          >Request Refill
        </button> -->
      </div>
      <!-- <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-card text-card-foreground rounded-xl border shadow">
          <div class="p-6 pt-0 p-4">
            <p class="text-gray-600 text-sm">Active Prescriptions</p>
            <p class="text-2xl font-bold text-purple-600">3</p>
          </div>
        </div>
        <div class="bg-card text-card-foreground rounded-xl border shadow">
          <div class="p-6 pt-0 p-4">
            <p class="text-gray-600 text-sm">Pending Refills</p>
            <p class="text-2xl font-bold text-purple-600">1</p>
          </div>
        </div>
        <div class="bg-card text-card-foreground rounded-xl border shadow">
          <div class="p-6 pt-0 p-4">
            <p class="text-gray-600 text-sm">Expiring Soon</p>
            <p class="text-2xl font-bold text-purple-600">2</p>
          </div>
        </div>
      </div> -->

      <div
        role="alert"
        class="relative w-full rounded-lg border px-4 py-3 text-sm [&amp;>svg+div]:translate-y-[-3px] [&amp;>svg]:absolute [&amp;>svg]:left-4 [&amp;>svg]:top-4 [&amp;>svg]:text-foreground [&amp;>svg~*]:pl-7 bg-background text-foreground mb-6 bg-purple-50 border-purple-100"
      >
        <div class="absolute -top-3 right-6">
          <span
            class="bg-purple-100 text-purple-600 text-xs font-medium px-5 py-1 rounded-full"
          >
            Coming Soon
          </span>
        </div>
        <div class="flex items-start space-x-4">
          <div
            class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-circle-alert w-4 h-4 text-purple-600"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" x2="12" y1="8" y2="12"></line>
              <line x1="12" x2="12.01" y1="16" y2="16"></line>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-purple-900">Medication Alert</h3>
            <div class="text-sm [&amp;_p]:leading-relaxed mt-1 text-purple-800">
              Your Lisinopril prescription will need a refill in 5 days. Would
              you like me to submit a refill request to your pharmacy now?
            </div>
            <div class="mt-3 flex space-x-4">
              <button
                class="text-sm px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Request Refill</button
              ><button
                class="text-sm px-3 py-1 text-purple-600 hover:bg-purple-100 rounded-md"
              >
                Remind Later
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Filter buttons -->
      <!-- <div class="flex space-x-2 mb-6">
        <button class="px-4 py-2 rounded-lg text-sm bg-purple-600 text-white">
          Active</button
        ><button
          class="px-4 py-2 rounded-lg text-sm bg-gray-100 text-gray-600 hover:bg-gray-200"
        >
          Pending</button
        ><button
          class="px-4 py-2 rounded-lg text-sm bg-gray-100 text-gray-600 hover:bg-gray-200"
        >
          Expired
        </button>
      </div> -->

      <div
        v-if="!Object.keys(groupedPrescriptions).length"
        class="bg-white rounded-xl text-center py-8 text-gray-500"
      >
        No prescriptions
      </div>
      <div v-else class="space-y-4">
        <DashboardPrescription
          v-for="(prescriptionGroup, encounterId) in groupedPrescriptions"
          :key="encounterId"
          :prescription-group="prescriptionGroup"
        />
        
      </div>
    </div>
  </div>
</template>

<script>
import { get, post } from "../../config/request";
import DashboardPrescription from "../../components/Dashboard/DashboardPrescription";

export default {
  components: {DashboardPrescription},
  data: () => {
    return {
      isLoading: false,
      patientPrescriptionList: [],
    };
  },
  mounted() {
    this.init();
  },
  computed: {
    groupedPrescriptions() {
      return this.patientPrescriptionList.reduce((acc, prescription) => {
        const encounterKey = prescription.encounter_id;
        if (!acc[encounterKey]) {
          acc[encounterKey] = [];
        }
        acc[encounterKey].push(prescription);
        return acc;
      }, {});
    }
  },
  methods: {
    init: function () {
      this.fetchPrescriptions();
    },
    login_id() {
      return this.$store.state.userDataModule.user.ID;
    },
    async fetchPrescriptions() {
      try {
        this.loading = true;
        const response = await get("get_prescription_list_by_patient_id", {
          login_id: this.login_id(),
          patient_id: this.login_id(),
        });

        if (response.data.status) {
          this.patientPrescriptionList = response.data.data;
        } else {
          this.error = response.data.message || "Failed to fetch prescriptions";
        }
      } catch (error) {
        console.error("Error fetching prescriptions:", error);
        this.error = "An error occurred while fetching prescriptions";
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
