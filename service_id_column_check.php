<?php
// Path: service_id_column_check.php

// Include WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Check if user is logged in and has admin privileges
if (!current_user_can('administrator')) {
    wp_die('You do not have permission to access this page.');
}

global $wpdb;
$table_name = $wpdb->prefix . 'kc_clinic_sessions';

// Check if the table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

echo '<h1>Service Session Database Structure Check</h1>';

if ($table_exists) {
    echo '<p>Table exists: ' . $table_name . '</p>';
    
    // Check if column exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'service_id'");
    
    if (!empty($column_exists)) {
        echo '<p>Column exists: service_id</p>';
        echo '<pre>';
        print_r($column_exists);
        echo '</pre>';
    } else {
        echo '<p>Column does not exist. Adding service_id column...</p>';
        $result = $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN `service_id` bigint(20) DEFAULT NULL AFTER `doctor_id`");
        
        if ($result !== false) {
            echo '<p>Column added successfully.</p>';
        } else {
            echo '<p>Error adding column: ' . $wpdb->last_error . '</p>';
        }
        
        $column_added = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'service_id'");
        echo '<pre>';
        print_r($column_added);
        echo '</pre>';
    }
    
    // Show a sample of the data with service_id
    echo '<h2>Sessions with service_id</h2>';
    $service_sessions = $wpdb->get_results("SELECT * FROM {$table_name} WHERE service_id IS NOT NULL AND service_id > 0 LIMIT 10");
    
    if (!empty($service_sessions)) {
        echo '<p>Found ' . count($service_sessions) . ' service-specific sessions:</p>';
        echo '<pre>';
        print_r($service_sessions);
        echo '</pre>';
    } else {
        echo '<p>No service-specific sessions found in the database.</p>';
    }
} else {
    echo '<p>Table does not exist: ' . $table_name . '</p>';
}

echo '<p>This page checks the database structure and ensures the service_id column exists.</p>';
echo '<p>After verifying the column is present, please try creating service-specific sessions again.</p>';