<?php
/**
 * Template: Mobile Audio Upload
 * This is a standalone page template for KiviCare Mobile Audio Upload
 */

// Make sure WordPress is loaded
if (!defined('ABSPATH')) {
    require_once($_SERVER['DOCUMENT_ROOT'] . '/wp-load.php');
}

// Set content type
header('Content-Type: text/html; charset=UTF-8');

// Check if session ID is provided
$session_id = isset($_GET['session_id']) ? sanitize_text_field($_GET['session_id']) : null;
$error_message = '';
$success_message = '';

// Validate the session ID
if (!$session_id) {
    $error_message = 'Invalid or missing session ID. Please scan the QR code again.';
}

// Get session data from transient
$session_data = $session_id ? get_transient('kc_mobile_audio_' . $session_id) : null;

// Check if session exists and is valid
if (!$session_data) {
    $error_message = 'Recording session not found or expired. Please request a new QR code.';
}

// Make sure patient_id is set to avoid null reference errors
if ($session_data && !isset($session_data['patient_id'])) {
    error_log('Mobile audio record: No patient_id in session, setting default');
    $session_data['patient_id'] = 0;
}

// Handle audio upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['document']) && !$error_message) {
    // Check if the session is still valid
    if (time() > $session_data['expires_at']) {
        $error_message = 'Recording session has expired. Please request a new QR code.';
    } else {
        try {
            // Get file data
            $file = $_FILES['document'];
            
            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File upload error: ' . $file['error']);
            }
            
            // Prepare variables
            $encounter_id = intval($session_data['encounter_id']);
            $patient_id = intval($session_data['patient_id']);
            $doc_name = isset($_POST['document_name']) ? sanitize_text_field($_POST['document_name']) : 'Audio Recording ' . date('Y-m-d H:i:s');
            $doc_type = isset($_POST['document_type']) ? sanitize_text_field($_POST['document_type']) : 'medical_audio';
            
            // Upload to WordPress media library
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            
            $file_id = media_handle_upload('document', 0);
            
            if (is_wp_error($file_id)) {
                throw new Exception($file_id->get_error_message());
            }
            
            // Save to database
            global $wpdb;
            
            $insert_result = $wpdb->insert($wpdb->prefix . 'kc_patient_document', [
                'name' => $doc_name,
                'type' => $doc_type,
                'description' => 'Audio recording from mobile device',
                'patient_id' => $patient_id,
                'document_id' => $file_id,
                'appointment_id' => $encounter_id,
                'created_by' => 1,
                'created_at' => current_time('mysql')
            ]);
            
            if (!$insert_result) {
                throw new Exception('Error saving to database');
            }
            
            // Update session data
            $session_data['processed'] = true;
            $session_data['processed_at'] = time();
            set_transient('kc_mobile_audio_' . $session_id, $session_data, $session_data['expires_at'] - time());
            
            $success_message = 'Audio uploaded successfully! You may now close this page.';
            
            // Log activity
            kcLogActivity(
                'mobile_audio_uploaded',
                sprintf(esc_html__('Audio uploaded via mobile for encounter #%d', 'kc-lang'), $encounter_id),
                [
                    'patient_id' => isset($session_data['patient_id']) ? $session_data['patient_id'] : 0,
                    'resource_id' => $encounter_id,
                    'resource_type' => 'encounter'
                ]
            );
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
            error_log('Mobile audio upload error: ' . $error_message);
        }
    }
}

// Handle base64 audio data
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['audio_data']) && !$error_message) {
    // Check if the session is still valid
    if (time() > $session_data['expires_at']) {
        $error_message = 'Recording session has expired. Please request a new QR code.';
    } else {
        try {
            // Get audio data and clean it
            $audio_data = $_POST['audio_data'];
            
            // Remove the data URL prefix
            $audio_data = str_replace('data:audio/webm;base64,', '', $audio_data);
            $audio_data = str_replace('data:audio/mp4;base64,', '', $audio_data);
            $audio_data = str_replace('data:audio/wav;base64,', '', $audio_data);
            $audio_data = str_replace('data:audio/ogg;base64,', '', $audio_data);
            $audio_data = str_replace(' ', '+', $audio_data);
            
            // Decode base64 data
            $decoded_audio = base64_decode($audio_data);
            
            if ($decoded_audio === false) {
                throw new Exception('Error decoding audio data.');
            }
            
            // Prepare variables
            $encounter_id = intval($session_data['encounter_id']);
            $patient_id = intval($session_data['patient_id']);
            $doc_name = isset($_POST['document_name']) ? sanitize_text_field($_POST['document_name']) : 'Audio Recording ' . date('Y-m-d H:i:s');
            $doc_type = isset($_POST['document_type']) ? sanitize_text_field($_POST['document_type']) : 'medical_audio';
            
            // Create a temporary file
            $upload_dir = wp_upload_dir();
            $filename = 'audio_recording_' . time() . '.wav';
            $file_path = $upload_dir['path'] . '/' . $filename;
            
            // Save the file
            if (file_put_contents($file_path, $decoded_audio) === false) {
                throw new Exception('Error saving audio data.');
            }
            
            // Create an attachment
            $attachment = [
                'guid' => $upload_dir['url'] . '/' . $filename,
                'post_mime_type' => 'audio/wav',
                'post_title' => $doc_name,
                'post_content' => '',
                'post_status' => 'inherit'
            ];
            
            // Insert into media library
            $file_id = wp_insert_attachment($attachment, $file_path);
            
            if (is_wp_error($file_id)) {
                throw new Exception($file_id->get_error_message());
            }
            
            // Save to database
            global $wpdb;
            
            $insert_result = $wpdb->insert($wpdb->prefix . 'kc_patient_document', [
                'name' => $doc_name,
                'type' => $doc_type,
                'description' => 'Audio recording from mobile device',
                'patient_id' => $patient_id,
                'document_id' => $file_id,
                'appointment_id' => $encounter_id,
                'created_by' => 1,
                'created_at' => current_time('mysql')
            ]);
            
            if (!$insert_result) {
                throw new Exception('Error saving to database');
            }
            
            // Update session data
            $session_data['processed'] = true;
            $session_data['processed_at'] = time();
            set_transient('kc_mobile_audio_' . $session_id, $session_data, $session_data['expires_at'] - time());
            
            $success_message = 'Audio recorded and uploaded successfully! You may now close this page.';
            
            // Log activity
            kcLogActivity(
                'mobile_audio_uploaded',
                sprintf(esc_html__('Audio uploaded via mobile for encounter #%d', 'kc-lang'), $encounter_id),
                [
                    'patient_id' => isset($session_data['patient_id']) ? $session_data['patient_id'] : 0,
                    'resource_id' => $encounter_id,
                    'resource_type' => 'encounter'
                ]
            );
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
            error_log('Mobile audio upload error: ' . $error_message);
        }
    }
}

// Output HTML directly, don't use WordPress theme
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>AI Scribe - Voice Recording</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.5;
            padding: 0;
            margin: 0;
        }
        .kc-mobile-upload-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .kc-mobile-upload-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .kc-mobile-upload-header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .kc-mobile-upload-header p {
            color: #666;
            margin: 0;
        }
        .kc-upload-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .kc-upload-tab {
            flex: 1;
            text-align: center;
            padding: 12px;
            cursor: pointer;
            color: #666;
            transition: all 0.3s;
        }
        .kc-upload-tab.active {
            color: #000;
            border-bottom: 2px solid #000;
            font-weight: 500;
        }
        .kc-upload-content {
            display: none;
        }
        .kc-upload-content.active {
            display: block;
        }
        .kc-form-group {
            margin-bottom: 20px;
        }
        .kc-form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .kc-form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .kc-form-control:focus {
            border-color: #000;
            outline: none;
        }
        .kc-btn {
            display: inline-block;
            background-color: #000;
            color: #fff;
            border: none;
            border-radius: 5px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            text-align: center;
        }
        .kc-btn:hover {
            background-color: #333;
        }
        .kc-btn-outline {
            background-color: transparent;
            color: #000;
            border: 1px solid #000;
        }
        .kc-btn-outline:hover {
            background-color: #f5f5f5;
        }
        .kc-btn-primary {
            background-color: #4299e1;
        }
        .kc-btn-primary:hover {
            background-color: #3182ce;
        }
        .kc-btn-danger {
            background-color: #e53e3e;
        }
        .kc-btn-danger:hover {
            background-color: #c53030;
        }
        .kc-audio-container {
            position: relative;
            overflow: hidden;
            width: 100%;
            border-radius: 8px;
            background-color: #f5f5f5;
            margin-bottom: 20px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .kc-audio-controls {
            width: 100%;
            padding: 10px;
            text-align: center;
        }
        .kc-audio-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }
        .kc-alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .kc-alert-error {
            background-color: #fff5f5;
            color: #e53e3e;
            border: 1px solid #feb2b2;
        }
        .kc-alert-success {
            background-color: #f0fff4;
            color: #38a169;
            border: 1px solid #c6f6d5;
        }
        .kc-file-input {
            padding: 30px;
            border: l2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .kc-file-input:hover {
            border-color: #000;
        }
        .kc-file-input input {
            display: none;
        }
        .kc-file-input-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #666;
        }
        .kc-file-input-text {
            color: #666;
        }
        .kc-footer {
            text-align: center;
            margin-top: 30px;
            font-size: 14px;
            color: #666;
        }
        .kc-session-info {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
        .recording-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #e53e3e;
            margin-right: 8px;
            display: inline-block;
            animation: pulse 1.5s infinite;
        }
        .recording-time {
            font-size: 36px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
        }
        .recording-status {
            text-align: center;
            margin-bottom: 10px;
            font-weight: 500;
        }
        .visualization-container {
            width: 100%;
            height: 60px;
            margin-bottom: 20px;
            position: relative;
        }
        .visualization-canvas {
            width: 100%;
            height: 100%;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        @media (max-width: 767px) {
            .kc-mobile-upload-container {
                margin: 0;
                width: 100%;
                min-height: 100vh;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
<div class="kc-mobile-upload-container">
    <div class="kc-mobile-upload-header">
        <h1>AI Scribe Voice Recording</h1>
        <p>Use your mobile device to record and upload audio for the consultation</p>
    </div>
    
    <?php if ($error_message): ?>
        <div class="kc-alert kc-alert-error">
            <?php echo esc_html($error_message); ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success_message): ?>
        <div class="kc-alert kc-alert-success">
            <?php echo esc_html($success_message); ?>
        </div>
    <?php endif; ?>
    
    <?php if (!$error_message && $session_data && empty($success_message)): ?>
        <div class="kc-upload-tabs">
            <div class="kc-upload-tab active" data-tab="record">Record Audio</div>
            <div class="kc-upload-tab" data-tab="upload">Upload File</div>
        </div>
        
        <!-- Record Audio Tab -->
        <div class="kc-upload-content active" id="record-tab">
            <div class="kc-audio-container">
                <div id="recording-status" class="recording-status">Ready to record</div>
                <div id="recording-time" class="recording-time">00:00</div>
                <div class="visualization-container">
                    <canvas id="visualization" class="visualization-canvas"></canvas>
                </div>
            </div>
            
            <div class="kc-audio-actions">
                <button id="start-recording" class="kc-btn kc-btn-primary" style="width: 100%;">Start Recording</button>
                <button id="pause-recording" class="kc-btn kc-btn-outline" style="display: none; width: 48%;">Pause</button>
                <button id="resume-recording" class="kc-btn kc-btn-outline" style="display: none; width: 48%;">Resume</button>
                <button id="stop-recording" class="kc-btn kc-btn-danger" style="display: none; width: 100%;">Stop Recording</button>
            </div>
            
            <div class="kc-audio-controls" style="display: none;" id="audio-preview-container">
                <audio id="audio-preview" controls style="width: 100%;"></audio>
                
                <div class="kc-audio-actions" style="margin-top: 20px;">
                    <button id="discard-recording" class="kc-btn kc-btn-outline" style="width: 48%;">Discard</button>
                    <button id="submit-recording" class="kc-btn" style="width: 48%;">Upload</button>
                </div>
            </div>
            
            <form id="audio-form" method="post" enctype="multipart/form-data" style="display: none;">
                <input type="hidden" name="audio_data" id="audio-data">
                <input type="hidden" name="document_name" value="Audio Recording <?php echo date('Y-m-d H:i:s'); ?>">
                <input type="hidden" name="document_type" value="medical_audio">
            </form>
        </div>
        
        <!-- Upload Audio Tab -->
        <div class="kc-upload-content" id="upload-tab">
            <form method="post" enctype="multipart/form-data">
                <div class="kc-file-input" onclick="document.getElementById('file-input').click()">
                    <div class="kc-file-input-icon">🎤</div>
                    <div class="kc-file-input-text">Click to select an audio file or drag and drop</div>
                    <input type="file" name="document" id="file-input" accept="audio/*">
                </div>
                
                <div id="file-preview" class="kc-audio-controls" style="display: none;">
                    <audio id="preview-audio" controls style="width: 100%;"></audio>
                </div>
                
                <input type="hidden" name="document_name" value="Audio Recording <?php echo date('Y-m-d H:i:s'); ?>">
                <input type="hidden" name="document_type" value="medical_audio">
                <button type="submit" class="kc-btn" style="width: 100%; margin-top: 20px;">Upload Audio</button>
            </form>
        </div>
        
        <div class="kc-session-info">
            This recording session will expire in 
            <span id="session-timer">
                <?php 
                $minutes_left = floor(($session_data['expires_at'] - time()) / 60);
                echo esc_html($minutes_left . ' minute' . ($minutes_left != 1 ? 's' : '')); 
                ?>
            </span>
        </div>
    <?php endif; ?>
    
    <div class="kc-footer">
        Powered by Medroid EHR
    </div>
</div>

<script>
    // Tab switching
    document.querySelectorAll('.kc-upload-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs and content
            document.querySelectorAll('.kc-upload-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.kc-upload-content').forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(this.dataset.tab + '-tab').classList.add('active');
        });
    });
    
    // File input preview
    const fileInput = document.getElementById('file-input');
    const filePreview = document.getElementById('file-preview');
    const previewAudio = document.getElementById('preview-audio');
    
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                
                // Show audio preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewAudio.src = e.target.result;
                    filePreview.style.display = 'block';
                }
                reader.readAsDataURL(file);
            }
        });
        
        // Drag and drop
        const dropArea = document.querySelector('.kc-file-input');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            dropArea.style.borderColor = '#000';
        }
        
        function unhighlight() {
            dropArea.style.borderColor = '#ddd';
        }
        
        dropArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            fileInput.files = files;
            
            // Trigger change event
            const event = new Event('change');
            fileInput.dispatchEvent(event);
        }
    }
    
    // Audio recording functionality
    const startRecordingBtn = document.getElementById('start-recording');
    const pauseRecordingBtn = document.getElementById('pause-recording');
    const resumeRecordingBtn = document.getElementById('resume-recording');
    const stopRecordingBtn = document.getElementById('stop-recording');
    const discardRecordingBtn = document.getElementById('discard-recording');
    const submitRecordingBtn = document.getElementById('submit-recording');
    const recordingTime = document.getElementById('recording-time');
    const recordingStatus = document.getElementById('recording-status');
    const audioPreviewContainer = document.getElementById('audio-preview-container');
    const audioPreview = document.getElementById('audio-preview');
    const audioForm = document.getElementById('audio-form');
    const audioDataInput = document.getElementById('audio-data');
    const visualizationCanvas = document.getElementById('visualization');
    
    let mediaRecorder;
    let audioChunks = [];
    let audioBlob;
    let audioStream;
    let recordingStartTime;
    let recordingTimer;
    let totalRecordingTime = 0;
    let isPaused = false;
    let pauseStartTime;
    let analyserNode;
    let visualizationContext;
    
    // Audio visualization setup
    function setupVisualization(stream) {
        if (!visualizationCanvas) return;
        
        visualizationContext = visualizationCanvas.getContext('2d');
        
        // Set canvas dimensions to match container
        visualizationCanvas.width = visualizationCanvas.clientWidth;
        visualizationCanvas.height = visualizationCanvas.clientHeight;
        
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const source = audioContext.createMediaStreamSource(stream);
        analyserNode = audioContext.createAnalyser();
        
        analyserNode.fftSize = 256;
        analyserNode.smoothingTimeConstant = 0.8;
        source.connect(analyserNode);
        
        // Start the animation loop
        drawVisualization();
    }
    
    // Draw the visualization
    function drawVisualization() {
        if (!analyserNode || !visualizationContext) return;
        
        requestAnimationFrame(drawVisualization);
        
        const bufferLength = analyserNode.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        analyserNode.getByteFrequencyData(dataArray);
        
        // Clear the canvas
        visualizationContext.clearRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);
        
        // Set bar width based on canvas width
        const barWidth = (visualizationCanvas.width / bufferLength) * 2.5;
        let barHeight;
        let x = 0;
        
        // Draw bars
        for (let i = 0; i < bufferLength; i++) {
            barHeight = dataArray[i] / 2;
            
            const gradient = visualizationContext.createLinearGradient(
                0, visualizationCanvas.height - barHeight, 0, visualizationCanvas.height
            );
            gradient.addColorStop(0, '#4299e1');
            gradient.addColorStop(1, '#3182ce');
            
            visualizationContext.fillStyle = gradient;
            visualizationContext.fillRect(
                x, visualizationCanvas.height - barHeight, 
                barWidth, barHeight
            );
            
            x += barWidth + 1;
        }
    }
    
    // Format time as MM:SS
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        seconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    // Update recording time display
    function updateRecordingTime() {
        if (isPaused) return;
        
        const currentTime = Date.now();
        const elapsedSeconds = (currentTime - recordingStartTime) / 1000;
        const totalSeconds = totalRecordingTime + elapsedSeconds;
        
        recordingTime.textContent = formatTime(totalSeconds);
    }
    
    // Start recording
    async function startRecording() {
        try {
            audioChunks = [];
            
            // Request audio permissions
            audioStream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            
            // Setup visualization
            setupVisualization(audioStream);
            
            // Determine best MIME type with fallbacks
            let mimeType = null;
            
            // Try different MIME types in order of preference
            const mimeTypes = [
                'audio/webm;codecs=opus',
                'audio/webm',
                'audio/mp4',
                'audio/ogg;codecs=opus',
                'audio/wav',
                'audio/aac'
            ];
            
            for (const type of mimeTypes) {
                if (MediaRecorder.isTypeSupported(type)) {
                    mimeType = type;
                    break;
                }
            }
            
            // Create media recorder with detected MIME type (or default)
            const recorderOptions = { audioBitsPerSecond: 128000 };
            if (mimeType) {
                recorderOptions.mimeType = mimeType;
                console.log('Using MIME type:', mimeType);
            } else {
                console.log('No supported MIME type found, using browser default');
            }
            
            mediaRecorder = new MediaRecorder(audioStream, recorderOptions);
            
            // Handle data chunks
            mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    audioChunks.push(e.data);
                }
            };
            
            // Start recording
            mediaRecorder.start(1000); // Collect in 1-second chunks
            
            // Update UI
            recordingStartTime = Date.now();
            recordingTimer = setInterval(updateRecordingTime, 1000);
            recordingStatus.innerHTML = '<span class="recording-indicator"></span> Recording...';
            
            startRecordingBtn.style.display = 'none';
            pauseRecordingBtn.style.display = 'inline-block';
            stopRecordingBtn.style.display = 'inline-block';
            
            isPaused = false;
        } catch (error) {
            console.error('Error starting recording:', error);
            recordingStatus.textContent = 'Error: ' + (error.message || 'Could not access microphone');
        }
    }
    
    // Pause recording
    function pauseRecording() {
        if (!mediaRecorder || mediaRecorder.state !== 'recording') return;
        
        // Pause the recorder if supported
        try {
            mediaRecorder.pause();
        } catch (e) {
            // Fallback for browsers that don't support pause
            if (audioStream) {
                audioStream.getTracks().forEach(track => {
                    track.enabled = false;
                });
            }
        }
        
        // Update tracking variables
        pauseStartTime = Date.now();
        isPaused = true;
        
        // Update UI
        recordingStatus.textContent = 'Paused';
        pauseRecordingBtn.style.display = 'none';
        resumeRecordingBtn.style.display = 'inline-block';
    }
    
    // Resume recording
    function resumeRecording() {
        if (!mediaRecorder || !isPaused) return;
        
        // Resume the recorder if supported
        try {
            mediaRecorder.resume();
        } catch (e) {
            // Fallback for browsers that don't support resume
            if (audioStream) {
                audioStream.getTracks().forEach(track => {
                    track.enabled = true;
                });
            }
        }
        
        // Update tracking variables
        totalRecordingTime += (Date.now() - pauseStartTime) / 1000;
        recordingStartTime = Date.now();
        isPaused = false;
        
        // Update UI
        recordingStatus.innerHTML = '<span class="recording-indicator"></span> Recording...';
        resumeRecordingBtn.style.display = 'none';
        pauseRecordingBtn.style.display = 'inline-block';
    }
    
    // Stop recording
    function stopRecording() {
        if (!mediaRecorder) return;
        
        // Stop the recorder
        if (mediaRecorder.state !== 'inactive') {
            mediaRecorder.stop();
        }
        
        // Stop all tracks
        if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop());
        }
        
        // Clear timer
        clearInterval(recordingTimer);
        
        // Update UI
        recordingStatus.textContent = 'Recording complete';
        startRecordingBtn.style.display = 'none';
        pauseRecordingBtn.style.display = 'none';
        resumeRecordingBtn.style.display = 'none';
        stopRecordingBtn.style.display = 'none';
        
        // Create audio blob and show preview
        setTimeout(() => {
            audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            const audioUrl = URL.createObjectURL(audioBlob);
            audioPreview.src = audioUrl;
            audioPreviewContainer.style.display = 'block';
        }, 500);
    }
    
    // Discard recording
    function discardRecording() {
        // Reset all recording state
        audioChunks = [];
        audioBlob = null;
        totalRecordingTime = 0;
        
        // Reset UI
        audioPreviewContainer.style.display = 'none';
        startRecordingBtn.style.display = 'block';
        recordingStatus.textContent = 'Ready to record';
        recordingTime.textContent = '00:00';
        
        // Revoke object URL
        if (audioPreview.src) {
            URL.revokeObjectURL(audioPreview.src);
            audioPreview.src = '';
        }
    }
    
    // Submit recording
    function submitRecording() {
        if (!audioBlob) return;
        
        // Convert blob to base64
        const reader = new FileReader();
        reader.onloadend = () => {
            // Log file type for debugging
            console.log('Audio file type:', audioBlob.type);
            
            // Try to convert audio to a widely supported format if possible
            try {
                // Create an audio element to check if we can play the recorded audio
                const audio = new Audio();
                const canPlayType = audio.canPlayType(audioBlob.type);
                console.log('Browser can play this type:', canPlayType);
                
                // Make sure we have a proper data URL with MIME type
                let dataUrl = reader.result;
                if (!dataUrl.startsWith('data:audio/')) {
                    // If no MIME type in data URL, add it
                    dataUrl = 'data:audio/wav;base64,' + dataUrl.split(',')[1];
                }
                audioDataInput.value = dataUrl;
                
                // Update UI before submission
                recordingStatus.textContent = 'Uploading audio recording...';
                submitRecordingBtn.disabled = true;
                discardRecordingBtn.disabled = true;
                
                // Submit the form
                audioForm.submit();
            } catch (error) {
                console.error('Error processing audio:', error);
                recordingStatus.textContent = 'Error processing audio: ' + error.message;
                submitRecordingBtn.disabled = false;
                discardRecordingBtn.disabled = false;
            }
        };
        reader.readAsDataURL(audioBlob);
    }
    
    // Add event listeners
    if (startRecordingBtn) {
        startRecordingBtn.addEventListener('click', startRecording);
    }
    
    if (pauseRecordingBtn) {
        pauseRecordingBtn.addEventListener('click', pauseRecording);
    }
    
    if (resumeRecordingBtn) {
        resumeRecordingBtn.addEventListener('click', resumeRecording);
    }
    
    if (stopRecordingBtn) {
        stopRecordingBtn.addEventListener('click', stopRecording);
    }
    
    if (discardRecordingBtn) {
        discardRecordingBtn.addEventListener('click', discardRecording);
    }
    
    if (submitRecordingBtn) {
        submitRecordingBtn.addEventListener('click', submitRecording);
    }
    
    // Session timer
    const sessionTimer = document.getElementById('session-timer');
    if (sessionTimer) {
        const expiresAt = <?php echo json_encode($session_data ? $session_data['expires_at'] : 0); ?>;
        
        function updateTimer() {
            const now = Math.floor(Date.now() / 1000);
            const secondsLeft = expiresAt - now;
            
            if (secondsLeft <= 0) {
                sessionTimer.textContent = 'Expired';
                return;
            }
            
            const minutesLeft = Math.floor(secondsLeft / 60);
            const seconds = secondsLeft % 60;
            
            sessionTimer.textContent = minutesLeft + ' minute' + (minutesLeft !== 1 ? 's' : '') + 
                                      ' and ' + seconds + ' second' + (seconds !== 1 ? 's' : '');
            
            setTimeout(updateTimer, 1000);
        }
        
        updateTimer();
    }
</script>
</body>
</html>