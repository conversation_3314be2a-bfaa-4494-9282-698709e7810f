<!-- components/EditPatientModal.vue -->
<template>
  <div
    v-if="showEditPatientModal"
    class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
  >
    <div class="bg-white rounded-lg w-full max-w-2xl">
      <!-- Modal Header -->
      <div class="border-b px-4 py-3 flex justify-between items-center">
        <h3 class="text-lg font-semibold">Edit Patient Details</h3>
        <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-4 py-4">
        <form @submit.prevent="handleSubmit">
          <div class="grid grid-cols-2 gap-4">
            <!-- First Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >First Name</label
              >
              <input
                type="text"
                v-model="formData.first_name"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                required
              />
            </div>

            <!-- Last Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Last Name</label
              >
              <input
                type="text"
                v-model="formData.last_name"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                required
              />
            </div>

            <!-- Email -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Email</label
              >
              <input
                type="email"
                v-model="formData.user_email"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                required
              />
            </div>

            <!-- Mobile Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Mobile Number</label
              >
              <div class="flex gap-2">
                <select
                  v-model="formData.country_calling_code"
                  class="w-24 px-2 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  required
                >
                  <option value="+44">+44</option>
                  <!-- Add more country codes as needed -->
                </select>
                <input
                  type="tel"
                  v-model="formData.mobile_number"
                  class="flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            <!-- Date of Birth -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Date of Birth</label
              >
              <input
                type="date"
                v-model="formData.dob"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            <!-- Gender -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Gender</label
              >
              <select
                v-model="formData.gender"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                required
              >
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
            </div>

            <!-- NHS Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >NHS Number</label
              >
              <input
                type="text"
                v-model="formData.nhs"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                required
              />
            </div>

            <!-- Country -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Country</label
              >
              <select
                v-model="formData.country"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                required
                @change="handleCountryChange"
              >
                <option value="GB">United Kingdom</option>
                <!-- Add more countries as needed -->
              </select>
            </div>

            <!-- Address -->
            <div class="col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Address</label
              >
              <textarea
                v-model="formData.address"
                rows="3"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              ></textarea>
            </div>

            <!-- City -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >City</label
              >
              <input
                type="text"
                v-model="formData.city"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            <!-- Post Code -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Post Code</label
              >
              <input
                type="text"
                v-model="formData.postal_code"
                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>

          <!-- Error Message -->
          <!-- <div v-if="errorMessage" class="mt-4 text-red-500 text-sm">
            {{ errorMessage }}
          </div> -->

          <!-- Modal Footer -->
          <div class="mt-6 flex justify-end gap-3">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border rounded-md text-gray-600 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-black text-white rounded-md hover:bg-black"
              :disabled="loading"
            >
              {{ loading ? "Saving..." : "Save Changes" }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { updatePatientDetails } from "../../services/patient.js";

export default {
  name: "EditPatientModal",
  props: {
    showEditPatientModal: {
      type: Boolean,
      required: true,
    },
    patientDetails: {
      type: Object,
      required: true,
    },
    patientMetaData: {
      type: Object,
      default: () => ({}),
    },
    isEncounterTemp: {
      // Add this prop
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      errorMessage: "",
      formData: {
        ID: "",
        first_name: "",
        last_name: "",
        user_email: "",
        mobile_number: "",
        gender: "",
        dob: "",
        u_id: "",
        nhs: "",
        address: "",
        city: "",
        country: "GB",
        postal_code: "",
        country_code: "GB",
        country_calling_code: "+44",
      },
    };
  },
  watch: {
    showEditPatientModal: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.initializeForm();
        }
      },
    },
    patientDetails: {
      handler(newVal) {
        if (newVal) {
          this.initializeForm();
        }
      },
      deep: true,
    },
  },
  methods: {
    initializeForm() {
      // Split patient name into first and last name
      const [firstName = "", lastName = ""] = (
        this.patientDetails?.patient_name || ""
      ).split(" ");

      // Initialize form data from patient details and metadata
      this.formData = {
        ID: this.patientDetails?.patient_id || "",
        first_name: firstName,
        last_name: lastName,
        user_email: this.patientDetails?.patient_email || "",
        mobile_number: this.patientMetaData?.mobile_number || "",
        gender: this.patientMetaData?.gender || "male",
        dob: this.patientMetaData?.dob || "",
        u_id: this.patientDetails?.patient_unique_id || "",
        nhs: this.patientMetaData?.nhs || "",
        address:
          this.patientMetaData?.address ||
          this.patientDetails?.patient_address ||
          "",
        city: this.patientMetaData?.city || "",
        country: this.patientMetaData?.country || "GB",
        postal_code: this.patientMetaData?.postal_code || "",
        country_code: "GB",
        country_calling_code: "+44",
      };
    },

    handleCountryChange(event) {
      this.formData.country_code = event.target.value;
    },

    async handleSubmit() {
      try {
        this.loading = true;
        this.errorMessage = "";

        // Format mobile number by removing spaces, parentheses, and dashes
        const formattedMobile = this.formData.mobile_number.replace(
          /[\(\)\-\s]/g,
          ""
        );

        // Prepare payload for API
        const payload = {
          ID: this.formData.ID,
          first_name: this.formData.first_name,
          last_name: this.formData.last_name,
          user_email: this.formData.user_email,
          mobile_number: formattedMobile,
          gender: this.formData.gender,
          dob: this.formData.dob,
          u_id: this.formData.u_id,
          nhs: this.formData.nhs,
          address: this.formData.address,
          city: this.formData.city,
          country: this.formData.country,
          postal_code: this.formData.postal_code,
          country_code: this.formData.country_code,
          country_calling_code: this.formData.country_calling_code,
          is_encounter_temp: this.isEncounterTemp,
        };

        // Make API call to update patient details
        const response = await updatePatientDetails(payload);

        if (response?.status) {
          // Emit success event with updated data
          this.$emit("patient-updated", {
            ...this.patientDetails,
            patient_name: `${payload.first_name} ${payload.last_name}`,
            patient_unique_id: payload.u_id,
            patient_email: payload.user_email,
            patient_address: payload.address,
          });

          this.$swal.fire({
            icon: "success",
            title: "Success",
            text: response.message || "Patient details updated successfully",
            showConfirmButton: false,
            timer: 1500,
          });

          // Show success message
          this.$toast.success(
            response.message || "Patient details updated successfully"
          );

          // Close the modal
          this.closeModal();
        } else {
          throw new Error(
            response.message || "Failed to update patient details"
          );
        }
      } catch (error) {
        console.error("Error updating patient details:", error);
        this.errorMessage =
          error.message ||
          "Failed to update patient details. Please try again.";
        this.$toast.error(this.errorMessage);
      } finally {
        this.loading = false;
      }
    },
    closeModal() {
      this.$emit("update:showEditPatientModal", false); // Emit the update event
    },
  },
};
</script>
