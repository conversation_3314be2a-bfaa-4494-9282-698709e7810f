<template>
  <div class="category-management">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Category Management</h1>
        <p class="text-gray-600 mt-1">Manage service categories and their visibility settings</p>
      </div>
      <button
        v-if="isAdmin"
        @click="openCreateModal"
        class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
      >
        <i class="fas fa-plus"></i>
        <span>Add Category</span>
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-64">
          <label class="block text-sm font-medium text-gray-700 mb-2">Module Type</label>
          <select v-model="filters.module_type" @change="getCategories" class="w-full border border-gray-300 rounded-lg px-3 py-2">
            <option value="service">Service</option>
            <option value="product">Product</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div class="flex-1 min-w-64">
          <label class="block text-sm font-medium text-gray-700 mb-2">Visibility</label>
          <select v-model="filters.visibility" @change="getCategories" class="w-full border border-gray-300 rounded-lg px-3 py-2">
            <option value="">All</option>
            <option value="public">Public</option>
            <option value="backend_only">Backend Only</option>
            <option value="disabled">Disabled</option>
          </select>
        </div>
        <div class="flex-1 min-w-64">
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select v-model="filters.status" @change="getCategories" class="w-full border border-gray-300 rounded-lg px-3 py-2">
            <option value="">All</option>
            <option value="1">Active</option>
            <option value="0">Inactive</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Categories Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 border-b border-gray-200">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Module</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visibility</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sort Order</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="loading">
              <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                <i class="fas fa-spinner fa-spin mr-2"></i>Loading categories...
              </td>
            </tr>
            <tr v-else-if="categories.length === 0">
              <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                No categories found
              </td>
            </tr>
            <tr v-else v-for="category in categories" :key="category.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ category.name }}</div>
                <div v-if="category.description" class="text-sm text-gray-500">{{ category.description }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ category.slug }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ category.module_type }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getVisibilityBadgeClass(category.visibility)" class="px-2 py-1 text-xs font-medium rounded-full">
                  {{ getVisibilityLabel(category.visibility) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ category.sort_order }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="category.status == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                      class="px-2 py-1 text-xs font-medium rounded-full">
                  {{ category.status == 1 ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    v-if="isAdmin"
                    @click="editCategory(category)"
                    class="text-purple-600 hover:text-purple-900 transition-colors"
                  >
                    <i class="fas fa-edit"></i>
                  </button>
                  <button
                    v-if="isAdmin"
                    @click="deleteCategory(category)"
                    class="text-red-600 hover:text-red-900 transition-colors"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <CategoryForm
      v-if="showModal"
      :category="selectedCategory"
      :is-edit="isEdit"
      @close="closeModal"
      @saved="onCategorySaved"
    />
  </div>
</template>

<script>
import { get, post } from "../../config/request";
import CategoryForm from "./Create.vue";

export default {
  name: "CategoryIndex",
  components: {
    CategoryForm
  },
  data() {
    return {
      categories: [],
      loading: false,
      showModal: false,
      selectedCategory: null,
      isEdit: false,
      filters: {
        module_type: 'service',
        visibility: '',
        status: ''
      }
    };
  },
  computed: {
    isAdmin() {
      const userRole = this.getUserRole();
      return userRole === 'administrator' || userRole === 'clinic_admin';
    }
  },
  mounted() {
    // Check if user is admin, if not redirect
    if (!this.isAdmin) {
      this.$router.push({ name: 'dashboard' });
      return;
    }
    this.getCategories();
  },
  methods: {
    getCategories() {
      this.loading = true;
      const params = {
        module_type: this.filters.module_type,
        status: this.filters.status
      };

      get('category_list', params)
        .then(response => {
          this.loading = false;
          if (response.data.status) {
            let categories = response.data.data;

            // Apply visibility filter if selected
            if (this.filters.visibility) {
              categories = categories.filter(cat => cat.visibility === this.filters.visibility);
            }

            // Apply status filter if selected (client-side backup)
            if (this.filters.status !== '') {
              categories = categories.filter(cat => cat.status == this.filters.status);
            }

            this.categories = categories;
          } else {
            this.categories = [];
          }
        })
        .catch(error => {
          this.loading = false;
          console.error('Error fetching categories:', error);
          this.$swal.fire({
            title: 'Error!',
            text: 'Failed to fetch categories',
            icon: 'error'
          });
        });
    },

    openCreateModal() {
      this.selectedCategory = null;
      this.isEdit = false;
      this.showModal = true;
    },

    editCategory(category) {
      this.selectedCategory = { ...category };
      this.isEdit = true;
      this.showModal = true;
    },

    closeModal() {
      this.showModal = false;
      this.selectedCategory = null;
      this.isEdit = false;
    },

    onCategorySaved(successMessage) {
      this.closeModal();
      this.getCategories();
      // Show success message after modal is closed
      if (successMessage) {
        this.$nextTick(() => {
          this.$swal.fire({
            title: 'Success!',
            text: successMessage,
            icon: 'success'
          });
        });
      }
    },

    deleteCategory(category) {
      this.$swal.fire({
        title: 'Are you sure?',
        text: `Do you want to delete the category "${category.name}"?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
      }).then((result) => {
        if (result.isConfirmed) {
          post('category_delete', { id: category.id })
            .then(response => {
              if (response.data.status) {
                this.$swal.fire({
                  title: 'Deleted!',
                  text: response.data.message,
                  icon: 'success'
                });
                this.getCategories();
              } else {
                this.$swal.fire({
                  title: 'Error!',
                  text: response.data.message,
                  icon: 'error'
                });
              }
            })
            .catch(error => {
              console.error('Error deleting category:', error);
              this.$swal.fire({
                title: 'Error!',
                text: 'Failed to delete category',
                icon: 'error'
              });
            });
        }
      });
    },

    getVisibilityLabel(visibility) {
      const labels = {
        'public': 'Public',
        'backend_only': 'Backend Only',
        'disabled': 'Disabled'
      };
      return labels[visibility] || visibility;
    },

    getVisibilityBadgeClass(visibility) {
      const classes = {
        'public': 'bg-green-100 text-green-800',
        'backend_only': 'bg-blue-100 text-blue-800',
        'disabled': 'bg-gray-100 text-gray-800'
      };
      return classes[visibility] || 'bg-gray-100 text-gray-800';
    },

    getUserRole() {
      const user = this.$store.state.userDataModule.user;
      if (user && user.roles) {
        if (user.roles.includes('administrator')) return 'administrator';
        if (user.roles.includes(window.pluginPREFIX + 'clinic_admin')) return 'clinic_admin';
      }
      return '';
    }
  }
};
</script>

<style scoped>
.category-management {
  padding: 1.5rem;
}
</style>