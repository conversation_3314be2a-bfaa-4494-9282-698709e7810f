<?php

namespace App\models;

use App\baseClasses\MDModel;

class KCChatSetting extends MDModel {
    public function __construct() {
        parent::__construct('chat_settings');
    }
    
    /**
     * Override the insert method to ensure tables exist and add debugging
     *
     * @param array $data Data to insert
     * @return int|false ID of new record or false on failure
     */
    public function insert($data) {
        global $wpdb;
        
        // Check if table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $wpdb->prefix . 'md_chat_settings'
            )
        );

        // Now insert the data
        try {
            return parent::insert($data);
        } catch (\Exception $e) {
            error_log('Error creating chat setting: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Override the update method to ensure table exists
     *
     * @param array $data Data to update
     * @param array $where Where conditions
     * @return int|false Number of rows updated or false on failure
     */
    public function update($data, $where) {
        global $wpdb;
        
        // Check if table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $wpdb->prefix . 'chat_settings'
            )
        );
        
        // Now update the data
        try {
            return parent::update($data, $where);
        } catch (\Exception $e) {
            error_log('Error updating chat setting: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get chat settings for a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return object|array Settings object or empty array if none
     */
    public function getClinicSettings($clinic_id) {
        // If clinic_id is 0, get default settings
        if ($clinic_id === 0) {
            return [
                'allow_patient_doctor_chat' => 'no'
            ];
        }
        
        // Get settings from database
        $settings = $this->get_by(['clinic_id' => $clinic_id]);
        
        // If no settings, return default
        if (empty($settings)) {
            return [
                'allow_patient_doctor_chat' => 'no'
            ];
        }
        
        return $settings;
    }
}