# TDL Labs Integration Implementation Plan

This document provides a step-by-step implementation guide to complete the TDL Labs integration for KiviCare EHR. Each task is broken down into specific development steps with potential issues highlighted.

## Phase 1: Azure Blob Storage Integration

### Task 1.1: Install Azure Storage SDK
1. Add the Azure Storage SDK to the project's dependencies
   ```bash
   composer require microsoft/azure-storage-blob
   ```
2. Verify the SDK is properly loaded in the project
   ```php
   require_once(KIVICARE_BASE_DIR . 'vendor/autoload.php');
   use MicrosoftAzure\Storage\Blob\BlobRestProxy;
   ```

**Potential Issues:**
- Composer may fail if PHP extensions like `ext-curl` are missing
- Make sure to add the vendor directory to .gitignore if not already present

### Task 1.2: Update the TDL Settings Model
1. Open `/app/models/KCTDLClinicSetting.php`
2. Modify the model to validate Azure connection strings
3. Add a method to generate a full connection string from the settings

```php
public function validateConnectionString($connection_string) {
    return strpos($connection_string, 'AccountName=') !== false &&
           strpos($connection_string, 'AccountKey=') !== false;
}

public function getConnectionStringFromCredentials($account_name, $account_key) {
    return "DefaultEndpointsProtocol=https;AccountName={$account_name};AccountKey={$account_key};EndpointSuffix=core.windows.net";
}
```

**Potential Issues:**
- Ensure proper sanitization of inputs to prevent injection
- Validate the connection string format to avoid runtime errors

### Task 1.3: Implement HL7 Upload to Azure
1. Open `/app/controllers/KCTDLLabsController.php`
2. Complete the `sendHL7ToAzure()` method:

```php
private function sendHL7ToAzure($request_id, $hl7_message, $clinic_id) {
    // Get clinic TDL settings
    $tdl_settings = $this->tdl_clinic_setting->getClinicSettings($clinic_id);
    
    if (empty($tdl_settings) || empty($tdl_settings['azure_storage_connection'])) {
        throw new Exception(esc_html__('Azure storage settings not configured', 'kc-lang'));
    }
    
    // Load Azure Storage SDK
    require_once(KIVICARE_BASE_DIR . 'vendor/autoload.php');
    use MicrosoftAzure\Storage\Blob\BlobRestProxy;
    use MicrosoftAzure\Storage\Blob\Models\CreateBlockBlobOptions;
    
    // Use the Azure SDK to upload the HL7 message
    try {
        $connectionString = $tdl_settings['azure_storage_connection'];
        $blobClient = BlobRestProxy::createBlobService($connectionString);
        
        $containerName = "requests";
        $fileName = "REQ_" . $clinic_id . "_" . $request_id . "_" . date('YmdHis') . ".hl7";
        
        // Set content type for HL7
        $options = new CreateBlockBlobOptions();
        $options->setContentType('application/hl7-v2');
        
        // Upload HL7 message to Azure
        $blobClient->createBlockBlob($containerName, $fileName, $hl7_message, $options);
        
        // Log the successful upload
        error_log("TDL: Successfully uploaded request ID {$request_id} to Azure as {$fileName}");
        
        // Update request status
        $this->tdl_test_request->updateRequest($request_id, ['status' => 'sent']);
        
        return true;
    } catch(Exception $e) {
        error_log("TDL Azure upload error: " . $e->getMessage());
        throw new Exception(esc_html__('Failed to upload request to TDL: ', 'kc-lang') . $e->getMessage());
    }
}
```

**Potential Issues:**
- The Azure container "requests" must already exist
- The connection string might be malformed
- Network connectivity issues may cause timeouts

### Task 1.4: Implement Result Polling from Azure
1. Open `/app/controllers/KCTDLLabsController.php`
2. Complete the `pollAzureForResults()` method:

```php
public function pollAzureForResults() {
    try {
        $request_data = $this->request->getInputs();
        $clinic_id = isset($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : 0;
        
        if (empty($clinic_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Clinic ID is required', 'kc-lang'),
                'data' => []
            ]);
        }
        
        // Get clinic TDL settings
        $tdl_settings = $this->tdl_clinic_setting->getClinicSettings($clinic_id);
        
        if (empty($tdl_settings) || empty($tdl_settings['azure_storage_connection'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Azure storage settings not configured', 'kc-lang'),
                'data' => []
            ]);
        }
        
        // Load Azure Storage SDK
        require_once(KIVICARE_BASE_DIR . 'vendor/autoload.php');
        use MicrosoftAzure\Storage\Blob\BlobRestProxy;
        use MicrosoftAzure\Storage\Blob\Models\ListBlobsOptions;
        
        // Use the Azure SDK to list blobs
        $connectionString = $tdl_settings['azure_storage_connection'];
        $blobClient = BlobRestProxy::createBlobService($connectionString);
        
        $containerName = "results";
        $listBlobsOptions = new ListBlobsOptions();
        $listBlobsOptions->setPrefix("");
        
        // Get all blobs in the results container
        $result = $blobClient->listBlobs($containerName, $listBlobsOptions);
        $blobs = $result->getBlobs();
        
        $processed_count = 0;
        $failed_count = 0;
        $error_details = [];
        
        foreach($blobs as $blob) {
            try {
                // Download the blob
                $blob_content = $blobClient->getBlob($containerName, $blob->getName())->getContentStream();
                $content = stream_get_contents($blob_content);
                
                // Determine format (HL7 or JSON)
                $format = (strpos($content, 'MSH|') === 0) ? 'hl7' : 'json';
                
                // Process the result
                if ($format === 'hl7') {
                    $result_id = $this->processHL7Results($content, $clinic_id);
                } else {
                    $result_id = $this->processJSONResults($content, $clinic_id);
                }
                
                // If successfully processed, delete the blob
                $blobClient->deleteBlob($containerName, $blob->getName());
                $processed_count++;
                
                // Log the successful processing
                error_log("TDL: Successfully processed result from Azure blob {$blob->getName()}, format: {$format}, result_id: {$result_id}");
            } catch (Exception $e) {
                $failed_count++;
                $error_details[] = [
                    'blob_name' => $blob->getName(),
                    'error' => $e->getMessage()
                ];
                error_log("TDL: Error processing result from blob {$blob->getName()}: " . $e->getMessage());
                // Skip this file and continue processing others
                continue;
            }
        }
        
        wp_send_json([
            'status' => true,
            'message' => sprintf(esc_html__('Processed %d new results, %d failed', 'kc-lang'), $processed_count, $failed_count),
            'data' => [
                'processed_results' => $processed_count,
                'failed_results' => $failed_count,
                'errors' => $error_details
            ]
        ]);
    } catch (Exception $e) {
        wp_send_json([
            'status' => false,
            'message' => $e->getMessage(),
            'data' => []
        ]);
    }
}
```

**Potential Issues:**
- The Azure container "results" must already exist
- Stream handling may cause memory issues with large files
- Error handling must be robust to prevent stopping on a single failure

### Task 1.5: Create a WordPress Cron Job for Polling
1. Add cron registration in the plugin's main file:

```php
// In kivicare-clinic-management-system.php or a suitable initialization file
function kc_register_tdl_polling_cron() {
    if (!wp_next_scheduled('kc_tdl_poll_results')) {
        wp_schedule_event(time(), 'fifteen_minutes', 'kc_tdl_poll_results');
    }
}
add_action('init', 'kc_register_tdl_polling_cron');

// Define a custom 15-minute interval
function kc_add_cron_interval($schedules) {
    $schedules['fifteen_minutes'] = array(
        'interval' => 900,
        'display'  => esc_html__('Every 15 Minutes', 'kc-lang'),
    );
    return $schedules;
}
add_filter('cron_schedules', 'kc_add_cron_interval');

// Define the cron job callback
function kc_tdl_poll_results_callback() {
    // Get all clinics with TDL enabled
    global $wpdb;
    $table_name = $wpdb->prefix . 'kc_tdl_clinic_settings';
    $clinics = $wpdb->get_results("SELECT clinic_id FROM {$table_name} WHERE azure_storage_connection IS NOT NULL AND azure_storage_connection != ''", ARRAY_A);
    
    $controller = new \App\controllers\KCTDLLabsController();
    
    foreach ($clinics as $clinic) {
        try {
            // Simulate a request for each clinic
            $controller->request->set_input('clinic_id', $clinic['clinic_id']);
            $controller->pollAzureForResults();
        } catch (Exception $e) {
            error_log("TDL Cron error for clinic {$clinic['clinic_id']}: " . $e->getMessage());
            continue;
        }
    }
}
add_action('kc_tdl_poll_results', 'kc_tdl_poll_results_callback');
```

**Potential Issues:**
- WordPress cron requires site visits to trigger
- Consider using a real cron job via the server for production

## Phase 2: HL7 Message Processing

### Task 2.1: Fix the HL7 Message Generation
1. Open `/app/controllers/KCTDLLabsController.php`
2. Update the `generateHL7Message()` method to use proper HL7 formatting:

```php
private function generateHL7Message($request_data, $tests) {
    // Get patient, doctor, and clinic details
    $doctor = get_user_by('ID', $request_data['doctor_id']);
    $patient = get_user_by('ID', $request_data['patient_id']);
    $clinic = $this->clinic_model->get_by(['id' => $request_data['clinic_id']]);
    
    if (empty($patient) || empty($doctor) || empty($clinic)) {
        throw new Exception(esc_html__('Invalid patient, doctor, or clinic', 'kc-lang'));
    }
    
    $patient_meta = get_user_meta($patient->ID);
    $doctor_meta = get_user_meta($doctor->ID);
    
    // Get clinic TDL settings
    $tdl_settings = $this->tdl_clinic_setting->getClinicSettings($request_data['clinic_id']);
    
    if (empty($tdl_settings)) {
        throw new Exception(esc_html__('TDL settings not configured for this clinic', 'kc-lang'));
    }
    
    // Format patient DOB
    $patient_data = isset($patient_meta['basic_data'][0]) ? json_decode($patient_meta['basic_data'][0], true) : [];
    $dob = isset($patient_data['dob']) ? $patient_data['dob'] : '';
    $formatted_dob = !empty($dob) ? date('Ymd', strtotime($dob)) : '';
    
    // Build HL7 message
    $field_separator = '|';
    $component_separator = '^';
    $subcomponent_separator = '&';
    $repeat_separator = '~';
    $escape_char = '\\';
    
    // Ensure values don't exceed max lengths
    $sending_application = 'KIVICARE';
    $sending_facility = substr('KIVCARE_' . preg_replace('/[^A-Za-z0-9]/', '', $clinic->name), 0, 10);
    $receiving_application = 'TDL';
    $receiving_facility = 'TDL';
    $message_type = 'ORM';
    $message_event = 'O01';
    $processing_id = 'P';  // Production
    $version_id = '2.3';
    
    // MSH segment
    $datetime = date('YmdHis');
    $message_control_id = 'KC' . date('ymdHis') . sprintf('%03d', rand(0, 999));
    
    $msh = "MSH{$field_separator}{$component_separator}{$subcomponent_separator}{$repeat_separator}{$escape_char}{$field_separator}";
    $msh .= "{$sending_application}{$field_separator}{$sending_facility}{$field_separator}{$receiving_application}{$field_separator}{$receiving_facility}{$field_separator}";
    $msh .= "{$datetime}{$field_separator}{$field_separator}{$message_type}{$component_separator}{$message_event}{$field_separator}";
    $msh .= "{$message_control_id}{$field_separator}{$processing_id}{$field_separator}{$version_id}";
    
    // PID segment - ensure patient ID is properly formatted
    $patient_id = substr(preg_replace('/[^A-Za-z0-9]/', '', $patient->ID), 0, 10);
    
    // Format names - clean and limit length
    $lastname = substr(preg_replace('/[^A-Za-z0-9 ]/', '', $patient->last_name), 0, 20);
    $firstname = substr(preg_replace('/[^A-Za-z0-9 ]/', '', $patient->first_name), 0, 20);
    
    // Format gender
    $gender = isset($patient_data['gender']) ? strtoupper($patient_data['gender']) : '';
    $gender = ($gender == 'MALE') ? 'M' : (($gender == 'FEMALE') ? 'F' : 'U');
    
    $pid = "PID{$field_separator}1{$field_separator}{$patient_id}{$field_separator}{$field_separator}{$field_separator}";
    $pid .= "{$lastname}{$component_separator}{$firstname}{$field_separator}{$field_separator}{$field_separator}";
    $pid .= "{$formatted_dob}{$field_separator}{$gender}";
    
    // PV1 segment for healthcare provider
    $doctor_id = substr(preg_replace('/[^A-Za-z0-9]/', '', $doctor->ID), 0, 10);
    $doctor_lastname = isset($doctor_meta['last_name'][0]) ? substr(preg_replace('/[^A-Za-z0-9 ]/', '', $doctor_meta['last_name'][0]), 0, 20) : '';
    $doctor_firstname = isset($doctor_meta['first_name'][0]) ? substr(preg_replace('/[^A-Za-z0-9 ]/', '', $doctor_meta['first_name'][0]), 0, 20) : '';
    
    $pv1 = "PV1{$field_separator}1{$field_separator}O{$field_separator}{$field_separator}{$field_separator}{$field_separator}{$field_separator}";
    $pv1 .= "{$doctor_id}{$component_separator}{$doctor_lastname}{$component_separator}{$doctor_firstname}";
    
    // ORC segments (one per test)
    $order_control = 'NW';  // New order
    $order_number = $request_data['order_number'];
    $order_date = date('YmdHis', strtotime($request_data['collection_date']));
    
    $orc_list = [];
    $obr_list = [];
    
    foreach ($tests as $index => $test) {
        $sequence = $index + 1;
        $filler_number = $order_number . sprintf("%02d", $sequence);
        
        // ORC segment
        $orc = "ORC{$field_separator}{$order_control}{$field_separator}{$order_number}{$field_separator}{$filler_number}{$field_separator}{$field_separator}";
        $orc .= "{$order_date}";
        $orc_list[] = $orc;
        
        // OBR segment
        $test_code = preg_replace('/[^A-Za-z0-9]/', '', $test['test_code']);
        $test_name = substr(preg_replace('/[^A-Za-z0-9 ]/', '', $test['test_name']), 0, 30);
        
        $obr = "OBR{$field_separator}{$sequence}{$field_separator}{$order_number}{$field_separator}{$filler_number}{$field_separator}";
        $obr .= "{$test_code}{$component_separator}{$test_name}{$field_separator}{$field_separator}{$field_separator}{$field_separator}";
        $obr .= "{$order_date}";
        $obr_list[] = $obr;
    }
    
    // Build complete message with proper line endings
    $message = $msh . "\r" . $pid . "\r" . $pv1 . "\r";
    
    for ($i = 0; $i < count($tests); $i++) {
        $message .= $orc_list[$i] . "\r" . $obr_list[$i] . "\r";
    }
    
    return $message;
}
```

**Potential Issues:**
- Patient data may be incomplete, ensure fallbacks for missing fields
- Special characters in names might need better handling
- Proper HL7 line endings are critical (just \r, not \r\n or \n)

### Task 2.2: Implement HL7 Message Parser for Results
1. Create a new method in `KCTDLLabsController.php` to parse HL7 results:

```php
private function processHL7Results($hl7_content, $clinic_id) {
    // First clean up line endings to ensure consistent format
    $hl7_content = str_replace(["\r\n", "\n"], "\r", $hl7_content);
    
    // Split the message into segments
    $segments = explode("\r", $hl7_content);
    
    // Get message details from MSH segment
    $msh_segment = null;
    $pid_segment = null;
    $obr_segments = [];
    $obx_segments = [];
    
    foreach ($segments as $segment) {
        if (empty($segment)) continue;
        
        $segment_name = substr($segment, 0, 3);
        
        switch ($segment_name) {
            case 'MSH':
                $msh_segment = $segment;
                break;
            case 'PID':
                $pid_segment = $segment;
                break;
            case 'OBR':
                $obr_segments[] = $segment;
                break;
            case 'OBX':
                $obx_segments[] = $segment;
                break;
        }
    }
    
    // Validate required segments
    if (!$msh_segment || !$pid_segment || empty($obr_segments) || empty($obx_segments)) {
        throw new Exception(esc_html__('Invalid HL7 message format: missing essential segments', 'kc-lang'));
    }
    
    // Parse MSH segment
    $msh_fields = explode('|', $msh_segment);
    if (count($msh_fields) < 10) {
        throw new Exception(esc_html__('Invalid MSH segment format', 'kc-lang'));
    }
    
    $message_control_id = $msh_fields[9] ?? '';
    $message_date = isset($msh_fields[7]) ? date('Y-m-d H:i:s', strtotime($msh_fields[7])) : current_time('mysql');
    
    // Parse PID segment to get patient ID
    $pid_fields = explode('|', $pid_segment);
    if (count($pid_fields) < 4) {
        throw new Exception(esc_html__('Invalid PID segment format', 'kc-lang'));
    }
    
    $patient_id_field = $pid_fields[3] ?? '';
    
    // Try to match patient by ID
    $patient = get_user_by('ID', $patient_id_field);
    if (!$patient) {
        // If no direct match, try to search by patient identifier
        // This is a simplified approach - in reality, you might need more complex matching
        $patients = get_users(['role' => 'kiviCare_patient', 'search' => $patient_id_field]);
        if (!empty($patients)) {
            $patient = $patients[0];
        } else {
            throw new Exception(esc_html__('Patient not found in system', 'kc-lang'));
        }
    }
    
    $patient_id = $patient->ID;
    
    // Extract order number from OBR
    $order_number = null;
    if (!empty($obr_segments[0])) {
        $obr_fields = explode('|', $obr_segments[0]);
        $order_number = $obr_fields[3] ?? null;
    }
    
    // Search for a matching request
    $request = null;
    $doctor_id = null;
    
    if ($order_number) {
        $request = $this->tdl_test_request->getRequestByOrderNumber($order_number);
        if ($request) {
            $doctor_id = $request['doctor_id'];
        }
    }
    
    // Create the result record
    $result_data = [
        'patient_id' => $patient_id,
        'doctor_id' => $doctor_id,
        'clinic_id' => $clinic_id,
        'order_number' => $order_number,
        'result_id' => 'TDL_' . $message_control_id,
        'result_date' => $message_date,
        'result_status' => 'received',
        'result_data' => $hl7_content,
    ];
    
    if ($request) {
        $result_data['request_id'] = $request['id'];
        // Update request status
        $this->tdl_test_request->updateRequest($request['id'], ['status' => 'completed']);
    }
    
    // Create the result record
    $result_id = $this->tdl_test_result->createResult($result_data);
    
    if (!$result_id) {
        throw new Exception(esc_html__('Failed to create result record', 'kc-lang'));
    }
    
    // Process OBX segments (test results)
    $current_test_code = '';
    $current_test_name = '';
    
    // First extract test information from OBR segments
    $tests_info = [];
    foreach ($obr_segments as $obr) {
        $obr_fields = explode('|', $obr);
        if (count($obr_fields) >= 5) {
            $test_info = explode('^', $obr_fields[4]);
            if (count($test_info) >= 2) {
                $tests_info[$obr_fields[1]] = [
                    'code' => $test_info[0],
                    'name' => $test_info[1]
                ];
            }
        }
    }
    
    // Process OBX segments
    foreach ($obx_segments as $obx) {
        $obx_fields = explode('|', $obx);
        
        if (count($obx_fields) < 6) {
            // Skip invalid OBX segments
            continue;
        }
        
        $set_id = $obx_fields[1] ?? '1';
        $value_type = $obx_fields[2] ?? '';
        $observation_id = $obx_fields[3] ?? '';
        $observation_value = $obx_fields[5] ?? '';
        $units = $obx_fields[6] ?? '';
        $reference_range = $obx_fields[7] ?? '';
        $abnormal_flag = $obx_fields[8] ?? '';
        $observation_date = isset($obx_fields[14]) && !empty($obx_fields[14]) 
            ? date('Y-m-d H:i:s', strtotime($obx_fields[14])) 
            : $message_date;
        
        // Extract test code and name from observation ID
        $obs_parts = explode('^', $observation_id);
        $biomarker_code = $obs_parts[0] ?? '';
        $biomarker_name = $obs_parts[1] ?? $biomarker_code;
        
        // Find the associated test from OBR
        $sub_id = $obx_fields[4] ?? '1';
        if (isset($tests_info[$sub_id])) {
            $current_test_code = $tests_info[$sub_id]['code'];
            $current_test_name = $tests_info[$sub_id]['name'];
        }
        
        // If we can't determine the test, use a default
        if (empty($current_test_code)) {
            $current_test_code = 'UNKNOWN';
            $current_test_name = 'Unknown Test';
        }
        
        // Add the result item
        $item_data = [
            'result_id' => $result_id,
            'test_code' => $current_test_code,
            'test_name' => $current_test_name,
            'biomarker_name' => $biomarker_name,
            'value' => $observation_value,
            'units' => $units,
            'reference_range' => $reference_range,
            'abnormal_flag' => $abnormal_flag,
            'observation_datetime' => $observation_date,
        ];
        
        $this->tdl_test_result_item->addItem($item_data);
    }
    
    return $result_id;
}
```

**Potential Issues:**
- HL7 format variations might need more robust parsing
- Patient matching might fail if identifiers don't match exactly
- Missing fields in the HL7 message might cause issues

## Phase 3: Frontend Implementation

### Task 3.1: Update Test Results View Component
1. Open `/resources/js/views/TDLLabs/TDLResultDetails.vue`
2. Implement the biomarker visualization with range indicators:

```vue
<template>
  <div class="bg-white shadow rounded-lg p-4 mb-4">
    <div class="border-b pb-4 mb-4">
      <h2 class="text-xl font-bold text-gray-800">{{ test.test_name }}</h2>
      <p class="text-sm text-gray-600">{{ test.test_code }}</p>
    </div>
    
    <div class="space-y-4">
      <div v-for="biomarker in test.biomarkers" :key="biomarker.id" 
           class="border-b pb-4 mb-2 last:border-b-0">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="font-semibold text-gray-800">{{ biomarker.biomarker_name }}</h3>
            <div class="flex items-center gap-2 mt-1">
              <span class="font-bold text-lg" :class="getBiomarkerValueClass(biomarker)">
                {{ biomarker.value }}
              </span>
              <span class="text-gray-500 text-sm">{{ biomarker.units }}</span>
              
              <span v-if="biomarker.abnormal_flag" 
                    class="ml-2 px-2 py-0.5 rounded text-xs font-medium"
                    :class="{'bg-red-100 text-red-800': biomarker.abnormal_flag === 'H', 
                             'bg-blue-100 text-blue-800': biomarker.abnormal_flag === 'L'}">
                {{ biomarker.abnormal_flag === 'H' ? 'HIGH' : 'LOW' }}
              </span>
            </div>
          </div>
          <div class="text-right text-sm text-gray-500">
            <div>Reference: {{ biomarker.reference_range || 'Not specified' }}</div>
            <div>{{ formatDateTime(biomarker.observation_datetime) }}</div>
          </div>
        </div>
        
        <!-- Range visualization if reference range is available and numeric -->
        <div v-if="canShowRangeVisual(biomarker)" class="mt-3">
          <div class="relative h-2 bg-gray-200 rounded overflow-hidden">
            <div class="absolute inset-y-0 bg-green-200" 
                 :style="getRangeStyle(biomarker)"></div>
            <div class="absolute inset-y-0 w-1 bg-black transform -translate-x-1/2"
                 :style="getValuePosition(biomarker)"></div>
          </div>
          <div class="flex justify-between text-xs mt-1 text-gray-600">
            <span>{{ getRangeMin(biomarker) }}</span>
            <span>{{ getRangeMax(biomarker) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    test: {
      type: Object,
      required: true
    }
  },
  methods: {
    getBiomarkerValueClass(biomarker) {
      if (!biomarker.abnormal_flag) return 'text-gray-900';
      return biomarker.abnormal_flag === 'H' ? 'text-red-600' : 'text-blue-600';
    },
    formatDateTime(datetime) {
      if (!datetime) return '';
      const date = new Date(datetime);
      return date.toLocaleString();
    },
    canShowRangeVisual(biomarker) {
      if (!biomarker.reference_range) return false;
      
      // Check if reference range is in format like "3.5-5.0" or "< 10" or "> 2"
      const rangeRegex = /^((\d+(\.\d+)?)\s*-\s*(\d+(\.\d+)?)|[<>]\s*(\d+(\.\d+)?))$/;
      return rangeRegex.test(biomarker.reference_range) && !isNaN(parseFloat(biomarker.value));
    },
    getRangeMin(biomarker) {
      const range = biomarker.reference_range;
      
      if (range.includes('-')) {
        return parseFloat(range.split('-')[0].trim());
      } else if (range.includes('<')) {
        return 0; // Assume 0 as min when range is "< X"
      } else if (range.includes('>')) {
        return parseFloat(range.split('>')[1].trim());
      }
      
      return 0;
    },
    getRangeMax(biomarker) {
      const range = biomarker.reference_range;
      
      if (range.includes('-')) {
        return parseFloat(range.split('-')[1].trim());
      } else if (range.includes('<')) {
        return parseFloat(range.split('<')[1].trim());
      } else if (range.includes('>')) {
        return parseFloat(range.split('>')[1].trim()) * 2; // Double the min as a visual max
      }
      
      return 100;
    },
    getRangeStyle(biomarker) {
      const min = this.getRangeMin(biomarker);
      const max = this.getRangeMax(biomarker);
      const range = max - min;
      
      const left = `${(min / max) * 100}%`;
      const width = `${(range / max) * 100}%`;
      
      return {
        left,
        width
      };
    },
    getValuePosition(biomarker) {
      const value = parseFloat(biomarker.value);
      const max = this.getRangeMax(biomarker);
      
      // Clamp between 0-100%
      const position = Math.min(Math.max((value / max) * 100, 0), 100);
      
      return {
        left: `${position}%`
      };
    }
  }
};
</script>
```

**Potential Issues:**
- Range visualization might not work for all reference range formats
- Ensure numeric calculations have proper error handling

### Task 3.2: Update the TDL Settings UI
1. Open `/resources/js/views/TDLLabs/TDLSettings.vue`
2. Update the form to include the specific TDL and Azure credentials:

```vue
<template>
  <!-- Keep existing template structure -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
    <!-- Azure Storage Configuration -->
    <div>
      <label for="azure_account_name" class="block text-sm font-medium text-gray-700 mb-1">
        {{ $t('Azure Storage Account Name') }}
      </label>
      <input
        type="text"
        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        id="azure_account_name"
        v-model="azureAccountName"
        placeholder="tdlmedroidehr"
      />
      <p class="mt-1 text-sm text-gray-500">{{ $t('TDL Azure storage account name') }}</p>
    </div>
    
    <div>
      <label for="azure_account_key" class="block text-sm font-medium text-gray-700 mb-1">
        {{ $t('Azure Account Key') }}
      </label>
      <input
        type="password"
        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        id="azure_account_key"
        v-model="azureAccountKey"
      />
      <p class="mt-1 text-sm text-gray-500">{{ $t('TDL Azure storage account access key') }}</p>
    </div>
  </div>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
    <div>
      <label for="azure_requests_container" class="block text-sm font-medium text-gray-700 mb-1">
        {{ $t('Requests Container') }}
      </label>
      <input
        type="text"
        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        id="azure_requests_container"
        v-model="azureRequestsContainer"
        placeholder="requests"
      />
      <p class="mt-1 text-sm text-gray-500">{{ $t('Container for test requests') }}</p>
    </div>
    
    <div>
      <label for="azure_results_container" class="block text-sm font-medium text-gray-700 mb-1">
        {{ $t('Results Container') }}
      </label>
      <input
        type="text"
        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        id="azure_results_container"
        v-model="azureResultsContainer"
        placeholder="results"
      />
      <p class="mt-1 text-sm text-gray-500">{{ $t('Container for test results') }}</p>
    </div>
  </div>
  
  <!-- Keep rest of the template -->
</template>

<script>
export default {
  data() {
    return {
      // Keep existing data properties
      azureAccountName: 'tdlmedroidehr',
      azureAccountKey: '',
      azureRequestsContainer: 'requests',
      azureResultsContainer: 'results'
    };
  },
  computed: {
    // Compute the full connection string from the individual fields
    azureConnectionString() {
      if (!this.azureAccountName || !this.azureAccountKey) return '';
      
      return `DefaultEndpointsProtocol=https;AccountName=${this.azureAccountName};AccountKey=${this.azureAccountKey};EndpointSuffix=core.windows.net`;
    }
  },
  watch: {
    // Update the storage connection when the computed value changes
    azureConnectionString(val) {
      this.settings.azure_storage_connection = val;
    },
    // Update containers when they change
    azureRequestsContainer(val) {
      this.settings.azure_requests_container = val;
    },
    azureResultsContainer(val) {
      this.settings.azure_results_container = val;
    }
  },
  methods: {
    // In the getSettings method, parse the connection string to set individual fields
    getSettings() {
      // Existing code...
      
      // After loading settings, parse connection string
      if (this.settings.azure_storage_connection) {
        const connStr = this.settings.azure_storage_connection;
        
        // Extract account name
        const nameMatch = connStr.match(/AccountName=([^;]+)/);
        if (nameMatch && nameMatch[1]) {
          this.azureAccountName = nameMatch[1];
        }
        
        // Extract account key
        const keyMatch = connStr.match(/AccountKey=([^;]+)/);
        if (keyMatch && keyMatch[1]) {
          this.azureAccountKey = keyMatch[1];
        }
      }
      
      // Set container values
      this.azureRequestsContainer = this.settings.azure_requests_container || 'requests';
      this.azureResultsContainer = this.settings.azure_results_container || 'results';
    },
    // Add container info to the save data
    saveSettings() {
      // Update the settings with containers before saving
      this.settings.azure_requests_container = this.azureRequestsContainer;
      this.settings.azure_results_container = this.azureResultsContainer;
      
      // Continue with existing save logic...
    }
  }
};
</script>
```

**Potential Issues:**
- Ensure sensitive credentials are properly handled
- Validate connection string format
- Add proper database fields for the new container settings

## Phase 4: Testing and Deployment

### Task 4.1: Set Up Test Environment
1. Create a test installation of the plugin
2. Configure with test TDL credentials
3. Prepare test patient and doctor data

### Task 4.2: Test the Request Flow
1. Create a test lab request
2. Verify HL7 message format
3. Check Azure upload functionality
4. Confirm request status updates

### Task 4.3: Test the Results Flow
1. Manually upload a test result file to Azure
2. Trigger the polling function
3. Verify result processing
4. Check result visualization

### Task 4.4: Security Review
1. Verify credentials are stored securely
2. Check access control for sensitive data
3. Ensure proper input validation

### Task 4.5: Performance Testing
1. Test with large result sets
2. Verify memory usage during processing
3. Check async handling for long-running operations

## Implementation Timeline

| Task | Estimated Time | Dependencies |
|------|----------------|--------------|
| 1.1 Install Azure SDK | 1 hour | None |
| 1.2 Update TDL Settings Model | 2 hours | 1.1 |
| 1.3 Implement HL7 Upload | 4 hours | 1.1, 1.2 |
| 1.4 Implement Result Polling | 4 hours | 1.1, 1.2 |
| 1.5 Create WordPress Cron | 2 hours | 1.4 |
| 2.1 Fix HL7 Message Generation | 3 hours | None |
| 2.2 Implement HL7 Parser | 6 hours | None |
| 3.1 Update Results View | 5 hours | None |
| 3.2 Update TDL Settings UI | 3 hours | None |
| 4.1-4.5 Testing & Deployment | 8 hours | All previous tasks |
| **Total** | **38 hours** | |

## Known Limitations and Considerations

1. **WordPress Cron Reliability**: WordPress cron depends on site traffic to trigger. For a production environment, consider setting up a server cron job that calls the WordPress cron directly.

2. **Azure SDK Requirements**: The Azure SDK requires PHP 7.2+ and several PHP extensions. Verify your hosting environment meets these requirements.

3. **HL7 Format Variations**: Different lab systems may use slightly different HL7 formats. The implementation might need adjustments based on TDL's specific requirements.

4. **Error Handling Strategy**: Determine how to handle partial failures (e.g., one result fails to process but others succeed). Consider implementing a retry mechanism for failed results.

5. **Security Considerations**:
   - Store the Azure access key securely
   - Implement proper access control for lab results
   - Consider encrypting sensitive data in the database

6. **Scaling Considerations**:
   - For high-volume clinics, consider processing results in batches
   - Implement rate limiting for Azure API calls
   - Add monitoring for Azure storage usage

7. **Future Maintenance**:
   - Document the API endpoints and expected formats
   - Create a troubleshooting guide for common issues
   - Set up logging for important operations for audit trails

By following this detailed implementation plan, developers can complete the TDL Labs integration efficiently while addressing potential issues proactively.