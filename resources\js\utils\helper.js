export const disabledButton = (
  ele,
  removeClass = "",
  addClass = "fa fa-sync fa-spin"
) => {
  ele
    .prop("disabled", true)
    .find("i")
    .removeClass(removeClass)
    .addClass(removeClass);
};

export const enabledButton = (
  ele,
  addClass = "",
  removeClass = "fa fa-sync fa-spin"
) => {
  ele
    .prop("disabled", false)
    .find("i")
    .removeClass(removeClass)
    .addClass(addClass);
};

export const formatDate = (dateString) => {  // Changed parameter name from message to dateString
  if (!dateString) return "";
  let date = new Date(dateString);
  return date.toLocaleDateString("en-GB"); // Will output DD/MM/YYYY
};