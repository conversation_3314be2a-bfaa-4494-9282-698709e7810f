<template>
  <div id="doctor_profile">
    <!-- Loader -->
    <div
      v-if="formLoader"
      class="flex justify-center items-center min-h-screen"
    >
      <loader-component-2></loader-component-2>
    </div>

    <div v-else class="flex flex-col lg:flex-row gap-6">
      <!-- Review Modal -->
      <AppointmentReviewCard
        v-if="appointmentReviewModal"
        :appointmentDetails="{}"
        :doctor_id="select_doctor_id"
        :appointmentReviewModal="appointmentReviewModal"
        @closeModal="appointmentReviewModal = false"
      />

      <!-- Main Content -->
      <div class="lg:w-3/4">
        <div class="bg-white rounded-lg shadow">
          <!-- Tabs -->
          <div class="border-b border-gray-200">
            <div class="px-4">
              <nav class="flex space-x-4">
                <button
                  @click="viewMode = 'editProfile'"
                  :class="[
                    'px-3 py-2 text-sm font-medium rounded-t-lg',
                    viewMode === 'editProfile'
                      ? 'border-b-2 border-primary-500 text-primary-600'
                      : 'text-gray-500 hover:text-gray-700',
                  ]"
                >
                  {{ formTranslation.patient.edit_profile }}
                </button>
                
                <button
                  @click="viewMode = 'signatureRXSettings'"
                  :class="[
                    'px-3 py-2 text-sm font-medium rounded-t-lg',
                    viewMode === 'signatureRXSettings'
                      ? 'border-b-2 border-primary-500 text-primary-600'
                      : 'text-gray-500 hover:text-gray-700',
                  ]"
                >
                  SignatureRX Settings
                </button>

                <template
                  v-if="
                    userData.addOns.kiviPro && doctorData.custom_forms?.length
                  "
                >
                  <button
                    v-for="(custom_form_data, key) in doctorData.custom_forms"
                    :key="key"
                    @click="viewMode = 'custom_form_' + custom_form_data.id"
                    :class="[
                      'px-3 py-2 text-sm font-medium rounded-t-lg',
                      viewMode === 'custom_form_' + custom_form_data.id
                        ? 'border-b-2 border-primary-500 text-primary-600'
                        : 'text-gray-500 hover:text-gray-700',
                    ]"
                  >
                    {{ custom_form_data.name?.text || "" }}
                  </button>
                </template>
              </nav>
            </div>
          </div>

          <!-- Form Content -->
          <div 
            v-if="viewMode === 'signatureRXSettings'"
            class="p-6"
          >
            <h6 class="text-sm font-medium text-gray-600 mb-4">
              SignatureRX Integration Settings
            </h6>
            
            <div class="mb-6">
              <div
                class="text-sm bg-blue-50 text-blue-800 rounded-lg p-4 mb-6"
                role="alert"
              >
                <div class="flex items-center">
                  <svg
                    class="w-5 h-5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <p>
                    Configure your SignatureRX integration details. These credentials will be used to send prescriptions directly to the pharmacy.
                  </p>
                </div>
              </div>
              
              <div class="grid grid-cols-1 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Integration Code <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    v-model="signatureRXData.integration_code"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="Enter your SignatureRX Integration Code"
                  />
                  <div class="mt-1 text-xs text-gray-500">
                    This code identifies you as an authorized prescriber in the SignatureRX system
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    SignatureRX Clinic ID
                  </label>
                  <input
                    type="text"
                    v-model="signatureRXData.signaturerx_clinic_id"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="Enter your SignatureRX Clinic ID"
                  />
                  <div class="mt-1 text-xs text-gray-500">
                    The clinic ID in the SignatureRX system (leave blank to use the consultation clinic ID)
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    SignatureRX Email
                  </label>
                  <input
                    type="email"
                    v-model="signatureRXData.signaturerx_email"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="Enter your SignatureRX Email"
                  />
                  <div class="mt-1 text-xs text-gray-500">
                    Your email in the SignatureRX system (leave blank to use your WordPress email)
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Default Prescription Action
                  </label>
                  <select
                    v-model="signatureRXData.default_action"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  >
                    <option value="draft">Save as Draft</option>
                    <option value="issueOnly">Issue Only</option>
                    <option value="issueForCollection">Issue for Collection</option>
                    <option value="issueToContact">Issue to Contact</option>
                    <option value="issueForDelivery">Issue for Delivery</option>
                  </select>
                  <div class="mt-1 text-xs text-gray-500">
                    <ul class="list-disc pl-4">
                      <li><strong>Draft</strong>: Save prescription for later</li>
                      <li><strong>Issue Only</strong>: Issue prescription immediately</li>
                      <li><strong>Issue for Collection</strong>: Patient collects from pharmacy</li>
                      <li><strong>Issue to Contact</strong>: Send to patient's contact</li>
                      <li><strong>Issue for Delivery</strong>: Deliver to patient's address</li>
                    </ul>
                  </div>
                </div>
                
                <div class="mt-4">
                  <button
                    type="button"
                    @click="saveSignatureRXSettings"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    :disabled="signatureRXLoading"
                  >
                    <i :class="signatureRXLoading ? 'fa fa-spinner fa-spin mr-2' : 'fas fa-save mr-2'"></i>
                    Save SignatureRX Settings
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <form
            v-if="viewMode === 'editProfile'"
            id="doctorDataForm"
            @submit.prevent="handleSubmit"
            enctype="multipart/form-data"
            :novalidate="true"
            class="p-6"
          >
            <!-- Basic Information -->
            <div class="mb-8">
              <h6 class="text-sm font-medium text-gray-600 mb-4">
                {{ formTranslation.doctor.basic_information }}
              </h6>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- First Name -->
                <div>
                  <label
                    for="first_name"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.fname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="first_name"
                    v-model="doctorData.first_name"
                    :placeholder="formTranslation.doctor.fname_plh"
                    required
                    type="text"
                    :class="[
                      'w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400',
                      submitted && $v.doctorData.first_name.$error
                        ? 'border-red-500'
                        : '',
                    ]"
                  />
                  <div
                    v-if="submitted && !$v.doctorData.first_name.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{ formTranslation.common.fname_required }}
                  </div>
                </div>

                <!-- Last Name -->
                <div>
                  <label
                    for="last_name"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.lname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="last_name"
                    v-model="doctorData.last_name"
                    :placeholder="formTranslation.doctor.lname_placeholder"
                    required
                    type="text"
                    :class="[
                      'w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400',
                      submitted && $v.doctorData.last_name.$error
                        ? 'border-red-500'
                        : '',
                    ]"
                  />
                  <div
                    v-if="submitted && !$v.doctorData.last_name.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{ formTranslation.common.lname_required }}
                  </div>
                </div>

                <!-- Email -->
                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.email_address }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    v-model="doctorData.user_email"
                    :placeholder="formTranslation.doctor.address_placeholder"
                    required
                    type="email"
                    :class="[
                      'w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400',
                      submitted && $v.doctorData.user_email.$error
                        ? 'border-red-500'
                        : '',
                    ]"
                  />
                  <div
                    v-if="submitted && !$v.doctorData.user_email.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{ formTranslation.common.email_required }}
                  </div>
                </div>

                <!-- Phone -->
                <div>
                  <label
                    for="telephone_no"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <VuePhoneNumberInput
                    v-model="doctorData.mobile_number"
                    id="telephone_no"
                    clearable
                    :default-country-code="defaultCountryCode"
                    @update="contactUpdateHandaler"
                    no-example
                    class="phone-input"
                  />
                  <div
                    v-if="submitted && !$v.doctorData.mobile_number.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{ formTranslation.common.contact_num_required }}
                  </div>
                </div>

                <!-- Date of Birth -->
                <div>
                  <label
                    for="doctor_birthdate"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.dob }}
                  </label>
                  <input
                    type="date"
                    v-model="doctorData.dob"
                    name="doc_birthdate"
                    id="doc_birthdate"
                    :max="new Date().toISOString().slice(0, 10)"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  />
                </div>

                <!-- Gender -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.gender }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                      <input
                        type="radio"
                        v-model="doctorData.gender"
                        value="male"
                        class="form-radio text-primary-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.male
                      }}</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input
                        type="radio"
                        v-model="doctorData.gender"
                        value="female"
                        class="form-radio text-primary-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.female
                      }}</span>
                    </label>
                    <label
                      v-if="defaultUserRegistrationFormSettingData === 'on'"
                      class="inline-flex items-center"
                    >
                      <input
                        type="radio"
                        v-model="doctorData.gender"
                        value="other"
                        class="form-radio text-primary-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.other
                      }}</span>
                    </label>
                  </div>
                  <div
                    v-if="submitted && !$v.doctorData.gender.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{ formTranslation.common.gender_required }}
                  </div>
                </div>

                <!-- Specialization -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.patient_front_widget.specialization }}
                    <span class="text-red-500">*</span>
                  </label>
                  <multi-select
                    v-model="doctorData.specialties"
                    deselect-label=""
                    select-label=""
                    :tag-placeholder="formTranslation.doctor.tag_doc_sp_plh"
                    id="specialization"
                    :placeholder="formTranslation.doctor.search_placeholder"
                    :class="[
                      'w-full',
                      submitted && $v.doctorData.specialties.$error
                        ? 'border-red-500'
                        : '',
                    ]"
                    label="label"
                    track-by="id"
                    :options="doctorSpecialization"
                    :multiple="true"
                    :taggable="true"
                    :loading="specializationMultiselectLoader"
                    :disabled="specializationMultiselectLoader"
                  >
                  </multi-select>
                  <div
                    v-if="submitted && !$v.doctorData.specialties.required"
                    class="mt-1 text-sm text-red-600"
                  >
                    {{ formTranslation.doctor.doctor_specialization_required }}
                  </div>
                </div>

                <!-- Registration Number -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    <span v-if="doctorData.registration_prefix">{{ doctorData.registration_prefix }}</span><span v-else>SET PREFIX</span> No.
                  </label>
                  <input
                    id="gmc_no"
                    v-model="doctorData.gmc_no"
                    :placeholder="'Enter ' + (doctorData.registration_prefix || 'Set Prefix') + ' No.'"
                    type="text"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  />
                </div>
                
                <!-- Registration Prefix -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Registration Prefix
                  </label>
                  <input
                    id="registration_prefix"
                    v-model="doctorData.registration_prefix"
                    placeholder="Enter Registration Prefix (e.g., GMC, NMC)"
                    type="text"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 uppercase"
                  />
                  <div class="text-xs text-gray-500 mt-1">
                    This prefix will be displayed before "No." (Will appear in uppercase)
                  </div>
                </div>

                <!-- Language Selection -->
                <div
                  v-if="
                    $store.state.userDataModule.user.addOns.kiviPro !== false
                  "
                >
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.choose_language }}
                  </label>
                  <multi-select
                    deselect-label=""
                    select-label=""
                    v-model="doctorData.choose_language"
                    id="choose_language"
                    :placeholder="formTranslation.common.choose_language"
                    label="label"
                    track-by="lang"
                    :options="kc_available_translations"
                    :multiple="false"
                    class="w-full"
                  />
                </div>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="mb-8">
              <h6 class="text-sm font-medium text-gray-600 mb-4">
                {{ formTranslation.common.contact_info }}
              </h6>

              <div class="space-y-6">
                <!-- Address -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.address }}
                  </label>
                  <textarea
                    v-model="doctorData.address"
                    :placeholder="formTranslation.doctor.plh_clinic_address"
                    rows="3"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  ></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <!-- City -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      {{ formTranslation.common.city }}
                    </label>
                    <input
                      v-model="doctorData.city"
                      :placeholder="formTranslation.doctor.plh_enter_city"
                      type="text"
                      class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                  </div>

                  <!-- Country -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      {{ formTranslation.common.country }}
                    </label>
                    <input
                      v-model="doctorData.country"
                      :placeholder="
                        formTranslation.doctor.plh_enter_country_name
                      "
                      type="text"
                      class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                  </div>

                  <!-- Postal Code -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      {{ formTranslation.common.postal_code }}
                    </label>
                    <input
                      v-model="doctorData.postal_code"
                      :placeholder="
                        formTranslation.doctor.plh_enter_postal_code
                      "
                      type="text"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Qualifications -->
            <div class="bg-white rounded-lg shadow mb-8">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                  {{ formTranslation.doctor.qualification_information }}
                </h3>
              </div>

              <div class="p-6">
                <!-- Qualifications Table -->
                <div class="mb-6 overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.common.sr_no }}
                        </th>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.doctor.degree }}
                        </th>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.doctor.university }}
                        </th>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.doctor.year }}
                        </th>
                        <th
                          scope="col"
                          class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {{ formTranslation.common.action }}
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <template
                        v-if="
                          doctorData.qualifications !== undefined &&
                          doctorData.qualifications !== null
                        "
                      >
                        <tr
                          v-for="(
                            qualification, index
                          ) in doctorData.qualifications"
                          :key="index"
                        >
                          <td
                            class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                          >
                            {{ ++index }}
                          </td>
                          <td
                            class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                          >
                            {{ qualification.degree }}
                          </td>
                          <td
                            class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                          >
                            {{ qualification.university }}
                          </td>
                          <td
                            class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                          >
                            {{ qualification.year }}
                          </td>
                          <td
                            class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                          >
                            <div class="flex space-x-2">
                              <button
                                type="button"
                                @click="editQualification(index)"
                                class="text-primary-600 hover:text-primary-900"
                                :title="formTranslation.common.edit"
                              >
                                <svg
                                  class="h-5 w-5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                  />
                                </svg>
                              </button>
                              <button
                                type="button"
                                @click="deleteQualification(index)"
                                class="text-red-600 hover:text-red-900"
                                :title="formTranslation.common.delete"
                              >
                                <svg
                                  class="h-5 w-5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                  />
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </template>
                      <tr v-else>
                        <td colspan="5" class="px-6 py-4 text-center text-sm">
                          <h4 class="text-primary-600 font-medium">
                            {{ formTranslation.common.no_records_found }}
                          </h4>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- Add/Edit Qualification Form -->
                <div class="space-y-6">
                  <h6 class="text-sm font-medium text-gray-600">
                    {{ qualificationTitle }}
                  </h6>

                  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Degree -->
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 mb-1"
                      >
                        {{ formTranslation.doctor.degree }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        v-model="qualification.degree"
                        :placeholder="formTranslation.doctor.plh_enter_degree"
                        type="text"
                        :class="[
                          'w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400',
                          qualificationSubmitted &&
                          $v.qualification.degree.$error
                            ? 'border-red-500'
                            : '',
                        ]"
                      />
                      <div
                        v-if="
                          qualificationSubmitted &&
                          !$v.qualification.degree.required
                        "
                        class="mt-1 text-sm text-red-600"
                      >
                        {{ formTranslation.doctor.degree_required }}
                      </div>
                    </div>

                    <!-- University -->
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 mb-1"
                      >
                        {{ formTranslation.doctor.university }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        v-model="qualification.university"
                        :placeholder="
                          formTranslation.doctor.plh_enter_university
                        "
                        type="text"
                        :class="[
                          'w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400',
                          qualificationSubmitted &&
                          $v.qualification.university.$error
                            ? 'border-red-500'
                            : '',
                        ]"
                      />
                      <div
                        v-if="
                          qualificationSubmitted &&
                          !$v.qualification.university.required
                        "
                        class="mt-1 text-sm text-red-600"
                      >
                        {{ formTranslation.doctor.university_required }}
                      </div>
                    </div>

                    <!-- Year -->
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 mb-1"
                      >
                        {{ formTranslation.doctor.year }}
                        <span class="text-red-500">*</span>
                      </label>
                      <select
                        v-model="qualification.year"
                        :class="[
                          'w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400',
                          qualificationSubmitted && $v.qualification.year.$error
                            ? 'border-red-500'
                            : '',
                        ]"
                      >
                        <option value="">
                          {{ formTranslation.doctor.select_year }}
                        </option>
                        <option
                          v-for="(year, index) in qualificationYears"
                          :value="year"
                          :key="index"
                        >
                          {{ year }}
                        </option>
                      </select>
                      <div
                        v-if="
                          qualificationSubmitted &&
                          !$v.qualification.year.required
                        "
                        class="mt-1 text-sm text-red-600"
                      >
                        {{ formTranslation.doctor.year_required }}
                      </div>
                    </div>
                  </div>

                  <!-- Add/Save Qualification Button -->
                  <div class="flex justify-end">
                    <button
                      type="button"
                      @click="addQualification"
                      class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      v-html="qualificationBtn"
                    ></button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Signature Section -->
            <div class="mb-8">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.signature }}
                  </label>
                  <div
                    id="signaturePreview"
                    class="mb-4 bg-gray-100 rounded-lg"
                    :style="'background-image: url(' + signaturePreview + ');'"
                  ></div>
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      @click="uploadSignature"
                      class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      {{ formTranslation.common.upload }}
                    </button>
                    <button
                      type="button"
                      @click="removeSignature"
                      class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      {{ formTranslation.common.remove }}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Custom Fields Section -->
            <div v-if="showCustomField" class="mb-8">
              <h6 class="text-sm font-medium text-gray-600 mb-4">
                {{ formTranslation.doctor.extra_detail }}
              </h6>
              <edit-custom-fields
                module_type=""
                :module_id="String(doctorData.ID)"
                @bindCustomField="getCustomFieldsValues"
                :fieldsValue="custom_fields !== undefined ? custom_fields : []"
                :customFieldsObj="
                  custom_fields !== undefined ? custom_fields : []
                "
                @requiredCustomField="getRequireFields"
              />
            </div>

            <!-- Form Submit -->
            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="loading"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg
                  v-if="loading"
                  class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>
                  <i class="fa fa-save mr-2"></i>
                  {{
                    loading
                      ? formTranslation.common.loading
                      : formTranslation.common.save
                  }}
                </span>
              </button>
            </div>
          </form>

          <!-- Custom Forms Content -->
          <div
            v-for="(custom_form_data, key) in doctorData.custom_forms"
            :key="key"
            v-if="
              doctorData.custom_forms?.length &&
              viewMode === 'custom_form_' + custom_form_data.id
            "
            class="p-6"
          >
            <CustomForm
              :data="customFormDataUpdate(custom_form_data, doctorData.ID)"
              :viewMode="false"
              :customFormModal="
                viewMode === 'custom_form_' + custom_form_data.id
              "
              :useModal="false"
            />
          </div>
        </div>
      </div>

      <!-- Profile Card -->
      <div class="lg:w-1/4">
        <div class="bg-white rounded-lg shadow">
          <div class="flex justify-center pt-6">
            <div class="relative">
              <div
                class="w-32 h-32 rounded-full bg-cover bg-center border-4 border-white shadow"
                :style="'background-image: url(' + profileImage + ');'"
              ></div>
              <button
                @click="uploadProfile"
                class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50"
              >
                <i class="fas fa-pencil-alt"></i>
              </button>
            </div>
          </div>

          <div class="p-6 text-center">
            <h5 class="text-xl font-semibold">
              {{ doctorData.display_name }}
            </h5>
            <div class="text-gray-600 mt-2">
              {{ doctorData.user_email }}
            </div>
            <div class="mt-4" v-if="doctorData.specialties">
              {{ doctorData.specialties | clinicSpecialityFormat }}
            </div>
          </div>

          <!-- Ratings -->
          <div
            v-if="userData.addOns.kiviPro && doctorData.rating > 0"
            class="mt-4"
          >
            <!-- Star Rating Component -->
            <div class="flex justify-center">
              <i class="kivi-star" :data-star="doctorData.rating"></i>
            </div>

            <div class="mt-2 text-sm text-gray-600">
              {{
                doctorData.total_rating +
                " " +
                formTranslation.appointments.ratings
              }}
            </div>

            <button
              @click="
                select_doctor_id = doctorData.ID;
                appointmentReviewModal = true;
              "
              class="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-black hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {{
                formTranslation.appointments.ratings +
                " " +
                formTranslation.common.detail
              }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import CustomForm from "../CustomForm/Form.vue";
import {
  required,
  numeric,
  requiredIf,
  alpha,
  minLength,
  maxLength,
  minValue,
  maxValue,
} from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import vueSignature from "vue-signature";
import AppointmentReviewCard from "../../components/appointment/AppointmentReviewCard";
import {
  alphaSpace,
  minimumValue,
  objToTime,
  phoneNumber,
  postalCode,
  validateForm,
  emailValidate,
} from "../../config/helper";
export default {
  components: {
    vueSignature,
    AppointmentReviewCard,
    VuePhoneNumberInput,
    CustomForm,
  },
  data: () => {
    return {
      isEditProfile: false,
      signaturePreview: false,
      doctorData: {},
      loading: false,
      submitted: false,
      qualificationSubmitted: false,
      cardTitle: "Edit Profile",
      doctorTimeSlot: "",
      buttonText: '<i class="fa fa-plus"></i> Add',
      editProfileBtnText: '<i class="fa fa-pen-fancy"></i> Edit Profile',
      qualification: {},
      qualificationTitle: "Add Qualification",
      qualificationBtn: '<i class="fa fa-plus"></i> Add qualification',
      qualificationEdit: false,
      qualificationYears: [],
      price_type: ["range", "fixed"],
      timeSlots: [],
      customFields: [],
      profileImage: "",
      enableTeleMed: false,
      formLoader: true,
      signatureOption: {
        penColor: "rgb(0, 0, 0)",
        backgroundColor: "rgb(220,220,220)",
      },
      showCanvas: false,
      showCustomField: false,
      custom_fields: [],
      select_doctor_id: 0,
      appointmentReviewModal: false,
      defaultCountryCode: null,
      requiredFields: [],
      defaultUserRegistrationFormSettingData: "on",
      viewMode: "editProfile",
      // SignatureRX data
      signatureRXData: {
        integration_code: "",
        signaturerx_clinic_id: "",
        signaturerx_email: "",
        default_action: "issueOnly", // Default to issueOnly
        doctor_id: ""
      },
      signatureRXLoading: false,
    };
  },
  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();
    this.doctorData = this.defaultDoctorData();
    this.qualification = this.defaultQualification();
    this.qualificationYears = this.getQualificationYear();
    this.init();
    this.profileImage =
      window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png";
  },
  validations: {
    doctorData: {
      first_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(15),
      },
      last_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(15),
      },
      user_email: {
        required,
        emailValidate,
      },
      gender: {
        required,
      },
      mobile_number: {
        required,
        // phoneNumber,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      specialties: { required },
      // state: {
      //     alphaSpace,
      //     maxLength: maxLength(30)
      // },
      // city: {
      //     alphaSpace,
      //     maxLength: maxLength(30)
      // },
      // country: {
      //     alphaSpace,
      //     maxLength: maxLength(30)
      // },
      // postal_code: {
      //     postalCode,
      //     maxLength: maxLength(12)
      // },
      // dob: {required}
    },
    qualification: {
      degree: { required },
      university: { required },
      year: { required },
    },
  },
  filters: {
    clinicSpecialityFormat: function (Speciality) {
      let doctors = Speciality;
      let result = [];
      if (doctors != undefined && doctors.length > 0) {
        doctors.forEach(function (doctor) {
          result.push(doctor.label);
        });
        let implodedArray = result.join(" , ");
        return implodedArray;
      } else {
        return "No";
      }
    },
    dateFormat: function (date) {
      return moment(date).format("YYYY-MM-DD");
    },
  },
  methods: {
    contactUpdateHandaler: function (val) {
      this.doctorData.country_code = val.countryCode;
      this.doctorData.country_calling_code = val.countryCallingCode;
    },
    init: function () {
      this.qualificationTitle = this.formTranslation.doctor.add_qualification;
      this.qualificationBtn =
        '<i class="fa fa-plus"></i> ' +
        this.formTranslation.doctor.add_qualification;

      if (this.$store.state.userDataModule.user.ID !== undefined) {
        let profileID = this.$store.state.userDataModule.user.ID;
        this.editProfileData(profileID);
      } else {
        this.$store.dispatch("userDataModule/fetchUserData", {});
        setTimeout(() => {
          let profileID = this.$store.state.userDataModule.user.ID;
          this.editProfileData(profileID);
        }, 1000);
      }

      this.getTimeSlots();
    },
    handleTimeSlotChange: function () {
      if (this.doctorData.time_slot !== parseInt(this.doctorTimeSlot)) {
        displayAlert(
          "Warning!",
          this.formTranslation.doctor.change_time_will_effect
        );
      }
    },
    getCustomFieldsValues: function (fieldsObj) {
      if (!fieldsObj || fieldsObj === undefined) {
        return false;
      }
      this.doctorData.custom_fields = fieldsObj;
    },
    addQualification: function () {
      this.qualificationSubmitted = true;
      this.$v.qualification.$touch();
      if (this.$v.qualification.$invalid) {
        return;
      }
      if (!this.qualificationEdit) {
        if (this.doctorData.qualifications === undefined) {
          this.doctorData.qualifications = [];
        }
        this.doctorData.qualifications.push(this.qualification);
      } else {
        this.qualificationEdit = false;
      }
      this.qualificationTitle = this.formTranslation.doctor.add_qualification;
      this.qualificationBtn =
        '<i class="fa fa-plus"></i> ' +
        this.formTranslation.doctor.add_qualification;
      this.qualification = this.defaultQualification();
      this.qualificationSubmitted = false;
    },
    getTimeSlot: function (startTime, endTime, doctor) {
      var timeSlotDiff =
        doctor !== null && doctor.timeSlot !== undefined ? doctor.timeSlot : "";
      var newTimeSlot = "";
      let slots = [];

      if (
        startTime.HH !== "" &&
        startTime.mm !== "" &&
        endTime.HH !== "" &&
        endTime.mm !== "" &&
        timeSlotDiff !== ""
      ) {
        let sessionOneStartTime = objToTime(startTime);
        let sessionOneEndTime = objToTime(endTime);

        let timeDiff = sessionOneEndTime.diff(sessionOneStartTime, "minutes");

        let loopCount = Math.ceil(timeDiff / timeSlotDiff);

        for (let i = 0; i <= loopCount; i++) {
          if (i === 0) {
            newTimeSlot = sessionOneStartTime.format("HH:mm");
          } else {
            newTimeSlot = moment(newTimeSlot, "HH:mm")
              .add(timeSlotDiff, "minutes")
              .format("HH:mm");
          }

          let temp = {
            time: newTimeSlot,
            isValid: true,
            timeSlotDiff: timeSlotDiff,
          };

          if (moment(newTimeSlot, "HH:mm").isAfter(sessionOneEndTime)) {
            let timeDiff = moment(newTimeSlot, "HH:mm").diff(
              sessionOneEndTime,
              "minutes"
            );
            temp.isValid = false;
            temp.timeSlotDiff = Math.abs(timeSlotDiff - timeDiff);
          }

          slots.push(temp);
        }
      }
      return slots;
    },
    editQualification: function (index) {
      this.qualificationTitle = this.formTranslation.doctor.edit_qualification;
      this.qualificationEdit = true;
      this.qualificationBtn =
        '<i class="fa fa-save"></i> ' +
        this.formTranslation.doctor.save_qualification;
      this.qualification = this.doctorData.qualifications[index - 1];
    },
    deleteQualification: function (index) {
      if (this.doctorData.qualifications[index - 1] !== undefined) {
        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.clinic_schedule.dt_press_dlt,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              this.doctorData.qualifications.splice(index - 1, 1);
            }
          });
      }
    },
    defaultDoctorData: function () {
      return {
        first_name: "",
        last_name: "",
        username: "",
        user_email: "",
        user_pass: "",
        country_calling_code: "",
        country_code: "",
        mobile_number: "",
        gender: this.formTranslation.common.male,
        dob: "",
        about_me: "",
        address: "",
        city: "",
        state: "",
        country: "",
        postal_code: "",
        specialties: [],
        price_type: "range",
        price: 0,
        minPrice: 0,
        maxPrice: 0,
        qualifications: [],
        time_slot: 5,
        user_status: 0,
        signature: "",
        rating: 0,
        total_rating: 0,
        profile_image: "",
      };
    },
    defaultQualification: function () {
      return {
        degree: "",
        university: "",
        year: "",
        file: "",
      };
    },
    handleSubmit: function () {
      this.loading = true;

      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      this.$nextTick(() => {
        if (
          document.querySelector(".is-invalid") !== null &&
          document.querySelector(".is-invalid") !== undefined
        ) {
          document
            .querySelector(".is-invalid")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        } else if (
          document.querySelector(".invalid-feedback") !== null &&
          document.querySelector(".invalid-feedback") !== undefined
        ) {
          document
            .querySelector(".invalid-feedback")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        }
      });
      if (this.$v.doctorData.$invalid) {
        this.loading = false;
        return;
      }

      if (this.requiredFields.length > 0) {
        this.loading = false;
        displayErrorMessage(
          this.formTranslation.common.all_required_field_validation
        );
        return;
      }

      if (validateForm("doctorDataForm")) {
        post("doctor_save", this.doctorData)
          .then((response) => {
            this.loading = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              // this.doctorData.dob =  new Date(moment(this.doctorData.dob ).format("YYYY-MM-DD"));
              if (response.data.choose_language_updated) {
                this.$store.dispatch(
                  "staticDataModule/refreshDashboardLocale",
                  { self: this }
                );
              }
              displayMessage(response.data.message);
              this.isEditProfile = false;
            } else {
              // this.doctorData.dob =  new Date(moment(this.doctorData.dob ).format("YYYY-MM-DD"));
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            // this.doctorData.dob =  new Date(moment(this.doctorData.dob ).format("YYYY-MM-DD"));
            console.log(error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    getQualificationYear: function () {
      let years = [];
      let d = new Date();
      let x = d.getFullYear();
      for (let i = 1940; i <= x; i++) {
        years.push(i);
      }

      return years.reverse();
    },
    uploadSignature() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.signaturePreview = attachment.url;
        _this.doctorData.signature = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    removeSignature() {
      this.signaturePreview = "";
      this.doctorData.signature = "";
    },
    editProfileData: function (editId) {
      if (editId !== undefined) {
        this.cardTitle = this.formTranslation.doctor.edit_profile;
        this.buttonText =
          '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
        this.formLoader = true;
        get("doctor_edit", {
          id: editId,
        })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.doctorData = response.data.data;
              if (
                response.data.data.country_calling_code !== "" &&
                response.data.data.country_calling_code !== undefined
              ) {
                this.defaultCountryCode = response.data.data.country_code;
              }
              this.doctorTimeSlot = this.doctorData.time_slot;
              this.isEditProfile = false;
              // this.doctorData.dob = new Date(this.doctorData.dob + ' 00:00');
              if (this.doctorData.user_profile) {
                // this.doctorData.profile_photo = this.doctorData.user_profile
                this.profileImage = this.doctorData.user_profile;
              }
              if (
                this.doctorData.signature ||
                this.doctorData.signature_preview
              ) {
                this.signaturePreview = this.doctorData.signature_preview;
              }
              if (
                response.data.custom_filed !== undefined &&
                response.data.custom_filed.length > 0
              ) {
                this.showCustomField = true;
                this.custom_fields = response.data.custom_filed;
              }
              this.doctorData.choose_language =
                this.kc_available_translations.find(
                  (el) => el.lang === response.data.data.choose_language
                );
            }
            this.formLoader = false;
            this.showCanvas = true;
          })
          .catch((error) => {
            this.formLoader = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      } else {
        displayErrorMessage(this.formTranslation.widgets.login_user_not_found);
      }
    },
    getTimeSlots: function () {
      let slot = 5;
      for (let i = 0; i < 12; i++) {
        if (slot <= 60) {
          this.timeSlots.push(slot);
        }
        slot = slot + 5;
      }
    },
    handleResetCharge: function () {
      if (this.doctorData.price_type === "range") {
        this.doctorData.price = 0;
      } else {
        this.doctorData.minPrice = 0;
        this.doctorData.maxPrice = 0;
      }
    },
    uploadProfile() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.profileImage = attachment.url;
        _this.doctorData.profile_image = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    getRequireFields: function (validateRequired) {
      this.requiredFields = validateRequired;
    },
    getCountryCodeData: function () {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultCountryCode = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getRequireFields: function (validateRequired) {
      this.requiredFields = validateRequired;
    },
    getUserRegistrationFormData: function () {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    customFormDataUpdate(custom_form_data, id) {
      custom_form_data.module_id = id;
      return custom_form_data;
    },
    
    // SignatureRX methods
    getSignatureRXSettings() {
      // Fetch SignatureRX settings when opening the tab
      if (this.viewMode === 'signatureRXSettings' && this.doctorData.ID) {
        get("signaturerx_get_doctor_settings", { doctor_id: this.doctorData.ID })
          .then((response) => {
            if (response.data.status !== undefined && response.data.status === true) {
              this.signatureRXData.integration_code = response.data.data.integration_code || "";
              this.signatureRXData.signaturerx_clinic_id = response.data.data.signaturerx_clinic_id || "";
              this.signatureRXData.signaturerx_email = response.data.data.signaturerx_email || "";
              this.signatureRXData.default_action = response.data.data.default_action || "issueOnly";
              this.signatureRXData.doctor_id = this.doctorData.ID;
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.common.internal_server_error);
          });
      }
    },
    
    saveSignatureRXSettings() {
      // Validate the integration code is required
      if (!this.signatureRXData.integration_code) {
        displayErrorMessage("Integration Code is required");
        return;
      }
      
      this.signatureRXLoading = true;
      this.signatureRXData.doctor_id = this.doctorData.ID;
      
      // Prepare data for API
      const dataToSend = {
        doctor_id: this.signatureRXData.doctor_id,
        integration_code: this.signatureRXData.integration_code,
        signaturerx_clinic_id: this.signatureRXData.signaturerx_clinic_id,
        signaturerx_email: this.signatureRXData.signaturerx_email,
        default_action: this.signatureRXData.default_action
      };
      
      console.log("Saving SignatureRX settings:", dataToSend);
      
      post("signaturerx_save_doctor_settings", dataToSend)
        .then((response) => {
          this.signatureRXLoading = false;
          console.log("SignatureRX settings response:", response);
          
          if (response.data.status !== undefined && response.data.status === true) {
            displayMessage(response.data.message || "SignatureRX settings saved successfully");
          } else {
            displayErrorMessage(response.data.message || "Error saving SignatureRX settings");
          }
        })
        .catch((error) => {
          console.log("Error saving SignatureRX settings:", error);
          this.signatureRXLoading = false;
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    doctorSpecialization() {
      return this.$store.state.staticDataModule.static_data.specialization;
    },
    specializationMultiselectLoader() {
      return this.$store.state.staticDataModule.static_data_loader;
    },
    kc_available_translations() {
      return this.$store.state.userDataModule.user.kc_available_translations;
    },
  },
  
  // Watch for viewMode changes to load SignatureRX settings
  watch: {
    viewMode(newVal) {
      if (newVal === 'signatureRXSettings') {
        this.getSignatureRXSettings();
      }
    }
  },
};
</script>
<style>
#doctor_profile [type="date"] {
  background: #fff
    url(https://cdn1.iconfinder.com/data/icons/cc_mono_icon_set/blacks/16x16/calendar_2.png)
    97% 50% no-repeat;
}
#doctor_profile [type="date"]::-webkit-inner-spin-button {
  display: none;
}
#doctor_profile [type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}
#doctor_profile label {
  display: block;
}
#doc_birthdate {
  border: 1px solid #c4c4c4;
  border-radius: 5px;
  background-color: #fff;
  padding: 3px 5px;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.1);
  /* width: 190px; */
  width: 100%;
  height: 45px;
  color: #8c9cad;
}
#doc_birthdate ::placeholder {
  color: #8c9cad;
}

#doctor_profile .nav.nav-tabs {
  margin-bottom: 1rem;
}
#doctor_profile .nav-tabs .nav-link.active {
  color: var(--primary);
  /* border-top-color: var(--primary);
    border-left-color: var(--primary);
    border-right-color: var(--primary); */
}
#doctor_profile .nav-tabs .nav-link {
  font-size: 1rem;
  font-weight: 400;
  background-color: #fff;
  border-bottom-color: #dee2e6;
}

#signaturePreview {
  background-size: contain;
  background-repeat: no-repeat;
  min-height: 100px;
  background-color: rgb(197, 197, 197);
  margin-bottom: 1em;
}
</style>
