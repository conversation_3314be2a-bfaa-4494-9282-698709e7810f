<?php

namespace App\models;

use App\baseClasses\KCModel;
class KCNotification extends KCModel {

    private $db;

    public function __construct() {
        parent::__construct('notifications');

    }

    /**
     * Create a new notification
     *
     * @param array $data Notification data
     * @return int|bool ID of the new notification or false on failure
     */
    public function createNotification($data) {
        return $this->db->insert($data);
    }

    /**
     * Update an existing notification
     *
     * @param int $id Notification ID
     * @param array $data Updated notification data
     * @return bool Success or failure
     */
    public function updateNotification($id, $data) {
        return $this->db->update($id, $data);
    }

    /**
     * Delete a notification
     *
     * @param int $id Notification ID
     * @return bool Success or failure
     */
    public function deleteNotification($id) {
        return $this->db->delete($id);
    }

    /**
     * Get a notification by ID
     *
     * @param int $id Notification ID
     * @return object|null Notification object or null if not found
     */
    public function getNotification($id) {
        return $this->db->get($id);
    }

    /**
     * Get all notifications for a user
     *
     * @param int $user_id User ID
     * @param array $args Additional arguments
     * @return array Array of notification objects
     */
    public function getUserNotifications($user_id, $args = []) {
        return $this->db->get_user_notifications($user_id, $args);
    }

    /**
     * Count notifications for a user
     *
     * @param int $user_id User ID
     * @param bool $only_unread Count only unread notifications
     * @return int Count of notifications
     */
    public function countUserNotifications($user_id, $only_unread = false) {
        return $this->db->count_user_notifications($user_id, $only_unread);
    }

    /**
     * Mark a notification as read
     *
     * @param int|array $notification_ids Notification ID or array of IDs
     * @param int $user_id User ID (optional, for verification)
     * @return bool Success or failure
     */
    public function markAsRead($notification_ids, $user_id = null) {
        return $this->db->mark_as_read($notification_ids, $user_id);
    }

    /**
     * Mark all notifications as read for a user
     *
     * @param int $user_id User ID
     * @return bool Success or failure
     */
    public function markAllAsRead($user_id) {
        return $this->db->mark_all_as_read($user_id);
    }

    /**
     * Delete old notifications
     *
     * @param int $days_old Delete notifications older than this many days
     * @return int Number of deleted notifications
     */
    public function cleanupOldNotifications($days_old = 30) {
        return $this->db->delete_old_notifications($days_old);
    }
}