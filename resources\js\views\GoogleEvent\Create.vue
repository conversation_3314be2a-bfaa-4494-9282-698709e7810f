<template>
  <div class="w-full">
    <!-- Loader Section -->
    <div v-if="formLoader" class="w-full flex justify-center items-center min-h-[200px]">
      <loader-component-2></loader-component-2>
    </div>
    
    <div v-else class="w-full">
      <div class="w-full">
        <!-- Overlay wrapper -->
        <div class="relative">
          <!-- Overlay message for non-pro users -->
          <div v-if="userData.addOns.kiviPro != true" 
               class="absolute inset-0 bg-white bg-opacity-90 z-50 flex items-center justify-center">
            <overlay-message addon_type="pro"></overlay-message>
          </div>

          <form id="doctorDataForm" class="w-full" :novalidate="true">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
              <!-- Header -->
              <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="flex justify-between items-center">
                  <div class="flex items-center space-x-2">
                    <h2 class="text-xl font-semibold text-gray-800">
                      {{ formTranslation.common.google_event_template }}
                    </h2>
                    <a v-if="request_status == 'off'" 
                       href="https://apps.medroid.ai/docs/product/kivicare/google-calendar/google-event-template/" 
                       target="_blank"
                       class="text-gray-500 hover:text-gray-700">
                      <i class="fa fa-question-circle"></i>
                    </a>
                  </div>
                </div>
              </div>

              <!-- Form Content -->
              <div class="p-6">
                <div v-for="(item, index) in googleEventList" :key="index" 
                     class="mb-6 last:mb-0">
                  <div class="space-y-4">
                    <!-- Event Title Input -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{formTranslation.google_event.google_event_title}}
                      </label>
                      <input
                        type="text"
                        v-model="item.post_title"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <!-- Event Description Editor -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{formTranslation.google_event.google_event_desc}}
                      </label>
                      <vue-editor 
                        :editorToolbar="customToolbar" 
                        v-model="item.post_content"
                        class="min-h-[200px] border border-gray-300 rounded-md"
                      ></vue-editor>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Footer Actions -->
              <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div class="flex justify-end space-x-3">
                  <button 
                    v-if="!loading" 
                    @click="saveGoogleEventTemplate"
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <i class="fa fa-save mr-2"></i>
                    {{formTranslation.common.save}}
                  </button>
                  <button 
                    v-else 
                    type="submit" 
                    disabled
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 opacity-75 cursor-not-allowed"
                  >
                    <i class="fa fa-sync fa-spin mr-2"></i>
                    {{formTranslation.common.loading}}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {post,get} from "../../config/request";
export default {
  data: () => {
    return {
      googleEventList:[],
      eventTitle: '',
      request_status:'off',
      loading: false,
      templateSaveRequest: {
        ID: 0,
        post_content: '',
      },
      formLoader:true,
      customToolbar: [[{
        header: [false, 1, 2, 3, 4, 5, 6]
      }], ["bold", "italic", "underline", "strike"], // toggled buttons
        [{
          align: ""
        }, {
          align: "center"
        }, {
          align: "right"
        }, {
          align: "justify"
        }], ["blockquote", "code-block"], [{
          list: "ordered"
        }, {
          list: "bullet"
        }, {
          list: "check"
        }], [{
          indent: "-1"
        }, {
          indent: "+1"
        }], // outdent/indent
        [{
          color: []
        }, {
          background: []
        }], // dropdown with defaults from theme
      ]
    }
  },
  
  mounted() {
    this.init();
    this.getModule();
  },
  methods: {
    init: function () {
        this.getGoogleEventTemplate();
    },
    getGoogleEventTemplate: function () {
      this.formLoader =true;
      get('get_google_event_template', {})
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.googleEventList = response.data.data
          }
          this.formLoader =false;
        })
        .catch((error) => {
          this.formLoader =false;
          console.log(error);
        })
    },
    saveGoogleEventTemplate: function () {
      if(this.userData.addOns.kiviPro != true){
        return;
      }
      this.loading = true;
      post('save_google_event_template', { data : this.googleEventList } )
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.getGoogleEventTemplate();
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
          displayErrorMessage(this.formTranslation.common.server_error);
        })
    },
    getModule:function (){
        if(window.request_data.link_show_hide !== undefined && window.request_data.link_show_hide !== ''){
        this.request_status = window.request_data.link_show_hide;
        }
    }
  },
  watch: {},
  computed:{
    userData () {
      return this.$store.state.userDataModule.user;
    }
  }
}

</script>