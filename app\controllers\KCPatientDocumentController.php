<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCPatientDocument;
use App\models\KCPatientEncounterSummeryDocument;
use App\models\KCUser;
use wpdb;

class KCPatientDocumentController extends KCBase
{
    public $db;
    private $request;

    public function __construct()
    {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        parent::__construct();
    }
    
    /**
     * Helper method to check if a page exists
     * 
     * @param string $path Page path to check
     * @return bool Whether the page exists
     */
    private function page_exists($path) {
        $page = get_page_by_path(trim($path, '/'));
        return $page !== null;
    }

    public function uploadPatientReport()
    {
        if (!kcCheckPermission('patient_report_add')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        if (!(new KCUser())->patientPermissionUserWise($request_data['patient_id'])) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $convertedPdf = convert_to_protected_pdf($request_data['document_id'], $request_data['patient_id']);
        $request_data['document_id'] = $convertedPdf['new_attachment_id'];
        
        $patient_document = new KCPatientDocument;

        $data = array(
            'name' => !empty($request_data['name']) ? sanitize_text_field($request_data['name']) : null,
            'type' => sanitize_text_field($request_data['type']),
            'description' => !empty($request_data['description']) ? sanitize_textarea_field($request_data['description']) : null,
            'patient_id' => (int) $request_data['patient_id'],
            'document_id' => sanitize_text_field($request_data['document_id']),
            'appointment_id' => !empty($request_data['appointment_id']) ? (int) $request_data['appointment_id'] : null,
            'created_by' => get_current_user_id()
        );

        $result = $patient_document->insert($data);

        if ($result) {
            // Log document upload activity
            kcLogActivity(
                'file_uploaded',
                sprintf(esc_html__('Document "%s" was uploaded for patient #%d', 'kc-lang'), 
                    $data['name'] ? $data['name'] : esc_html__('Unnamed document', 'kc-lang'),
                    $data['patient_id']
                ),
                [
                    'patient_id' => $data['patient_id'],
                    'resource_id' => $result,
                    'resource_type' => 'document',
                    'clinic_id' => !empty($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : null
                ]
            );
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Document uploaded successfully', 'kiviCare-clinic-&-patient-management-system-pro'),
                'data' => $result
            ]);
        }

        wp_send_json([
            'status' => false,
            'message' => esc_html__('Failed to upload document', 'kiviCare-clinic-&-patient-management-system-pro')
        ]);
    }

    public function getPatientReport_delete()
    {
        if (!kcCheckPermission('patient_report')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        $patient_id = (int) $request_data['patient_id'] ?? 0;
        if (!empty($request_data['document_id'])) {
            $patient_id = (new KCPatientDocument())->get_var(['id' => (int) $request_data['document_id']], 'patient_id');
        }

        if (!(new KCUser())->patientPermissionUserWise($patient_id)) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $conditions = ['patient_id' => $patient_id];
        if (!empty($request_data['appointment_id'])) {
            $conditions['appointment_id'] = (int) $request_data['appointment_id'];
        }

        // Get documents from patient documents table
        $patient_documents = collect((new KCPatientDocument())->get_by($conditions))
            ->map(function ($doc) {
                return (object) [
                    'id' => $doc->id,
                    'name' => $doc->name,
                    'type' => $doc->type,
                    'description' => $doc->description,
                    'document_id' => $doc->document_id,
                    'encounter_id' => $doc->appointment_id,
                    'document_url' => wp_get_attachment_url($doc->document_id),
                    'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
                    'system_generated' => 0
                ];
            });

        // Get documents from encounters summary table
        $encounter_conditions = [];
        if (!empty($request_data['appointment_id'])) {
            $encounter_conditions[] = "encounter_id = " . (int) $request_data['appointment_id'];
        }

        $query = "SELECT id, name, description, attachment_id as document_id, created_date as created_at 
                  FROM {$this->db->prefix}kc_patient_encounters_summery_document";

        if (!empty($encounter_conditions)) {
            $query .= " WHERE " . implode(' AND ', $encounter_conditions);
        }

        $encounter_documents = collect($this->db->get_results($query))
            ->map(function ($doc) {
                return (object) [
                    'id' => $doc->id,
                    'name' => $doc->name,
                    'type' => 'encounter_document',
                    'description' => $doc->description,
                    'document_id' => $doc->document_id,
                    'encounter_id' => $doc->encounter_id,
                    'document_url' => wp_get_attachment_url($doc->document_id),
                    'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
                    'system_generated' => 1
                ];
            });

        // Combine and sort documents
        $documents = $patient_documents->concat($encounter_documents)
            ->sortBy(function ($doc) {
                return $doc->type === 'identity_document' ? -1 : strtotime($doc->created_at);
            }, SORT_DESC);

        wp_send_json([
            'status' => true,
            'data' => $documents
        ]);
    }

    public function getPatientReport()
    {
        if (!kcCheckPermission('patient_report')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        // Initialize conditions
        $patient_id = null;
        $appointment_id = !empty($request_data['encounter_id']) ? (int) $request_data['encounter_id'] : null;

        // Get patient_id from either direct input or document_id
        if (!empty($request_data['patient_id'])) {
            $patient_id = (int) $request_data['patient_id'];
        } elseif (!empty($request_data['document_id'])) {
            $patient_id = (new KCPatientDocument())->get_var(['id' => (int) $request_data['document_id']], 'patient_id');
        }

        // If we have a patient_id, check permissions
        if ($patient_id && !(new KCUser())->patientPermissionUserWise($patient_id)) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        // Build conditions for patient documents
        $patient_doc_conditions = [];
        if ($patient_id) {
            $patient_doc_conditions['patient_id'] = $patient_id;
        }
        // if ($appointment_id) {
        //     $patient_doc_conditions['appointment_id'] = $appointment_id;
        // }

        // Get documents from patient documents table if we have any conditions
        $patient_documents = collect([]);
        if (!empty($patient_doc_conditions)) {
            $patient_documents = collect((new KCPatientDocument())->get_by($patient_doc_conditions))
                ->map(function ($doc) {
                    return (object) [
                        'id' => $doc->id,
                        'name' => $doc->name,
                        'type' => $doc->type,
                        'description' => $doc->description,
                        'attachment_id' => $doc->document_id,
                        'document_id' => $doc->document_id,
                        'encounter_id' => $doc->appointment_id,
                        'document_url' => wp_get_attachment_url($doc->document_id),
                        'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
                        'system_generated' => 0
                    ];
                });
        }

        // Build conditions for encounter documents
        $encounter_conditions = [];
        // if ($appointment_id) {
        //     $encounter_conditions[] = "encounter_id = " . $appointment_id;
        // }
        if ($patient_id) {
            $encounter_conditions['patient_id'] = $patient_id;
        }
        // Get documents from encounters summary table
        $query = "SELECT id, name, description, encounter_id, attachment_id as document_id, created_date as created_at 
                  FROM {$this->db->prefix}kc_patient_encounters_summery_document";

        global $wpdb;
        if (!empty($appointment_id )) {
            $query .= $wpdb->prepare(" WHERE encounter_id LIKE  %d",$appointment_id) ;
        }

        $encounter_documents = collect([]);
        if ($appointment_id) {
            $encounter_documents = collect($this->db->get_results($query))
                ->map(function ($doc) {
                    return (object) [
                        'id' => $doc->id,
                        'name' => $doc->name,
                        'type' => 'encounter_document',
                        'description' => $doc->description,
                        'attachment_id' => $doc->document_id,
                        'document_id' => $doc->document_id,
                        'encounter_id' => $doc->encounter_id,
                        'document_url' => wp_get_attachment_url($doc->document_id),
                        'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
                        'system_generated' => 1
                    ];
                });
        }

        // If neither patient_id nor appointment_id is provided, return empty result
        if (!$patient_id && !$appointment_id) {
            wp_send_json([
                'status' => true,
                'data' => []
            ]);
        }

        // Combine and sort documents
        $documents = $patient_documents->concat($encounter_documents)
            ->sortBy(function ($doc) {
                return $doc->type === 'identity_document' ? -1 : strtotime($doc->created_at);
            }, SORT_DESC);

        wp_send_json([
            'status' => true,
            'data' => $documents->values()
        ]);
    }

    public function getPatientReportByAppointmentId()
    {
        if (!kcCheckPermission('patient_report')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        // Initialize conditions
        $patient_id = null;
        $appointment_id = !empty($request_data['appointment_id']) ? (int) $request_data['appointment_id'] : null;

        // Get patient_id from either direct input or document_id
        if (!empty($request_data['patient_id'])) {
            $patient_id = (int) $request_data['patient_id'];
        } elseif (!empty($request_data['document_id'])) {
            $patient_id = (new KCPatientDocument())->get_var(['id' => (int) $request_data['document_id']], 'patient_id');
        }

        // If we have a patient_id, check permissions
        if ($patient_id && !(new KCUser())->patientPermissionUserWise($patient_id)) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        // Build conditions for patient documents
        $patient_doc_conditions = [];
        if ($patient_id) {
            $patient_doc_conditions['patient_id'] = $patient_id;
        }
        if ($appointment_id) {
            $patient_doc_conditions['appointment_id'] = $appointment_id;
        }

        // Get documents from patient documents table if we have any conditions
        $patient_documents = collect([]);
        if (!empty($patient_doc_conditions)) {
            $patient_documents = collect((new KCPatientDocument())->get_by($patient_doc_conditions))
                ->map(function ($doc) {
                    return (object) [
                        'id' => $doc->id,
                        'name' => $doc->name,
                        'type' => $doc->type,
                        'description' => $doc->description,
                        'attachment_id' => $doc->document_id,
                        'document_id' => $doc->document_id,
                        'encounter_id' => $doc->appointment_id,
                        'document_url' => wp_get_attachment_url($doc->document_id),
                        'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
                        'system_generated' => 0
                    ];
                });
        }

        // Build conditions for encounter documents
        $encounter_conditions = [];
        if ($appointment_id) {
            $encounter_conditions[] = "encounter_id = " . $appointment_id;
        }

        // Get documents from encounters summary table
        $query = "SELECT id, name, description, encounter_id, attachment_id as document_id, created_date as created_at 
                  FROM {$this->db->prefix}kc_patient_encounters_summery_document";

        if (!empty($encounter_conditions)) {
            $query .= " WHERE " . implode(' AND ', $encounter_conditions);
        }

        $encounter_documents = collect([]);
        if ($appointment_id) {
            $encounter_documents = collect($this->db->get_results($query))
                ->map(function ($doc) {
                    return (object) [
                        'id' => $doc->id,
                        'name' => $doc->name,
                        'type' => 'encounter_document',
                        'description' => $doc->description,
                        'attachment_id' => $doc->document_id,
                        'document_id' => $doc->document_id,
                        'encounter_id' => $doc->encounter_id,
                        'document_url' => wp_get_attachment_url($doc->document_id),
                        'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
                        'system_generated' => 1
                    ];
                });
        }

        // If neither patient_id nor appointment_id is provided, return empty result
        if (!$patient_id && !$appointment_id) {
            wp_send_json([
                'status' => true,
                'data' => []
            ]);
        }

        // Combine and sort documents
        $documents = $patient_documents->concat($encounter_documents)
            ->sortBy(function ($doc) {
                return $doc->type === 'identity_document' ? -1 : strtotime($doc->created_at);
            }, SORT_DESC);

        wp_send_json([
            'status' => true,
            'data' => $documents->values()
        ]);
    }

    public function viewPatientReport()
    {
        if (!kcCheckPermission('patient_report_view')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        if (!(new KCUser())->patientPermissionUserWise($request_data['patient_id'])) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        if (empty($request_data['doc_id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Document ID is required', 'kiviCare-clinic-&-patient-management-system-pro')
            ]);
        }

        $document = (new KCPatientDocument())->get_by(['id' => (int) $request_data['doc_id']], '=', true);
        if (empty($document)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Document not found', 'kiviCare-clinic-&-patient-management-system-pro')
            ]);
        }

        $url = wp_get_attachment_url($document->document_id);

        wp_send_json([
            'status' => true,
            'data' => $url
        ]);
    }

    public function deletePatientReport()
    {
        if (!kcCheckPermission('patient_report_delete')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();
        $patient_id = (new KCPatientDocument())->get_var(['id' => (int) $request_data['id']], 'patient_id');

        if (!(new KCUser())->patientPermissionUserWise($patient_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You dont have permission to delete this file as you have not created this file', 'kiviCare-clinic-&-patient-management-system-pro')
            ]);
        }
        $document= $request_data['type'] =='encounter_document'? new KCPatientEncounterSummeryDocument ():new KCPatientDocument() ;  
        
        // Get document details for logging before deletion
        $document_data = $document->get_by(['id' => (int) $request_data['id']], '=', true);
        $document_name = !empty($document_data->name) ? $document_data->name : esc_html__('Unnamed document', 'kc-lang');

        $result = $document->delete(['id' => (int) $request_data['id']]);
        if ($result) {
            // Log document deletion activity
            kcLogActivity(
                'file_deleted',
                sprintf(esc_html__('Document "%s" was deleted', 'kc-lang'), $document_name),
                [
                    'patient_id' => $patient_id,
                    'resource_id' => (int) $request_data['id'],
                    'resource_type' => 'document'
                ]
            );
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Document deleted successfully', 'kiviCare-clinic-&-patient-management-system-pro')
            ]);
        }

        wp_send_json([
            'status' => false,
            'message' => esc_html__('Failed to delete document', 'kiviCare-clinic-&-patient-management-system-pro')
        ]);
    }

    public function patientReportMail()
    {
        $request_data = $this->request->getInputs();
        $status = false;
        $message = esc_html__('Failed to send report', 'kc-lang');

        if (!empty($request_data['data']) && is_array($request_data['data']) && !empty($request_data['patient_id'])) {
            if (!(new KCUser())->patientPermissionUserWise($request_data['patient_id'])) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $patient_report = collect($request_data['data'])->pluck('upload_report')->toArray();
            if (!empty($patient_report) && count($patient_report) > 0) {
                $user_email = $this->db->get_var('select user_email from ' . $this->db->base_prefix . 'users where ID=' . (int) $request_data['patient_id']);
                $patient_report = array_map(function ($v) {
                    return get_attached_file($v);
                }, $patient_report);

                $data = [
                    'user_email' => $user_email != null ? $user_email : '',
                    'attachment_file' => $patient_report,
                    'attachment' => true,
                    'email_template_type' => 'patient_report'
                ];

                $status = kcSendEmail($data);
                $message = $status ? esc_html__('Report sent successfully', 'kc-lang') : esc_html__('Failed to send report', 'kc-lang');
                
                // Log document sharing activity if email sent successfully
                if ($status) {
                    kcLogActivity(
                        'data_shared',
                        sprintf(esc_html__('Document(s) shared via email to patient #%d', 'kc-lang'), $request_data['patient_id']),
                        [
                            'patient_id' => $request_data['patient_id'],
                            'resource_type' => 'document'
                        ]
                    );
                }
            }
        }

        wp_send_json([
            'status' => $status,
            'message' => $message,
        ]);
    }

    public function editPatientReport()
    {
        // Check permission
        if (!current_user_can(KIVI_CARE_PREFIX . 'patient_report_edit')) {
            wp_send_json_error(esc_html__('you do not have permission to edit report', 'kc-lang'), 403);
        }

        $request_data = $this->request->getInputs();
        $patient_id = (new KCPatientDocument())->get_var(['id' => (int) $request_data['id']], 'patient_id');

        // Validate required parameters
        if (empty($request_data['id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Record ID is required', 'kiviCare-clinic-&-patient-management-system-pro')
            ]);
        }

        $patient_document = new KCPatientDocument;

        // Get existing document to verify ownership and get patient_id
        $existing_document = $patient_document->get_by(['id' => (int) $request_data['id']], '=', true);

        if (empty($existing_document)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Document record not found', 'kiviCare-clinic-&-patient-management-system-pro')
            ]);
        }

        // Check patient permission
        if (!(new KCUser())->patientPermissionUserWise($patient_id)) {
            kcUnauthorizeAccessResponse(403);
        }

        // Prepare update data
        $data = array(
            'name' => !empty($request_data['name']) ? sanitize_text_field($request_data['name']) : $existing_document->name,
            'type' => !empty($request_data['type']) ? sanitize_text_field($request_data['type']) : $existing_document->type,
            'description' => !empty($request_data['description']) ? sanitize_textarea_field($request_data['description']) : $existing_document->description,
            'updated_at' => current_time('Y-m-d H:i:s')
        );

        // If a new document file is uploaded, update document_id
        if (!empty($request_data['document_id']) && $request_data['document_id'] !== $existing_document->document_id) {
            // Verify the new document_id exists in WordPress media
            $attachment_exists = wp_get_attachment_url($request_data['document_id']);
            if (!$attachment_exists) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Invalid document file', 'kiviCare-clinic-&-patient-management-system-pro')
                ]);
            }

            $data['document_id'] = sanitize_text_field($request_data['document_id']);

            // Optionally delete old media file if needed
            // wp_delete_attachment($existing_document->document_id, true);
        }

        // If appointment_id is provided, update it
        if (isset($request_data['appointment_id'])) {
            $data['appointment_id'] = !empty($request_data['appointment_id']) ? (int) $request_data['appointment_id'] : null;
        }

        // Perform update using record id
        $result = $patient_document->update($data, ['id' => (int) $request_data['id']]);

        if ($result) {
            // Get updated document data
            $updated_document = $patient_document->get_by(['id' => (int) $request_data['id']], '=', true);

            wp_send_json([
                'status' => true,
                'message' => esc_html__('Document updated successfully', 'kiviCare-clinic-&-patient-management-system-pro'),
                'data' => [
                    'id' => $updated_document->id,
                    'name' => $updated_document->name,
                    'type' => $updated_document->type,
                    'description' => $updated_document->description,
                    'patient_id' => $updated_document->patient_id,
                    'document_id' => $updated_document->document_id,
                    'document_url' => wp_get_attachment_url($updated_document->document_id),
                    'appointment_id' => $updated_document->appointment_id,
                    'created_at' => !empty($updated_document->created_at) ? kcGetFormatedDate($updated_document->created_at) : null,
                    'updated_at' => !empty($updated_document->updated_at) ? kcGetFormatedDate($updated_document->updated_at) : null
                ]
            ]);
        }

        wp_send_json([
            'status' => false,
            'message' => esc_html__('Failed to update document', 'kiviCare-clinic-&-patient-management-system-pro')
        ]);
    }

    public function editPatientReport_delete()
    {
        if (!current_user_can(KIVI_CARE_PREFIX . 'patient_report_edit')) {
            wp_send_json_error(esc_html__('you do not have permission to edit report', 'kc-lang'), 403);
        }

        $request_data = $this->request->getInputs();
        $patient_id = (new KCPatientDocument())->get_var(['id' => (int) $request_data['id']], 'patient_id');

        if (!(new KCUser())->patientPermissionUserWise($patient_id)) {
            kcUnauthorizeAccessResponse(403);
        }

        do_action('kcpro_edit_patient_report', $request_data);
        wp_send_json_error(esc_html__('This API Is Only For KiviCare Pro', 'kc-lang'), 403);
    }

    public function getDocumentAccessKey() {
        $request_data = $this->request->getInputs();
        $document_id = isset($request_data['document_id']) ? (int) $request_data['document_id'] : 0;
        $system_generated = isset($request_data['system_generated']) ? $request_data['system_generated'] : false;
        $patient_id = isset($request_data['patient_id']) ? (int) $request_data['patient_id'] : 0;
        
        try {
            global $wpdb;
            
            if ($system_generated && $document_id > 0) {
                // Get encounter_id from encounter summary document
                $summary_table = $wpdb->prefix . 'kc_patient_encounters_summery_document';
                $encounter_id_query = $wpdb->prepare(
                    "SELECT encounter_id FROM {$summary_table} WHERE id = %d",
                    $document_id
                );
                $encounter_id = $wpdb->get_var($encounter_id_query);
                
                if ($encounter_id) {
                    // Get patient_id from encounter table
                    $encounter_table = $wpdb->prefix . 'kc_patient_encounters';
                    $patient_id_query = $wpdb->prepare(
                        "SELECT patient_id FROM {$encounter_table} WHERE id = %d",
                        $encounter_id
                    );
                    $patient_id = $wpdb->get_var($patient_id_query);
                }
            } else if ($document_id > 0) {
                // Get patient_id from documents table
                $document_table = $wpdb->prefix . 'kc_patient_document';
                $patient_id_query = $wpdb->prepare(
                    "SELECT patient_id FROM {$document_table} WHERE id = %d",
                    $document_id
                );
                $patient_id = $wpdb->get_var($patient_id_query);
            }
            
            // Get password from patient ID or use default
            $pdf_password = ($patient_id > 0) ? get_pdf_password_from_patient_id($patient_id) : 'demo';
            
            $encrypted_password = $this->encrypt_password($pdf_password);

            wp_send_json([
                'status' => true,
                'message' => esc_html__('Document retrieved successfully', 'kiviCare-clinic-&-patient-management-system-pro'),
                'data' => [
                    'password' => $pdf_password,
                ]
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    public function encrypt_password($password) {
        $encryption_key = 'medroid-ehr'; // Store this in wp-config.php in production
        $method = 'AES-256-CBC';
        $iv = substr(hash('sha256', $encryption_key), 0, 16);
        
        $encrypted = openssl_encrypt($password, $method, $encryption_key, 0, $iv);
        return base64_encode($encrypted);
    }
    
    /**
     * Creates a mobile upload session for patients to upload documents via QR code
     */
    public function createMobileUploadSession() {
        $request_data = $this->request->getInputs();
        
        try {
            // Validate required parameters
            if (empty($request_data['encounter_id']) || empty($request_data['patient_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Missing required parameters', 'kiviCare-clinic-&-patient-management-system-pro')
                ]);
            }
            
            // Generate a unique session ID
            $session_id = uniqid('kivi_', true);
            
            // Set expiration time (default 30 minutes)
            $expire_minutes = !empty($request_data['expire_minutes']) ? (int)$request_data['expire_minutes'] : 30;
            $expires_at = time() + ($expire_minutes * 60);
            
            // Store session data in WordPress transient
            $session_data = [
                'session_id' => $session_id,
                'encounter_id' => $request_data['encounter_id'],
                'patient_id' => $request_data['patient_id'],
                'created_at' => time(),
                'expires_at' => $expires_at,
                'uploads' => []
            ];
            
            set_transient('kc_mobile_upload_' . $session_id, $session_data, $expire_minutes * 60);
            
            // Use direct page URL instead of REST API
            $upload_url = add_query_arg('session_id', $session_id, site_url('/mobile-upload'));
            
            // Generate QR code - use QR server API for simplicity
            $qr_code_url = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($upload_url);
            
            // Log mobile upload session creation
            kcLogActivity(
                'mobile_upload_created',
                sprintf(esc_html__('Mobile upload session created for patient #%d, encounter #%d', 'kc-lang'), 
                    $request_data['patient_id'],
                    $request_data['encounter_id']
                ),
                [
                    'patient_id' => $request_data['patient_id'],
                    'resource_id' => $request_data['encounter_id'],
                    'resource_type' => 'encounter'
                ]
            );
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Mobile upload session created', 'kiviCare-clinic-&-patient-management-system-pro'),
                'data' => [
                    'session_id' => $session_id,
                    'upload_url' => $upload_url,
                    'qr_code_url' => $qr_code_url,
                    'expires_at' => date('Y-m-d H:i:s', $expires_at)
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Checks if a mobile upload session has any new uploads
     */
    public function checkMobileUploadSession() {
        $request_data = $this->request->getInputs();
        
        try {
            // Validate required parameters
            if (empty($request_data['session_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Session ID is required', 'kiviCare-clinic-&-patient-management-system-pro')
                ]);
            }
            
            $session_id = sanitize_text_field($request_data['session_id']);
            $session_data = get_transient('kc_mobile_upload_' . $session_id);
            
            if (!$session_data) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Upload session not found or expired', 'kiviCare-clinic-&-patient-management-system-pro')
                ]);
            }
            
            // Check if session is expired
            if (time() > $session_data['expires_at']) {
                delete_transient('kc_mobile_upload_' . $session_id);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Upload session has expired', 'kiviCare-clinic-&-patient-management-system-pro')
                ]);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Upload session active', 'kiviCare-clinic-&-patient-management-system-pro'),
                'data' => [
                    'session_id' => $session_id,
                    'encounter_id' => $session_data['encounter_id'],
                    'patient_id' => $session_data['patient_id'],
                    'created_at' => date('Y-m-d H:i:s', $session_data['created_at']),
                    'expires_at' => date('Y-m-d H:i:s', $session_data['expires_at']),
                    'uploads' => $session_data['uploads']
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}