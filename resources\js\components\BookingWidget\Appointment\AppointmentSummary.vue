<template>
  <div>
    <h2 class="kivi-step-title">Confirm Your Appointment</h2>
    <p class="kivi-step-subtitle">Please review your appointment details before confirming.</p>

    <div class="kivi-booking-summary">
      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Clinic:</div>
        <div class="kivi-summary-value">{{ bookingData.clinic ? bookingData.clinic.name : 'Not selected' }}</div>
      </div>

      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Category:</div>
        <div class="kivi-summary-value">{{ bookingData.category ? bookingData.category.name : 'Not selected' }}</div>
      </div>

      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Service(s):</div>
        <div class="kivi-summary-value">
          <ul v-if="bookingData.services && bookingData.services.length" class="kivi-summary-list">
            <li v-for="service in bookingData.services" :key="service.id">
              {{ service.name }} <span class="kivi-service-price">{{ service.price }}</span>
            </li>
          </ul>
          <span v-else>No services selected</span>
        </div>
      </div>

      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Date:</div>
        <div class="kivi-summary-value">{{ formatDate(bookingData.date) }}</div>
      </div>

      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Time:</div>
        <div class="kivi-summary-value">{{ formatTime(bookingData.time) }}</div>
      </div>

      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Patient:</div>
        <div class="kivi-summary-value">{{ bookingData.patient ? bookingData.patient.name : 'Not provided' }}</div>
      </div>

      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Email:</div>
        <div class="kivi-summary-value">{{ bookingData.patient ? bookingData.patient.email : 'Not provided' }}</div>
      </div>

      <div class="kivi-summary-item">
        <div class="kivi-summary-label">Phone:</div>
        <div class="kivi-summary-value">{{ bookingData.patient ? bookingData.patient.phone : 'Not provided' }}</div>
      </div>

      <div class="kivi-summary-item" v-if="bookingData.patient && bookingData.patient.notes">
        <div class="kivi-summary-label">Notes:</div>
        <div class="kivi-summary-value">{{ bookingData.patient.notes }}</div>
      </div>

      <div v-if="servicesTotal > 0" class="kivi-summary-item kivi-summary-total">
        <div class="kivi-summary-label">Total:</div>
        <div class="kivi-summary-value">{{ formatPrice(servicesTotal) }}</div>
      </div>

      <!-- Payment Options -->
      <div v-if="paymentOptions.length > 0" class="kivi-payment-options">
        <h3 class="kivi-payment-title">Payment Method</h3>

        <div class="kivi-payment-selector">
          <label
            v-for="option in paymentOptions"
            :key="option.id"
            class="kivi-payment-option"
            :class="{ 'selected': selectedPaymentMethod === option.id }"
          >
            <input
              type="radio"
              :value="option.id"
              v-model="selectedPaymentMethod"
              name="payment_method"
              @change="handlePaymentOptionSelect(option.id)"
            >
            <div class="kivi-payment-content">
              <div class="kivi-payment-header">
                <span class="kivi-payment-icon" v-if="option.icon" v-html="option.icon"></span>
                <span class="kivi-payment-label">{{ option.label }}</span>
              </div>
              <span class="kivi-payment-description" v-if="option.description">{{ option.description }}</span>
            </div>
          </label>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="bookingError" class="kivi-booking-error">
        {{ bookingError }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AppointmentSummary',
  props: {
    bookingData: {
      type: Object,
      required: true
    },
    paymentOptions: {
      type: Array,
      default: () => []
    },
    selectedPaymentMethod: {
      type: String,
      default: 'paymentStripepay'
    },
    bookingError: {
      type: String,
      default: null
    },
    servicesTotal: {
      type: Number,
      default: 0
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return '';
      // Use the same date formatting logic as in the parent component
      const options = { year: 'numeric', month: 'long', day: 'numeric' };
      return new Date(date).toLocaleDateString(undefined, options);
    },
    formatTime(time) {
      if (!time) return '';
      // Return the time as is, assuming it's already formatted
      return time;
    },
    formatPrice(price) {
      if (!price) return '';
      // Format price with currency symbol
      return new Intl.NumberFormat(undefined, {
        style: 'currency',
        currency: 'USD' // This should ideally come from a configuration
      }).format(price);
    },
    handlePaymentOptionSelect(optionId) {
      this.$emit('payment-option-select', optionId);
    }
  }
}
</script>