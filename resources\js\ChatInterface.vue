<template>
  <div>
    <!-- Floating <PERSON><PERSON> (Always Visible) -->
    <button 
      @click="isExpanded = !isExpanded" 
      class="fixed z-50 bottom-20 left-5 w-14 h-14 rounded-full bg-pink-600 text-white shadow-lg flex items-center justify-center hover:bg-pink-700 transition-all duration-300 focus:outline-none"
      :class="{'rotate-45': isExpanded}"
    >
      <i :class="isExpanded ? 'fas fa-times text-xl' : 'fas fa-comments text-xl'"></i>
      <span v-if="unreadCount > 0" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
        {{unreadCount > 9 ? '9+' : unreadCount}}
      </span>
    </button>

    <!-- Expandable Chat Container -->
    <div 
      v-show="isExpanded"
      class="fixed z-40 bottom-40 left-5 w-80 md:w-96 bg-white rounded-lg shadow-xl transition-all duration-300 overflow-hidden flex flex-col"
      :class="{'max-h-[500px]': isExpanded, 'max-h-0': !isExpanded}"
      style="height: 500px;"
    >
      <!-- Chat Header -->
      <div class="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-white">
        <h2 class="text-lg font-semibold text-gray-800">Chat</h2>
        <div class="flex items-center gap-2">
          <button 
            @click="openFullChat" 
            class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
            title="Open full chat"
          >
            <i class="fas fa-external-link-alt"></i>
          </button>
          <button 
            @click="isExpanded = false" 
            class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
            title="Minimize"
          >
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>

      <!-- Mini Dashboard or Full Chat Interface -->
      <div v-if="showMiniDashboard" class="flex-1 overflow-y-auto p-3 bg-gray-50">
        <!-- Loading State -->
        <div v-if="loading" class="h-full flex items-center justify-center">
          <div class="animate-spin h-8 w-8 border-4 border-gray-300 border-t-pink-500 rounded-full"></div>
        </div>
        
        <!-- Conversation List -->
        <div v-else>
          <p class="text-sm text-gray-500 mb-2">Recent conversations</p>
          
          <!-- No Conversations State -->
          <div v-if="conversations.length === 0" class="py-10 text-center">
            <div class="text-gray-400 mb-3">
              <i class="fas fa-comments text-4xl"></i>
            </div>
            <p class="text-gray-500 mb-3">No conversations yet</p>
            <button 
              @click="startNewChat" 
              class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded text-sm"
            >
              Start a new chat
            </button>
          </div>
          
          <!-- Conversation List -->
          <div v-else class="space-y-2">
            <button 
              v-for="conversation in conversations" 
              :key="conversation.id"
              @click="openChat(conversation)"
              class="w-full text-left p-3 rounded bg-white hover:bg-gray-100 flex items-start border border-gray-200"
            >
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 flex-shrink-0">
                <i :class="conversation.type === 'direct' ? 'fas fa-user' : 'fas fa-users'"></i>
              </div>
              
              <div class="ml-2 flex-1 min-w-0">
                <div class="flex justify-between items-center mb-1">
                  <h4 class="font-medium text-gray-900 truncate text-sm">
                    {{ getConversationName(conversation) }}
                  </h4>
                  <span class="text-xs text-gray-500">
                    {{ formatTime(conversation.last_activity) }}
                  </span>
                </div>
                
                <div class="flex justify-between items-center">
                  <p class="text-xs text-gray-600 truncate">
                    {{ getLastMessage(conversation) }}
                  </p>
                  
                  <!-- Unread badge -->
                  <span 
                    v-if="conversation.unread_count > 0"
                    class="flex-shrink-0 w-5 h-5 bg-pink-500 text-white text-xs rounded-full flex items-center justify-center"
                  >
                    {{ conversation.unread_count > 9 ? '9+' : conversation.unread_count }}
                  </span>
                </div>
              </div>
            </button>
          </div>
          
          <!-- New Chat Button -->
          <div class="mt-4 text-center">
            <button 
              @click="startNewChat" 
              class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded text-sm inline-flex items-center"
            >
              <i class="fas fa-plus-circle mr-2"></i>
              New Chat
            </button>
          </div>
        </div>
      </div>
      
      <!-- Active Chat Area -->
      <div v-else class="flex-1 flex flex-col overflow-hidden">
        <!-- Chat Header -->
        <div class="p-3 border-b border-gray-200 flex items-center bg-white">
          <button @click="backToList" class="mr-2 text-gray-500 hover:text-gray-700">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700">
              <i :class="activeConversation && activeConversation.type === 'direct' ? 'fas fa-user' : 'fas fa-users'"></i>
            </div>
            <div class="ml-2">
              <h3 class="font-medium text-gray-900 text-sm">
                {{ activeConversation ? getConversationName(activeConversation) : 'Chat' }}
              </h3>
            </div>
          </div>
        </div>
        
        <!-- Messages List -->
        <div class="flex-1 overflow-y-auto p-3 bg-gray-50" ref="messagesContainer">
          <div v-if="messagesLoading" class="h-full flex items-center justify-center">
            <div class="animate-spin h-8 w-8 border-4 border-gray-300 border-t-pink-500 rounded-full"></div>
          </div>
          
          <template v-else>
            <!-- Messages with date separators -->
            <template v-for="(message, index) in messages">
              <!-- Date separator -->
              <div v-if="shouldShowDate(message, index)" :key="'date-'+message.id" class="w-full text-center my-3">
                <div class="inline-block px-3 py-1 bg-gray-200 rounded-full text-xs text-gray-600">
                  {{ formatMessageDate(message.created_at) }}
                </div>
              </div>

              <!-- Message row -->
              <div :key="message.id" class="flex items-end mb-3 relative"
                :class="parseInt(message.user_id) == parseInt(getUserId()) ? 'justify-end' : 'justify-start'">
              
              <!-- Sender Avatar (for received messages) -->
              <div v-if="parseInt(message.user_id) != parseInt(getUserId())" 
                class="w-6 h-6 rounded-full bg-gray-200 flex-shrink-0 mr-1 mb-1 flex items-center text-gray-500">
                <i class="fas fa-user text-xs"></i>
              </div>
              
              <!-- Sender Avatar for your messages (right side) -->
              <div v-if="parseInt(message.user_id) == parseInt(getUserId())"
                class="w-6 h-6 rounded-full bg-pink-200 flex-shrink-0 ml-1 mb-1 flex items-center justify-center text-pink-600">
                <i class="fas fa-user text-xs"></i>
              </div>
              
              <!-- Message bubble with different styling for sent vs received -->
              <div class="max-w-[70%] px-3 py-2 rounded-lg shadow-sm relative"
                :class="parseInt(message.user_id) == parseInt(getUserId()) ? 
                  'bg-pink-100 text-gray-900 rounded-tr-none border-r-2 border-pink-300 mr-1' : 
                  'bg-gray-100 text-gray-900 rounded-tl-none border-l-2 border-gray-300 ml-1'">
                
                <!-- Removed triangle tip since we're using border instead -->
                
                <!-- Tiny label showing "You" for sender's messages -->
                <div v-if="parseInt(message.user_id) == parseInt(getUserId())" class="absolute -top-3 right-0 text-[10px] text-gray-500 font-medium">
                  You
                </div>
                
                <!-- Sender name (for group chats) -->
                <div v-if="activeConversation && activeConversation.type === 'group' && message.user_id !== getUserId()"
                  class="text-xs text-pink-600 font-medium mb-1">
                  {{ message.sender_name }}
                </div>
                
                <!-- File attachment if any -->
                <div v-if="message.file_url" class="mb-2">
                  <!-- Image -->
                  <img v-if="isImage(message.file_type)" :src="message.file_url"
                    class="max-w-full rounded-lg max-h-40 cursor-pointer" @click="openAttachment(message.file_url)"
                    alt="Image attachment" />
                  
                  <!-- PDF Document -->
                  <div v-else-if="isPDF(message.file_type)"
                    class="flex items-center p-2 bg-white rounded-lg border border-gray-200">
                    <i class="far fa-file-pdf text-lg text-red-500 mr-2"></i>
                    <div class="flex-grow min-w-0">
                      <p class="text-xs font-medium text-gray-900 truncate">PDF Document</p>
                    </div>
                    <button @click="openAttachment(message.file_url)"
                      class="ml-1 p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                      <i class="fas fa-external-link-alt text-xs"></i>
                    </button>
                  </div>
                  
                  <!-- Other file types -->
                  <div v-else class="flex items-center p-2 bg-white rounded-lg border border-gray-200">
                    <i class="far fa-file text-lg text-blue-500 mr-2"></i>
                    <div class="flex-grow min-w-0">
                      <p class="text-xs font-medium text-gray-900 truncate">File attachment</p>
                    </div>
                    <button @click="openAttachment(message.file_url)"
                      class="ml-1 p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                      <i class="fas fa-download text-xs"></i>
                    </button>
                  </div>
                </div>
                
                <!-- Message text -->
                <p class="text-sm whitespace-pre-wrap break-words">{{ message.message }}</p>
                
                <!-- Message time -->
                <div class="text-xs text-gray-500 text-right mt-1">
                  {{ formatMessageTime(message.created_at) }}
                </div>
              </div>
            </div>
            </template>
            
            <!-- No messages placeholder -->
            <div v-if="messages.length === 0" class="h-full flex flex-col items-center justify-center p-4">
              <div class="text-gray-400 mb-3">
                <i class="fas fa-comments text-3xl"></i>
              </div>
              <p class="text-gray-500 text-center text-sm">No messages yet. Send your first message.</p>
            </div>
          </template>
        </div>
        
        <!-- Message Input -->
        <div class="p-3 border-t border-gray-200 bg-white">
          <div class="flex items-end gap-2">
            <!-- File Attachment Button -->
            <label class="p-2 text-gray-500 hover:text-pink-600 hover:bg-gray-100 rounded cursor-pointer">
              <input type="file" class="hidden" @change="handleFileUpload" />
              <i class="fas fa-paperclip"></i>
            </label>
            
            <!-- Text Input -->
            <div class="flex-1 border border-gray-200 rounded overflow-hidden bg-gray-50">
              <textarea v-model="newMessage" @keydown.enter.exact.prevent="sendMessage"
                placeholder="Type a message..." rows="1"
                class="w-full p-2 bg-transparent focus:outline-none resize-none text-sm" ref="messageInput"></textarea>
              
              <!-- Preview of attached file if any -->
              <div v-if="fileToUpload"
                class="p-2 bg-gray-100 border-t border-gray-200 flex justify-between items-center">
                <div class="flex items-center">
                  <i :class="getFileIcon(fileToUpload.type)" class="mr-2 text-sm"></i>
                  <span class="text-xs truncate max-w-[150px]">{{ fileToUpload.name }}</span>
                </div>
                <button @click="fileToUpload = null"
                  class="p-1 text-gray-500 hover:text-red-600 hover:bg-gray-200 rounded-full">
                  <i class="fas fa-times text-xs"></i>
                </button>
              </div>
            </div>
            
            <!-- Send Button -->
            <button @click="sendMessage" :disabled="!canSendMessage"
              :class="{ 'bg-pink-600 hover:bg-pink-700': canSendMessage, 'bg-gray-300 cursor-not-allowed': !canSendMessage }"
              class="p-2 text-white rounded transition-colors duration-200">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- New Chat Modal -->
    <div v-if="showNewChatModal"
      class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">New Conversation</h3>
          <button @click="showNewChatModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="p-4">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Select User</label>
            <div class="relative">
              <input type="text" v-model="userSearchQuery" placeholder="Search by name or email..."
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-pink-500 focus:border-pink-500"
                @input="filterUsers" />

              <div v-if="!selectedUser && userSearchQuery && filteredUsers.length > 0"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm">
                <p class="px-4 py-2 text-xs text-gray-500 bg-gray-50 font-medium">Search results ({{
                  filteredUsers.length
                }})</p>
                <button v-for="user in filteredUsers" :key="user.ID || user.id" 
                  @click.prevent.stop="selectUser(user)"
                  type="button"
                  class="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center">
                  <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mr-3">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="flex-grow min-w-0">
                    <p class="font-medium text-gray-900 truncate">
                      {{ user.data ? user.data.display_name : user.display_name }}
                    </p>
                    <div class="flex items-center">
                      <span class="text-xs text-gray-500 truncate mr-2">
                        {{ user.data ? user.data.user_email : user.user_email }}
                      </span>
                      <span class="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
                        {{ getUserRoleLabel(user.roles && user.roles.length ? user.roles[0] : '') }}
                      </span>
                    </div>
                  </div>
                </button>
              </div>

              <div v-if="!selectedUser && !userSearchQuery && users.length > 0"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm">
                <p class="px-4 py-2 text-xs text-gray-500 bg-gray-50 font-medium">Available users ({{ users.length }})
                </p>
                <button v-for="user in users.slice(0, 10)" :key="user.ID || user.id" 
                  @click.prevent.stop="selectUser(user)"
                  type="button"
                  class="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center">
                  <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mr-3">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="flex-grow min-w-0">
                    <p class="font-medium text-gray-900 truncate">
                      {{ user.data ? user.data.display_name : user.display_name }}
                    </p>
                    <div class="flex items-center">
                      <span class="text-xs text-gray-500 truncate mr-2">
                        {{ user.data ? user.data.user_email : user.user_email }}
                      </span>
                      <span class="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
                        {{ getUserRoleLabel(user.roles && user.roles.length ? user.roles[0] : '') }}
                      </span>
                    </div>
                  </div>
                </button>
              </div>

              <div v-else-if="userSearchQuery && filteredUsers.length === 0 && !loadingUsers"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-6 text-center">
                <p class="text-gray-500">No users matching '{{ userSearchQuery }}'</p>
              </div>

              <div v-else-if="users.length === 0 && !loadingUsers"
                class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-6 text-center">
                <p class="text-gray-500">No users available for chat</p>
              </div>

              <div v-if="loadingUsers" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-6 text-center">
                <div class="animate-spin h-6 w-6 border-4 border-gray-300 border-t-pink-500 rounded-full mx-auto mb-2">
                </div>
                <p class="text-gray-500">Loading available users...</p>
              </div>
            </div>
          </div>

          <div v-if="selectedUser" class="p-3 bg-gray-50 rounded-lg flex justify-between items-center mb-4">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mr-3">
                <i class="fas fa-user"></i>
              </div>
              <div>
                <p class="font-medium text-gray-900">{{ selectedUser.display_name }}</p>
                <p class="text-xs text-gray-500">{{ selectedUser.user_email }}</p>
              </div>
            </div>
            <button @click="selectedUser = null"
              class="text-gray-500 hover:text-gray-700 p-1 hover:bg-gray-200 rounded-full">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="flex justify-end mt-6">
            <button @click="showNewChatModal = false"
              class="mr-3 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg">
              Cancel
            </button>
            <button @click="createConversation" :disabled="!selectedUser || creatingConversation"
              :class="{ 'bg-pink-600 hover:bg-pink-700': selectedUser && !creatingConversation, 'bg-gray-300 cursor-not-allowed': !selectedUser || creatingConversation }"
              class="px-4 py-2 text-white rounded-lg new-chat-modal-start-chat-button">
              <span v-if="creatingConversation">
                <i class="fas fa-spinner fa-spin mr-2"></i>Creating...
              </span>
              <span v-else>Start Chat</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from './config/request';
import NotificationSound from './utils/NotificationSound';

export default {
  name: 'ChatInterface',
  data() {
    return {
      // UI States
      isExpanded: false,
      showMiniDashboard: true,
      
      // Conversations
      conversations: [],
      activeConversation: null,
      loading: true,
      unreadCount: 0,
      
      // Messages
      messages: [],
      messagesLoading: false,
      newMessage: '',
      fileToUpload: null,
      
      // Message tracking to prevent duplicates
      pendingMessages: new Map(),
      recentlyAddedServerMessages: new Set(),
      lastPollingRequestId: null,
      
      // User data
      currentUserId: null,
      users: [],
      filteredUsers: [],
      loadingUsers: false,
      userSearchQuery: '',
      selectedUser: null,
      
      // Modals
      showNewChatModal: false,
      creatingConversation: false,
    };
  },
  methods: {
    loadConversations(showLoading = true) {
      if (showLoading) {
        this.loading = true;
      }

      get('get_conversations', {})
        .then(response => {
          if (response.data.status) {
            // Process the received conversations
            const conversations = response.data.data || [];
            
            // Make sure unread_count is a number, not a string
            conversations.forEach(conv => {
              if (conv.unread_count !== undefined) {
                conv.unread_count = parseInt(conv.unread_count, 10) || 0;
              } else {
                conv.unread_count = 0;
              }
            });
            
            this.conversations = conversations;
            
            // Calculate total unread count for badge
            this.unreadCount = conversations.reduce((total, conv) => total + (conv.unread_count || 0), 0);

            // If we have an active conversation, refresh its data
            if (this.activeConversation) {
              const updatedConversation = this.conversations.find(c => c.id === this.activeConversation.id);
              if (updatedConversation) {
                this.activeConversation = updatedConversation;
                
                // If this is the active conversation, mark messages as read
                if (updatedConversation.unread_count > 0) {
                  post('mark_as_read', {
                    conversation_id: updatedConversation.id
                  }).then(() => {
                    // Update the unread count locally too
                    updatedConversation.unread_count = 0;
                    // Recalculate total unread count
                    this.calculateTotalUnreadCount();
                  }).catch(err => {
                    console.error('Error marking messages as read:', err);
                  });
                }
              }
            }
          } else {
            console.error('Error loading conversations:', response.data.message);
          }
        })
        .catch(error => {
          console.error('Error loading conversations:', error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    calculateTotalUnreadCount() {
      this.unreadCount = this.conversations.reduce((total, conv) => total + (conv.unread_count || 0), 0);
    },
    
    startNewChat() {
      // Load users if not already loaded
      if (this.users.length === 0) {
        this.loadUsersForChat();
      } else {
        // Initialize filtered users from existing user list
        this.filteredUsers = this.users.slice(0, 10);
      }
      
      // Reset search and selection
      this.userSearchQuery = '';
      this.selectedUser = null;
      
      // Show the modal
      this.showNewChatModal = true;
    },
    
    openChat(conversation) {
      this.activeConversation = conversation;
      this.showMiniDashboard = false;
      this.loadMessages(conversation.id);
      
      // If this conversation has unread messages, mark them as read
      if (conversation.unread_count && conversation.unread_count > 0) {
        post('mark_as_read', {
          conversation_id: conversation.id
        }).then(() => {
          // Update the unread count in our local data
          conversation.unread_count = 0;
          
          // Find this conversation in the full list and update it there too
          const index = this.conversations.findIndex(c => c.id === conversation.id);
          if (index !== -1) {
            this.conversations[index].unread_count = 0;
          }
          
          // Recalculate total unread count
          this.calculateTotalUnreadCount();
        }).catch(error => {
          console.error('Error marking messages as read:', error);
        });
      }
    },
    
    backToList() {
      this.showMiniDashboard = true;
      this.activeConversation = null;
      this.messages = [];
    },
    
    openFullChat() {
      // Collapse the chat widget
      this.isExpanded = false;

      // Redirect to full chat interface with the active conversation if any
      if (this.activeConversation) {
        this.$router.push({ 
          path: '/chat',
          query: { conversation: this.activeConversation.id }
        });
      } else {
        this.$router.push('/chat');
      }
    },
    
    loadMessages(conversationId) {
      this.messagesLoading = true;
      this.messages = [];

      get('get_messages', { conversation_id: conversationId })
        .then(response => {
          if (response.data.status) {
            let messagesArray = [];
            
            // Check the structure of the response
            if (response.data.data && response.data.data.messages) {
              // New response format
              messagesArray = response.data.data.messages;
              
              // Update active conversation with more details if available
              if (response.data.data.conversation) {
                this.activeConversation = {
                  ...this.activeConversation,
                  ...response.data.data.conversation
                };
              }
              
              // Update members if available
              if (response.data.data.members) {
                this.activeConversation.members = response.data.data.members;
              }
            } else {
              // Legacy format or flat array of messages
              messagesArray = Array.isArray(response.data.data) ? response.data.data : [];
            }

            // Create a map to deduplicate messages by ID
            const messageMap = new Map();
            
            messagesArray.forEach(message => {
              if (message && message.id) {
                // Only add if not already in the map
                if (!messageMap.has(message.id)) {
                  messageMap.set(message.id, message);
                }
              }
            });
            
            // Convert back to array and sort by created_at
            this.messages = Array.from(messageMap.values()).sort((a, b) => {
              return new Date(a.created_at) - new Date(b.created_at);
            });
            
            // Scroll to bottom after messages load
            this.$nextTick(() => {
              this.scrollToBottom();
            });
          } else {
            console.error('Error loading messages:', response.data.message);
          }
        })
        .catch(error => {
          console.error('Error loading messages:', error);
        })
        .finally(() => {
          this.messagesLoading = false;
        });
    },
    
    checkForNewMessages() {
      // Check for new messages in active conversation if one is open
      if (this.activeConversation) {
        const pollingRequestId = 'poll-' + Date.now();
        this.lastPollingRequestId = pollingRequestId;
        
        get('get_messages', {
          conversation_id: this.activeConversation.id
        }).then(response => {
          // If another polling request has started since this one, discard these results
          if (this.lastPollingRequestId !== pollingRequestId) return;
          
          if (response.data.status) {
            let serverMessages = [];
            
            // Handle the new response format
            if (response.data.data && response.data.data.messages) {
              serverMessages = response.data.data.messages;
              
              // Update conversation and members if available
              if (response.data.data.conversation) {
                this.activeConversation = {
                  ...this.activeConversation,
                  ...response.data.data.conversation
                };
              }
              
              if (response.data.data.members) {
                this.activeConversation.members = response.data.data.members;
              }
            } else if (Array.isArray(response.data.data)) {
              serverMessages = response.data.data;
            }
            
            // Process messages
            if (Array.isArray(serverMessages) && serverMessages.length > 0) {
              // Create maps for existing messages and server messages
              const existingMessagesMap = new Map();
              this.messages.forEach(msg => {
                if (msg && msg.id) {
                  existingMessagesMap.set(msg.id.toString(), msg);
                }
              });
              
              // Check for new messages
              const newMessages = serverMessages.filter(msg => 
                !existingMessagesMap.has(msg.id.toString()) && 
                !this.recentlyAddedServerMessages.has(msg.id.toString())
              );
              
              // If there are new messages
              if (newMessages.length > 0) {
                // Add new messages to our list
                const updatedMessages = [...this.messages];
                newMessages.forEach(newMsg => {
                  updatedMessages.push(newMsg);
                  this.recentlyAddedServerMessages.add(newMsg.id.toString());
                });
                
                // Sort by created_at
                updatedMessages.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
                
                // Update messages array
                this.messages = updatedMessages;
                
                // Play notification sound for new messages from others
                const newMessagesFromOthers = newMessages.filter(m => m.user_id !== this.getUserId());
                if (newMessagesFromOthers.length > 0) {
                  NotificationSound.play();
                }
                
                // Scroll to bottom
                this.$nextTick(() => {
                  this.scrollToBottom();
                });
              }
            }
          }
        }).catch(error => {
          console.error('Error checking for new messages:', error);
        });
      }
      
      // Also update conversations list to get fresh unread counts
      this.loadConversations(false);
    },
    
    loadUsersForChat() {
      this.loadingUsers = true;

      get('get_users_for_chat', {})
        .then(response => {
          if (response.data.status) {
            this.users = response.data.data;
            // Initialize filtered users with the first 10 users
            this.filteredUsers = this.users.slice(0, 10);
          } else {
            console.error('Error loading users for chat:', response.data.message);
          }
        })
        .catch(error => {
          console.error('Error loading users for chat:', error);
        })
        .finally(() => {
          this.loadingUsers = false;
        });
    },
    
    filterUsers() {
      if (!this.userSearchQuery.trim()) {
        this.filteredUsers = this.users.slice(0, 10);
        return;
      }

      if (!this.users.length) {
        this.filteredUsers = [];
        return;
      }
      
      const query = this.userSearchQuery.toLowerCase();
      this.filteredUsers = this.users.filter(user => {
        // Get display name and email from the correct property path
        let displayName = '';
        let email = '';
        
        if (user.data && user.data.display_name) {
          displayName = user.data.display_name;
        } else if (user.display_name) {
          displayName = user.display_name;
        }
        
        if (user.data && user.data.user_email) {
          email = user.data.user_email;
        } else if (user.user_email) {
          email = user.user_email;
        }
        
        // Match against both display name and email
        return (displayName && displayName.toLowerCase().includes(query)) ||
               (email && email.toLowerCase().includes(query));
      });
    },
    
    selectUser(user) {
      // Normalize user data structure
      this.selectedUser = {
        id: user.ID || user.id,
        display_name: user.data ? user.data.display_name : user.display_name,
        user_email: user.data ? user.data.user_email : user.user_email
      };
      
      // Clear the search UI
      this.userSearchQuery = '';
      this.filteredUsers = [];
    },
    
    createConversation() {
      if (!this.selectedUser || this.creatingConversation) return;

      this.creatingConversation = true;

      // Use the normalized user ID from our structure
      const userID = parseInt(this.selectedUser.id);

      if (!userID) {
        console.error('Invalid user ID:', this.selectedUser);
        this.creatingConversation = false;
        return;
      }

      post('create_conversation', {
        members: [userID],
        type: 'direct'
      })
        .then(response => {
          if (response.data.status) {
            // Close modal
            this.showNewChatModal = false;

            // Reset form
            this.selectedUser = null;
            this.userSearchQuery = '';

            // Reload conversations
            this.loadConversations(false);

            // Select the new conversation if available
            if (response.data.data && response.data.data.id) {
              this.openChat(response.data.data);
            }
          } else {
            console.error('Error creating conversation:', response.data.message);
            alert('Error creating conversation: ' + response.data.message);
          }
        })
        .catch(error => {
          console.error('Error creating conversation:', error);
          alert('Error creating conversation. Please try again later.');
        })
        .finally(() => {
          this.creatingConversation = false;
        });
    },
    
    sendMessage() {
      if (!this.canSendMessage) return;

      const formData = new FormData();
      formData.append('conversation_id', this.activeConversation.id);
      formData.append('message', this.newMessage);

      if (this.fileToUpload) {
        formData.append('file', this.fileToUpload);
      }

      // Create a unique temporary ID
      const tempId = 'temp-' + Date.now() + '-' + Math.floor(Math.random() * 10000);
      const messageContent = this.newMessage;
      const messageSentTime = new Date().toISOString();
      
      // Store details to help with identification later
      this.pendingMessages.set(tempId, {
        content: messageContent,
        time: messageSentTime,
        processed: false,
        lastChecked: Date.now()
      });
      
      // Add optimistic message
      const tempMessage = {
        id: tempId,
        conversation_id: this.activeConversation.id,
        user_id: this.getUserId(),
        message: messageContent,
        file_url: this.fileToUpload ? URL.createObjectURL(this.fileToUpload) : '',
        file_type: this.fileToUpload ? this.fileToUpload.type : '',
        created_at: messageSentTime,
        sender_name: this.$store.state.userDataModule.user.display_name || 'You',
        isTemporary: true
      };
      
      // Add to messages array
      this.messages = [...this.messages, tempMessage];
      
      // Scroll to bottom
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // Clear input
      this.newMessage = '';
      this.fileToUpload = null;

      // Focus on input field
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.focus();
        }
      });
      
      // Send message
      post('send_message', formData, true)
        .then(response => {
          if (response.data.status) {
            // Get the new message from the response
            let newMessage = null;
            
            if (response.data.data) {
              if (Array.isArray(response.data.data) && response.data.data.length > 0) {
                newMessage = response.data.data[0];
              } else if (typeof response.data.data === 'object' && response.data.data !== null) {
                newMessage = response.data.data;
              }
            }
            
            if (newMessage && newMessage.id) {
              // Remember this message ID to prevent duplication
              this.recentlyAddedServerMessages.add(newMessage.id.toString());
              
              // Mark temporary message as processed
              if (this.pendingMessages.has(tempId)) {
                const pendingMsg = this.pendingMessages.get(tempId);
                pendingMsg.processed = true;
                pendingMsg.serverMessageId = newMessage.id;
                this.pendingMessages.set(tempId, pendingMsg);
              }
              
              // Replace temporary message with server message
              const index = this.messages.findIndex(m => m.id === tempId);
              if (index !== -1) {
                const updatedMessages = [...this.messages];
                updatedMessages[index] = newMessage;
                this.messages = updatedMessages;
              }
            }
            
            // Update conversations list to reflect the new message
            setTimeout(() => {
              this.loadConversations(false);
            }, 500);
          } else {
            console.error('Error sending message:', response.data.message);
            // Remove failed message
            this.messages = this.messages.filter(m => m.id !== tempId);
            if (this.pendingMessages.has(tempId)) {
              this.pendingMessages.delete(tempId);
            }
          }
        })
        .catch(error => {
          console.error('Error sending message:', error);
          // Remove failed message
          this.messages = this.messages.filter(m => m.id !== tempId);
          if (this.pendingMessages.has(tempId)) {
            this.pendingMessages.delete(tempId);
          }
        });
    },
    
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }

      this.fileToUpload = file;

      // Focus on input field
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.focus();
        }
      });
    },
    
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight;
      }
    },
    
    getConversationName(conversation) {
      // For direct chats, show the other person's name
      if (conversation.type === 'direct') {
        // Find the other member
        const otherMember = conversation.members?.find(member => member.user_id !== this.getUserId());
        return otherMember ? otherMember.display_name : 'Chat';
      }

      // For group chats, show the group name
      return conversation.name || 'Group Chat';
    },
    
    getLastMessage(conversation) {
      if (!conversation.last_message) {
        return 'No messages yet';
      }

      if (conversation.last_message.file_url) {
        // If message has an attachment
        const fileType = this.getFileTypeName(conversation.last_message.file_type);
        return `${conversation.last_message.sender_name}: [${fileType}]${conversation.last_message.message ? ' ' + conversation.last_message.message : ''}`;
      }

      // Regular text message
      return conversation.last_message.sender_name + ': ' + conversation.last_message.message;
    },
    
    getFileTypeName(fileType) {
      if (!fileType) return 'File';

      if (fileType.startsWith('image/')) {
        return 'Image';
      } else if (fileType === 'application/pdf') {
        return 'PDF';
      } else if (fileType.startsWith('audio/')) {
        return 'Audio';
      } else if (fileType.startsWith('video/')) {
        return 'Video';
      } else {
        return 'File';
      }
    },
    
    formatTime(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      const now = new Date();
      const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) {
        // Today
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } else if (diffInDays === 1) {
        // Yesterday
        return 'Yesterday';
      } else if (diffInDays < 7) {
        // This week
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        return days[date.getDay()];
      } else {
        // More than a week ago
        return date.toLocaleDateString();
      }
    },
    
    formatMessageTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },
    
    formatMessageDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const now = new Date();
      const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) {
        return 'Today';
      } else if (diffInDays === 1) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString(undefined, {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    },
    
    shouldShowDate(message, index) {
      if (index === 0) return true;
      
      if (!message || !message.created_at || !this.messages[index - 1] || !this.messages[index - 1].created_at) {
        return false;
      }

      const currentDate = new Date(message.created_at).toLocaleDateString();
      const prevDate = new Date(this.messages[index - 1].created_at).toLocaleDateString();

      return currentDate !== prevDate;
    },
    
    isImage(fileType) {
      return fileType && fileType.startsWith('image/');
    },
    
    isPDF(fileType) {
      return fileType === 'application/pdf';
    },
    
    getFileIcon(fileType) {
      if (!fileType) return 'far fa-file';

      if (fileType.startsWith('image/')) {
        return 'far fa-file-image text-green-500';
      } else if (fileType === 'application/pdf') {
        return 'far fa-file-pdf text-red-500';
      } else if (fileType.startsWith('audio/')) {
        return 'far fa-file-audio text-blue-500';
      } else if (fileType.startsWith('video/')) {
        return 'far fa-file-video text-purple-500';
      } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'far fa-file-word text-blue-500';
      } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
        return 'far fa-file-excel text-green-500';
      } else {
        return 'far fa-file text-gray-500';
      }
    },
    
    openAttachment(url) {
      window.open(url, '_blank');
    }
  },
  mounted() {
    // Get current user ID

    // Load conversations
    this.loadConversations();

    // Set up polling for new messages and conversations
    this.messagePollingInterval = setInterval(() => {
      this.checkForNewMessages();
    }, 15000); // Every 15 seconds

    // Clean up on component unmount
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.messagePollingInterval);
    });
  },
  computed: {
    canSendMessage() {
      return (this.newMessage.trim() !== '' || this.fileToUpload) && this.activeConversation;
    }
  },
  watch: {
    isExpanded(newValue) {
      if (newValue) {
        // When expanding, make sure we have fresh data
        this.loadConversations();
      }
    }
  }
}
</script>

<style scoped>
/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Transition for the button icon */
button {
  transition: transform 0.3s ease;
}
</style>