<?php

use App\models\MDTemplateManager;

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

global $wpdb;

$wpdb->query('SET SESSION sql_mode=""');

$kc_charset_collate = $wpdb->get_charset_collate();

$table_name = $wpdb->prefix . 'md_template_manager';
// do not forget about tables prefix

$sql = "CREATE TABLE `{$table_name}` (
    id bigint(20) NOT NULL AUTO_INCREMENT,    
    name varchar(191) NOT NULL,
    category varchar(191) NOT NULL,
    content longtext NOT NULL,
    doctor_id bigint(20) UNSIGNED NULL,
    clinic_id bigint(20) UNSIGNED NULL,
    is_system tinyint(1) DEFAULT 0,
    share_status varchar(50) DEFAULT 'private',
    status tinyint(1) DEFAULT 1,
    created_at datetime NOT NULL,
    updated_at datetime NULL,
    PRIMARY KEY (id)
) $kc_charset_collate;";

maybe_create_table($table_name, $sql);

// Check if we need to populate default templates
$count_query = "select count(*) from " . $table_name;
$num = $wpdb->get_var($count_query);

if ($num == 0) {
    // Default templates will be inserted from our template.json file
    // after model is created
}