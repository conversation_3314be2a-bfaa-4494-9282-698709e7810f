<?php
/**
 * Migration: Add Service ID to Clinic Sessions
 */

if (!defined('ABSPATH')) {
    exit;
}

class AddServiceIdToClinicSessions {
    /**
     * Run the migration
     */
    public function up() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'kc_clinic_sessions';
        
        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        if ($table_exists) {
            // Check if column exists
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'service_id'");
            
            // If column doesn't exist, add it
            if (empty($column_exists)) {
                $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN `service_id` bigint(20) DEFAULT NULL AFTER `doctor_id`");
            }
        }
    }
    
    /**
     * Reverse the migration (optional)
     */
    public function down() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'kc_clinic_sessions';
        
        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        if ($table_exists) {
            // Check if column exists
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'service_id'");
            
            // If column exists, drop it
            if (!empty($column_exists)) {
                $wpdb->query("ALTER TABLE {$table_name} DROP COLUMN `service_id`");
            }
        }
    }
}