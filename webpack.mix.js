const mix = require('laravel-mix');

// Simple configuration - just compile JS files
mix.js('resources/js/app.js', 'assets/js/app.min.js');
mix.js('resources/js/front-app.js', 'assets/js/front-app.min.js');

// Only add CSS in development to avoid build issues
if (process.env.NODE_ENV !== 'production') {
    mix.sass('resources/sass/app.scss', 'assets/css/app.min.css');
    mix.sass('resources/sass/front-app.scss', 'assets/css/front-app.min.css');
}

// jQuery autoload
mix.autoload({
    jquery: ['$', 'window.jQuery', 'jQuery', 'jquery']
});

// Override Laravel Mix's webpack config completely
mix.webpackConfig({
    resolve: {
        alias: {
            '@': __dirname + '/resources/js'
        }
    },
    optimization: {
        minimize: false,
        minimizer: [],
        splitChunks: false
    },
    plugins: [],
    // Override the mode to development even in production to avoid Terser
    mode: 'development'
});

// Force disable all Laravel Mix optimizations
mix.options({
    terser: {
        terserOptions: {
            compress: false,
            mangle: false
        }
    },
    processCssUrls: false,
    postCss: []
});