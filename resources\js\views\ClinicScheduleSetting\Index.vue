<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button @click="$router.back()"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="m12 19-7-7 7-7"></path>
            <path d="M19 12H5"></path>
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation?.clinic_schedule?.holiday_list || "Holiday List" }}
        </h1>
      </div>
      <div class="flex gap-3">
        <module-data-export v-if="kcCheckPermission('clinic_schedule_export')" :module-data="clinicScheduleList.data"
          :module-name="formTranslation.clinic_schedule.holiday_list" module-type="clinic_doctor_holiday">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          {{ formTranslation.common.import }}
        </module-data-export>
        <button v-if="kcCheckPermission('clinic_schedule_add')" @click="openCreateModal"
          class="px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800 flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M12 5v14M5 12h14" />
          </svg>
          <span>{{ formTranslation.clinic_schedule.add_holiday_btn }}</span>
        </button>
      </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="space-y-6 mb-6">
      <!-- Search Bar -->
      <div class="relative">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="lucide lucide-search w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.3-4.3"></path>
        </svg>
        <input type="text" v-model="serverParams.searchTerm" @input="globalFilter($event)" :placeholder="formTranslation?.common?.search_holiday_data_global_placeholder ||
          'Search...'
          "
          class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
      </div>

      <!-- Filters -->
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{
            formTranslation.clinic_schedule.dt_lbl_from_date
          }}</label>
          <input type="date" v-model="serverParams.columnFilters.start_date" @input="onColumnFilter"
            class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{
            formTranslation.clinic_schedule.dt_lbl_to_date
          }}</label>
          <input type="date" v-model="serverParams.columnFilters.end_date" @input="onColumnFilter"
            class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        </div>
      </div>
    </div>

    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div class="relative" v-if="pageLoader">
        <div class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>

      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th v-for="column in columns" :key="column.field"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr v-for="(row, index) in clinicScheduleList.data" :key="row.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">{{ row.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.module_type }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ formatDate(row.start_date) }}
              <span v-if="row.start_time && !row.all_day" class="text-xs text-gray-500 block">
                {{ formatTime(row.start_time) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ formatDate(row.end_date) }}
              <span v-if="row.end_time && !row.all_day" class="text-xs text-gray-500 block">
                {{ formatTime(row.end_time) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span v-if="row.all_day" class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                Full Day
              </span>
              <span v-else class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                Specific Hours
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2">
                <button v-if="kcCheckPermission('clinic_schedule_edit')" @click="editClinicSchedule(row, row.id)"
                  class="p-1 hover:bg-gray-100 rounded" :title="formTranslation.common.edit">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-pen w-4 h-4 text-gray-600">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                  </svg>
                </button>
                <button v-if="kcCheckPermission('clinic_schedule_delete')" @click="deleteClinicSchedule(row.id)"
                  class="p-1 hover:bg-gray-100 rounded" :title="formTranslation.clinic_schedule.dt_lbl_dlt">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-trash2 w-4 h-4">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    <line x1="10" x2="10" y1="11" y2="17"></line>
                    <line x1="14" x2="14" y1="11" y2="17"></line>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="!clinicScheduleList.data.length">
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              {{ formTranslation.common.no_data_found }}
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select v-model="serverParams.perPage" @change="onPerPageChange"
            class="border border-gray-300 rounded-md text-sm p-1">
            <option :value="10">10</option>
            <option :value="25">25</option>
            <option :value="50">50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ serverParams.page }} of
            {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <div class="flex gap-2">
            <button @click="onPageChange(serverParams.page - 1)" :disabled="serverParams.page === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600">
                <path d="m15 18-6-6 6-6" />
              </svg>
            </button>
            <button @click="onPageChange(serverParams.page + 1)" :disabled="serverParams.page >= Math.ceil(totalRows / serverParams.perPage)
              " class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600">
                <path d="m9 18 6-6-6-6" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <template>
      <transition name="modal" enter-active-class="transition ease-out duration-200"
        enter-from-class="opacity-0 translate-y-4" enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition ease-in duration-150" leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 translate-y-4">
        <div v-if="showModal" class="fixed inset-0 z-50 overflow-y-auto">
          <!-- Backdrop -->
          <div class="fixed inset-0 bg-black bg-opacity-40 backdrop-blur-sm transition-opacity"></div>

          <!-- Modal Container -->
          <div class="flex min-h-screen items-center justify-center p-4">
            <div class="relative w-full max-w-2xl transform rounded-xl bg-white shadow-xl transition-all" @click.stop>
              <!-- Header -->
              <div class="border-b border-gray-100">
                <div class="flex items-center justify-between p-6">
                  <h2 class="text-xl font-semibold text-gray-900">
                    {{
                      editMode
                        ? formTranslation.common.edit
                        : formTranslation.clinic_schedule.add_holiday_btn
                    }}
                  </h2>

                  <button @click="closeModal"
                    class="rounded-full p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Content -->
              <div class="p-6">
                <Create v-if="showModal" :holidayId="holidayId" :holidayDetail="editHolidayDetail"
                  @getClinicScheduleList="getClinicScheduleList" @closeForm="closeModal" />
              </div>
            </div>
          </div>
        </div>
      </transition>
    </template>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import Create from "./Create";
import _ from "lodash";

export default {
  name: "HolidaySchedule",
  components: { Create },

  data() {
    return {
      showModal: false,
      editMode: false,
      holidayId: -1,
      editHolidayDetail: [],
      pageLoader: true,
      clinicScheduleList: {
        data: [],
      },
      serverParams: {
        columnFilters: {
          service_type: "",
          start_date: "",
          end_date: "", // Added to prevent undefined
        },
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "", // Initialize searchTerm
        type: "list",
      },
      totalRows: 0,
      request_status: "off",
    };
  },

  computed: {
    columns() {
      return this.getDefaultColumns();
    },
  },

  mounted() {
    if (["patient"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
      return;
    }
    this.init();
  },

  methods: {
    getDefaultColumns() {
      return [
        {
          field: "id",
          label: this.formTranslation?.common?.id || "ID",
        },
        {
          field: "module_type",
          label:
            this.formTranslation?.clinic_schedule?.dt_lbl_schedul_of ||
            "Schedule Of",
        },
        {
          field: "name",
          label: this.formTranslation?.clinic_schedule?.dt_lbl_name || "Name",
        },
        {
          field: "start_date",
          label:
            this.formTranslation?.clinic_schedule?.dt_lbl_from_date ||
            "Start Date",
        },
        {
          field: "end_date",
          label:
            this.formTranslation?.clinic_schedule?.dt_lbl_to_date || "End Date",
        },
        {
          field: "all_day",
          label: "Duration",
        },
        {
          field: "actions",
          label:
            this.formTranslation?.clinic_schedule?.dt_lbl_action || "Actions",
        },
      ];
    },

    formatDate(date) {
      return date ? new Date(date).toLocaleDateString() : "";
    },
    
    formatTime(time) {
      if (!time) return "";
      
      // Convert 24-hour time format to 12-hour format with AM/PM
      try {
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours, 10);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const hour12 = hour % 12 || 12;
        return `${hour12}:${minutes} ${ampm}`;
      } catch (e) {
        console.error("Time formatting error:", e);
        return time; // Return original time if there's an error
      }
    },

    openCreateModal() {
      this.editMode = false;
      this.holidayId = -1;
      this.editHolidayDetail = [];
      this.showModal = true;
    },

    closeModal() {
      this.showModal = false;
      this.editMode = false;
      this.holidayId = -1;
      this.editHolidayDetail = [];
    },

    editClinicSchedule(data, id) {
      this.editMode = true;
      // Make a deep copy of the data to avoid reference issues
      this.editHolidayDetail = JSON.parse(JSON.stringify(data));
      this.holidayId = id;
      this.showModal = true;
      
      // Ensure the modal is properly initialized with the correct data
      this.$nextTick(() => {
        // This ensures the Create component has time to mount before we pass data to it
        setTimeout(() => {
          // Force a refresh of the component
          this.$forceUpdate();
        }, 100);
      });
    },

    init() {
      this.getClinicScheduleList();
    },

    async getClinicScheduleList() {
      try {
        this.pageLoader = true;
        const response = await get("clinic_schedule_list", this.serverParams);
        if (response.data.status) {
          this.clinicScheduleList.data = response.data.data;
          this.totalRows = response.data.total_rows;
        } else {
          this.clinicScheduleList.data = [];
          this.totalRows = 0;
        }
      } catch (error) {
        console.error("Failed to fetch schedule list:", error);
        this.$toast.error(this.formTranslation.clinic_schedule.dt_srvr_err);
      } finally {
        this.pageLoader = false;
      }
    },

    onPageChange(page) {
      if (
        page >= 1 &&
        page <= Math.ceil(this.totalRows / this.serverParams.perPage)
      ) {
        this.serverParams.page = page;
        this.getClinicScheduleList();
      }
    },

    onPerPageChange() {
      this.serverParams.page = 1;
      this.getClinicScheduleList();
    },

    globalFilter: _.debounce(function () {
      this.serverParams.page = 1;
      this.getClinicScheduleList();
    }, 300),

    onColumnFilter: _.debounce(function () {
      this.serverParams.page = 1;
      this.getClinicScheduleList();
    }, 300),

    deleteClinicSchedule(id) {
      // Use Swal for confirmation dialog
      this.$swal.fire({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        text: this.formTranslation.clinic_schedule.dt_press_dlt,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: this.formTranslation.common.yes,
        cancelButtonText: this.formTranslation.common.cancel
      }).then((result) => {
        if (result.isConfirmed) {
          // Show loading state
          this.$swal.fire({
            title: 'Deleting...',
            text: 'Please wait while we process your request',
            allowOutsideClick: false,
            didOpen: () => {
              this.$swal.showLoading();
            }
          });

          get("clinic_schedule_delete", {
            id: id
          })
          .then((response) => {
            if (response.data.status) {
              this.getClinicScheduleList();
              this.$swal.fire({
                icon: 'success',
                title: 'Success',
                text: response.data.message,
                showConfirmButton: false,
                timer: 1500
              });
            } else {
              this.$swal.fire({
                icon: 'error',
                title: 'Error',
                text: response.data.message || this.formTranslation.clinic_schedule.dt_srvr_err,
                showConfirmButton: true
              });
            }
          })
          .catch((error) => {
            console.error("Failed to delete schedule:", error);
            this.$swal.fire({
              icon: 'error',
              title: 'Error',
              text: this.formTranslation.clinic_schedule.dt_srvr_err,
              showConfirmButton: true
            });
          });
        }
      });
    },
  },
};
</script>

<style>
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

@-moz-document url-prefix() {
  [type="date"] {
    background-image: none;
  }
}
</style>
