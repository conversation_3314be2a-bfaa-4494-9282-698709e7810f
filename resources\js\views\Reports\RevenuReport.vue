<template>
  <div class="w-full">
    <div class="w-full">
      <b-overlay
        :show="userData.addOns.kiviPro != true"
        variant="white"
        :opacity="overlayOpacity"
      >
        <template #overlay>
          <overlay-message addon_type="pro"></overlay-message>
        </template>
        <div class="bg-white rounded-lg shadow">
          <div class="mt-2">
            <div class="p-2 ml-4">
              <h3 class="text-primary text-xl font-bold">
                {{ formTranslation.common.filters }}
              </h3>
            </div>
          </div>
          <div class="mx-3 mb-3">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <!-- Clinic Select -->
              <div
                v-if="currentUser !== 'kiviCare_clinic_admin'"
                class="transform transition-all duration-200 pb-4"
              >
                <div class="px-4 transition-colors duration-200">
                  <label
                    for="clinic_id"
                    class="flex items-center text-sm font-medium text-gray-700 mb-2"
                  >
                    <svg
                      class="w-4 h-4 mr-2 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                    {{ formTranslation.clinic.select_clinic }}
                    <span class="ml-1 text-red-500">*</span>
                  </label>
                  <select
                    v-model="filterData.clinic_id"
                    id="clinic_id"
                    @change="clinicChange"
                    :disabled="clinicMultiselectLoader"
                    class="w-full px-3 py-2 bg-white rounded-md border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 cursor-pointer appearance-none"
                  >
                    <option value="" disabled selected>
                      {{ formTranslation.plh_search }}
                    </option>
                    <option
                      v-for="clinic in clinics"
                      :key="clinic.id"
                      :value="clinic.id"
                    >
                      {{ clinic.label }}
                    </option>
                  </select>
                  <!-- Custom dropdown arrow -->
                  <div
                    class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                  >
                    <svg
                      class="w-4 h-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Doctor Select -->
              <div
                v-if="getUserRole !== 'doctor'"
                class="transform transition-all duration-200 pb-4"
              >
                <div class="px-4 transition-colors duration-200">
                  <label
                    for="doctor_id"
                    class="flex items-center text-sm font-medium text-gray-700 mb-2"
                  >
                    <svg
                      class="w-4 h-4 mr-2 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                    {{ formTranslation.patient_encounter.tag_select_doctor }}
                    <span class="ml-1 text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="filterData.doctor_id"
                      id="doctor_id"
                      @change="getAllSubType"
                      :disabled="doctorMultiselectLoader"
                      class="w-full px-3 py-2 bg-white rounded-md border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 cursor-pointer appearance-none"
                    >
                      <option value="" disabled selected>
                        {{ formTranslation.plh_search }}
                      </option>
                      <option
                        v-for="doctor in doctors"
                        :key="doctor.id"
                        :value="doctor.id"
                      >
                        {{ doctor.label }}
                      </option>
                    </select>
                    <!-- Custom dropdown arrow -->
                    <div
                      class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                    >
                      <svg
                        class="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Filter Type -->
              <div class="transform transition-all duration-200 pb-4">
                <div class="px-4 transition-colors duration-200">
                  <label
                    for="filter_id"
                    class="flex items-center text-sm font-medium text-gray-700 mb-2"
                  >
                    <svg
                      class="w-4 h-4 mr-2 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                      />
                    </svg>
                    {{ formTranslation.reports.filter_by }}
                    <span class="ml-1 text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="filterData.filter_id"
                      id="filter_id"
                      @change="getAllSubType"
                      class="w-full px-3 py-2 bg-white rounded-md border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 cursor-pointer appearance-none"
                    >
                      <option value="" disabled selected>
                        {{ formTranslation.plh_select }}
                      </option>
                      <option
                        v-for="filter in filterReport"
                        :key="filter.id"
                        :value="filter.id"
                      >
                        {{ filter.label }}
                      </option>
                    </select>
                    <!-- Custom dropdown arrow -->
                    <div
                      class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                    >
                      <svg
                        class="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Sub Type -->
              <div class="transform transition-all duration-200 pb-4">
                <div class="px-4 transition-colors duration-200">
                  <label
                    for="sub_type_id"
                    class="flex items-center text-sm font-medium text-gray-700 mb-2"
                  >
                    <svg
                      class="w-4 h-4 mr-2 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    {{ formTranslation.reports.filter_by }}
                    <span class="ml-1 text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="filterData.sub_type"
                      id="sub_type_id"
                      @change="getAllSubType"
                      class="w-full px-3 py-2 bg-white rounded-md border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 cursor-pointer appearance-none"
                    >
                      <option value="" disabled selected>
                        {{ formTranslation.doctor.select_year }}
                      </option>
                      <option
                        v-for="(type, index) in Object.keys(filterSubType)"
                        :key="index"
                        :value="type"
                      >
                        {{ type }}
                      </option>
                    </select>
                    <!-- Custom dropdown arrow -->
                    <div
                      class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                    >
                      <svg
                        class="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-12 gap-4 mb-3">
          <div class="md:col-span-4">
            <div class="bg-white rounded-lg shadow">
              <div class="p-4 border-b">
                <h3 class="text-primary text-xl font-bold">
                  {{ formTranslation.reports.clinic_revenue_overall }}
                </h3>
              </div>
              <div class="p-4 relative">
                <div
                  v-if="chartLoading"
                  class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75"
                >
                  <loader-component-2></loader-component-2>
                </div>
                <apexcharts
                  v-show="!chartLoading"
                  type="donut"
                  :height="320"
                  id="someId"
                  ref="revenueChart"
                  :options="chartOptions"
                  :series="series"
                >
                </apexcharts>
              </div>
            </div>
          </div>

          <div class="md:col-span-8">
            <div class="bg-white rounded-lg shadow">
              <div class="p-4 border-b">
                <h3 class="text-primary text-xl font-bold">
                  {{ formTranslation.reports.clinic_revenue_detail }}
                </h3>
              </div>
              <div class="p-4 relative">
                <div
                  v-if="chartLoading"
                  class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75"
                >
                  <loader-component-2></loader-component-2>
                </div>
                <apexcharts
                  v-show="!chartLoading"
                  type="bar"
                  height="320"
                  id="barId"
                  ref="barRevenueChart"
                  :options="barChartOption"
                  :series="barseries"
                >
                </apexcharts>
              </div>
            </div>
          </div>

          <div class="md:col-span-12">
            <div class="bg-white rounded-lg shadow">
              <div class="p-4 border-b">
                <h3 class="text-primary text-xl font-bold">
                  {{ formTranslation.reports.clinic_doctor_revenue }}
                </h3>
              </div>
              <div class="p-4 relative">
                <div
                  v-if="chartLoading"
                  class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75"
                >
                  <loader-component-2></loader-component-2>
                </div>
                <apexcharts
                  v-show="!chartLoading"
                  type="bar"
                  height="320"
                  id="doctorId"
                  ref="barDoctorRevenueChart"
                  :options="barDoctorOption"
                  :series="docseries"
                >
                </apexcharts>
              </div>
            </div>
          </div>

          <div class="md:col-span-6">
            <div class="bg-white rounded-lg shadow">
              <div class="p-4 border-b">
                <div class="flex justify-between items-center flex-wrap">
                  <h3 class="text-primary text-xl font-bold mr-2">
                    {{ formTranslation.common.clinic_appointment_count }}
                  </h3>
                  <select
                    v-model="filterData.appointment_status_clinic"
                    @change="clinicAppointmentCount"
                    class="form-select rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                  >
                    <option
                      v-for="(index, key) in allStatus"
                      :key="key"
                      :value="index.value"
                    >
                      {{ index.label }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="p-4 relative">
                <div
                  v-if="appointmentClinicChartLoading"
                  class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75"
                >
                  <loader-component-2></loader-component-2>
                </div>
                <apexcharts
                  v-show="!appointmentClinicChartLoading"
                  type="bar"
                  height="320"
                  id="clinicId"
                  ref="barClinicAppointmentCountChart"
                  :options="barDoctorOption"
                  :series="clinicAppointmentSeries"
                >
                </apexcharts>
              </div>
            </div>
          </div>

          <div class="md:col-span-6">
            <div class="bg-white rounded-lg shadow">
              <div class="p-4 border-b">
                <div class="flex justify-between items-center flex-wrap">
                  <h3 class="text-primary text-xl font-bold mr-2">
                    {{ formTranslation.common.doctor_appointment_count }}
                  </h3>
                  <select
                    v-model="filterData.appointment_status_doctor"
                    @change="appointmentCount"
                    class="form-select rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                  >
                    <option
                      v-for="(index, key) in allStatus"
                      :key="key"
                      :value="index.value"
                    >
                      {{ index.label }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="p-4 relative">
                <div
                  v-if="appointmentDoctorChartLoading"
                  class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75"
                >
                  <loader-component-2></loader-component-2>
                </div>
                <apexcharts
                  v-show="!appointmentDoctorChartLoading"
                  type="bar"
                  height="320"
                  id="doctorId"
                  ref="barAppointmentCountChart"
                  :options="barDoctorOption"
                  :series="appointmentSeries"
                >
                </apexcharts>
              </div>
            </div>
          </div>
        </div>
      </b-overlay>
    </div>
  </div>
</template>
<script>
import VueApexCharts from "vue-apexcharts";
import { post, get } from "../../config/request";
export default {
  name: "RevenueReport",
  data: () => {
    return {
      currentUser: window.request_data.current_user_role,
      key: 0,
      series: [],
      chartOptions: {
        chart: {
          type: "donut",
          height: 320,
        },
        legend: {
          showForSingleSeries: true,
          position: "bottom",
        },
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width: 320,
              },
              legend: {
                position: "bottom",
              },
            },
          },
        ],
        plotOptions: {
          pie: {
            donut: {
              labels: {
                show: true,
                total: {
                  show: true,
                  showAlways: true,
                  label: "Total",
                },
              },
            },
          },
        },
        series: [],
        noData: {
          text: "Loading.....",
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
        },
        labels: [],
        tooltip: {
          y: {
            formatter: function (val) {
              return val;
            },
          },
        },
      },
      barseries: [
        {
          name: "",
          data: [],
        },
      ],
      docseries: [
        {
          name: "",
          data: [],
        },
      ],
      barChartOption: {
        chart: {
          type: "bar",
          height: 320,
        },
        xaxis: {
          categories: [],
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: "30%",
            endingShape: "rounded",
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          show: true,
          width: 2,
          colors: ["transparent"],
        },
        fill: {
          opacity: 1,
        },
        tooltip: {},
        noData: {
          text: "Loading.....",
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
        },
        legend: {
          showForSingleSeries: true,
        },
      },
      barDoctorOption: {
        chart: {
          type: "bar",
          height: 350,
        },
        xaxis: {
          categories: [],
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: "30%",
            endingShape: "rounded",
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          show: true,
          width: 2,
          colors: ["transparent"],
        },
        fill: {
          opacity: 1,
        },
        tooltip: {},
        noData: {
          text: "Loading.....",
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
        },
        legend: {
          showForSingleSeries: true,
        },
      },
      filterData: {},
      filterReport: [
        {
          id: "weekly",
          label: "Weekly",
        },
        {
          id: "monthly",
          label: "Monthly",
        },
        {
          id: "yearly",
          label: "Yearly",
        },
      ],
      getAllType: [],
      filterSubType: [],
      clinic_currency: {
        prefix: "",
        postfix: "",
      },
      clinics: [],
      clinicMultiselectLoader: true,
      chartLoading: false,
      appointmentClinicChartLoading: false,
      appointmentDoctorChartLoading: false,
      doctorMultiselectLoader: true,
      doctors: [],
      doctorApiData: {
        data_type: "clinic_doctors",
        clinic_id: "",
      },
      allStatus: [
        { label: "all", value: "all" },
        { label: "booked", value: "1" },
        { label: "completed", value: "3" },
        { label: "cancelled", value: "cancel" },
        { label: "checkin", value: "4" },
        { label: "pending", value: "2" },
      ],
      appointmentSeries: [
        {
          name: "",
          data: [],
        },
      ],
      clinicAppointmentSeries: [
        {
          name: "",
          data: [],
        },
      ],
      doctor_colors: [],
      clinic_colors: [],
    };
  },
  mounted() {
    if (["doctor", "patient"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.allStatus = this.allStatus.map((item) => {
      if (["booked", "pending"].includes(item.label)) {
        item.label = this.formTranslation.appointments[item.label];
      } else {
        item.label = this.formTranslation.common[item.label];
      }
      return item;
    });
    this.filterData = this.defaultFilterData();
    get("get_all_report_type", {})
      .then((response) => {
        if (
          response.data.status !== undefined &&
          response.data.status === true
        ) {
          this.getAllType = response.data.data;
          this.filterSubType = this.getAllType.years;
          this.filterData.sub_type = this.getAllType.default_year;
          this.clinic_currency = response.data.clinic_currency;
          this.doctor_colors = response.data.doctor_colors;
          this.clinic_colors = response.data.clinic_colors;
        }
        this.init();
      })
      .catch((error) => {
        console.log(error);
      });
  },
  components: {
    apexcharts: VueApexCharts,
  },
  methods: {
    init: function () {
      this.getClinicRevenue();
      this.barChartClinicRevenue();
      this.doctorRevenue();
      this.getClinic();
      this.appointmentCount();
      this.clinicAppointmentCount();
      this.getDoctorsData(0);
    },
    defaultFilterData: function () {
      return {
        clinic_id: {
          id: "all",
          label: this.formTranslation.common.all,
        },
        filter_id: {
          id: "yearly",
          label: this.formTranslation.common.yearly,
        },
        doctor_id: {
          id: "all",
          label: this.formTranslation.common.all,
        },
        sub_type: "",
        appointment_status_clinic: "all",
        appointment_status_doctor: "all",
        patient_id: "",
      };
    },
    getClinicRevenue: function () {
      let _this = this;
      this.chartLoading = true;
      get("get_clinic_revenue", this.filterData)
        .then((response) => {
          _this.chartLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            _this.series = response.data.data;
            _this.chartOptions.labels = response.data.labels;
            _this.chartOptions.noData.text =
              _this.formTranslation.common.no_data_found;
            _this.chartOptions.series = response.data.data;
            _this.chartOptions.plotOptions.pie.donut.labels.total.label =
              _this.formTranslation.common.total;
            _this.chartOptions.legend.formatter = function (seriesName, opts) {
              let val =
                _this.clinic_currency.prefix +
                (typeof numberWithCommas !== "undefined"
                  ? numberWithCommas(opts.w.globals.series[opts.seriesIndex])
                  : opts.w.globals.series[opts.seriesIndex]) +
                _this.clinic_currency.postfix;
              return `<div class="legend-info"><span>${seriesName}</span>: <strong>${val}</strong></div>`;
            };
            _this.chartOptions.plotOptions.pie.donut.labels.total.formatter =
              function (w) {
                let totals = w.globals.seriesTotals.reduce((a, b) => {
                  return a + b;
                }, 0);
                return (
                  _this.clinic_currency.prefix +
                  (typeof numberWithCommas !== "undefined"
                    ? numberWithCommas(totals)
                    : val) +
                  _this.clinic_currency.postfix
                );
              };
            _this.chartOptions.tooltip.y.formatter = function (val) {
              return (
                _this.clinic_currency.prefix +
                (typeof numberWithCommas !== "undefined"
                  ? numberWithCommas(val)
                  : val) +
                _this.clinic_currency.postfix
              );
            };
            if (this.clinic_colors.length > 0) {
              let color = this.clinic_colors.slice(
                0,
                response.data.data.length
              );
              _this.chartOptions.colors = color;
              _this.chartOptions.fill = {
                colors: color,
              };
            }
            _this.$refs.revenueChart.updateSeries(response.data.data);
            _this.$refs.revenueChart.updateOptions(_this.chartOptions);
          }
        })
        .catch((error) => {
          _this.chartLoading = false;
          console.log(error);
        });
    },
    barChartClinicRevenue() {
      let _this = this;
      this.chartLoading = true;
      get("get_clinic_bar_revenue", this.filterData)
        .then((response) => {
          _this.chartLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            _this.barseries = response.data.data;
            if (
              this.userData.theme_mode !== undefined &&
              [true, "true"].includes(this.userData.theme_mode)
            ) {
              response.data.data = response.data.data.map((item) => {
                item.data = item.data.reverse();
                return item;
              });
              _this.$refs.barRevenueChart.updateSeries(response.data.data);
            } else {
              _this.$refs.barRevenueChart.updateSeries(response.data.data);
            }
            if (this.clinic_colors.length > 0) {
              let color = this.clinic_colors.slice(
                0,
                response.data.data.length
              );
              _this.chartOptions.colors = color;
              _this.chartOptions.fill = {
                colors: color,
              };
            }
            let updateOptions = {
              xaxis: {
                categories:
                  this.userData.theme_mode !== undefined &&
                  [true, "true"].includes(this.userData.theme_mode)
                    ? response.data.date.reverse()
                    : response.data.date,
              },
              noData: {
                text: _this.formTranslation.common.no_data_found,
              },
              yaxis: {
                labels: {
                  formatter: function (val) {
                    return (
                      _this.clinic_currency.prefix +
                      (typeof numberWithCommas !== "undefined"
                        ? numberWithCommas(val)
                        : val) +
                      _this.clinic_currency.postfix
                    );
                  },
                },
                opposite:
                  this.userData.theme_mode !== undefined &&
                  [true, "true"].includes(this.userData.theme_mode)
                    ? true
                    : false,
              },
            };
            if (this.clinic_colors.length > 0) {
              let color = this.clinic_colors.slice(
                0,
                response.data.data.length
              );
              updateOptions.colors = color;
              updateOptions.fill = {
                colors: color,
              };
            }
            _this.$refs.barRevenueChart.updateOptions(updateOptions);
            // this.doctorRevenue()
          }
        })
        .catch((error) => {
          _this.chartLoading = false;
          console.log(error);
        });
    },
    doctorRevenue() {
      let _this = this;
      _this.chartLoading = true;
      get("get_doctor_wise_revenue", this.filterData)
        .then((response) => {
          _this.chartLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            _this.docseries = response.data.data;
            if (
              this.userData.theme_mode !== undefined &&
              [true, "true"].includes(this.userData.theme_mode)
            ) {
              response.data.data = response.data.data.map((item) => {
                item.data = item.data.reverse();
                return item;
              });
              _this.$refs.barDoctorRevenueChart.updateSeries(
                response.data.data
              );
            } else {
              _this.$refs.barDoctorRevenueChart.updateSeries(
                response.data.data
              );
            }
            let chartOption = {
              xaxis: {
                categories:
                  this.userData.theme_mode !== undefined &&
                  [true, "true"].includes(this.userData.theme_mode)
                    ? response.data.date.reverse()
                    : response.data.date,
              },
              noData: {
                text: _this.formTranslation.common.no_data_found,
              },
              yaxis: {
                labels: {
                  formatter: function (val) {
                    return (
                      _this.clinic_currency.prefix +
                      (typeof numberWithCommas !== "undefined"
                        ? numberWithCommas(val)
                        : val) +
                      _this.clinic_currency.postfix
                    );
                  },
                },
                opposite:
                  this.userData.theme_mode !== undefined &&
                  [true, "true"].includes(this.userData.theme_mode)
                    ? true
                    : false,
              },
            };
            if (this.doctor_colors.length > 0) {
              let color = this.doctor_colors.slice(
                0,
                response.data.data.length
              );
              chartOption.colors = color;
              chartOption.fill = {
                colors: color,
              };
            }
            _this.$refs.barDoctorRevenueChart.updateOptions(chartOption);
          }
        })
        .catch((error) => {
          _this.chartLoading = false;
          console.log(error);
        });
    },
    clinicChange(selectOption) {
      let clinic_id = 0;
      if (selectOption.id && selectOption.id !== "all") {
        this.doctorApiData.data_type = "get_users_by_clinic";
        clinic_id = selectOption.id;
      } else {
        this.doctorApiData.data_type = "clinic_doctors";
      }
      this.getDoctorsData(clinic_id);
      this.getAllSubType(selectOption);
    },
    appointmentCount() {
      let _this = this;
      _this.appointmentDoctorChartLoading = true;
      get("get_appointment_count", this.filterData).then((response) => {
        _this.appointmentDoctorChartLoading = false;
        if (
          response.data.status !== undefined &&
          response.data.status === true
        ) {
          _this.appointmentSeries = response.data.data;
          if (
            this.userData.theme_mode !== undefined &&
            [true, "true"].includes(this.userData.theme_mode)
          ) {
            response.data.data = response.data.data.map((item) => {
              item.data = item.data.reverse();
              return item;
            });
            _this.$refs.barAppointmentCountChart.updateSeries(
              response.data.data
            );
          } else {
            _this.$refs.barAppointmentCountChart.updateSeries(
              response.data.data
            );
          }
          let chartOption = {
            xaxis: {
              categories:
                this.userData.theme_mode !== undefined &&
                [true, "true"].includes(this.userData.theme_mode)
                  ? response.data.date.reverse()
                  : response.data.date,
            },
            yaxis: {
              opposite:
                this.userData.theme_mode !== undefined &&
                [true, "true"].includes(this.userData.theme_mode)
                  ? true
                  : false,
            },
            noData: {
              text: _this.formTranslation.common.no_data_found,
            },
          };
          if (this.doctor_colors.length > 0) {
            let color = this.doctor_colors.slice(0, response.data.data.length);
            chartOption.colors = color;
            chartOption.fill = {
              colors: color,
            };
          }
          _this.$refs.barAppointmentCountChart.updateOptions(chartOption);
        }
      });
    },
    clinicAppointmentCount() {
      let _this = this;
      _this.appointmentClinicChartLoading = true;
      get("get_clinic_appointment_count", this.filterData).then((response) => {
        _this.appointmentClinicChartLoading = false;
        if (
          response.data.status !== undefined &&
          response.data.status === true
        ) {
          _this.clinicAppointmentSeries = response.data.data;
          if (
            this.userData.theme_mode !== undefined &&
            [true, "true"].includes(this.userData.theme_mode)
          ) {
            response.data.data = response.data.data.map((item) => {
              item.data = item.data.reverse();
              return item;
            });
            _this.$refs.barClinicAppointmentCountChart.updateSeries(
              response.data.data
            );
          } else {
            _this.$refs.barClinicAppointmentCountChart.updateSeries(
              response.data.data
            );
          }
          let chartOption = {
            xaxis: {
              categories:
                this.userData.theme_mode !== undefined &&
                [true, "true"].includes(this.userData.theme_mode)
                  ? response.data.date.reverse()
                  : response.data.date,
            },
            yaxis: {
              opposite:
                this.userData.theme_mode !== undefined &&
                [true, "true"].includes(this.userData.theme_mode)
                  ? true
                  : false,
            },
            noData: {
              text: _this.formTranslation.common.no_data_found,
            },
          };
          if (this.clinic_colors.length > 0) {
            let color = this.clinic_colors.slice(0, response.data.data.length);
            chartOption.colors = color;
            chartOption.fill = {
              colors: color,
            };
          }
          _this.$refs.barClinicAppointmentCountChart.updateOptions(chartOption);
        }
      });
    },
    getAllSubType(selectOption) {
      var data = this.filterData;
      if (selectOption.id == "weekly") {
        this.filterSubType = this.getAllType.weeks;
        this.filterData.sub_type = this.getAllType.default_week;
      }
      if (selectOption.id == "monthly") {
        this.filterSubType = this.getAllType.months;
        this.filterData.sub_type = this.getAllType.default_month;
      }
      if (selectOption.id == "yearly") {
        this.filterSubType = this.getAllType.years;
        this.filterData.sub_type = this.getAllType.default_year;
      }
      let _this = this;
      this.getClinicRevenue();
      this.barChartClinicRevenue();
      this.doctorRevenue();
      this.clinicAppointmentCount();
      this.appointmentCount();
    },
    getClinic: function () {
      this.filterReport = this.filterReport.map((item) => {
        item.label = this.formTranslation.common[item.id];
        return item;
      });
      // Get dropdown data for clinics
      this.clinicMultiselectLoader = true;
      get("get_static_data", {
        data_type: "clinics",
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.clinics = response.data.data;
            if (this.clinics.length > 0) {
              this.clinics.push({
                id: "all",
                label: this.formTranslation.common.all,
              });
            }
          } else {
            displayErrorMessage(response.data.message);
          }
          this.clinicMultiselectLoader = false;
        })
        .catch((error) => {
          console.log(error);
          this.clinicMultiselectLoader = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getDoctorsData: function (clinic_id) {
      this.doctorApiData.clinic_id = clinic_id;
      this.doctorMultiselectLoader = true;
      get("get_static_data", this.doctorApiData)
        .then((data) => {
          this.doctorMultiselectLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            if (data.data.data.length > 0) {
              data.data.data.unshift({
                id: "all",
                label: this.formTranslation.common.all,
              });
            }
            this.doctors = data.data.data;
          }
        })
        .catch((error) => {
          this.doctorMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    // clinics() {
    //     return this.$store.state.clinic
    // },
    //     formTranslation: function () {
    //   return this.$store.state.staticDataModule.langTranslateData ;
    // },
  },
};
</script>
