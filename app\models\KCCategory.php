<?php

namespace App\models;

use App\baseClasses\KCModel;

class KCCategory extends KCModel {

    public function __construct() {
        parent::__construct('categories');
    }

    /**
     * Get categories by module type with visibility filtering
     */
    public function getCategoriesByModule($module_type, $visibility_filter = ['public'], $status_filter = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'kc_categories';
        $visibility_placeholders = implode(',', array_fill(0, count($visibility_filter), '%s'));

        $status_condition = '';
        $params = [$module_type];
        $params = array_merge($params, $visibility_filter);

        if ($status_filter !== null && $status_filter !== '') {
            $status_condition = 'AND status = %d';
            $params[] = intval($status_filter);
        }
        // If no status filter is provided, show all categories (both active and inactive)

        $query = $wpdb->prepare("
            SELECT * FROM {$table_name}
            WHERE module_type = %s
            AND visibility IN ({$visibility_placeholders})
            {$status_condition}
            ORDER BY sort_order ASC, name ASC
        ", $params);

        return $wpdb->get_results($query);
    }

    /**
     * Get categories for frontend (public only)
     */
    public function getPublicCategories($module_type, $status_filter = 1) {
        return $this->getCategoriesByModule($module_type, ['public'], $status_filter);
    }

    /**
     * Get categories for backend (public + backend_only)
     */
    public function getBackendCategories($module_type, $status_filter = null) {
        return $this->getCategoriesByModule($module_type, ['public', 'backend_only'], $status_filter);
    }

    /**
     * Generate unique slug
     */
    public function generateSlug($name, $module_type, $id = null) {
        $base_slug = sanitize_title($name);
        $slug = $base_slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $module_type, $id)) {
            $slug = $base_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slugExists($slug, $module_type, $exclude_id = null) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'kc_categories';
        $query = "SELECT id FROM {$table_name} WHERE slug = %s AND module_type = %s";
        $params = [$slug, $module_type];
        
        if ($exclude_id) {
            $query .= " AND id != %d";
            $params[] = $exclude_id;
        }
        
        return $wpdb->get_var($wpdb->prepare($query, $params)) !== null;
    }

    /**
     * Get category hierarchy
     */
    public function getCategoryHierarchy($module_type, $visibility_filter = ['public'], $status_filter = 1) {
        $categories = $this->getCategoriesByModule($module_type, $visibility_filter, $status_filter);
        return $this->buildHierarchy($categories);
    }

    /**
     * Build category hierarchy
     */
    private function buildHierarchy($categories, $parent_id = null) {
        $hierarchy = [];
        
        foreach ($categories as $category) {
            if ($category->parent_id == $parent_id) {
                $category->children = $this->buildHierarchy($categories, $category->id);
                $hierarchy[] = $category;
            }
        }
        
        return $hierarchy;
    }

    /**
     * Get category with service count
     */
    public function getCategoriesWithServiceCount($module_type, $clinic_id = null, $visibility_filter = ['public'], $status_filter = 1) {
        global $wpdb;

        $categories_table = $wpdb->prefix . 'kc_categories';
        $services_table = $wpdb->prefix . 'kc_services';

        $visibility_placeholders = implode(',', array_fill(0, count($visibility_filter), '%s'));
        $clinic_condition = $clinic_id ? "AND s.clinic_id = %d" : "";

        $status_condition = '';
        $params = array_merge([$module_type], $visibility_filter);

        if ($status_filter !== null && $status_filter !== '') {
            $status_condition = 'AND c.status = %d';
            $params[] = intval($status_filter);
        }

        if ($clinic_id) {
            $params[] = $clinic_id;
        }

        $query = $wpdb->prepare("
            SELECT
                c.*,
                COUNT(DISTINCT s.id) as service_count
            FROM {$categories_table} c
            LEFT JOIN {$services_table} s ON c.id = s.category_id
                AND s.status = 1
                AND s.doctor_id > 0
            WHERE c.module_type = %s
            AND c.visibility IN ({$visibility_placeholders})
            {$status_condition}
            {$clinic_condition}
            GROUP BY c.id
            ORDER BY c.sort_order ASC, c.name ASC
        ", $params);

        return $wpdb->get_results($query);
    }
}