// Place this at the very top, before kcAppointmentBookJsContent function
(function () {
  // Wait for DOM to be ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializePaymentCheck);
  } else {
    initializePaymentCheck();
  }

  function initializePaymentCheck() {
    const urlParams = new URLSearchParams(window.location.search);
    const stripePayment = urlParams.get("kivicare_stripe_payment");
    const appointmentId = urlParams.get("appointment_id");

    // Store URL parameters for direct navigation to specific tabs
    window.kcServiceLinkParams = {
      service_id: urlParams.get("service_id"),
      doctor_id: urlParams.get("doctor_id"),
      clinic_id: urlParams.get("clinic_id"),
      step: urlParams.get("step")
    };

    // Only proceed if we have a success payment and appointment ID
    if (stripePayment === "success" && appointmentId) {
      // Prevent any default failure screens from showing
      const possibleErrorElements = document.querySelectorAll(
        ".payment-error, #payment_error, .error-screen"
      );
      possibleErrorElements.forEach((el) => {
        if (el) el.style.display = "none";
      });

      const storedAppointmentId = sessionStorage.getItem(
        "kivicare_appointment_id"
      );

      if (storedAppointmentId === appointmentId) {
        // Create and show loading overlay immediately
        const loadingElement = document.createElement("div");
        loadingElement.id = "kivicare-payment-loader";
        loadingElement.innerHTML = `
          <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                      background: rgba(255, 255, 255, 0.98); z-index: 99999;
                      display: flex; justify-content: center; align-items: center">
            <div style="text-align: center">
              <svg xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.0" width="64px" height="72px" viewBox="0 0 128 143" xml:space="preserve"><rect x="0" y="0" width="100%" height="100%" fill="#FFFFFF" /><path fill="#000000" d="M119.8 31.3l-.6-.6a2.8 2.8 0 0 1-.4.5L116 34a2.8 2.8 0 0 1-4 0v-.2l-1.5 1.5a64 64 0 1 1-48.4-20v-2.6h-.6A3.7 3.7 0 0 1 57.6 9V3.7A3.7 3.7 0 0 1 61.4 0h5.2a3.7 3.7 0 0 1 3.8 3.7V9a3.7 3.7 0 0 1-3.8 3.7H66v2.6a63.8 63.8 0 0 1 42 17.4l1.4-1.5a2.8 2.8 0 0 1 0-4l2.7-2.7a2.8 2.8 0 0 1 .5-.4l-.6-.5a2 2 0 1 1 2.7-2.8l8 8a2 2 0 0 1-3 2.6zM64 19.3a59.7 59.7 0 1 0 60 60 59.8 59.8 0 0 0-60-60zM109.7 80v-1.6h7V80h-7zm-6.5-24.3l6-3.5 1 1.3-6.3 3.5zm-17 63.3l1.3-.8 3.5 6.2-1.3.7zm0-79.7l3.5-6 1.3.6-3.5 6zM64 86a6.7 6.7 0 1 1 6.7-6.8A6.7 6.7 0 0 1 64 86zm0-11.7a5 5 0 1 0 5 5 5 5 0 0 0-5-5zm-.7-47.8h1.5v7h-1.5v-7zm-26.3 98l3.5-6.3 1.3.8-3.5 6zm0-90.6l1.3-.8 3.5 6-1.3 1zm-19 70.8l6-3.5.8 1.3-6 3.5zm0-51.3l.7-1.3 6 3.5-.6 1.3zm.3 26.4h-7v-1.6h7V80zm46.4 51.8h-1.5v-7h1.5v7zm45.4-27l-.7 1.3-6-3.4.6-1.3z"/><path fill="#000000" d="M64 73.6l-3-.4.8-34 2.2-3.7 2.2 3.7.8 34z"><animateTransform attributeName="transform" type="rotate" from="0 64 79" to="360 64 79" dur="720ms" repeatCount="indefinite"></animateTransform></path></svg>
              <p style="font-weight: 500;">Processing payment...</p>
            </div>
          </div>
        `;
        document.body.appendChild(loadingElement);

        const post = (
          route,
          data = {},
          frontEnd = false,
          headers = {
            headers: { "Content-Type": "application/json" },
          }
        ) => {
          validateBookAppointmentWidgetData(bookAppointmentWidgetData);

          window.ajaxurl = bookAppointmentWidgetData.ajax_url;
          window.nonce = bookAppointmentWidgetData.ajax_post_nonce;

          let url = ajaxurl;
          if (data.action === undefined) {
            url = ajaxurl + "?action=ajax_post";
          }

          if (route === undefined) {
            return false;
          }

          if (data.append !== undefined) {
            data.append("route_name", route);
            data.append("_ajax_nonce", nonce);
          } else {
            data.route_name = route;
            data._ajax_nonce = nonce;
          }

          return new Promise((resolve, reject, headers) => {
            axios
              .post(url, data, headers)
              .then((data) => {
                if (
                  data.data.status_code !== undefined &&
                  data.data.status_code === 403
                ) {
                  kivicareShowToastMessage(
                    "error",
                    bookAppointmentWidgetData.message.route_not_found
                  );
                }
                resolve(data);
              })
              .catch((error) => {
                reject(error);
                kivicareShowToastMessage(
                  "error",
                  bookAppointmentWidgetData.message.internal_server_msg
                );
              });
          });
        };

        // Function to disable all interactive elements
        function disableInteraction() {
          const elements = document.querySelectorAll(
            "button, [data-step], a, input, select, .iq-tab-pannel"
          );
          elements.forEach((el) => {
            if (el.tagName === "A") {
              el.style.pointerEvents = "none";
            } else {
              el.disabled = true;
              el.style.pointerEvents = "none";
            }
          });
        }

        // Disable interaction immediately
        disableInteraction();

        // Also disable any new elements that might be added
        const observer = new MutationObserver(disableInteraction);
        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });

        // Handle the payment status update
        post("save_appointment_payment_status", {
          appointment_id: appointmentId,
          payment_status: "approved",
        })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              kivicareLoadConfirmPage(appointmentId);
              sessionStorage.removeItem("kivicare_appointment_id");
            }
          })
          .catch((error) => {
            console.error("Payment status update failed:", error);
          })
          .finally(() => {
            // Stop observing for new elements
            observer.disconnect();

            // Remove the loading overlay
            const loader = document.getElementById("kivicare-payment-loader");
            if (loader) {
              loader.remove();
            }

            // Re-enable interaction
            const elements = document.querySelectorAll(
              "button, [data-step], a, input, select, .iq-tab-pannel"
            );
            elements.forEach((el) => {
              if (el.tagName === "A") {
                el.style.pointerEvents = "auto";
              } else {
                el.disabled = false;
                el.style.pointerEvents = "auto";
              }
            });
          });
      }
    }
  }
})();

function kcAppointmentBookJsContent() {
  let restrictionData = null;
  let days = null;
  let holidays = null;
  let currentDisplayMonth = new Date().getMonth();
  let currentDisplayYear = new Date().getFullYear();

  (function ($) {
    window.name = "kivicareWidget";
    $("#kivicare-widget-main-content").removeClass("d-none");
    $("#kivicare-main-page-loader").addClass("d-none");
    if (bookAppointmentWidgetData.popup_appointment_book) {
      $(".kivi-widget-close").on("click", function () {
        $.magnificPopup.close();
      });
    }

    if (window.matchMedia("(max-width: 768px)").matches) {
      $(".kivi-widget").css("padding", "8px");
    } else {
      $(".kivi-widget").css("padding", "16px");
    }
    const post = (
      route,
      data = {},
      frontEnd = false,
      headers = {
        headers: { "Content-Type": "application/json" },
      }
    ) => {
      validateBookAppointmentWidgetData(bookAppointmentWidgetData);

      window.ajaxurl = bookAppointmentWidgetData.ajax_url;
      window.nonce = bookAppointmentWidgetData.ajax_post_nonce;

      let url = ajaxurl;
      if (data.action === undefined) {
        url = ajaxurl + "?action=ajax_post";
      }

      if (route === undefined) {
        return false;
      }

      if (data.append !== undefined) {
        data.append("route_name", route);
        data.append("_ajax_nonce", nonce);
      } else {
        data.route_name = route;
        data._ajax_nonce = nonce;
      }

      return new Promise((resolve, reject, headers) => {
        axios
          .post(url, data, headers)
          .then((data) => {
            if (
              data.data.status_code !== undefined &&
              data.data.status_code === 403
            ) {
              kivicareShowToastMessage(
                "error",
                bookAppointmentWidgetData.message.route_not_found
              );
            }
            resolve(data);
          })
          .catch((error) => {
            reject(error);
            kivicareShowToastMessage(
              "error",
              bookAppointmentWidgetData.message.internal_server_msg
            );
          });
      });
    };

    const get = (route, data, frontEnd = false) => {
      validateBookAppointmentWidgetData(bookAppointmentWidgetData);

      window.ajaxurl = bookAppointmentWidgetData.ajax_url;
      window.nonce = bookAppointmentWidgetData.ajax_get_nonce;

      data._ajax_nonce = bookAppointmentWidgetData.ajax_get_nonce;
      let url = ajaxurl;
      if (data.action === undefined) {
        url = ajaxurl + "?action=ajax_get";
      }

      if (route === undefined) {
        return false;
      }

      url = url + "&route_name=" + route;
      return new Promise((resolve, reject) => {
        axios
          .get(url, { params: data })
          .then((data) => {
            if (
              data.data.status_code !== undefined &&
              data.data.status_code === 403
            ) {
              kivicareShowToastMessage(
                "error",
                bookAppointmentWidgetData.message.route_not_found
              );
            }
            resolve(data);
          })
          .catch((error) => {
            reject(error);
            kivicareShowToastMessage(
              "error",
              bookAppointmentWidgetData.message.internal_server_msg
            );
          });
      });
    };

    getCountryCodeData();
    jQuery("#CountryCode").select2({
      dropdownParent: jQuery(".contact-box-inline"),
      templateSelection: function (data, container) {
        var countrycodedata = JSON.parse(data.id);
        return countrycodedata.countryCallingCode;
      },
    });

    kivicareLoadConfirmPage(bookAppointmentWidgetData.print_confirm_page);

    kcInitMultiselectElement("customFieldsList");

    var timer = "";

    var appointmentUploadFiles = [];

    var appointment_custom_fields = {};

    var appointmentDate = "";

    var child = "";

    var payment_status = "";

    var appointment_id = "";

    var payment_select_mode = "";

    var userLogin = bookAppointmentWidgetData.user_login;

    var service_clinic = [];

    var tax_details = [];

    // Function to handle direct navigation to date-time step for service links
    function handleServiceLinkNavigation() {
      if (window.kcServiceLinkParams && window.kcServiceLinkParams.step === "datetime") {
        // Ensure we have all required parameters
        if (window.kcServiceLinkParams.service_id && window.kcServiceLinkParams.doctor_id && window.kcServiceLinkParams.clinic_id) {
          // Store these in bookAppointmentWidgetData for later use by the appointment form
          bookAppointmentWidgetData.preselected_service = window.kcServiceLinkParams.service_id;
          bookAppointmentWidgetData.preselected_doctor = window.kcServiceLinkParams.doctor_id;
          bookAppointmentWidgetData.preselected_clinic_id = window.kcServiceLinkParams.clinic_id;

          // Process through all the tabs in sequence to properly set up the UI
          // First handle clinic selection
          setTimeout(function() {
            kivicareGetClinicsLists();
            setTimeout(function() {
              // Select the clinic
              $('input[name="selected-clinic"][value="' + window.kcServiceLinkParams.clinic_id + '"]').prop('checked', true);

              // Now handle doctor selection
              setTimeout(function() {
                kivicareGetDoctorLists();
                setTimeout(function() {
                  // Select the doctor
                  $('input[name="kivicare-doctor-widget"][value="' + window.kcServiceLinkParams.doctor_id + '"]').prop('checked', true);

                  // Now handle service selection
                  setTimeout(function() {
                    kivicareGetServiceCategoryLists();
                    setTimeout(function() {
                      // Select service category when available
                      kivicareGetServiceLists();
                      setTimeout(function() {
                        // Select the service
                        $('#service_' + window.kcServiceLinkParams.service_id).prop('checked', true);

                        // Now navigate to date-time tab
                        setTimeout(function() {
                          // Find date-time tab and show it
                          $('#date-time').removeClass('hidden').addClass('active');
                          $('.iq-tab-pannel').not('#date-time').removeClass('active').addClass('hidden');

                          // Update tab navigation
                          $('a[href="#date-time"]').closest('.tab-item').addClass('active');
                          $('a[href="#date-time"]').closest('.tab-item').siblings().removeClass('active');

                          // Load the date-time content
                          kivicareGetDoctorWeekday(window.kcServiceLinkParams.doctor_id);
                        }, 200);
                      }, 200);
                    }, 200);
                  }, 200);
                }, 200);
              }, 200);
            }, 300);
          }, 300);
        }
      }
    }

    if (bookAppointmentWidgetData.print_confirm_page === "off") {
      switch ($(".iq-fade.iq-tab-pannel.active").attr("id")) {
        case "clinic":
          kivicareGetClinicsLists();
          // After loading clinics, check if we need to navigate to a specific step
          setTimeout(handleServiceLinkNavigation, 500);
          break;
        case "category":
          kivicareGetServiceCategoryLists();
          break;
        case "doctor":
          kivicareGetDoctorLists();
          break;
        case "date-time":
          kivicareGetDoctorWeekday(kivicareGetSelectedItem("selected-doctor"));
          break;
      }
    } else {
      // Handle URL parameters for when we're not on the initial page
      setTimeout(handleServiceLinkNavigation, 500);
    }

    //logout button click event
    $(document).on("click", "#kivicare_logout_btn", function () {
      let logoutElement = $("#kivicare_logout_btn");
      logoutElement.prop("disabled", true);
      logoutElement.html(bookAppointmentWidgetData.message.loading);
      post("logout", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            kivicareShowToastMessage("success", response.data.message);
            //wait 1 sec before reload
            setTimeout(function () {
              location.reload();
            }, 2000);
          } else {
            logoutElement.prop("disabled", false);
            logoutElement.html(bookAppointmentWidgetData.message.logout);
          }
        })
        .catch((error) => {
          logoutElement.prop("disabled", false);
          logoutElement.html(bookAppointmentWidgetData.message.logout);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    });

    $(document).on("click", '[data-toggle="active"]', function () {
      $(this).toggleClass("active block");
    });

    //clinic search event
    $("#clinicSearch").keyup(function () {
      kivicareGetClinicsLists(this.value);
    });

    //doctor search event
    $("#doctorSearch").keyup(function () {
      kivicareGetDoctorLists(this.value);
    });

    //service search event
    $("#serviceSearch").keyup(function () {
      kivicareGetServiceLists(this.value);
    });
    $("#serviceCategorySearch").keyup(function () {
      kivicareGetServiceCategoryLists(this.value);
    });

    //check if google recaptcha enable
    if (bookAppointmentWidgetData.google_recaptcha_enable) {
      //add recaptcha in login/register tab
      grecaptcha.ready(function () {
        let tabs = ["detail-info"];
        for (let i = 0; i < tabs.length; i++) {
          kcCreateGoogleRecaptcha(tabs[i]);
        }
      });
    }

    function kcInitMultiselectElement(id) {
      $("#" + id)
        .find(".appointment_widget_multiselect")
        .each(function () {
          $(this).select2({
            placeholder: $(this).attr("placeholder"),
            allowClear: true,
            dropdownCssClass: "kivicare-custom-dropdown-width",
          });
        });
    }

    /**
     * It's a function that creates a Google Recaptcha token for the current tab
     * @param currentTab - The ID of the tab you want to create the reCAPTCHA for.
     */
    function kcCreateGoogleRecaptcha(currentTab) {
      if (bookAppointmentWidgetData.google_recaptcha_enable) {
        grecaptcha
          .execute(bookAppointmentWidgetData.google_recatcha_site_key, {
            action: "validate_captcha",
          })
          .then(function (token) {
            var tab = document.getElementById(currentTab);
            $(tab).find("#g-recaptcha-response").val(token);
          });
      }
    }

    //next button click event
    $(document).off("click", '[data-step="next"]');
    $(document).on("click", '[data-step="next"]', function (e) {
      var perviousTab = $(".iq-tab-pannel.active")
        .find("form")
        .attr("data-prev");
      var target = $(".iq-tab-pannel.active").find("form").attr("action");
      removeTabActiveLink($(`[href="${target}"]`).closest(".tab-item"));
      var currentTab = $(".iq-tab-pannel.active")
        .find("form")
        .closest(".iq-tab-pannel")
        .attr("id");

      console.log(currentTab);

      switch (currentTab) {
        case "clinic":
          e.preventDefault();
          var selected_clinic = kivicareGetSelectedItem("selected-clinic");
          if (selected_clinic == 0) {
            kivicareShowToastMessage(
              "error",
              bookAppointmentWidgetData.message.select_clinic
            );
            return;
          } else {
            if (target == "#doctor") {
              kivicareGetDoctorLists();
            } else if (target == "#category") {
              service_clinic = [];
              kivicareGetServiceCategoryLists();
            }
          }
          $("#categoryTab div")
            .removeClass("text-gray-400")
            .addClass("text-indigo-600");
          $("#categoryTab div")
            .removeClass("bg-gray-50")
            .addClass("bg-indigo-100");
          $("#categoryTab span")
            .removeClass("text-gray-400")
            .addClass("text-indigo-600");

          // update next button color
          $('[data-step="next"]')
            .removeClass("bg-blue-600 hover:bg-blue-700")
            .addClass("bg-indigo-600 hover:bg-indigo-700");

          break;
        case "category":
          e.preventDefault();
          var selected_clinic = kivicareGetSelectedItem("selected-clinic");
          if (selected_clinic == 0) {
            kivicareShowToastMessage(
              "error",
              bookAppointmentWidgetData.message.select_clinic
            );
            return;
          } else {
            if (target == "#doctor") {
              kivicareGetDoctorLists();
            } else if (target == "#services") {
              service_clinic = [];
              kivicareGetServiceLists();
            }
          }
          $("#serviceTab div")
            .removeClass("text-gray-400")
            .addClass("text-indigo-600");
          $("#serviceTab div")
            .removeClass("bg-gray-50")
            .addClass("bg-indigo-100");
          $("#serviceTab span")
            .removeClass("text-gray-400")
            .addClass("text-indigo-600");

          // update next button color
          $('[data-step="next"]')
            .removeClass("bg-blue-600 hover:bg-blue-700")
            .addClass("bg-indigo-600 hover:bg-indigo-700");

          break;
        case "services":
          e.preventDefault();
          service_clinic = [];
          var service_data = kivicareGetSelectedServie("single", "doctor_id");
          var selected = service_data.length;
          if (selected == 0) {
            kivicareShowToastMessage(
              "error",
              bookAppointmentWidgetData.message.select_service
            );
            return;
          } else {
            if (target == "#doctor") {
              kivicareGetDoctorLists();
            } else if (target == "#clinic") {
              kivicareGetClinicsLists();
            }
          }
          $("#dateTimeTab div")
            .removeClass("text-gray-400")
            .addClass("text-violet-600");
          $("#dateTimeTab div")
            .removeClass("bg-gray-50")
            .addClass("bg-violet-50");
          $("#dateTimeTab span")
            .removeClass("text-gray-400")
            .addClass("text-violet-600");
          // update next button color
          $('[data-step="next"]')
            .removeClass("bg-purple-600 hover:bg-purple-700")
            .addClass("bg-violet-600 hover:bg-violet-700");

          break;
        case "date-time":
          e.preventDefault();
          var select_time = kivicareGetSelectedItem("selected-time");
          console.log(select_time);

          if (select_time == 0) {
            kivicareShowToastMessage(
              "error",
              bookAppointmentWidgetData.message.select_date_and_time
            );
            return;
          }
          if (userLogin) {
            if (bookAppointmentWidgetData.extra_tab_show) {
              target = "#file-uploads-custom";
            } else {
              target = "#confirm";
            }
          }
          $("#detailsTab div")
            .removeClass("text-gray-400")
            .addClass("text-fuchsia-600");
          $("#detailsTab div")
            .removeClass("bg-gray-50")
            .addClass("bg-fuchsia-100");
          $("#detailsTab span")
            .removeClass("text-gray-400")
            .addClass("text-fuchsia-600");
          // update next button color
          $('[data-step="next"]')
            .removeClass("bg-violet-600 hover:bg-violet-700")
            .addClass("bg-fuchsia-600 hover:bg-fuchsia-700");

          break;
        case "file-uploads-custom":
          $("#customFieldsListAppointment .kivicare-required").prop(
            "required",
            true
          );
          $.each(
            $("#customFieldsListAppointment")
              .find(":input:checkbox")
              .parent()
              .parent(),
            function (key, value) {
              let cbx_group = $(value).find(":input:checkbox");
              if (cbx_group.is(":checked")) {
                cbx_group.prop("required", false);
              }
            }
          );

          if (!$("#kivicare-file-upload-form")[0].checkValidity()) {
            return;
          }
          appointment_custom_fields = kivicareCustomFieldsData(
            "customFieldsListAppointment"
          );
          if (userLogin) {
            target = "#confirm";
          }
          window.requestAnimationFrame(function () {
            var element = document.getElementById("CountryCode").parentElement;
            var elementwidth = element.offsetWidth;
            element.style.setProperty(
              "--kc-country-code-width",
              elementwidth + "px"
            );
          });
          e.preventDefault();
          $("#loginTab div")
            .removeClass("text-gray-400")
            .addClass("text-rose-600");
          $("#loginTab div").removeClass("bg-gray-50").addClass("bg-rose-100");
          $("#loginTab span")
            .removeClass("text-gray-400")
            .addClass("text-rose-600");
          // update next button color
          $('[data-step="next"]')
            .removeClass("bg-fuchsia-600 hover:bg-fuchsia-700")
            .addClass("bg-rose-600 hover:bg-rose-700");

          break;
        case "detail-info":
          var formName = document.getElementsByClassName("authActive active");
          for (var i = 0; i < formName.length; i++) {
            if (formName[i].id == "kc_login") {
              $("#kivicare-login-form input").prop("required", true);
              $(
                "#kivicare-register-form input,textarea, select,#kivicare-register-form #nhs"
              ).prop("required", false);

              if (!$("#kiviLoginRegister")[0].checkValidity()) {
                kivicareShowToastMessage(
                  "error",
                  "Please fill in all required fields"
                );
                return;
              }

              var result = {};
              $.each($("#kivicare-login :input").serializeArray(), function () {
                result[this.name] = this.value;
              });
              e.preventDefault();
              kivicareButtonDisableChangeText(
                "#kiviLoginRegister",
                true,
                bookAppointmentWidgetData.message.loading
              );
              post("appointmentLogin", result, true)
                .then((response) => {
                  kivicareButtonDisableChangeText(
                    "#kiviLoginRegister",
                    false,
                    bookAppointmentWidgetData.message.login
                  );
                  if (response.data.status) {
                    validateBookAppointmentWidgetData(
                      bookAppointmentWidgetData
                    );
                    bookAppointmentWidgetData.ajax_get_nonce =
                      response.data.token.get;
                    bookAppointmentWidgetData.ajax_post_nonce =
                      response.data.token.post;
                    userLogin = true;
                    $("#kivicare_logout_btn").removeClass("hidden");
                    kivicareShowToastMessage("success", response.data.message);
                    showConfirmPage(target, currentTab);
                    $(`[href="#${currentTab}"]`)
                      .closest(".tab-item")
                      .attr("data-check", true);
                    $(`[href="${target}"]`)
                      .closest(".tab-item")
                      .addClass("active block");
                    tabShow(target);
                  } else {
                    kivicareShowToastMessage("error", response.data.message);
                  }
                })
                .catch((error) => {
                  kivicareButtonDisableChangeText(
                    "#kiviLoginRegister",
                    false,
                    bookAppointmentWidgetData.message.login
                  );
                  console.log(error);
                  kivicareShowToastMessage(
                    "error",
                    bookAppointmentWidgetData.message.internal_server_msg
                  );
                });
            }
            if (formName[i].id == "kc_register") {
              $("#kivicare-login-form input").prop("required", false);
              $("#kivicare-register input").prop("required", true);
              $("#customFieldsList .kivicare-required").prop("required", true);
              $("#kivicare-register-form #nhs").prop("required", false);

              $.each(
                $("#customFieldsList")
                  .find(":input:checkbox")
                  .parent()
                  .parent(),
                function (key, value) {
                  let cbx_group = $(value).find(":input:checkbox");
                  if (cbx_group.is(":checked")) {
                    cbx_group.prop("required", false);
                  }
                }
              );

              if (!$("#kiviLoginRegister")[0].checkValidity()) {
                kivicareShowToastMessage(
                  "error",
                  "Please fill in all required fields"
                );
                return;
              }

              var result = {};
              $.each(
                $("#kivicare-register :input").serializeArray(),
                function () {
                  result[this.name] = this.value;
                }
              );

              var custom_fields = kivicareCustomFieldsData("customFieldsList");
              kivicareButtonDisableChangeText(
                "#kiviLoginRegister",
                true,
                bookAppointmentWidgetData.message.loading
              );
              e.preventDefault();
              result["clinic"] = [
                {
                  id: kivicareGetSelectedItem("selected-clinic"),
                },
              ];
              const registerData = { ...result, ...{ custom_fields } };
              let formData = new FormData(
                document.getElementById("kiviLoginRegister")
              );
              $.each(registerData, function (key, value) {
                if (typeof value === "object") {
                  value = JSON.stringify(value);
                }
                formData.append(key, value);
              });
              post("register", formData, true)
                .then((response) => {
                  kivicareButtonDisableChangeText(
                    "#kiviLoginRegister",
                    false,
                    bookAppointmentWidgetData.message.register
                  );
                  if (response.data.status) {
                    validateBookAppointmentWidgetData(
                      bookAppointmentWidgetData
                    );
                    bookAppointmentWidgetData.ajax_get_nonce =
                      response.data.token.get;
                    bookAppointmentWidgetData.ajax_post_nonce =
                      response.data.token.post;
                    userLogin = true;
                    $("#kivicare_logout_btn").removeClass("hidden");
                    kivicareShowToastMessage("success", response.data.message);
                    showConfirmPage(target, currentTab);
                    $(`[href="#${currentTab}"]`)
                      .closest(".tab-item")
                      .attr("data-check", true);
                    $(`[href="${target}"]`)
                      .closest(".tab-item")
                      .addClass("active block");
                    tabShow(target);
                  } else {
                    kivicareShowToastMessage("error", response.data.message);
                    kcCreateGoogleRecaptcha(currentTab);
                  }
                })
                .catch((error) => {
                  kivicareButtonDisableChangeText(
                    "#kiviLoginRegister",
                    false,
                    bookAppointmentWidgetData.message.register
                  );
                  console.log(error);
                  kcCreateGoogleRecaptcha(currentTab);
                  kivicareShowToastMessage(
                    "error",
                    bookAppointmentWidgetData.message.internal_server_msg
                  );
                });
            }
          }
          $("#confirmTab div")
            .removeClass("text-gray-400")
            .addClass("text-pink-600");
          $("#confirmTab div")
            .removeClass("bg-gray-50")
            .addClass("bg-pink-100");
          $("#confirmTab span")
            .removeClass("text-gray-400")
            .addClass("text-pink-600");
          // update next button color
          $('[data-step="next"]')
            .removeClass("bg-rose-600 hover:bg-rose-700")
            .addClass("bg-pink-600 hover:bg-pink-700");

          return;
          break;
        case "confirm":
          e.preventDefault();
          if (target !== "#payment_mode") {
            var result = [];
            $.each(
              $("#confirm_detail_form :input").serializeArray(),
              function () {
                result[this.name] = this.value;
              }
            );
            kivicareBookAppointment(
              this,
              bookAppointmentWidgetData.first_payment_method,
              result
            );
            return;
          } else {
            showPaymentPage();
          }
          $("#confirmTab div")
            .removeClass("text-gray-400")
            .addClass("text-pink-600");
          $("#confirmTab div")
            .removeClass("bg-gray-50")
            .addClass("bg-pink-100");
          $("#confirmTab span")
            .removeClass("text-gray-400")
            .addClass("text-pink-600");
          break;
        case "payment_mode":
          e.preventDefault();
          if (
            $('#payment_mode input:radio[name="payment_option"]:checked')
              .length == 0
          ) {
            kivicareShowToastMessage(
              "success",
              bookAppointmentWidgetData.message.select_payment_mode
            );
          } else {
            var result = [];
            $.each(
              $("#payment_mode_form :input").serializeArray(),
              function () {
                result[this.name] = this.value;
              }
            );
            kivicareBookAppointment(
              this,
              $(
                '#payment_mode input:radio[name="payment_option"]:checked'
              ).attr("id"),
              result
            );
          }
          return;
          break;
      }

      switch (target) {
        case "#confirm":
          showConfirmPage(target, currentTab);
          break;
        case "#file-uploads-custom":
          if (bookAppointmentWidgetData.pro_plugin_active) {
            document.getElementById("customFieldsListAppointment").innerHTML =
              " ";
            get(
              "get_appointment_custom_field",
              {
                doctor_id: kivicareGetSelectedItem("selected-doctor"),
                service_id: kivicareGetSelectedServie("single", "service_id"),
              },
              true
            )
              .then((res) => {
                if (res.data.status !== undefined && res.data.status) {
                  document.getElementById(
                    "customFieldsListAppointment"
                  ).innerHTML = validateDOMData(res.data.data);
                  kcInitMultiselectElement("customFieldsListAppointment");

                  $(".select2-multiple").each(function () {
                    if (!$(this).hasClass("select2-initialized")) {
                      initializeSelect2(this);
                      $(this).addClass("select2-initialized");
                    }
                  });
                }
              })
              .catch((error) => {
                console.log(error);
              });
          }
          break;
        case "#date-time":
          kivicareGetDoctorWeekday(kivicareGetSelectedItem("selected-doctor"));
          break;
        case "#confirmed":
          jQuery('[href="#confirm"]')
            .closest(".tab-item")
            .removeClass("active block");
          break;
      }

      $(`[href="#${currentTab}"]`)
        .closest(".tab-item")
        .attr("data-check", true)
        .removeClass("hidden");
      $(`[href="${target}"]`).closest(".tab-item").addClass("block");
      if (target != "#confirm") {
        tabShow(target);
      }
    });

    //previous button click event
    $(document).off("click", '[data-step="prev"]');
    $(document).on("click", '[data-step="prev"]', function (e) {
      e.preventDefault();
      let target = $(".iq-tab-pannel.active").find("form").attr("data-prev");
      const currentTab = $(".iq-tab-pannel.active")
        .find("form")
        .closest(".iq-tab-pannel")
        .attr("id");
      $(`[href="#${currentTab}"]`)
        .closest(".tab-item")
        .attr("data-check", false);
      $(`[href="#${currentTab}"]`)
        .closest(".tab-item")
        .removeClass("active block");

      switch (currentTab) {
        case "confirm":
          if (bookAppointmentWidgetData.extra_tab_show) {
            target = "#file-uploads-custom";
          } else {
            target = "#date-time";
          }
          $("#confirmTab div")
            .removeClass("text-pink-600")
            .addClass("text-gray-400");
          $("#confirmTab div")
            .removeClass("bg-pink-100")
            .addClass("bg-gray-50");
          $("#confirmTab span")
            .removeClass("text-pink-600")
            .addClass("text-gray-400");
          $('[data-step="next"]')
            .removeClass("bg-pink-600 hover:bg-pink-700")
            .addClass("bg-rose-600 hover:bg-rose-700");
          break;
        case "detail-info":
          if (bookAppointmentWidgetData.extra_tab_show) {
            target = "#file-uploads-custom";
          } else {
            target = "#date-time";
          }
          $("#loginTab div")
            .removeClass("text-rose-600")
            .addClass("text-gray-400");
          $("#loginTab div").removeClass("bg-rose-100").addClass("bg-gray-50");
          $("#loginTab span")
            .removeClass("text-rose-600")
            .addClass("text-gray-400");
          $('[data-step="next"]')
            .removeClass("bg-rose-600 hover:bg-rose-700")
            .addClass("bg-fuchsia-600 hover:bg-fuchsia-700");
          break;
        case "file-uploads-custom":
          target = "#date-time";
          $("#detailsTab div")
            .removeClass("text-fuchsia-600")
            .addClass("text-gray-400");
          $("#detailsTab div")
            .removeClass("bg-fuchsia-100")
            .addClass("bg-gray-50");
          $("#detailsTab span")
            .removeClass("text-fuchsia-600")
            .addClass("text-gray-400");
          $('[data-step="next"]')
            .removeClass("bg-fuchsia-600 hover:bg-fuchsia-700")
            .addClass("bg-violet-600 hover:bg-violet-700");
          break;
        case "date-time":
          target = "#services";
          $("#dateTimeTab div")
            .removeClass("text-violet-600")
            .addClass("text-gray-400");
          $("#dateTimeTab div")
            .removeClass("bg-violet-50")
            .addClass("bg-gray-50");
          $("#dateTimeTab span")
            .removeClass("text-violet-600")
            .addClass("text-gray-400");
          $('[data-step="next"]')
            .removeClass("bg-violet-600 hover:bg-violet-700")
            .addClass("bg-purple-600 hover:bg-purple-700");
          let timeslot = document.getElementById("timeSlotLists");
          timeslot.classList.remove("d-grid");
          timeslot.style.height = "100%";
          timeslot.parentNode.style.height = "400px";
          timeslot.innerHTML =
            `<p class="loader-class">` +
            bookAppointmentWidgetData.message.please_select_date +
            `</p>`;
          break;
        case "doctor":
          target = "#category";
          $(".iq-tab-pannel.active")
            .find("form")
            .find('input[type="radio"]')
            .prop("checked", false);
          $("#doctorTab div")
            .removeClass("text-purple-600")
            .addClass("text-gray-400");
          $("#doctorTab div")
            .removeClass("bg-purple-100")
            .addClass("bg-gray-50");
          $("#doctorTab span")
            .removeClass("text-purple-600")
            .addClass("text-gray-400");
          $('[data-step="next"]')
            .removeClass("bg-purple-600 hover:bg-purple-700")
            .addClass("bg-indigo-600 hover:bg-indigo-700");
          break;
        case "category":
          target = "#clinic";
          $(".iq-tab-pannel.active")
            .find("form")
            .find('input[type="checkbox"]')
            .prop("checked", false);
          $("#serviceTab div")
            .removeClass("text-indigo-600")
            .addClass("text-gray-400");
          $("#serviceTab div")
            .removeClass("bg-indigo-100")
            .addClass("bg-gray-50");
          $("#serviceTab span")
            .removeClass("text-indigo-600")
            .addClass("text-gray-400");
          $('[data-step="next"]')
            .removeClass("bg-indigo-600 hover:bg-indigo-700")
            .addClass("bg-blue-600 hover:bg-blue-700");
          break;
        case "clinic":
          $(".iq-tab-pannel.active")
            .find("form")
            .find('input[type="radio"]')
            .prop("checked", false);
          break;
      }

      $(`[href="${target}"]`).closest(".tab-item").addClass("active block");
      $(`[href="${target}"]`).closest(".tab-item").attr("data-check", false);

      if (target == "#clinic") {
        kivicareGetClinicsLists();
      }
      tabShow(target);
    });

    //previous button click event - working code without color change in below button and top navigation
    // $(document).off('click', '[data-step="prev"]');
    // $(document).on('click', '[data-step="prev"]', function (e) {
    //     e.preventDefault();
    //     let target = $('.iq-tab-pannel.active').find('form').attr('data-prev');
    //     const currentTab = $('.iq-tab-pannel.active').find('form').closest('.iq-tab-pannel').attr('id')
    //     $(`[href="#${currentTab}"]`).closest('.tab-item').attr('data-check', false)
    //     $(`[href="#${currentTab}"]`).closest('.tab-item').removeClass('active block')
    //     if (currentTab == 'confirm') {
    //         if (bookAppointmentWidgetData.extra_tab_show) {
    //             target = '#file-uploads-custom';
    //         } else {
    //             target = '#date-time';
    //         }
    //     } else if (currentTab == 'detail-info') {
    //         if (bookAppointmentWidgetData.extra_tab_show) {
    //             target = '#file-uploads-custom';
    //         } else {
    //             target = '#date-time';
    //         }
    //     } else if (currentTab == 'clinic' || currentTab == 'doctor') {
    //         $('.iq-tab-pannel.active').find('form').find('input[type="radio"]').prop('checked', false);
    //     } else if (currentTab == 'category') {
    //         $('.iq-tab-pannel.active').find('form').find('input[type="checkbox"]').prop('checked', false);
    //         tabShow(target);
    //     } else if (currentTab == 'date-time') {
    //         let timeslot = document.getElementById("timeSlotLists")
    //         timeslot.classList.remove('d-grid')
    //         timeslot.style.height = '100%';
    //         timeslot.parentNode.style.height = '400px';
    //         timeslot.innerHTML = `<p class="loader-class">` + bookAppointmentWidgetData.message.please_select_date + `</p>`
    //     }
    //     $(`[href="${target}"]`).closest('.tab-item').addClass('active block')
    //     $(`[href="${target}"]`).closest('.tab-item').attr('data-check', false)
    //     if("#clinic"){
    //         kivicareGetClinicsLists()
    //     }
    //     tabShow(target);

    // })

    // for doctor tab
    $(document).on("click", ".widget-doctor-item", function () {
      $(this).find(".card-checkbox").prop("checked", true);
    });

    $(document).off("change", ".selected-service");
    $(document).on("change", ".selected-service", function (e) {
      const selected_service_clinic = $(this).attr("clinic_id");
      const service_id = $(this).attr("value");
      if (this.checked) {
        if (service_clinic.length > 0) {
          if (
            service_clinic.findIndex(
              (a) =>
                a.service_id === service_id &&
                a.clinic_id === selected_service_clinic
            ) === -1
          ) {
            if (
              service_clinic.findIndex(
                (a) => a.clinic_id === selected_service_clinic
              ) === -1
            ) {
              $(this).prop("checked", false);
              return;
            }
          } else {
            $(this).prop("checked", false);
            return;
          }
        }
        service_clinic.push({
          service_id: service_id,
          clinic_id: selected_service_clinic,
        });
      } else {
        service_clinic.splice(
          service_clinic.findIndex(
            (a) =>
              a.service_id === service_id &&
              a.clinic_id === selected_service_clinic
          ),
          1
        );
      }
    });
    //change tab if service is single select
    $(document).off("change", ".selected-service-single");
    $(document).on("change", ".selected-service-single", function (e) {
      if (this.checked) {
        $(".selected-service").prop("checked", false);
        $(this).prop("checked", true);
        if ($(this).attr("multipleservice") == "no") {
          $(".selected-service").prop("disabled", true);
          $(this).prop("disabled", false);
        }
        //move to next tab
        $(".iq-tab-pannel.active")
          .find("form")
          .find('[data-step="next"]')
          .trigger("click");
      } else {
        if ($(this).attr("multipleservice") == "no") {
          $(".selected-service").prop("disabled", false);
        }
      }
    });

    //move to next tab when doctor select
    $(document).off("change", ".kivicare-doctor-widget");
    $(document).on("change", ".kivicare-doctor-widget", function (e) {
      $(".iq-tab-pannel.active")
        .find("form")
        .find('[data-step="next"]')
        .trigger("click");
    });

    //move to next tab when doctor select
    $(document).off("change", ".service-category");
    $(document).on("change", ".service-category", function (e) {
      $(".iq-tab-pannel.active")
        .find("form")
        .find('[data-step="next"]')
        .trigger("click");
    });

    //move to next tab when timeslot selected
    $(document).off("change", ".selected-time");
    $(document).on("change", ".selected-time", function (e) {
      $(".iq-tab-pannel.active")
        .find("form")
        .find('[data-step="next"]')
        .trigger("click");
    });

    //move to next tab when clinic selected
    $(document).off("change", ".selected-clinic");
    $(document).on("change", ".selected-clinic", function (e) {
      $(".iq-tab-pannel.active")
        .find("form")
        .find('[data-step="next"]')
        .trigger("click");
    });

    //get appointment confirmation page details
    function showConfirmPage(target, currentTab) {
      var selectedService = kivicareGetSelectedServie("single", "service_id");
      let description = document.getElementById(
        "appointment-descriptions-field"
      );
      description = description !== null ? description.value : "";
      $("#kivi_confirm_page").addClass("d-none");
      $("#confirm_loader").removeClass("d-none");
      document.getElementById("kivi_confirm_page").innerHTML = "";
      setTimeout(
        () => {
          post("appointment_confirm_page", {
            clinic_id: kivicareGetSelectedItem("selected-clinic"),
            doctor_id: kivicareGetSelectedItem("selected-doctor"),
            service_list: selectedService,
            time: kivicareGetSelectedItem("selected-time"),
            date: appointmentDate,
            description: description,
            file: appointmentUploadFiles,
            custom_field: appointment_custom_fields,
          })
            .then((response) => {
              $("#kivi_confirm_page").removeClass("d-none");
              $("#confirm_loader").addClass("d-none");
              if (response.data.status) {
                document.getElementById("kivi_confirm_page").innerHTML =
                  validateDOMData(response.data.data);
                tax_details = response.data.tax_details;
              }
            })
            .catch((error) => {
              $("#kivi_confirm_page").removeClass("d-none");
              $("#confirm_loader").addClass("d-none");
              console.log(error);
              kivicareShowToastMessage(
                "error",
                bookAppointmentWidgetData.message.internal_server_msg
              );
            });

          $(`[href="#${currentTab}"]`)
            .closest(".tab-item")
            .attr("data-check", true);
          $(`[href="${target}"]`).closest(".tab-item").addClass("active block");
          tabShow(target);
        },
        bookAppointmentWidgetData.user_login ? 100 : 1000
      );
    }

    // register/login tab navbar change event
    $('[data-iq-toggle="tab"]').on("click", function (e) {
      $("#detail-info").removeClass("hidden");
      if ($(this).attr("href") === "#kc_login") {
        $(this)
          .closest("form")
          .find('button[data-step="next"]')
          .html(bookAppointmentWidgetData.message.login);
      }
      if ($(this).attr("href") === "#kc_register") {
        $(this)
          .closest("form")
          .find('button[data-step="next"]')
          .html(bookAppointmentWidgetData.message.register);
      }

      e.preventDefault();
      const tab_id = $(this).attr("href");
      if ($(this).attr("data-iq-tab") !== "prevent") {
        $(this)
          .closest(".tab-item")
          .find(".tab-link.active")
          .removeClass("active block");
        activeNavItem($(this));
        tabShow(tab_id);
        removeTabActiveLink($(this));
      }
    });

    //add active navitem class
    function activeNavItem(navlink) {
      $(navlink).addClass("active block");
      $(navlink)
        .closest(".tab-item")
        .addClass("active block")
        .removeClass("hidden");
    }

    //remove not active navitems class
    function removeTabActiveLink(target) {
      $(target)
        .closest(".tab-item")
        .siblings()
        .removeClass("active block")
        .addClass("hidden");
    }

    // Model script
    $(document).on("click", '[data-toggle="modal"]', function () {
      const target = $(this).data("target");
      showModal(target);
    });

    function showModal(target) {
      $(".modal").removeClass("show");
      $(target).addClass("show");
      const event = new CustomEvent("modalShown", {
        detail: {
          target: target,
        },
      });
      document.dispatchEvent(event);
    }

    //get doctor list from api
    function kivicareGetDoctorLists(searchKey_in = "") {
      let doctorLists = document.getElementById("doctorLists");
      doctorLists.classList.remove("card-list");
      kivicareAddLoader(doctorLists);
      var service_id = kivicareGetSelectedServie("single", "service_id");
      if (
        service_id.length == 0 &&
        bookAppointmentWidgetData.preselected_service != 0
      ) {
        service_id = [bookAppointmentWidgetData.preselected_service];
      }
      get(
        "get_clinic_selected_details",
        {
          clinic_id:
            kivicareGetSelectedItem("selected-clinic") ??
            bookAppointmentWidgetData.preselected_clinic_id,
          service_id: service_id,
          searchKey: searchKey_in,
          preselected_doctor: bookAppointmentWidgetData.preselected_doctor,
        },
        true
      )
        .then((res) => {
          doctorLists.innerHTML = "";
          let html;
          if (res.data.status !== undefined && res.data.status) {
            doctorLists.classList.add("card-list");
            doctorLists.innerHTML = validateDOMData(res.data.data);
          } else {
            doctorLists.innerHTML =
              `<p class="loader-class">` +
              bookAppointmentWidgetData.message.no_doctor_available +
              `</p>`;
          }
        })
        .catch((error) => {
          console.log(error);
          kivicareShowToastMessage(
            "kivicare_error_msg",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    }

    function initializeSelect2(element) {
      $(element).select2({
        placeholder: "Select options",
        allowClear: true,
        width: "100%",
        theme: "classic",
        containerCssClass:
          "w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black",
        dropdownCssClass: "select2-dropdown-custom",
      });
    }

    function kivicareGetClinicsLists(searchKey_in = "") {
      let clinicCard = document.getElementById("clinicLists");
      clinicCard.classList.remove("card-list");
      kivicareAddLoader(clinicCard);
      var service_id = kivicareGetSelectedServie("single", "service_id");
      if (
        service_id.length == 0 &&
        bookAppointmentWidgetData.preselected_service != 0
      ) {
        service_id = [bookAppointmentWidgetData.preselected_service];
      }
      get(
        "get_clinic_details_appointment",
        {
          doctor_id: kivicareGetSelectedItem("selected-doctor"),
          service_id: service_id,
          searchKey: searchKey_in,
          preselected_clinic: bookAppointmentWidgetData.preselected_clinic_id,
        },
        true
      )
        .then((res) => {
          clinicCard.innerHTML = "";
          if (res.data.status !== undefined && res.data.status) {
            clinicCard.classList.add("card-list");
            clinicCard.innerHTML = validateDOMData(res.data.data);
          } else {
            clinicCard.innerHTML =
              `<p class="loader-class"> ` +
              bookAppointmentWidgetData.message.no_clinic_available +
              ` </p>`;
          }
        })
        .catch((error) => {
          console.log(error);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    }

    //get service list from api
    function kivicareGetServiceLists(searchKey_in) {
      let serviceLists = document.getElementById("serviceLists");
      kivicareAddLoader(serviceLists);
      get(
        "get_clinic_service",
        {
          doctor_id: kivicareGetSelectedItem("selected-doctor"),
          searchKey: searchKey_in,
          widgetType: "phpWidget",
          clinic_id: kivicareGetSelectedItem("selected-clinic"),
          preselected_service: bookAppointmentWidgetData.preselected_service,
          service_category: kivicareGetSelectedItem("service-category"),
        },
        true
      )
        .then((res) => {
          serviceLists.innerHTML = "";
          if (res.data.status !== undefined && res.data.status) {
            console.log(res.data.html);

            serviceLists.innerHTML = validateDOMData(res.data.html);
            var service_data = kivicareGetSelectedServie("single", "doctor_id");

            if (
              bookAppointmentWidgetData.skip_service_when_single == true &&
              service_data.length !== 0
            ) {
              if (
                searchKey_in == undefined ||
                searchKey_in == null ||
                searchKey_in == ""
              ) {
                document.querySelector('[data-step="next"]').click();
              }
            }
          } else {
            serviceLists.innerHTML =
              `<p class="loader-class">` +
              bookAppointmentWidgetData.message.no_service_available +
              `</p>`;
          }
        })
        .catch((error) => {
          console.log(error);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    }
    //get service list from api
    function kivicareGetServiceCategoryLists(searchKey_in) {
      let serviceCategoryLists = document.getElementById(
        "serviceCategoryLists"
      );
      kivicareAddLoader(serviceCategoryLists);
      get(
        "get_clinic_service_category",
        {
          doctor_id: kivicareGetSelectedItem("selected-doctor"),
          searchKey: searchKey_in,
          widgetType: "phpWidget",
          clinic_id: kivicareGetSelectedItem("selected-clinic"),
          preselected_service: bookAppointmentWidgetData.preselected_service,
        },
        true
      )
        .then((res) => {
          serviceCategoryLists.innerHTML = "";
          if (res.data.status !== undefined && res.data.status) {
            console.log(res.data.html);

            serviceCategoryLists.innerHTML = validateDOMData(res.data.html);
            var service_data = kivicareGetSelectedServie("single", "doctor_id");

            if (
              bookAppointmentWidgetData.skip_service_when_single == true &&
              service_data.length !== 0
            ) {
              if (
                searchKey_in == undefined ||
                searchKey_in == null ||
                searchKey_in == ""
              ) {
                document.querySelector('[data-step="next"]').click();
              }
            }
          } else {
            serviceCategoryLists.innerHTML =
              `<p class="loader-class">` +
              bookAppointmentWidgetData.message.no_service_available +
              `</p>`;
          }
        })
        .catch((error) => {
          console.log(error);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    }

    //get selected/checked value id (clinic/doctor)
    function kivicareGetSelectedItem(element) {
      let defaultSelected = 0;
      if (element === "selected-clinic") {
        //return clinic id already in pass in shortcode or by query parameters
        // if (parseInt(bookAppointmentWidgetData.preselected_clinic_id) !== 0) {
        //     return bookAppointmentWidgetData.preselected_clinic_id
        // }

        if (bookAppointmentWidgetData.preselected_single_clinic_id) {
          return bookAppointmentWidgetData.preselected_clinic_id;
        }
      }

      if (element === "selected-doctor") {
        //return doctor id already in pass in shortcode or by query parameters
        if (bookAppointmentWidgetData.preselected_single_doctor_id) {
          return bookAppointmentWidgetData.preselected_doctor;
        }

        let tempElement = $(".selected-service");
        if (tempElement.length > 0) {
          for (let i = 0; i < tempElement.length; i++) {
            if (tempElement[i].checked == true) {
              defaultSelected = tempElement[i].getAttribute("doctor_id");
            }
          }
        }
        return defaultSelected;
      }

      let tempElement = $("." + element);
      if (tempElement.length > 0) {
        for (let i = 0; i < tempElement.length; i++) {
          if (tempElement[i].checked == true) {
            defaultSelected = tempElement[i].value;
          }
        }
      }
      return defaultSelected;
    }

    //get selected service
    function kivicareGetSelectedServie(type = "all", value = "") {
      var service_id = [];
      var visit_type;
      if (bookAppointmentWidgetData.selected_service_id_data != null) {
        let service_single_data =
          bookAppointmentWidgetData.selected_service_id_data;
        if (type === "all") {
          visit_type = {
            id: service_single_data.id,
            service_id: service_single_data.service_id,
            name: service_id.name,
            charges: service_id.charges,
          };
          service_id.push(visit_type);
        } else {
          service_single_data.service_id &&
            service_id.push(service_single_data.service_id);
        }
        return service_id;
      } else {
        var name = $(".selected-service");
        if (name.length > 0) {
          for (var i = 0; i < name.length; i++) {
            if (name[i].checked == true) {
              if (type === "all") {
                visit_type = {
                  id: name[i].value,
                  service_id: name[i].attributes.service_id.nodeValue,
                  name: name[i].attributes.service_name.nodeValue,
                  charges: name[i].attributes.service_price.nodeValue,
                };
                service_id.push(visit_type);
              } else {
                service_id.push(name[i].attributes[value].nodeValue);
              }
            }
          }
        }

        return service_id;
      }
    }

    function kivicareShowToastMessage(type, message) {
      // Define toast configurations for different types
      const toastConfigs = {
        success: {
          background: "rgba(16, 185, 129, 0.1)",
          icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(16, 185, 129)" stroke-width="2"><path d="M20 6L9 17L4 12"></path></svg>`,
          iconColor: "rgb(16, 185, 129)",
          borderColor: "rgba(16, 185, 129, 0.2)",
        },
        error: {
          background: "rgba(239, 68, 68, 0.1)",
          icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(239, 68, 68)" stroke-width="2"><path d="M18 6L6 18M6 6l12 12"></path></svg>`,
          iconColor: "rgb(239, 68, 68)",
          borderColor: "rgba(239, 68, 68, 0.2)",
        },
        warning: {
          background: "rgba(245, 158, 11, 0.1)",
          icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(245, 158, 11)" stroke-width="2"><path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>`,
          iconColor: "rgb(245, 158, 11)",
          borderColor: "rgba(245, 158, 11, 0.2)",
        },
        info: {
          background: "rgba(59, 130, 246, 0.1)",
          icon: `<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="rgb(59, 130, 246)" stroke-width="2"><path d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`,
          iconColor: "rgb(59, 130, 246)",
          borderColor: "rgba(59, 130, 246, 0.2)",
        },
      };

      // Get config for the specified type, default to 'info' if type not found
      const config = toastConfigs[type.toLowerCase()] || toastConfigs.info;

      // Create the message with icon
      const messageWithIcon = `
          <div class="flex items-center">
              ${config.icon}
              <span>${message}</span>
          </div>
      `;

      // Add custom styles if not already added
      if (!document.getElementById("toast-custom-styles")) {
        const styleSheet = document.createElement("style");
        styleSheet.id = "toast-custom-styles";
        styleSheet.textContent = `
              @keyframes toast-progress {
                  from { width: 100%; }
                  to { width: 0%; }
              }
              .toast-progress-bar {
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  height: 2px;
                  background: ${config.iconColor};
                  opacity: 0.7;
                  animation: toast-progress 3s linear forwards;
              }
          `;
        document.head.appendChild(styleSheet);
      }

      Toastify({
        text: messageWithIcon,
        duration: 3000,
        close: true,
        gravity: "top",
        position: "right",
        stopOnFocus: true,
        className: "toast-modern",
        style: {
          background: config.background,
          borderRadius: "12px",
          padding: "16px",
          fontSize: "14px",
          fontWeight: "500",
          color: "#1f2937",
          border: `1px solid ${config.borderColor}`,
          backdropFilter: "blur(8px)",
          boxShadow:
            "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          minWidth: "300px",
          maxWidth: "400px",
          transition: "transform 0.2s ease",
        },
        callback: function (toast) {
          // Add progress bar
          const progressBar = document.createElement("div");
          progressBar.className = "toast-progress-bar";
          toast.element.appendChild(progressBar);

          // Add hover effect
          toast.element.addEventListener("mouseenter", () => {
            toast.element.style.transform = "translateY(-2px)";
          });
          toast.element.addEventListener("mouseleave", () => {
            toast.element.style.transform = "translateY(0)";
          });
        },
        escapeMarkup: false,
        onClick: function () {
          this.close();
        },
      }).showToast();
    }

    //show error message
    function kivicareShowErrorMessage(element, message) {
      document.getElementById(element).style.display = "block";
      if (message !== "") {
        document.getElementById(element).innerHTML = message;
      }
      setTimeout(() => {
        document.getElementById(element).style.display = "none";
      }, 3000);
    }

    function getCountryCodeData() {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            var valueString =
              '{"countryCallingCode":"+' +
              response.data.data.country_calling_code +
              '","countryCode":"' +
              response.data.data.country_code +
              '"}';
            jQuery("#CountryCode").val(valueString).trigger("change");
          }
        })
        .catch((error) => {
          console.log(error);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    }

    //get doctor working days array
    function kivicareGetDoctorWeekday(id) {
      const loader = document.querySelector("#doctor-datepicker-loader");
      const sessionError = document.querySelector(".doctor-session-error");
      const calendarSlot = document.querySelector(".iq-kivi-calendar-slot");

      // Check if elements exist
      if (loader) loader.classList.remove("d-none");
      if (sessionError) sessionError.classList.add("d-none");
      if (calendarSlot) calendarSlot.classList.add("d-none");

      let selected_clinic = kivicareGetSelectedItem("selected-clinic");
      let doctorWorkdayajaxData = {
        clinic_id: selected_clinic,
        doctor_id: id,
        type: "flatpicker", // Adjust if needed
      };

      get("get_doctor_workdays", doctorWorkdayajaxData)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            restrictionData = bookAppointmentWidgetData.restriction_data;
            days = response.data.data;
            holidays = response.data.holiday;

            if ([0, 1, 2, 3, 4, 5, 6].every((r) => days.includes(r))) {
              if (sessionError) sessionError.classList.remove("d-none");
              if (loader) loader.classList.add("d-none");
              if (calendarSlot) calendarSlot.classList.add("d-none");
            } else {
              if (calendarSlot) calendarSlot.classList.remove("d-none");
              if (loader) loader.classList.add("d-none");
              if (sessionError) sessionError.classList.add("d-none");

              // Render calendar
              renderCalendarDays(
                new Date().getMonth(),
                new Date().getFullYear(),
                days,
                restrictionData,
                holidays
              );
            }
          }
        })
        .catch((error) => {
          console.log(error);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    }

    function renderCalendarDays(
      month,
      year,
      disabledDays,
      restrictionData,
      holidays
    ) {
      const calendarDays = document.getElementById("calendar-days");
      const calendarTitle = document.getElementById("calendar-title");

      if (!calendarDays || !calendarTitle) return;

      calendarDays.innerHTML = "";

      const monthName = new Date(year, month).toLocaleString("default", {
        month: "long",
      });
      calendarTitle.textContent = `${monthName} ${year}`;

      const firstDay = new Date(year, month, 1).getDay();
      const lastDate = new Date(year, month + 1, 0).getDate();

      // Convert holidays to a set of dates for quick lookup
      const holidayDates = new Set();
      holidays.forEach((holiday) => {
        const startDate = new Date(holiday.start_date);
        const endDate = new Date(holiday.end_date);
        for (
          let d = new Date(startDate);
          d <= endDate;
          d.setDate(d.getDate() + 1)
        ) {
          holidayDates.add(d.toISOString().split("T")[0]);
        }
      });

      // Add empty cells for days before the first day of the month
      for (let i = 0; i < firstDay; i++) {
        calendarDays.innerHTML +=
          '<div class="text-center text-sm text-gray-300"></div>';
      }

      // Add days of the month
      for (let day = 1; day <= lastDate; day++) {
        const date = new Date(year, month, day);
        const formattedDate = date.toISOString().split("T")[0];
        const dayButton = document.createElement("button");
        dayButton.type = "button";
        dayButton.classList.add(
          "date-container",
          "p-1",
          "text-sm",
          "rounded-lg",
          "transition-colors",
          "hover:bg-violet-50",
          "text-gray-700"
        );
        dayButton.textContent = day;
        const today = new Date();
  const isToday = today.getFullYear() === year &&
                  today.getMonth() === month &&
                  today.getDate() === day;

  if (isToday) {
    dayButton.classList.add("ring-1", "ring-violet-600", "font-bold");
  }


        // Disable if it's a restricted day or a holiday
        // Use UTC date comparison to avoid timezone/DST issues
        const dateToCheck = new Date(Date.UTC(year, month, day, 0, 0, 0));
        const todayUTC = new Date();
        const todayAtMidnight = new Date(Date.UTC(todayUTC.getFullYear(), todayUTC.getMonth(), todayUTC.getDate(), 0, 0, 0));
        const isPastDate = dateToCheck < todayAtMidnight && !isToday;

  if (disabledDays.includes(date.getDay()) ||
      holidayDates.has(formattedDate) ||
      isPastDate) {
    dayButton.classList.add("opacity-50", "cursor-not-allowed", "text-gray-300");
    dayButton.disabled = true;
  } else {
    dayButton.addEventListener("click", () => {
      loadTimeSlots(year, month + 1, day);
    });
  }

  calendarDays.appendChild(dayButton);
}

      // Get today's day number
      const today = new Date().getDate();

      // Collect all the buttons that represent dates
      const calendarButtons = Array.from(
        document.querySelectorAll("#calendar-days button.date-container")
      );

      // Helper: Find a button with the day text matching the given number
      const getButtonForDay = (day) =>
        calendarButtons.find(
          (button) => button.textContent.trim() === String(day)
        );

      // Attempt to get the button for today
      let targetButton = getButtonForDay(today);

      // Check if the button for today is available (exists and not disabled)
      if (targetButton && !targetButton.disabled) {
        targetButton.click();
        console.log(`Clicked today's button: ${today}`);
      } else {
        // If today's button is not available, find the next enabled button
        // First filter for buttons that are enabled
        const availableButtons = calendarButtons
          .filter((button) => !button.disabled)
          .sort(
            (a, b) =>
              parseInt(a.textContent.trim(), 10) -
              parseInt(b.textContent.trim(), 10)
          );

        // Look for the first enabled button with a day number greater than today
        targetButton = availableButtons.find(
          (button) => parseInt(button.textContent.trim(), 10) > today
        );

        // If no later date is found, optionally click the first available one.
        if (!targetButton && availableButtons.length > 0) {
          targetButton = availableButtons[0];
        }

        if (targetButton) {
          targetButton.click();
          console.log(
            `Today's button was unavailable. Clicked next available day: ${targetButton.textContent.trim()}`
          );
        } else {
          console.log("No available day button found to click.");
        }
      }
    }

    // Update navigation handlers
    $(document).on("click", "#prev-month", function () {
      currentDisplayMonth--;
      if (currentDisplayMonth < 0) {
        currentDisplayMonth = 11;
        currentDisplayYear--;
      }
      renderCalendarDays(
        currentDisplayMonth,
        currentDisplayYear,
        days,
        restrictionData,
        holidays
      );
    });

    $(document).on("click", "#next-month", function () {
      currentDisplayMonth++;
      if (currentDisplayMonth > 11) {
        currentDisplayMonth = 0;
        currentDisplayYear++;
      }
      renderCalendarDays(
        currentDisplayMonth,
        currentDisplayYear,
        days,
        restrictionData,
        holidays
      );
    });

    function loadTimeSlots(year, month, day) {
      // Important fix: Ensure we're working with numeric values
      year = parseInt(year);
      month = parseInt(month);
      day = parseInt(day);

      // Create a date object using UTC to avoid timezone issues
      // Note: month is 1-based in the input but Date constructor uses 0-based months
      const dateObj = new Date(Date.UTC(year, month - 1, day));

      // Format the date properly as YYYY-MM-DD using UTC values to avoid DST issues
      const formattedDate = dateObj.getUTCFullYear() + '-' +
                           (dateObj.getUTCMonth() + 1 < 10 ? '0' + (dateObj.getUTCMonth() + 1) : (dateObj.getUTCMonth() + 1)) + '-' +
                           (dateObj.getUTCDate() < 10 ? '0' + dateObj.getUTCDate() : dateObj.getUTCDate());

      // Store the formatted date
      appointmentDate = formattedDate;

      console.log('Loading time slots for date:', formattedDate);

      const timeSlotLists = document.getElementById("timeSlotLists");
      const timeSlotGrid = document.getElementById("timeSlotGrid");

      timeSlotLists.innerHTML = '<p class="loader-class text-center text-gray-500 font-semibold">Loading time slots...</p>';
      timeSlotLists.classList.remove("hidden");
      timeSlotGrid.classList.add("hidden");

      const selectedClinic = kivicareGetSelectedItem("selected-clinic");
      const selectedDoctor = kivicareGetSelectedItem("selected-doctor");
      const visitTypeData = kivicareGetSelectedServie("all", "");

      const timeSlotAjaxData = {
        doctor_id: selectedDoctor,
        clinic_id: selectedClinic,
        date: formattedDate,
        widgetType: "phpWidget",
        service: visitTypeData,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone // Send client timezone
      };

      // Fetch time slots
      get("get_time_slots", timeSlotAjaxData)
        .then((res) => {
          if (res.data.status) {
            timeSlotGrid.innerHTML = validateDOMData(res.data.html);
            timeSlotLists.classList.add("hidden");
            timeSlotGrid.classList.remove("hidden");
          } else {
            timeSlotLists.innerHTML = `<p class="loader-class text-center text-gray-500 font-semibold">${res.data.message}</p>`;
            timeSlotLists.classList.remove("hidden");
            timeSlotGrid.classList.add("hidden");
          }
        })
        .catch((error) => {
          console.log(error);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    }

    //function to get loader content
    function kivicareAddLoader(ele) {
      let load = bookAppointmentWidgetData.loader_by_image
        ? '<img src="' + bookAppointmentWidgetData.loader_image_url + '">'
        : '<div class="double-lines-spinner"></div>';
      ele.innerHTML =
        `<span class="loader-class" id="doctor_loader">` + load + `</span>`;
    }

    //appointment file upload
    $(document).off("change", "#kivicareaddMedicalReport");
    $(document).on("change", "#kivicareaddMedicalReport", function (e) {
      document.getElementById("kivicare_file_upload_review").innerHTML = "";
      appointmentUploadFiles = [];
      let form_id = document.getElementById("kivicare-file-upload-form");
      let formData = new FormData(form_id);
      kivicareButtonDisableChangeText(
        "#kivicare-file-upload-form",
        true,
        bookAppointmentWidgetData.message.loading
      );
      //api to upload files and get upload files attachment ids
      post("upload_multiple_report", formData)
        .then((response) => {
          kivicareButtonDisableChangeText(
            "#kivicare-file-upload-form",
            false,
            bookAppointmentWidgetData.message.next
          );
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            if (response.data.data.length > 0) {
              kivicareShowToastMessage("success", response.data.message);
              appointmentUploadFiles = response.data.data;
              document.getElementById("kivicare_file_upload_review").innerHTML =
                validateDOMData(response.data.html);
            }
          } else {
            kivicareShowToastMessage("error", response.data.message);
          }
        })
        .catch((error) => {
          kivicareButtonDisableChangeText(
            "#kivicare-file-upload-form",
            false,
            bookAppointmentWidgetData.message.next
          );
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });
    });

    //get custom fields data
    function kivicareCustomFieldsData(ele) {
      var custom_fields = {};
      $.each(
        $("#" + ele)
          .find("select, textarea, :input:not(:checkbox)")
          .serializeArray(),
        function () {
          custom_fields[this.name] = this.value;
        }
      );
      var temp = [];
      var temp2 = "";
      $.each(
        $("#" + ele)
          .find(":input:checkbox")
          .serializeArray(),
        function (key, value) {
          if (temp2 !== value.name) {
            temp = [];
          }
          temp.push(value.value);
          custom_fields[value.name] = temp;
          temp2 = value.name;
        }
      );
      $("#" + ele)
        .find(".appointment_widget_multiselect")
        .each(function () {
          custom_fields[$(this).attr("name")] = $(this)
            .val()
            .map((index) => {
              return { id: index, text: index };
            });
        });
      return custom_fields;
    }

    //save appointment function
    function kivicareBookAppointment(_this, payment_mode, result = []) {
      payment_select_mode = payment_mode;
      var visit_type_data = kivicareGetSelectedServie("all", "");
      let description = document.getElementById(
        "appointment-descriptions-field"
      );
      description = description !== null ? description.value : "";

      let formElement = $(_this).parents("form");
      let opacityChangeElement = formElement;
      let messageSpanElementId = $(formElement)
        .find(".card-widget-footer span")
        .attr("id");
      let overlaySpinElement = $(formElement).find(".kivi-overlay-spinner");
      if (formElement.attr("id") !== "payment_mode_form") {
        opacityChangeElement = $(formElement).find(".kivi-col-6");
      }
      $(opacityChangeElement).css("opacity", "0.5");
      kivicareButtonDisableChangeText(
        formElement,
        true,
        bookAppointmentWidgetData.message.loading
      );
      kivicareButtonDisableBackButton(formElement, true);
      $(overlaySpinElement).removeClass("d-none");
      // document.getElementById(messageSpanElementId).style.display = 'none';

      post("save_appointment", {
        appointment_start_date: appointmentDate,
        appointment_start_time: kivicareGetSelectedItem("selected-time"),
        visit_type: visit_type_data,
        patient_id: bookAppointmentWidgetData.current_user_id,
        doctor_id: { id: kivicareGetSelectedItem("selected-doctor") },
        clinic_id: { id: kivicareGetSelectedItem("selected-clinic") },
        status: 1,
        enableTeleMed: "",
        file: appointmentUploadFiles,
        custom_fields: appointment_custom_fields,
        description: description,
        widgetType: bookAppointmentWidgetData.popup_appointment_book
          ? "popupPhpWidget"
          : "phpWidget",
        payment_mode: payment_mode,
        g_recaptcha_response: result
          ? result["g-recaptcha-response"]
            ? result["g-recaptcha-response"]
            : ""
          : "",
        tax: tax_details,
        pageId: bookAppointmentWidgetData.pageId,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            let checkWoocommerceCart = response.data;

            appointment_id = response.data.data.id;
            sessionStorage.setItem("kivicare_appointment_id", appointment_id);

            if (checkWoocommerceCart.woocommerce_cart_data !== undefined) {
              if (
                checkWoocommerceCart.woocommerce_cart_data
                  .woocommerce_redirect !== undefined
              ) {
                if (payment_mode === "paymentPaypal") {
                  child = window.open(
                    checkWoocommerceCart.woocommerce_cart_data
                      .woocommerce_redirect,
                    "_blank",
                    "toolbar=0,status=0,width=360,height=500,top=100,left=" +
                      (window.screen ? Math.round(screen.width / 2 - 275) : 100)
                  );
                  appointment_id = response.data.data.id;
                  timer = setInterval(checkChildWindow, 500);
                  return;
                } else {
                  location.href =
                    checkWoocommerceCart.woocommerce_cart_data.woocommerce_redirect;
                }
              }
            } else {
              if (payment_mode === "paymentRazorpay") {
                if (response.data.checkout_detail) {
                  kivicareCreateRazorpayCheckoutPage(
                    response.data.checkout_detail
                  );
                } else {
                  kivicareShowToastMessage(
                    messageSpanElementId,
                    response.data.message
                  );
                }
              } else if (payment_mode === "paymentStripepay") {
                if (response.data.checkout_detail) {
                  // Open Stripe Checkout in a tab
                  window.location.href =
                    response.data.checkout_detail.stripe_redirect_url;
                  return;
                } else {
                  kivicareShowToastMessage(
                    messageSpanElementId,
                    response.data.message
                  );
                }
              } else {
                kivicareLoadConfirmPage(response.data.data.id);
              }
            }
          } else {
            let message =
              response.data.message !== undefined
                ? response.data.message
                : bookAppointmentWidgetData.message.internal_server_msg;
            kivicareShowToastMessage(messageSpanElementId, message);
          }
          kivicareButtonDisableChangeText(
            formElement,
            false,
            bookAppointmentWidgetData.message.confirm
          );
          $(opacityChangeElement).css("opacity", "");
          $(overlaySpinElement).addClass("d-none");
        })
        .catch((error) => {
          kivicareButtonDisableChangeText(
            formElement,
            false,
            bookAppointmentWidgetData.message.confirm
          );
          $(opacityChangeElement).css("opacity", "");
          $(overlaySpinElement).addClass("d-none");
          kivicareShowToastMessage(
            messageSpanElementId,
            bookAppointmentWidgetData.message.internal_server_msg
          );
          console.log(error);
        });
    }

    //check child window close (open for paypay payment )
    function checkChildWindow() {
      //if child window closed and payment failed
      if (child.closed && payment_status === "") {
        clearInterval(timer);
        let formElement = jQuery("#payment_mode_form");
        kivicareButtonDisableChangeText(
          formElement,
          false,
          bookAppointmentWidgetData.message.confirm
        );
        jQuery(formElement).css("opacity", "");
        jQuery(formElement).find(".kivi-overlay-spinner").addClass("d-none");
        tabShow("#payment_error");
        if (appointment_id !== "") {
          validateBookAppointmentWidgetData(bookAppointmentWidgetData);

          axios
            .get(
              bookAppointmentWidgetData.ajax_url +
                "?action=ajax_get&route_name=appointment_delete&id=" +
                appointment_id +
                "&_ajax_nonce=" +
                bookAppointmentWidgetData.ajax_get_nonce
            )
            .then((response) => {})
            .catch((error) => {
              console.log(error);
            });
        }
      }
    }

    //close child window if parent window close
    window.onbeforeunload = function () {
      if (child !== "") {
        child.close();
      }
    };

    //focus child window on loader click
    $(document).on("click", "#payment_mode_confirm_loader", function (e) {
      e.preventDefault();
      if (child !== "") {
        child.focus();
      }
    });

    //get appointment payment page content
    function showPaymentPage(target, currentTab) {
      var selectedService = kivicareGetSelectedServie("all", "");
      let description = document.getElementById(
        "appointment-descriptions-field"
      );
      description = description !== null ? description.value : "";
      post("get_widget_payment_options", {
        clinic_id: kivicareGetSelectedItem("selected-clinic"),
        doctor_id: kivicareGetSelectedItem("selected-doctor"),
        service_list: selectedService,
        time: kivicareGetSelectedItem("selected-time"),
        date: appointmentDate,
        description: description,
        file: appointmentUploadFiles,
        custom_field: appointment_custom_fields,
      })
        .then((response) => {
          $("#kivi_confirm_payment_page").removeClass("d-none");
          $("#confirm_loader").addClass("d-none");
          if (response.data.status) {
            document.getElementById("kivi_confirm_payment_page").innerHTML =
              validateDOMData(response.data.data);
          }
        })
        .catch((error) => {
          $("#kivi_confirm_payment_page").removeClass("d-none");
          $("#confirm_loader").addClass("d-none");
          console.log(error);
          kivicareShowToastMessage(
            "error",
            bookAppointmentWidgetData.message.internal_server_msg
          );
        });

      $(`[href="#${currentTab}"]`)
        .closest(".tab-item")
        .attr("data-check", true);
      $(`[href="${target}"]`)
        .closest(".tab-item")
        .addClass("active block")
        .removeClass("hidden");
      tabShow(target);
    }
  })(window.jQuery);
}

//check payment complete by razorpay or PayPal gateway
function kivicareCheckPaymentStatus(newStatus, newAppointmentID) {
  // clearInterval(timer);
  switch (newStatus) {
    case "approved":
      kivicareLoadConfirmPage(newAppointmentID);
      break;
    case "failed":
      let formElement = jQuery("#payment_mode_form");
      kivicareButtonDisableChangeText(
        formElement,
        false,
        bookAppointmentWidgetData.message.confirm
      );
      jQuery(formElement).css("opacity", "");
      jQuery(formElement).find(".kivi-overlay-spinner").addClass("d-none");
      tabShow("#payment_error");
      jQuery("#kivicare-widget-main-content")
        .find(".iq-tab-pannel")
        .each(function () {
          if (jQuery(this).is("#payment_error")) {
            jQuery(this)
              .addClass("active")
              .removeClass("hidden")
              .attr("data-check", false);
          } else {
            jQuery(this)
              .removeClass("active")
              .addClass("hidden")
              .attr("data-check", true);
          }
        });
      break;
  }
}

//load appointment confirmation page
function kivicareLoadConfirmPage(value) {
  if (value !== "off") {
    kivicarePrintContent(value);
    highlightAllTabs();
    if (jQuery("#kivicare-widget-main-content").length > 0) {
      jQuery("#kivicare-widget-main-content")
        .find(".iq-tab-pannel")
        .each(function () {
          if (jQuery(this).is("#confirmed")) {
            jQuery(this)
              .addClass("active")
              .removeClass("hidden")
              .attr("data-check", false);
          } else {
            jQuery(this)
              .removeClass("active")
              .addClass("hidden")
              .attr("data-check", true);
          }
        });
    }
  }
}

var prr = "";

var config = "";

//get appointment print content after successfull booking
function kivicarePrintContent(id) {
  validateBookAppointmentWidgetData(bookAppointmentWidgetData);

  axios
    .get(
      bookAppointmentWidgetData.ajax_url +
        "?action=ajax_get&route_name=get_appointment_print&id=" +
        id +
        "&calendar_enable=yes&_ajax_nonce=" +
        bookAppointmentWidgetData.ajax_get_nonce
    )
    .then((response) => {
      if (response.data.status !== undefined && response.data.status === true) {
        prr = response.data.data;

        if (
          response.data.patient_id_match !== undefined &&
          response.data.patient_id_match === true
        ) {
          const printButton = document.querySelector("#kivicare_print_detail");
          if (
            response.data.calendar_content !== undefined &&
            response.data.calendar_content !== ""
          ) {
            printButton.classList.remove("d-none");
            config = response.data.calendar_content;
            const button = document.querySelector("#kivicare_add_to_calendar");
            button.classList.remove("d-none");
            button.addEventListener("click", () => atcb_action(config, button));
          }
        } else if (response.data.patient_id_match === false) {
          // Get the current URL
          const currentUrl = window.location.href;

          // Check if the URL contains "?confirm_page="
          if (currentUrl.includes("?confirm_page=")) {
            // Remove the query parameter and redirect
            const newUrl = currentUrl.split("?")[0];
            window.location.href = newUrl;
          }
        }
        // document.body.innerHTML = prr;
      }
    })
    .catch((error) => {
      console.log(error);
    });
}

// document.addEventListener('click', function(event) {
//     if (event.target.closest('#kivi_confirm_payment_page .relative')) {
//       const radioInput = event.target.closest('#kivi_confirm_payment_page .relative').querySelector('input[name="payment_option"]');
//       if (radioInput) {
//         radioInput.checked = true;
//       }
//     }
//   });

// Find all payment option containers and add click handler
document.addEventListener("click", function (event) {
  // Check if clicked element or its parent is a payment option container
  const paymentContainer = event.target.closest(
    "#kivi_confirm_payment_page .relative"
  );
  if (paymentContainer) {
    // Find the radio input within this container
    const radioInput = paymentContainer.querySelector(
      'input[name="payment_option"]'
    );
    if (radioInput) {
      // Check the radio input
      radioInput.checked = true;

      // Reset all containers to default state
      document.querySelectorAll(".relative").forEach((container) => {
        const label = container.querySelector("label");
        if (label) {
          label.classList.remove("border-purple-500", "bg-purple-50");
          label.classList.add("border-gray-200");
        }
        const circle = container.querySelector(".w-5.h-5");
        if (circle) {
          circle.classList.remove("border-purple-500", "bg-purple-500");
          circle.classList.add("border-gray-300");
        }
      });

      // Style the selected container
      const selectedLabel = paymentContainer.querySelector("label");
      if (selectedLabel) {
        selectedLabel.classList.remove("border-gray-200");
        selectedLabel.classList.add("border-purple-500", "bg-purple-50");
      }

      // Style the check circle
      const selectedCircle = paymentContainer.querySelector(".w-5.h-5");
      if (selectedCircle) {
        selectedCircle.classList.remove("border-gray-300");
        selectedCircle.classList.add("border-purple-500", "bg-purple-500");
      }
    }
  }
});

// Also handle direct radio button changes
document.querySelectorAll('input[name="payment_option"]').forEach((radio) => {
  radio.addEventListener("change", function () {
    // Trigger the same styling logic when radio is changed directly
    this.closest(".relative").click();
  });
});

//appointment print click event
jQuery(document).on("click", "#kivicare_print_detail", function () {
  jQuery(prr).printArea({});
});

//tabshow function
function tabShow(target) {
  let $ = jQuery;
  jQuery(target)
    .removeClass("hidden")
    .addClass("active block")
    .siblings()
    .addClass("hidden")
    .removeClass("active");
  const tab = jQuery(target).closest(".tab-content").attr("id");
  const event = new CustomEvent(`tabShown-${tab}`, {
    detail: {
      target: target,
    },
  });
  document.dispatchEvent(event);
}

//enable/disable button
function kivicareButtonDisableChangeText(ele, disableEnable, buttonText) {
  let element = jQuery(ele).find('button[type="submit"]');
  element.prop("disabled", disableEnable);
  element.html(buttonText);
}

//disable back button
function kivicareButtonDisableBackButton(ele, disableEnable) {
  let element = jQuery(ele).find('button[id="iq-widget-back-button"]');
  element.prop("disabled", disableEnable);
}

function kivicareFileUploadSizeCheck(event) {
  if (
    event.target.files &&
    event.target.files.length > 0 &&
    event.target.files[0].size > bookAppointmentWidgetData.allowed_file_size
  ) {
    jQuery(event.target).css("border", "1px solid var(--iq-secondary-dark)");
    jQuery(event.target).siblings("div").css("display", "block");
    event.target.value = ""; // Clear the file input field
  } else {
    jQuery(event.target).css("border", "1px solid #eee");
    jQuery(event.target).siblings("div").css("display", "none");
  }
}

function validateBookAppointmentWidgetData(bookAppointmentWidgetData) {
  if (!bookAppointmentWidgetData.ajax_url) {
    console.error("ajax_url is required.");
    return;
  }

  var urlPattern = /^(?:https?):\/\/[\S]+$/;
  if (!urlPattern.test(bookAppointmentWidgetData.ajax_url)) {
    console.error(
      "ajax_url is not a valid URL:",
      bookAppointmentWidgetData.ajax_url
    );
    return;
  }

  if (!bookAppointmentWidgetData.ajax_post_nonce) {
    console.error("ajax_post_nonce is required.");
    return;
  }

  var noncePattern = /^[a-zA-Z0-9_-]{10,}$/;

  if (!noncePattern.test(bookAppointmentWidgetData.ajax_post_nonce)) {
    console.error("ajax_post_nonce is not a valid");
    return;
  }

  if (!bookAppointmentWidgetData.ajax_get_nonce) {
    console.error("ajax_get_nonce is required.");
    return;
  }

  if (!noncePattern.test(bookAppointmentWidgetData.ajax_get_nonce)) {
    console.error("ajax_get_nonce is not a valid");
    return;
  }
}

function validateDOMData(html) {
  html = html.replace(
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    ""
  );
  return html.replace(/on\w+="[^"]*"/g, "");
}

function highlightAllTabs() {
  // Configuration for each tab: key is the tab's selector,
  // value is an object with the text and background classes.
  const tabConfig = {
    "#categoryTab": { text: "text-indigo-600", bg: "bg-indigo-100" },
    "#serviceTab": { text: "text-indigo-600", bg: "bg-indigo-100" },
    "#dateTimeTab": { text: "text-violet-600", bg: "bg-violet-50" },
    "#detailsTab": { text: "text-fuchsia-600", bg: "bg-fuchsia-100" },
    "#loginTab": { text: "text-rose-600", bg: "bg-rose-100" },
    "#confirmTab": { text: "text-pink-600", bg: "bg-pink-100" },
  };

  // Loop through each tab configuration and update the classes.
  jQuery.each(tabConfig, function (selector, classes) {
    const $tab = jQuery(selector);
    // Update all divs: remove default classes and add the new text and bg classes.
    $tab
      .find("div")
      .removeClass("text-gray-400 bg-gray-50")
      .addClass(`${classes.text} ${classes.bg}`);
    // Update all spans: remove default text class and add the new text class.
    $tab.find("span").removeClass("text-gray-400").addClass(classes.text);
  });
}
