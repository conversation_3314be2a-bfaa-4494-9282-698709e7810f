<template>
  <div class="kivi-booking-widget">
    <div class="kivi-booking-container">
      <!-- Booking Widget Header with Modern Step Indicator -->
      <div class="kivi-booking-header">
        <div class="border-b">
          <div class="flex px-6 py-4 overflow-x-auto justify-center">
            <!-- Clinic Step (Blue) - Hide if clinic is preselected from URL -->
            <div class="flex items-center" v-if="!hideClinicStep">
              <div class="flex items-center" :class="{'text-blue-600': currentStep >= 0, 'text-gray-400': currentStep < 0}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-blue-600 bg-blue-50': currentStep >= 0, 'text-gray-400 bg-gray-50': currentStep < 0}"
                     @click="goToStep(0)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.3-4.3"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-blue-600': currentStep >= 0, 'text-gray-400': currentStep < 0}">Clinic</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Category Step (Purple) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-indigo-600': currentStep >= 1, 'text-gray-400': currentStep < 1}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-indigo-600 bg-indigo-50': currentStep >= 1, 'text-gray-400 bg-gray-50': currentStep < 1}"
                     @click="goToStep(1)">
                  <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <rect x="4" y="4" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                    <rect x="14" y="4" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                    <rect x="4" y="14" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                    <rect x="14" y="14" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-indigo-600': currentStep >= 1, 'text-gray-400': currentStep < 1}">Category</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Service Step (Purple) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-purple-600': currentStep >= 2, 'text-gray-400': currentStep < 2}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-purple-600 bg-purple-50': currentStep >= 2, 'text-gray-400 bg-gray-50': currentStep < 2}"
                     @click="goToStep(2)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-purple-600': currentStep >= 2, 'text-gray-400': currentStep < 2}">Service</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Time Step (Blue) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-blue-600': currentStep >= 3, 'text-gray-400': currentStep < 3}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-blue-600 bg-blue-50': currentStep >= 3, 'text-gray-400 bg-gray-50': currentStep < 3}"
                     @click="goToStep(3)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M8 2v4"></path>
                    <path d="M16 2v4"></path>
                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                    <path d="M3 10h18"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-blue-600': currentStep >= 3, 'text-gray-400': currentStep < 3}">Time</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Details Step (Pink) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-pink-600': currentStep >= 4, 'text-gray-400': currentStep < 4}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-pink-600 bg-pink-50': currentStep >= 4, 'text-gray-400 bg-gray-50': currentStep < 4}"
                     @click="goToStep(4)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M10 9H8"></path>
                    <path d="M16 13H8"></path>
                    <path d="M16 17H8"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-pink-600': currentStep >= 4, 'text-gray-400': currentStep < 4}">Details</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Login Step (Gray) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-gray-600': currentStep >= 5, 'text-gray-400': currentStep < 5}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-gray-600 bg-gray-100': currentStep >= 5, 'text-gray-400 bg-gray-50': currentStep < 5}"
                     @click="goToStep(5)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z"></path>
                    <circle cx="16.5" cy="7.5" r=".5"></circle>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-gray-600': currentStep >= 5, 'text-gray-400': currentStep < 5}">Login</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Confirm Step (Green) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-green-600': currentStep >= 6, 'text-gray-400': currentStep < 6}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-green-600 bg-green-50': currentStep >= 6, 'text-gray-400 bg-gray-50': currentStep < 6}"
                     @click="goToStep(6)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <path d="m9 11 3 3L22 4"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-green-600': currentStep >= 6, 'text-gray-400': currentStep < 6}">Confirm</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Booking Widget Body -->
      <div class="kivi-booking-body">
        <div class="form-card">
          <keep-alive>
            <component
              :is="currentComponent"
              :booking-data="bookingData"
              @update:booking-data="updateBookingData"
              @next-step="nextStep"
              @prev-step="prevStep"
              @go-to-step="goToStep"
              @go-to-login-register="goToLoginStep"
              @time-selected="handleTimeSelected"
              @appointment-booked="handleAppointmentBooked"
              @user-authenticated="handleAuthentication"
              @payment-method-selected="setPaymentMethod"
              @payment-success="handlePaymentSuccess"
              @payment-error="handlePaymentError"
              ref="currentComponent"
            ></component>
          </keep-alive>
        </div>
      </div>

      <!-- Navigation Buttons -->
      <div class="flex justify-between mt-6 pt-6 mx-5 mb-5 border-t" v-if="!isPaymentProcessing">
        <button
          v-if="currentStep > 0"
          @click="prevStep"
          class="px-4 py-2 text-sm text-gray-600 hover:text-gray-900"
          data-step="prev"
        >
          Back
        </button>
        <div v-else class="px-4 py-2"></div> <!-- Empty div to maintain spacing when back button is hidden -->

        <button
          v-if="currentStep < steps.length - 1 || (currentStep === steps.length - 1 && !showSuccessOrErrorScreen)"
          @click="handleNextButtonClick"
          class="px-4 py-2 text-sm text-white rounded-md transition-colors duration-300"
          :class="{
            'bg-pink-600 hover:bg-pink-700': currentStep === 4 || currentStep === 5,
            'bg-green-600 hover:bg-green-700': currentStep === steps.length - 1,
            'bg-blue-600 hover:bg-blue-700': currentStep !== 4 && currentStep !== 5 && currentStep !== steps.length - 1
          }"
          :disabled="(!isCurrentStepValid && currentStep !== 5) || isLoading || processingDetailsStep"
          data-step="next"
        >
          {{ getButtonText() }}
        </button>
      </div>

      <!-- Trust badge -->
      <div class="trust-badge">
        <svg class="shield-icon" viewBox="0 0 24 24">
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"></path>
        </svg>
        <span class="badge-text">Encrypted & HIPAA Compliant</span>
      </div>
    </div>
  </div>
</template>

<script>
import ClinicStep from './Steps/ClinicStep.vue';
import CategoryStep from './Steps/CategoryStep.vue';
import ServiceStep from './Steps/ServiceStep.vue';
import DateTimeStep from './Steps/DateTimeStep.vue';
import AppointmentDetailsStep from './Steps/AppointmentDetailsStep.vue';
import ConfirmationStep from './Steps/ConfirmationStep.vue';
import LoginRegisterStep from './Steps/LoginRegisterStep.vue';
import { get, post } from '../../config/request';

export default {
  name: 'AppointmentBookingWidget',
  components: {
    ClinicStep,
    CategoryStep,
    ServiceStep,
    DateTimeStep,
    AppointmentDetailsStep,
    LoginRegisterStep,
    ConfirmationStep
  },
  props: {
    presetClinicId: {
      type: [String, Number],
      default: 0
    },
    presetCategoryId: {
      type: String,
      default: ''
    },
    presetServiceId: {
      type: [String, Number],
      default: 0
    },
    presetDoctorId: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    // CRITICAL GDPR FIX: Determine initial step based on URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const clinicId = urlParams.get('clinic_id');
    const doctorId = urlParams.get('doctor_id');
    const serviceId = urlParams.get('service_id');

    let initialStep = 0;
    let hideClinicStep = false;

    // If any clinic_id is provided, skip clinic selection entirely
    if (clinicId && !isNaN(clinicId)) {
      hideClinicStep = true;

      // Determine which step to start from based on available parameters
      if (clinicId && doctorId && serviceId) {
        initialStep = 3; // datetime step
      } else if (clinicId && doctorId) {
        initialStep = 2; // services step
      } else if (clinicId) {
        initialStep = 1; // category step
      }
    }

    return {
      currentStep: initialStep,
      steps: [
        { id: 'clinic', label: 'Clinic', component: 'ClinicStep', valid: false },
        { id: 'category', label: 'Category', component: 'CategoryStep', valid: false },
        { id: 'services', label: 'Service', component: 'ServiceStep', valid: false },
        { id: 'datetime', label: 'Time', component: 'DateTimeStep', valid: false },
        { id: 'details', label: 'Details', component: 'AppointmentDetailsStep', valid: false },
        { id: 'login', label: 'Login', component: 'LoginRegisterStep', valid: false },
        { id: 'confirm', label: 'Confirm', component: 'ConfirmationStep', valid: true }
      ],
      bookingData: {
        clinic: null,
        category: null,
        services: [],
        doctor: null,
        date: null,
        time: null,
        description: '',
        patient: {
          name: '',
          email: '',
          phone: '',
          notes: ''
        }
      },
      isLoading: false,
      isPaymentProcessing: false,
      isAuthenticated: false,
      appointmentId: null,
      paymentMethod: null,
      processingDetailsStep: false,
      hideClinicStep: hideClinicStep
    };
  },
  computed: {
    currentComponent() {
      return this.steps[this.currentStep].component;
    },
    isCurrentStepValid() {
      const currentStepData = this.steps[this.currentStep];
      if (!currentStepData) return false;

      switch (currentStepData.id) {
        case 'clinic':
          return this.bookingData.clinic?.id;
        case 'category':
          return this.bookingData.category?.id;
        case 'services':
          return this.bookingData.services?.length > 0;
        case 'datetime':
          return this.bookingData.date && this.bookingData.time;
        case 'details':
          return true; // Details step is always valid
        case 'login':
          return true; // Login step is always valid
        case 'confirm':
          return this.bookingData.clinic &&
                 this.bookingData.services?.length > 0 &&
                 this.bookingData.date &&
                 this.bookingData.time;
        default:
          return false;
      }
    },
    isConfirmationPage() {
      return this.currentStep === this.steps.length - 1;
    },
    showSuccessOrErrorScreen() {
      // Check if we're on the confirmation step and if the current component has success or error screens showing
      if (this.currentStep === this.steps.length - 1 && this.$refs.currentComponent) {
        return this.$refs.currentComponent.showSuccessMessage || this.$refs.currentComponent.showErrorMessage;
      }
      return false;
    },
    startFromCategory() {
      // Start from category step if we have a preset clinic ID
      return this.presetClinicId && this.presetClinicId > 0;
    }
  },
  created() {
    // Handle URL parameters for direct navigation
    this.handleUrlParameters();

    // Initialize with preset values if provided
    this.initializePresetValues();

    // Check if user is already authenticated
    this.checkUserAuthentication();
  },



  mounted() {
    // Remove hash from URL if present
    this.removeHashFromUrl();

    // Only preload clinic if we don't have URL parameters that should take us to datetime step
    // If we have clinic_id, doctor_id, and service_id in URL, we should stay on datetime step
    const urlParams = new URLSearchParams(window.location.search);
    const hasAllUrlParams = urlParams.get('clinic_id') && urlParams.get('doctor_id') && urlParams.get('service_id');

    // If we have a preset clinic ID and startFromCategory is true,
    // but we don't have all URL params, preload the clinic and go to category step
    if (this.presetClinicId && this.startFromCategory && !hasAllUrlParams) {
      console.log('Preloading clinic with ID:', this.presetClinicId);
      this.preloadClinic(this.presetClinicId);
    }

    // Also check URL path for clinic name
    this.checkUrlForClinicName();

    // Listen for the clinic-selected-from-url event
    this.$root.$on('clinic-selected-from-url', this.handleClinicSelectedFromUrl);
  },

  beforeDestroy() {
    // Clean up event listener
    this.$root.$off('clinic-selected-from-url', this.handleClinicSelectedFromUrl);
  },
  methods: {
    getButtonText() {
      // Return appropriate button text based on current step
      if (this.currentStep === this.steps.length - 1) {
        return 'Confirm Appointment';
      } else if (this.currentStep === this.steps.length - 2) {
        return 'Next';
      } else {
        return 'Next';
      }
    },
    handleUrlParameters() {
      const urlParams = new URLSearchParams(window.location.search);
      const clinicId = urlParams.get('clinic_id');
      const doctorId = urlParams.get('doctor_id');
      const serviceId = urlParams.get('service_id');

      // Store URL parameters
      this.bookingData.urlParams = {};

      // Validate and store IDs as numbers
      if (clinicId && !isNaN(clinicId)) {
        this.bookingData.urlParams.clinicId = parseInt(clinicId);
      }
      if (doctorId && !isNaN(doctorId)) {
        this.bookingData.urlParams.doctorId = parseInt(doctorId);
      }
      if (serviceId && !isNaN(serviceId)) {
        this.bookingData.urlParams.serviceId = parseInt(serviceId);
      }
    },

    initializePresetValues() {
      // Process URL parameters for direct navigation if any are available
      if (this.bookingData.urlParams && Object.keys(this.bookingData.urlParams).length > 0) {
        const { clinicId, doctorId, serviceId } = this.bookingData.urlParams;

        // Store the intended step before loading data
        const intendedStep = this.currentStep;

        // Load preset data and mark appropriate steps as valid
        if (clinicId) {
          this.loadPresetData().then(() => {
            // Mark clinic step as valid since we have clinic data
            this.steps[0].valid = true;

            // Mark additional steps as valid based on available data
            if (clinicId && doctorId && serviceId) {
              this.steps[1].valid = true; // category
              this.steps[2].valid = true; // services
              // Ensure we stay on the datetime step if all params are present
              if (intendedStep === 3) {
                this.currentStep = 3;
              }
            } else if (clinicId && doctorId) {
              this.steps[1].valid = true; // category
            }
          });
        }
      }
    },

    async loadPresetData() {
      const { clinicId, doctorId, serviceId } = this.bookingData.urlParams;

      try {
        // Load clinic data
        if (clinicId) {
          await this.fetchClinicDetails(clinicId);
        }

        // Load doctor data
        if (doctorId) {
          await this.fetchDoctorDetails(doctorId);
        }

        // Load service data
        if (serviceId) {
          await this.fetchServiceDetails(serviceId, doctorId, clinicId);
        }
      } catch (error) {
        console.error('Error loading preset data:', error);
      }
    },



    async preloadClinic(clinicId) {
      try {
        this.isLoading = true;

        // Fetch clinic details
        const response = await get('get_clinic_detail', { id: clinicId });

        if (response.data.status) {
          // Handle different response formats
          let clinic;
          if (Array.isArray(response.data.data)) {
            // If it's an array, find the clinic with the matching ID
            clinic = response.data.data.find(c => c.id == clinicId);
          } else if (response.data.data && response.data.data.id) {
            // If it's a single object
            clinic = response.data.data;
          }

          if (clinic) {
            // Set the clinic in booking data
            this.bookingData.clinic = {
              id: clinic.id,
              name: clinic.name,
              address: clinic.address || ''
            };

            // Mark the clinic step as valid
            this.steps[0].valid = true;

            // Skip to category step
            this.goToStep(1);
          }
        }
      } catch (error) {
        console.error('Error preloading clinic:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async fetchClinicDetails(clinicId) {
      try {
        const response = await get('get_clinic_detail', { id: clinicId });

        if (response.data.status && response.data.data) {
          const clinic = Array.isArray(response.data.data)
            ? response.data.data.find(c => c.id == clinicId)
            : response.data.data;

          this.bookingData.clinic = {
            id: clinic?.id || clinicId,
            name: clinic?.name || 'Clinic',
            address: clinic?.address || ''
          };
        } else {
          this.bookingData.clinic = {
            id: clinicId,
            name: 'Clinic',
            address: ''
          };
        }

        this.steps[0].valid = true;
      } catch (error) {
        console.error('Error fetching clinic details:', error);
        this.bookingData.clinic = {
          id: clinicId,
          name: 'Clinic',
          address: ''
        };
        this.steps[0].valid = true;
      }
    },

    async fetchDoctorDetails(doctorId) {
      try {
        const response = await get('get_doctor_detail', { id: doctorId });

        if (response.data.status && response.data.data) {
          const doctor = response.data.data;
          this.bookingData.doctor = {
            id: doctor.id || doctorId,
            name: doctor.display_name || doctor.name || 'Doctor'
          };
        } else {
          this.bookingData.doctor = {
            id: doctorId,
            name: 'Doctor'
          };
        }

        // Update doctor_id in services if we have services
        if (this.bookingData.services && this.bookingData.services.length > 0) {
          this.bookingData.services.forEach(service => {
            if (!service.doctor_id) {
              service.doctor_id = doctorId;
              service.doctor_name = this.bookingData.doctor.name;
            }
          });
        }
      } catch (error) {
        console.error('Error fetching doctor details:', error);
        this.bookingData.doctor = {
          id: doctorId,
          name: 'Doctor'
        };
      }
    },

    async fetchServiceDetails(serviceId, doctorId = null, clinicId = null) {
      try {
        const params = { id: serviceId };
        if (doctorId) params.doctor_id = doctorId;
        if (clinicId) params.clinic_id = clinicId;

        const response = await get('get_service_detail', params);

        if (response.data.status && response.data.data) {
          const service = response.data.data;

          this.bookingData.services = [{
            id: service.id || serviceId,
            service_id: service.id || serviceId,
            name: service.name || 'Service',
            price: service.price || '0',
            duration: service.duration || '30',
            telemed_service: service.telemed_service || 'no',
            doctor_id: service.doctor_id || this.bookingData.urlParams?.doctorId || null,
            doctor_name: service.doctor_name || ''
          }];

          // Set category if available
          if (service.category) {
            this.bookingData.category = {
              id: service.category.id,
              name: service.category.name
            };
          } else {
            this.bookingData.category = {
              id: 1,
              name: 'General'
            };
          }
        } else {
          // Create minimal service object
          this.bookingData.services = [{
            id: serviceId,
            service_id: serviceId,
            name: 'Service',
            price: '0',
            duration: '30',
            telemed_service: 'no',
            doctor_id: this.bookingData.urlParams?.doctorId || null,
            doctor_name: ''
          }];

          this.bookingData.category = {
            id: 1,
            name: 'General'
          };
        }

        this.steps[1].valid = true; // category
        this.steps[2].valid = true; // services
      } catch (error) {
        console.error('Error fetching service details:', error);

        // Create minimal service object on error
        this.bookingData.services = [{
          id: serviceId,
          service_id: serviceId,
          name: 'Service',
          price: '0',
          duration: '30',
          telemed_service: 'no',
          doctor_id: this.bookingData.urlParams?.doctorId || null,
          doctor_name: ''
        }];

        this.bookingData.category = {
          id: 1,
          name: 'General'
        };

        this.steps[1].valid = true; // category
        this.steps[2].valid = true; // services
      }
    },

    updateBookingData(newData) {
      this.bookingData = { ...newData };
    },

    handleClinicSelectedFromUrl(data) {
      // Hide the clinic step
      this.hideClinicStep = true;

      // Make sure the clinic step is marked as valid
      this.steps[0].valid = true;

      // Go to the category step
      this.goToStep(1);

      // If we have a clinic ID, update the booking data
      if (data && data.clinicId) {
        // Update the booking data with the selected clinic
        if (!this.bookingData.clinic || this.bookingData.clinic.id !== data.clinicId) {
          this.bookingData.clinic = {
            id: data.clinicId,
            name: data.clinicName || 'Selected Clinic',
            address: ''
          };
        }
      }
    },

    removeHashFromUrl() {
      // Check if the URL has a hash
      if (window.location.hash) {
        // Get the current URL without the hash
        const urlWithoutHash = window.location.href.split('#')[0];

        // Replace the current URL without the hash
        if (window.history && window.history.replaceState) {
          window.history.replaceState('', document.title, urlWithoutHash);
        }
      }
    },



    checkUrlForClinicName() {
      // Get the full URL (including hash)
      const fullUrl = window.location.href;

      // Extract the path part (before any hash or query string)
      const urlWithoutHash = fullUrl.split('#')[0].split('?')[0];

      // Extract path segments
      const pathSegments = urlWithoutHash.replace(/https?:\/\/[^\/]+/, '').split('/').filter(segment => segment);

      let clinicSlug = null;

      // Check the last segment first
      if (pathSegments.length > 0) {
        const lastSegment = pathSegments[pathSegments.length - 1];

        // If the last segment is not a known endpoint, it might be a clinic name
        if (lastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(lastSegment)) {
          clinicSlug = lastSegment;
        }
        // If we didn't find a clinic name in the last segment and there are at least 2 segments,
        // check the second-to-last segment
        else if (pathSegments.length > 1) {
          const secondToLastSegment = pathSegments[pathSegments.length - 2];
          if (secondToLastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(secondToLastSegment)) {
            clinicSlug = secondToLastSegment;
          }
        }
      }

      // If we found a potential clinic slug, hide the clinic step
      if (clinicSlug) {
        this.hideClinicStep = true;
      }
    },

    async nextStep() {
      if (this.currentStep >= this.steps.length - 1) return;

      const currentStepId = this.steps[this.currentStep].id;

      try {
        // Handle special step logic
        if (currentStepId === 'details') {
          await this.handleDetailsStep();
        } else if (currentStepId === 'login') {
          await this.handleLoginStep();
        } else if (currentStepId === 'confirm') {
          await this.handleConfirmStep();
        } else {
          // Regular step advancement
          this.currentStep++;
          this.scrollToTop();
        }
      } catch (error) {
        console.error('Error in nextStep:', error);
      }
    },

    async handleDetailsStep() {
      if (this.processingDetailsStep) return;

      this.processingDetailsStep = true;

      try {
        const currentComponent = this.$refs.currentComponent;
        if (currentComponent?.submitForm) {
          const isValid = await currentComponent.submitForm();
          if (!isValid) return;
        }

        await this.checkUserAuthentication();

        if (this.isAuthenticated) {
          const success = await this.submitAppointmentData();
          if (success) {
            this.goToStep(6); // Go to confirm step
          }
        } else {
          this.goToStep(5); // Go to login step
        }
      } finally {
        this.processingDetailsStep = false;
      }
    },

    async handleLoginStep() {
      const loginComponent = this.$refs.currentComponent;
      if (loginComponent) {
        if (loginComponent.isLogin) {
          loginComponent.handleLogin();
        } else {
          loginComponent.handleRegister();
        }
      }
    },

    async handleConfirmStep() {
      const confirmComponent = this.$refs.currentComponent;
      if (confirmComponent?.submitAppointment &&
          !confirmComponent.showSuccessMessage &&
          !confirmComponent.showErrorMessage) {
        await confirmComponent.submitAppointment();
      }
    },

    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
        this.scrollToTop();
      }
    },

    goToStep(stepIndex) {
      if (stepIndex >= 0 && stepIndex < this.steps.length) {
        this.currentStep = stepIndex;
        this.scrollToTop();
      }
    },

    goToLoginStep() {
      // Find the index of the login step
      const loginStepIndex = this.steps.findIndex(step => step.id === 'login');
      if (loginStepIndex !== -1) {
        this.goToStep(loginStepIndex);
      } else {
        console.error('Login step not found');
      }
    },

    scrollToTop() {
      // Scroll to the top of the booking widget
      this.$nextTick(() => {
        const element = document.querySelector('.kivi-booking-widget');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    },

    handleTimeSelected(timeData) {
      // Update booking data with selected date and time
      this.bookingData.date = timeData.date;
      this.bookingData.time = timeData.time;
    },

    handleAppointmentBooked(appointmentData) {
      // Store the appointment ID
      this.appointmentId = appointmentData.id ||
                          appointmentData.data?.appointment_id ||
                          appointmentData.appointment_id ||
                          appointmentData.data?.id;

      // Check if we have checkout details for payment
      const checkoutDetail = appointmentData.checkout_detail ||
                            appointmentData.data?.checkout_detail;

      // If payment is required, process payment
      if (this.paymentMethod === 'paymentStripepay' && checkoutDetail) {
        // Store appointment ID in session storage for retrieval after payment
        sessionStorage.setItem('kivicare_appointment_id', this.appointmentId);
        this.isPaymentProcessing = true;

        // Redirect to Stripe checkout
        window.location.href = checkoutDetail.stripe_redirect_url;
      } else {
        // No payment needed or payment at clinic, move to confirmation
        this.goToStep(this.steps.length - 1);
      }
    },

    async handleAuthentication(userData) {
      this.isAuthenticated = true;

      // Update nonces if provided after authentication (WordPress generates new nonces for authenticated users)
      if (userData.token) {
        if (window.ajaxData) {
          if (userData.token.get) window.ajaxData.get_nonce = userData.token.get;
          if (userData.token.post) window.ajaxData.nonce = userData.token.post;
          if (userData.token.post) window.ajaxData.post_nonce = userData.token.post;
        }

        if (window.request_data) {
          if (userData.token.get) window.request_data.get_nonce = userData.token.get;
          if (userData.token.post) window.request_data.nonce = userData.token.post;
        }
      }

      // If we have an appointment ID in session storage, it means we came from payment
      const appointmentId = sessionStorage.getItem('kivicare_appointment_id');
      if (appointmentId) {
        this.updateAppointmentPaymentStatus(appointmentId);
      }

      // Submit appointment data to get confirmation HTML before moving to confirmation step
      await this.submitAppointmentData();

      // Skip login step and move to confirmation step
      const confirmStepIndex = this.steps.findIndex(step => step.id === 'confirm');
      if (confirmStepIndex !== -1) {
        this.goToStep(confirmStepIndex);
      } else {
        this.nextStep();
      }
    },

    async updateAppointmentPaymentStatus(appointmentId) {
      try {
        const response = await post('save_appointment_payment_status', {
          appointment_id: appointmentId,
          payment_status: 'approved'
        });

        if (response.data.status) {
          sessionStorage.removeItem('kivicare_appointment_id');
        }
      } catch (error) {
        console.error('Error updating appointment payment status:', error);
      }
    },

    setPaymentMethod(method) {
      this.paymentMethod = method;
    },

    async submitAppointmentData() {
      try {
        this.isLoading = true;

        // Validate required booking data
        if (!this.bookingData.clinic?.id || !this.bookingData.services?.length ||
            !this.bookingData.date || !this.bookingData.time) {
          throw new Error('Required booking information is missing');
        }

        // Prepare appointment data
        const params = {
          clinic_id: this.bookingData.clinic.id,
          doctor_id: this.bookingData.doctor?.id || '',
          service_list: this.bookingData.services.map(s => s.service_id || s.id),
          time: this.formatTimeToAMPM(this.bookingData.time),
          date: this.bookingData.date,
          description: this.bookingData.description || '',
          file: [],
          custom_field: this.bookingData.customField || {}
          // Note: Don't manually set action, route_name, or _ajax_nonce - let the post helper function handle them
        };

        // Include patient data if available (especially important for newly registered patients)
        if (this.bookingData.patient) {
          params.patient_name = this.bookingData.patient.name;
          params.patient_email = this.bookingData.patient.email;
          params.patient_phone = this.bookingData.patient.phone;
          params.patient_user_id = this.bookingData.patient.user_id;
        }

        // Make API request using the post helper
        const response = await post('appointment_confirm_page', params);

        if (response.data?.status) {
          this.appointmentId = response.data.data?.appointment_id || null;
          this.bookingData.confirmationHtml = response.data.data;
          this.bookingData.taxDetails = response.data.tax_details || [];

          // Emit confirmation page loaded event
          this.$emit('confirmation-page-loaded', {
            html: response.data.data,
            taxDetails: response.data.tax_details || []
          });

          return true;
        } else {
          console.error('Error in API response:', response.data);
          return false;
        }
      } catch (error) {
        console.error('Error submitting appointment data:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    async checkUserAuthentication() {
      try {
        // Check if WordPress has a logged-in user via ajaxData
        if (window.ajaxData?.is_user_logged_in === 'yes') {
          this.isAuthenticated = true;
          return;
        }

        // Try to get user profile
        const response = await get('get_user');
        if (response.data?.status && response.data?.data && Object.keys(response.data.data).length > 0) {
          this.isAuthenticated = true;

          // Update booking data with user information
          const userData = response.data.data;
          this.bookingData.patient = {
            ...this.bookingData.patient,
            name: userData.display_name || userData.user_login || '',
            email: userData.user_email || '',
            phone: userData.mobile_number || '',
            user_id: userData.ID || userData.id || null
          };
          return;
        }

        this.isAuthenticated = false;
      } catch (error) {
        console.error('Error checking user authentication:', error);
        this.isAuthenticated = false;
      }
    },

    handleConfirmationPageLoaded(data) {
      console.log('Confirmation page loaded:', data);

      // Store the confirmation HTML in the booking data
      if (data && data.html) {
        this.bookingData.confirmationHtml = data.html;
        this.bookingData.taxDetails = data.taxDetails || [];
      }
    },

    handlePaymentSuccess() {
      console.log('Payment successful');

      try {
        // Show success message
        alert('Payment successful! Your appointment has been confirmed.');

        // Clear any stored appointment ID
        sessionStorage.removeItem('kivicare_appointment_id');
        sessionStorage.removeItem('kivicare_appointment_in_progress');

        // Update the confirmation component to show success
        const confirmationComponent = this.$refs.currentComponent;
        if (confirmationComponent && typeof confirmationComponent.showSuccessMessage !== 'undefined') {
          confirmationComponent.showSuccessMessage = true;
          confirmationComponent.successMessage = 'Payment successful! Your appointment has been confirmed.';
        }

        // Redirect to the home page or a success page after a delay
        setTimeout(() => {
          window.location.href = window.location.origin;
        }, 3000);
      } catch (error) {
        console.error('Error handling payment success:', error);
      }
    },

    handlePaymentError(errorMessage) {
      console.error('Payment error:', errorMessage);

      try {
        // Show error message
        const message = errorMessage || 'Payment failed. Please try again.';
        alert('Payment failed: ' + message);

        // Update the confirmation component to show error
        const confirmationComponent = this.$refs.currentComponent;
        if (confirmationComponent && typeof confirmationComponent.showErrorMessage !== 'undefined') {
          confirmationComponent.showErrorMessage = true;
          confirmationComponent.errorMessage = message;
        }

        // Clear any stored appointment ID on error
        sessionStorage.removeItem('kivicare_appointment_id');
        sessionStorage.removeItem('kivicare_appointment_in_progress');
      } catch (error) {
        console.error('Error handling payment error:', error);
      }
    },

    handleNextButtonClick() {
      // If we're on the confirmation step, handle it specially
      if (this.steps[this.currentStep].id === 'confirm') {
        const confirmationComponent = this.$refs.currentComponent;

        if (confirmationComponent) {
          // Check if already showing success/error screens
          if (confirmationComponent.showSuccessMessage || confirmationComponent.showErrorMessage) {
            return;
          }

          // Call showPaymentPage or submitAppointment
          if (typeof confirmationComponent.showPaymentPage === 'function') {
            confirmationComponent.showPaymentPage();
            return;
          } else if (typeof confirmationComponent.submitAppointment === 'function') {
            confirmationComponent.submitAppointment();
            return;
          }
        }
      } else {
        // For other steps, just call nextStep
        this.nextStep();
      }
    },

    // Format time to AM/PM format
    formatTimeToAMPM(timeStr) {
      if (!timeStr) return '';

      try {
        // Check if the time is already in AM/PM format
        if (timeStr.toLowerCase().includes('am') || timeStr.toLowerCase().includes('pm')) {
          return timeStr; // Return as is if already in AM/PM format
        }

        const timeParts = timeStr.split(':');
        if (timeParts.length < 2) {
          console.warn('Invalid time format:', timeStr);
          return timeStr; // Return original if format is invalid
        }

        const hours = parseInt(timeParts[0]);
        const minutes = timeParts[1] || '00';

        if (isNaN(hours) || hours < 0 || hours > 23) {
          console.warn('Invalid hour value:', hours);
          return timeStr; // Return original if hour is invalid
        }

        const ampm = hours >= 12 ? 'pm' : 'am';
        const hour12 = hours % 12 || 12;

        return `${hour12}:${minutes} ${ampm}`;
      } catch (error) {
        console.error('Error formatting time:', error);
        return timeStr; // Return original time on error
      }
    },

    // Validate booking data before submission
    validateBookingData() {
      const errors = [];

      if (!this.bookingData.clinic || !this.bookingData.clinic.id) {
        errors.push('Please select a clinic');
      }

      if (!this.bookingData.services || this.bookingData.services.length === 0) {
        errors.push('Please select at least one service');
      }

      if (!this.bookingData.date) {
        errors.push('Please select an appointment date');
      }

      if (!this.bookingData.time) {
        errors.push('Please select an appointment time');
      }

      if (errors.length > 0) {
        console.error('Booking validation errors:', errors);
        alert('Please complete the following:\n• ' + errors.join('\n• '));
        return false;
      }

      return true;
    },

    // Reset component state
    resetComponentState() {
      this.isLoading = false;
      this.processingDetailsStep = false;
      this.isPaymentProcessing = false;
      this.appointmentId = null;
      this.paymentMethod = null;
    }
  }
};
</script>

<style scoped>
.kivi-booking-widget {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  max-width: 1000px;
  margin: 0 auto;
  color: #333;
  --primary-color: #7093e5;
  --primary-color-dark: #4367b9;
  --secondary-color: #f68685;
  --secondary-color-dark: #df504e;
  --black: #333;
  --dark-gray: #4b5563;
  --gray: #6b7280;
  --light-gray: #f9fafb;
  --white: #fff;
  --radius: 8px;
}

.kivi-booking-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.kivi-booking-header {
  background-color: #fff;
}

/* Tailwind-like utility classes for the new header */
.border-b {
  border-bottom: 1px solid #e5e7eb;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.overflow-x-auto {
  overflow-x: auto;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.text-blue-600 {
  color: #2563eb;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-purple-600 {
  color: #9333ea;
}

.text-pink-600 {
  color: #db2777;
}

.text-green-600 {
  color: #16a34a;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-300 {
  color: #d1d5db;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-indigo-50 {
  background-color: #eef2ff;
}

.bg-purple-50 {
  background-color: #faf5ff;
}

.bg-pink-50 {
  background-color: #fdf2f8;
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.text-sm {
  font-size: 0.875rem;
}

.font-medium {
  font-weight: 500;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.hidden {
  display: none;
}

@media (min-width: 768px) {
  .md\:inline-block {
    display: inline-block;
  }
}

.progress-container {
  position: relative;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  margin-top: 0.5rem;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-color, #7093e5);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-indicator {
  position: absolute;
  top: -20px;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #6b7280;
  transition: left 0.3s ease;
}

.kivi-booking-body {
  padding: 1.5rem;
}

.form-card {
  min-height: 300px;
}

.trust-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Additional Tailwind-like utility classes for the footer */
.justify-between {
  justify-content: space-between;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.border-t {
  border-top: 1px solid #e5e7eb;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.text-gray-600 {
  color: #4b5563;
}

.hover\:text-gray-900:hover {
  color: #111827;
}

.text-white {
  color: white;
}

.rounded-md {
  border-radius: 0.375rem;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.bg-pink-600 {
  background-color: #db2777;
}

.hover\:bg-pink-700:hover {
  background-color: #be185d;
}

.shield-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  color: #6b7280;
}

.badge-text {
  font-weight: 500;
}

@media (max-width: 768px) {
  .kivi-booking-body {
    padding: 1rem;
  }

  /* Responsive adjustments for the new header */
  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 640px) {
  /* Responsive adjustments for the new footer */
  .flex.justify-between.mt-6.pt-6.mx-5.mb-5.border-t {
    flex-direction: column;
    gap: 1rem;
  }

  .flex.justify-between.mt-6.pt-6.mx-5.mb-5.border-t button {
    width: 100%;
  }

  /* Responsive adjustments for the new header */
  .px-6 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .w-8 {
    width: 1.75rem;
  }

  .h-8 {
    height: 1.75rem;
  }

  .mx-3 {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
}
</style>
