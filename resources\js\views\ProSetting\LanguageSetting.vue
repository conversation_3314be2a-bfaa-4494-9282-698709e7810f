<template>
  <div class="w-full">
    <div class="flex flex-col">
      <div class="w-full">
        <form id="clinicForm" @submit.prevent="langView" novalidate>
          <div class="bg-white rounded-lg shadow-lg">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200">
              <div
                class="flex flex-col md:flex-row justify-between items-center"
              >
                <h3 class="text-xl font-semibold mb-4 md:mb-0">
                  {{ formTranslation.pro_setting.custom_langauge_translation }}
                </h3>
                <button
                  type="button"
                  @click="$router.go(-1)"
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg
                    class="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  {{ formTranslation.common.back }}
                </button>
              </div>
            </div>

            <!-- Loco Translate Toggle -->
            <div class="p-6">
              <div class="mb-6">
                <h4 class="text-lg font-medium mb-2">
                  {{ formTranslation.common.loco_translate }}
                </h4>
                <!-- Toggle button -->
                <toggle-switch
                  :value="locoTranslateOn"
                  :on-value="true"
                  :off-value="false"
                  @input="(value) => (locoTranslateOn = value)"
                  @change="locoTranslate"
                >
                  <template v-slot:default>
                    <span class="ml-3 text-sm font-medium text-gray-900">
                      {{ locoTranslateOn ? "Yes" : "No" }}
                    </span>
                  </template>
                </toggle-switch>
              </div>

              <!-- Language Selection Section -->
              <div v-if="!locoTranslateOn">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <!-- Set Language -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ formTranslation.pro_setting.set_language }}
                    </label>
                    <multi-select
                      class="w-full"
                      deselect-label=""
                      select-label=""
                      @select="langChange"
                      v-model="selectedLang"
                      :tag-placeholder="formTranslation.settings.tag_plh_option"
                      id="lang_id"
                      :placeholder="formTranslation.settings.plh_search"
                      label="label"
                      track-by="id"
                      :options="langsOption"
                    />
                  </div>

                  <!-- Add New Language -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ formTranslation.pro_setting.add_new_langauge }}
                      <span class="text-red-500">*</span>
                    </label>
                    <multi-select
                      class="w-full"
                      deselect-label=""
                      select-label=""
                      v-model="lang"
                      id="language"
                      label="title"
                      track-by="id"
                      @select="onLanguageChange"
                      :options="language"
                    />
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex items-end">
                    <button
                      type="button"
                      @click="saveLanguage"
                      :disabled="isTranslating"
                      class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2 disabled:opacity-50"
                    >
                      {{
                        isTranslating
                          ? formTranslation.common.loading
                          : formTranslation.pro_setting.translate
                      }}
                      <div
                        v-if="isTranslating"
                        class="ml-2 animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"
                      ></div>
                    </button>
                    <button
                      type="button"
                      @click="languageFilters"
                      class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Language filters
                    </button>
                  </div>
                </div>

                <!-- Language Filters -->
                <div v-if="filters" class="mb-6">
                  <div class="flex flex-wrap gap-2">
                    <a
                      v-for="(json, key) in jsonData['en']"
                      :key="key"
                      :href="'#' + key"
                      class="inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      {{ key }}
                    </a>
                  </div>
                </div>

                <!-- Translation Form -->
                <div v-if="show">
                  <form
                    id="langSave"
                    @submit.prevent="saveLanguage"
                    class="w-full"
                  >
                    <div class="relative">
                      <!-- Loading Overlay -->
                      <div
                        v-if="isLanguageLoading"
                        class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"
                      >
                        <div
                          class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"
                        ></div>
                      </div>

                      <!-- Translation Content -->
                      <div class="bg-white rounded-lg shadow-lg">
                        <div
                          class="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200"
                        >
                          <div class="col-span-3">
                            <h3 class="text-blue-600 font-semibold">
                              {{ formTranslation.common.key }}
                            </h3>
                          </div>
                          <div class="col-span-6">
                            <h3 class="text-blue-600 font-semibold">
                              {{ formTranslation.common.english }}
                            </h3>
                          </div>
                          <div class="col-span-3">
                            <h3 class="text-blue-600 font-semibold">
                              {{ lang.title }}
                            </h3>
                          </div>
                        </div>

                        <div class="divide-y divide-gray-200">
                          <div
                            v-for="(json, key) in jsonData['en']"
                            :key="key"
                            class="w-full"
                          >
                            <div
                              :id="key"
                              class="w-full p-4 text-center bg-blue-50 border-b border-blue-200"
                            >
                              <span class="font-semibold text-blue-600">{{
                                key
                              }}</span>
                            </div>

                            <div
                              v-for="(json1, key1) in Object.keys(json)"
                              :key="key1"
                              class="grid grid-cols-12 gap-4 p-4 hover:bg-gray-50"
                            >
                              <div class="col-span-3">
                                <span class="text-sm text-gray-600">{{
                                  json1
                                }}</span>
                              </div>
                              <div class="col-span-4">
                                <span class="text-sm">{{ json[json1] }}</span>
                              </div>
                              <div class="col-span-5">
                                <input
                                  type="text"
                                  v-model="jsonData[lang.id][key][json1]"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>

          <!-- Save Button -->
          <div class="flex justify-end mt-6">
            <button
              type="submit"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ formTranslation.common.save }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
<script>
import { post } from "../../config/request";
export default {
  name: "LanguageSetting",
  data: () => {
    return {
      locoTranslateOn: false,
      langsOption: [],
      show: false,
      lang: {
        title: "English",
        id: "en",
      },
      selectedLang: {
        label: "English",
        id: "en",
      },
      langTitle: "",
      jsonData: [],
      showNote: false,
      data: [],
      isLanguageLoading: true,
      isTranslating: false,
      filters: false,
      url: window.request_data.kiviCarePluginURL + "resources/js/lang/en.json",
      locationUrl: "",
      language: [
        { title: "Abkhaz", id: "ab" },
        { title: "Afar", id: "aa" },
        { title: "Afrikaans", id: "af" },
        { title: "Akan", id: "ak" },
        { title: "Albanian", id: "sq" },
        { title: "Amharic", id: "am" },
        { title: "Arabic", id: "ar" },
        { title: "Aragonese", id: "an" },
        { title: "Armenian", id: "hy" },
        { title: "Assamese", id: "as" },
        { title: "Avaric", id: "av" },
        { title: "Avestan", id: "ae" },
        { title: "Aymara", id: "ay" },
        { title: "Azerbaijani", id: "az" },
        { title: "Bambara", id: "bm" },
        { title: "Bashkir", id: "ba" },
        { title: "Basque", id: "eu" },
        { title: "Belarusian", id: "be" },
        { title: "Bengali", id: "bn" },
        { title: "Bihari", id: "bh" },
        { title: "Bislama", id: "bi" },
        { title: "Bosnian", id: "bs" },
        { title: "Breton", id: "br" },
        { title: "Bulgarian", id: "bg" },
        { title: "Burmese", id: "my" },
        { title: "Catalan; Valencian", id: "ca" },
        { title: "Chamorro", id: "ch" },
        { title: "Chechen", id: "ce" },
        { title: "Chichewa; Chewa; Nyanja", id: "ny" },
        { title: "Chinese", id: "zh" },
        { title: "Chuvash", id: "cv" },
        { title: "Cornish", id: "kw" },
        { title: "Corsican", id: "co" },
        { title: "Cree", id: "cr" },
        { title: "Croatian", id: "hr" },
        { title: "Czech", id: "cs" },
        { title: "Danish", id: "da" },
        { title: "Divehi; Dhivehi; Maldivian;", id: "dv" },
        { title: "Dutch", id: "nl" },
        { title: "English", id: "en" },
        { title: "Esperanto", id: "eo" },
        { title: "Estonian", id: "et" },
        { title: "Ewe", id: "ee" },
        { title: "Faroese", id: "fo" },
        { title: "Fijian", id: "fj" },
        { title: "Finnish", id: "fi" },
        { title: "French", id: "fr" },
        { title: "Fula; Fulah; Pulaar; Pular", id: "ff" },
        { title: "Galician", id: "gl" },
        { title: "Georgian", id: "ka" },
        { title: "German", id: "de" },
        { title: "Greek, Modern", id: "el" },
        { title: "Guaraní", id: "gn" },
        { title: "Gujarati", id: "gu" },
        { title: "Haitian; Haitian Creole", id: "ht" },
        { title: "Hausa", id: "ha" },
        { title: "Hebrew (modern)", id: "he" },
        { title: "Herero", id: "hz" },
        { title: "Hindi", id: "hi" },
        { title: "Hiri Motu", id: "ho" },
        { title: "Hungarian", id: "hu" },
        { title: "Interlingua", id: "ia" },
        { title: "Indonesian", id: "id" },
        { title: "Interlingue", id: "ie" },
        { title: "Irish", id: "ga" },
        { title: "Igbo", id: "ig" },
        { title: "Inupiaq", id: "ik" },
        { title: "Ido", id: "io" },
        { title: "Icelandic", id: "is" },
        { title: "Italian", id: "it" },
        { title: "Inuktitut", id: "iu" },
        { title: "Japanese", id: "ja" },
        { title: "Javanese", id: "jv" },
        { title: "Kalaallisut, Greenlandic", id: "kl" },
        { title: "Kannada", id: "kn" },
        { title: "Kanuri", id: "kr" },
        { title: "Kashmiri", id: "ks" },
        { title: "Kazakh", id: "kk" },
        { title: "Khmer", id: "km" },
        { title: "Kikuyu, Gikuyu", id: "ki" },
        { title: "Kinyarwanda", id: "rw" },
        { title: "Kirghiz, Kyrgyz", id: "ky" },
        { title: "Komi", id: "kv" },
        { title: "Kongo", id: "kg" },
        { title: "Korean", id: "ko" },
        { title: "Kurdish", id: "ku" },
        { title: "Kwanyama, Kuanyama", id: "kj" },
        { title: "Latin", id: "la" },
        { title: "Luxembourgish, Letzeburgesch", id: "lb" },
        { title: "Luganda", id: "lg" },
        { title: "Limburgish, Limburgan, Limburger", id: "li" },
        { title: "Lingala", id: "ln" },
        { title: "Lao", id: "lo" },
        { title: "Lithuanian", id: "lt" },
        { title: "Luba-Katanga", id: "lu" },
        { title: "Latvian", id: "lv" },
        { title: "Manx", id: "gv" },
        { title: "Macedonian", id: "mk" },
        { title: "Malagasy", id: "mg" },
        { title: "Malay", id: "ms" },
        { title: "Malayalam", id: "ml" },
        { title: "Maltese", id: "mt" },
        { title: "Māori", id: "mi" },
        { title: "Marathi (Marāṭhī)", id: "mr" },
        { title: "Marshallese", id: "mh" },
        { title: "Mongolian", id: "mn" },
        { title: "Nauru", id: "na" },
        { title: "Navajo, Navaho", id: "nv" },
        { title: "Norwegian Bokmål", id: "nb" },
        { title: "North Ndebele", id: "nd" },
        { title: "Nepali", id: "ne" },
        { title: "Ndonga", id: "ng" },
        { title: "Norwegian Nynorsk", id: "nn" },
        { title: "Norwegian", id: "no" },
        { title: "Nuosu", id: "ii" },
        { title: "South Ndebele", id: "nr" },
        { title: "Occitan", id: "oc" },
        { title: "Ojibwe, Ojibwa", id: "oj" },
        { title: "Oromo", id: "om" },
        { title: "Oriya", id: "or" },
        { title: "Ossetian, Ossetic", id: "os" },
        { title: "Panjabi, Punjabi", id: "pa" },
        { title: "Pāli", id: "pi" },
        { title: "Persian", id: "fa" },
        { title: "Polish", id: "pl" },
        { title: "Pashto, Pushto", id: "ps" },
        { title: "Portuguese", id: "pt" },
        { title: "Quechua", id: "qu" },
        { title: "Romansh", id: "rm" },
        { title: "Kirundi", id: "rn" },
        { title: "Romanian, Moldavian, Moldovan", id: "ro" },
        { title: "Russian", id: "ru" },
        { title: "Sanskrit (Saṁskṛta)", id: "sa" },
        { title: "Sardinian", id: "sc" },
        { title: "Sindhi", id: "sd" },
        { title: "Northern Sami", id: "se" },
        { title: "Samoan", id: "sm" },
        { title: "Sango", id: "sg" },
        { title: "Serbian", id: "sr" },
        { title: "Scottish Gaelic; Gaelic", id: "gd" },
        { title: "Shona", id: "sn" },
        { title: "Sinhala, Sinhalese", id: "si" },
        { title: "Slovak", id: "sk" },
        { title: "Slovene", id: "sl" },
        { title: "Somali", id: "so" },
        { title: "Southern Sotho", id: "st" },
        { title: "Spanish; Castilian", id: "es" },
        { title: "Sundanese", id: "su" },
        { title: "Swahili", id: "sw" },
        { title: "Swati", id: "ss" },
        { title: "Swedish", id: "sv" },
        { title: "Tamil", id: "ta" },
        { title: "Telugu", id: "te" },
        { title: "Tajik", id: "tg" },
        { title: "Thai", id: "th" },
        { title: "Tigrinya", id: "ti" },
        { title: "Tibetan Standard, Tibetan, Central", id: "bo" },
        { title: "Turkmen", id: "tk" },
        { title: "Tagalog", id: "tl" },
        { title: "Tswana", id: "tn" },
        { title: "Tonga (Tonga Islands)", id: "to" },
        { title: "Turkish", id: "tr" },
        { title: "Tsonga", id: "ts" },
        { title: "Tatar", id: "tt" },
        { title: "Twi", id: "tw" },
        { title: "Tahitian", id: "ty" },
        { title: "Uighur, Uyghur", id: "ug" },
        { title: "Ukrainian", id: "uk" },
        { title: "Urdu", id: "ur" },
        { title: "Uzbek", id: "uz" },
        { title: "Venda", id: "ve" },
        { title: "Vietnamese", id: "vi" },
        { title: "Volapük", id: "vo" },
        { title: "Walloon", id: "wa" },
        { title: "Welsh", id: "cy" },
        { title: "Wolof", id: "wo" },
        { title: "Western Frisian", id: "fy" },
        { title: "Xhosa", id: "xh" },
        { title: "Yiddish", id: "yi" },
        { title: "Yoruba", id: "yo" },
        { title: "Zhuang, Chuang", id: "za" },
      ],
    };
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.selectedLang = this.getLang;
    this.locationUrl = window.location.href;
    if (this.getLang.id == undefined) {
      this.lang.id = this.lang.id;
      this.lang.title = this.lang.title;
    } else {
      this.lang.id = this.getLang.id;
      this.lang.title = this.getLang.label;
      this.show = true;
    }
    this.show = true;
    this.getJsonFile();
    this.getAllLangOption();
    this.getTranslate();
  },
  methods: {
    getAllLangOption() {
      post("get_all_lang_option", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.langsOption = response.data.data.lang_option;
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          this.loading = false;
          this.submitted = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    langChange(lang) {
      var loginUser = this.$store.state.userDataModule.user;
      this.$i18n.locale = lang.id;
      post("update_language", { user_id: loginUser.ID, lang: lang })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            this.selectedLang = response.data.data;
            window.location.reload();
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    languageFilters() {
      this.filters = !this.filters;
    },
    langView() {
      this.langTitle = this.lang.title;
      this.show = true;
      this.getJsonFile();
    },
    getJsonFile() {
      this.isLanguageLoading = true;
      post("get_json_file", {
        filePath: window.request_data.kiviCarePluginURL,
        current: this.lang.id,
      })
        .then((response) => {
          this.isLanguageLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.jsonData = response.data.data;
          }
        })
        .catch((error) => {
          this.isLanguageLoading = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    hide() {
      this.showNote = false;
    },
    showWarning() {
      this.showNote = true;
    },
    saveLanguage() {
      this.isTranslating = true;
      post("save_json_data", {
        data: this.jsonData[this.lang.id],
        file_name: this.lang.id,
        langTitle: this.lang.title,
      })
        .then((response) => {
          this.isTranslating = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            this.$store.dispatch("userDataModule/fetchUserData", {});
            this.$i18n.locale = this.lang.id;
            window.location.href = this.locationUrl;
            window.location.reload();
          }
        })
        .catch((error) => {
          this.isTranslating = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    onLanguageChange(val) {
      this.lang = val;
      let english = JSON.parse(JSON.stringify(this.jsonData["en"]));
      this.jsonData[val.id] = english;
      this.getJsonFile();
    },
    locoTranslate() {
      var state = this.locoTranslateOn;
      post("save_loco_translate", { locoState: state })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            // this.locoTranslateOn = response.data.data;
            displayMessage(response.data.message);
            window.location.reload();
            // this.$store.dispatch("userDataModule/fetchUserData", {});
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getTranslate() {
      var state = this.locoTranslateOn;
      post("get_loco_translate", { locoState: 0 })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.locoTranslateOn = response.data.data;
            // this.$store.dispatch("userDataModule/fetchUserData", {});
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    getLang() {
      return this.userData.get_lang;
    },
  },
  watch: {
    lang(val) {},
  },
};
</script>

<style scoped>
.tanslation-key-scroll {
  width: 100%;
  overflow-y: scroll;
  height: 500px;
}
</style>
