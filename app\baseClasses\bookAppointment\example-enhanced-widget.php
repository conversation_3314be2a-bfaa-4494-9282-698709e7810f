<?php
/**
 * KiviCare Enhanced Booking Widget Example
 * 
 * This file demonstrates how to use the enhanced booking widget with Vue.js and Tailwind CSS.
 * You can include this functionality in your WordPress pages using shortcodes.
 */
?>

<h2>KiviCare Enhanced Booking Widget</h2>

<p>To use the enhanced booking widget with the new flow (Clinic → Category → Services → Date/Time → Patient Details → Confirmation), 
you can add the following shortcode to any WordPress page or post:</p>

<pre><code>[kivicareEnhancedBooking]</code></pre>

<p>This will display the booking widget with all steps. Users will be able to select a clinic, category, services, date and time, 
and provide their information to book an appointment.</p>

<h3>Shortcode Parameters</h3>

<p>You can pre-select certain options by passing parameters to the shortcode:</p>

<ul>
    <li><strong>clinic_id</strong>: Pre-select a specific clinic (e.g., clinic_id="2")</li>
    <li><strong>category</strong>: Pre-select a service category (e.g., category="general_service")</li>
    <li><strong>service_id</strong>: Pre-select a specific service (e.g., service_id="5")</li>
</ul>

<h4>Example with Parameters:</h4>

<pre><code>[kivicareEnhancedBooking clinic_id="2" category="general_service" service_id="5"]</code></pre>

<p>In this example, the widget will start with Clinic ID 2, the "General Service" category, and Service ID 5 pre-selected.</p>

<h3>Live Example:</h3>

<div class="kivicare-example-widget">
    <?php 
    // Only output the live example if this file is included directly
    if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
        echo do_shortcode('[kivicareEnhancedBooking]');
    }
    ?>
</div>

<h3>Customization Options</h3>

<p>The enhanced booking widget can be customized in several ways:</p>

<ol>
    <li>
        <strong>CSS Customization</strong>: The widget uses CSS variables for colors, fonts, and other properties. 
        You can override these variables in your theme's CSS to match your site's design.
    </li>
    <li>
        <strong>JavaScript Hooks</strong>: The widget provides events that you can listen to and respond to from your custom JavaScript code.
    </li>
    <li>
        <strong>PHP Filters</strong>: The widget exposes filters that let you modify the data being displayed or processed.
    </li>
</ol>

<p>For more customization options, please refer to the KiviCare documentation or contact support.</p>