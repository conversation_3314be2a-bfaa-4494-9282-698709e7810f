<!-- PatientDocuments.vue -->
<template>
  <div class="space-y-6">
    <!-- New Document Button -->
    <div class="flex justify-end">
      <button
        @click="handleUploadClick()"
        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center space-x-2"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="w-5 h-5"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="17 8 12 3 7 8"></polyline>
          <line x1="12" x2="12" y1="3" y2="15"></line>
        </svg>
        <span>Upload Document</span>
      </button>
    </div>
    <!-- Identification Documents -->
    <div class="bg-white text-card-foreground rounded-xl border shadow">
      <div class="flex flex-col space-y-1.5 p-6">
        <h3 class="font-semibold leading-none tracking-tight">
          Identification Documents
        </h3>
      </div>
      <div class="p-6 pt-0">
        <div class="space-y-4">
          <div
            v-for="doc in identificationDocs"
            :key="doc.id"
            class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
          >
            <div>
              <h4 class="font-semibold">{{ doc.name }}</h4>
              <p class="text-sm text-gray-600">
                Created: {{ formatDate(doc.created_at) }}
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <button
                @click="handleDownload(doc)"
                class="p-2 text-gray-600 hover:bg-gray-200 rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="w-4 h-4"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" x2="12" y1="15" y2="3"></line>
                </svg>
              </button>
              <button
                @click="handleUploadClick(doc)"
                class="p-2 text-gray-600 hover:bg-gray-200 rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="w-4 h-4"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" x2="12" y1="3" y2="15"></line>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Other Document Types -->
    <div
      v-for="(docs, type) in groupedDocuments"
      :key="type"
      class="bg-white text-card-foreground rounded-xl border shadow"
    >
      <div class="flex flex-col space-y-1.5 p-6">
        <h3 class="font-semibold leading-none tracking-tight">
          {{ formatDocumentType(type) }}
        </h3>
      </div>
      <div class="p-6 pt-0">
        <div class="space-y-4">
          <div
            v-for="doc in docs"
            :key="doc.id"
            class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
          >
            <div>
              <h4 class="font-semibold">{{ doc.name }}</h4>
              <p class="text-sm text-gray-600">
                Created: {{ formatDate(doc.created_at) }}
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <button
                @click="handleDownload(doc)"
                class="p-2 text-gray-600 hover:bg-gray-200 rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="w-4 h-4"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" x2="12" y1="15" y2="3"></line>
                </svg>
              </button>
              <button
                @click="handleUploadClick(doc)"
                class="p-2 text-gray-600 hover:bg-gray-200 rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="w-4 h-4"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" x2="12" y1="3" y2="15"></line>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Modal -->
    <UploadModal
      v-if="showUploadDialog"
      :show="showUploadDialog"
      :encounter-id="selectedDocument?.encounter_id || ''"
      :patient-details="{
        patient_id: patientData.ID,
        ...patientData,
      }"
      :editing-document="selectedDocument"
      @close="closeUploadModal"
      @upload="handleUploadSuccess"
    />
  </div>
</template>

<script>
import { get } from "../../config/request";
import { displayErrorMessage } from "../../utils/message";
import UploadModal from "../NewAppointment/UploadModal";

export default {
  name: "PatientDocuments",

  components: {
    UploadModal,
  },

  props: {
    patientData: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      documents: [],
      loading: false,
      showUploadDialog: false,
      selectedDocument: null,
    };
  },

  computed: {
    identificationDocs() {
      return (
        this.documents.filter((doc) => doc.type === "identity_document") || []
      );
    },
    groupedDocuments() {
      return this.documents.reduce((acc, doc) => {
        if (doc.type !== "identity_document") {
          if (!acc[doc.type]) {
            acc[doc.type] = [];
          }
          acc[doc.type].push(doc);
        }
        return acc;
      }, {});
    },
  },

  mounted() {
    this.loadDocuments();
  },

  methods: {
    formatDate(date) {
      if (!date) return "N/A";
      return date; // API already returns formatted date
    },

    formatDocumentType(type) {
      return type
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    },

    async loadDocuments() {
      this.loading = true;
      try {
        const response = await get("get_patient_document", {
          patient_id: this.patientData.ID,
        });
        if (response?.data?.status) {
          this.documents = response.data.data || [];
        } else {
          throw new Error(
            response?.data?.message || "Failed to load documents"
          );
        }
      } catch (error) {
        displayErrorMessage("Failed to load documents");
      } finally {
        this.loading = false;
      }
    },

    handleUploadClick(doc = null) {
      this.selectedDocument = doc;
      this.showUploadDialog = true;
    },

    closeUploadModal() {
      this.showUploadDialog = false;
      this.selectedDocument = null;
    },

    async handleUploadSuccess() {
      await this.loadDocuments();
      this.closeUploadModal();
    },

    async handleDownload(doc) {
      try {
        if (!doc.document_url) {
          const response = await get("view_patient_document", {
            document_id: doc.id,
          });

          if (response?.data?.status && response.data.document_url) {
            this.downloadFile(response.data.document_url, doc.name);
          } else {
            throw new Error("Download URL not available");
          }
        } else {
          this.downloadFile(doc.document_url, doc.name);
        }
      } catch (error) {
        displayErrorMessage("Failed to download document");
      }
    },

    downloadFile(url, filename) {
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
  },
};
</script>
