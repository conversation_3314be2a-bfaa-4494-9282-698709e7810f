<!-- SessionModal.vue -->
<template>
  <div>
    <!-- <PERSON><PERSON> Backdrop -->
    <div
      v-if="showAddEditSessionModal"
      class="fixed inset-0 bg-black backdrop-blur-sm transition-opacity opacity-50 duration-300 z-40"
      @click="closeModal"
    ></div>

    <!-- Modal Container -->
    <div
      v-if="showAddEditSessionModal"
      class="fixed inset-0 z-50 overflow-y-auto"
      role="dialog"
      aria-modal="true"
    >
      <div class="flex min-h-screen items-center justify-center p-4">
        <div
          class="bg-white rounded-xl shadow-2xl w-full max-w-6xl transform transition-all duration-300 ease-out"
        >
          <!-- Modal Header -->
          <div
            class="flex justify-between items-center p-6 border-b border-gray-100"
          >
            <h2
              class="text-2xl font-semibold text-gray-800 flex items-center gap-3"
            >
              <span
                class="w-8 h-8 flex items-center justify-center bg-purple-100 rounded-lg"
              >
                <i class="fas fa-calendar-alt text-purple-600"></i>
              </span>
              {{
                isEdit
                  ? formTranslation.doctor_session.edit_session
                  : formTranslation.doctor_session.add_session_btn
              }}
            </h2>
            <button
              @click="closeModal"
              class="w-8 h-8 flex items-center justify-center rounded-lg text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors duration-200"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="p-6 space-y-6">
            <form @submit.prevent="handleSubmit">
              <!-- Form Grid -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Clinic Selection -->
                <div
                  v-if="
                    userData.addOns.kiviPro === true &&
                    (getUserRole() === 'administrator' ||
                      getUserRole() == 'doctor')
                  "
                  class="space-y-2"
                >
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.clinic.select_clinic }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="formData.clinic_id"
                      @change="handleClinicChange"
                      class="w-full pl-3 pr-10 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-colors duration-200"
                      :class="{
                        'border-red-500 focus:ring-red-400':
                          submitted && !$v.formData.clinic_id.required,
                      }"
                    >
                      <option value="">
                        {{ formTranslation.doctor_session.plh_search }}
                      </option>
                      <option
                        v-for="clinic in allClinics"
                        :key="clinic.id"
                        :value="clinic.id"
                      >
                        {{ clinic.label }}
                      </option>
                    </select>
                    <div
                      class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                    >
                      <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                  </div>
                  <div
                    v-if="submitted && !$v.formData.clinic_id.required"
                    class="text-red-500 text-sm mt-1 flex items-center gap-2"
                  >
                    <i class="fas fa-exclamation-circle"></i>
                    {{ formTranslation.common.clinic_is_required }}
                  </div>
                </div>

                <!-- Doctor Selection -->
                <div v-if="getUserRole() !== 'doctor'" class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.doctors }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="formData.doctors"
                      @change="handleDoctorChange"
                      class="w-full pl-3 pr-10 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-colors duration-200"
                      :class="{
                        'border-red-500 focus:ring-red-400':
                          submitted && !$v.formData.doctors.required,
                      }"
                    >
                      <option value="">
                        {{ formTranslation.doctor_session.plh_search }}
                      </option>
                      <option
                        v-for="doctor in doctors"
                        :key="doctor.id"
                        :value="doctor"
                      >
                        {{ doctor.label }}
                      </option>
                    </select>
                    <div
                      class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                    >
                      <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                  </div>
                  <div
                    v-if="submitted && !$v.formData.doctors.required"
                    class="text-red-500 text-sm mt-1 flex items-center gap-2"
                  >
                    <i class="fas fa-exclamation-circle"></i>
                    {{ formTranslation.appointments.doc_required }}
                  </div>
                </div>

                <!-- Time Slot Selection -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.setup_wizard.time_slot_minute }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="formData.buffertime"
                      class="w-full pl-3 pr-10 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-colors duration-200"
                      :class="{
                        'border-red-500 focus:ring-red-400':
                          submitted && !$v.formData.buffertime.required,
                      }"
                    >
                      <option
                        v-for="(slot, index) in timeSlots"
                        :key="index"
                        :value="slot"
                      >
                        {{ slot }}
                      </option>
                    </select>
                    <div
                      class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                    >
                      <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                  </div>
                  <div
                    v-if="submitted && !$v.formData.buffertime.required"
                    class="text-red-500 text-sm mt-1 flex items-center gap-2"
                  >
                    <i class="fas fa-exclamation-circle"></i>
                    {{ formTranslation.appointments.time_slot_required }}
                  </div>
                </div>
              </div>

              <!-- Weekly Schedule Table -->
              <div class="mt-8">
                <div class="border border-gray-200 rounded-lg overflow-x-auto">
                  <table class="w-full border-collapse bg-white">
                    <thead>
                      <tr>
                        <th
                          v-for="(day, index) in formData.days"
                          :key="index"
                          class="border-b border-gray-200 bg-gray-50 p-4"
                        >
                          <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-700">{{
                              day.label
                            }}</span>
                            <div class="flex gap-2">
                              <button
                                v-if="index !== 0"
                                type="button"
                                @click="copyFromPreviousDay(index)"
                                class="p-1.5 hover:bg-gray-100 rounded-lg text-gray-600 transition-colors duration-200"
                                title="Copy from previous day"
                              >
                                <i class="far fa-copy"></i>
                              </button>
                              <button
                                type="button"
                                @click="addSlot(index)"
                                class="p-1.5 hover:bg-gray-100 rounded-lg text-gray-600 transition-colors duration-200"
                                title="Add time slot"
                              >
                                <i class="fas fa-plus"></i>
                              </button>
                            </div>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td
                          v-for="(day, dayIndex) in formData.days"
                          :key="dayIndex"
                          class="border-b border-gray-200 p-4"
                          style="min-width: 300px"
                        >
                          <div class="space-y-4">
                            <div
                              v-for="(slot, slotIndex) in day.slots"
                              :key="slotIndex"
                              class="flex items-center gap-3 group"
                            >
                              <vue-timepicker
                                v-model="slot.start"
                                :minute-interval="5"
                                format="HH:mm"
                                class="flex-1"
                                @change="validateTime(dayIndex)"
                              />
                              <span class="text-gray-400">to</span>
                              <vue-timepicker
                                v-model="slot.end"
                                :minute-interval="5"
                                format="HH:mm"
                                class="flex-1"
                                @change="validateTime(dayIndex)"
                              />
                              <button
                                type="button"
                                @click="removeSlot(dayIndex, slotIndex)"
                                class="p-1.5 text-gray-400 hover:bg-red-50 hover:text-red-500 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200"
                                title="Remove slot"
                              >
                                <i class="fas fa-trash-alt"></i>
                              </button>
                            </div>
                            <div
                              v-if="day.error"
                              class="text-red-500 text-sm p-2 bg-red-50 rounded-lg flex items-center gap-2"
                            >
                              <i class="fas fa-exclamation-circle"></i>
                              {{ day.error }}
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </form>
          </div>

          <!-- Modal Footer -->
          <div
            class="flex justify-end gap-4 p-6 border-t border-gray-100 bg-gray-50 rounded-b-xl"
          >
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-200 transition-colors duration-200"
            >
              {{ formTranslation.common.cancel }}
            </button>
            <button
              @click="handleSubmit"
              :disabled="loading"
              class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-400 disabled:opacity-50 disabled:hover:bg-purple-600 transition-colors duration-200 flex items-center gap-2"
            >
              <i
                :class="loading ? 'fas fa-circle-notch fa-spin' : 'fas fa-save'"
              ></i>
              {{
                loading
                  ? formTranslation.common.loading
                  : formTranslation.doctor_session.save_btn
              }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  maxLength,
  minLength,
  required,
  requiredIf,
} from "vuelidate/lib/validators";
import VueTimepicker from "vue2-timepicker";
import "vue2-timepicker/dist/VueTimepicker.css";

export default {
  name: "SessionModal",
  components: {
    VueTimepicker,
  },
  props: {
    showAddEditSessionModal: {
      type: Boolean,
      required: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    allClinics: {
      type: Array,
      default: () => [],
    },
    doctors: {
      type: Array,
      default: () => [],
    },
    userData: {
      type: Object,
      required: true,
    },
  },
  data() {
    const days = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"];
    const timeSlots = Array.from({ length: 12 }, (_, i) => i * 5).filter(
      (slot) => slot <= 60
    );

    return {
      formData: {
        doctors: {},
        clinic_id: {},
        buffertime: 0,
        days: days.map((day) => ({
          name: day,
          label: (this.formTranslation?.days || {})[day] || day,
          slots: [],
          error: null,
        })),
      },
      loading: false,
      submitted: false,
      timeSlots,
      days,
    };
  },
  validations() {
    return {
      formData: {
        clinic_id: {
          required: requiredIf(() => {
            return (
              this.userData?.addOns?.kiviPro === true &&
              (this.getUserRole() === "administrator" ||
                this.getUserRole() === "doctor")
            );
          }),
        },
        doctors: {
          required: requiredIf(() => this.getUserRole() !== "doctor"),
        },
        buffertime: { required },
      },
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (Object.keys(newVal).length) {
          // Find the matching clinic from allClinics if clinic_id is provided
          let clinicData = {};
          if (newVal.clinic_id) {
            clinicData =
              this.allClinics.find(
                (clinic) => clinic.id === newVal.clinic_id
              ) || {};
          }

          this.formData = {
            ...this.defaultFormData(),
            ...newVal,
            clinic_id: clinicData, // Use the full clinic object
          };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    defaultFormData() {
      const translations = this.formTranslation?.days || {};
      const days = this.days || [
        "mon",
        "tue",
        "wed",
        "thu",
        "fri",
        "sat",
        "sun",
      ];

      return {
        doctors: {},
        clinic_id: "",
        buffertime: 0,
        days: days.map((day) => ({
          name: day,
          label: translations[day] || day,
          slots: [],
          error: null,
        })),
      };
    },
    closeModal() {
      this.$emit("close");
      this.formData = this.defaultFormData();
      this.submitted = false;
    },
    addSlot(dayIndex) {
      this.formData.days[dayIndex].slots.push({ start: "", end: "" });
    },
    removeSlot(dayIndex, slotIndex) {
      this.formData.days[dayIndex].slots.splice(slotIndex, 1);
      this.validateTime(dayIndex);
    },
    copyFromPreviousDay(dayIndex) {
      const previousDay = this.formData.days[dayIndex - 1];
      if (previousDay?.slots.length > 0) {
        this.formData.days[dayIndex].slots = previousDay.slots.map((slot) => ({
          start: slot.start,
          end: slot.end,
        }));
        this.validateTime(dayIndex);
      }
    },
    validateTime(dayIndex) {
      const slots = this.formData.days[dayIndex].slots;
      let hasOverlap = false;

      // Reset errors
      this.formData.days[dayIndex].error = null;

      // Validate slots
      for (let i = 0; i < slots.length; i++) {
        const current = slots[i];

        // Check empty slots
        if (!current.start || !current.end) {
          this.formData.days[dayIndex].error =
            this.formTranslation.common.emptyTimeSlots;
          return;
        }

        // Check invalid time range
        if (current.start >= current.end) {
          this.formData.days[dayIndex].error =
            this.formTranslation.common.invalidTimeRange;
          return;
        }

        // Check overlaps
        for (let j = i + 1; j < slots.length; j++) {
          const other = slots[j];
          if (
            (current.start < other.end && current.end > other.start) ||
            (other.start < current.end && other.end > current.start)
          ) {
            hasOverlap = true;
            break;
          }
        }
      }

      if (hasOverlap) {
        this.formData.days[dayIndex].error =
          this.formTranslation.common.overlappingSlots;
      }
    },
    handleClinicChange(event) {
      const selectedClinicId = event.target.value;
      this.$emit("clinic-change", selectedClinicId);
    },
    handleDoctorChange(event) {
      const doctorId = event.target.value;
      if (doctorId) {
        this.$emit("doctor-change", doctorId);
      }
    },
    async handleSubmit() {
      this.submitted = true;
      this.loading = true;

      try {
        this.$v.$touch();
        if (this.$v.$invalid) {
          throw new Error("Form validation failed");
        }

        let isValid = true;

        // Time slot validation
        this.formData.days.forEach((day, index) => {
          this.validateTime(index);
          if (day.error) isValid = false;
        });

        if (!isValid) {
          throw new Error("Time slot validation failed");
        }

        // Check if any slots are defined
        const hasAnySlots = this.formData.days.some(
          (day) => day.slots.length > 0
        );
        if (!hasAnySlots) {
          this.$swal.fire({
            title: "Error!",
            text: this.formTranslation.doctor_session.no_slots_error,
            icon: "error",
          });
          throw new Error("No time slots defined");
        }

        // Find the full clinic object from allClinics
        let selectedClinic = this.allClinics.find(
          (clinic) => clinic.id === this.formData.clinic_id
        );

        // Update the formData with the full clinic object
        this.formData.clinic_id = selectedClinic || {};

        await this.$emit("submit", this.formData);
        this.closeModal();
      } catch (error) {
        console.error("Submit error:", error);
        if (!error.message.includes("validation")) {
          this.$swal.fire({
            title: "Error!",
            text:
              error.message ||
              this.formTranslation.common.internal_server_error,
            icon: "error",
          });
        }
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped>
.vue__time-picker input.display-time {
  @apply w-full px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-colors duration-200;
}

.vue__time-picker .dropdown {
  @apply border border-gray-200 rounded-lg shadow-lg bg-white;
}

.vue__time-picker .dropdown ul li:not([disabled]).active,
.vue__time-picker .dropdown ul li:not([disabled]).active:hover {
  @apply bg-purple-500 text-white;
}

.vue__time-picker .dropdown ul li:not([disabled]):hover {
  @apply bg-purple-100;
}

/* Transition classes for modal */
.modal-enter-active,
.modal-leave-active {
  @apply transition-all duration-300;
}

.modal-enter-from,
.modal-leave-to {
  @apply opacity-0 scale-95;
}

.modal-enter-to,
.modal-leave-from {
  @apply opacity-100 scale-100;
}
</style>
