<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCAppointment;
use App\models\KCBill;
use App\models\KCClinicSession;
use App\models\KCPatientEncounter;
use App\models\KCAppointmentServiceMapping;
use App\models\KCBillItem;
use App\models\KCClinic;
use App\models\KCReceptionistClinicMapping;
use App\controllers\KCPaymentController;
use DateTime;
use DateTimeZone;
use Exception;

class MedroidWebhook extends KCBase
{

	public $db;

	private $request;

	public function __construct()
	{

		global $wpdb;

		$this->db = $wpdb;

		$this->request = new KCRequest();
		parent::__construct();

	}

	public function updatePatientSidebar()
	{
		if ($this->getLoginUserRole() !== 'administrator') {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$sidebar = kcAdminSidebarArray();
		update_option(KIVI_CARE_PREFIX . 'administrator_dashboard_sidebar_data', $sidebar);

		$sidebar = kcClinicAdminSidebarArray();
		update_option(KIVI_CARE_PREFIX . 'clinic_admin_dashboard_sidebar_data', $sidebar);

		$sidebar = kcReceptionistSidebarArray();
		update_option(KIVI_CARE_PREFIX . 'receptionist_dashboard_sidebar_data', $sidebar);

		$sidebar = kcDoctorSidebarArray();
		update_option(KIVI_CARE_PREFIX . 'doctor_dashboard_sidebar_data', $sidebar);

		$sidebar = kcPatientSidebarArray();
		update_option(KIVI_CARE_PREFIX . 'patient_dashboard_sidebar_data', $sidebar);

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Updated Patient Sidebar list', 'kc-lang'),
			'data' => "Success"
		]);
	}

	public function sqlStatements()
	{
		$this->medroid_run_database_migrations();
	}

	public function medroid_run_database_migrations()
	{
		global $wpdb;

		// Get WordPress table prefix
		$prefix = $wpdb->prefix;

		// Start transaction for safety
		$wpdb->query('START TRANSACTION');

		try {
			// Alter clinic table to add website column
			$wpdb->query("ALTER TABLE `{$prefix}kc_clinics` ADD `website` VARCHAR(255) NULL DEFAULT NULL AFTER `profile_image`");

			// Alter prescription table to add dose and route columns
			$wpdb->query("ALTER TABLE `{$prefix}kc_prescription` ADD `dose` VARCHAR(255) NULL DEFAULT NULL AFTER `name`, ADD `route` VARCHAR(255) NULL DEFAULT NULL AFTER `dose`");

			// Create encounter vitals table
			$wpdb->query("
				CREATE TABLE IF NOT EXISTS {$prefix}kc_encounter_vitals (
					id BIGINT PRIMARY KEY AUTO_INCREMENT,
					encounter_id BIGINT NOT NULL,
					patient_id BIGINT NOT NULL,
					vital_type VARCHAR(50) NOT NULL,
					vital_value VARCHAR(50),
					created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
					updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
				)
			");

			// Alter custom fields table to add module_sub_type column
			// $wpdb->query("ALTER TABLE `{$prefix}kc_custom_fields` ADD `module_sub_type` VARCHAR(255) NOT NULL AFTER `module_type`");

			// Create patient document table
			$wpdb->query("
				CREATE TABLE IF NOT EXISTS `{$prefix}kc_patient_document` (
					`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
					`name` TEXT COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
					`type` TEXT COLLATE utf8mb4_unicode_520_ci NOT NULL,
					`description` TEXT COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
					`patient_id` BIGINT(20) UNSIGNED NOT NULL,
					`document_id` VARCHAR(20) COLLATE utf8mb4_unicode_520_ci NOT NULL,
					`appointment_id` BIGINT(20) UNSIGNED DEFAULT NULL,
					`created_by` BIGINT(20) UNSIGNED DEFAULT NULL,
					`updated_by` BIGINT(20) UNSIGNED DEFAULT NULL,
					`created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
					`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
					PRIMARY KEY (`id`)
				) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci
			");

			// Handle zhu_kc_custom_fields table recreation
			// First check if the table exists before dropping
			$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$prefix}kc_custom_fields'");
			if ($table_exists) {
				$wpdb->query("DROP TABLE `{$prefix}kc_custom_fields`");
			}

			// Create new zhu_kc_custom_fields table
			$wpdb->query("
				CREATE TABLE `{$prefix}kc_custom_fields` (
					`id` bigint(20) NOT NULL,
					`module_type` varchar(191) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
					`module_sub_type` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
					`module_id` bigint(20) DEFAULT NULL,
					`fields` longtext COLLATE utf8mb4_unicode_520_ci,
					`status` tinyint(3) UNSIGNED DEFAULT '0',
					`created_at` datetime DEFAULT NULL,
					PRIMARY KEY (`id`)
				) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci
			");

			// Alter encounter tabs table
			$wpdb->query("
				ALTER TABLE `{$prefix}kc_encounter_tabs`
				ADD COLUMN `patient_id` bigint UNSIGNED NULL AFTER `encounter_id`,
				ADD COLUMN `added_by` bigint UNSIGNED NULL AFTER `metadata`,
				ADD COLUMN `is_from_template` tinyint NULL DEFAULT 0 AFTER `added_by`
			");

			// Migrate data from medical history to encounter tabs
			$wpdb->query("
				INSERT INTO `{$prefix}kc_encounter_tabs` 
				(encounter_id, patient_id, added_by, is_from_template, type, content, created_at, updated_at)
				SELECT 
					mh.encounter_id,
					mh.patient_id,
					mh.added_by,
					mh.is_from_template,
					mh.type,
					COALESCE(mh.title, ''),
					mh.created_at,
					mh.created_at
				FROM {$prefix}kc_medical_history mh
			");

			// Update encounter tabs for compatibility with existing data
			$tab_type_mappings = [
				['old' => 'problem', 'new' => 'concerns'],
				['old' => 'observations', 'new' => 'examinations'],
				['old' => 'safeguarding_concerns', 'new' => 'safegaurding'],
				['old' => 'note', 'new' => 'notes']
			];

			foreach ($tab_type_mappings as $mapping) {
				$wpdb->query("
					UPDATE `{$prefix}kc_encounter_tabs` 
					SET type = '{$mapping['new']}' 
					WHERE type = '{$mapping['old']}'
				");
			}

			// Commit the transaction if all operations are successful
			$wpdb->query('COMMIT');

			return true;
		} catch (Exception $e) {
			// Rollback transaction on error
			$wpdb->query('ROLLBACK');

			// Log the error
			error_log('Database migration error: ' . $e->getMessage());

			return false;
		}
	}

	public function executeCustomFieldsData()
	{
		$this->medroid_insert_all_custom_fields();
	}

	/**
	 * Add a function to insert all custom fields from the SQL data
	 */
	public function medroid_insert_all_custom_fields()
	{
		global $wpdb;

		// Get WordPress table prefix
		$prefix = $wpdb->prefix;

		// Check if table exists
		$table_name = $prefix . 'kc_custom_fields';
		$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

		if (!$table_exists) {
			error_log('Table ' . $table_name . ' does not exist');
			return false;
		}

		// This array should contain all 18 entries from the SQL INSERT statement
		$all_custom_fields = [
			[
				'id' => 1,
				'module_type' => 'identity_information',
				'module_sub_type' => 'identity_information',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Identity Document\",\"type\":\"file_upload\",\"name\":\"Identity Document\",\"options\":[],\"isRequired\":\"\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\",\"file_upload_type\":[{\"text\":\"png\",\"id\":\"image\\/png\"},{\"text\":\"pdf\",\"id\":\"application\\/pdf\"},{\"text\":\"jpg jpeg jpe\",\"id\":\"image\\/jpeg\"}]}',
				'status' => 1,
				'created_at' => '2024-11-18 11:41:11'
			],
			[
				'id' => 2,
				'module_type' => 'identity_information',
				'module_sub_type' => 'identity_information',
				'module_id' => 0,
				'fields' => '{\"id\":\"\",\"label\":\"Your registered GP Name?\",\"type\":\"text\",\"name\":\"Your registered GP Name?\",\"options\":[],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 1,
				'created_at' => '2024-11-27 14:24:31'
			],
			[
				'id' => 3,
				'module_type' => 'identity_information',
				'module_sub_type' => 'identity_information',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Registered GP\'s address?\",\"type\":\"text\",\"name\":\"Registered GP\'s address?\",\"options\":[],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 1,
				'created_at' => '2024-11-27 14:26:39'
			],
			[
				'id' => 4,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":\"\",\"label\":\"Please list any medical conditions you have been diagnosed with\",\"type\":\"text\",\"name\":\"Please list any medical conditions you have been diagnosed with\",\"options\":[],\"isRequired\":\"\",\"priority\":\"0\",\"placeholder\":\"(e.g., diabetes, hypertension, asthma)\",\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 14:27:25'
			],
			[
				'id' => 5,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Do you have any allergies?\",\"type\":\"select\",\"name\":\"Do you have any allergies?\",\"options\":[{\"id\":\"Yes\",\"text\":\"Yes\"},{\"id\":\"No\",\"text\":\"No\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 14:27:54'
			],
			[
				'id' => 6,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Are you currently taking any medications?\",\"type\":\"select\",\"name\":\"Are you currently taking any medications?\",\"options\":[{\"id\":\"Yes\",\"text\":\"Yes\"},{\"id\":\"No\",\"text\":\"No\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 14:28:46'
			],
			[
				'id' => 7,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Do you have a history of any of the following?\",\"type\":\"multiselect\",\"name\":\"Do you have a history of any of the following?\",\"options\":[{\"id\":\"Heart disease\",\"text\":\"Heart disease\"},{\"id\":\"Stroke\",\"text\":\"Stroke\"},{\"id\":\"Cancer\",\"text\":\"Cancer\"},{\"id\":\"Asthma\\/Breathing problems\",\"text\":\"Asthma\\/Breathing problems\"},{\"id\":\"Mental health conditions\",\"text\":\"Mental health conditions\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 14:29:54'
			],
			[
				'id' => 8,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":\"\",\"label\":\"What is the reason for your visit today? (Please provide a brief description of your symptoms or reason for seeking care):\",\"type\":\"text\",\"name\":\"What is the reason for your visit today? (Please provide a brief description of your symptoms or reason for seeking care):\",\"options\":[],\"isRequired\":\"\",\"priority\":\"0\",\"placeholder\":\"\",\"status\":\"1\"}',
				'status' => 1,
				'created_at' => '2024-11-27 14:30:32'
			],
			[
				'id' => 9,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Have your symptoms been ongoing or recent?\",\"type\":\"select\",\"name\":\"Have your symptoms been ongoing or recent?\",\"options\":[{\"id\":\"Ongoing\",\"text\":\"Ongoing\"},{\"id\":\"Recent\",\"text\":\"Recent\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 14:31:00'
			],
			[
				'id' => 10,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"How severe is your pain or discomfort (if applicable)?\",\"type\":\"select\",\"name\":\"How severe is your pain or discomfort (if applicable)?\",\"options\":[{\"id\":\"Mild\",\"text\":\"Mild\"},{\"id\":\"Moderate\",\"text\":\"Moderate\"},{\"id\":\"Severe\",\"text\":\"Severe\"},{\"id\":\"Very Severe\",\"text\":\"Very Severe\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 14:31:43'
			],
			[
				'id' => 11,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Have you had any recent hospitalizations or surgeries?\",\"type\":\"select\",\"name\":\"Have you had any recent hospitalizations or surgeries?\",\"options\":[{\"id\":\"Yes\",\"text\":\"Yes\"},{\"id\":\"No\",\"text\":\"No\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 16:56:47'
			],
			[
				'id' => 12,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'medical_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"If yes, please provide details:\",\"type\":\"text\",\"name\":\"If yes, please provide details:\",\"options\":[],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 17:03:24'
			],
			[
				'id' => 13,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'lifestyle_&_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Do you smoke?\",\"type\":\"select\",\"name\":\"Do you smoke?\",\"options\":[{\"id\":\"Yes\",\"text\":\"Yes\"},{\"id\":\"No\",\"text\":\"No\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 17:03:53'
			],
			[
				'id' => 14,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'lifestyle_&_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Do you drink alcohol?\",\"type\":\"select\",\"name\":\"Do you drink alcohol?\",\"options\":[{\"id\":\"Yes\",\"text\":\"Yes\"},{\"id\":\"No\",\"text\":\"No\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 17:04:21'
			],
			[
				'id' => 15,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'lifestyle_&_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Do you exercise regularly?\",\"type\":\"select\",\"name\":\"Do you exercise regularly?\",\"options\":[{\"id\":\"Yes\",\"text\":\"Yes\"},{\"id\":\"No\",\"text\":\"No\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 17:04:39'
			],
			[
				'id' => 16,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'lifestyle_&_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Do you follow any specific diet or nutrition plan?\",\"type\":\"select\",\"name\":\"Do you follow any specific diet or nutrition plan?\",\"options\":[{\"id\":\"Yes\",\"text\":\"Yes\"},{\"id\":\"No\",\"text\":\"No\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 17:05:00'
			],
			[
				'id' => 17,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'lifestyle_&_history',
				'module_id' => 0,
				'fields' => '{\"id\":null,\"label\":\"Does anyone in your family have a history of the following conditions?\",\"type\":\"multiselect\",\"name\":\"Does anyone in your family have a history of the following conditions?\",\"options\":[{\"id\":\"Heart disease\",\"text\":\"Heart disease\"},{\"id\":\"Stroke\",\"text\":\"Stroke\"},{\"id\":\"Cancer\",\"text\":\"Cancer\"},{\"id\":\"Diabetes\",\"text\":\"Diabetes\"},{\"id\":\"Mental health conditions\",\"text\":\"Mental health conditions\"}],\"isRequired\":\"0\",\"priority\":\"0\",\"placeholder\":null,\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 17:05:48'
			],
			[
				'id' => 18,
				'module_type' => 'appointment_module',
				'module_sub_type' => 'confirmation',
				'module_id' => 0,
				'fields' => '{\"id\":\"\",\"label\":\"By completing this questionnaire, I confirm that the information provided is accurate and complete to the best of my knowledge. I understand that this information will be used to assist my healthcare provider in offering the best care.\",\"type\":\"checkbox\",\"name\":\"By completing this questionnaire, I confirm that the information provided is accurate and complete to the best of my knowledge. I understand that this information will be used to assist my healthcare provider in offering the best care.\",\"options\":[{\"id\":\"I agree\",\"text\":\"I agree\"}],\"isRequired\":\"\",\"priority\":\"0\",\"placeholder\":\"\",\"status\":\"1\"}',
				'status' => 0,
				'created_at' => '2024-11-27 17:47:59'
			]
		];

		// Start transaction
		$wpdb->query('START TRANSACTION');

		try {
			foreach ($all_custom_fields as $field) {
				// Clean the JSON string by removing escaped backslashes
				$fields_json = stripslashes($field['fields']);
				$fields_array = json_decode($fields_json, true);
				
				if ($fields_array === null && json_last_error() !== JSON_ERROR_NONE) {
					throw new Exception('JSON decode error: ' . json_last_error_msg() . ' in field ID: ' . $field['id']);
				}
				
				// Re-encode as clean JSON
				$clean_json = json_encode($fields_array);
				
				// Update the field with clean JSON
				$field['fields'] = $clean_json;

				// Check if ID already exists
				$exists = $wpdb->get_var(
					$wpdb->prepare(
						"SELECT id FROM {$table_name} WHERE id = %d",
						$field['id']
					)
				);

				if ($exists) {
					// Update instead of insert
					$result = $wpdb->update(
						$table_name,
						[
							'module_type' => $field['module_type'],
							'module_sub_type' => $field['module_sub_type'],
							'module_id' => $field['module_id'],
							'fields' => $field['fields'],
							'status' => $field['status'],
							'created_at' => $field['created_at']
						],
						['id' => $field['id']]
					);
				} else {
					// Insert new record
					$result = $wpdb->insert(
						$table_name,
						[
							'id' => $field['id'],
							'module_type' => $field['module_type'],
							'module_sub_type' => $field['module_sub_type'],
							'module_id' => $field['module_id'],
							'fields' => $field['fields'],
							'status' => $field['status'],
							'created_at' => $field['created_at']
						]
					);
				}

				if ($result === false) {
					throw new Exception($wpdb->last_error);
				}
			}

			$wpdb->query('COMMIT');
			error_log('Custom fields inserted/updated successfully');
			return true;
		} catch (Exception $e) {
			$wpdb->query('ROLLBACK');
			error_log('Error processing custom fields: ' . $e->getMessage());
			return false;
		}
	}
}
