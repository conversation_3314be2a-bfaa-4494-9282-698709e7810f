<template>
    <div class="dashboard-wrapper">
      <!-- SMTP Warning if needed -->
      <div v-if="!isEmailSMTPWorking" class="alerts-section">
        <div class="modern-alert glass-effect">
          <div class="alert-content">
            <p class="m-0">
              <b>SMTP setup is required.</b>
              <a 
                v-if="request_status === 'off'" 
                class="info-link"
                href="https://apps.medroid.ai/docs/product/kivicare/email-sms-and-whatsapp/" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <i class="fa fa-question-circle"></i>
              </a>
            </p>
            <button class="btn btn-sm btn-glass" @click="openTestEmailModal = true">
              <i class="fas fa-paper-plane"></i> 
              Send Test Email
            </button>
          </div>
  
          <!-- ModalPopup for Sending Test Email -->
          <ModalPopup
            v-if="openTestEmailModal"
            modalId="send-test-email"
            modalSize="lg"
            :openModal="openTestEmailModal"
            modalTitle="Send Email"
            @closeModal="openTestEmailModal = false"
          > 
            <div class="form-group">
              <label for="email" class="form-control-label"> Email </label>
              <input 
                class="form-control" 
                id="email" 
                v-model="testEmail.recieverDetails" 
                placeholder="Enter email"
              >
              <div v-if="testSubmitted && !$v.testEmail.recieverDetails.required" class="invalid-feedback">
                Sender email is required.
              </div>
            </div>
            <div class="form-group">
              <label for="email_content" class="form-control-label"> Email Content </label>
              <textarea 
                class="form-control" 
                id="email_content" 
                v-model="testEmail.content"
              ></textarea>
              <div v-if="testSubmitted && !$v.testEmail.content.required" class="invalid-feedback">
                Email content is required.
              </div>
            </div>
            <div class="d-flex justify-content-end">
              <button class="btn btn-outline-primary" @click="openTestEmailModal=false">
                Cancel
              </button>
              <button class="btn btn-primary" @click="sendTestEmail">
                Test
              </button>
            </div>
          </ModalPopup>
        </div>
      </div>
  
      <!-- Stats Counter Section - Fixed and Colorful -->
      <div class="stats-grid">
        <router-link :to="{ name: 'patient'}" class="stat-card patients" v-if="kcCheckPermission('dashboard_total_patient')">
          <div class="stat-content">
            <div class="stat-icon patients">
              <i class="fas fa-user-injured"></i>
            </div>
            <div class="stat-info">
              <span class="stat-label">Total Patients</span>
              <span class="stat-value" v-if="isdataLoading">
                <i class="fa fa-spinner fa-spin"></i>
              </span>
              <span class="stat-value" v-else>{{ dashboardData.patient_count }}</span>
              <!-- <span class="stat-subtitle">Total visited patients</span> -->
            </div>
          </div>
        </router-link>
  
        <router-link :to="{ name: 'doctor'}" class="stat-card doctors" v-if="kcCheckPermission('dashboard_total_doctor')">
          <div class="stat-content">
            <div class="stat-icon doctors">
              <i class="fas fa-user-md"></i>
            </div>
            <div class="stat-info">
              <span class="stat-label">Total Doctors</span>
              <span class="stat-value" v-if="isdataLoading">
                <i class="fa fa-spinner fa-spin"></i>
              </span>
              <span class="stat-value" v-else>{{ dashboardData.doctor_count }}</span>
              <!-- <span class="stat-subtitle">Total clinic doctors</span> -->
            </div>
          </div>
        </router-link>
  
        <router-link :to="{ name: 'appointment-list.index'}" class="stat-card appointments" v-if="kcCheckPermission('dashboard_total_appointment')">
          <div class="stat-content">
            <div class="stat-icon appointments">
              <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stat-info">
              <span class="stat-label">Total Appointments</span>
              <span class="stat-value" v-if="isdataLoading">
                <i class="fa fa-spinner fa-spin"></i>
              </span>
              <span class="stat-value" v-else>{{ dashboardData.appointment_count }}</span>
              <!-- <span class="stat-subtitle">Total clinic appointments</span> -->
            </div>
          </div>
        </router-link>
  
        <div class="stat-card revenue" v-if="kcCheckPermission('dashboard_total_revenue')">
          <div class="stat-content">
            <div class="stat-icon revenue">
              <i class="fas fa-money-check-alt"></i>
            </div>
            <div class="stat-info">
              <span class="stat-label">Total Revenue</span>
              <span class="stat-value" v-if="isdataLoading">
                <i class="fa fa-spinner fa-spin"></i>
              </span>
              <span class="stat-value" v-else>{{ dashboardData.revenue }}</span>
              <!-- <span class="stat-subtitle">Total generated revenue</span> -->
            </div>
          </div>
        </div>
      </div>
  
      <!-- Dynamic Widgets Grid -->
      <div class="widgets-container">
        <!-- Appointments Widget -->
        <div class="widget-card" :class="{ 'expanded': isAppointmentExpanded }">
          <div class="widget-header">
            <div class="header-left">
              <h3>Latest Appointments</h3>
            </div>
            <div class="header-actions">
              <router-link 
                :to="{ name: 'appointment-list.index' }"
                class="action-btn primary">
                <i class="fas fa-eye"></i> 
                <span>View All</span>
              </router-link>
              <button 
                class="action-btn secondary" 
                @click="refreshAppointments">
                <i class="fas fa-sync"></i>
              </button>
              <button 
                class="action-btn expand"
                @click="toggleAppointmentWidget">
                <i :class="isAppointmentExpanded ? 'fas fa-compress' : 'fas fa-expand'"></i>
              </button>
            </div>
          </div>
          <div class="widget-body">
            <appointment-list 
              ref="appointment_list"
              :isLoading="isLoading"
              @isReloadTrue="appointmentReload"
              @csvData="csvLoadAppointmentData"
              @refreshDashboard="getDashboardData"
            />
            <!-- <dashboard-appointment-list
              ref="appointment_list"
              :isLoading="isLoading"
              @isReloadTrue="appointmentReload"
              @csvData="csvLoadAppointmentData"
              @refreshDashboard="getDashboardData"
            /> -->
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import AppointmentList from "../../components/appointment/AppointmentList";
  import { get, post } from "../../config/request";
  import { required } from "vuelidate/lib/validators";
  import VueApexCharts from 'vue-apexcharts';
  import ModalPopup from "../../components/Modal/Index";
  
  export default {
    components: {
      AppointmentList,
      apexcharts: VueApexCharts,
      ModalPopup
    },
    data: () => ({
      isLoading: false,
      dashboardData: {
        appointment_count: 0,
        doctor_count: 0,
        patient_count: 0,
        revenue: 0,
        is_email_working: true
      },
      isAppointmentReload: false,
      appointmentRequest: {},
      isEmailSMTPWorking: true,
      testSubmitted: false,
      testEmail: {
        recieverDetails: '',
        content: 'Welcome to Kivicare, This is a test mail.'
      },
      notificationType: 'email',
      isLocoTranslationUpdated: false,
      isdataLoading: true,
      csvLoader: true,
      csvAppointmentData: [],
      chartFilterType: 'weekly',
      filterType: [
        { label: 'Weekly', value: "weekly" },
        { label: 'Monthly', value: "monthly" },
      ],
      versionData: {
        kivi_pro_version: '0',
        kivi_telemed_version: '0',
        kivi_googlemeet_version: '0'
      },
      request_status: 'off',
      openTestEmailModal: false,
      totalCardEnable: 0,
      // Symbi AI Integration
      showSymbiChat: false,
      isSymbiMaximized: false,
      symbiUrl: 'https://app.medroid.ai', // Replace with your actual Symbi AI iframe URL
      showVersionWarning: false,
      // Widgets
      stats: [
        {
          id: 'patients',
          title: 'Total Patients',
          value: 0,
          label: 'Total visited patients',
          icon: 'fas fa-user-injured',
          isExpanded: false
        },
        {
          id: 'doctors',
          title: 'Total Doctors',
          value: 0,
          label: 'Total clinic doctors',
          icon: 'fas fa-user-md',
          isExpanded: false
        },
        {
          id: 'appointments',
          title: 'Total Appointments',
          value: 0,
          label: 'Total clinic appointments',
          icon: 'fas fa-calendar-check',
          isExpanded: false
        },
        {
          id: 'revenue',
          title: 'Total Revenue',
          value: 0,
          label: 'Total generated revenue',
          icon: 'fas fa-money-check-alt',
          isExpanded: false
        }
      ],
      isAppointmentExpanded: true,
      isChartExpanded: false,
      isSymbiExpanded: false,
    }),
    validations: {
      testEmail: {
        recieverDetails: { required },
        content: { required }
      }
    },
    mounted() {
      this.init();
      this.getModule();
      this.updateTotalCardEnable();
      this.computeVersionWarning();
      this.animateEntrance();
    },
    computed: {
      userData() {
        return this.$store.state.userDataModule.user;
      },
    },
    methods: {
      // Widget Toggle Methods
      toggleWidget(id) {
        this.stats = this.stats.map(stat => ({
          ...stat,
          isExpanded: stat.id === id ? !stat.isExpanded : false
        }));
      },
      
      toggleAppointments() {
        this.isAppointmentExpanded = !this.isAppointmentExpanded;
        this.isChartExpanded = false;
      },
      
      toggleChart() {
        this.isChartExpanded = !this.isChartExpanded;
        this.isAppointmentExpanded = false;
      },
      
      toggleAppointmentWidget() {
        this.isAppointmentExpanded = !this.isAppointmentExpanded;
        this.isChartExpanded = false;
      },
  
      toggleChartWidget() {
        this.isChartExpanded = !this.isChartExpanded;
        this.isAppointmentExpanded = false;
      },
      
      // Symbi AI Methods
      openSymbi() {
        this.showSymbiChat = true;
      },
      
      closeSymbi() {
        this.showSymbiChat = false;
        this.isSymbiExpanded = false;
      },
      
      toggleSymbi() {
        this.isSymbiExpanded = !this.isSymbiExpanded;
      },
  
      toggleSymbiSize() {
        this.isSymbiExpanded = !this.isSymbiExpanded;
      },
    
      // General Methods
      chartFilterChange(value) {
        this.chartFilterType = value;
        const chartWidget = document.querySelector('.chart-widget');
        chartWidget?.classList.add('filter-change');
        setTimeout(() => {
          chartWidget?.classList.remove('filter-change');
        }, 300);
      },
      
      csvLoadAppointmentData(data) {
        this.csvAppointmentData = this.formatJsonAppointmentData(data);
        this.csvLoader = false;
      },
      
      getWoocoomerceStatus() {
        const user = this.$store.state.userDataModule.user;
        return user.roles !== undefined ? user.woocommercePayment : '';
      },
      
      init() {
        this.getDashboardData();
        this.dashboardData = this.defaultDashboardData();
        this.appointmentRequest = this.defaultAppointmentRequest();
        this.getVersionData();
        // this.getUnderstandLocoTranslation();
      },
      
      defaultDashboardData() {
        return {
          appointment_count: 0,
          doctor_count: 0,
          patient_count: 0,
          revenue: 0,
          is_email_working: true
        };
      },
      
      getDashboardData() {
        get('get_dashboard', {})
          .then(response => {
            if (response.data.status === true) {
              this.isdataLoading = false;
              this.dashboardData = response.data.data;
              this.isEmailSMTPWorking = response.data.data.is_email_working;
              this.updateStats();
            }
          })
          .catch(error => {
            console.error(error);
            alert('Record not found.');
          });
      },
      
      getVersionData() {
        get('get_version_data', {})
          .then(response => {
            if (response.data.status === true) {
              this.versionData.kivi_pro_version = response.data.data.kivi_pro_version;
              this.versionData.kivi_telemed_version = response.data.data.kivi_telemed_version;
              this.versionData.kivi_googlemeet_version = response.data.data.kivi_googlemeet_version;
              this.computeVersionWarning();
            }
          })
          .catch(error => {
            console.error(error);
            alert('Record not found.');
          });
      },
      
      defaultAppointmentRequest() {
        return {
          date: new Date()
        };
      },
      
      appointmentReload() {
        this.isLoading = false;
        // Add smooth reload animation
        const widget = document.querySelector('.appointments-widget');
        widget?.classList.add('reload-animation');
        setTimeout(() => {
          widget?.classList.remove('reload-animation');
        }, 500);
      },
      
      sendTestEmail() {
        this.testSubmitted = true;
  
        // Touch all fields to trigger validation
        this.$v.$touch();
  
        if (this.$v.testEmail.$invalid) {
          return;
        }
  
        this.testEmail.type = this.notificationType;
        post('send_test_notification', this.testEmail)
          .then(response => {
            this.testSubmitted = false;
            if (response.data.status === true) {
              alert(response.data.message);
              this.isEmailSMTPWorking = true;
              this.openTestEmailModal = false;
            } else {
              alert(response.data.message);
            }
          })
          .catch(error => {
            this.testSubmitted = false;
            console.error(error);
            alert('Server error.');
          });
      },
      
      getModule() {
        if (window.request_data && window.request_data.link_show_hide) {
          this.request_status = window.request_data.link_show_hide;
        }
      },
      
      computeVersionWarning() {
        this.showVersionWarning = 
          (this.userData.addOns.kiviPro && this.versionData.kivi_pro_version < '2.5.2') ||
          (this.userData.addOns.telemed && this.versionData.kivi_telemed_version < '2.1.0') ||
          (this.userData.addOns.googlemeet && this.versionData.kivi_googlemeet_version < '1.0.6');
      },
      
      formatJsonAppointmentData(data) {
        // Implement your JSON formatting logic here
        return data;
      },
      
      // Update stats based on dashboardData
      updateStats() {
        this.stats = this.stats.map(stat => {
          switch(stat.id) {
            case 'patients':
              return { ...stat, value: this.dashboardData.patient_count };
            case 'doctors':
              return { ...stat, value: this.dashboardData.doctor_count };
            case 'appointments':
              return { ...stat, value: this.dashboardData.appointment_count };
            case 'revenue':
              return { ...stat, value: this.dashboardData.revenue };
            default:
              return stat;
          }
        });
      },
      
      // Update the number of enabled cards to adjust grid layout if needed
      updateTotalCardEnable() {
        this.totalCardEnable = 0;
        this.stats.forEach(stat => {
          if (this.kcCheckPermission(`dashboard_total_${stat.id}`)) {
            this.totalCardEnable++;
          }
        });
        if (this.totalCardEnable > 0) {
          this.totalCardEnable = 12 / this.totalCardEnable;
        }
      },
      
      // Refresh Appointments
      refreshAppointments() {
        this.isLoading = true;
        this.$refs.appointment_list.refresh();
      },
  
      // Entrance Animations
      animateEntrance() {
        const stats = document.querySelectorAll('.stat-card');
        stats.forEach((stat, index) => {
          stat.style.animationDelay = `${index * 0.1}s`;
          stat.classList.add('fade-in');
        });
  
        const widgets = document.querySelectorAll('.widget-card');
        widgets.forEach((widget, index) => {
          widget.style.animationDelay = `${(index + 4) * 0.1}s`;
          widget.classList.add('fade-in');
        });
      },
    }
  }
  </script>
  
  <style lang="scss">
  /* Brighter gradients and enhanced colors */
  :root {
    --gradient-purple: linear-gradient(135deg, #8B5CF6 0%, #6366F1 100%);
    --gradient-blue: linear-gradient(135deg, #2563EB 0%, #3B82F6 100%);
    --gradient-green: linear-gradient(135deg, #10B981 0%, #059669 100%);
    --gradient-orange: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.7);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --primary: #4F46E5;
    --warning: #F59E0B;
    --success: #48BB78;
    --info: #4299E1;
    --text-primary: #2D3748;
    --text-secondary: #4A5568;
    --border-color: rgba(226, 232, 240, 0.7);
  }
  
  /* Main Container */
  .dashboard-wrapper {
    min-height: 100vh;
    padding: 1.5rem;
  }
  
  /* Alerts Section */
  .alerts-section {
    margin-bottom: 1.5rem;
  }
  
  /* Glass Effect */
  .glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-md);
    border-radius: 1rem;
    transition: all 0.3s ease;
  }
  
  .glass-effect:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
  
  /* Modern Alert */
  .modern-alert {
    background: rgba(251, 191, 36, 0.1) !important;
    backdrop-filter: blur(8px) !important;
    border: 1px solid rgba(251, 191, 36, 0.2) !important;
    border-radius: 12px !important;
  
    .alert-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
  
    p {
      font-size: 0.875rem !important;
      color: #92400E !important;
    }
  
    .btn-glass {
      background: rgba(255, 255, 255, 0.2) !important;
      backdrop-filter: blur(8px) !important;
      border: 1px solid rgba(255, 255, 255, 0.3) !important;
      transition: all 0.2s ease;
  
      &:hover {
        background: rgba(255, 255, 255, 0.3) !important;
        transform: translateY(-1px);
      }
    }
  
    .info-link {
      color: #92400E;
      margin-left: 0.5rem;
      font-size: 1rem;
  
      &:hover {
        color: #FFAA00;
      }
    }
  }
  
  /* Stats Grid */
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.25rem;
    margin-bottom: 2rem;
  }
  
  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
  
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      opacity: 0.08;
      z-index: 0;
      transition: opacity 0.3s ease;
    }
  
    &:hover::before {
      opacity: 0.12;
    }
  
    &.patients::before { background: var(--gradient-purple); }
    &.doctors::before { background: var(--gradient-blue); }
    &.appointments::before { background: var(--gradient-green); }
    &.revenue::before { background: var(--gradient-orange); }
  
    &:hover {
      box-shadow: var(--shadow-lg);
    }
  
    .stat-content {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
    }
  
    .stat-icon {
      width: 52px;
      height: 52px;
      border-radius: 1rem;
      margin-right: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
      box-shadow: var(--shadow-sm);
  
      &.patients { background: var(--gradient-purple); }
      &.doctors { background: var(--gradient-blue); }
      &.appointments { background: var(--gradient-green); }
      &.revenue { background: var(--gradient-orange); }
    }
  
    .stat-info {
      display: flex;
      flex-direction: column;
  
      .stat-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #4B5563;
        margin-bottom: 0.5rem;
      }
  
      .stat-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: #111827;
        margin-bottom: 0.25rem;
      }
  
      .stat-subtitle {
        font-size: 0.75rem;
        color: #6B7280;
        font-weight: 500;
      }
    }
  }
  
  /* Widgets Container */
  .widgets-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
    margin-top: 1.5rem;
  }
  
  .widget-card {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  
    &.expanded {
      grid-column: 1 / -1;
  
      .widget-body {
        max-height: 800px;
      }
  
      & + .widget-card {
        grid-column: auto;
      }
    }
  
    &:hover {
      box-shadow: var(--shadow-lg);
    }
  
    .widget-header {
      padding: 1.25rem;
      background: white;
      border-bottom: 1px solid #E5E7EB;
      display: flex;
      align-items: center;
      justify-content: space-between;
  
      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #111827;
        margin: 0;
      }
  
      p.subtitle {
        font-size: 0.875rem;
        color: #6B7280;
        margin: 0.25rem 0 0 0;
      }
    }
  
    .header-actions {
      display: flex;
      align-items: center;
      gap: 0.75rem;
  
      .action-btn {
        padding: 0.5rem;
        border-radius: 0.5rem;
        border: none;
        background: transparent;
        color: #6B7280;
        cursor: pointer;
        transition: all 0.2s ease;
  
        &.primary {
          background: var(--gradient-purple);
          color: white;
          padding: 0.5rem 1rem;
          font-weight: 500;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
          }
        }
  
        &:hover {
          background: #F3F4F6;
          color: #111827;
        }
      }
    }
  
    /* Filter Group Styling */
    .filter-group {
      display: flex;
      gap: 0.5rem;
      padding: 0.25rem;
      background: #F3F4F6;
      border-radius: 0.5rem;
  
      .filter-btn {
        padding: 0.5rem 1rem;
        border: none;
        background: transparent;
        color: #6B7280;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.2s ease;
  
        &.active {
          background: white;
          color: #111827;
          box-shadow: var(--shadow-sm);
        }
  
        &:hover:not(.active) {
          color: #111827;
        }
      }
    }
  
    /* Widget Body */
    .widget-body {
      padding: 1.25rem;
      background: white;
      transition: all 0.3s ease;
    }
  }
  
  /* Symbi AI Styling */
  .symbi-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 50;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &.expanded {
      inset: 2rem;
      .symbi-interface {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  .symbi-interface {
    width: 380px;
    height: 600px;
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  .symbi-header {
    background: var(--gradient-purple);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
  }
  
  .ai-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .ai-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ADE80;
    animation: pulse 2s infinite;
  }
  
  .symbi-controls {
    display: flex;
    gap: 0.5rem;
  }
  
  .symbi-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
  
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
  }
  
  .symbi-content {
    flex: 1;
    overflow: hidden;
  }
  
  .symbi-frame {
    flex: 1;
    width: 100%;
    border: none;
    border-radius: 0 0 1rem 1rem;
    background: rgba(255, 255, 255, 0.5);
  }
  
  /* Symbi Trigger */
  .symbi-trigger {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    background: var(--gradient-purple);
    border: none;
    border-radius: 1rem;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 1rem;
    transform-origin: right bottom;
    transition: all 0.3s ease;
    z-index: 1000;
  
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .trigger-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .trigger-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .dot {
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    opacity: 0.5;
    animation: bounce 1.4s infinite ease-in-out;
  
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.4s; }
  }
  
  @keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
  }
  
  @keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(74, 222, 128, 0); }
    100% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0); }
  }
  
  /* Animations */
  .fade-in {
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .reload-animation {
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      background: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(4px);
      display: flex;
      align-items: center;
      justify-content: center;
      animation: fadeOut 0.5s ease forwards;
    }
  }
  
  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }
  
  .filter-change {
    animation: pulseScale 0.3s ease;
  }
  
  @keyframes pulseScale {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
  }
  
  /* Responsive Adjustments */
  @media (max-width: 1280px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 1024px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
    }
  }
  
  @media (max-width: 768px) {
    .widgets-container {
      grid-template-columns: 1fr;
    }
  
    .widget-card.expanded {
      grid-column: auto;
    }
  
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }
  
    .stat-card {
      padding: 1.25rem;
    }
  
    .stat-icon {
      width: 48px;
      height: 48px;
      font-size: 1.5rem;
    }
  
    .stat-info {
      .stat-value {
        font-size: 1.5rem;
      }
    }
  
    .symbi-container.expanded {
      inset: 0;
    }
  
    .symbi-interface {
      width: 100%;
      height: 100%;
      border-radius: 0;
    }
  }
  
  @media (max-width: 640px) {
    .stats-grid {
      grid-template-columns: 1fr;
    }
  }
  
  /* Loading State Enhancements */
  .loading-overlay {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  
    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--border-color);
      border-top-color: var(--primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
  
  /* Chart Widget Enhancements */
  .chart-widget {
    .widget-body {
      position: relative;
      min-height: 300px;
      transition: all 0.3s ease;
    }
  }
  
  /* Appointment List Enhancements */
  .appointment-list-container {
    transition: all 0.3s ease;
    
    table {
      border-collapse: separate;
      border-spacing: 0;
      width: 100%;
      
      th, td {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
      }
      
      tr:last-child td {
        border-bottom: none;
      }
      
      tbody tr {
        transition: background-color 0.2s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }
  
  /* Spinner Animation */
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  </style>
  