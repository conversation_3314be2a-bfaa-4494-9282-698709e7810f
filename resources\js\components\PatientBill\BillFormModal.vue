<template>
  <div
    v-if="showGenerateBillModal"
    class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 py-8"
  >
    <div class="bg-white rounded-xl w-full max-w-4xl m-4 shadow-xl">
      <!-- <PERSON><PERSON> Header -->
      <div class="flex items-center justify-between px-8 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-t-xl">
        <h3 class="text-xl font-semibold text-white flex">
          <span>

            <svg xmlns="http://www.w3.org/2000/svg"  width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text text-white"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>
          </span>

          <span class="ml-4">
            {{ formTranslation.patient_bill.generate_invoice }}
          </span>
        </h3>
        <button @click="closeModal" class="text-gray-500 hover:text-gray-700 transition-colors">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-5">
        <form
          id="patientBillDataForm"
          @submit.prevent="handleSubmit"
          :novalidate="true"
        >
          <!-- Clinic Selection -->
          <div v-if="!isFromEncounter" class="flex flex-wrap -mx-2 mb-6">
            <!-- Column 1: Clinic Selection -->
            <div class="w-full md:w-1/3 px-2 mb-4">
              <label for="clinic_id" class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.appointments.select_clinic }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select
                deselect-label=""
                select-label=""
                @select="clinicChange"
                v-model="appointmentData.clinic_id"
                :tag-placeholder="formTranslation.appointments.select_clinic_plh"
                id="clinic_id"
                :placeholder="formTranslation.appointments.search_plh"
                label="label"
                track-by="id"
                :options="$store.state.clinic"
                class="w-full"
                :disabled="isClinicAdmin || isDoctor"
              />
              <p v-if="submitted && !$v.appointmentData.clinic_id.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.clinic_is_required }}
              </p>
            </div>

            <!-- Column 2: Doctor Selection -->
            <div class="w-full md:w-1/3 px-2 mb-4">
              <label for="doctor_id" class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.common.doctor }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select
                deselect-label=""
                select-label=""
                @select="fetchServices"
                v-model="appointmentData.doctor_id"
                :tag-placeholder="formTranslation.appointments.doctor_plh"
                :placeholder="formTranslation.appointments.search_plh"
                id="doctor_id"
                label="label"
                track-by="id"
                :loading="doctorMultiselectLoader"
                :options="doctors"
                class="w-full"
                :disabled="isDoctor"
              />
              <p v-if="submitted && !$v.appointmentData.doctor_id.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.doc_required }}
              </p>
            </div>

            <!-- Column 3: Patient Selection - Visible for all roles -->
            <div class="w-full md:w-1/3 px-2 mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.common.patient }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select
                v-model="appointmentData.patient_id"
                :loading="patientMultiselectLoader"
                :options="patients"
                :tag-placeholder="formTranslation.appointments.tag_patient_type_plh"
                :placeholder="formTranslation.appointments.search_plh"
                label="label"
                track-by="id"
                class="w-full"
              />
              <p v-if="submitted && !$v.appointmentData.patient_id.required" class="text-sm text-red-500 mt-1">
                {{ formTranslation.appointments.patient_requires }}
              </p>
            </div>
          </div>

          <!-- Bill Items Section -->
          <div class="mb-6">
            <div class="flex justify-between items-center mb-3">
              <h3 class="text-lg font-semibold text-gray-800 border-l-4 border-blue-500 pl-3">Bill Items</h3>
              <button
                type="button"
                class="px-3 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors focus:outline-none flex items-center gap-2"
                @click="addEmptyRow"
              >
                <i v-if="!visible" class="fa fa-plus"></i>
                <i v-else class="fa fa-minus"></i>
                {{ formTranslation.patient_bill.bill_add_item }}
              </button>
            </div>

            <div class="w-full overflow-x-auto bg-white border rounded-lg" style="overflow: visible !important;">
              <table class="w-full text-sm border-collapse" style="overflow: visible;">
                <thead>
                  <tr class="bg-gray-50 border-b">
                    <th class="p-3 text-left font-semibold text-gray-600">#</th>
                    <th class="p-3 text-left font-semibold text-gray-600">{{ formTranslation.common.services }}</th>
                    <th class="p-3 text-left font-semibold text-gray-600">{{ formTranslation.common.price }}</th>
                    <th class="p-3 text-left font-semibold text-gray-600">{{ formTranslation.common.quantity }}</th>
                    <th class="p-3 text-left font-semibold text-gray-600">{{ formTranslation.common.total }}</th>
                    <th class="p-3 text-left font-semibold text-gray-600">Payment Status</th>
                    <th class="p-3 text-left font-semibold text-gray-600">{{ formTranslation.common.action }}</th>
                  </tr>
                </thead>
                <tbody
                  v-if="
                    patientBillData.billItems !== undefined &&
                    patientBillData?.billItems?.length > 0
                  "
                >
                  <tr
                    v-for="(billing_item, index) in patientBillData.billItems"
                    :key="index"
                    class="hover:bg-gray-50 border-b"
                  >
                    <td class="p-3">{{ index + 1 }}</td>

                    <!-- Service Cell -->
                    <td class="p-3" style="position: relative; overflow: visible;">
                      <div v-if="!billing_item.isEditing" class="font-medium">
                        {{ billing_item.item_id?.label || "-" }}
                      </div>
                      <multi-select
                        v-else
                        v-model="billing_item.item_id"
                        :options="services"
                        :searchable="true"
                        deselect-label=""
                        select-label=""
                        @select="(selected) => handleItemChange(selected, index + 1)"
                        :placeholder="formTranslation.appointments.search_plh || 'Search for a service'"
                        label="label"
                        track-by="id"
                        class="w-full service-dropdown"
                      >
                        <template slot="option" slot-scope="props">
                          <div class="option__desc">
                            <span class="option__title">{{ props.option.label }}</span>
                            <span v-if="props.option.price > 0" class="option__small text-gray-500 ml-1">
                              ({{ localCurrencyPrefix }}{{ props.option.price }}{{ localCurrencyPostfix }})
                            </span>
                          </div>
                        </template>
                      </multi-select>
                    </td>

                    <!-- Price Cell -->
                    <td class="p-3 text-gray-700">
                      <div class="flex items-center">
                        {{ localCurrencyPrefix }}{{ billing_item.price }}{{ localCurrencyPostfix }}
                        <span v-if="billing_item.isCustomPrice" class="ml-2 px-1.5 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full" title="Custom price set by doctor">Custom</span>
                      </div>
                    </td>

                    <!-- Quantity Cell -->
                    <td class="p-3">
                      <div v-if="!billing_item.isEditing" class="text-gray-700">
                        {{ billing_item.qty }}
                      </div>
                      <input
                        v-else
                        type="number"
                        v-model="billing_item.qty"
                        @change="updateBillTotal"
                        min="0"
                        class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </td>

                    <!-- Total Cell -->
                    <td class="p-3 font-medium">
                      <div v-if="!billing_item.isEditing" class="text-gray-700">
                        {{ localCurrencyPrefix }}{{ billing_item.price * billing_item.qty }}{{ localCurrencyPostfix }}
                      </div>
                      <div v-else class="relative">
                        <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">{{ localCurrencyPrefix }}</span>
                        <input
                          type="number"
                          v-model="billing_item.price"
                          @change="handlePriceChange(index)"
                          min="0"
                          step="0.01"
                          class="w-full px-8 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Custom price"
                        />
                        <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">{{ localCurrencyPostfix }}</span>
                      </div>
                    </td>

                    <!-- Status Cell -->
                    <td class="p-3">
                      <span
                        v-if="(billing_item.payment_status!='paid')"
                        class="px-2 py-1 text-xs font-medium text-white bg-red-500 rounded-full"
                      >
                        Unpaid
                      </span>
                      <span
                        v-else
                        class="px-2 py-1 text-xs font-medium text-white bg-green-500 rounded-full"
                      >
                        Paid
                      </span>
                    </td>

                    <!-- Actions Cell -->
                    <td class="p-3">
                      <div class="flex space-x-1" v-if="billing_item.payment_status!='paid'">
                        <button
                          type="button"
                          v-if="!billing_item.isEditing"
                          class="p-1 text-blue-500 hover:bg-blue-50 rounded-md transition-colors"
                          @click="toggleEdit(index)"
                        >
                          <i class="fa fa-pen-alt"></i>
                        </button>
                        <button
                          type="button"
                          v-else
                          class="p-1 text-green-500 hover:bg-green-50 rounded-md transition-colors"
                          @click="saveEdit(index)"
                        >
                          <i class="fa fa-check"></i>
                        </button>
                        <button
                          type="button"
                          v-if="billing_item.isEditing"
                          class="p-1 text-gray-500 hover:bg-gray-50 rounded-md transition-colors"
                          @click="cancelEdit(index)"
                        >
                          <i class="fa fa-times"></i>
                        </button>
                        <button
                          type="button"
                          class="p-1 text-red-500 hover:bg-red-50 rounded-md transition-colors"
                          @click="deleteBillItem(index + 1)"
                        >
                          <i class="fa fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
                <tbody v-else>
                  <tr>
                    <td colspan="7" class="p-6 text-center">
                      <h4 class="text-blue-500 text-lg">
                        {{ formTranslation.common.no_records_found }}
                      </h4>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div
                v-if="
                  submitted &&
                  patientBillData.billItems !== undefined &&
                  !(patientBillData?.billItems?.length > 0)
                "
                class="text-red-500 text-sm p-3"
              >
                {{ formTranslation.patient_bill.please_add_bill_items }}
              </div>
            </div>
          </div>

          <!-- Tax Table Section Removed -->

          <!-- Amount Details Section -->
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-800 border-l-4 border-blue-500 pl-3 mb-3">Payment Details</h3>
            <div class="flex flex-wrap -mx-2 bg-gray-50 p-4 rounded-lg">
              <!-- Total Amount -->
              <div class="px-2 w-full sm:w-1/2 md:w-1/3">
                <div class="mb-4">
                  <label for="total_amount" class="block mb-2 font-medium text-gray-700">
                    {{ formTranslation.common.total }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500" style="line-height: 0;">{{
                      localCurrencyPrefix
                    }}</span>
                    <input
                      id="total_amount"
                      readonly
                      disabled
                      :class="{
                        'border-red-500': submitted && $v.patientBillData?.total_amount.$error,
                        'bg-gray-100': true
                      }"
                      :value="patientBillData?.total_amount"
                      :placeholder="formTranslation.patient_bill.plh_total_amount"
                      type="text"
                      class="w-full px-8 py-2 border rounded-md"
                    />
                    <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">{{
                      localCurrencyPostfix
                    }}</span>
                  </div>
                  <div
                    v-if="submitted && !$v.patientBillData?.total_amount.required"
                    class="mt-1 text-red-500 text-sm"
                  >
                    {{ formTranslation.patient_bill.bill_total_required }}
                  </div>
                </div>
              </div>

              <!-- Discount -->
              <div class="px-2 w-full sm:w-1/2 md:w-1/3">
                <div class="mb-4">
                  <label for="discount" class="block mb-2 font-medium text-gray-700">
                    {{ formTranslation.patient_bill.discount }}
                    <span class="text-red-500">*</span>
                    <span class="text-blue-600 text-xs ml-1">
                      {{ formTranslation.patient_bill.discount_amount }}
                    </span>
                  </label>
                  <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500" style="line-height: 0;">{{
                      localCurrencyPrefix
                    }}</span>
                    <input
                      id="discount"
                      v-model="patientBillData.discount"
                      :class="{
                        'border-red-500': submitted && $v.patientBillData.discount.$error,
                      }"
                      :disabled="
                        patientBillData?.billItems !== undefined &&
                        !(patientBillData?.billItems?.length > 0)
                      "
                      type="number"
                      min="0"
                      class="w-full px-8 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none"
                      oninput="validity.valid||(value='');"
                    />
                    <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">{{
                      localCurrencyPostfix
                    }}</span>
                  </div>
                  <div
                    v-if="submitted && !$v.patientBillData.discount.required"
                    class="mt-1 text-red-500 text-sm"
                  >
                    {{ formTranslation.patient_bill.discount_required }}
                  </div>
                </div>
              </div>

              <!-- Paid Amount -->
              <div class="px-2 w-full sm:w-1/2 md:w-1/3">
                <div class="mb-4">
                  <label for="paid_amount" class="block mb-2 font-medium text-gray-700">
                    {{ formTranslation.patient_bill.paid_amount || 'Paid Amount' }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500" style="line-height: 0;">{{
                      localCurrencyPrefix
                    }}</span>
                    <input
                      id="paid_amount"
                      v-model="patientBillData.paid_amount"
                      :class="{
                        'border-red-500': submitted && $v.patientBillData.paid_amount.$error,
                        'bg-gray-100': true
                      }"
                      disabled
                      type="number"
                      min="0"
                      class="w-full px-8 py-2 border rounded-md"
                      oninput="validity.valid||(value='');"
                    />
                    <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">{{
                      localCurrencyPostfix
                    }}</span>
                  </div>
                  <div
                    v-if="submitted && !$v.patientBillData.paid_amount.required"
                    class="mt-1 text-red-500 text-sm"
                  >
                    {{ formTranslation.patient_bill.paid_amount_required || 'Paid amount is required' }}
                  </div>
                </div>
              </div>

              <!-- Payable Amount -->
              <div class="px-2 w-full sm:w-1/2 md:w-1/3">
                <div class="mb-4">
                  <label for="actual_amount" class="block mb-2 font-medium text-gray-700">
                    {{ formTranslation.patient_bill.payable_amount }}
                  </label>
                  <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500" style="line-height: 0;">{{
                      localCurrencyPrefix
                    }}</span>
                    <input
                      id="actual_amount"
                      readonly
                      disabled
                      v-model="patientBillData.actual_amount"
                      type="number"
                      class="w-full px-8 py-2 border rounded-md bg-blue-50 font-bold text-blue-600"
                    />
                    <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">{{
                      localCurrencyPostfix
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Method Selection -->
          <div class="mb-6" v-if="patientBillData.actual_amount > 0">
            <h3 class="text-lg font-semibold text-gray-800 border-l-4 border-blue-500 pl-3 mb-3">Payment Method</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Stripe Payment Option -->
              <label
                :class="[
                  'flex items-center p-4 border rounded-lg cursor-pointer transition-colors relative',
                  patientBillData.payment_mode === 'paymentStripepay'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                ]"
              >
                <input
                  type="radio"
                  name="paymentMethod"
                  value="paymentStripepay"
                  v-model="patientBillData.payment_mode"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 absolute top-2 right-2 accent-primary"
                />
                <div class="ml-3 flex items-center">
                  <div class="mr-4 flex-shrink-0 h-8 w-8 flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full shadow"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-credit-card h-4 w-4 text-white"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg></div>
                  <div>
                    <p class="font-medium text-gray-900">Stripe</p>
                    <p class="text-sm text-gray-500">Pay securely online with credit card</p>
                  </div>
                </div>
              </label>

              <!-- Offline Payment Option -->
              <label
                :class="[
                  'flex items-center p-4 border rounded-lg cursor-pointer transition-colors relative ',
                  patientBillData.payment_mode === 'paymentOffline'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                ]"
              >
                <input
                  type="radio"
                  name="paymentMethod"
                  value="paymentOffline"
                  v-model="patientBillData.payment_mode"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 absolute top-2 right-2 accent-primary"
                />
                <div class="ml-3 flex items-center">
                  <div class="mr-4flex-shrink-0 h-8 w-8 flex items-center justify-center bg-gradient-to-r from-gray-100 to-gray-200 rounded-full shadow"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-dollar-sign h-4 w-4 text-gray-600"><line x1="12" x2="12" y1="2" y2="22"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg></div>
                  <div>
                    <p class="font-medium text-gray-900">Pay Offline</p>
                    <p class="text-sm text-gray-500">Pay with cash, bank transfer or Cheque</p>
                  </div>
                </div>
              </label>
            </div>
            <p v-if="submitted && !$v.patientBillData.payment_mode.required" class="text-sm text-red-500 mt-2">
              Please select a payment method to continue
            </p>
          </div>

          <!-- Footer Section -->
          <div class="flex justify-end space-x-3 pt-4 border-t mt-6">
            <button
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              @click="$emit('onBillCancel') || closeModal"
              type="button"
            >
              {{ formTranslation.common.cancel }}
            </button>

            <button
              v-if="!loading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              type="submit"
              v-html="buttonText"
            ></button>
            <button
              v-else
              class="px-4 py-2 bg-blue-600 text-white rounded-md opacity-70 cursor-not-allowed flex items-center gap-2 inline-flex justify-center py-2 px-5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-all"
              type="submit"
              disabled
            >
              <i class="fa fa-sync fa-spin"></i>&nbsp;
              {{ formTranslation.common.loading }}
            </button>

          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { maxValue, minValue, required } from "vuelidate/lib/validators";
import { get, post } from "../../config/request";
import {
  minimumValue,
  validateForm,
  maximumDiscount,
  alphaSpace,
} from "../../config/helper";
import { requiredIf } from "vuelidate/lib/validators";

export default {
  name: "BillFormModal",
  props: {
    clinic_currency_prefix: String,
    clinic_currency_postfix: String,
    // buttonText: String,
    loading: {
      type: Boolean,
      default: false,
    },
    submitted: Boolean,
    userData: {
      type: Object,
      required: true,
    },
    showGenerateBillModal: {
      type: Boolean,
      required: true,
    },
    encounterId: {
      type: [String, Number],
      default() {
        return 0;
      },
    },
    doctorId: {
      type: [String, Number],
      default() {
        return 0;
      },
    },
    appointmentData: {
      type: Object,
      default: () => ({}),
    },
    clinic_extra: {
      type: [Object, Array, String],
      default: () => ({}),
    },
    checkOutVal: {
      type: [String, Number],
      default() {
        return 0;
      },
    },
    isFromEncounter:true
  },
  data: () => {
    return {
      patientBillData: {
        billItems: [],
      },
      woocommerceActive: "off",
      cardTitle: "Add bill",
      encounter_id: 0,
      services: [],
      billItem: {},
      billItemTitle: "Add Bill Item",
      buttonText: "",
      billItemBtn: '<i class="fa fa-plus"></i> Save',
      billItemSubmitted: false,
      billItemEdit: false,
      visible: false,
      showEditForm: false,
      showAddForm: false,
      staticId: -1,
      localBillData: null,
      status: [
        {
          id: "paid",
          label: "Paid",
        },
        {
          id: "unpaid",
          label: "Unpaid",
        },
      ],
      isLoading: false,
      payment_mode: "paymentOffline",
      biilingDetail:{},
      doctorMultiselectLoader:false,
      patientMultiselectLoader:false,
      patients: [],
        // All doctors data for filtering
      doctors: [],
    };
  },
  validations: {
    patientBillData: {
      total_amount: { required },
      discount: {
        required,
        maximumDiscount,
        minValue: minValue(0),
      },
      actual_amount: { required },
      paid_amount :{ required },
      payment_mode: {
        required: requiredIf(function () {
          return (
            this.patientBillData?.actual_amount > 0 &&
            this.patientBillData?.billItems?.length > 0
          );
        }),
      },
    },
    billItem: {
      item_id: { required },
      qty: {
        required,
        minValue: minValue(1),
        maxValue: maxValue(10000),
      },
      price: {
        required,
        minValue: minValue(0),
        maxValue: maxValue(1000000000000000000),
      },
    },
    appointmentData: {
      clinic_id: { required },
      doctor_id: { required },
      patient_id: { required },
    },
  },
  created() {
    this.localBillData = this.defaultMedicalRecordData();
    // this.patientBillData = this.defaultMedicalRecordData();
  },
  mounted() {
    // Wait for store data to be available
    this.$nextTick(() => {
      // Set default values based on user role first
      this.setDefaultValuesBasedOnRole();
      // Then initialize the component
      this.init();
    });
  },
  computed: {
    localCurrencyPrefix() {
      return (
        this.userData.clinic_currency_detail.prefix || this.clinic_currency_prefix || ""
      );
    },

    localCurrencyPostfix() {
      return (
        this.userData.clinic_currency_detail.postfix ||
        this.clinic_currency_postfix ||
        ""
      );
    },

    // User role computed properties
    isAdmin() {
      return this.userData && this.userData.roles &&
        (this.userData.roles.includes('administrator'));
    },

    isClinicAdmin() {
      return this.userData && this.userData.roles &&
        this.userData.roles.includes('kiviCare_clinic_admin');
    },

    isDoctor() {
      return this.userData && this.userData.roles &&
        this.userData.roles.includes('kiviCare_doctor');
    },

    isPatient() {
      return this.userData && this.userData.roles &&
        this.userData.roles.includes('kiviCare_patient');
    },
  },
  methods: {
    init() {
      this.buttonText = this.formTranslation.common.save;

      // Check if clinic is selected
      if (!this.appointmentData?.clinic_id) {
        console.log("No clinic selected, cannot initialize");
        this.isLoading = false;
        return;
      }

      console.log("Initializing with clinic:", this.appointmentData.clinic_id);
      console.log("Doctor selected:", this.appointmentData.doctor_id);

      this.billItemBtn =
        '<i class="fa fa-plus"></i> ' + this.formTranslation.common.save;
      this.patientBillData = this.defaultMedicalRecordData();
      this.billItem = this.defaultBillingItemData();
      //this.checkWoocommerceActive();

      if (this.encounterId !== 0) {
        console.log("edit generate bill");
        this.patientBillData.encounter_id = this.encounterId;
        this.editBill(this.encounterId);
      }

      // Fetch services based on the selected doctor
      if (this.appointmentData.doctor_id) {
        console.log("Fetching services for doctor:", this.appointmentData.doctor_id);
        this.fetchServices();
      } else if (this.isDoctor) {
        // If doctor role but no doctor selected, set the current user as doctor
        this.appointmentData.doctor_id = {
          id: this.userData.ID,
          label: this.userData.display_name
        };
        this.fetchServices();
      }
    },

    // Set default values based on user role
    setDefaultValuesBasedOnRole() {
      console.log("Setting default values based on user role");
      console.log("User data:", this.userData);
      console.log("Available clinics:", this.$store.state.clinic);

      // For clinic admin, pre-select their clinic
      if (this.isClinicAdmin) {
        // Get the clinic admin's clinic ID
        const clinicId = this.userData.clinic_id || this.userData.user_clinic_id;
        console.log("Clinic admin clinic ID:", clinicId);

        // Find the clinic in the store
        const clinicAdminClinic = this.$store.state.clinic.find(
          clinic => parseInt(clinic.id) === parseInt(clinicId)
        );

        console.log("Found clinic:", clinicAdminClinic);

        if (clinicAdminClinic) {
          // Set the clinic
          this.appointmentData.clinic_id = clinicAdminClinic;

          // After setting clinic, fetch doctors for this clinic
          this.clinicChange(clinicAdminClinic);
        }
      }

      // For doctor, pre-select their clinic and themselves
      if (this.isDoctor) {
        // Get the doctor's clinic ID
        const clinicId = this.userData.clinic_id || this.userData.user_clinic_id;
        console.log("Doctor clinic ID:", clinicId);

        // Find the clinic in the store
        const doctorClinic = this.$store.state.clinic.find(
          clinic => parseInt(clinic.id) === parseInt(clinicId)
        );

        console.log("Found clinic for doctor:", doctorClinic);

        if (doctorClinic) {
          // Set the clinic
          this.appointmentData.clinic_id = doctorClinic;

          // Set the doctor to the current user
          this.appointmentData.doctor_id = {
            id: this.userData.ID,
            label: this.userData.display_name
          };

          // Fetch patients for this clinic
          this.clinicChange(doctorClinic);
        }
      }
    },

    fetchServices() {
      // Determine the doctor ID to use
      let doctorId;

      if (this.appointmentData.doctor_id?.id) {
        // Use the selected doctor if available
        doctorId = this.appointmentData.doctor_id.id;
      } else if (this.isDoctor) {
        // If user is a doctor, use their ID
        doctorId = this.userData.ID;
      } else {
        // Fallback to the doctorId prop
        doctorId = this.doctorId;
      }

      // Determine the clinic ID to use
      let clinicId;

      if (this.appointmentData.clinic_id?.id) {
        // Use the selected clinic if available
        clinicId = this.appointmentData.clinic_id.id;
      } else {
        // Fallback to the clinic_id property
        clinicId = this.appointmentData.clinic_id;
      }

      console.log("Fetching services for doctor:", doctorId, "and clinic:", clinicId);

      get("service_list", {
        module_type: "appointment_form",
        limit: 0,
        doctor_id: doctorId,
        without_currency: "yes",
        clinic_id: clinicId,
      })
        .then((response) => {
          this.appointmentTypes = response.data.data;
          if (response.data.data.length > 0) {
            this.services = response.data.data.map((val) => ({
              id: val.service_id,
              label: val.name || val.full_service_name,
              price: parseFloat(val.service_base_price || val.charges || 0),
              service_type: val.service_type,
              duration: val.duration,
              description: val.description,
            }));
          }
          this.isLoading = false;
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
          this.isLoading = false;
        });
    },
    checkWoocommerceActive() {
      get("get_payment_status", { status: "" })
        .then((response) => {
          this.woocommerceActive = response.data.data;
        })
        .catch((error) => {
          console.log("error", error);
        });
    },
    SendBillToPatient() {
      var element = $("#send_bill").find("i");
      element.removeClass("fa fa-paper-plane");
      element.addClass("fa fa-spinner fa-spin");
    },

    handleSubmit() {
      this.submitted = true;
      this.$v.$touch();

      if (this.$v.patientBillData.$invalid) {
        return;
      }

      if (!this.patientBillData.billItems?.length) {
        return;
      }

      let data = {
        ...this.patientBillData,
        appointment_id: this.appointmentData.appointment_id,
        checkOutVal: this.checkOutVal,
        doctor_id: this.doctorId,
        appointmentData:this.appointmentData
      };

      this.buttonText= "Loading..."
      post("patient_bill_save", data)
        .then((response) => {
          if (response.data.status) {
            this.$emit("onBillSaved", this.patientBillData);
            this.closeModal();
            this.buttonText= this.formTranslation.common.save
            this.$swal.fire({
                icon: "success",
                title: response.data.message,
              });
          }
        })
        .catch((error) => {
          this.buttonText= this.formTranslation.common.save
          console.error(error);
        });
    },
    afterTaxDetails() {
      if (!this.billItemEdit) {
        if (this.patientBillData.billItems === undefined) {
          this.patientBillData.billItems = [];
        }
        this.patientBillData?.billItems?.push(this.billItem);
      } else {
        this.updateBillTotal();
        this.billItemEdit = false;
      }

      this.billItemTitle = this.formTranslation.patient_bill.bill_add_item;
      this.billItemBtn =
        '<i class="fa fa-plus"></i> ' +
        this.formTranslation.patient_bill.bill_add_item;
      this.billItem = this.defaultBillingItemData();

      this.billItemSubmitted = false;
    },
    async deleteBillItem(index) {
      const billItem = this.patientBillData.billItems[index - 1];
      if (!billItem) return;

      if (billItem.id) {
        const result = await this.$swal.fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: this.formTranslation.common.press_yes_delete_billitems,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#dc3545",
          cancelButtonColor: "#6c757d",
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        });

        if (result.isConfirmed) {
          try {
            const { data } = await post("patient_bill_item_delete", {
              bill_item_id: billItem.id,
            });

            if (data.status) {
              this.patientBillData?.billItems?.splice(index - 1, 1);
              this.billItem = this.defaultBillingItemData();
              this.updateBillTotal()
              this.$swal.fire({
                icon: "success",
                title: data.message,
              });
            }
          } catch (error) {
            this.$swal.fire({
              icon: "error",
              title:
                error.response?.data?.message ||
                this.formTranslation.common.internal_server_error,
            });
          }
        }
      } else {
        this.patientBillData?.billItems?.splice(index - 1, 1);
        this.billItem = this.defaultBillingItemData();

      }
    },
    defaultMedicalRecordData() {
      return {
        title: "",
        encounter_id: 0,
        appointment_id: 0,
        doctor_id: 0,
        total_amount: 0,
        discount: 0,
        actual_amount: "",
        status: 0,
        billItems: [],
        payment_status: "unpaid"
      };
    },
    defaultBillingItemData() {
      return {
        item_id: "",
        qty: 1,
        price: 0,
        total: this.billItem.price * this.billItem.qty || 0,
      };
    },
    handleItemChange(selectedService, index) {
      const itemIndex = index - 1;

      if (selectedService && this.patientBillData.billItems[itemIndex]) {
        const currentItem = this.patientBillData.billItems[itemIndex];
        const currentPrice = currentItem.price;
        const isCustomPrice = currentItem.isCustomPrice;

        // With multi-select, we already have the full service object
        // Just ensure we have all the properties we need
        this.$set(this.patientBillData.billItems[itemIndex], "item_id", {
          id: selectedService.id,
          label: selectedService.label,
          price: selectedService.price,
          service_type: selectedService.service_type,
          duration: selectedService.duration,
          description: selectedService.description,
        });

        // Only update price if it's not a custom price
        if (!isCustomPrice) {
          this.$set(
            this.patientBillData.billItems[itemIndex],
            "price",
            selectedService.price
          );
        } else {
          // Keep the custom price
          this.$set(
            this.patientBillData.billItems[itemIndex],
            "price",
            currentPrice
          );
        }

        // Set quantity if not already set
        if (!currentItem.qty || currentItem.qty <= 0) {
          this.$set(this.patientBillData.billItems[itemIndex], "qty", 1);
        }

        // Calculate total for this item
        const total = this.patientBillData.billItems[itemIndex].price *
                     this.patientBillData.billItems[itemIndex].qty;
        this.$set(this.patientBillData.billItems[itemIndex], "total", total);

        // Update totals
        this.updateBillTotal();
      }
    },

    handleBillItemUnselect() {
      this.billItem.price = 0;
      this.billItem.qty = 1;
    },
    addServiceTag(newTag) {
      const tag = {
        id: newTag,
        label: newTag,
        price: 0,
      };
      this.billItem.item_id = tag;
      this.billItem.price = 0;
      this.billItem.qty = 1;
      this.billItem.total = 0;
      this.services.push(tag);
    },
    calculateGrandTotal() {
      this.patientBillData.total_amount =
        this.patientBillData?.billItems?.reduce((sum, item) => {
          return sum + parseFloat(item.price) * parseFloat(item.qty);
        }, 0);
    },
    editBill(encounterId) {
      if (parseInt(encounterId) !== 0) {
        get("patient_bill_detail", {
          encounter_id: encounterId,
        })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.isLoading = false;
              if (response.data.status) {
                if (
                  response.data.data.id !== undefined &&
                  response.data.data.id !== null &&
                  response.data.data.id !== ""
                ) {
                  this.patientBillData = response.data.data;
                }
              }
            }
          })
          .catch((error) => {
            this.isLoading = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      }
    },

    // Tax details section removed

    updateBillTotal() {
      let sum = 0;
      let paid_amount = 0;
      // Calculate sum of valid items only
      if (this.patientBillData.billItems?.length > 0) {
        paid_amount = this.patientBillData.billItems.reduce((total, item) => {
          if (item.payment_status === 'paid') {
            return total + parseFloat(item.price) * parseFloat(item.qty);
          }
          return total;
        }, 0);
      }

       // Calculate sum of valid items only
       if (this.patientBillData.billItems?.length > 0) {
        sum = this.patientBillData.billItems.reduce((total, item) => {
          if (item.price && item.qty) {
            return total + parseFloat(item.price) * parseFloat(item.qty);
          }
          return total;
        }, 0);
      }

      // Update totals using $set to ensure reactivity
      this.$set(this.patientBillData, "total_amount", sum);

      // For other statuses, keep paid_amount as is or initialize it if undefined
      if (!this.patientBillData.paid_amount) {
        this.$set(this.patientBillData, "paid_amount", paid_amount);
      }

      // Calculate actual amount (after discount)
      const actualAmount = sum - (parseFloat(this.patientBillData.discount) || 0) - (parseFloat(this.patientBillData.paid_amount) || 0);
      this.$set(this.patientBillData, "actual_amount", actualAmount);
    },

    billItemReset: function () {
      this.billItem = this.defaultBillingItemData();
    },

    closeModal() {
      this.$emit("update:showGenerateBillModal", false);
    },

    // Add to existing methods
    toggleEdit(index) {
      this.$set(this.patientBillData.billItems[index], "isEditing", true);
      this.$set(this.patientBillData.billItems[index], "_backup", {
        ...this.patientBillData.billItems[index],
      });
    },

    saveEdit(index) {
      const item = this.patientBillData.billItems[index];

      // Validate item before saving
      if (!item.item_id || !item.price || !item.qty) {
        this.$swal.fire({
          icon: "error",
          title: "Invalid Item",
          text: "Please fill in all required fields",
        });
        return;
      }

      // If the price has been changed from the original service price, mark it as custom
      if (item.item_id && item.item_id.price !== undefined && item.price !== item.item_id.price) {
        this.$set(item, "isCustomPrice", true);
      }

      delete item._backup;
      this.$set(item, "isEditing", false);
      this.updateBillTotal()
    },

    cancelEdit(index) {
      const backup = this.patientBillData.billItems[index]._backup;
      Object.keys(backup).forEach((key) => {
        if (key !== "_backup" && key !== "isEditing") {
          this.$set(this.patientBillData.billItems[index], key, backup[key]);
        }
      });
      this.$set(this.patientBillData.billItems[index], "isEditing", false);
    },

    handlePriceChange(index) {
      // Mark this price as custom so it won't be overwritten when service is selected
      this.$set(this.patientBillData.billItems[index], "isCustomPrice", true);

      // Update the bill total
      this.updateBillTotal();
    },

    addEmptyRow() {
      const newItem = {
        item_id: "",
        qty: 1,
        price: 0,
        isEditing: true,
        isCustomPrice: false,
        total: 0,
        payment_status:'unpaid'
      };
      if (!this.patientBillData.billItems) {
        this.patientBillData.billItems = [];
      }
      this.patientBillData.billItems.push(newItem);
      this.visible = true; // Show the form
    },
    paymentLink() {
      if (
        this.patientBillData?.billItems?.length === 0 &&
        this.woocommerceActive === "off"
      ) {
        displayErrorMessage(
          this.formTranslation.patient_bill.payment_or_bill_item_error
        );
        return;
      }
      post("send_payment_link", { id: this.encounterId })
        .then((response) => {
          displayMessage(response.data.message);
        })
        .catch((error) => {
          console.log("error", error);
        });
    },
    async clinicChange(clinicId) {
      if (!clinicId || !clinicId.id) {
        console.log("Invalid clinic ID:", clinicId);
        return;
      }

      console.log("Clinic changed to:", clinicId);

      try {
        // Only fetch doctors if the user is not a doctor
        if (!this.isDoctor) {
          this.doctorMultiselectLoader = true;
          const response = await get("get_static_data", {
            data_type: "get_users_by_clinic",
            clinic_id: clinicId.id,
            module_type: "appointment",
          });

          if (response.data.status === true) {
            this.doctors = response.data.data;
            console.log("Doctors loaded:", this.doctors.length);

            // If user is clinic admin and no doctor is selected, don't auto-select one
            if (this.isClinicAdmin && !this.appointmentData.doctor_id) {
              // Leave doctor selection empty for clinic admin to choose
            }
          }
        } else {
          // For doctor role, always set the doctor to the current user
          this.appointmentData.doctor_id = {
            id: this.userData.ID,
            label: this.userData.display_name
          };
        }
      } finally {
        this.doctorMultiselectLoader = false;
      }

      // Always fetch patients
      this.patientMultiselectLoader = true;
      get("get_static_data", {
          data_type: "users",
          user_type: 'kiviCare_patient',
          request_clinic_id: clinicId.id,
        })
          .then((response) => {
            this.patientMultiselectLoader = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.patients = response.data.data;
              console.log("Patients loaded:", this.patients.length);
            }
          })
          .catch((error) => {
            this.patientMultiselectLoader = false;
            console.log(error);
            displayErrorMessage("Internal server error");
          });

    },
  },
  watch: {
    "patientBillData.billItems": function () {
      this.updateBillTotal();
    },
    "patientBillData.total_amount": function () {
      this.patientBillData.actual_amount =
        this.patientBillData.total_amount - this.patientBillData.discount - this.patientBillData.paid_amount;
    },
    "billItem.price": function () {
      this.billItem.qty = this.billItem.qty == "" ? 0 : this.billItem.qty;
      this.billItem.price = this.billItem.price == "" ? 0 : this.billItem.price;
      this.billItem.total = isNaN(
        parseFloat(this.billItem.price) * parseFloat(this.billItem.qty)
      )
        ? 0
        : parseFloat(this.billItem.price) * parseFloat(this.billItem.qty);
    },
    "billItem.qty": function () {
      this.billItem.qty = this.billItem.qty == "" ? 0 : this.billItem.qty;
      this.billItem.price = this.billItem.price == "" ? 0 : this.billItem.price;
      this.billItem.total = isNaN(
        parseFloat(this.billItem.price) * parseFloat(this.billItem.qty)
      )
        ? 0
        : parseFloat(this.billItem.price) * parseFloat(this.billItem.qty);
    },
    "patientBillData.discount": function () {
      this.patientBillData.actual_amount =
        this.patientBillData.total_amount - this.patientBillData.discount - this.patientBillData.paid_amount;
    },

    encounterId(newVal) {
      this.patientBillData.encounter_id = newVal;
    },
  },
};
</script>
<style>
.accent-primary{
  accent-color: rgb(37 99 235/var(--tw-bg-opacity));
}

/* Fix for dropdown visibility */
.service-dropdown .multiselect__content-wrapper {
  z-index: 9999 !important;
  position: absolute !important;
  width: 100% !important;
  overflow: auto !important;
  max-height: 300px !important;
}

.service-dropdown .multiselect__element {
  z-index: 9999 !important;
}

/* Ensure dropdown appears above other elements */
.service-dropdown {
  position: relative !important;
  z-index: 999 !important;
}

/* Make sure the dropdown is visible and not cut off */
.multiselect__tags {
  overflow: visible !important;
}

.multiselect__content {
  overflow: visible !important;
}

/* Ensure the table doesn't clip the dropdown */
table {
  overflow: visible !important;
}
</style>