<template>
    <div class="w-full px-4">
        <div class="w-full">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="px-6 py-4 flex justify-between items-center border-b border-gray-200">
                        <h4 class="text-xl font-medium text-gray-800">{{ $t('Import TDL Lab Results') }}</h4>
                        <router-link to="/tdl-results"
                            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm leading-5 font-medium rounded-md text-gray-700 bg-white hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue">
                            <i class="fa fa-arrow-left mr-1"></i>
                            {{ $t('Back to Results') }}
                        </router-link>
                    </div>
                    <div class="p-6">
                        <div v-if="loading" class="text-center py-8">
                            <div
                                class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent">
                            </div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <form v-else @submit.prevent="importResults">
                            <!-- Clinic Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="clinic_id" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('Clinic') }} <span class="text-red-500">*</span>
                                    </label>
                                    <select
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        :class="{ 'border-red-500': validationErrors.clinic_id }" id="clinic_id"
                                        v-model="importData.clinic_id" required :disabled="importing">
                                        <option value="">{{ $t('Select Clinic') }}</option>
                                        <option v-for="clinic in clinics" :key="clinic.id" :value="clinic.id">
                                            {{ clinic.name }}
                                        </option>
                                    </select>
                                    <p v-if="validationErrors.clinic_id" class="mt-1 text-sm text-red-600">
                                        {{ $t('Please select a clinic') }}
                                    </p>
                                </div>
                                <div>
                                    <label for="format" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ $t('Result Format') }} <span class="text-red-500">*</span>
                                    </label>
                                    <select
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        :class="{ 'border-red-500': validationErrors.format }" id="format"
                                        v-model="importData.format" required :disabled="importing">
                                        <option value="">{{ $t('Select Format') }}</option>
                                        <option value="json">{{ $t('JSON') }}</option>
                                        <option value="hl7">{{ $t('HL7') }}</option>
                                        <option value="xml">{{ $t('XML') }}</option>
                                    </select>
                                    <p v-if="validationErrors.format" class="mt-1 text-sm text-red-600">
                                        {{ $t('Please select a format') }}
                                    </p>
                                </div>
                            </div>

                            <!-- Format Instructions -->
                            <div class="mb-6" v-if="importData.format">
                                <div class="bg-blue-50 border-l-4 border-blue-500 p-4 text-blue-700">
                                    <div class="font-medium">
                                        {{ getFormatInstructionsTitle() }}
                                    </div>
                                    <p class="mt-2">
                                        {{ getFormatInstructionsContent() }}
                                    </p>
                                    <hr class="my-2 border-blue-300">
                                    <p class="mt-2">
                                        {{ $t('Patient identifiers in the imported data must match existing patients in the system.') }}
                                    </p>
                                </div>
                            </div>

                            <!-- Result Content -->
                            <div class="mb-6">
                                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ $t('Result Content') }} <span class="text-red-500">*</span>
                                </label>
                                <textarea
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    :class="{ 'border-red-500': validationErrors.content }" id="content"
                                    v-model="importData.content" rows="15" required
                                    :placeholder="getContentPlaceholder()" :disabled="importing"></textarea>
                                <p v-if="validationErrors.content" class="mt-1 text-sm text-red-600">
                                    {{ validationMessages.content }}
                                </p>
                            </div>

                            <div class="mt-6 flex">
                                <button type="submit"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                    :disabled="importing || !isFormValid">
                                    <i v-if="importing" class="fa fa-sync fa-spin mr-2"></i>
                                    <i v-else class="fa fa-file-import mr-2"></i>
                                    {{ importing ? $t('Importing...') : $t('Import Results') }}
                                </button>
                                <button type="button"
                                    class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    @click="resetForm" :disabled="importing || isFormEmpty">
                                    <i class="fa fa-undo mr-2"></i>
                                    {{ $t('Reset Form') }}
                                </button>
                            </div>
                        </form>

                        <!-- JSON Example -->
                        <div v-if="importData.format === 'json'" class="mt-8 border-t border-gray-200 pt-6">
                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between">
                                    <h5 class="text-base font-medium text-gray-800">{{ $t('Example JSON Format') }}</h5>
                                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm"
                                        @click="copyExample('json')">
                                        <i
                                            :class="{ 'fa mr-1': true, 'fa-copy': !jsonCopied, 'fa-check': jsonCopied }"></i>
                                        {{ jsonCopied ? $t('Copied') : $t('Copy Example') }}
                                    </button>
                                </div>
                                <div class="p-4">
                                    <pre class="bg-gray-50 p-3 border border-gray-200 rounded-md overflow-auto text-xs"><code>{
  "patient_id": 123,
  "doctor_id": 456,
  "order_number": "TDL123456",
  "result_id": "TDL_RESULT_789",
  "result_date": "2023-04-15 14:30:00",
  "panels": [
    {
      "code": "LFTB",
      "name": "Liver Function Test Basic",
      "biomarkers": [
        {
          "name": "ALT",
          "value": "32",
          "units": "U/L",
          "reference_range": "0-35",
          "abnormal_flag": null,
          "observation_datetime": "2023-04-15 14:30:00"
        },
        {
          "name": "AST",
          "value": "45",
          "units": "U/L",
          "reference_range": "0-35",
          "abnormal_flag": "H",
          "observation_datetime": "2023-04-15 14:30:00"
        }
      ]
    },
    {
      "code": "FBCP",
      "name": "Full Blood Count",
      "biomarkers": [
        {
          "name": "WBC",
          "value": "5.5",
          "units": "10^9/L",
          "reference_range": "4.0-11.0",
          "abnormal_flag": null,
          "observation_datetime": "2023-04-15 14:30:00"
        },
        {
          "name": "Hb",
          "value": "13.5",
          "units": "g/dL",
          "reference_range": "13.5-17.5",
          "abnormal_flag": null,
          "observation_datetime": "2023-04-15 14:30:00"
        }
      ]
    }
  ]
}</code></pre>
                                </div>
                            </div>
                        </div>

                        <!-- HL7 Example -->
                        <div v-if="importData.format === 'hl7'" class="mt-8 border-t border-gray-200 pt-6">
                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between">
                                    <h5 class="text-base font-medium text-gray-800">{{ $t('Example HL7 Format') }}</h5>
                                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm"
                                        @click="copyExample('hl7')">
                                        <i :class="{ 'fa mr-1': true, 'fa-copy': !hl7Copied, 'fa-check': hl7Copied }"></i>
                                        {{ hl7Copied ? $t('Copied') : $t('Copy Example') }}
                                    </button>
                                </div>
                                <div class="p-4">
                                    <pre class="bg-gray-50 p-3 border border-gray-200 rounded-md overflow-auto text-xs"><code>MSH|^~\&|TDL|TDL|||20230415143000||ORU^R01|TDL20230415143000|P|2.3
PID|1|123456|123456||Smith^John||19750610|M
OBR|1|TDL123456||LFTB^Liver Function Test Basic|||20230415143000|||||||||456^Doe^Jane
OBX|1|NM|ALT^ALT||32|U/L|0-35|N|||F
OBX|2|NM|AST^AST||45|U/L|0-35|H|||F
OBR|2|TDL123456||FBCP^Full Blood Count|||20230415143000|||||||||456^Doe^Jane
OBX|3|NM|WBC^WBC||5.5|10^9/L|4.0-11.0|N|||F
OBX|4|NM|HB^Hb||13.5|g/dL|13.5-17.5|N|||F</code></pre>
                                </div>
                            </div>
                        </div>

                        <!-- XML Example -->
                        <div v-if="importData.format === 'xml'" class="mt-8 border-t border-gray-200 pt-6">
                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between">
                                    <h5 class="text-base font-medium text-gray-800">{{ $t('Example XML Format') }}</h5>
                                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm"
                                        @click="copyExample('xml')">
                                        <i :class="{ 'fa mr-1': true, 'fa-copy': !xmlCopied, 'fa-check': xmlCopied }"></i>
                                        {{ xmlCopied ? $t('Copied') : $t('Copy Example') }}
                                    </button>
                                </div>
                                <div class="p-4">
                                    <pre class="bg-gray-50 p-3 border border-gray-200 rounded-md overflow-auto text-xs"><code>&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;TDLResults&gt;
  &lt;patient_id&gt;123&lt;/patient_id&gt;
  &lt;doctor_id&gt;456&lt;/doctor_id&gt;
  &lt;order_number&gt;TDL123456&lt;/order_number&gt;
  &lt;result_id&gt;TDL_RESULT_789&lt;/result_id&gt;
  &lt;result_date&gt;2023-04-15 14:30:00&lt;/result_date&gt;
  &lt;panels&gt;
    &lt;panel&gt;
      &lt;code&gt;LFTB&lt;/code&gt;
      &lt;name&gt;Liver Function Test Basic&lt;/name&gt;
      &lt;biomarkers&gt;
        &lt;biomarker&gt;
          &lt;name&gt;ALT&lt;/name&gt;
          &lt;value&gt;32&lt;/value&gt;
          &lt;units&gt;U/L&lt;/units&gt;
          &lt;reference_range&gt;0-35&lt;/reference_range&gt;
          &lt;abnormal_flag&gt;&lt;/abnormal_flag&gt;
          &lt;observation_datetime&gt;2023-04-15 14:30:00&lt;/observation_datetime&gt;
        &lt;/biomarker&gt;
        &lt;biomarker&gt;
          &lt;name&gt;AST&lt;/name&gt;
          &lt;value&gt;45&lt;/value&gt;
          &lt;units&gt;U/L&lt;/units&gt;
          &lt;reference_range&gt;0-35&lt;/reference_range&gt;
          &lt;abnormal_flag&gt;H&lt;/abnormal_flag&gt;
          &lt;observation_datetime&gt;2023-04-15 14:30:00&lt;/observation_datetime&gt;
        &lt;/biomarker&gt;
      &lt;/biomarkers&gt;
    &lt;/panel&gt;
    &lt;panel&gt;
      &lt;code&gt;FBCP&lt;/code&gt;
      &lt;name&gt;Full Blood Count&lt;/name&gt;
      &lt;biomarkers&gt;
        &lt;biomarker&gt;
          &lt;name&gt;WBC&lt;/name&gt;
          &lt;value&gt;5.5&lt;/value&gt;
          &lt;units&gt;10^9/L&lt;/units&gt;
          &lt;reference_range&gt;4.0-11.0&lt;/reference_range&gt;
          &lt;abnormal_flag&gt;&lt;/abnormal_flag&gt;
          &lt;observation_datetime&gt;2023-04-15 14:30:00&lt;/observation_datetime&gt;
        &lt;/biomarker&gt;
        &lt;biomarker&gt;
          &lt;name&gt;Hb&lt;/name&gt;
          &lt;value&gt;13.5&lt;/value&gt;
          &lt;units&gt;g/dL&lt;/units&gt;
          &lt;reference_range&gt;13.5-17.5&lt;/reference_range&gt;
          &lt;abnormal_flag&gt;&lt;/abnormal_flag&gt;
          &lt;observation_datetime&gt;2023-04-15 14:30:00&lt;/observation_datetime&gt;
        &lt;/biomarker&gt;
      &lt;/biomarkers&gt;
    &lt;/panel&gt;
  &lt;/panels&gt;
&lt;/TDLResults&gt;</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
    name: 'TDLImportResults',
    data() {
        return {
            loading: true,
            importing: false,
            clinics: [],
            jsonCopied: false,
            hl7Copied: false,
            xmlCopied: false,
            validationErrors: {
                clinic_id: false,
                format: false,
                content: false
            },
            validationMessages: {
                content: ''
            },
            importData: {
                clinic_id: '',
                format: '',
                content: ''
            },
            examples: {
                json: `{
  "patient_id": 123,
  "doctor_id": 456,
  "order_number": "TDL123456",
  "result_id": "TDL_RESULT_789",
  "result_date": "2023-04-15 14:30:00",
  "panels": [
    {
      "code": "LFTB",
      "name": "Liver Function Test Basic",
      "biomarkers": [
        {
          "name": "ALT",
          "value": "32",
          "units": "U/L",
          "reference_range": "0-35",
          "abnormal_flag": null,
          "observation_datetime": "2023-04-15 14:30:00"
        },
        {
          "name": "AST",
          "value": "45",
          "units": "U/L",
          "reference_range": "0-35",
          "abnormal_flag": "H",
          "observation_datetime": "2023-04-15 14:30:00"
        }
      ]
    },
    {
      "code": "FBCP",
      "name": "Full Blood Count",
      "biomarkers": [
        {
          "name": "WBC",
          "value": "5.5",
          "units": "10^9/L",
          "reference_range": "4.0-11.0",
          "abnormal_flag": null,
          "observation_datetime": "2023-04-15 14:30:00"
        },
        {
          "name": "Hb",
          "value": "13.5",
          "units": "g/dL",
          "reference_range": "13.5-17.5",
          "abnormal_flag": null,
          "observation_datetime": "2023-04-15 14:30:00"
        }
      ]
    }
  ]
}`,
                hl7: `MSH|^~\\&|TDL|TDL|||20230415143000||ORU^R01|TDL20230415143000|P|2.3
PID|1|123456|123456||Smith^John||19750610|M
OBR|1|TDL123456||LFTB^Liver Function Test Basic|||20230415143000|||||||||456^Doe^Jane
OBX|1|NM|ALT^ALT||32|U/L|0-35|N|||F
OBX|2|NM|AST^AST||45|U/L|0-35|H|||F
OBR|2|TDL123456||FBCP^Full Blood Count|||20230415143000|||||||||456^Doe^Jane
OBX|3|NM|WBC^WBC||5.5|10^9/L|4.0-11.0|N|||F
OBX|4|NM|HB^Hb||13.5|g/dL|13.5-17.5|N|||F`,
                xml: `<?xml version="1.0" encoding="UTF-8"?>
<TDLResults>
  <patient_id>123</patient_id>
  <doctor_id>456</doctor_id>
  <order_number>TDL123456</order_number>
  <result_id>TDL_RESULT_789</result_id>
  <result_date>2023-04-15 14:30:00</result_date>
  <panels>
    <panel>
      <code>LFTB</code>
      <name>Liver Function Test Basic</name>
      <biomarkers>
        <biomarker>
          <name>ALT</name>
          <value>32</value>
          <units>U/L</units>
          <reference_range>0-35</reference_range>
          <abnormal_flag></abnormal_flag>
          <observation_datetime>2023-04-15 14:30:00</observation_datetime>
        </biomarker>
        <biomarker>
          <name>AST</name>
          <value>45</value>
          <units>U/L</units>
          <reference_range>0-35</reference_range>
          <abnormal_flag>H</abnormal_flag>
          <observation_datetime>2023-04-15 14:30:00</observation_datetime>
        </biomarker>
      </biomarkers>
    </panel>
    <panel>
      <code>FBCP</code>
      <name>Full Blood Count</name>
      <biomarkers>
        <biomarker>
          <name>WBC</name>
          <value>5.5</value>
          <units>10^9/L</units>
          <reference_range>4.0-11.0</reference_range>
          <abnormal_flag></abnormal_flag>
          <observation_datetime>2023-04-15 14:30:00</observation_datetime>
        </biomarker>
        <biomarker>
          <name>Hb</name>
          <value>13.5</value>
          <units>g/dL</units>
          <reference_range>13.5-17.5</reference_range>
          <abnormal_flag></abnormal_flag>
          <observation_datetime>2023-04-15 14:30:00</observation_datetime>
        </biomarker>
      </biomarkers>
    </panel>
  </panels>
</TDLResults>`
            }
        };
    },
    computed: {
        isFormValid() {
            return !!this.importData.clinic_id &&
                !!this.importData.format &&
                !!this.importData.content;
        },
        isFormEmpty() {
            return !this.importData.clinic_id &&
                !this.importData.format &&
                !this.importData.content;
        }
    },
    created() {
        this.getClinicList();
    },
    methods: {
        getClinicList() {
            this.isLoadingClinics = true;
            get("get_static_data", {
                data_type: "clinic_list",
            })
                .then((response) => {
                    this.clinicMultiselectLoader = false;
                    if (
                        response.data.status !== undefined &&
                        response.data.status === true
                    ) {
                        this.clinicList = response.data.data;
                    }
                })
                .catch((error) => {
                    this.clinicMultiselectLoader = false;
                    console.log(error);
                    displayErrorMessage(
                        this.formTranslation.common.internal_server_error
                    );
                });
        },
        getFormatInstructionsTitle() {
            switch (this.importData.format) {
                case 'json':
                    return this.$t('JSON Format Instructions');
                case 'hl7':
                    return this.$t('HL7 Format Instructions');
                case 'xml':
                    return this.$t('XML Format Instructions');
                default:
                    return this.$t('Format Instructions');
            }

            return isValid;
        },
        importResults() {
            if (!this.validateImportData()) {
                return;
            }

            this.importing = true;

            post('tdl_import_test_results', this.importData)
                .then(response => {
                    this.importing = false;

                    if (response.data.status === true) {
                        displayMessage(this.$t('Results imported successfully.'));

                        // Navigate to the result details page if we have a result ID
                        if (response.data.data && response.data.data.result_id) {
                            this.$router.push('/tdl-result/' + response.data.data.result_id);
                        } else {
                            // Navigate to the results list
                            this.$router.push('/tdl-results');
                        }
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to import results.'));
                    }
                })
                .catch(error => {
                    this.importing = false;
                    console.error('Error importing results:', error);

                    let errorMessage = this.$t('Failed to import results.');
                    if (error.response && error.response.data && error.response.data.message) {
                        errorMessage = error.response.data.message;
                    }

                    displayErrorMessage(errorMessage);
                });
        },
        getFormatInstructionsContent() {
            switch (this.importData.format) {
                case 'json':
                    return this.$t('Paste TDL JSON format results in the text area below. The JSON should contain patient_id, result information and biomarker values with reference ranges.');
                case 'hl7':
                    return this.$t('Paste TDL HL7 ORU^R01 message format results in the text area below. The content should follow the HL7 v2.3.1 standard and contain MSH, PID, OBR, and OBX segments.');
                case 'xml':
                    return this.$t('Paste TDL XML format results in the text area below. The XML should contain patient_id, result information and biomarker values with reference ranges.');
                default:
                    return '';
            }
        },
        getContentPlaceholder() {
            switch (this.importData.format) {
                case 'json':
                    return this.$t('Paste TDL JSON result content here...');
                case 'hl7':
                    return this.$t('Paste TDL HL7 message content here...');
                case 'xml':
                    return this.$t('Paste TDL XML result content here...');
                default:
                    return this.$t('Select a format to see instructions...');
            }
        },
        resetForm() {
            if (this.importing) return;

            // Ask for confirmation if there's content
            if (!this.isFormEmpty) {
                if (confirm(this.$t('Are you sure you want to reset the form? All entered data will be lost.'))) {
                    this.importData = {
                        clinic_id: this.clinics.length === 1 ? this.clinics[0].id : '',
                        format: '',
                        content: ''
                    };

                    // Reset validation
                    Object.keys(this.validationErrors).forEach(key => {
                        this.validationErrors[key] = false;
                    });
                }
            }
        },
        copyExample(type) {
            if (!this.examples[type]) return;

            // Create a temporary textarea to copy the text
            const textarea = document.createElement('textarea');
            textarea.value = this.examples[type];
            textarea.setAttribute('readonly', '');
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);

            // Select and copy the text
            textarea.select();
            document.execCommand('copy');

            // Remove the textarea
            document.body.removeChild(textarea);

            // Update example content in form
            this.importData.content = this.examples[type];

            // Set the copied flag
            switch (type) {
                case 'json':
                    this.jsonCopied = true;
                    setTimeout(() => { this.jsonCopied = false; }, 2000);
                    break;
                case 'hl7':
                    this.hl7Copied = true;
                    setTimeout(() => { this.hl7Copied = false; }, 2000);
                    break;
                case 'xml':
                    this.xmlCopied = true;
                    setTimeout(() => { this.xmlCopied = false; }, 2000);
                    break;
            }
        },
        validateImportData() {
            let isValid = true;

            // Reset validation errors
            Object.keys(this.validationErrors).forEach(key => {
                this.validationErrors[key] = false;
            });

            // Validate clinic
            if (!this.importData.clinic_id) {
                this.validationErrors.clinic_id = true;
                isValid = false;
            }

            // Validate format
            if (!this.importData.format) {
                this.validationErrors.format = true;
                isValid = false;
            }

            // Validate content
            if (!this.importData.content) {
                this.validationErrors.content = true;
                this.validationMessages.content = this.$t('Please enter result content');
                isValid = false;
            } else {
                // Validate content format
                try {
                    switch (this.importData.format) {
                        case 'json':
                            JSON.parse(this.importData.content);
                            break;
                        case 'hl7':
                            if (!this.importData.content.includes('MSH|') ||
                                !this.importData.content.includes('PID|') ||
                                !this.importData.content.includes('OBR|')) {
                                this.validationErrors.content = true;
                                this.validationMessages.content = this.$t('Invalid HL7 format. Must include MSH, PID, and OBR segments.');
                                isValid = false;
                            }
                            break;
                        case 'xml':
                            if (!this.importData.content.includes('<?xml') ||
                                !this.importData.content.includes('<TDLResults>')) {
                                this.validationErrors.content = true;
                                this.validationMessages.content = this.$t('Invalid XML format. Must be a valid TDL XML results format.');
                                isValid = false;
                            }
                            break;
                    }
                } catch (error) {
                    this.validationErrors.content = true;
                    this.validationMessages.content = this.$t('Invalid format: ') + error.message;
                    isValid = false;
                }
            }

            return isValid;
        }
    }
}
</script>

<style scoped></style>