<?php
/*
* Direct access activity logs
* This is for debugging purposes only
*/

// Find the WordPress installation path
$path = __DIR__;
while (!file_exists($path . '/wp-config.php')) {
    $path = dirname($path);
    if ($path === '/' || empty($path)) {
        die('WordPress installation not found.');
    }
}
require_once($path . '/wp-load.php');

// Ensure user is logged in as admin
if (!current_user_can('administrator')) {
    wp_die('Access denied');
}

global $wpdb;
$table_name = $wpdb->prefix . 'kc_activity_logs';

// Make sure the activity logs table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
if (!$table_exists) {
    require_once(__DIR__ . '/app/database/kc-activity-log-db.php');
    kivicareCreateActivityLogTable();
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    if (!$table_exists) {
        die('Failed to create activity logs table');
    }
}

// Get all logs directly from the database
$logs = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY id DESC LIMIT 50");

// Get user details for each log
$processed_logs = [];
foreach ($logs as $log) {
    $user_data = get_userdata($log->user_id);
    $log->user_name = ($user_data) ? $user_data->display_name : 'Unknown User';
    $processed_logs[] = $log;
}

// Create a page to display the logs
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Activity Logs - Direct Access</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }
        h1 { color: #333; }
        .container { max-width: 1200px; margin: 0 auto; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
        tr:hover { background-color: #f5f5f5; }
        .empty { padding: 20px; text-align: center; color: #666; }
        
        /* Color coding for activity types */
        .activity-login { background-color: #e6f7ff; }
        .activity-logout { background-color: #f5f5f5; }
        .activity-appointment_created { background-color: #e6ffe6; }
        .activity-appointment_updated { background-color: #fff9e6; }
        .activity-appointment_deleted { background-color: #ffe6e6; }
        .activity-test_activity { background-color: #e6e6ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Activity Logs - Direct Access</h1>
        <p>This page bypasses the normal API routes and accesses the activity logs directly.</p>
        
        <?php if (empty($processed_logs)): ?>
            <div class="empty">No activity logs found.</div>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>User</th>
                        <th>Role</th>
                        <th>Activity Type</th>
                        <th>Description</th>
                        <th>Date/Time</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($processed_logs as $log): ?>
                        <tr class="activity-<?php echo esc_attr($log->activity_type); ?>">
                            <td><?php echo esc_html($log->id); ?></td>
                            <td><?php echo esc_html($log->user_name); ?></td>
                            <td><?php 
                                $role = $log->user_type;
                                // Use role mapping for consistent display
                                $role_map = [
                                    'kivicare_clinic_admin' => 'Clinic Admin',
                                    'kivicare_doctor' => 'Doctor',
                                    'kivicare_patient' => 'Patient',
                                    'kivicare_receptionist' => 'Admin Staff',
                                    'clinic_admin' => 'Clinic Admin',
                                    'doctor' => 'Doctor',
                                    'patient' => 'Patient',
                                    'receptionist' => 'Admin Staff',
                                    'administrator' => 'Administrator'
                                ];
                                
                                if (isset($role_map[$role])) {
                                    echo esc_html($role_map[$role]);
                                } else {
                                    // Clean up role display for other roles
                                    if (strpos($role, 'kivicare_') === 0) {
                                        $role = substr($role, 9);
                                    }
                                    echo esc_html(ucwords(str_replace('_', ' ', $role)));
                                }
                            ?></td>
                            <td><?php echo esc_html($log->activity_type); ?></td>
                            <td><?php echo esc_html($log->activity_description); ?></td>
                            <td><?php echo esc_html($log->created_at); ?></td>
                            <td><?php echo esc_html($log->ip_address); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
        
        <div>
            <h2>Create Test Log Entry</h2>
            <form method="post">
                <?php wp_nonce_field('create_test_log_nonce', 'nonce'); ?>
                <button type="submit" name="create_test_log" style="padding: 10px; cursor: pointer;">Create Test Log Entry</button>
            </form>
            
            <?php
            // Create a test log if requested
            if (isset($_POST['create_test_log']) && isset($_POST['nonce']) && wp_verify_nonce($_POST['nonce'], 'create_test_log_nonce')) {
                $user_id = get_current_user_id();
                $user_data = get_userdata($user_id);
                $user_role = reset($user_data->roles);
                
                $result = $wpdb->insert(
                    $table_name,
                    [
                        'user_id' => $user_id,
                        'user_type' => $user_role,
                        'activity_type' => 'test_activity',
                        'activity_description' => 'Test activity log entry from direct access page',
                        'ip_address' => $_SERVER['REMOTE_ADDR'],
                        'created_at' => current_time('mysql')
                    ]
                );
                
                if ($result) {
                    echo '<p style="color: green;">Test log created successfully!</p>';
                } else {
                    echo '<p style="color: red;">Failed to create test log: ' . $wpdb->last_error . '</p>';
                }
                
                // Refresh the page after 1 second to show the new log
                echo '<script>setTimeout(function() { window.location.reload(); }, 1000);</script>';
            }
            ?>
        </div>
    </div>
</body>
</html>