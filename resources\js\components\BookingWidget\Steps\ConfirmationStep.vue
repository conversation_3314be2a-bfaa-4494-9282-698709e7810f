<template>
  <div class="kivi-booking-step" id="step-confirm">
    <!-- Logo<PERSON> in Top Right Corner -->
    <div class="kivi-logout-container my-4">
      <button @click="handleLogout" class="kivi-logout-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="kivi-logout-icon">
          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
          <polyline points="16 17 21 12 16 7"></polyline>
          <line x1="21" y1="12" x2="9" y2="12"></line>
        </svg>
        Logout
      </button>
    </div>

    <!-- Payment Success Screen -->
    <payment-success
      v-if="showSuccessMessage"
      :appointment-id="appointmentId"
      :booking-data="bookingData"
      :services-total="getServicesTotal()"
    />

    <!-- Payment Error Screen -->
    <payment-error
      v-else-if="showErrorMessage"
      :error-message="errorMessage"
      :appointment-id="appointmentId"
      @retry-payment="retryPayment"
      @change-payment-method="changePaymentMethod"
    />

    <!-- Confirmation HTML from server -->
    <div v-else-if="confirmationHtml" class="kivi-confirmation-page">
      <div v-html="confirmationHtml"></div>

      <!-- Error Message -->
      <div v-if="bookingError" class="kivi-booking-error">
        {{ bookingError }}
      </div>
    </div>
    <div v-else class="kivi-confirmation-page">
      <!-- Loading or error state -->
      <div v-if="isLoading" class="kivi-loading">
        <div class="kivi-spinner-large"></div>
        <p>Loading appointment details...</p>
      </div>
      <div v-else-if="bookingError" class="kivi-booking-error">
        {{ bookingError }}
      </div>
      <div v-else class="kivi-empty-state">
        <p>Loading...</p>
      </div>
    </div>
  </div>
</template>

<script>
import PaymentSuccess from '../Payment/PaymentSuccess.vue';
import PaymentError from '../Payment/PaymentError.vue';

export default {
  name: 'ConfirmationStep',
  components: {
    PaymentSuccess,
    PaymentError
  },
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isLoading: false,
      bookingInProgress: false,
      showSuccessMessage: false,
      showErrorMessage: false,
      bookingError: null,
      successMessage: '',
      errorMessage: '',
      appointmentId: null,
      confirmationHtml: null,
      taxDetails: [],
      selectedPaymentMethod: 'paymentStripepay'
    };
  },
  async created() {
    this.resetStates();
    this.ensureServicePrices();

    if (this.isNewBookingFlow()) {
      this.clearPaymentData();
    }

    await this.handlePaymentStatus();
    this.$root.$on('confirmation-page-loaded', this.setConfirmationPage);
    this.loadConfirmationHtml();

    if (this.isNewBookingFlow() && !this.confirmationHtml) {
      await this.fetchConfirmationAndPaymentOptions();
    }
  },

  beforeDestroy() {
    // Remove event listener
    this.$root.$off('confirmation-page-loaded', this.setConfirmationPage);
  },
  methods: {
    resetStates() {
      Object.assign(this, {
        showErrorMessage: false,
        showSuccessMessage: false,
        errorMessage: '',
        bookingError: null
      });
    },

    isNewBookingFlow() {
      return this.bookingData &&
             this.bookingData.clinic &&
             this.bookingData.doctor &&
             this.bookingData.date &&
             this.bookingData.time;
    },

    loadConfirmationHtml() {
      if (this.bookingData && this.bookingData.confirmationHtml) {
        this.confirmationHtml = this.bookingData.confirmationHtml;
        this.taxDetails = this.bookingData.taxDetails || [];
      }
    },

    async handlePaymentStatus() {
      // Check URL parameters for payment status
      const urlParams = new URLSearchParams(window.location.search);
      const isSuccess = urlParams.get('stripe_success') === '1' ||
                        urlParams.get('stripe_success') === 'true' ||
                        urlParams.get('payment_success') === '1' ||
                        urlParams.get('payment_success') === 'true' ||
                        urlParams.get('kivicare_stripe_payment') === 'success' ||
                        urlParams.get('kivicare_payment') === 'success';

      const isError = urlParams.get('stripe_cancel') === '1' ||
                      urlParams.get('stripe_cancel') === 'true' ||
                      urlParams.get('payment_error') === '1' ||
                      urlParams.get('payment_error') === 'true' ||
                      urlParams.get('kivicare_stripe_payment') === 'failed' ||
                      urlParams.get('kivicare_payment') === 'failed';

      // Get appointment ID from URL or session storage
      const urlAppointmentId = urlParams.get('appointment_id');
      const sessionAppointmentId = sessionStorage.getItem('kivicare_appointment_id');
      const appointmentId = urlAppointmentId || sessionAppointmentId;

      // If we're on the appointment page with payment status parameters,
      // the PHP code will handle displaying the success/error content
      if ((isSuccess || isError) && appointmentId && window.location.pathname.includes('/appointment/')) {
        // Just clean up session storage and return
        sessionStorage.removeItem('kivicare_appointment_id');
        sessionStorage.removeItem('kivicare_payment_method');
        return;
      }

      if (appointmentId) {
        this.appointmentId = appointmentId;

        if (isSuccess) {
          // Show success message and update payment status
          this.displayPaymentSuccess();
          this.updatePaymentStatus(appointmentId, 'approved');
          sessionStorage.removeItem('kivicare_appointment_id');
          sessionStorage.removeItem('kivicare_payment_method');

          // Redirect to success page if not already there
          if (!window.location.pathname.includes('payment-success')) {
            window.location.href = `${window.location.origin}/payment-success/?appointment_id=${appointmentId}&stripe_success=1`;
          }
        } else if (isError) {
          // Show error message
          this.errorMessage = urlParams.get('error_message') ||
                             'Payment was cancelled or failed. Please try again or choose another payment method.';
          this.displayPaymentError(this.errorMessage);

          // Redirect to error page if not already there
          if (!window.location.pathname.includes('payment-error')) {
            window.location.href = `${window.location.origin}/payment-error/?appointment_id=${appointmentId}&stripe_cancel=1`;
          }
        } else if (sessionStorage.getItem('kivicare_payment_method') === 'stripe') {
          // Check payment status on the server
          await this.checkPaymentStatus(appointmentId);
        }
      } else {
        // Check if the user is logged in
        const isLoggedIn = await this.checkUserLoggedIn();
        if (!isLoggedIn) {
          this.$emit('go-to-login-register');
        }
      }
    },

    preparePaymentRequestData() {
      // Get selected services as an array of IDs
      const serviceIds = this.bookingData.services.map(service => service.service_id);

      // Get description from the user input or patient notes
      const description = this.bookingData.description || this.bookingData.patient?.notes || '';

      // Check if any service is a telemedicine service
      const hasTelemedService = this.bookingData.services.some(service =>
        service.telemed_service === 'yes' ||
        service.serviceType === 'virtual' ||
        service.type === 'virtual'
      );

      // Return the formatted request data in the exact format required by the API
      return {
        clinic_id: this.bookingData.clinic?.id || null,
        doctor_id: this.bookingData.doctor?.id || null,
        service_list: serviceIds,
        time: this.formatTimeToAMPM(this.bookingData.time),
        date: this.bookingData.date,
        description: description,
        file: this.bookingData.files || [],
        custom_fields: this.bookingData.custom_fields || {},
        enableTeleMed: hasTelemedService ? 'true' : '',
        route_name: 'appointment_confirm_page'
      };
    },



    // Book appointment via API
    async bookAppointment() {
      if (this.bookingInProgress) return;

      this.bookingInProgress = true;
      this.bookingError = null;

      try {
        // Use the unified payload preparation and API call
        const payload = this.prepareSaveAppointmentPayload();
        const result = await this.callSaveAppointmentApi(payload);

        if (result.success) {
          this.$emit('payment-method-selected', this.selectedPaymentMethod);
          this.$emit('appointment-booked', { id: result.appointmentId, status: true });
        } else {
          this.bookingError = result.message || 'Failed to book appointment. Please try again.';
        }
      } catch (error) {
        this.bookingError = 'An error occurred while booking your appointment. Please try again.';
      } finally {
        this.bookingInProgress = false;
      }
    },

    // Prepare common appointment payload for API
    prepareSaveAppointmentPayload(appointmentId = null, paymentMode = null) {
      const patientId = this.bookingData.patient?.id ||
                       (window.ajaxData?.current_user_id || 0);

      // Format services data
      const visitTypeData = this.bookingData.services.map(service => ({
        id: service.id,
        service_id: service.service_id || service.id,
        name: service.name,
        charges: service.price
      }));

      // Check if any service is a telemedicine service
      const hasTelemedService = this.bookingData.services.some(service =>
        service.telemed_service === 'yes' ||
        service.serviceType === 'virtual' ||
        service.type === 'virtual'
      );

      // Format time to AM/PM format if needed
      const formattedTime = this.formatTimeToAMPM(this.bookingData.time);
      const customField = this.bookingData.customField || {};

      // Get nonce from available sources
      const nonce = this.getNonce();
      // Prepare the common payload
      const payload = {
        action: 'ajax_post',
        route_name: 'save_appointment',
        appointment_start_date: this.bookingData.date,
        appointment_start_time: formattedTime,
        visit_type: visitTypeData,
        patient_id: patientId,
        doctor_id: { id: this.bookingData.doctor?.id || null },
        clinic_id: { id: this.bookingData.clinic?.id || null },
        status: 1,
        enableTeleMed: hasTelemedService ? 'true' : 'false',
        file: [],
        custom_fields: customField,
        description: this.bookingData.description || this.bookingData.patient?.notes || '',
        widgetType: 'phpWidget',
        payment_mode: paymentMode || this.selectedPaymentMethod,
        g_recaptcha_response: '',
        tax: [],
        pageId: parseInt(window.request_data?.page_id) || 0,
        _ajax_nonce: nonce
      };

      // Add appointment ID if provided
      if (appointmentId) {
        payload.appointment_id = appointmentId;
      }

      return payload;
    },

    // Call the save_appointment API with the prepared payload
    async callSaveAppointmentApi(payload, options = {}) {
      const { returnFullResponse = false } = options;
      const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';

      try {
        console.log('Making API call to:', ajaxurl);
        console.log('With payload:', payload);

        const flattenedPayload = this.flattenObject(payload);
        console.log('Flattened payload:', flattenedPayload);

        const response = await fetch(ajaxurl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: new URLSearchParams(flattenedPayload)
        });

        if (!response.ok) {
          console.error('API response not OK:', response.status, response.statusText);
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseText = await response.text();
        console.log('Raw API response:', responseText);

        let responseData;
        try {
          responseData = JSON.parse(responseText);
          console.log('Parsed API response:', responseData);
        } catch (parseError) {
          console.error('Error parsing JSON response:', parseError);
          console.error('Response text:', responseText);
          throw new Error('Invalid JSON response from server');
        }

        if (returnFullResponse) {
          return responseData;
        }

        const appointmentId = this.extractAppointmentId(responseData);
        console.log('Extracted appointment ID:', appointmentId);

        return {
          success: responseData.status || false,
          data: responseData.data || null,
          message: responseData.message || '',
          appointmentId: appointmentId
        };
      } catch (error) {
        console.error('Error in callSaveAppointmentApi:', error);
        return {
          success: false,
          data: null,
          message: 'An error occurred while processing your request: ' + error.message,
          error
        };
      }
    },

    // Legacy method for backward compatibility
    prepareAppointmentData() {
      const patientId = this.bookingData.patient?.id ||
                       (window.ajaxData?.current_user_id || 0);

      // Check if any service is a telemedicine service
      const hasTelemedService = this.bookingData.services.some(service =>
        service.telemed_service === 'yes' ||
        service.serviceType === 'virtual' ||
        service.type === 'virtual'
      );

      // Using any type to avoid TypeScript errors with dynamic properties
      const appointmentData = {
        clinic_id: this.bookingData.clinic?.id || null,
        doctor_id: this.bookingData.doctor?.id || null,
        appointment_start_date: this.bookingData.date,
        appointment_start_time: this.bookingData.time,
        visit_type: this.bookingData.services.map(s => s.service_id).join(','),
        description: this.bookingData.description || this.bookingData.patient?.notes || '',
        payment_mode: this.selectedPaymentMethod,
        status: 0, // Pending status
        patient_id: { id: patientId },
        enableTeleMed: hasTelemedService ? 'true' : '',
        custom_fields: this.bookingData.custom_fields || {}
      };

      // Add patient data if available
      if (this.bookingData.patient) {
        // Using bracket notation to avoid TypeScript errors with dynamic properties
        appointmentData['patient_name'] = this.bookingData.patient.name;
        appointmentData['patient_email'] = this.bookingData.patient.email;
        appointmentData['patient_phone'] = this.bookingData.patient.phone;
      }

      return appointmentData;
    },

    async checkPaymentStatus(appointmentId) {
      try {
        this.appointmentId = appointmentId;

        // Get nonce from available sources
        const nonce = this.getNonce();

        // Try to get payment status first
        const paymentStatus = await this.fetchPaymentStatus(appointmentId, nonce);

        if (paymentStatus) {
          this.handlePaymentStatusResult(paymentStatus);
        } else {
          // Fallback to appointment details if payment status fails
          const appointmentDetails = await this.fetchAppointmentDetails(appointmentId, nonce);

          if (appointmentDetails) {
            this.handleAppointmentDetailsResult(appointmentDetails);
          } else {
            this.displayPaymentError('Could not verify payment status. Please contact support.');
          }
        }
      } catch (error) {
        this.displayPaymentError('Error checking payment status. Please contact support.');
      }
    },

    getNonce() {
      // Use the backend-provided nonce from ajaxData first
      return window.ajaxData?.nonce ||
             window.ajaxData?.post_nonce ||
             window.request_data?.nonce ||
             '';
    },

    // Fetch payment status from API
    async fetchPaymentStatus(appointmentId, nonce) {
      const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';

      const params = {
        action: 'ajax_get',
        route_name: 'get_appointment_payment_status',
        appointment_id: appointmentId,
        _ajax_nonce: nonce
      };

      try {
        const response = await fetch(`${ajaxurl}?${new URLSearchParams(params).toString()}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        const data = await response.json();

        if (data.status && data.data) {
          return data.data;
        }
        return null;
      } catch (error) {
        return null;
      }
    },

    // Fetch appointment details from API
    async fetchAppointmentDetails(appointmentId, nonce) {
      const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';

      const params = {
        action: 'ajax_get',
        route_name: 'get_appointment_detail',
        id: appointmentId,
        _ajax_nonce: nonce
      };

      try {
        const response = await fetch(`${ajaxurl}?${new URLSearchParams(params).toString()}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        const data = await response.json();

        if (data.status && data.data) {
          return data.data;
        }
        return null;
      } catch (error) {
        return null;
      }
    },

    // Handle payment status result
    handlePaymentStatusResult(paymentData) {
      const isCompleted = ['paid', 'approved', 'completed'].includes(paymentData.payment_status);
      const isPending = paymentData.payment_status === 'pending';

      if (isCompleted) {
        this.displayPaymentSuccess();
        this.clearPaymentData();
      } else if (isPending) {
        this.displayPaymentError('Your payment is still being processed. Please wait or contact support if this persists.');
      } else {
        this.displayPaymentError('Payment was not completed. Please try again with a different payment method.');
      }
    },

    // Handle appointment details result
    handleAppointmentDetailsResult(appointmentData) {
      const isCompleted = ['paid', 'approved', 'completed'].includes(appointmentData.payment_status);

      if (isCompleted) {
        this.displayPaymentSuccess();
        this.clearPaymentData();
      } else {
        this.displayPaymentError('Payment was not completed. Please try again.');
      }
    },

    formatDate(dateStr) {
      if (!dateStr) return 'Not selected';
      return new Date(dateStr).toLocaleDateString(undefined, {
        weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'
      });
    },

    formatTime(timeStr) {
      if (!timeStr) return 'Not selected';
      const [hours, minutes] = timeStr.split(':');
      const hour = parseInt(hours);
      const hour12 = hour % 12 || 12;
      const ampm = hour >= 12 ? 'PM' : 'AM';
      return `${hour12}:${minutes} ${ampm}`;
    },

    // Calculate total price of all services
    getServicesTotal() {
      if (!this.bookingData.services || !this.bookingData.services.length) {
        return 0;
      }

      return this.bookingData.services.reduce((total, service) => {
        const priceSource = service.price || service.charges || service.amount || 0;
        return total + this.extractPriceValue(priceSource);
      }, 0);
    },

    // Extract numeric price value from various formats
    extractPriceValue(priceStr) {
      if (!priceStr) return 0;

      // Handle if it's already a number
      if (typeof priceStr === 'number') return priceStr;

      // Convert to string and extract numeric value
      const priceString = String(priceStr);
      const numStr = priceString.replace(/[^0-9.]/g, '');

      return parseFloat(numStr) || 0;
    },

    // Format price with currency symbol
    formatPrice(price) {
      let currencySymbol = '$';

      // Try to get currency symbol from first service
      if (this.bookingData.services?.length > 0) {
        const firstService = this.bookingData.services[0];
        if (firstService.price && typeof firstService.price === 'string') {
          const firstChar = firstService.price.trim().charAt(0);
          if (!/[0-9.]/.test(firstChar)) {
            currencySymbol = firstChar;
          }
        }
      }

      return `${currencySymbol}${price.toFixed(2)}`;
    },

    // Set confirmation page HTML from event
    setConfirmationPage(data) {
      if (data && data.html) {
        this.confirmationHtml = data.html;
        this.taxDetails = data.taxDetails || [];
      }
    },

    // Submit appointment to server
    async submitAppointment() {
      if (this.bookingInProgress) return;

      this.bookingInProgress = true;
      this.bookingError = null;

      console.log('submitAppointment called');

      try {
        // Check if user is logged in
        const isLoggedIn = await this.checkUserLoggedIn();
        if (!isLoggedIn) {
          this.bookingInProgress = false;
          this.$emit('go-to-login-register');
          return;
        }

        // Check for existing appointment
        const existingId = this.getExistingAppointmentId();

        console.log('Existing appointment ID:', existingId);

        if (existingId) {
          // For existing appointments, we'll use the Stripe payment URL directly
          if (this.selectedPaymentMethod === 'paymentStripepay') {
            console.log('Processing existing appointment with Stripe');

            // Use the unified payload preparation and API call
            const payload = this.prepareSaveAppointmentPayload(existingId, 'paymentStripepay');
            console.log('Prepared payload for Stripe payment:', payload);

            const result = await this.callSaveAppointmentApi(payload, { returnFullResponse: true });
            console.log('API response for Stripe payment:', result);

            if (result.status) {
              console.log('API call successful, checking for redirect URL');

              // Direct access to checkout_detail.stripe_redirect_url
              if (result.checkout_detail && result.checkout_detail.stripe_redirect_url) {
                console.log('Found stripe_redirect_url:', result.checkout_detail.stripe_redirect_url);
                window.location.href = result.checkout_detail.stripe_redirect_url;
                return;
              }

              // Direct access to checkout_detail.payment_url
              if (result.checkout_detail && result.checkout_detail.payment_url) {
                console.log('Found payment_url:', result.checkout_detail.payment_url);
                window.location.href = result.checkout_detail.payment_url;
                return;
              }

              // Try the findRedirectUrl method as a fallback
              const redirectUrl = this.findRedirectUrl(result);
              if (redirectUrl) {
                console.log('Found redirect URL via findRedirectUrl:', redirectUrl);
                window.location.href = redirectUrl;
                return;
              }

              // If we still don't have a URL, show an error
              this.bookingError = 'Payment gateway configuration error. Could not find payment URL.';
              console.error('No payment URL found in response:', result);
            } else {
              this.bookingError = result.message || 'Unable to process payment at this time';
              console.error('API returned error status:', result.message);
            }
          } else {
            // For non-Stripe payments, use the existing method
            await this.processExistingAppointment(existingId);
          }
        } else {
          console.log('Creating new appointment');
          // For new appointments
          const payload = this.prepareSaveAppointmentPayload(null, this.selectedPaymentMethod);
          console.log('Prepared payload for new appointment:', payload);

          const result = await this.callSaveAppointmentApi(payload, { returnFullResponse: true });
          console.log('API response for new appointment:', result);

          if (result.status) {
            // Extract appointment ID
            const appointmentId = this.extractAppointmentId(result);
            console.log('Extracted appointment ID:', appointmentId);

            if (appointmentId) {
              // Store appointment ID
              sessionStorage.setItem('kivicare_appointment_id', appointmentId);

              // For Stripe payments, handle redirection
              if (this.selectedPaymentMethod === 'paymentStripepay') {
                console.log('Processing Stripe payment for new appointment');

                // Direct access to checkout_detail.stripe_redirect_url
                if (result.checkout_detail && result.checkout_detail.stripe_redirect_url) {
                  console.log('Found stripe_redirect_url:', result.checkout_detail.stripe_redirect_url);
                  window.location.href = result.checkout_detail.stripe_redirect_url;
                  return;
                }

                // Direct access to checkout_detail.payment_url
                if (result.checkout_detail && result.checkout_detail.payment_url) {
                  console.log('Found payment_url:', result.checkout_detail.payment_url);
                  window.location.href = result.checkout_detail.payment_url;
                  return;
                }

                // Try the findRedirectUrl method as a fallback
                const redirectUrl = this.findRedirectUrl(result);
                if (redirectUrl) {
                  console.log('Found redirect URL via findRedirectUrl:', redirectUrl);
                  window.location.href = redirectUrl;
                  return;
                }

                // If we still don't have a URL, show an error
                this.bookingError = 'Payment gateway configuration error. Could not find payment URL.';
                console.error('No payment URL found in response:', result);
              } else {
                // For non-Stripe payments, emit the appointment-booked event
                this.$emit('appointment-booked', { id: appointmentId, status: true });
                this.$emit('next-step');
              }
            } else {
              this.bookingError = 'No appointment ID returned from server';
              console.error('No appointment ID in response:', result);
            }
          } else {
            this.bookingError = result.message || 'Failed to book appointment. Please try again.';
            console.error('API returned error status:', result.message);
          }
        }
      } catch (error) {
        console.error('Error in submitAppointment:', error);
        this.bookingError = 'An error occurred while booking your appointment. Please try again.';
        sessionStorage.removeItem('kivicare_appointment_in_progress');
      } finally {
        this.bookingInProgress = false;
      }
    },

    // Get existing appointment ID from session storage
    getExistingAppointmentId() {
      return sessionStorage.getItem('kivicare_appointment_id') ||
             sessionStorage.getItem('kivicare_appointment_in_progress');
    },

    // Process existing appointment
    async processExistingAppointment(appointmentId) {
      if (this.selectedPaymentMethod === 'paymentStripepay') {
        // Process with Stripe
        this.bookingError = null;
        this.showErrorMessage = false;
        this.getStripePaymentUrl(appointmentId);
      } else if (this.selectedPaymentMethod === 'payAtClinic') {
        // Update payment status to pending
        const nonce = this.getNonce();
        await this.updateAppointmentPaymentStatus(appointmentId, 'pending', 'payAtClinic', nonce);
        this.$emit('appointment-booked', { id: appointmentId, status: true });
        this.$emit('next-step');
      }
    },

    // Update appointment payment status
    async updateAppointmentPaymentStatus(appointmentId, status, paymentMode, nonce) {
      const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';

      const params = {
        action: 'ajax_post',
        route_name: 'save_appointment_payment_status',
        appointment_id: appointmentId,
        payment_status: status,
        payment_mode: paymentMode,
        _ajax_nonce: nonce
      };

      const response = await fetch(ajaxurl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(params)
      });

      return await response.json();
    },

    // Create new appointment
    async createNewAppointment() {
      // Set temporary flag
      sessionStorage.setItem('kivicare_appointment_in_progress', 'true');

      try {
        // Use the unified payload preparation and API call
        const payload = this.prepareSaveAppointmentPayload();
        const result = await this.callSaveAppointmentApi(payload);

        // Clear in-progress flag
        sessionStorage.removeItem('kivicare_appointment_in_progress');

        if (result.success && result.appointmentId) {
          // Store appointment ID
          sessionStorage.setItem('kivicare_appointment_id', result.appointmentId);

          // Process payment
          if (this.selectedPaymentMethod === 'paymentStripepay') {
            this.getStripePaymentUrl(result.appointmentId);
          } else {
            this.$emit('appointment-booked', { id: result.appointmentId, status: true });
            this.$emit('next-step');
          }
        } else {
          this.bookingError = result.message || 'Failed to book appointment. Please try again.';
        }
      } catch (error) {
        // Clear in-progress flag
        sessionStorage.removeItem('kivicare_appointment_in_progress');
        this.bookingError = 'An error occurred while booking your appointment. Please try again.';
      }
    },

    // Extract appointment ID from response
    extractAppointmentId(responseData) {
      if (responseData.data?.id) return responseData.data.id;
      if (responseData.appointment_id) return responseData.appointment_id;
      return null;
    },

    // Prepare visit type data from services (kept for backward compatibility)
    prepareVisitTypeData() {
      if (!this.bookingData.services) return [];

      return this.bookingData.services.map(service => ({
        id: service.id,
        service_id: service.service_id || service.id,
        name: service.name,
        charges: service.price
      }));
    },

    // Format time to AM/PM format
    formatTimeToAMPM(timeStr) {
      if (!timeStr) return '';

      try {
        // Check if the time is already in AM/PM format
        if (timeStr.toLowerCase().includes('am') || timeStr.toLowerCase().includes('pm')) {
          return timeStr; // Return as is if already in AM/PM format
        }

        const timeParts = timeStr.split(':');
        if (timeParts.length < 2) {
          console.warn('Invalid time format:', timeStr);
          return timeStr; // Return original if format is invalid
        }

        const hours = parseInt(timeParts[0]);
        const minutes = timeParts[1] || '00';

        if (isNaN(hours) || hours < 0 || hours > 23) {
          console.warn('Invalid hour value:', hours);
          return timeStr; // Return original if hour is invalid
        }

        const ampm = hours >= 12 ? 'pm' : 'am';
        const hour12 = hours % 12 || 12;

        return `${hour12}:${minutes} ${ampm}`;
      } catch (error) {
        console.error('Error formatting time:', error);
        return timeStr; // Return original time on error
      }
    },

    // Helper method to flatten nested objects for form submission
    flattenObject(obj, prefix = '') {
      const flattened = {};

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key];
          const newKey = prefix ? `${prefix}[${key}]` : key;

          // Special handling for visit_type array
          if (key === 'visit_type' && Array.isArray(value)) {
            value.forEach((item, index) => {
              if (typeof item === 'object' && item !== null) {
                for (const itemKey in item) {
                  if (item.hasOwnProperty(itemKey)) {
                    flattened[`${newKey}[${index}][${itemKey}]`] = item[itemKey];
                  }
                }
              } else {
                flattened[`${newKey}[${index}]`] = item;
              }
            });
          }
          // Special handling for patient_id
          else if (key === 'patient_id' && typeof value === 'object' && value !== null) {
            if (value.id) {
              flattened[newKey] = value.id; // Send patient_id directly as a number
            }
          }
          // Special handling for doctor_id and clinic_id objects
          else if ((key === 'doctor_id' || key === 'clinic_id') && typeof value === 'object' && value !== null) {
            if (value.id) {
              flattened[`${newKey}[id]`] = value.id;
            }
          }
          // Special handling for custom_fields object
          else if (key === 'custom_fields' && typeof value === 'object' && value !== null) {
            Object.keys(value).forEach(fieldKey => {
              flattened[`custom_field[custom_field_${fieldKey}]`] = value[fieldKey];
            });
          }
          // Special handling for custom_field object
          else if (key === 'custom_field' && typeof value === 'object' && value !== null) {
            Object.keys(value).forEach(fieldKey => {
              // Check if the key already has the 'custom_field_' prefix
              const finalKey = fieldKey.startsWith('custom_field_') ? fieldKey : `custom_field_${fieldKey}`;
              flattened[`custom_field[${finalKey}]`] = value[fieldKey];
            });
          }
          // General handling for other nested objects
          else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            Object.assign(flattened, this.flattenObject(value, newKey));
          }
          // General handling for arrays
          else if (Array.isArray(value)) {
            value.forEach((item, index) => {
              if (typeof item === 'object' && item !== null) {
                Object.assign(flattened, this.flattenObject(item, `${newKey}[${index}]`));
              } else {
                flattened[`${newKey}[${index}]`] = item;
              }
            });
          }
          // Simple values
          else {
            flattened[newKey] = value;
          }
        }
      }

      return flattened;
    },

    displayPaymentSuccess() {
      Object.assign(this, {
        showSuccessMessage: true,
        showErrorMessage: false,
        successMessage: 'Your appointment has been confirmed and payment has been processed successfully.',
        bookingInProgress: false
      });

      ['kivicare_appointment_in_progress', 'kivicare_payment_method'].forEach(key =>
        sessionStorage.removeItem(key)
      );

      this.$emit('payment-success');
    },

    displayPaymentError(errorMessage) {
      Object.assign(this, {
        showErrorMessage: true,
        showSuccessMessage: false,
        errorMessage: errorMessage || 'We were unable to process your payment. Please try again or choose another payment method.',
        bookingInProgress: false
      });

      sessionStorage.removeItem('kivicare_appointment_in_progress');
      this.$emit('payment-error', this.errorMessage);
    },

    // Update payment status on the server
    async updatePaymentStatus(appointmentId, status) {
      try {
        const nonce = this.getNonce();
        await this.updateAppointmentPaymentStatus(appointmentId, status, 'paymentStripepay', nonce);
      } catch (error) {
        // Non-critical error, so we don't need to show an error message
      }
    },

    // Navigate back to home page
    goHome() {
      window.location.href = '/';
    },

    // Retry the payment
    retryPayment() {
      if (this.appointmentId) {
        this.getStripePaymentUrl(this.appointmentId);
      } else {
        this.submitAppointment();
      }
    },

    // Change payment method
    changePaymentMethod() {
      this.showErrorMessage = false;
      this.showSuccessMessage = false;
      this.bookingError = null;
      this.errorMessage = '';
    },

    // Ensure all services have prices
    ensureServicePrices() {
      if (!this.bookingData.services?.length) return;

      // Make a copy of the services array
      const updatedServices = [...this.bookingData.services];
      let hasChanges = false;

      // Check each service and add default price if missing
      updatedServices.forEach(service => {
        if (!service.price && !service.charges && !service.amount) {
          // Try to get price from other properties
          if (service.charges) {
            service.price = service.charges;
          } else if (service.amount) {
            service.price = service.amount;
          } else {
            // No default price for production
            service.price = '';
          }
          hasChanges = true;
        }
      });

      // Update the services array if changes were made
      if (hasChanges) {
        this.bookingData.services = updatedServices;
      }
    },

    // Check if user is logged in
    async checkUserLoggedIn() {
      try {
        // Check if we have user data in the booking data
        if (this.bookingData.patient?.user_id) return true;

        // Check if WordPress has a logged-in user via ajaxData
        if (window.ajaxData?.is_user_logged_in === 'yes') return true;

        // Try to get user data from the API
        const userData = await this.fetchUserData();

        if (userData) {
          this.updatePatientData(userData);
          return true;
        }

        return false;
      } catch (error) {
        return false;
      }
    },

    // Fetch user data from API
    async fetchUserData() {
      const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
      const nonce = window.ajaxData?.get_nonce || '';

      const params = {
        action: 'ajax_get',
        route_name: 'login_user_detail',
        _ajax_nonce: nonce
      };

      try {
        const response = await fetch(`${ajaxurl}?${new URLSearchParams(params).toString()}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        const data = await response.json();

        if (data.status && data.data && Object.keys(data.data).length > 0) {
          return data.data;
        }
        return null;
      } catch (error) {
        return null;
      }
    },

    // Update patient data from user data
    updatePatientData(userData) {
      if (!userData.ID) return;

      if (!this.bookingData.patient) {
        this.bookingData.patient = {};
      }

      // Store the patient ID
      this.bookingData.patient.id = userData.ID;
      this.bookingData.patient.user_id = userData.ID;

      // Store display name if available
      if (userData.data?.display_name) {
        this.bookingData.patient.name = userData.data.display_name;
      }

      // Store email if available
      if (userData.data?.user_email) {
        this.bookingData.patient.email = userData.data.user_email;
      }
    },

    // Get Stripe payment URL for an appointment
    async getStripePaymentUrl(appointmentId) {
      if (this.bookingInProgress) return;

      try {
        this.bookingInProgress = true;
        this.bookingError = null;

        console.log('Starting Stripe payment process for appointment ID:', appointmentId);

        // Store appointment ID in session storage for return from Stripe
        if (appointmentId && appointmentId !== 'true') {
          sessionStorage.setItem('kivicare_appointment_id', appointmentId);
          sessionStorage.setItem('kivicare_payment_method', 'stripe');
          console.log('Stored appointment ID in session storage:', appointmentId);
        } else if (appointmentId === 'true') {
          this.bookingError = 'Appointment creation is still in progress. Please try again in a moment.';
          this.bookingInProgress = false;
          return;
        }

        // Get the base URL for success and error pages - use absolute URLs
        const baseUrl = window.location.origin;
        // Use the appointment URL with parameters for Stripe to redirect back to
        // The PHP code in bookAppointment.php will handle the payment status
        const successUrl = `${baseUrl}/appointment/?appointment_id=${appointmentId}&kivicare_stripe_payment=success`;
        const errorUrl = `${baseUrl}/appointment/?appointment_id=${appointmentId}&kivicare_stripe_payment=failed`;

        // Use the unified payload preparation and API call
        const payload = this.prepareSaveAppointmentPayload(appointmentId, 'paymentStripepay');

        // Add success and error URLs to the payload
        payload.return_url = successUrl;
        payload.cancel_url = errorUrl;

        console.log('Prepared payload for Stripe payment:', payload);

        const result = await this.callSaveAppointmentApi(payload, { returnFullResponse: true });
        console.log("Full API response:", JSON.stringify(result, null, 2));

        if (result.status) {
          console.log('API call successful, checking for redirect URL');

          // Check if checkout_detail exists
          if (!result.checkout_detail) {
            console.error('checkout_detail is missing in the response');
            this.bookingError = 'Payment gateway configuration error: Missing checkout details';
            return;
          }

          // Check if stripe_redirect_url exists
          if (!result.checkout_detail.stripe_redirect_url) {
            console.error('stripe_redirect_url is missing in checkout_detail');

            // Try payment_url as fallback
            if (result.checkout_detail.payment_url) {
              console.log('Using payment_url as fallback:', result.checkout_detail.payment_url);
              window.location.href = result.checkout_detail.payment_url;
              return;
            }

            this.bookingError = 'Payment gateway configuration error: Missing redirect URL';
            return;
          }

          // Use the stripe_redirect_url
          console.log('Found stripe_redirect_url:', result.checkout_detail.stripe_redirect_url);
          window.location.href = result.checkout_detail.stripe_redirect_url;
          return;
        } else {
          console.error('API returned error status:', result.message);
          this.bookingError = result.message || 'Unable to process payment at this time';
        }
      } catch (error) {
        console.error('Error in getStripePaymentUrl:', error);
        this.bookingError = 'There was a problem connecting to the payment gateway. Please try again.';
      } finally {
        this.bookingInProgress = false;
      }
    },

    clearPaymentData() {
      ['kivicare_appointment_id', 'kivicare_payment_method'].forEach(key =>
        sessionStorage.removeItem(key)
      );
      this.appointmentId = null;
    },

    // Handle logout button click
    async handleLogout() {
      try {
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        // Try to get the most current POST nonce
        let nonce = '';
        if (window.ajaxData && window.ajaxData.post_nonce) {
          nonce = window.ajaxData.post_nonce;
        } else if (window.request_data && window.request_data.nonce) {
          nonce = window.request_data.nonce;
        }

        console.log('Logging out user with nonce:', nonce);

        // Prepare logout request
        const params = {
          action: 'ajax_post',
          route_name: 'logout',
          _ajax_nonce: nonce
        };

        // Make the API request
        const response = await fetch(ajaxurl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: new URLSearchParams(params)
        });

        const responseData = await response.json();

        if (responseData.status) {
          console.log('Logout successful');
          // Clear any session/local storage data
          this.clearPaymentData();
          sessionStorage.clear();
          localStorage.removeItem('kivicare_login_user');

          // Reload the page to reset the booking flow
          window.location.reload();
        } else {
          console.error('Logout failed:', responseData.message);
          // Fallback: just reload the page anyway
          window.location.reload();
        }
      } catch (error) {
        console.error('Error during logout:', error);
        // Fallback: just reload the page anyway
        window.location.reload();
      }
    }
  }
};
</script>

<style scoped>
.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-logout-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}


.kivi-logout-button {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: var(--light-gray, #f9fafb);
  color: var(--gray, #6b7280);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.kivi-logout-button:hover {
  background-color: #f3f4f6;
  color: #4b5563;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.kivi-logout-icon {
  width: 16px;
  height: 16px;
}

.kivi-booking-summary {
  background-color: var(--light-gray);
  border-radius: var(--radius);
  padding: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
}

.kivi-summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.kivi-summary-label {
  font-weight: 500;
  color: var(--dark-gray);
  flex: 0 0 30%;
}

.kivi-summary-value {
  font-weight: 400;
  color: var(--black);
  flex: 0 0 70%;
  text-align: right;
}

.kivi-summary-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: right;
}

.kivi-summary-list li {
  margin-bottom: 0.25rem;
}

.kivi-service-price {
  font-weight: 500;
  color: var(--primary-color);
  margin-left: 0.5rem;
}

.kivi-summary-total {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
  font-weight: 600;
  font-size: 1rem;
}



/* Proceed button styles */
.kivi-payment-proceed {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.kivi-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.kivi-btn-primary {
  background-color: var(--primary-color, #4f46e5);
  color: white;
}

.kivi-btn-primary:hover {
  background-color: #4338ca;
}

.kivi-btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Loading state styles */
.kivi-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.kivi-spinner-large {
  width: 3rem;
  height: 3rem;
  border: 3px solid rgba(79, 70, 229, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color, #4f46e5);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

.kivi-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--gray, #6b7280);
}

.kivi-booking-error {
  margin-top: 1.5rem;
  padding: 0.75rem;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: var(--radius);
  color: #b91c1c;
  font-size: 0.875rem;
}

.kivi-booking-actions {
  margin-top: 2rem;
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.kivi-btn-large {
  padding: 0.875rem 2rem;
  font-size: 1rem;
}

.kivi-btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.kivi-btn-secondary:hover {
  background-color: #e5e7eb;
}

.kivi-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 640px) {
  .kivi-payment-option {
    flex: 1 0 100%;
  }
}

/* Confirmation page styles */
.kivi-confirmation-page {
  max-width: 800px;
  margin: 0 auto;
  text-align: left;
}

.kivi-confirmation-page h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-confirmation-page p {
  margin-bottom: 1rem;
}

.kivi-confirmation-page .grid {
  display: grid;
  grid-gap: 1.5rem;
}

.kivi-confirmation-page .grid-cols-1 {
  grid-template-columns: 1fr;
}

.kivi-confirmation-page .grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.kivi-confirmation-page .gap-6 {
  gap: 1.5rem;
}

.kivi-confirmation-page .bg-white {
  background-color: white;
}

.kivi-confirmation-page .rounded-lg {
  border-radius: 0.5rem;
}

.kivi-confirmation-page .border {
  border: 1px solid #e5e7eb;
}

.kivi-confirmation-page .p-4 {
  padding: 1rem;
}

.kivi-confirmation-page .space-y-6 > * + * {
  margin-top: 1.5rem;
}

.kivi-confirmation-page .space-y-4 > * + * {
  margin-top: 1rem;
}

.kivi-confirmation-page .space-y-3 > * + * {
  margin-top: 0.75rem;
}

.kivi-confirmation-page .space-y-2 > * + * {
  margin-top: 0.5rem;
}

.kivi-confirmation-page .flex {
  display: flex;
}

.kivi-confirmation-page .items-center {
  align-items: center;
}

.kivi-confirmation-page .items-start {
  align-items: flex-start;
}

.kivi-confirmation-page .justify-between {
  justify-content: space-between;
}

.kivi-confirmation-page .space-x-3 > * + * {
  margin-left: 0.75rem;
}

.kivi-confirmation-page .text-lg {
  font-size: 1.125rem;
}

.kivi-confirmation-page .text-left {
  text-align: left;
}

.kivi-confirmation-page .font-medium {
  font-weight: 500;
}

.kivi-confirmation-page .text-gray-900 {
  color: #111827;
}

.kivi-confirmation-page .text-gray-600 {
  color: #4b5563;
}

.kivi-confirmation-page .text-gray-400 {
  color: #9ca3af;
}

.kivi-confirmation-page .text-purple-600 {
  color: #9333ea;
}

.kivi-confirmation-page .mb-3 {
  margin-bottom: 0.75rem;
}

.kivi-confirmation-page .mb-4 {
  margin-bottom: 1rem;
}

.kivi-confirmation-page .mt-1 {
  margin-top: 0.25rem;
}

.kivi-confirmation-page .mr-3 {
  margin-right: 0.75rem;
}

.kivi-confirmation-page .w-5 {
  width: 1.25rem;
}

.kivi-confirmation-page .h-5 {
  height: 1.25rem;
}

.kivi-confirmation-page .py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.kivi-confirmation-page .border-t {
  border-top: 1px solid #e5e7eb;
}

.kivi-confirmation-page .pt-4 {
  padding-top: 1rem;
}

.kivi-confirmation-page .mt-4 {
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .kivi-confirmation-page .md\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
/* Payment Result Styles */
.kivi-payment-result {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  border-radius: var(--radius);
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.kivi-payment-success .kivi-payment-result-icon {
  color: #10b981; /* Green */
  background-color: rgba(16, 185, 129, 0.1);
}

.kivi-payment-error .kivi-payment-result-icon {
  color: #ef4444; /* Red */
  background-color: rgba(239, 68, 68, 0.1);
}

.kivi-payment-result-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 1rem;
}

.kivi-payment-result-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-payment-result-message {
  font-size: 1rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.kivi-payment-result-details {
  background-color: var(--light-gray, #f9fafb);
  border-radius: var(--radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: left;
}

.kivi-payment-result-details p {
  margin-bottom: 0.5rem;
}

.kivi-payment-result-details h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-payment-error-steps {
  list-style: none;
  padding: 0;
  margin: 0;
}

.kivi-payment-error-steps li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.kivi-payment-error-steps li:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--primary-color, #4f46e5);
  border-radius: 50%;
}

.kivi-payment-result-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

@media (max-width: 640px) {
  .kivi-payment-result {
    padding: 1.5rem;
  }

  .kivi-payment-result-actions {
    flex-direction: column;
  }

  .kivi-payment-result-icon {
    width: 64px;
    height: 64px;
  }
}
</style>