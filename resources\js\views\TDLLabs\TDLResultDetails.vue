<template>
    <div class="w-full px-4">
        <div class="grid grid-cols-1 gap-6">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                        <h4 class="font-medium text-lg text-gray-800">{{ $t('TDL Lab Test Result') }}</h4>
                        <div class="flex space-x-2">
                            <button 
                                @click="printResult" 
                                class="inline-flex items-center px-3 py-1 text-sm border border-blue-600 rounded-md text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                :disabled="printing"
                            >
                                <i :class="{'fa fa-sync fa-spin mr-1': printing, 'fa fa-print mr-1': !printing}"></i>
                                {{ printing ? $t('Printing...') : $t('Print') }}
                            </button>
                            <router-link to="/tdl-results" class="inline-flex items-center px-3 py-1 text-sm border border-blue-600 rounded-md text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fa fa-arrow-left mr-1"></i>
                                {{ $t('Back to Results') }}
                            </router-link>
                        </div>
                    </div>
                    <div class="p-6">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <div v-else>
                            <!-- Patient and Order Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                                        <div class="p-5">
                                            <h5 class="text-base font-medium text-gray-800 mb-4">{{ $t('Patient Information') }}</h5>
                                            <div class="divide-y divide-gray-200">
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Patient Name') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ result.patient ? result.patient.name : 'N/A' }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Doctor') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ result.doctor ? result.doctor.name : 'N/A' }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Clinic') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ result.clinic ? result.clinic.name : 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                                        <div class="p-5">
                                            <h5 class="text-base font-medium text-gray-800 mb-4">{{ $t('Test Information') }}</h5>
                                            <div class="divide-y divide-gray-200">
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Order Number') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ result.order_number || 'N/A' }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Result ID') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ result.result_id || 'N/A' }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Result Date') }}:</div>
                                                    <div class="text-sm text-gray-900">{{ formatDate(result.result_date) }}</div>
                                                </div>
                                                <div class="py-3 grid grid-cols-2">
                                                    <div class="text-sm font-medium text-gray-700">{{ $t('Status') }}:</div>
                                                    <div class="text-sm">
                                                        <span
                                                            class="px-2 py-1 text-xs font-medium rounded-full"
                                                            :class="{
                                                                'bg-blue-100 text-blue-800': result.result_status === 'received',
                                                                'bg-green-100 text-green-800': result.result_status === 'reviewed'
                                                            }"
                                                        >
                                                            {{ result.result_status === 'received' ? $t('Received') : $t('Reviewed') }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Test Results -->
                            <div class="mb-6">
                                <h5 class="text-base font-medium text-gray-800 mb-4">{{ $t('Test Results') }}</h5>
                                
                                <div class="space-y-4">
                                    <div class="bg-white border rounded-lg shadow-sm overflow-hidden" v-for="(test, index) in result.tests" :key="index">
                                        <div class="px-5 py-3 border-b border-gray-200">
                                            <div
                                                class="w-full flex items-center justify-between text-left cursor-pointer"
                                                @click="toggleTestPanel(index)"
                                            >
                                                <div class="flex items-center">
                                                    <i class="fa fa-flask mr-2 text-gray-500"></i>
                                                    <span class="font-medium text-gray-900">{{ test.test_name }}</span>
                                                    <span class="text-gray-500 ml-2">({{ test.test_code }})</span>
                                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {{ test.biomarkers ? test.biomarkers.length : 0 }} {{ $t('biomarkers') }}
                                                    </span>
                                                </div>
                                                <i :class="['fa', expandedTests.includes(index) ? 'fa-chevron-up' : 'fa-chevron-down', 'text-gray-500']"></i>
                                            </div>
                                        </div>
                                        
                                        <div v-show="expandedTests.includes(index)" class="p-5">
                                            <div class="overflow-x-auto">
                                                <table class="min-w-full divide-y divide-gray-200">
                                                    <thead class="bg-gray-50">
                                                        <tr>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Biomarker') }}</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">{{ $t('Result') }}</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">{{ $t('Units') }}</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">{{ $t('Reference Range') }}</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">{{ $t('Status') }}</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="bg-white divide-y divide-gray-200">
                                                        <tr
                                                            v-for="biomarker in test.biomarkers"
                                                            :key="biomarker.id"
                                                            :class="{
                                                                'bg-red-50': biomarker.abnormal_flag === 'H' || biomarker.abnormal_flag === 'HH',
                                                                'bg-yellow-50': biomarker.abnormal_flag === 'L' || biomarker.abnormal_flag === 'LL'
                                                            }"
                                                        >
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ biomarker.name }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ biomarker.value }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ biomarker.units }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ biomarker.reference_range }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                                <span
                                                                    v-if="biomarker.abnormal_flag"
                                                                    class="px-2 py-1 text-xs font-medium rounded-full"
                                                                    :class="{
                                                                        'bg-red-100 text-red-800': biomarker.abnormal_flag === 'H' || biomarker.abnormal_flag === 'HH',
                                                                        'bg-yellow-100 text-yellow-800': biomarker.abnormal_flag === 'L' || biomarker.abnormal_flag === 'LL'
                                                                    }"
                                                                >
                                                                    <span v-if="biomarker.abnormal_flag === 'H'">{{ $t('High') }}</span>
                                                                    <span v-else-if="biomarker.abnormal_flag === 'HH'">{{ $t('Very High') }}</span>
                                                                    <span v-else-if="biomarker.abnormal_flag === 'L'">{{ $t('Low') }}</span>
                                                                    <span v-else-if="biomarker.abnormal_flag === 'LL'">{{ $t('Very Low') }}</span>
                                                                </span>
                                                                <span v-else class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">{{ $t('Normal') }}</span>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Physician Interpretation -->
                            <div>
                                <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                                    <div class="px-5 py-3 border-b border-gray-200">
                                        <h5 class="font-medium text-gray-800">{{ $t('Physician Interpretation') }}</h5>
                                    </div>
                                    <div class="p-5">
                                        <div v-if="result.result_status === 'reviewed'">
                                            <div class="mb-4">
                                                <div>
                                                    <span class="font-medium text-gray-700">{{ $t('Reviewed By') }}:</span>
                                                    <span class="text-gray-900">{{ result.reviewer ? result.reviewer.name : 'N/A' }}</span>
                                                    <span class="text-gray-500 ml-2">
                                                        {{ formatDate(result.reviewed_at) }}
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <div>
                                                <span class="font-medium text-gray-700">{{ $t('Notes') }}:</span>
                                                <p class="mt-2 text-gray-700">
                                                    {{ result.physician_notes || $t('No notes provided.') }}
                                                </p>
                                            </div>
                                        </div>
                                        
                                        <div v-else>
                                            <textarea
                                                v-model="reviewNotes"
                                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                                rows="4"
                                                :placeholder="$t('Enter your medical interpretation notes here...')"
                                            ></textarea>
                                            
                                            <button
                                                @click="reviewResult"
                                                class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="reviewingResult"
                                            >
                                                <i v-if="reviewingResult" class="fa fa-sync fa-spin mr-2"></i>
                                                <i v-else class="fa fa-check-circle mr-2"></i>
                                                {{ reviewingResult ? $t('Saving...') : $t('Mark as Reviewed') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
    name: 'TDLResultDetails',
    data() {
        return {
            loading: true,
            reviewingResult: false,
            printing: false,
            expandedTests: [0], // Default: First test is expanded
            result: {
                id: 0,
                patient_id: 0,
                doctor_id: 0,
                clinic_id: 0,
                order_number: '',
                result_id: '',
                result_date: '',
                result_status: 'received',
                physician_notes: '',
                reviewed_by: null,
                reviewed_at: null,
                created_at: '',
                updated_at: '',
                patient: null,
                doctor: null,
                clinic: null,
                reviewer: null,
                tests: []
            },
            reviewNotes: ''
        };
    },
    computed: {
        currentLocale() {
            return this.$i18n.locale || 'en';
        }
    },
    created() {
        this.fetchResultDetails();
    },
    methods: {
        fetchResultDetails() {
            const resultId = this.$route.params.id;
            
            if (!resultId) {
                this.redirectToResults('No result ID provided');
                return;
            }
            
            this.loading = true;
            
            get('tdl_get_test_result_details', { id: resultId })
                .then(response => {
                    this.loading = false;
                    
                    if (response.data.status === true) {
                        this.result = response.data.data;
                        
                        // Initialize tests array if it's null
                        if (!this.result.tests) {
                            this.result.tests = [];
                        }
                        
                        // Initialize biomarkers array if it's null for any test
                        this.result.tests.forEach(test => {
                            if (!test.biomarkers) {
                                test.biomarkers = [];
                            }
                        });
                    } else {
                        this.redirectToResults(response.data.message || this.$t('Failed to load result details.'));
                    }
                })
                .catch(error => {
                    this.loading = false;
                    console.error('Error fetching result details:', error);
                    this.redirectToResults(this.$t('Failed to load result details.'));
                });
        },
        redirectToResults(errorMessage) {
            displayErrorMessage(errorMessage);
            this.$router.push('/tdl-results');
        },
        formatDate(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const options = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                
                return new Date(dateString).toLocaleDateString(this.currentLocale, options);
            } catch (error) {
                console.error('Error formatting date:', error);
                return dateString || 'N/A';
            }
        },
        printResult() {
            this.printing = true;
            
            // Expand all test panels for printing
            const allTestIndices = Array.from({ length: this.result.tests.length }, (_, i) => i);
            this.expandedTests = allTestIndices;
            
            // Give time for the DOM to update before printing
            setTimeout(() => {
                window.print();
                this.printing = false;
            }, 500);
        },
        toggleTestPanel(index) {
            if (this.expandedTests.includes(index)) {
                this.expandedTests = this.expandedTests.filter(i => i !== index);
            } else {
                this.expandedTests.push(index);
            }
        },
        reviewResult() {
            if (!this.reviewNotes.trim()) {
                displayErrorMessage(this.$t('Please enter your interpretation notes before marking as reviewed.'));
                return;
            }
            
            this.reviewingResult = true;
            
            // Get current user information from store
            const currentUser = this.$store.getters.get_current_user;
            
            if (!currentUser || !currentUser.id) {
                displayErrorMessage(this.$t('User information not available.'));
                this.reviewingResult = false;
                return;
            }
            
            const reviewData = {
                result_id: this.result.id,
                reviewer_id: currentUser.id,
                notes: this.reviewNotes.trim()
            };
            
            post('tdl_review_test_result', reviewData)
                .then(response => {
                    this.reviewingResult = false;
                    
                    if (response.data.status === true) {
                        displayMessage(this.$t('Result marked as reviewed successfully.'));
                        // Refresh the result details
                        this.fetchResultDetails();
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to mark result as reviewed.'));
                    }
                })
                .catch(error => {
                    this.reviewingResult = false;
                    console.error('Error reviewing result:', error);
                    displayErrorMessage(this.$t('Failed to mark result as reviewed.'));
                });
        }
    }
};
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media print {
    button, .flex.space-x-2 {
        display: none !important;
    }
    
    /* Ensure all test panels are expanded in print view */
    [v-show] {
        display: block !important;
        height: auto !important;
    }
    
    /* Add page breaks between sections */
    .mb-6 {
        page-break-after: always;
    }
    
    /* Hide overflow to fit content on page */
    .overflow-x-auto {
        overflow: visible !important;
    }
    
    /* More condensed layout for print */
    .px-6 {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
    
    .p-5 {
        padding: 0.75rem !important;
    }
    
    /* Ensure good contrast for print */
    .text-gray-700 {
        color: #000 !important;
    }
    
    /* Ensure tables print well */
    table {
        width: 100% !important;
        border-collapse: collapse !important;
    }
    
    th, td {
        border: 1px solid #ddd !important;
        padding: 4px 8px !important;
    }
    
    /* Style for headers in print */
    h4, h5 {
        margin-top: 1rem !important;
        margin-bottom: 0.5rem !important;
        font-weight: bold !important;
    }
}
</style>