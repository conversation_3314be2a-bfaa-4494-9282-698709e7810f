<!-- SummaryModal.vue -->
<template>
    <ModalPopup v-if="show" modalId="summary-modal" modalSize="lg" :openModal="show" :modalTitle="'Generated Summary'"
        @closeModal="$emit('close')">
        <div class="summary-modal">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <b-button variant="primary" @click="downloadPDF">
                    <i class="fas fa-download"></i> Download PDF 
                </b-button>
            </div>

            <!-- Editor -->
            <vue-editor  :editorToolbar="customToolbar" class="custom-editor"  :key="index" v-model="editedSummary"></vue-editor>
        </div>

        <!-- Footer -->
        <div class="d-flex justify-content-end mt-3">
            <b-button variant="secondary" class="mr-2" @click="$emit('close')">
                Cancel
            </b-button>
            <b-button variant="primary" @click="$emit('next')">
                {{ isLoading  ? "Saving" : "Save" }}
            </b-button>
        </div>
    </ModalPopup>
</template>

<script>
import ModalPopup from '../Modal/Index.vue'

export default {
    name: 'SummaryModal',
    components: {
        ModalPopup
    },
    props: {
        show: {
            type: Boolean,
            required: true
        },
        summary: {
            type: String,
            required: true
        },
        template: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            editedSummary: this.summary,
            isLoading:false,
            index:'summary-editor',
            customToolbar: [
                [{
                    header: [false, 1, 2, 3, 4, 5, 6]
                }], ["bold", "italic", "underline", "strike"], // toggled buttons
                [{
                    align: ""
                }, {
                    align: "center"
                }, {
                    align: "right"
                }, {
                    align: "justify"
                }], ["blockquote", "code-block"], [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }, {
                    list: "check"
                }], [{
                    indent: "-1"
                }, {
                    indent: "+1"
                }], // outdent/indent
                [{
                    color: []
                }, {
                    background: []
                },
                ],
            ]
        }
    },
    watch: {
        summary: {
            immediate: true,
            handler(newVal) {
                // Convert **text** to bold HTML
                this.editedSummary = this.convertAsterisksToBold(newVal);
            }
        }
    },
    methods: {
        downloadPDF() {
            console.log(this.summary,'ritesh');
            const plainText = this.editedSummary.replace(/<\/?strong>/g, '**');
            this.$emit('download', plainText);
        },
        convertAsterisksToBold(text) {
            if (!text) return '';
            // Replace **text** with <strong>text</strong>
            return text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        },
    }
}
</script>

<style>
.summary-modal {
    padding: 1rem;
}

.editor-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

#quill-container {
  height: 300px !important;
  overflow-y: auto;
}

/* Target the editable content area */
:deep(.ql-container) {
  height: calc(100% - 42px) !important; /* 42px is toolbar height */
}

:deep(.ql-editor) {
  max-height: 100%;
  overflow-y: auto;
}
</style>