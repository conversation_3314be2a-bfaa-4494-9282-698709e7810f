<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\MDTemplateManager;
use App\models\KCClinic;
use App\models\KCDoctorClinicMapping;
use Exception;
use WP_User;

class MDTemplateManagerController extends KCBase {

    public $db;

    /**
     * @var KCRequest
     */
    private $request;

    /**
     * @var MDTemplateManager
     */
    private $template_manager;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;

        $this->db = $wpdb;
        $this->request = new KCRequest();
        $this->template_manager = new MDTemplateManager();

        parent::__construct();
    }

    /**
     * Get templates based on user role and permissions
     */
    public function index() {
        // Check permission for template list
        // if (!kcCheckPermission('template_list') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }

        $request_data = $this->request->getInputs();

        try {
            // Get request inputs
            $request_data = $this->request->getInputs();

            // Get current user information
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();

            // Default query conditions
            $search_condition = $doctor_condition = $clinic_condition = $paginationCondition = "";
            $orderByCondition = " ORDER BY created_at DESC ";
            $template_table = $this->db->prefix . 'md_template_manager';

            // Handle pagination
            if (isset($request_data['type']) && $request_data['type'] === 'list') {
                if ((int) $request_data['perPage'] > 0) {
                    $perPage = (int) $request_data['perPage'];
                    $offset = ((int) $request_data['page'] - 1) * $perPage;
                    $paginationCondition = " LIMIT {$perPage} OFFSET {$offset} ";
                }

                // Handle sorting
                if (!empty($request_data['sort'])) {
                    $request_data['sort'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['sort'][0]), true));
                    if (!empty($request_data['sort']['field']) && !empty($request_data['sort']['type']) && $request_data['sort']['type'] !== 'none') {
                        $sortField = esc_sql($request_data['sort']['field']);
                        $sortByValue = esc_sql(strtoupper($request_data['sort']['type']));

                        // Apply sorting based on allowed fields
                        switch ($sortField) {
                            case 'id':
                            case 'name':
                            case 'category':
                            case 'share_status':
                            case 'created_at':
                            case 'updated_at':
                                $orderByCondition = " ORDER BY {$sortField} {$sortByValue} ";
                                break;
                        }
                    }
                }

                // Handle search
                if (isset($request_data['searchTerm']) && trim($request_data['searchTerm']) !== '') {
                    $searchTerm = esc_sql(strtolower(trim($request_data['searchTerm'])));
                    $search_condition .= " AND (
                        {$template_table}.id LIKE '%{$searchTerm}%'
                        OR {$template_table}.name LIKE '%{$searchTerm}%'
                        OR {$template_table}.category LIKE '%{$searchTerm}%'
                        OR {$template_table}.share_status LIKE '%{$searchTerm}%'
                    ) ";
                } else if (!empty($request_data['columnFilters'])) {
                    $request_data['columnFilters'] = json_decode(stripslashes($request_data['columnFilters']), true);
                    foreach ($request_data['columnFilters'] as $column => $searchValue) {
                        $searchValue = !empty($searchValue) ? $searchValue : '';
                        $searchValue = esc_sql(strtolower(trim($searchValue)));
                        $column = esc_sql($column);
                        if ($searchValue === '') {
                            continue;
                        }

                        // Apply filtering based on column
                        switch ($column) {
                            case 'id':
                            case 'name':
                            case 'category':
                            case 'share_status':
                                $search_condition .= " AND {$template_table}.{$column} LIKE '%{$searchValue}%' ";
                                break;
                            case 'doctor_name':
                                $search_condition .= " AND doctor_name LIKE '%{$searchValue}%' ";
                                break;
                            case 'clinic_name':
                                $search_condition .= " AND clinic_name LIKE '%{$searchValue}%' ";
                                break;
                        }
                    }
                }
            }

            // Role-specific conditions
            $templates_query = "";

            switch ($current_login_user_role) {
                case 'administrator':
                    // Admin can see all templates
                    $templates_query = "SELECT t.*,
                            CASE WHEN t.doctor_id > 0 THEN
                                (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id)
                                ELSE '" . esc_sql(__('System', 'kc-lang')) . "'
                            END as doctor_name,
                            CASE WHEN t.clinic_id > 0 THEN
                                (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id)
                                ELSE ''
                            END as clinic_name
                        FROM {$template_table} t
                        WHERE t.status = 1 {$search_condition}
                        {$orderByCondition}";
                    break;

                case $this->getClinicAdminRole():
                    // Clinic admin sees clinic templates and personal templates
                    $clinic_id = kcGetClinicIdOfClinicAdmin();

                    if (empty($clinic_id)) {
                        $templates_query = "SELECT * FROM {$template_table} WHERE 1=0"; // Empty result
                    } else {
                        $templates_query = "SELECT t.*,
                                CASE WHEN t.doctor_id > 0 THEN
                                    (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id)
                                    ELSE '" . esc_sql(__('System', 'kc-lang')) . "'
                                END as doctor_name,
                                CASE WHEN t.clinic_id > 0 THEN
                                    (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id)
                                    ELSE ''
                                END as clinic_name
                            FROM {$template_table} t
                            WHERE t.status = 1
                            AND (t.clinic_id = {$clinic_id} OR t.doctor_id = {$user_id} OR t.share_status = 'public')
                            {$search_condition}
                            {$orderByCondition}";
                    }
                    break;

                case $this->getDoctorRole():
                    // Doctor sees personal, clinic and public templates
                    $doctor_clinics = $this->db->get_col("
                        SELECT clinic_id FROM {$this->db->prefix}kc_doctor_clinic_mappings
                        WHERE doctor_id = {$user_id} AND status = 1
                    ");

                    $clinic_condition = "";
                    if (!empty($doctor_clinics)) {
                        $clinic_ids = implode(',', array_map('intval', $doctor_clinics));
                        $clinic_condition = " OR t.clinic_id IN ({$clinic_ids}) ";
                    }

                    $templates_query = "SELECT t.*,
                            CASE WHEN t.doctor_id > 0 THEN
                                (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id)
                                ELSE '" . esc_sql(__('System', 'kc-lang')) . "'
                            END as doctor_name,
                            CASE WHEN t.clinic_id > 0 THEN
                                (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id)
                                ELSE ''
                            END as clinic_name
                        FROM {$template_table} t
                        WHERE t.status = 1
                        AND (t.doctor_id = {$user_id} {$clinic_condition} OR t.share_status = 'public')
                        {$search_condition}
                        {$orderByCondition}";
                    break;

                default:
                    // Other roles see no templates or only public ones
                    $templates_query = "SELECT t.*,
                            CASE WHEN t.doctor_id > 0 THEN
                                (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id)
                                ELSE '" . esc_sql(__('System', 'kc-lang')) . "'
                            END as doctor_name,
                            CASE WHEN t.clinic_id > 0 THEN
                                (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id)
                                ELSE ''
                            END as clinic_name
                        FROM {$template_table} t
                        WHERE t.status = 1
                        AND t.share_status = 'public'
                        {$search_condition}
                        {$orderByCondition}";
                    break;
            }

            // Count total records for pagination
            $total = 0;
            if (isset($request_data['type']) && $request_data['type'] === 'list') {
                $count_query = preg_replace('/SELECT.*?FROM/is', 'SELECT COUNT(*) FROM', $templates_query);
                $count_query = preg_replace('/ORDER BY.*$/is', '', $count_query);
                $total = $this->db->get_var($count_query);

                // Add pagination to the main query
                $templates_query .= $paginationCondition;
            }

            // Execute the query
            $templates = $this->db->get_results($templates_query);

            // Process templates to add additional information
            if (!empty($templates)) {
                $share_status_labels = [
                    'private' => __('Private', 'kc-lang'),
                    'clinic' => __('Clinic', 'kc-lang'),
                    'public' => __('Public', 'kc-lang')
                ];

                $category_labels = [
                    'general' => __('General', 'kc-lang'),
                    'referral' => __('Referral', 'kc-lang'),
                    'sick_note' => __('Sick Note', 'kc-lang'),
                    'consultation' => __('Consultation', 'kc-lang'),
                    'procedure' => __('Procedure', 'kc-lang')
                ];

                foreach ($templates as $key => $template) {
                    // Set user-friendly labels
                    $templates[$key]->share_status_label = $share_status_labels[$template->share_status] ?? $template->share_status;
                    $templates[$key]->category_label = $category_labels[$template->category] ?? $template->category;

                    // Ownership and editing permissions
                    $templates[$key]->is_owner = ($template->doctor_id == $user_id);
                    $templates[$key]->is_editable = ($template->doctor_id == $user_id) || ($current_login_user_role === 'administrator') || ($current_login_user_role === $this->getClinicAdminRole() && $template->clinic_id == kcGetClinicIdOfClinicAdmin());
                }
            }

            // Send the response
            if (empty($templates)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('No templates found', 'kc-lang'),
                    'data' => []
                ]);
            } else {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Templates retrieved successfully', 'kc-lang'),
                    'data' => $templates,
                    'total_rows' => isset($request_data['type']) && $request_data['type'] === 'list' ? $total : count($templates)
                ]);
            }

        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check if the templates table exists
     *
     * @return bool
     */
    private function tableExists() {
        return $this->db->get_var("SHOW TABLES LIKE '{$this->db->prefix}md_template_manager'") === $this->db->prefix . 'md_template_manager';
    }

    /**
     * Save a new template
     */
    public function save_template() {
        // Check permission for template save
        // if (!kcCheckPermission('template_add') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }

        try {
            // Get request data
            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();

            // Validate required fields
            if (empty($request_data['name']) || empty($request_data['category']) || empty($request_data['content'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Name, category and content are required fields', 'kc-lang')
                ]);
            }

            // Prepare template data
            $template_data = [
                'name' => sanitize_text_field($request_data['name']),
                'category' => sanitize_text_field($request_data['category']),
                'content' => wp_kses_post($request_data['content']), // Content may contain HTML but sanitize it
                'share_status' => sanitize_text_field($request_data['share_status'] ?? 'private'),
                'status' => 1
            ];

            // Check if we're updating an existing template
            if (!empty($request_data['id'])) {
                $template_id = (int) $request_data['id'];
                $existing_template = $this->template_manager->get_template($template_id);

                if (!$existing_template) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Template not found', 'kc-lang')
                    ]);
                }

                // System templates cannot be edited
                if ($existing_template->is_system) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('System templates cannot be edited. Please clone it to make your own version.', 'kc-lang')
                    ]);
                }

                // Check if user has permission to edit this template
                $can_edit = false;

                if ($current_login_user_role === 'administrator') {
                    $can_edit = true;
                } else if ($current_login_user_role === $this->getClinicAdminRole() &&
                           $existing_template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                    $can_edit = true;
                } else if ($existing_template->doctor_id == $user_id) {
                    $can_edit = true;
                }

                if (!$can_edit) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('You do not have permission to edit this template', 'kc-lang')
                    ]);
                }

                $template_data['updated_at'] = current_time('Y-m-d H:i:s');
                $template_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : null;

                $result = $this->db->update($this->db->prefix . "md_template_manager", $template_data, ['id' => $template_id]);

                if ($result) {
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Template updated successfully', 'kc-lang'),
                        'data' => [
                            'id' => $template_id
                        ]
                    ]);
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to update template', 'kc-lang')
                    ]);
                }
            } else {
                // Set doctor_id and clinic_id based on user role for new templates
                switch ($current_login_user_role) {
                    case $this->getDoctorRole():
                        $template_data['doctor_id'] = $user_id;

                        // If clinic_id is provided and doctor belongs to this clinic, use it
                        if (!empty($request_data['clinic_id'])) {
                            $clinic_id = (int) $request_data['clinic_id'];

                            // Verify doctor belongs to this clinic
                            $is_doctor_in_clinic = $this->db->get_var($this->db->prepare(
                                "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings
                                WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                                $user_id, $clinic_id
                            ));

                            if ($is_doctor_in_clinic) {
                                $template_data['clinic_id'] = $clinic_id;
                            } else {
                                // If doctor does not belong to the clinic, use their default clinic
                                $template_data['clinic_id'] = kcGetDefaultClinicId();
                            }
                        } else {
                            // Use default clinic if none specified
                            $template_data['clinic_id'] = kcGetDefaultClinicId();
                        }
                        break;

                    case $this->getClinicAdminRole():
                        $template_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();

                        // Check if the clinic admin is also a doctor
                        $is_also_doctor = in_array($this->getDoctorRole(), get_userdata($user_id)->roles);
                        if ($is_also_doctor) {
                            $template_data['doctor_id'] = $user_id;
                        } else {
                            $template_data['doctor_id'] = null;
                        }
                        break;

                    case 'administrator':
                        // Admin can create system templates or assign to specific doctor/clinic
                        $template_data['is_system'] = !empty($request_data['is_system']) ? 1 : 0;

                        if (!empty($request_data['doctor_id'])) {
                            $template_data['doctor_id'] = (int) $request_data['doctor_id'];
                        } else {
                            $template_data['doctor_id'] = null;
                        }

                        if (!empty($request_data['clinic_id'])) {
                            $template_data['clinic_id'] = (int) $request_data['clinic_id'];
                        } else {
                            $template_data['clinic_id'] = null;
                        }
                        break;

                    default:
                        // Other roles cannot create templates
                        wp_send_json([
                            'status' => false,
                            'message' => esc_html__('You do not have permission to create templates', 'kc-lang')
                        ]);
                        break;
                }

                // Insert new template
                $template_data['created_at'] = current_time('Y-m-d H:i:s');
                $template_data['is_system'] = $template_data['is_system'] ?? 0;

                $id = $this->template_manager->insert($template_data);

                if ($id) {
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Template saved successfully', 'kc-lang'),
                        'data' => [
                            'id' => $id
                        ]
                    ]);
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to save template', 'kc-lang')
                    ]);
                }
            }

        } catch (Exception $e) {
            error_log('Template update error: ' . $e->getMessage());

            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update an existing template
     */
    public function update_template_delete($request) {
        try {
            $params = $request->get_params();

            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }

            // Use params directly or fall back to the old way
            $request_data = isset($params) && !empty($params) ? $params : $this->request->getInputs();
            $user_id = get_current_user_id();
            $user = new WP_User($user_id);

            // Validate required fields
            if (empty($request_data['id']) || empty($request_data['name']) || empty($request_data['category']) || empty($request_data['content'])) {
                throw new Exception(__('ID, name, category and content are required fields', 'kc-lang'));
            }

            // Get the existing template
            $template = $this->template_manager->get_template($request_data['id']);

            if (!$template) {
                throw new Exception(__('Template not found', 'kc-lang'));
            }

            // Check if user has permission to edit this template
            $has_permission = false;

            // Admin can edit any template
            if (in_array('administrator', $user->roles)) {
                $has_permission = true;
            }
            // Clinic admin can edit clinic templates
            else if (in_array('kiviCare_clinic_admin', $user->roles) && $template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $has_permission = true;
            }
            // Doctor can edit their own templates
            else if ($template->doctor_id == $user_id) {
                $has_permission = true;
            }

            if (!$has_permission) {
                throw new Exception(__('You do not have permission to edit this template', 'kc-lang'));
            }

            // System templates cannot be edited
            if ($template->is_system) {
                throw new Exception(__('System templates cannot be edited. Please clone it to make your own version.', 'kc-lang'));
            }

            // Prepare updated data
            $template_data = [
                'name' => sanitize_text_field($request_data['name']),
                'category' => sanitize_text_field($request_data['category']),
                'content' => $request_data['content'], // Content may contain HTML
                'share_status' => sanitize_text_field($request_data['share_status'] ?? $template->share_status),
                'updated_at' => current_time('Y-m-d H:i:s')
            ];

            // Update the template
            $result = $this->template_manager->update($template_data, ['id' => $request_data['id']]);

            if ($result) {
                return new WP_REST_Response([
                    'status' => true,
                    'message' => __('Template updated successfully', 'kc-lang')
                ], 200);
            } else {
                throw new Exception(__('Failed to update template', 'kc-lang'));
            }

        } catch (Exception $e) {
            error_log('Template update error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a template (soft delete by setting status = 0)
     */
    public function delete_template() {
        // Check permission for template delete
        // if (!kcCheckPermission('template_delete') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }

        try {
            // Get request data
            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();

            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template ID is required', 'kc-lang')
                ]);
            }

            $template_id = (int) $request_data['id'];

            // Get template from database
            $template = $this->template_manager->get_template($template_id);

            if (!$template) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template not found', 'kc-lang')
                ]);
            }

            // System templates cannot be deleted
            if ($template->is_system) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('System templates cannot be deleted', 'kc-lang')
                ]);
            }

            // Check if user has permission to delete this template
            $can_delete = false;

            if ($current_login_user_role === 'administrator') {
                $can_delete = true;
            } else if ($current_login_user_role === $this->getClinicAdminRole() &&
                      $template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $can_delete = true;
            } else if ($template->doctor_id == $user_id) {
                $can_delete = true;
            }

            if (!$can_delete) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to delete this template', 'kc-lang')
                ]);
            }

            // Soft delete the template by setting status = 0
            $result = $this->db->update($this->db->prefix . "md_template_manager", ['status' => 0], ['id' => $template_id]);

            if ($result) {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Template deleted successfully', 'kc-lang')
                ]);
            } else {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to delete template', 'kc-lang')
                ]);
            }

        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get details of a single template
     */
    public function getTemplateDetails() {
        // Check permission for template details
        // if (!kcCheckPermission('template_view') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }

        try {
            // Get request data
            $request_data = $this->request->getInputs();

            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template ID is required', 'kc-lang')
                ]);
            }

            // Sanitize input
            $template_id = (int) $request_data['id'];

            // Get template from database
            $template = $this->template_manager->get_template($template_id);

            if (!$template) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template not found', 'kc-lang')
                ]);
            }

            // Check if user has access to this template
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            $has_access = false;

            // Access control based on user role
            switch ($current_login_user_role) {
                case 'administrator':
                    // Admin can access any template
                    $has_access = true;
                    break;

                case $this->getClinicAdminRole():
                    // Clinic admin can access clinic templates
                    $clinic_id = kcGetClinicIdOfClinicAdmin();
                    if ($template->clinic_id == $clinic_id || $template->doctor_id == $user_id || $template->share_status == 'public') {
                        $has_access = true;
                    }
                    break;

                case $this->getDoctorRole():
                    // Doctor can access their templates, their clinic's templates and public templates
                    if ($template->doctor_id == $user_id || $template->share_status == 'public' || $template->is_system) {
                        $has_access = true;
                    } else if ($template->share_status == 'clinic') {
                        // Check if doctor belongs to the clinic
                        $doctor_clinic_count = $this->db->get_var($this->db->prepare(
                            "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings
                            WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                            $user_id, $template->clinic_id
                        ));

                        if ($doctor_clinic_count > 0) {
                            $has_access = true;
                        }
                    }
                    break;

                default:
                    // Other roles can access only public templates
                    if ($template->share_status == 'public') {
                        $has_access = true;
                    }
                    break;
            }

            if (!$has_access) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have access to this template', 'kc-lang')
                ]);
            }

            // Add additional information to the template

            // Get doctor name
            if (!empty($template->doctor_id)) {
                $doctor = get_userdata($template->doctor_id);
                $template->doctor_name = $doctor ? $doctor->display_name : esc_html__('Unknown', 'kc-lang');
            } else {
                $template->doctor_name = $template->is_system ? esc_html__('System', 'kc-lang') : '';
            }

            // Get clinic name
            if (!empty($template->clinic_id)) {
                $clinic = $this->db->get_row($this->db->prepare(
                    "SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = %d",
                    $template->clinic_id
                ));
                $template->clinic_name = $clinic ? decodeSpecificSymbols($clinic->name) : esc_html__('Unknown', 'kc-lang');
            } else {
                $template->clinic_name = '';
            }

            // Get category and share status labels
            $category_labels = [
                'general' => esc_html__('General', 'kc-lang'),
                'referral' => esc_html__('Referral', 'kc-lang'),
                'sick_note' => esc_html__('Sick Note', 'kc-lang'),
                'consultation' => esc_html__('Consultation', 'kc-lang'),
                'procedure' => esc_html__('Procedure', 'kc-lang')
            ];

            $share_status_labels = [
                'private' => esc_html__('Private', 'kc-lang'),
                'clinic' => esc_html__('Clinic', 'kc-lang'),
                'public' => esc_html__('Public', 'kc-lang')
            ];

            $template->category_label = $category_labels[$template->category] ?? $template->category;
            $template->share_status_label = $share_status_labels[$template->share_status] ?? $template->share_status;

            // Check if user can edit this template
            $template->is_owner = ($template->doctor_id == $user_id);
            $template->is_editable = ($template->doctor_id == $user_id) ||
                                    ($current_login_user_role === 'administrator') ||
                                    ($current_login_user_role === $this->getClinicAdminRole() &&
                                    $template->clinic_id == kcGetClinicIdOfClinicAdmin());

            // Send response
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Template details retrieved successfully', 'kc-lang'),
                'data' => $template
            ]);

        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Clone a template (create a copy for the current user)
     */
    public function clone_template() {
        // Check permission for template cloning
        // if (!kcCheckPermission('template_add') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }

        try {
            // Get request data
            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();

            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template ID is required', 'kc-lang')
                ]);
            }

            $template_id = (int) $request_data['id'];

            // Get the source template
            $source = $this->template_manager->get_template($template_id);

            if (!$source) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template not found', 'kc-lang')
                ]);
            }

            // Check if user can access this template
            $has_access = false;

            switch ($current_login_user_role) {
                case 'administrator':
                    // Admin can clone any template
                    $has_access = true;
                    break;

                case $this->getClinicAdminRole():
                    // Clinic admin can clone clinic templates
                    $clinic_id = kcGetClinicIdOfClinicAdmin();
                    if ($source->clinic_id == $clinic_id || $source->doctor_id == $user_id ||
                        $source->share_status == 'public' || $source->is_system) {
                        $has_access = true;
                    }
                    break;

                case $this->getDoctorRole():
                    // Doctor can clone their templates, their clinic's templates and public templates
                    if ($source->doctor_id == $user_id || $source->share_status == 'public' || $source->is_system) {
                        $has_access = true;
                    } else if ($source->share_status == 'clinic') {
                        // Check if doctor belongs to the clinic
                        $doctor_clinic_count = $this->db->get_var($this->db->prepare(
                            "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings
                            WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                            $user_id, $source->clinic_id
                        ));

                        if ($doctor_clinic_count > 0) {
                            $has_access = true;
                        }
                    }
                    break;

                default:
                    // Other roles cannot clone templates
                    $has_access = false;
                    break;
            }

            if (!$has_access) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have access to clone this template', 'kc-lang')
                ]);
            }

            // Prepare new template data
            $clone_data = [
                'name' => sprintf(esc_html__('Copy of %s', 'kc-lang'), $source->name),
                'category' => $source->category,
                'content' => $source->content,
                'is_system' => 0,
                'share_status' => 'private', // Default to private for cloned templates
                'status' => 1,
                'created_at' => current_time('Y-m-d H:i:s')
            ];

            // Set doctor_id and clinic_id based on user role
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                    $clone_data['doctor_id'] = $user_id;

                    // Use doctor's default clinic or source clinic if doctor belongs to it
                    if ($source->clinic_id) {
                        $doctor_in_source_clinic = $this->db->get_var($this->db->prepare(
                            "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings
                            WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                            $user_id, $source->clinic_id
                        ));

                        if ($doctor_in_source_clinic) {
                            $clone_data['clinic_id'] = $source->clinic_id;
                        } else {
                            $clone_data['clinic_id'] = kcGetDefaultClinicId();
                        }
                    } else {
                        $clone_data['clinic_id'] = kcGetDefaultClinicId();
                    }
                    break;

                case $this->getClinicAdminRole():
                    $clone_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();

                    // If clinic admin is also a doctor, assign doctor_id
                    $is_also_doctor = in_array($this->getDoctorRole(), get_userdata($user_id)->roles);
                    if ($is_also_doctor) {
                        $clone_data['doctor_id'] = $user_id;
                    } else {
                        $clone_data['doctor_id'] = null;
                    }
                    break;

                case 'administrator':
                    // Admin can assign to specific doctor/clinic or keep it as system-wide
                    $clone_data['doctor_id'] = !empty($request_data['doctor_id']) ? (int) $request_data['doctor_id'] : null;
                    $clone_data['clinic_id'] = !empty($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : null;
                    break;

                default:
                    $clone_data['doctor_id'] = null;
                    $clone_data['clinic_id'] = null;
                    break;
            }

            // Create the cloned template
            $id = $this->template_manager->insert($clone_data);

            if ($id) {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Template cloned successfully', 'kc-lang'),
                    'data' => [
                        'id' => $id
                    ]
                ]);
            } else {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to clone template', 'kc-lang')
                ]);
            }

        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Import default templates from the templates.json file
     */
    public function import_default_templates($request) {
        try {
            $params = $request->get_params();

            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            // Check if templates already exist
            $templates_count = $this->db->get_var("SELECT COUNT(*) FROM {$this->db->prefix}md_template_manager WHERE is_system = 1");

            if ($templates_count > 0) {
                throw new Exception(__('Default templates have already been imported', 'kc-lang'));
            }

            // Get templates from JSON file
            $templates_file = KIVI_CARE_DIR . 'resources/js/lib/templates.json';

            if (!file_exists($templates_file)) {
                throw new Exception(__('Templates file not found', 'kc-lang'));
            }

            $templates_content = file_get_contents($templates_file);
            $templates = json_decode($templates_content, true);

            if (empty($templates)) {
                throw new Exception(__('No templates found in the templates file', 'kc-lang'));
            }

            // Import templates
            $this->template_manager->insert_system_templates($templates);

            // Return success
            return new WP_REST_Response([
                'status' => true,
                'message' => __('Default templates imported successfully', 'kc-lang')
            ], 200);

        } catch (Exception $e) {
            error_log('Import templates error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register the templates page in the admin menu
     */
    public function register_menu() {
        // This will be handled in Vue router
    }
    
    /**
     * Process a template directly without using filters
     * This is a direct endpoint to avoid filter-related issues
     */
    public function processTemplateDirectly() {
        try {
            // Get request data
            $request_data = $this->request->getInputs();
            
            $this->logTemplateData($request_data);
            
            // Validate required fields
            if (empty($request_data['templateContent'])) {
                wp_send_json([
                    'status' => false,
                    'message' => 'Template content is required'
                ]);
                return;
            }
            
            // Get the template content
            $template_content = $request_data['templateContent'];
            
            // Ensure all necessary keys exist in request_data to prevent errors
            $necessary_keys = [
                'concerns', 'history', 'examination', 'diagnosis', 'plan',
                'medical_history', 'allergies', 'medications', 'safeguarding'
            ];
            
            foreach ($necessary_keys as $key) {
                if (!isset($request_data[$key])) {
                    $request_data[$key] = '';
                }
            }
            
            // Make sure we have appointment data
            if (empty($request_data['appointment_id']) && !empty($request_data['encounterId'])) {
                // Try to find appointment related to this encounter
                global $wpdb;
                $appointment_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT appointment_id FROM {$wpdb->prefix}kc_patient_encounters WHERE id = %d",
                    $request_data['encounterId']
                ));
                
                if ($appointment_id) {
                    $request_data['appointment_id'] = $appointment_id;
                    error_log("Found appointment ID $appointment_id for encounter " . $request_data['encounterId']);
                }
            }
            
            // Make sure we have current date and time
            $request_data['current_date'] = date('d/m/Y');
            $request_data['current_time'] = date('H:i');
            
            // Replace variables in the template content with actual data
            $processed_content = $this->replace_template_variables($template_content, $request_data);
            
            // Return the processed template
            wp_send_json([
                'status' => true,
                'message' => 'Template processed successfully',
                'data' => [
                    'template_content' => $processed_content,
                    'summary' => $processed_content
                ]
            ]);
            
        } catch (Exception $e) {
            error_log('Error in processTemplateDirectly: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            wp_send_json([
                'status' => false,
                'message' => 'Error processing template: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Initialize the template manager
     */
    public function init() {
        // Add hook to register the database table in KCActivate
        add_action('kcpro_init_db_tables', [$this, 'register_table']);

        // Add filter for encounter summarize
        add_filter('kivicare_encounter_summarize', [$this, 'process_user_template'], 10, 2);

        // Load the controller
        new self();
    }

    /**
     * Register the database table
     */
    public function register_table() {
        require_once KIVI_CARE_DIR . 'app/database/md-template-manager-db.php';
    }

    /**
     * Process user templates for encounter summarize
     *
     * @param array $data The data to be processed
     * @param array $request_data The original request data
     * @return array The processed data
     */
    public function process_user_template($data, $request_data) {
        try {
            error_log('Process user template called with request data: ' . print_r($request_data, true));

            // Check if this is a user template
            if (!empty($request_data['isUserTemplate']) && !empty($request_data['templateContent'])) {
                error_log('Processing user template with content length: ' . strlen($request_data['templateContent']));

                // Get the template content
                $template_content = $request_data['templateContent'];

                // Normalize request data
                if (empty($request_data['encounter_id']) && !empty($request_data['encounterId'])) {
                    $request_data['encounter_id'] = $request_data['encounterId'];
                    error_log('Set encounter_id from encounterId: ' . $request_data['encounter_id']);
                }

                // Ensure all necessary keys exist in request_data to prevent errors
                $necessary_keys = [
                    'concerns', 'history', 'examination', 'diagnosis', 'plan',
                    'medical_history', 'allergies', 'medications', 'safeguarding'
                ];
                
                foreach ($necessary_keys as $key) {
                    if (!isset($request_data[$key])) {
                        $request_data[$key] = '';
                    }
                }

                // Replace variables in the template content with actual data
                $template_content = $this->replace_template_variables($template_content, $request_data);

                error_log('Template content after variable replacement: ' . substr($template_content, 0, 100) . '...');

                // Replace the template content in the data
                $data['template_content'] = $template_content;

                // Also add to the response for the API
                $data['summary'] = $template_content;

                error_log('Template processing completed successfully');
            } else {
                error_log('Invalid template data: isUserTemplate=' .
                        (isset($request_data['isUserTemplate']) ? var_export($request_data['isUserTemplate'], true) : 'undefined') .
                        ', templateContent=' .
                        (isset($request_data['templateContent']) ? (strlen($request_data['templateContent']) > 0 ? 'present' : 'empty') : 'undefined'));
            }

            return $data;
        } catch (Exception $e) {
            error_log('Error in process_user_template: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            // Return a fallback response that won't break the API
            $data['error'] = $e->getMessage();
            $data['template_content'] = "Error processing template: " . $e->getMessage();
            $data['summary'] = "Error processing template: " . $e->getMessage();
            
            return $data;
        }
    }

    /**
     * Replace template variables with actual data
     *
     * @param string $content The template content
     * @param array $request_data The request data with patient, doctor, clinic and encounter information
     * @return string The processed content with variables replaced
     */
    /**
     * Process encounter data through Mistral AI to get summarized content
     * This follows the same pattern used in the default templates
     * 
     * @param array $request_data Request data containing encounter information
     * @return array Processed data with AI-generated summaries
     */
    private function process_through_ai($request_data) {
        try {
            // Initialize AI Scribe controller
            $aiScribeController = new \App\controllers\KCAIScribeController();
            
            // Get encounter ID to fetch tab data
            $encounter_id = isset($request_data['encounter_id']) ? $request_data['encounter_id'] : 
                           (isset($request_data['encounterId']) ? $request_data['encounterId'] : null);
            
            if (empty($encounter_id)) {
                error_log('No encounter ID found for AI processing');
                return $request_data;
            }
            
            // Get tab data to pass to AI
            global $wpdb;
            $encounter_tabs = [];
            
            // Get all encounter tabs from database
            $tab_results = $wpdb->get_results($wpdb->prepare(
                "SELECT type, content FROM {$wpdb->prefix}kc_encounter_tabs 
                WHERE encounter_id = %d 
                ORDER BY created_at DESC",
                $encounter_id
            ));
            
            if (!empty($tab_results)) {
                foreach ($tab_results as $tab) {
                    if (!empty($tab->content)) {
                        // If multiple tabs of same type, combine them
                        if (isset($encounter_tabs[$tab->type])) {
                            $encounter_tabs[$tab->type] .= "\n\n" . $tab->content;
                        } else {
                            $encounter_tabs[$tab->type] = $tab->content;
                        }
                    }
                }
            }
            
            // Merge encounter tab data with request data
            foreach ($encounter_tabs as $key => $value) {
                $request_data[$key] = $value;
            }
            
            // Use reflection to access the private callMistralAPI method
            // This allows us to directly use the same AI processing as the system templates
            $reflection = new \ReflectionClass($aiScribeController);
            $method = $reflection->getMethod('callMistralAPI');
            $method->setAccessible(true);
            
            // Prepare the data for AI analysis
            $sections = [
                'concerns', 'history', 'examination', 'diagnosis', 'plan',
                'medical_history', 'allergies', 'medications', 'safeguarding',
                'family_history', 'social_history', 'lifestyle'
            ];
            
            // Format encounter data for AI prompt
            $formatted_data = "Medical Consultation Summary:\n\n";
            foreach ($sections as $section) {
                if (!empty($request_data[$section])) {
                    $section_title = ucfirst(str_replace('_', ' ', $section));
                    $content = is_array($request_data[$section]) ? 
                               json_encode($request_data[$section]) : 
                               $request_data[$section];
                    
                    $formatted_data .= "## {$section_title}:\n{$content}\n\n";
                }
            }
            
            // Skip AI if we don't have enough data
            if (strlen($formatted_data) < 100) {
                error_log('Not enough data for AI processing');
                return $request_data;
            }
            
            // Prepare the AI prompt
            $user_prompt = "I am a doctor summarizing a patient consultation. Please analyze the following clinical information and organize it into well-formatted sections. Create a professional clinical summary for each section, maintaining all important medical details but presented in a narrative format rather than bullet points. Make the output suitable for a formal medical letter or report.

            Information:
            {$formatted_data}";
            
            // Call the AI service
            $processed_data = $method->invoke($aiScribeController, $user_prompt);
            
            // Merge the processed data with the original request data
            if (!empty($processed_data) && is_array($processed_data)) {
                foreach ($processed_data as $key => $value) {
                    if (!empty($value)) {
                        $request_data[$key] = $value;
                    }
                }
            }
            
            return $request_data;
            
        } catch (Exception $e) {
            error_log('Error in process_through_ai: ' . $e->getMessage());
            // Return original data if AI processing fails
            return $request_data;
        }
    }
    
    /**
     * Replace template variables with actual data
     *
     * @param string $content The template content
     * @param array $request_data The request data with patient, doctor, clinic and encounter information
     * @return string The processed content with variables replaced
     */
    private function replace_template_variables($content, $request_data) {
        try {
            error_log('replace_template_variables called with request data: ' . print_r($request_data, true));
            
            // First, process the data through AI to get nice summaries
            if (strpos($content, '${encounter.') !== false) {
                $request_data = $this->process_through_ai($request_data);
            }

            // Regular expression to find all ${variable.name} patterns
            $variable_regex = '/\${([^}]+)}/';

            // Find all variables in the content
            preg_match_all($variable_regex, $content, $matches);

            // If no variables found, return the content as is
            if (empty($matches[1])) {
                return $content;
            }

            // Get variables to replace
            $variables = $matches[1];

            // Create a mapping of variables to their values
            $replacements = [];

            // Loop through each variable and prepare replacement
            foreach ($variables as $variable) {
                try {
                    $value = $this->get_variable_value($variable, $request_data);
                    $replacements['${' . $variable . '}'] = $value;
                } catch (Exception $e) {
                    error_log('Error getting variable value for ' . $variable . ': ' . $e->getMessage());
                    // Keep the variable as is when there's an error
                    $replacements['${' . $variable . '}'] = '${' . $variable . '}';
                }
            }

            // Replace all variables in the content
            return str_replace(array_keys($replacements), array_values($replacements), $content);
        } catch (Exception $e) {
            error_log('Error in replace_template_variables: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            // Return original content on error to prevent breaking the API
            return $content;
        }
    }

    /**
     * Get the value of a variable based on its name and request data
     *
     * @param string $variable The variable name in format "section.field" (e.g. "patient.name")
     * @param array $request_data The request data
     * @return string The variable value or empty string if not found
     */
    private function get_variable_value($variable, $request_data) {
        // Parse variable name (format: section.field)
        $parts = explode('.', $variable);

        if (count($parts) !== 2) {
            error_log("Invalid variable format: {$variable}");
            return '';
        }

        $section = $parts[0];
        $field = $parts[1];

        error_log("Processing variable: {$section}.{$field}");

        // Handle dynamic date/time variables
        if ($section === 'date' && $field === '') {
            return date('d/m/Y');
        } elseif ($section === 'time' && $field === '') {
            return date('H:i');
        } elseif ($section === 'current_date') {
            return date('d/m/Y');
        } elseif ($section === 'current_time') {
            return date('H:i');
        }

        // Handle patient variables
        if ($section === 'patient') {
            // Check both patient_details and nested patient data
            $patient_data = [];
            
            if (!empty($request_data['patient_details'])) {
                $patient_data = $request_data['patient_details'];
            } elseif (!empty($request_data['patient'])) {
                $patient_data = $request_data['patient'];
            } elseif (!empty($request_data['appointment']) && !empty($request_data['appointment']['patient_id'])) {
                // Try to fetch patient data from the database using appointment patient_id
                global $wpdb;
                $patient_id = $request_data['appointment']['patient_id'];
                $user_data = get_userdata($patient_id);
                
                if ($user_data) {
                    $patient_data = [
                        'patient_name' => $user_data->display_name,
                        'patient_email' => $user_data->user_email,
                        'mobile_number' => get_user_meta($patient_id, 'mobile_number', true),
                        'dob' => get_user_meta($patient_id, 'date_of_birth', true),
                        'gender' => get_user_meta($patient_id, 'gender', true),
                        'address' => get_user_meta($patient_id, 'address', true),
                        'city' => get_user_meta($patient_id, 'city', true),
                        'country' => get_user_meta($patient_id, 'country', true),
                        'postal_code' => get_user_meta($patient_id, 'postal_code', true),
                    ];
                }
            }

            switch ($field) {
                case 'name':
                    return $patient_data['patient_name'] ?? $patient_data['name'] ?? '';
                case 'unique_id':
                    return $patient_data['patient_unique_id'] ?? $patient_data['unique_id'] ?? '';
                case 'dob':
                    $dob = $patient_data['dob'] ?? $patient_data['date_of_birth'] ?? '';
                    return !empty($dob) ? date('d/m/Y', strtotime($dob)) : '';
                case 'age':
                    // Calculate age if DOB is available
                    $dob = $patient_data['dob'] ?? $patient_data['date_of_birth'] ?? '';
                    if (!empty($dob)) {
                        $dob_date = new \DateTime($dob);
                        $now = new \DateTime();
                        $interval = $now->diff($dob_date);
                        return $interval->y;
                    }
                    return '';
                case 'gender':
                    return $patient_data['gender'] ?? '';
                case 'email':
                    return $patient_data['patient_email'] ?? $patient_data['email'] ?? '';
                case 'mobile_number':
                    return $patient_data['mobile_number'] ?? $patient_data['phone'] ?? '';
                case 'address':
                    $address = $patient_data['address'] ?? '';
                    return !empty($address) ? $address : 'No address provided';
                case 'city':
                    return $patient_data['city'] ?? '';
                case 'country':
                    return $patient_data['country'] ?? '';
                case 'postal_code':
                    $postal_code = $patient_data['postal_code'] ?? $patient_data['postcode'] ?? '';
                    return !empty($postal_code) ? $postal_code : 'No postcode';
                default:
                    return '';
            }
        }

        // Handle doctor variables
        if ($section === 'doctor') {
            $doctor_id = !empty($request_data['doctor_id']) ? $request_data['doctor_id'] : 0;
            
            // Try alternate sources for doctor_id
            if (!$doctor_id && !empty($request_data['appointment']) && !empty($request_data['appointment']['doctor_id'])) {
                $doctor_id = $request_data['appointment']['doctor_id'];
            }
            
            if (!$doctor_id && !empty($request_data['encounter_id'])) {
                // Try to get doctor_id from the encounter
                global $wpdb;
                $doctor_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT doctor_id FROM {$wpdb->prefix}kc_patient_encounters WHERE id = %d",
                    $request_data['encounter_id']
                ));
            }

            if ($doctor_id) {
                $doctor = get_userdata($doctor_id);

                switch ($field) {
                    case 'name':
                        return $doctor ? $doctor->display_name : '';
                    case 'speciality':
                        return get_user_meta($doctor_id, 'specialties', true) ?? '';
                    case 'qualification':
                        return get_user_meta($doctor_id, 'qualifications', true) ?? '';
                    default:
                        return '';
                }
            }
        }

        // Handle clinic variables
        if ($section === 'clinic') {
            $clinic_id = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : 0;
            
            // Try alternate sources for clinic_id
            if (!$clinic_id && !empty($request_data['appointment']) && !empty($request_data['appointment']['clinic_id'])) {
                $clinic_id = $request_data['appointment']['clinic_id'];
            }
            
            if (!$clinic_id && !empty($request_data['encounter_id'])) {
                // Try to get clinic_id from the encounter
                global $wpdb;
                $clinic_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT clinic_id FROM {$wpdb->prefix}kc_patient_encounters WHERE id = %d",
                    $request_data['encounter_id']
                ));
            }

            if ($clinic_id) {
                global $wpdb;
                $clinic = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}kc_clinics WHERE id = %d",
                    $clinic_id
                ));

                switch ($field) {
                    case 'name':
                        return $clinic ? $clinic->name : '';
                    case 'address':
                        return $clinic ? $clinic->address : '';
                    case 'contact':
                        return $clinic ? $clinic->telephone_no : '';
                    case 'email':
                        return $clinic ? $clinic->email : '';
                    default:
                        return '';
                }
            }
        }

        // Handle encounter variables
        if ($section === 'encounter') {
            $encounter_id = $request_data['encounter_id'] ?? $request_data['encounterId'] ?? 0;
            if (!$encounter_id) {
                // For concern, history, examination, diagnosis, and plan, try to get from request directly
                switch ($field) {
                    case 'concerns':
                        return isset($request_data['concerns']) ? $this->formatArrayOrString($request_data['concerns']) : '';
                    case 'history':
                        return isset($request_data['history']) ? $this->formatArrayOrString($request_data['history']) : '';
                    case 'examination':
                        return isset($request_data['examination']) ? $this->formatArrayOrString($request_data['examination']) : '';
                    case 'diagnosis':
                        return isset($request_data['diagnosis']) ? $this->formatArrayOrString($request_data['diagnosis']) : '';
                    case 'plan':
                        return isset($request_data['plan']) ? $this->formatArrayOrString($request_data['plan']) : '';
                    case 'medical_history':
                        return isset($request_data['medical_history']) ? $this->formatArrayOrString($request_data['medical_history']) : '';
                    case 'allergies':
                        return isset($request_data['allergies']) ? $this->formatArrayOrString($request_data['allergies']) : '';
                    case 'medications':
                        return isset($request_data['medications']) ? $this->formatArrayOrString($request_data['medications']) : '';
                    case 'safeguarding':
                        return isset($request_data['safeguarding']) ? $this->formatArrayOrString($request_data['safeguarding']) : '';
                    default:
                        return '';
                }
            }

            // For fields that might be in encounter tabs
            $tab_fields = ['medical_history', 'allergies', 'medications', 'safeguarding', 'family_history', 'social_history', 'lifestyle', 'mental_health', 'preventative_care', 'safety_netting', 'systems_review', 'notes', 'comments'];

            if (in_array($field, $tab_fields)) {
                global $wpdb;

                // Get the tab content from the encounter_tabs table
                $query = $wpdb->prepare(
                    "SELECT content FROM {$wpdb->prefix}kc_encounter_tabs WHERE encounter_id = %d AND type = %s ORDER BY created_at ASC",
                    $encounter_id,
                    $field
                );

                error_log("Encounter tab query: {$query}");

                $tab_content = $wpdb->get_results($query);

                error_log("Found " . count($tab_content) . " tabs for {$field}");

                if (!empty($tab_content)) {
                    // Format the content from all tabs of this type
                    $formatted_content = '';
                    foreach ($tab_content as $tab) {
                        if (!empty($tab->content)) {
                            $formatted_content .= $tab->content . "\n\n";
                            error_log("Added tab content: " . substr($tab->content, 0, 50) . "...");
                        }
                    }
                    error_log("Final formatted content for {$field}: " . substr($formatted_content, 0, 50) . "...");
                    return trim($formatted_content);
                }

                // Fallback to request data if available
                if (isset($request_data[$field])) {
                    return $this->formatArrayOrString($request_data[$field]);
                }
                
                return '';
            }

            // Handle other encounter fields
            switch ($field) {
                case 'id':
                    return $encounter_id;
                case 'concerns':
                    return isset($request_data['concerns']) ? $this->formatArrayOrString($request_data['concerns']) : '';
                case 'history':
                    return isset($request_data['history']) ? $this->formatArrayOrString($request_data['history']) : '';
                case 'examination':
                    return isset($request_data['examination']) ? $this->formatArrayOrString($request_data['examination']) : '';
                case 'diagnosis':
                    return isset($request_data['diagnosis']) ? $this->formatArrayOrString($request_data['diagnosis']) : '';
                case 'plan':
                    return isset($request_data['plan']) ? $this->formatArrayOrString($request_data['plan']) : '';
                case 'date':
                    global $wpdb;
                    $date = $wpdb->get_var($wpdb->prepare(
                        "SELECT created_at FROM {$wpdb->prefix}kc_patient_encounters WHERE id = %d",
                        $encounter_id
                    ));
                    return $date ? date('d/m/Y', strtotime($date)) : '';
                default:
                    return '';
            }
        }

        // Handle appointment variables
        if ($section === 'appointment') {
            $appointment_id = 0;
            
            // Try to get appointment_id from different sources
            if (!empty($request_data['appointment_id'])) {
                $appointment_id = $request_data['appointment_id'];
            } elseif (!empty($request_data['appointment']) && is_array($request_data['appointment']) && !empty($request_data['appointment']['id'])) {
                $appointment_id = $request_data['appointment']['id'];
            } elseif (!empty($request_data['encounter_id'])) {
                // Try to get appointment_id from the encounter
                global $wpdb;
                $appointment_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT appointment_id FROM {$wpdb->prefix}kc_patient_encounters WHERE id = %d",
                    $request_data['encounter_id']
                ));
            }

            if ($appointment_id) {
                global $wpdb;
                $appointment = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}kc_appointments WHERE id = %d",
                    $appointment_id
                ));

                switch ($field) {
                    case 'date':
                        return $appointment ? date('d/m/Y', strtotime($appointment->appointment_start_date)) : '';
                    case 'time':
                        return $appointment ? date('H:i', strtotime($appointment->appointment_start_time)) : '';
                    default:
                        return '';
                }
            }
        }

        return '';
    }
    
    /**
     * Helper function to format an array or string properly
     *
     * @param mixed $data Array or string data
     * @return string Formatted string
     */
    /**
     * Helper debug function to log request data
     * 
     * @param array $request_data The request data to log
     */
    private function logTemplateData($request_data) {
        error_log('Template processing request data:');
        error_log('Patient data: ' . json_encode($request_data['patient_details'] ?? []));
        error_log('Doctor ID: ' . ($request_data['doctor_id'] ?? 'not set'));
        error_log('Clinic ID: ' . ($request_data['clinic_id'] ?? 'not set'));
        error_log('Appointment ID: ' . ($request_data['appointment_id'] ?? 'not set'));
        error_log('Encounter ID: ' . ($request_data['encounter_id'] ?? $request_data['encounterId'] ?? 'not set'));
    }
    
    /**
     * Helper function to format an array or string properly
     *
     * @param mixed $data Array or string data
     * @return string Formatted string
     */
    private function formatArrayOrString($data) {
        // If data is null or empty, return empty string
        if (empty($data)) {
            return '';
        }
        
        // If data is already a string, return it directly
        if (is_string($data)) {
            return $data;
        }
        
        // If data is an array, format it
        if (is_array($data)) {
            $formatted = '';
            
            // Check if it's an associative array
            $is_assoc = (bool)count(array_filter(array_keys($data), 'is_string'));
            
            if ($is_assoc) {
                // Handle associative array
                foreach ($data as $key => $value) {
                    if (is_array($value)) {
                        $formatted .= "- " . $key . ": " . $this->formatArrayOrString($value) . "\n";
                    } else if (!empty($value)) {
                        $formatted .= "- " . $key . ": " . $value . "\n";
                    }
                }
            } else {
                // Handle sequential array
                foreach ($data as $item) {
                    if (is_array($item)) {
                        // Recursively format nested arrays
                        $nested_format = $this->formatArrayOrString($item);
                        if (!empty($nested_format)) {
                            $formatted .= "- " . $nested_format . "\n";
                        }
                    } else if (!empty($item)) {
                        $item = trim((string)$item);
                        if (!empty($item)) {
                            $formatted .= "- " . $item . "\n";
                        }
                    }
                }
            }
            return trim($formatted);
        }
        
        // For any other data type, convert to string
        return (string)$data;
    }
}