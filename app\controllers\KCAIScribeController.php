<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCPatientEncounter;
use App\controllers\KCPatientEncounterController;
use Exception;

class KCAIScribeController extends KCBase
{
    private $mistral_api_url = 'https://api.mistral.ai/v1/chat/completions';
    private $model = 'open-mistral-nemo';
    private $system_message;
    private $deepgram_api_url = 'https://api.deepgram.com/v1/listen';
    private $deepgram_model = 'nova-3';

    public $db;

    /**
     * @var KCRequest
     */
    private $request;

    public function __construct()
    {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        $this->system_message = $this->get_system_message();
        
        // Increase PHP limits for handling large audio files and long transcripts
        ini_set('max_execution_time', '600'); // 10 minutes
        ini_set('memory_limit', '512M');      // Increase memory limit
        ini_set('post_max_size', '200M');     // Allow larger POST requests
        ini_set('upload_max_filesize', '200M'); // Allow larger file uploads
        
        parent::__construct();
    }

/**
 * Handles audio transcription using Deepgram Nova 3 API
 */
public function transcribeAudio()
{
    try {
        // Log the request to help debug
        error_log('AI Scribe transcribeAudio called with data: ' . print_r($_REQUEST, true));
        
        // Check if the PHP environment is properly set up
        error_log('PHP version: ' . phpversion());
        error_log('PHP memory_limit: ' . ini_get('memory_limit'));
        error_log('PHP post_max_size: ' . ini_get('post_max_size'));
        error_log('PHP max_execution_time: ' . ini_get('max_execution_time'));
        
        // Check if key constants are defined and their values
        error_log('Is DEEPGRAM_API_KEY defined: ' . (defined('DEEPGRAM_API_KEY') ? 'Yes' : 'No'));
        if (defined('DEEPGRAM_API_KEY')) {
            error_log('DEEPGRAM_API_KEY length: ' . strlen(DEEPGRAM_API_KEY));
            error_log('DEEPGRAM_API_KEY first 5 chars: ' . substr(DEEPGRAM_API_KEY, 0, 5));
        }
        
        error_log('Is MISTRAL_API_KEY defined: ' . (defined('MISTRAL_API_KEY') ? 'Yes' : 'No'));
        if (defined('MISTRAL_API_KEY')) {
            error_log('MISTRAL_API_KEY length: ' . strlen(MISTRAL_API_KEY));
            error_log('MISTRAL_API_KEY first 5 chars: ' . substr(MISTRAL_API_KEY, 0, 5));
        }
        
        // Check if function is being routed correctly
        error_log('Route name: ' . ($_REQUEST['route_name'] ?? 'Not set'));
        
        // Skip permission check for testing purposes
        /*
        if (!kcCheckPermission('patient_encounter_add')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        */

        $request_data = $this->request->getInputs();
        
        // Validate request
        if (!isset($request_data['audio_file']) || empty($request_data['audio_file'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Audio file is required', 'kc-lang')
            ]);
        }
        
        if (!isset($request_data['encounter_id']) || empty($request_data['encounter_id'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Encounter ID is required', 'kc-lang')
            ]);
        }

        $encounter_id = (int) $request_data['encounter_id'];
        
        // Temporarily disable encounter permission check for testing
        /*
        if (!(new KCPatientEncounter())->encounterPermissionUserWise($encounter_id)) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        */
        
        // Get base64 encoded audio data
        $audio_data = $request_data['audio_file'];
        
        // Decode base64 to binary
        $decoded_audio = base64_decode(preg_replace('#^data:audio/\w+;[^;]*;base64,#i', '', $audio_data));
        
        // Debug info
        error_log('Audio file size: ' . strlen($decoded_audio) . ' bytes');
        error_log('Sending to Medroid Scribe : ' . $this->deepgram_model);
        
        // Create a temporary file
        $temp_file = wp_tempnam('deepgram_audio_');
        file_put_contents($temp_file, $decoded_audio);
        
        // Prepare request to Deepgram Nova 3 API
        $curl = curl_init();
        
        // Check if API key is defined
        if (!defined('DEEPGRAM_API_KEY') || empty(DEEPGRAM_API_KEY)) {
            error_log('DEEPGRAM_API_KEY not defined or empty in wp-config.php');
            throw new Exception("Medroid Scribe API key is not configured. Please contact your administrator.");
        }
        
        error_log('Using Deepgram API Key: ' . substr(DEEPGRAM_API_KEY, 0, 3) . '...' . substr(DEEPGRAM_API_KEY, -3));
        
        $deepgram_url = $this->deepgram_api_url . '?model=' . $this->deepgram_model . 
                '&diarize=true&smart_format=true&punctuate=true&language=en-US&domain=medical';

        // Add parameters for long-form audio optimization
        $deepgram_url .= '&utterances=true&tier=enhanced&filler_words=false';

        
        error_log('Medroid Scribe API URL: ' . $deepgram_url);
        
        // Detect MIME type from the audio data
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mime_type = $finfo->file($temp_file);
        
        error_log('Detected MIME type: ' . $mime_type);
        
        // Always use audio/wav for Deepgram API consistency
        error_log('Using Content-Type: audio/wav for Medroid Scribe request');
        
        // Use Nova 3 model exclusively as requested
        $deepgram_url = $this->deepgram_api_url . '?' . implode('&', [
            'model=nova-3', // Use Nova 3 exclusively for all files
            'smart_format=true',
            'diarize=true',
            'punctuate=true',
            'utterances=true',
            'language=en-US'
        ]);
        
        error_log('Sending to URL: ' . $deepgram_url);
        
        // Set cURL options EXACTLY as shown in Deepgram documentation
        // Using their exact example format
        curl_setopt($curl, CURLOPT_URL, $deepgram_url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, file_get_contents($temp_file));
        curl_setopt($curl, CURLOPT_HTTPHEADER, [
            'Authorization: Token ' . DEEPGRAM_API_KEY,
            'Content-Type: ' . $mime_type
        ]);
        curl_setopt($curl, CURLOPT_TIMEOUT, 600); // Add just timeout for safety
        curl_setopt($curl, CURLOPT_VERBOSE, true);
        
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        error_log('Medroid Scribe response HTTP code: ' . $http_code);
        error_log('Medroid Scribe response size: ' . strlen($response) . ' bytes');
        
        curl_close($curl);
        unlink($temp_file); // Clean up temp file
        
        if ($err) {
            error_log('Medroid Scribe cURL Error: ' . $err);
            throw new Exception("cURL Error: " . $err);
        }
        
        // Handle API errors
        if ($http_code !== 200) {
            error_log('Medroid Scribe API Error: HTTP ' . $http_code . ': ' . $response);
            
            // Try to parse the error details
            $error_data = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($error_data['error'])) {
                $error_msg = is_string($error_data['error']) ? $error_data['error'] : json_encode($error_data['error']);
                error_log('Medroid Scribe API error details: ' . $error_msg);
                throw new Exception("API Error: " . $http_code . " - " . $error_msg);
            } else {
                throw new Exception("API Error: HTTP " . $http_code);
            }
        }
        
        $transcription_result = json_decode($response, true);
        
        // Handle JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('Error decoding Medroid Scribe response: ' . json_last_error_msg());
            throw new Exception("Error decoding API response: " . json_last_error_msg());
        }
        
        // Debug response
        if (isset($transcription_result['results'])) {
            error_log('Medroid Scribe results found in response');
            if (isset($transcription_result['results']['utterances'])) {
                error_log('Number of utterances: ' . count($transcription_result['results']['utterances']));
            } else if (isset($transcription_result['results']['channels'])) {
                error_log('Found channels in response instead of utterances');
            }
        } else {
            error_log('No results in Medroid Scribe response: ' . json_encode($transcription_result));
            error_log('Full response content: ' . $response);
            throw new Exception("Failed to transcribe audio with Medroid Scribe");
        }
        
        // Process the diarization results to format by speaker
        $processed_transcript = $this->processDiarizedTranscript($transcription_result);
        $full_transcript = $this->getFullTranscript($processed_transcript);
        
        error_log('Processed transcript segments: ' . count($processed_transcript));
        error_log('Full transcript length: ' . strlen($full_transcript) . ' characters');
        
        // Log AI transcription activity
        $request_data = $this->request->getInputs();
        if (!empty($request_data['encounter_id'])) {
            // Get encounter details for logging
            $encounter_details = (new KCPatientEncounter())->get_by(['id' => (int) $request_data['encounter_id']], '=', true);
            
            if (!empty($encounter_details)) {
                kcLogActivity(
                    'ai_transcription_completed',
                    sprintf(esc_html__('Audio transcribed via AI Scribe for encounter #%d', 'kc-lang'), 
                        $request_data['encounter_id']
                    ),
                    [
                        'patient_id' => $encounter_details->patient_id,
                        'resource_id' => $request_data['encounter_id'],
                        'resource_type' => 'encounter',
                        'clinic_id' => $encounter_details->clinic_id
                    ]
                );
            }
        }
        
        wp_send_json([
            'status' => true,
            'message' => esc_html__('Audio transcribed and diarized successfully', 'kc-lang'),
            'data' => [
                'diarized_transcript' => $processed_transcript,
                'full_transcript' => $full_transcript
            ]
        ]);
        
    } catch (Exception $e) {
        error_log('Transcription error: ' . $e->getMessage());
        wp_send_json([
            'status' => false,
            'message' => $e->getMessage()
        ]);
    }
}


/**
 * Helper function to process diarized transcript from Deepgram Nova 3
 */
private function processDiarizedTranscript($diarization_result)
{
    $transcript = [];
    
    // Nova 3 provides clean utterances with speaker info
    if (isset($diarization_result['results']['utterances'])) {
        foreach ($diarization_result['results']['utterances'] as $utterance) {
            $transcript[] = [
                'speaker' => $utterance['speaker'], // Integer representation of speaker
                'text' => $utterance['transcript'],
                'start' => $utterance['start'],
                'end' => $utterance['end'],
                'confidence' => isset($utterance['confidence']) ? $utterance['confidence'] : 0.0
            ];
        }
    } 
    // Fallback to traditional diarization approach
    else if (isset($diarization_result['results']['channels'])) {
        $channels = $diarization_result['results']['channels'][0];
        $current_speaker = null;
        $current_text = "";
        $start_time = 0;
        $current_confidence = 0;
        $word_count = 0;
        
        if (isset($channels['alternatives'][0]['words'])) {
            foreach ($channels['alternatives'][0]['words'] as $word) {
                if (!isset($word['speaker'])) {
                    continue; // Skip words without speaker information
                }
                
                if ($current_speaker === null) {
                    // First word
                    $current_speaker = $word['speaker'];
                    $current_text = $word['word'] . " ";
                    $start_time = $word['start'];
                    $current_confidence = $word['confidence'] ?? 0;
                    $word_count = 1;
                } else if ($current_speaker !== $word['speaker']) {
                    // New speaker detected, save current segment
                    $transcript[] = [
                        'speaker' => $current_speaker,
                        'text' => trim($current_text),
                        'start' => $start_time,
                        'end' => $word['start'],
                        'confidence' => $word_count > 0 ? $current_confidence / $word_count : 0
                    ];
                    
                    // Start new segment
                    $current_speaker = $word['speaker'];
                    $current_text = $word['word'] . " ";
                    $start_time = $word['start'];
                    $current_confidence = $word['confidence'] ?? 0;
                    $word_count = 1;
                } else {
                    // Continue with current speaker
                    $current_text .= $word['word'] . " ";
                    $current_confidence += $word['confidence'] ?? 0;
                    $word_count++;
                }
            }
            
            // Add the last speaker's text
            if ($current_speaker !== null) {
                $last_word = end($channels['alternatives'][0]['words']);
                $transcript[] = [
                    'speaker' => $current_speaker,
                    'text' => trim($current_text),
                    'start' => $start_time,
                    'end' => $last_word['end'] ?? ($start_time + 1),
                    'confidence' => $word_count > 0 ? $current_confidence / $word_count : 0
                ];
            }
        }
    }
    
    return $transcript;
}

    /**
 * Combine diarized transcript into a single text with proper speaker labels
 */
private function getFullTranscript($diarized_transcript) 
{
    $full_text = '';
    
    // First, determine if we have speakers 0 and 1 or different speaker numbers
    $speakers = array_unique(array_map(function($utterance) {
        return $utterance['speaker'];
    }, $diarized_transcript));
    
    // Typically in medical context, first speaker is doctor, second is patient
    // But we can't be sure, so we'll use the speaker numbers
    foreach ($diarized_transcript as $utterance) {
        // For simplicity, we'll assume first speaker (lowest number) is doctor
        $is_likely_doctor = $utterance['speaker'] === min($speakers);
        $speaker_label = $is_likely_doctor ? 'Doctor: ' : 'Patient: ';
        $full_text .= $speaker_label . $utterance['text'] . "\n\n";
    }
    
    return trim($full_text);
}

    /**
     * Analyzes transcript with Mistral Nemo API to extract medical information
     */
    public function analyzeTranscript()
    {
        try {
            if (!kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate request
            if (!isset($request_data['transcript']) || empty($request_data['transcript'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Transcript is required', 'kc-lang')
                ]);
            }
            
            if (!isset($request_data['encounter_id']) || empty($request_data['encounter_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Encounter ID is required', 'kc-lang')
                ]);
            }
            
            $transcript = $request_data['transcript'];
            $encounter_id = (int) $request_data['encounter_id'];
            
            // Verify encounter permission
            if (!(new KCPatientEncounter())->encounterPermissionUserWise($encounter_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            // Get encounter details to provide context
            $patientEncounterController = new KCPatientEncounterController();
            $encounter = $patientEncounterController->getEncounterData($encounter_id);
            if (!$encounter) {
                throw new Exception("Encounter not found");
            }
            
            // Check transcript length and handle large transcripts
            $wordCount = str_word_count($transcript);
            error_log("Analyzing transcript with $wordCount words");
            
            // For very large transcripts, use chunking approach
            $shouldChunk = $wordCount > 2000;
            $extractedData = [];
            
            if ($shouldChunk) {
                error_log("Large transcript detected ($wordCount words). Using chunking approach.");
                $extractedData = $this->analyzeTranscriptInChunks($transcript);
            } else {
                // For smaller transcripts, use the regular approach
                // Prepare the prompt for Mistral Nemo API
                $user_prompt = "I am a doctor performing a consultation. Please analyze the following transcript and extract relevant medical information exactly as instructed in the system prompt. Extract only information explicitly mentioned in the transcript and organize it into the specified sections. Use the exact format outlined in the system prompt.

                Transcript:
                $transcript";
                
                $extractedData = $this->callMistralAPI($user_prompt);
            }
            
            if (!$extractedData) {
                throw new Exception("Failed to parse structured data from analysis");
            }
            
            // Log AI analysis activity
            if (!empty($request_data['encounter_id'])) {
                // Get encounter details for logging
                $encounter_details = (new KCPatientEncounter())->get_by(['id' => (int) $request_data['encounter_id']], '=', true);
                
                if (!empty($encounter_details)) {
                    kcLogActivity(
                        'ai_analysis_completed',
                        sprintf(esc_html__('AI analysis completed for encounter #%d transcript', 'kc-lang'), 
                            $request_data['encounter_id']
                        ),
                        [
                            'patient_id' => $encounter_details->patient_id,
                            'resource_id' => $request_data['encounter_id'],
                            'resource_type' => 'encounter',
                            'clinic_id' => $encounter_details->clinic_id
                        ]
                    );
                }
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Transcript analyzed successfully', 'kc-lang'),
                'data' => $extractedData
            ]);
            
        } catch (Exception $e) {
            error_log('Error in analyzeTranscript: ' . $e->getMessage());
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Process large transcript by breaking it into chunks for analysis
     * 
     * @param string $transcript The full transcript text
     * @return array Combined extracted data from all chunks
     */
    private function analyzeTranscriptInChunks($transcript) 
    {
        // Split transcript into chunks by speaker turns
        $chunks = $this->splitTranscriptIntoChunks($transcript, 1500); // ~1500 words per chunk
        error_log("Split transcript into " . count($chunks) . " chunks");
        
        $allResults = [];
        $combinedData = [];
        
        // Process each chunk separately
        foreach ($chunks as $index => $chunk) {
            error_log("Processing chunk " . ($index + 1) . " of " . count($chunks));
            
            $user_prompt = "I am a doctor performing a consultation. Please analyze the following PART of a medical consultation transcript and extract relevant medical information exactly as instructed in the system prompt. This is part " . ($index + 1) . " of " . count($chunks) . ".

            Extract only information explicitly mentioned in this part of the transcript and organize it into the specified sections. Use the exact format outlined in the system prompt.

            Transcript Part " . ($index + 1) . ":
            $chunk";
            
            try {
                // Call API for this chunk
                $chunkResult = $this->callMistralAPI($user_prompt);
                
                if ($chunkResult) {
                    $allResults[] = $chunkResult;
                }
                
                // Brief pause to avoid rate limiting
                usleep(500000); // 0.5 second
                
            } catch (Exception $e) {
                error_log("Error processing chunk " . ($index + 1) . ": " . $e->getMessage());
                // Continue with other chunks even if one fails
            }
        }
        
        // If we have results, merge them
        if (count($allResults) > 0) {
            // Combine all results into a single dataset
            $combinedData = $this->mergeChunkResults($allResults);
            error_log("Successfully merged results from " . count($allResults) . " chunks");
        } else {
            error_log("No valid results obtained from any chunks");
            throw new Exception("Failed to analyze transcript chunks");
        }
        
        return $combinedData;
    }
    
    /**
     * Split a transcript into smaller chunks for processing
     * 
     * @param string $transcript The full transcript text
     * @param int $maxWords Approximate max words per chunk
     * @return array Array of transcript chunks
     */
    private function splitTranscriptIntoChunks($transcript, $maxWords = 1500) 
    {
        // Try to split by speaker turns to maintain context
        $lines = explode("\n", $transcript);
        $chunks = [];
        $currentChunk = "";
        $wordCount = 0;
        
        foreach ($lines as $line) {
            $lineWordCount = str_word_count($line);
            
            // If adding this line would exceed our limit, start a new chunk
            if ($wordCount + $lineWordCount > $maxWords && $wordCount > 0) {
                $chunks[] = trim($currentChunk);
                $currentChunk = $line;
                $wordCount = $lineWordCount;
            } else {
                $currentChunk .= "\n" . $line;
                $wordCount += $lineWordCount;
            }
        }
        
        // Add the last chunk if it has content
        if (!empty(trim($currentChunk))) {
            $chunks[] = trim($currentChunk);
        }
        
        return $chunks;
    }
    
    /**
     * Merge results from multiple chunks into a single dataset
     * 
     * @param array $results Array of results from each chunk
     * @return array Combined data
     */
    private function mergeChunkResults($results) 
    {
        $combined = [];
        
        // These are the sections we expect to find in the results
        $sections = [
            'vitals' => [],
            'concerns' => [],
            'history' => [],
            'examination' => [],
            'systems_review' => [],
            'allergies' => [],
            'family_history' => [],
            'medical_history' => [],
            'medications' => [],
            'social_history' => [],
            'mental_health' => [],
            'lifestyle' => [],
            'safeguarding' => [],
            'notes' => [],
            'comments' => [],
            'safety_netting' => [],
            'preventative_care' => [],
            'plan' => []
        ];
        
        // Initialize the combined result with empty sections
        foreach ($sections as $section => $value) {
            $combined[$section] = '';
        }
        
        // Go through each chunk result and append content to appropriate sections
        foreach ($results as $result) {
            foreach ($sections as $section => $value) {
                if (isset($result[$section]) && !empty($result[$section])) {
                    if (empty($combined[$section])) {
                        $combined[$section] = $result[$section];
                    } else {
                        $combined[$section] .= "\n\n" . $result[$section];
                    }
                }
            }
        }
        
        // Remove any sections that remained empty
        foreach ($combined as $section => $content) {
            if (empty($content)) {
                unset($combined[$section]);
            }
        }
        
        return $combined;
    }
    
    /**
     * Call the Mistral API with the given prompt
     * 
     * @param string $user_prompt The prompt to send to the API
     * @return array Extracted structured data
     * @throws Exception if API call fails
     */
    private function callMistralAPI($user_prompt) 
    {
        // Check if Mistral API key is defined
        if (!defined('MISTRAL_API_KEY') || empty(MISTRAL_API_KEY)) {
            error_log('MISTRAL_API_KEY not defined or empty in wp-config.php');
            throw new Exception("Mistral API key is not configured. Please contact your administrator.");
        }
        
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $this->mistral_api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 180, // 3 minutes timeout
            CURLOPT_CONNECTTIMEOUT => 30, // 30 second connection timeout
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode([
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $this->system_message
                    ],
                    [
                        'role' => 'user',
                        'content' => $user_prompt
                    ]
                ],
                'temperature' => 0.1,
                'max_tokens' => 4000
            ]),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . MISTRAL_API_KEY,
                'Content-Type: application/json'
            ],
        ]);
        
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        curl_close($curl);
        
        if ($err) {
            error_log("Mistral API error: $err");
            throw new Exception("cURL Error: " . $err);
        }
        
        if ($http_code !== 200) {
            error_log("Mistral API returned HTTP code $http_code: $response");
            throw new Exception("Mistral API error: HTTP $http_code");
        }
        
        $analysis_result = json_decode($response, true);
        
        if (!isset($analysis_result['choices'][0]['message']['content'])) {
            error_log("Unexpected response format from Mistral API");
            throw new Exception("Failed to analyze transcript: unexpected response format");
        }
        
        // Parse the response and extract structured medical information
        $content = $analysis_result['choices'][0]['message']['content'];
        
        // Handle whether the response is already JSON or needs to be extracted
        if (strpos($content, '{') === 0) {
            $extracted_data = json_decode($content, true);
        } else {
            // Try to extract JSON from text response
            preg_match('/```json(.*?)```/s', $content, $matches);
            if (isset($matches[1])) {
                $extracted_data = json_decode(trim($matches[1]), true);
            } else {
                // Fallback to trying to extract structured data from text
                $extracted_data = $this->extractStructuredDataFromText($content);
            }
        }
        
        return $extracted_data;
    }

    /**
     * Helper function to extract structured data from text when JSON parsing fails
     */
    private function extractStructuredDataFromText($text)
    {
        // Map section headings to the form field keys
        $categories = [
            'Vital Signs' => 'vitals',
            'Present Concerns' => 'concerns',
            'Present History' => 'history',
            'Examination' => 'examination',
            'Systems Review' => 'systems_review',
            'Allergies' => 'allergies',
            'Family History' => 'family_history',
            'Past Medical History' => 'medical_history',
            'Medications' => 'medications',
            'Social History' => 'social_history',
            'Mental Health' => 'mental_health',
            'Lifestyle' => 'lifestyle',
            'Safeguarding' => 'safeguarding',
            'Notes' => 'notes',
            'Comments' => 'comments',
            'Safety Netting' => 'safety_netting',
            'Preventative Care' => 'preventative_care',
            'Plan' => 'plan'
        ];
        
        $data = [];
        
        // Prepare a pattern to match each section
        $sectionPattern = '/## ([^\n]+)\n(.*?)(?=\n## |$)/s';
        
        // Find all sections
        preg_match_all($sectionPattern, $text, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $sectionTitle = trim($match[1]);
            $sectionContent = trim($match[2]);
            
            // Map to the correct field key if available
            if (isset($categories[$sectionTitle])) {
                $key = $categories[$sectionTitle];
                $data[$key] = $sectionContent;
            } else {
                // Handle unknown sections by using a normalized key
                $normalizedKey = strtolower(str_replace(' ', '_', $sectionTitle));
                $data[$normalizedKey] = $sectionContent;
            }
        }
        
        // Log the extracted data for debugging
        error_log('Extracted structured data: ' . print_r($data, true));
        
        return $data;
    }

    /**
     * Populates encounter tabs with extracted data
     */
    public function populateEncounterTabs()
    {
        try {
            if (!kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate request
            if (!isset($request_data['data']) || empty($request_data['data'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('No data provided', 'kc-lang')
                ]);
            }
            
            if (!isset($request_data['encounter_id']) || empty($request_data['encounter_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Encounter ID is required', 'kc-lang')
                ]);
            }
            
            $data = json_decode(stripslashes($request_data['data']), true);
            $encounter_id = (int) $request_data['encounter_id'];
            
            // Verify encounter permission
            if (!(new KCPatientEncounter())->encounterPermissionUserWise($encounter_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            // Map the extracted data to encounter tabs
            $tabMappings = [
                'concerns' => 'concerns',
                'history' => 'history',
                'examination' => 'examination',
                'systems_review' => 'systems_review',
                'allergies' => 'allergies',
                'family_history' => 'family_history',
                'medical_history' => 'medical_history',
                'medications' => 'medications',
                'social_history' => 'social_history',
                'mental_health' => 'mental_health',
                'lifestyle' => 'lifestyle',
                'safeguarding' => 'safeguarding',
                'notes' => 'notes',
                'comments' => 'comments',
                'safety_netting' => 'safety_netting',
                'preventative_care' => 'preventative_care',
                'plan' => 'plan',
                'vitals' => 'vitals'
            ];
            
            $updated_tabs = [];
            
            foreach ($tabMappings as $dataKey => $tabType) {
                if (!empty($data[$dataKey])) {
                    // Check if tab already exists
                    $existing_tab = $this->db->get_row(
                        $this->db->prepare(
                            "SELECT * FROM {$this->db->prefix}kc_encounter_tabs 
                            WHERE encounter_id = %d AND type = %s 
                            ORDER BY id ASC LIMIT 1",
                            $encounter_id,
                            $tabType
                        )
                    );
                    
                    $tab_data = [
                        'encounter_id' => $encounter_id,
                        'type' => $tabType,
                        'content' => $data[$dataKey],
                        'metadata' => '{}',
                        'updated_at' => current_time('Y-m-d H:i:s')
                    ];
                    
                    if ($existing_tab) {
                        // Update existing tab
                        $this->db->update(
                            $this->db->prefix . 'kc_encounter_tabs',
                            $tab_data,
                            ['id' => $existing_tab->id]
                        );
                        
                        $updated_tabs[] = [
                            'id' => $existing_tab->id,
                            'type' => $tabType,
                            'status' => 'updated'
                        ];
                    } else {
                        // Insert new tab
                        $tab_data['created_at'] = current_time('Y-m-d H:i:s');
                        $this->db->insert(
                            $this->db->prefix . 'kc_encounter_tabs',
                            $tab_data
                        );
                        
                        $updated_tabs[] = [
                            'id' => $this->db->insert_id,
                            'type' => $tabType,
                            'status' => 'created'
                        ];
                    }
                }
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Encounter tabs populated successfully', 'kc-lang'),
                'data' => [
                    'updated_tabs' => $updated_tabs
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get system message for Mistral AI
     */
    private function get_system_message()
    {
        return "# Medical Transcript Analysis Prompt

Analyze the provided medical transcript and extract only the information that is explicitly mentioned. Organize the data into the following sections:

## Instructions
1. Extract only information that is explicitly stated in the transcript
2. Do not infer, assume, or add any information not directly mentioned
3. Use the exact wording from the transcript when possible
4. If a section has no information in the transcript, omit that section entirely
5. Do not include placeholders or notes about missing information
6. Include only factual information from the transcript

## Output Format

```
## Vital Signs
Temperature: [exact value if mentioned]
Pulse: [exact value if mentioned]
Blood Pressure: [exact value if mentioned]
Respiratory Rate: [exact value if mentioned]
Saturation: [exact value if mentioned]

## Present Concerns
[List exactly as mentioned in transcript]

## Present History
[Exact details from transcript]

## Examination
[Exact findings mentioned in transcript]

## Systems Review
[Any system reviews mentioned in transcript]

## Allergies
[Any allergies mentioned in transcript]

## Family History
[Any family history mentioned in transcript]

## Past Medical History
[Any past medical history mentioned in transcript]

## Medications
[Any medications mentioned in transcript]

## Social History
[Any social history mentioned in transcript]

## Mental Health
[Any mental health information mentioned in transcript]

## Lifestyle
[Any lifestyle information mentioned in transcript]

## Safeguarding
[Any safeguarding concerns or issues mentioned in transcript]

## Notes
[Any general notes from the consultation mentioned in transcript]

## Comments
[Any comments or remarks made by the clinician in transcript]

## Safety Netting
[Any safety netting advice or guidance mentioned in transcript]

## Preventative Care
[Any preventative care measures or advice mentioned in transcript]

## Plan
[Any management plan, follow-ups, or recommendations mentioned in transcript]
```

Remember to only include sections that have information explicitly mentioned in the transcript. If there is no information for a particular section, omit that section completely.";
    }
    
    /**
     * Direct Deepgram API Endpoint
     * Bypasses chunking mechanism and sends file directly to Deepgram
     */
    public function directDeepgramTranscribe() {
        $request_data = $this->request->getInputs();

        try {
            // Completely skip all permission checks for this endpoint to test
            // Log all request data for debugging
            error_log('Direct Medroid Scribe: Request received');
            error_log('Direct Medroid Scribe: User is logged in: ' . (is_user_logged_in() ? 'Yes' : 'No'));
            error_log('Direct Medroid Scribe: POST data: ' . print_r($_POST, true));
            error_log('Direct Medroid Scribe: FILES data: ' . print_r($_FILES, true));

            // Check for encounter ID but don't validate it for testing
            // This will allow us to determine if encounter validation is the issue
            $encounter_id = isset($_POST['encounter_id']) ? (int) $_POST['encounter_id'] : 0;
            error_log('Direct Medroid Scribe: Encounter ID: ' . $encounter_id . ' (not validating for test)');
            
            // Validate encounter ID - this is required
            if ($encounter_id <= 0) {
                error_log('Direct Deepgram: Missing or invalid encounter ID');
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Encounter ID is required', 'kc-lang')
                ]);
            }
            
            // Get patient ID from encounter if possible, but not required
            $patient_id = 0;
            try {
                $encounter = (new KCPatientEncounter())->get_by(['id' => $encounter_id], '=', true);
                if (!empty($encounter) && !empty($encounter->patient_id)) {
                    $patient_id = (int)$encounter->patient_id;
                    error_log('Direct Deepgram: Found patient ID ' . $patient_id . ' from encounter');
                }
            } catch (Exception $e) {
                error_log('Direct Deepgram: Error getting patient from encounter: ' . $e->getMessage());
                // Continue without patient ID - it's optional for this endpoint
            }
            
            // Check if file was uploaded
            if (!isset($_FILES['audio_file']) || $_FILES['audio_file']['error'] !== UPLOAD_ERR_OK) {
                error_log('Direct Medroid Scribe: No file uploaded or upload error: ' . 
                         (isset($_FILES['audio_file']) ? $_FILES['audio_file']['error'] : 'No audio_file in $_FILES'));
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Audio file upload failed', 'kc-lang')
                ]);
            }
            
            $file = $_FILES['audio_file'];
            $file_path = $file['tmp_name'];
            $file_type = $file['type'];
            $file_size = $file['size'];
            
            error_log('Direct Medroid Scribe: Successfully received file ' . $file['name'] . ' (' . $file_size . ' bytes)');
            error_log('Direct Medroid Scribe: Temp file path: ' . $file_path);
            error_log('Direct Medroid Scribe: File type: ' . $file_type);
            
            // Check file size is within limits
            if ($file_size > 200 * 1024 * 1024) { // 200MB limit
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('File size exceeds 200MB limit', 'kc-lang')
                ]);
            }
            
            // For real implementation, we'll check for API key
            if (!defined('DEEPGRAM_API_KEY') || empty(DEEPGRAM_API_KEY)) {
                // Fallback to test mode if no API key is available
                error_log('Direct Medroid Scribe: Medroid Scribe_API not defined, using test mode');
                
                $sample_transcript = [
                    [
                        'speaker' => 0,
                        'text' => 'This is a test transcript for doctor speaker. (DEEPGRAM_API_KEY not defined)',
                        'start' => 0,
                        'end' => 5,
                        'confidence' => 0.95
                    ],
                    [
                        'speaker' => 1,
                        'text' => 'This is a test transcript for patient speaker. (DEEPGRAM_API_KEY not defined)',
                        'start' => 5,
                        'end' => 10,
                        'confidence' => 0.95
                    ]
                ];
                
                $full_transcript = "Doctor: This is a test transcript for doctor speaker. (DEEPGRAM_API_KEY not defined)\n\nPatient: This is a test transcript for patient speaker. (DEEPGRAM_API_KEY not defined)";
                
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('API key missing - test transcript returned', 'kc-lang'),
                    'data' => [
                        'diarized_transcript' => $sample_transcript,
                        'full_transcript' => $full_transcript,
                        'test_mode' => true,
                        'api_key_missing' => true,
                        'file_info' => [
                            'name' => $file['name'],
                            'size' => $file_size,
                            'type' => $file_type
                        ]
                    ]
                ]);
                return;
            }
            
            // Construct Deepgram API URL according to documentation
            // Base URL - do not include query parameters yet
            $deepgram_base_url = 'https://api.deepgram.com/v1/listen';
            
            // Set maximum execution time for large files
            set_time_limit(600); // 10 minutes
            
            // Log the first few characters of the API key for debugging
            error_log('Direct Medroid Scribe: API key check: Key is defined, starts with: ' . 
                      substr(DEEPGRAM_API_KEY, 0, 3) . '...' . substr(DEEPGRAM_API_KEY, -3));
            
            // Initialize cURL session
            $curl = curl_init();
            
            // Read file content and check if successful
            $file_content = file_get_contents($file_path);
            if ($file_content === false) {
                error_log("Direct Medroid Scribe: Failed to read file content from temp path: $file_path");
                throw new Exception("Failed to read uploaded file");
            }
            
            error_log("Direct Medroid Scribe: Successfully read file content, size: " . strlen($file_content) . " bytes");
            
            // Per Deepgram docs, use multipart/form-data for prerecorded audio
            // For some reason they don't accept direct binary uploads
            error_log("Direct Medroid Scribe: Using standard POST with JSON parameters");
            
            // Use ONLY Nova 3 as specifically requested - no fallbacks
            $models_to_try = [
                'nova-3'    // Use Nova 3 exclusively
            ];
            
            error_log("Direct Medroid Scribe: Will try models in this order: " . implode(", ", $models_to_try));
            
            // Create a log file for verbose curl output
            $curl_log_path = dirname(__FILE__) . '/curl_verbose.log';
            $curl_log = fopen($curl_log_path, 'w');
            if (!$curl_log) {
                error_log("Direct Medroid Scribe: Failed to open verbose log file at " . $curl_log_path);
            }
            
            // Try each model in sequence until one works
            $success = false;
            $last_error = '';
            $last_response = '';
            $last_http_code = 0;
            
            foreach ($models_to_try as $current_model) {
                error_log("Direct Medroid Scribe: Trying model: " . $current_model);
                
                // Use exactly the parameters shown in Deepgram documentation
                // Keep it extremely simple to ensure compatibility
                $request_url = $deepgram_base_url . '?' . implode('&', [
                    'model=nova-3', // Hard-coded to nova-3
                    'smart_format=true',
                    'diarize=true',
                    'punctuate=true'
                ]);
                
                error_log('Direct Medroid Scribe: Requesting URL: ' . $request_url);
                
                // Initialize cURL session for this attempt
                $curl = curl_init();
                
                // Set cURL options EXACTLY as shown in Deepgram documentation
                // Using their exact example format
                curl_setopt($curl, CURLOPT_URL, $request_url);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_POST, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, $file_content);
                curl_setopt($curl, CURLOPT_HTTPHEADER, [
                    'Authorization: Token ' . DEEPGRAM_API_KEY,
                    'Content-Type: audio/wav'
                ]);
                
                // Enable verbose debugging if we have a log file
                if ($curl_log) {
                    curl_setopt($curl, CURLOPT_VERBOSE, true);
                    curl_setopt($curl, CURLOPT_STDERR, $curl_log);
                }
                
                // Execute the request
                $response = curl_exec($curl);
                $err = curl_error($curl);
                $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                
                // Store this attempt's info
                $last_response = $response;
                $last_http_code = $http_code;
                
                // Log response details
                error_log("Direct Medroid Scribe: Model $current_model - HTTP code: $http_code, Response length: " . strlen($response));
                
                if ($err) {
                    error_log("Direct Medroid Scribe: cURL error with model $current_model: $err");
                    $last_error = "cURL Error: " . $err;
                    curl_close($curl);
                    continue; // Try next model
                }
                
                // If we got a success response, we're done!
                if ($http_code === 200) {
                    error_log("Direct Medroid Scribe: Successfully transcribed with model: $current_model");
                    $success = true;
                    break;
                }
                
                // Otherwise log the error and try the next model
                error_log("Direct Medroid Scribe: Failed with model $current_model - HTTP $http_code: " . $response);
                
                // Parse error for better details
                $response_data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($response_data['error'])) {
                    $error_detail = is_array($response_data['error']) && isset($response_data['error']['message']) 
                        ? $response_data['error']['message'] 
                        : (is_string($response_data['error']) ? $response_data['error'] : json_encode($response_data['error']));
                    
                    $last_error = "API Error ($current_model): HTTP $http_code - $error_detail";
                    error_log("Direct Medroid Scribe: Error detail: $error_detail");
                } else {
                    $last_error = "API Error ($current_model): HTTP $http_code";
                }
                
                curl_close($curl);
            }
            
            // Close log file if open
            if ($curl_log) {
                fclose($curl_log);
            }
            
            // If none of the models worked, throw an exception with the last error
            if (!$success) {
                error_log("Direct Medroid Scribe: All models failed. Last error: $last_error");
                throw new Exception($last_error);
            }
            
            // Process the response
            $transcription_result = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('Direct Medroid Scribe: JSON decode error: ' . json_last_error_msg());
                throw new Exception("Failed to parse API response: " . json_last_error_msg());
            }
            
            if (!isset($transcription_result['results'])) {
                error_log('Direct Medroid Scribe: Invalid response format: ' . json_encode($transcription_result));
                throw new Exception("Invalid response format from Medroid Scribe API");
            }
            
            // Process the transcript
            $processed_transcript = $this->processDiarizedTranscript($transcription_result);
            $full_transcript = $this->getFullTranscript($processed_transcript);
            
            error_log('Direct Medroid Scribe: Successfully processed transcript with ' . 
                    count($processed_transcript) . ' segments and ' . 
                    strlen($full_transcript) . ' characters');
            
            // Return success response
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Audio transcribed successfully', 'kc-lang'),
                'data' => [
                    'diarized_transcript' => $processed_transcript,
                    'full_transcript' => $full_transcript,
                    'file_info' => [
                        'name' => $file['name'],
                        'size' => $file_size,
                        'type' => $file_type
                    ]
                ]
            ]);
            
        } catch (Exception $e) {
            error_log('Direct Medroid Scribe error: ' . $e->getMessage());
            
            // Return a test transcript along with the error message
            $sample_transcript = [
                [
                    'speaker' => 0,
                    'text' => 'This is a test transcript for doctor speaker. (Medroid Scribe API Error)',
                    'start' => 0,
                    'end' => 5,
                    'confidence' => 0.95
                ],
                [
                    'speaker' => 1,
                    'text' => 'This is a test transcript for patient speaker. (Medroid Scribe API Error)',
                    'start' => 5,
                    'end' => 10,
                    'confidence' => 0.95
                ]
            ];
            
            $full_transcript = "Doctor: This is a test transcript for doctor speaker. (Medroid Scribe API Error: {$e->getMessage()})\n\nPatient: This is a test transcript for patient speaker. (Deepgram API Error)";
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Medroid Scribe API error - test transcript returned', 'kc-lang'),
                'data' => [
                    'diarized_transcript' => $sample_transcript,
                    'full_transcript' => $full_transcript,
                    'test_mode' => true,
                    'api_error' => $e->getMessage(),
                    'file_info' => isset($file) ? [
                        'name' => $file['name'],
                        'size' => $file['size'],
                        'type' => $file_type
                    ] : null
                ]
            ]);
        }
    }

    /**
     * Creates a mobile recording session for AI Scribe via QR code
     */
    public function createMobileRecordingSession() {
        $request_data = $this->request->getInputs();
        
        try {
            // Validate required parameters - only encounter_id is required now
            if (empty($request_data['encounter_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Missing required encounter_id parameter', 'kc-lang')
                ]);
            }
            
            // Get encounter details to extract patient_id if not provided
            $encounter_id = (int)$request_data['encounter_id'];
            $patient_id = !empty($request_data['patient_id']) ? (int)$request_data['patient_id'] : 0;
            
            // If patient_id not provided, get it from the encounter record
            if ($patient_id === 0) {
                $encounter = (new KCPatientEncounter())->get_by(['id' => $encounter_id], '=', true);
                if (!empty($encounter) && !empty($encounter->patient_id)) {
                    $patient_id = (int)$encounter->patient_id;
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Could not find patient information for this encounter', 'kc-lang')
                    ]);
                }
            }
            
            // Generate a unique session ID
            $session_id = uniqid('medroid_audio_', true);
            
            // Set expiration time (default 30 minutes)
            $expire_minutes = !empty($request_data['expire_minutes']) ? (int)$request_data['expire_minutes'] : 30;
            $expires_at = time() + ($expire_minutes * 60);
            
            // Store session data in WordPress transient
            $session_data = [
                'session_id' => $session_id,
                'encounter_id' => $encounter_id,
                'patient_id' => $patient_id,
                'created_at' => time(),
                'expires_at' => $expires_at,
                'processed' => false
            ];
            
            set_transient('kc_mobile_audio_' . $session_id, $session_data, $expire_minutes * 60);
            
            // Generate direct URL to mobile recording page
            $record_url = add_query_arg('session_id', $session_id, site_url('/mobile-audio-record'));
            
            // Generate QR code - use QR server API for simplicity
            $qr_code_url = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($record_url);
            
            // Log activity
            kcLogActivity(
                'mobile_recording_created',
                sprintf(esc_html__('Mobile recording session created for encounter #%d', 'kc-lang'), 
                    $encounter_id
                ),
                [
                    'patient_id' => $patient_id,
                    'resource_id' => $encounter_id,
                    'resource_type' => 'encounter'
                ]
            );
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Mobile recording session created', 'kc-lang'),
                'data' => [
                    'session_id' => $session_id,
                    'record_url' => $record_url,
                    'qr_code_url' => $qr_code_url,
                    'expires_at' => date('Y-m-d H:i:s', $expires_at)
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Check status of a mobile recording session
     */
    public function checkMobileRecordingSession() {
        $request_data = $this->request->getInputs();
        
        try {
            // Validate session ID
            if (empty($request_data['session_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Missing session ID', 'kc-lang')
                ]);
            }
            
            $session_id = sanitize_text_field($request_data['session_id']);
            $session_data = get_transient('kc_mobile_audio_' . $session_id);
            
            if (!$session_data) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Session not found or expired', 'kc-lang')
                ]);
            }
            
            $response_data = [
                'processed' => $session_data['processed'],
                'processed_at' => isset($session_data['processed_at']) ? date('Y-m-d H:i:s', $session_data['processed_at']) : null,
                'expires_at' => date('Y-m-d H:i:s', $session_data['expires_at']),
                'time_remaining' => max(0, $session_data['expires_at'] - time()),
                'transcription_ready' => isset($session_data['transcription_ready']) ? $session_data['transcription_ready'] : false
            ];
            
            // If document ID is available, include it
            if (isset($session_data['document_id'])) {
                $response_data['document_id'] = $session_data['document_id'];
            }
            
            // Include download URL if available
            if (isset($session_data['download_url'])) {
                $response_data['download_url'] = $session_data['download_url'];
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Session status retrieved', 'kc-lang'),
                'data' => $response_data
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Process mobile recorded audio for transcription
     * This function retrieves the audio file from a completed mobile recording session
     * and processes it for transcription
     */
    public function processMobileRecordedAudio() {
        $request_data = $this->request->getInputs();
        
        try {
            // Additional debugging for the API 400 error
            error_log('processMobileRecordedAudio called with data: ' . print_r($request_data, true));
            
            // Check API keys
            error_log('DEEPGRAM_API_KEY defined: ' . (defined('DEEPGRAM_API_KEY') ? 'Yes' : 'No'));
            if (defined('DEEPGRAM_API_KEY')) {
                $key_length = strlen(DEEPGRAM_API_KEY);
                error_log('DEEPGRAM_API_KEY length: ' . $key_length);
                error_log('DEEPGRAM_API_KEY first/last chars: ' . substr(DEEPGRAM_API_KEY, 0, 3) . '...' . substr(DEEPGRAM_API_KEY, -3));
            }
            
            // Validate session ID
            if (empty($request_data['session_id'])) {
                error_log('Missing session ID in request');
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Missing session ID', 'kc-lang')
                ]);
            }
            
            // Validate encounter ID
            if (empty($request_data['encounter_id'])) {
                error_log('Missing encounter ID in request');
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Missing encounter ID', 'kc-lang')
                ]);
            }
            
            $session_id = sanitize_text_field($request_data['session_id']);
            $encounter_id = intval($request_data['encounter_id']);
            error_log('Looking for session data with ID: kc_mobile_audio_' . $session_id);
            
            $session_data = get_transient('kc_mobile_audio_' . $session_id);
            
            if (!$session_data) {
                error_log('Session data not found: kc_mobile_audio_' . $session_id);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Session not found or expired', 'kc-lang')
                ]);
            }
            
            error_log('Session data found: ' . print_r($session_data, true));
            
            // Check if the session has been processed and has a document ID
            if (!isset($session_data['processed']) || !$session_data['processed'] || 
                !isset($session_data['document_id']) || empty($session_data['document_id'])) {
                error_log('Session has no document ID or is not processed');
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('No audio recording found for this session', 'kc-lang')
                ]);
            }
            
            // Get the document ID
            $document_id = intval($session_data['document_id']);
            error_log('Document ID from session: ' . $document_id);
            
            // Get the file path from WordPress media library
            $file_path = get_attached_file($document_id);
            error_log('File path from WP: ' . ($file_path ?: 'Not found'));
            
            if (!$file_path || !file_exists($file_path)) {
                error_log('Audio file not found at path: ' . $file_path);
                
                // Try fallback using the stored temp file path
                if (isset($session_data['temp_file_path']) && file_exists($session_data['temp_file_path'])) {
                    error_log('Using fallback temp file path: ' . $session_data['temp_file_path']);
                    $file_path = $session_data['temp_file_path'];
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Audio file not found', 'kc-lang')
                    ]);
                }
            }
            
            // Get file content and encode as base64
            $file_content = file_get_contents($file_path);
            if ($file_content === false) {
                error_log('Failed to read audio file content from: ' . $file_path);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to read audio file', 'kc-lang')
                ]);
            }
            
            error_log('Successfully read audio file, size: ' . strlen($file_content) . ' bytes');
            
            $base64_audio = base64_encode($file_content);
            
            // Now process the file for transcription
            // This is similar to the code in the transcribeAudio method
            try {
                // Check if we have a valid API key before proceeding
                if (!defined('DEEPGRAM_API_KEY') || empty(DEEPGRAM_API_KEY)) {
                    error_log('DEEPGRAM_API_KEY is not defined or empty - returning mock data for testing');
                    
                    // Generate test transcript data
                    $processed_transcript = [
                        [
                            'speaker' => 0,
                            'text' => 'Hello, how are you feeling today?',
                            'start' => 0,
                            'end' => 3.5,
                            'confidence' => 0.95
                        ],
                        [
                            'speaker' => 1,
                            'text' => 'I\'ve been experiencing headaches for the past week.',
                            'start' => 4.2,
                            'end' => 7.8,
                            'confidence' => 0.92
                        ],
                        [
                            'speaker' => 0,
                            'text' => 'Can you tell me more about these headaches? When do they occur?',
                            'start' => 8.5,
                            'end' => 12.3,
                            'confidence' => 0.96
                        ],
                        [
                            'speaker' => 1,
                            'text' => 'They usually start in the afternoon and get worse in the evening. Sometimes they wake me up at night.',
                            'start' => 13.1,
                            'end' => 18.7,
                            'confidence' => 0.93
                        ]
                    ];
                    
                    $full_transcript = "Doctor: Hello, how are you feeling today?\n\n" .
                                      "Patient: I've been experiencing headaches for the past week.\n\n" .
                                      "Doctor: Can you tell me more about these headaches? When do they occur?\n\n" .
                                      "Patient: They usually start in the afternoon and get worse in the evening. Sometimes they wake me up at night.";
                    
                    // Log activity
                    if ($encounter_id) {
                        $encounter_details = (new KCPatientEncounter())->get_by(['id' => $encounter_id], '=', true);
                        
                        if (!empty($encounter_details)) {
                            kcLogActivity(
                                'ai_mobile_transcription_mock_data',
                                sprintf(esc_html__('Mock transcript generated for encounter #%d (API key missing)', 'kc-lang'), 
                                    $encounter_id
                                ),
                                [
                                    'patient_id' => $encounter_details->patient_id,
                                    'resource_id' => $encounter_id,
                                    'resource_type' => 'encounter',
                                    'clinic_id' => $encounter_details->clinic_id
                                ]
                            );
                        }
                    }
                    
                    // Clean up any temporary files
                    if ($document_id) {
                        wp_delete_attachment($document_id, true);
                        error_log('Deleted temporary media attachment: ' . $document_id);
                    }
                    
                    if (isset($session_data['temp_file_path']) && file_exists($session_data['temp_file_path'])) {
                        unlink($session_data['temp_file_path']);
                        error_log('Deleted temporary file: ' . $session_data['temp_file_path']);
                    }
                    
                    // Return test data
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Test transcript generated (API key missing)', 'kc-lang'),
                        'data' => [
                            'diarized_transcript' => $processed_transcript,
                            'full_transcript' => $full_transcript,
                            'mock_data' => true
                        ]
                    ]);
                    return; // End execution here
                }
                
                // Create a temporary file with a specific extension to help with MIME type detection
                $temp_file = wp_tempnam('deepgram_audio_') . '.wav';
                error_log('Created temp file: ' . $temp_file);
                
                if (file_put_contents($temp_file, $file_content) === false) {
                    error_log('Failed to write to temp file: ' . $temp_file);
                    throw new Exception("Failed to prepare audio file for processing");
                }
                
                // Detect and log MIME type from the audio data
                $finfo = new \finfo(FILEINFO_MIME_TYPE);
                $mime_type = $finfo->file($temp_file);
                error_log('Processing mobile recorded audio from: ' . $file_path);
                error_log('Detected MIME type: ' . $mime_type);
                
                // Convert mime type to content type header
                $content_type = 'audio/wav'; // Default
                if (strpos($mime_type, 'audio/') === 0) {
                    $content_type = $mime_type;
                    error_log('Using detected content type: ' . $content_type);
                } else {
                    error_log('Using default content type: ' . $content_type);
                }
                
                // Build Deepgram API URL with query parameters
                $deepgram_url = $this->deepgram_api_url . '?' . implode('&', [
                    'model=nova-3', // Use Nova 3 exclusively for all files
                    'smart_format=true',
                    'diarize=true',
                    'punctuate=true',
                    'utterances=true',
                    'language=en-US'
                ]);
                
                error_log('Sending to Deepgram URL: ' . $deepgram_url);
                
                // Read the file content directly to ensure it's available
                $temp_file_content = file_get_contents($temp_file);
                if ($temp_file_content === false || empty($temp_file_content)) {
                    error_log('Failed to read temp file for API request: ' . $temp_file);
                    throw new Exception("Failed to prepare audio data for transcription");
                }
                
                error_log('Temp file size: ' . strlen($temp_file_content) . ' bytes');
                
                // Set up verbose curl output logging
                $verbose_log = fopen('php://temp', 'w+');
                
                // Set cURL options with extensive error handling
                $curl = curl_init();
                curl_setopt($curl, CURLOPT_URL, $deepgram_url);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_POST, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, $temp_file_content);
                curl_setopt($curl, CURLOPT_HTTPHEADER, [
                    'Authorization: Token ' . DEEPGRAM_API_KEY,
                    'Content-Type: ' . $content_type
                ]);
                curl_setopt($curl, CURLOPT_TIMEOUT, 600); // 10 minute timeout for large files
                curl_setopt($curl, CURLOPT_VERBOSE, true);
                curl_setopt($curl, CURLOPT_STDERR, $verbose_log);
                
                // Execute the request
                $response = curl_exec($curl);
                $err = curl_error($curl);
                $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                
                // Log response details
                error_log('Deepgram response HTTP code: ' . $http_code);
                error_log('Deepgram response size: ' . (is_string($response) ? strlen($response) : 'N/A'));
                
                // Get verbose log
                rewind($verbose_log);
                $verbose_log_content = stream_get_contents($verbose_log);
                fclose($verbose_log);
                error_log('Curl verbose log: ' . $verbose_log_content);
                
                // Close curl and clean up temp file
                curl_close($curl);
                
                if (file_exists($temp_file)) {
                    unlink($temp_file);
                    error_log('Deleted temp file: ' . $temp_file);
                }
                
                // Handle curl errors
                if ($err) {
                    error_log('Deepgram cURL Error: ' . $err);
                    throw new Exception("Error communicating with transcription service: " . $err);
                }
                
                // Handle API errors with more detailed error information
                if ($http_code !== 200) {
                    error_log('Deepgram API Error: HTTP ' . $http_code);
                    
                    // Try to parse the error response for more details
                    $error_message = "Transcription service error (HTTP $http_code)";
                    
                    if (!empty($response)) {
                        error_log('Error response: ' . $response);
                        $error_data = json_decode($response, true);
                        if (json_last_error() === JSON_ERROR_NONE && isset($error_data['error'])) {
                            $api_error = is_string($error_data['error']) ? 
                                $error_data['error'] : 
                                (isset($error_data['error']['message']) ? $error_data['error']['message'] : json_encode($error_data['error']));
                            $error_message = "Transcription error: " . $api_error;
                        }
                    }
                    
                    throw new Exception($error_message);
                }
                
                if (empty($response)) {
                    error_log('Empty response from Deepgram API');
                    throw new Exception("Received empty response from transcription service");
                }
                
                $transcription_result = json_decode($response, true);
                
                // Handle JSON decode errors
                if (json_last_error() !== JSON_ERROR_NONE) {
                    error_log('Error decoding Deepgram response: ' . json_last_error_msg());
                    throw new Exception("Error decoding API response: " . json_last_error_msg());
                }
                
                // Process the diarization results to format by speaker
                $processed_transcript = $this->processDiarizedTranscript($transcription_result);
                
                // Verify we got a valid transcript
                if (empty($processed_transcript)) {
                    error_log('Mobile audio transcription: Empty transcript after processing');
                    throw new Exception('Failed to generate transcript from audio file. Please try again or upload a different file.');
                }
                
                $full_transcript = $this->getFullTranscript($processed_transcript);
                
                if (empty($full_transcript)) {
                    error_log('Mobile audio transcription: Empty full transcript');
                    throw new Exception('Failed to generate readable transcript from audio file.');
                }
                
                // Log AI transcription activity
                if ($encounter_id) {
                    // Get encounter details for logging
                    $encounter_details = (new KCPatientEncounter())->get_by(['id' => $encounter_id], '=', true);
                    
                    if (!empty($encounter_details)) {
                        kcLogActivity(
                            'ai_mobile_transcription_completed',
                            sprintf(esc_html__('Mobile recorded audio transcribed via AI Scribe for encounter #%d', 'kc-lang'), 
                                $encounter_id
                            ),
                            [
                                'patient_id' => $encounter_details->patient_id,
                                'resource_id' => $encounter_id,
                                'resource_type' => 'encounter',
                                'clinic_id' => $encounter_details->clinic_id
                            ]
                        );
                    }
                }
                
                // Clean up - delete the media attachment and the temporary file
                // Since we now have the transcript, we don't need to keep the audio file
                if ($document_id) {
                    wp_delete_attachment($document_id, true);
                    error_log('Deleted temporary media attachment: ' . $document_id);
                }
                
                // Delete the original file if it still exists and the path was stored in session
                if (isset($session_data['temp_file_path']) && file_exists($session_data['temp_file_path'])) {
                    unlink($session_data['temp_file_path']);
                    error_log('Deleted temporary file: ' . $session_data['temp_file_path']);
                }
                
                // Also delete the file we pulled directly if it exists
                if (file_exists($file_path)) {
                    unlink($file_path);
                    error_log('Deleted original file: ' . $file_path);
                }

                error_log('Mobile audio successfully transcribed with ' . count($processed_transcript) . ' segments and ' . 
                        strlen($full_transcript) . ' characters');
                
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Mobile audio transcribed successfully', 'kc-lang'),
                    'data' => [
                        'diarized_transcript' => $processed_transcript,
                        'full_transcript' => $full_transcript
                    ]
                ]);
                
            } catch (Exception $e) {
                error_log('Mobile audio transcription error: ' . $e->getMessage());
                wp_send_json([
                    'status' => false,
                    'message' => $e->getMessage()
                ]);
            }
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}