<template>
    <div class="w-full px-4">
        <div class="grid grid-cols-1 gap-6">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                        <h4 class="font-medium text-lg text-gray-800">{{ $t('New TDL Lab Test Request') }}</h4>
                        <router-link to="/tdl-requests"
                            class="inline-flex items-center px-3 py-1 text-sm border border-blue-600 rounded-md text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fa fa-arrow-left mr-1"></i>
                            {{ $t('Back to Requests') }}
                        </router-link>
                    </div>
                    <div class="p-6">
                        <div v-if="loading" class="text-center py-8">
                            <div
                                class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent">
                            </div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <form v-else @submit.prevent="submitRequest">
                            <!-- Patient Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div v-if="this.getUserRole() == 'administrator'">
                                    <div class="text-left mb-1.5">
                                        <label class="block text-sm font-medium text-gray-700"> Clinic <span
                                                class="text-red-500">*</span>
                                        </label>
                                    </div>
                                    <div class="relative">
                                        <multiselect v-model="request.clinic_id" :options="clinics" track-by="id"
                                            label="label" :searchable="true" placeholder="Select a clinic"
                                            :class="{ 'multiselect-invalid': validationErrors.clinic_id }"
                                            :loading="clinics.length === 0" :internal-search="true"
                                            :show-labels="false">
                                        </multiselect>
                                    </div>
                                </div>
                                <div>
                                    <div class="mb-4">
                                        <label for="patient_id" class="block text-sm font-medium text-gray-700 mb-1">{{
                                            $t('Patient') }} <span class="text-red-500">*</span></label>
                                        <select
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                            id="patient_id" v-model="request.patient_id" required
                                            :disabled="submitting">
                                            <option value="">{{ $t('Select Patient') }}</option>
                                            <option v-for="patient in patients" :key="patient.id" :value="patient.id">
                                                {{ patient.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div>
                                    <div class="mb-4">
                                        <label for="doctor_id" class="block text-sm font-medium text-gray-700 mb-1">{{
                                            $t('Doctor') }} <span class="text-red-500">*</span></label>
                                        <select
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                            id="doctor_id" v-model="request.doctor_id" required :disabled="submitting">
                                            <option value="">{{ $t('Select Doctor') }}</option>
                                            <option v-for="doctor in doctors" :key="doctor.id" :value="doctor.id">
                                                {{ doctor.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Collection Date & Time -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <div class="mb-4">
                                        <label for="collection_date"
                                            class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Collection Date & Time') }} <span class="text-red-500">*</span></label>
                                        <input type="datetime-local"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                            id="collection_date" v-model="request.collection_date" required
                                            :disabled="submitting" />
                                    </div>
                                </div>
                                <div>
                                    <div class="mb-4">
                                        <label for="clinical_notes"
                                            class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Clinical Notes')
                                            }}</label>
                                        <textarea
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                            id="clinical_notes" v-model="request.clinical_notes" rows="2"
                                            :disabled="submitting"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Selection -->
                            <div class="mt-6">
                                <div>
                                    <h5 class="text-base font-medium text-gray-700 mb-3">{{ $t('Select Tests') }} <span
                                            class="text-red-500">*</span></h5>

                                    <div class="mb-4">
                                        <div class="flex">
                                            <input type="text"
                                                class="w-full rounded-l-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                                v-model="searchQuery"
                                                :placeholder="$t('Search tests by name or code...')"
                                                @input="searchTests" :disabled="submitting" />
                                            <button
                                                class="px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                                type="button" @click="searchTests"
                                                :disabled="submitting || searchingTests">
                                                <i
                                                    :class="{ 'fa mr-1': true, 'fa-search': !searchingTests, 'fa-sync fa-spin': searchingTests }"></i>
                                                {{ searchingTests ? '' : '' }}
                                            </button>
                                        </div>
                                    </div>

                                    <div v-if="searchingTests" class="text-center py-4">
                                        <div
                                            class="inline-block animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent">
                                        </div>
                                        <span class="ml-2 text-sm text-gray-600">{{ $t('Searching tests...') }}</span>
                                    </div>

                                    <div v-else-if="searchResults.length > 0" class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        {{ $t('Select') }}</th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        {{ $t('Test Code') }}</th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        {{ $t('Test Name') }}</th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        {{ $t('Category') }}</th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        {{ $t('Sample Type') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                <tr v-for="test in searchResults" :key="test.test_code"
                                                    class="hover:bg-gray-50">
                                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                                        <div class="flex items-center justify-center">
                                                            <input type="checkbox"
                                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                                :id="'test-' + test.test_code" :value="test"
                                                                v-model="selectedTests" :disabled="submitting" />
                                                            <label class="sr-only"
                                                                :for="'test-' + test.test_code"></label>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{
                                                        test.test_code }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{
                                                        test.test_name }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{
                                                        test.test_category }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{
                                                        test.sample_type }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div v-else-if="searchQuery && !searchingTests"
                                        class="p-4 border rounded-md border-blue-200 bg-blue-50 text-blue-700 text-sm">
                                        {{ $t('No tests found matching your search criteria.') }}
                                    </div>

                                    <div v-if="selectedTests.length > 0" class="mt-6">
                                        <h6 class="text-sm font-medium text-gray-700 mb-2">{{ $t('Selected Tests') }}
                                            ({{ selectedTests.length }})</h6>
                                        <div class="overflow-x-auto bg-gray-50 border rounded-md">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <thead class="bg-gray-100">
                                                    <tr>
                                                        <th scope="col"
                                                            class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            {{ $t('Test Code') }}</th>
                                                        <th scope="col"
                                                            class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            {{ $t('Test Name') }}</th>
                                                        <th scope="col"
                                                            class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            {{ $t('Sample Type') }}</th>
                                                        <th scope="col"
                                                            class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                                                            {{ $t('Action') }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    <tr v-for="(test, index) in selectedTests" :key="index">
                                                        <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{{
                                                            test.test_code }}</td>
                                                        <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{{
                                                            test.test_name }}</td>
                                                        <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{{
                                                            test.sample_type || 'N/A' }}</td>
                                                        <td class="px-6 py-3 whitespace-nowrap text-center">
                                                            <button type="button"
                                                                class="inline-flex items-center p-1.5 border border-transparent rounded-full text-red-600 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                                                @click="removeTest(index)" :disabled="submitting">
                                                                <i class="fa fa-times"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <div v-if="selectedTests.length === 0"
                                        class="p-4 mt-4 border rounded-md border-yellow-200 bg-yellow-50 text-yellow-700 text-sm">
                                        {{ $t('You must select at least one test.') }}
                                    </div>
                                </div>
                            </div>

                            <div class="mt-8 pt-6 border-t border-gray-200">
                                <div class="flex">
                                    <button type="submit"
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                        :disabled="submitting || selectedTests.length === 0 || formInvalid">
                                        <i v-if="submitting" class="fa fa-sync fa-spin mr-2"></i>
                                        <i v-else class="fa fa-paper-plane mr-2"></i>
                                        {{ submitting ? $t('Submitting...') : $t('Submit Test Request') }}
                                    </button>
                                    <button type="button"
                                        class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                        @click="resetForm" :disabled="submitting">
                                        <i class="fa fa-undo mr-2"></i>
                                        {{ $t('Reset Form') }}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";
import Multiselect from 'vue-multiselect';

export default {
    name: 'TDLNewRequest',
    components: {
        Multiselect
    },
    data() {
        return {
            loading: true,
            submitting: false,
            searchingTests: false,
            searchDebounceTimeout: null,
            clinics: [],
            patients: [],
            doctors: [],
            searchQuery: '',
            searchResults: [],
            selectedTests: [],
            patientRoleName: 'patient', // Added missing property
            clinicMultiselectLoader: false,
            patientMultiselectLoader: false,
            request: {
                clinic_id: null, // Changed from 0 to null
                patient_id: '',
                doctor_id: '',
                collection_date: this.formatDatetime(new Date()),
                clinical_notes: '',
                tests: []
            },
            validationErrors: {
                clinic_id: false, // Added missing validation field
                patient: false,
                doctor: false,
                collection_date: false,
                tests: false
            }
        };
    },
    computed: {
        formInvalid() {
            // Add clinic_id to validation for administrator role
            const requiresClinic = this.getUserRole() === 'administrator';

            return (requiresClinic && !this.request.clinic_id) ||
                !this.request.patient_id ||
                !this.request.doctor_id ||
                !this.request.collection_date ||
                this.selectedTests.length === 0;
        },
        currentLocale() {
            return this.$i18n.locale || 'en';
        },
        userData() {
            // Get user data from store
            if (
                this.$store.state.userDataModule !== undefined &&
                this.$store.state.userDataModule.user !== undefined
            ) {
                return this.$store.state.userDataModule.user;
            } else {
                return {};
            }
        },
        userClinicId() {
            return this.userData?.user_clinic_id || this.userData?.default_clinic_id;
        },
    },
    watch: {
        'request.clinic_id': function (newClinic) {
            if (this.getUserRole() === 'administrator' && newClinic) {
                // Clear existing patients and doctors when clinic changes
                this.patients = [];
                this.doctors = [];

                // Load patients and doctors for the selected clinic
                this.fetchClinicPatients(newClinic.id);
                this.fetchClinicDoctors(newClinic.id);
            }
        }
    },
    created() {
        this.loadInitialData();
    },
    methods: {
        // Get user role method
        getUserRole() {
            if (this.userData && this.userData.roles) {
                if (Array.isArray(this.userData.roles)) {
                    // If roles is an array, return the first role
                    return this.userData.roles[0];
                } else if (typeof this.userData.roles === 'string') {
                    // If roles is a string, return it directly
                    return this.userData.roles;
                }
            }
            // Default role if none found
            return 'user';
        },

        // Update the method that fetches doctors to be clinic-specific
        async fetchClinicDoctors(clinicId) {
            try {
                // Show loading indicator if needed
                // this.doctorMultiselectLoader = true;

                const response = await get("get_static_data", {
                    data_type: "get_users_by_clinic",
                    clinic_id: clinicId,
                    module_type: "appointment"
                });

                if (response.data && response.data.status) {
                    this.doctors = response.data.data || [];
                } else {
                    this.doctors = [];
                    console.error('Failed to load doctors');
                }
            } catch (error) {
                console.error('Error fetching clinic doctors:', error);
                this.doctors = [];
            } finally {
                // Hide loading indicator if needed
                // this.doctorMultiselectLoader = false;
            }
        },

        // Change the loadInitialData method to not load patients and doctors initially for admin
        async loadInitialData() {
            try {
                // Step 1: Initialize based on user role
                const userRole = this.getUserRole();

                // Load clinics based on user role
                if (userRole === 'administrator') {
                    // Admin sees all clinics but doesn't load patients/doctors until clinic is selected
                    await this.getClinics();

                    // If only one clinic, select it and load its data
                    if (this.clinics.length === 1) {
                        this.request.clinic_id = this.clinics[0];
                        await Promise.all([
                            this.fetchClinicPatients(this.clinics[0].id),
                            this.fetchClinicDoctors(this.clinics[0].id)
                        ]);
                    }
                } else {
                    // Other roles use the logged-in user's clinic
                    if (this.userClinicId) {
                        // For non-admin users, set clinic_id directly
                        this.request.clinic_id = this.userClinicId;

                        // Load patients and doctors for the user's clinic
                        await Promise.all([
                            this.fetchClinicPatients(this.userClinicId),
                            this.getDoctors(this.userClinicId)
                        ]);
                    }
                }

                this.loading = false;
            } catch (error) {
                console.error('Error loading initial data:', error);
                this.loading = false;
                this.safeDisplayError(this.$t('Failed to load initial data. Please refresh the page.'));
            }
        },

        async fetchClinicPatients(clinicId) {
            if (!clinicId) return;

            // Show loading indicator
            this.patientMultiselectLoader = true;

            try {
                this.patientOptions = []; // Clear existing options

                // Fetch patients for the selected clinic using same method as AppointmentForm.vue
                const response = await get("get_static_data", {
                    data_type: "users",
                    user_type: this.patientRoleName,
                    request_clinic_id: clinicId,
                });

                if (response.data && response.data.status) {
                    this.patients = response.data.data;
                    console.log('Loaded patients:', this.patientOptions);
                } else {
                    console.error('Failed to load patients, status:', response.data?.status);
                }
            } catch (error) {
                console.error('Error fetching clinic patients:', error);
            } finally {
                // Hide loading indicator
                this.patientMultiselectLoader = false;
            }
        },

        async getClinics() {
            this.clinicMultiselectLoader = true;
            try {
                const response = await get("get_static_data", {
                    data_type: "clinic_list",
                });

                if (response.data.status) {
                    this.clinics = response.data.data;

                    // If only one clinic is available, select it automatically
                    if (this.clinics.length === 1) {
                        this.request.clinic_id = this.clinics[0];
                    }
                }
            } catch (error) {
                console.error("Error fetching clinics:", error);
                // Fall back to safe display method
                if (typeof displayErrorMessage === 'function') {
                    displayErrorMessage('Failed to load clinics');
                }
            } finally {
                this.clinicMultiselectLoader = false;
            }
        },

        async getDoctors() {
            try {
                const response = await get("doctor_list");

                if (response.data.status === true) {
                    this.doctors = response.data.data || [];
                }
            } catch (error) {
                console.error('Error fetching doctors:', error);
            }
        },

        formatDatetime(date) {
            if (!date) return '';

            try {
                const now = date instanceof Date ? date : new Date(date);
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');

                return `${year}-${month}-${day}T${hours}:${minutes}`;
            } catch (error) {
                console.error('Error formatting date:', error);
                return '';
            }
        },

        searchTests() {
            // Debounce search to avoid too many requests
            if (this.searchDebounceTimeout) {
                clearTimeout(this.searchDebounceTimeout);
            }

            this.searchDebounceTimeout = setTimeout(() => {
                if (!this.searchQuery.trim()) {
                    this.searchResults = [];
                    return;
                }

                this.searchingTests = true;

                get('tdl_get_tests_catalog', {
                    search: this.searchQuery.trim(),
                    per_page: 50
                }).then(response => {
                    this.searchingTests = false;

                    if (response.data.status === true) {
                        this.searchResults = response.data.data.tests || [];
                    } else {
                        this.searchResults = [];
                        this.safeDisplayError(response.data.message || this.$t('Failed to search tests.'));
                    }
                }).catch(error => {
                    this.searchingTests = false;
                    console.error('Error searching tests:', error);
                    this.safeDisplayError(this.$t('Failed to search tests.'));
                });
            }, 300); // 300ms debounce
        },

        // Safe display error method that works whether displayErrorMessage is available or not
        safeDisplayError(message) {
            if (typeof displayErrorMessage === 'function') {
                displayErrorMessage(message);
            } else {
                console.error(message);
                // Optional fallback
                // alert(message);
            }
        },

        // Safe display message method
        safeDisplayMessage(message) {
            if (typeof displayMessage === 'function') {
                displayMessage(message);
            } else {
                console.log(message);
                // Optional fallback
                // alert(message);
            }
        },

        removeTest(index) {
            this.selectedTests.splice(index, 1);
        },

        validateForm() {
            let isValid = true;

            // Reset validation errors
            Object.keys(this.validationErrors).forEach(key => {
                this.validationErrors[key] = false;
            });

            // Validate clinic for admin users
            if (this.getUserRole() === 'administrator' && !this.request.clinic_id) {
                this.validationErrors.clinic_id = true;
                isValid = false;
            }

            // Validate patient
            if (!this.request.patient_id) {
                this.validationErrors.patient = true;
                isValid = false;
            }

            // Validate doctor
            if (!this.request.doctor_id) {
                this.validationErrors.doctor = true;
                isValid = false;
            }

            // Validate collection date
            if (!this.request.collection_date) {
                this.validationErrors.collection_date = true;
                isValid = false;
            }

            // Validate tests
            if (this.selectedTests.length === 0) {
                this.validationErrors.tests = true;
                isValid = false;
            }

            return isValid;
        },

        resetForm() {
            // Show confirmation dialog
            if (this.selectedTests.length > 0 || this.request.clinical_notes ||
                this.request.patient_id || this.request.doctor_id) {

                if (confirm(this.$t('Are you sure you want to reset the form? All entered data will be lost.'))) {
                    // Keep clinic_id if user is not administrator
                    const clinicId = this.getUserRole() !== 'administrator' ? this.request.clinic_id : null;

                    this.request = {
                        clinic_id: clinicId,
                        patient_id: '',
                        doctor_id: '',
                        collection_date: this.formatDatetime(new Date()),
                        clinical_notes: '',
                        tests: []
                    };
                    this.selectedTests = [];
                    this.searchQuery = '';
                    this.searchResults = [];

                    // Reset validation errors
                    Object.keys(this.validationErrors).forEach(key => {
                        this.validationErrors[key] = false;
                    });
                }
            }
        },

        submitRequest() {
            if (!this.validateForm()) {
                this.safeDisplayError(this.$t('Please fill in all required fields and select at least one test.'));
                return;
            }

            this.submitting = true;

            // Get the clinic ID from the selected clinic object or directly if not admin
            const clinicId = this.getUserRole() === 'administrator'
                ? (this.request.clinic_id ? this.request.clinic_id.id : null)
                : this.request.clinic_id;

            // Prepare request data
            const requestData = {
                ...this.request,
                clinic_id: clinicId, // Ensure clinic_id is properly extracted
                tests: this.selectedTests.map(test => ({
                    test_code: test.test_code,
                    test_name: test.test_name,
                    sample_type: test.sample_type || ''
                }))
            };

            post('tdl_create_test_request', requestData)
                .then(response => {
                    this.submitting = false;

                    if (response.data.status === true) {
                        this.safeDisplayMessage(this.$t('Test request created successfully. Order Number: {order_number}', {
                            order_number: response.data.data.order_number
                        }));

                        // Navigate to the requests list
                        this.$router.push('/tdl-requests');
                    } else {
                        this.safeDisplayError(response.data.message || this.$t('Failed to create test request.'));
                    }
                })
                .catch(error => {
                    this.submitting = false;
                    console.error('Error creating test request:', error);

                    let errorMessage = this.$t('Failed to create test request.');
                    if (error.response && error.response.data && error.response.data.message) {
                        errorMessage = error.response.data.message;
                    }

                    this.safeDisplayError(errorMessage);
                });
        }
    }
};
</script>

<style scoped>
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* Add visual indication for validation errors */
select.border-red-500,
input.border-red-500,
textarea.border-red-500 {
    border-color: #f56565;
}
</style>