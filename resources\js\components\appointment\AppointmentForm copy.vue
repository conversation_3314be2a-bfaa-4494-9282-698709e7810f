<template>
  <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
    <form
      @submit.prevent="handleFormSubmit"
      :id="
        appointmentFormObj.id !== undefined
          ? 'appointmentDataForm' + appointmentFormObj.id
          : 'appointmentDataForm'
      "
      :appointment-form-id="
        appointmentFormObj.id !== undefined ? appointmentFormObj.id : 0
      "
    >
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Clinic Selection -->
        <div v-if="canShowClinicSelection" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.appointments.select_clinic }}
            <span class="text-red-500">*</span>
          </label>
          <multi-select
            v-model="appointmentFormObj.clinic_id"
            @select="clinicChange"
            @remove="clinicChange"
            :disabled="disabledClinicField"
            :loading="clinicMultiselectLoader"
            :options="clinics"
            :tag-placeholder="formTranslation.appointments.select_clinic_plh"
            :placeholder="formTranslation.appointments.search_plh"
            label="label"
            track-by="id"
            class="w-full"
          />
          <p
            v-if="submitted && !$v.appointmentFormObj.clinic_id.required"
            class="text-sm text-red-500 mt-1"
          >
            {{ formTranslation.appointments.clinic_is_required }}
          </p>
        </div>

        <!-- Doctor Selection -->
        <div v-if="getUserRole() !== 'doctor'" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.common.doctor }}
            <span class="text-red-500">*</span>
          </label>
          <multi-select
            v-model="appointmentFormObj.doctor_id"
            @select="handleDoctorChange"
            @remove="handleDoctorUnselect"
            :disabled="disabledDoctorField"
            :loading="doctorMultiselectLoader"
            :options="doctors"
            :tag-placeholder="formTranslation.appointments.doctor_plh"
            :placeholder="formTranslation.appointments.search_plh"
            label="label"
            track-by="id"
            class="w-full"
          />
          <p
            v-if="submitted && !$v.appointmentFormObj.doctor_id.required"
            class="text-sm text-red-500 mt-1"
          >
            {{ formTranslation.appointments.doc_required }}
          </p>
        </div>

        <!-- Service Selection -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.common.service }}
            <span class="text-red-500">*</span>
            <span v-if="canAddService" class="float-right">
              <router-link
                :to="{ name: 'service' }"
                class="text-blue-500 text-sm"
              >
                <i class="fa fa-plus"></i>
                {{ formTranslation.common.service_add }}
              </router-link>
            </span>
          </label>
          <multi-select
            v-model="appointmentFormObj.visit_type"
            @select="appointmentTypeChangeSelect"
            @remove="appointmentTypeChangeUnselect"
            :disabled="disabledServiceField"
            :loading="serviceMultiselectLoader"
            :options="appointmentTypes"
            :multiple="appointmentTypeMultiselect"
            :tag-placeholder="formTranslation.appointments.tag_visit_type_plh"
            :placeholder="formTranslation.appointments.search_plh"
            label="name"
            track-by="id"
            class="w-full"
          />
          <p
            v-if="submitted && !$v.appointmentFormObj.visit_type.required"
            class="text-sm text-red-500 mt-1"
          >
            {{ formTranslation.patient_bill.service_required }}
          </p>
        </div>

        <!-- Missing service details widget -->
        <div class="col-span-full">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ formTranslation.widgets.service_detail }}
          </label>
          <div class="border border-gray-200 rounded-md p-4">
            <div v-if="appointmentData.visit_type?.length > 0">
              <div
                v-for="(service, index) in appointmentData.visit_type"
                :key="index"
                class="text-sm"
              >
                <span class="font-medium">{{ service.name }}</span>
                <span>
                  -
                  {{
                    appointmentFormObj.id !== undefined
                      ? appointmentFormObj.clinic_prefix
                      : ""
                  }}
                  {{ service.charges }}
                  {{
                    appointmentFormObj.id !== undefined
                      ? appointmentFormObj.clinic_postfix
                      : ""
                  }}
                </span>
              </div>
            </div>
            <p v-else class="text-sm text-gray-500 text-center">
              {{ formTranslation.widgets.no_service_detail_found }}
            </p>
          </div>
        </div>

        <!-- Missing tax section -->
        <div v-if="userData.addOns.kiviPro" class="col-span-full">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ formTranslation.common.tax }}
          </label>
          <div class="border border-gray-200 rounded-md p-4">
            <div v-if="taxes.length > 0">
              <div
                v-for="(tax, index) in taxes"
                :key="index"
                class="flex justify-between text-sm"
              >
                <span class="font-medium">{{ tax.name }}</span>
                <span
                  >{{ userData.clinic_currency_detail.prefix }}{{ tax.charges
                  }}{{ userData.clinic_currency_detail.postfix }}</span
                >
              </div>
            </div>
            <p v-else class="text-center text-sm text-gray-500">
              {{ formTranslation.common.no_tax_found }}
            </p>
          </div>
        </div>

        <!-- Date Selection -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.appointments.appointment_date }}
            <span class="text-red-500">*</span>
          </label>
          <vc-date-picker
            v-model="appointmentFormObj.appointment_start_date"
            :disabled-dates="{ weekdays: DoctorWorkdays }"
            :min-date="minDate"
            :max-date="maxDate"
            @input="handleDateChange"
            :masks="masks"
          >
            <template v-slot="{ inputValue, inputEvents }">
              <input
                class="w-full rounded-md border border-gray-300 px-3 py-2 bg-white"
                :value="inputValue"
                v-on="inputEvents"
                readonly
                :placeholder="formTranslation.appointments.appointment_date_plh"
              />
            </template>
          </vc-date-picker>
          <p
            v-if="
              submitted &&
              !$v.appointmentFormObj.appointment_start_date.required
            "
            class="text-sm text-red-500 mt-1"
          >
            {{ formTranslation.appointments.appointment_date_required }}
          </p>
        </div>

        <!-- Patient Selection -->
        <div v-if="showPatientSelection" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.common.patient }}
            <span class="text-red-500">*</span>
            <span v-if="canAddPatient" class="float-right">
              <router-link
                :to="{ name: 'patient.create' }"
                class="text-blue-500 text-sm"
              >
                <i class="fa fa-plus"></i>
                {{ formTranslation.patient.add_patient }}
              </router-link>
            </span>
          </label>
          <multi-select
            v-model="appointmentFormObj.patient_id"
            :disabled="disabledPatientField"
            :loading="patientMultiselectLoader"
            :options="patients"
            :tag-placeholder="formTranslation.appointments.tag_patient_type_plh"
            :placeholder="formTranslation.appointments.search_plh"
            label="label"
            track-by="id"
            class="w-full"
          />
          <p
            v-if="submitted && !$v.appointmentFormObj.patient_id.required"
            class="text-sm text-red-500 mt-1"
          >
            {{ formTranslation.appointments.patient_requires }}
          </p>
        </div>

        <!-- Status Selection -->
        <div v-if="getUserRole() !== 'patient'" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.common.status }}
            <span class="text-red-500">*</span>
          </label>
          <select
            v-model="appointmentFormObj.status"
            class="w-full rounded-md border border-gray-300 px-3 py-2 bg-white"
          >
            <option value="">
              {{ formTranslation.appointments.select_status }}
            </option>
            <option value="1">{{ formTranslation.appointments.booked }}</option>
            <option value="2">
              {{ formTranslation.appointments.pending }}
            </option>
            <option value="3">
              {{ formTranslation.appointments.check_out }}
            </option>
            <option value="4">
              {{ formTranslation.appointments.check_in }}
            </option>
            <option value="0">
              {{ formTranslation.appointments.cancelled }}
            </option>
          </select>
          <p
            v-if="submitted && !$v.appointmentFormObj.status.required"
            class="text-sm text-red-500 mt-1"
          >
            {{ formTranslation.appointments.status_required }}
          </p>
        </div>

        <!-- Time Slots -->
        <div class="col-span-full">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ formTranslation.appointments.available_slot }}
            <span class="text-red-500">*</span>
          </label>
          <div
            class="border border-gray-200 rounded-md p-4 max-h-64 overflow-y-auto"
          >
            <div v-if="timeSlots.length > 0" class="space-y-4">
              <div
                v-for="(sessionSlots, index) in timeSlots"
                :key="index"
                class="space-y-2"
              >
                <p class="text-sm font-medium text-gray-700">
                  {{ formTranslation.appointments.session }} {{ index + 1 }}
                </p>
                <div class="flex flex-wrap gap-2">
                  <button
                    v-for="slot in sessionSlots"
                    :key="slot.time"
                    type="button"
                    :disabled="!slot.available"
                    @click="handleTimeChange(slot.time)"
                    :class="[
                      'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                      !slot.available
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : appointmentFormObj.appointment_start_time ===
                          slot.time
                        ? 'bg-blue-500 text-white'
                        : 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700',
                    ]"
                  >
                    <del v-if="!slot.available">{{ slot.time }}</del>
                    <span v-else>{{ slot.time }}</span>
                  </button>
                </div>
              </div>
            </div>
            <p v-else class="text-center text-sm text-gray-500">
              {{ formTranslation.appointments.no_time_slots_found }}
            </p>
          </div>
          <p
            v-if="
              submitted &&
              !$v.appointmentFormObj.appointment_start_time.required
            "
            class="text-sm text-red-500 mt-1"
          >
            {{ formTranslation.appointments.time_slot_required }}
          </p>
        </div>

        <!-- Description -->
        <div
          v-if="enableDisableAppointmentDescriptionStatus === 'on'"
          class="col-span-full"
        >
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ formTranslation.appointments.description }}
          </label>
          <textarea
            v-model="appointmentFormObj.description"
            rows="4"
            class="w-full rounded-md border border-gray-300 px-3 py-2 bg-white"
            :placeholder="formTranslation.appointments.appointment_desc_plh"
          ></textarea>
        </div>

        <!-- Missing file upload section -->
        <div v-if="fileUploadEnable === 'on'" class="col-span-full">
          <div v-if="appointmentFormObj.id === undefined">
            <h5 class="text-sm font-medium text-gray-700 mb-3">
              {{ formTranslation.patient.add_medical_report }}
            </h5>
            <div class="flex items-center space-x-2">
              <button
                type="button"
                class="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                @click.prevent="multiUploadProfile"
              >
                {{ formTranslation.common.choose_file }}
              </button>
              <span class="text-sm text-gray-600">
                {{
                  upload_appointment_report.length > 0
                    ? upload_appointment_report.length + " File selected"
                    : formTranslation.common.no_file_chosen
                }}
              </span>
            </div>
          </div>
          <!-- File list display -->
          <div v-if="upload_appointment_report.length > 0" class="mt-4">
            <div
              v-for="(report, index) in upload_appointment_report"
              :key="index"
              class="flex items-center justify-between p-2"
            >
              <a
                :href="report.url"
                target="_blank"
                class="text-blue-500 hover:underline"
              >
                <i class="fas fa-external-link-alt mr-2"></i>{{ report.name }}
              </a>
              <button
                @click="
                  upload_appointment_report.splice(index, 1);
                  appointmentData.file.splice(index, 1);
                "
                class="text-red-500 hover:text-red-700"
              >
                <i class="fa fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Submit Buttons -->
      <div
        class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200"
      >
        <button
          type="button"
          @click="appointmentCloseForm"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          {{ formTranslation.common.cancel }}
        </button>
        <button
          type="submit"
          :disabled="loading || !disabledButton"
          class="px-4 py-2 bg-black text-white rounded-md hover:bg-black disabled:opacity-50"
        >
          <i v-if="!loading" class="fa fa-save mr-2"></i>
          <i v-else class="fa fa-sync fa-spin mr-2"></i>
          {{
            loading
              ? formTranslation.common.loading
              : formTranslation.appointments.save_btn
          }}
        </button>
      </div>
    </form>

    <!-- Appointment Summary Modal -->
    <ModalPopup
      v-if="appointmentModel"
      modalId="appointment-detail"
      modalSize="lg"
      :openModal="appointmentModel"
      :modalTitle="formTranslation.widgets.summary"
      @closeModal="closeAppointmentModal"
    >
      <div v-if="overlaySpinner" class="page-loader-section">
        <loader-component-2></loader-component-2>
      </div>
      <AppointmentDetail
        ref="appointment_detail"
        :appointment-data="appointmentFormObj"
        :user-data="userData"
        :prefix="prefix"
        :postfix="postfix"
        @bookAppointment="bookAppointmentHandle"
        @cancelAppointment="closeAppointmentModal"
        :lazy="true"
      />
    </ModalPopup>
  </div>
</template>

<script>
import { required } from "vuelidate/lib/validators";
import { validateForm } from "../../config/helper";
import { post, get } from "../../config/request";
import AppointmentDetail from "./AppointmentDetail";
import ModalPopup from "../Modal/Index";
import moment from "moment";

export default {
  components: {
    AppointmentDetail,
    ModalPopup,
  },
  props: {
    appointmentData: {
      type: [Object, Array, Date],
      default() {
        return {};
      },
    },
    patient_profile_id: {
      type: [Number, String],
      default() {
        return "";
      },
    },
  },
  validations: {
    appointmentFormObj: {
      appointment_start_date: { required },
      appointment_start_time: { required },
      visit_type: { required },
      clinic_id: { required },
      doctor_id: { required },
      patient_id: { required },
      status: { required },
    },
  },
  data: () => {
    return {
      appointmentModel: false,
      overlaySpinner: false,
      loading: false,
      prefix: "",
      postfix: "",
      formTitle: "Add appointment",
      buttonText: '<i class="fa fa-save"></i> Save',
      appointmentFormObj: {
        clinic_id: null,
        doctor_id: null,
        visit_type: [],
        appointment_start_date: null,
        appointment_start_time: null,
        patient_id: null,
        status: "1",
        description: "",
        custom_fields: [],
        payment_mode: "paymentOffline",
      },
      submitted: false,
      doctors: [],
      timer: "",
      appointmentTypes: [],
      showCustomField: false,
      componentKey: 0,
      p_uid: "",
      disabledDoctorField: false,
      disabledServiceField: false,
      disabledPatientField: false,
      disabledClinicField: false,
      DoctorWorkdays: [],
      holiday: {},
      restrictAppointment: {
        pre_book: "0",
        post_book: "365",
        only_same_day_book: "on",
      },
      requiredFields: [],
      patientRoleName: "patient",
      minDate: new Date(),
      maxDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365),
      medicalReport: [],
      disabledButton: true,
      patients: [],
      clinicMultiselectLoader: true,
      doctorMultiselectLoader: true,
      patientMultiselectLoader: true,
      serviceMultiselectLoader: false,
      appointmentTypeMultiselect: true,
      upload_appointment_report: [],
      hideFormBtn: true,
      taxes: [],
      masks: {
        input: "DD/MM/YYYY",
      },
    };
  },
  beforeMount() {
    this.getRestrictAppointmentDay();
  },
  mounted() {
    // this.getDoctorsServices();
    if (this.getUserRole() === "doctor") {
      let doctor_id = this.userData;
      this.showCustomField = true;
      this.getDoctorsServices(doctor_id.ID);
      this.clinicChange({ id: this.clinics.id });
    } else if (this.getUserRole() === "patient") {
      this.appointmentFormObj.patient_id = {
        id: this.userData.ID,
        label: this.userData.display_name,
      };
    } else {
      // fatch default selected clinic doctors
      if (this.$store.state.clinic.length > 0) {
        if (this.$store.state.clinic[0].id !== undefined) {
          this.clinicChange({ id: this.$store.state.clinic[0].id });
        }
      }
    }

    // Initialize after ensuring data is available
    this.$nextTick(() => {
      this.init();
    });
  },
  methods: {
    customDisabledDates(date) {
      console.log("date", date);
      if (this.DoctorWorkdays.includes(date.date.getDay() + 1)) {
        return true;
      }
      if (this.holiday.length > 0) {
        let sample = false;
        this.holiday.map((holidays) => {
          const fromDate = new Date(holidays.start_date);
          const toDate = new Date(holidays.end_date);
          if (date.date >= fromDate && date.date <= toDate) {
            sample = true;
          }
        });
        return sample;
      }
    },
    async init() {
      // this.getUniqueId();
      this.appointmentFormObj.payment_mode = "paymentOffline";
      if (this.appointmentFormObj.id !== undefined) {
        // This is an edit/reschedule case
        this.taxes = this.appointmentFormObj.tax || [];
        this.hideFormBtn = this.appointmentFormObj.isEditAble;
        this.formTitle = "Edit appointment";
        this.buttonText =
          '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
        this.showCustomField = true;
        this.disabledDoctorField = true;
        this.disabledServiceField = true;
        this.disabledClinicField = true;
        this.disabledPatientField = true;
        this.clinicMultiselectLoader = false;
        this.doctorMultiselectLoader = false;
        this.patientMultiselectLoader = false;
        this.serviceMultiselectLoader = false;

        // Get doctor workdays and time slots
        if (
          this.appointmentFormObj.clinic_id &&
          this.appointmentFormObj.doctor_id
        ) {
          this.getDoctorActiveDays(
            this.appointmentFormObj.clinic_id.id,
            this.appointmentFormObj.doctor_id.id
          );
          this.dispatchTimeSlot();
        }

        if (
          this.appointmentFormObj?.restrictAppointment?.only_same_day_book ==
          "on"
        ) {
          this.minDate = new Date();
          this.maxDate = new Date();
        } else {
          this.minDate =
            new Date() ||
            new Date(
              Date.now() +
                1000 *
                  60 *
                  60 *
                  24 *
                  parseInt(this.restrictAppointment.pre_book) || 0
            );
          this.maxDate = new Date(
            Date.now() +
              1000 *
                60 *
                60 *
                24 *
                parseInt(this.restrictAppointment.post_book) || 365
          );
        }
      } else {
        this.appointmentFormObj.status = "1";
        if (this.restrictAppointment.only_same_day_book === "on") {
          this.minDate = new Date();
          this.maxDate = new Date();
        } else {
          this.minDate = new Date(
            Date.now() +
              1000 *
                60 *
                60 *
                24 *
                parseInt(this.restrictAppointment.pre_book) || 0
          );
          this.maxDate = new Date(
            Date.now() +
              1000 *
                60 *
                60 *
                24 *
                parseInt(this.restrictAppointment.post_book) || 365
          );
          this.appointmentFormObj.appointment_start_date = new Date(
            Date.now() +
              1000 *
                60 *
                60 *
                24 *
                parseInt(this.restrictAppointment.pre_book) || 0
          );
        }
      }

      if (this.getUserRole() !== "doctor") {
        this.getDoctorDropdown();
      } else {
        this.dispatchTimeSlot();
      }

      setTimeout(() => {
        if (
          this.getUserRole() === "doctor" &&
          this.userData.addOns.kiviPro != true
        ) {
          this.getDoctorActiveDays(
            this.userData.default_clinic,
            this.userData.ID
          );
        }
      }, 3000);

      if (
        typeof this.$store.state.userDataModule !== undefined &&
        typeof this.$store.state.userDataModule.userDropDownData !==
          undefined &&
        this.$store.state.userDataModule.userDropDownData.patients.length > 0
      ) {
        this.patientMultiselectLoader = false;
        this.patients =
          this.$store.state.userDataModule.userDropDownData.patients;
      } else {
        this.$store.dispatch("userDataModule/fetchUserForDropdown", {
          userRoleName: this.patientRoleName,
        });
        setTimeout(() => {
          this.patientMultiselectLoader = false;
          this.patients = Object.values(
            this.$store.state.userDataModule.userDropDownData.patients
          );
        }, 3000);
      }
    },
    getRestrictAppointmentDay: function () {
      get("restrict_appointment_edit", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.restrictAppointment = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    forceRerender() {
      this.componentKey += 1;
      this.showCustomField = true;
    },
    clearAppointmentData() {
      this.upload_appointment_report = [];
      // this.appointmentFormObj.appointment_start_date = ''
      this.appointmentFormObj.id !== undefined
        ? this.appointmentFormObj.id
        : "";
      // this.appointmentFormObj.clinic_id = ''
      this.appointmentFormObj.doctor_id = "";
      let patient_id = "";
      if (this.$route.params.patient_id !== undefined) {
        patient_id = this.$route.params.patient_id;
      } else if (this.patient_profile_id) {
        patient_id = this.patient_profile_id;
      }
      this.appointmentFormObj.patient_id = patient_id;
      this.appointmentFormObj.service_id = "";
      this.appointmentFormObj.visit_type = [];
      if (this.getUserRole() === "doctor") {
        this.appointmentFormObj.doctor_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
        let doctor_id = this.userData;
        this.showCustomField = true;
        // this.getDoctorsServices(doctor_id.ID);
      } else if (this.getUserRole() === "patient") {
        this.appointmentFormObj.patient_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
      }
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");
    },
    clinicChange(selectedOption) {
      if (this.getUserRole() !== "patient") {
        this.patientMultiselectLoader = true;
        get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: selectedOption.id,
        })
          .then((response) => {
            this.patientMultiselectLoader = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.patients = response.data.data;
            }
          })
          .catch((error) => {
            this.patientMultiselectLoader = false;
            console.log(error);
            displayErrorMessage("Internal server error");
          });
      }
      // reset appointment form data on clinic change
      this.clearAppointmentData();

      if (this.getUserRole() === "doctor") {
        let doctor_id = this.userData;
        this.getDoctorsServices(doctor_id.ID);
        this.getDoctorActiveDays(selectedOption.id, 1);
      } else {
        this.doctorMultiselectLoader = true;
        get("get_static_data", {
          data_type: "get_users_by_clinic",
          clinic_id: selectedOption.id,
        })
          .then((response) => {
            this.doctorMultiselectLoader = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.doctors = response.data.data;
              if (response.data.postfix !== undefined) {
                this.postfix = response.data.postfix;
              }
              if (response.data.prefix !== undefined) {
                this.prefix = response.data.prefix;
              }
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            this.doctorMultiselectLoader = false;
            console.log(error);
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
      this.appointment_tax_data();
    },
    dispatchTimeSlot: function () {
      let clinic_id =
        this.appointmentFormObj.clinic_id !== undefined &&
        this.appointmentFormObj.clinic_id.id !== undefined
          ? this.appointmentFormObj.clinic_id.id
          : this.userData.default_clinic_id;
      this.getTimeSlot({
        date: moment(this.appointmentFormObj.appointment_start_date).format(
          "YYYY-MM-DD"
        ),
        appointment_id:
          this.appointmentFormObj.id !== undefined
            ? this.appointmentFormObj.id
            : "",
        clinic_id: clinic_id,
        doctor_id: this.appointmentFormObj.doctor_id,
      });
    },
    appointmentCloseForm() {
      this.upload_appointment_report = [];
      this.$emit("closeAppointmentForm");
      this.appointmentModel = false;
      this.loading = false;
    },
    getTimeSlot: function (data) {
      data.service = this.appointmentFormObj.visit_type;
      this.$store.dispatch("appointmentModule/fetchAppointmentSlots", data);
    },
    handleTimeChange(time) {
      this.appointmentFormObj.appointment_start_time = time;
    },

    getDoctorDropdown: function () {
      this.doctorMultiselectLoader = true;
      let clinic_id = this.appointmentFormObj.clinic_id;
      if (typeof clinic_id === "object") {
        clinic_id = this.appointmentFormObj.clinic_id.id;
      }
      get("get_static_data", {
        data_type: "clinic_doctors",
        clinic_id: clinic_id,
        module_type: "appointment",
      })
        .then((response) => {
          this.doctorMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.doctors = response.data.data;
            if (response.data.postfix !== undefined) {
              this.postfix = response.data.postfix;
            }
            if (response.data.prefix !== undefined) {
              this.prefix = response.data.prefix;
            }
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.doctorMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    appointmentTypeChangeSelect(selected) {
      if (
        selected.multiple !== undefined &&
        selected.multiple !== "" &&
        selected.multiple == "no"
      ) {
        this.appointmentFormObj.visit_type = [];
        setTimeout(() => {
          this.appointmentFormObj.visit_type = [];
          this.appointmentFormObj.visit_type.push(selected);
        }, 1000);
        this.appointmentTypeMultiselect = false;
      } else {
        this.appointmentTypeMultiselect = true;
      }
      setTimeout(() => {
        this.dispatchTimeSlot();
        this.appointment_tax_data();
      }, 300);
    },
    appointmentTypeChangeUnselect(selected) {
      this.appointmentFormObj.visit_type = [];
      if (
        selected.multiple !== undefined &&
        selected.multiple !== "" &&
        selected.multiple == "no"
      ) {
        this.appointmentTypeMultiselect = true;
      }
      setTimeout(() => {
        this.dispatchTimeSlot();
        this.appointment_tax_data();
      }, 300);
    },
    handleDoctorChange: function (selectedOption) {
      this.DoctorWorkdays = [];
      let clinic_id =
        this.appointmentFormObj.clinic_id !== undefined &&
        this.appointmentFormObj.clinic_id.id !== undefined
          ? this.appointmentFormObj.clinic_id.id
          : this.userData.default_clinic_id;
      this.getTimeSlot({
        date: moment(this.appointmentFormObj.appointment_start_date).format(
          "YYYY-MM-DD"
        ),
        appointment_id:
          this.appointmentFormObj.id !== undefined
            ? this.appointmentFormObj.id
            : "",
        clinic_id: clinic_id,
        doctor_id: selectedOption.id,
      });
      this.getDoctorActiveDays(clinic_id, selectedOption.id);
      this.appointmentFormObj.visit_type = [];
      this.getDoctorsServices(selectedOption.id);
      this.forceRerender();
      this.appointment_tax_data();
    },
    getDoctorActiveDays: function (clinic_id, id) {
      get("get_doctor_workdays", {
        clinic_id: clinic_id,
        doctor_id: id,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.holiday = response.data.holiday;
            this.DoctorWorkdays = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    handleDateChange: function (selectedOption) {
      this.appointmentFormObj.appointment_start_time = "";
      let clinic_id =
        this.appointmentFormObj.clinic_id !== undefined &&
        this.appointmentFormObj.clinic_id.id !== undefined
          ? this.appointmentFormObj.clinic_id.id
          : this.userData.default_clinic_id;
      this.getTimeSlot({
        date: moment(selectedOption).format("YYYY-MM-DD"),
        appointment_id:
          this.appointmentFormObj.id !== undefined
            ? this.appointmentFormObj.id
            : "",
        clinic_id: clinic_id,
        doctor_id: this.appointmentFormObj.doctor_id.id,
      });
    },
    handleDoctorUnselect: function (id) {
      this.clearAppointmentData();
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");
      this.appointment_tax_data();
    },
    handleFormSubmit: function () {
      this.loading = true;
      this.submitted = true;
      if (this.userData.addOns.kiviPro != true) {
        this.appointmentFormObj.clinic_id =
          this.appointmentFormObj.clinic_id !== undefined &&
          this.appointmentFormObj.clinic_id.id !== undefined
            ? this.appointmentFormObj.clinic_id.id
            : this.userData.default_clinic_id;
      }
      // stop here if form is invalid
      let patient_id = "";
      if (this.$route.params.patient_id !== undefined) {
        patient_id = this.$route.params.patient_id;
      } else if (this.patient_profile_id) {
        patient_id = this.patient_profile_id;
      }
      if (patient_id) {
        this.appointmentFormObj.patient_id = {
          id: patient_id,
        };
      }
      this.$v.$touch();
      if (this.$v.appointmentFormObj.$invalid) {
        this.loading = false;
        return;
      }

      if (this.requiredFields !== undefined && this.requiredFields.length > 0) {
        this.loading = false;
        displayErrorMessage(
          this.formTranslation.common.all_required_field_validation
        );
        return;
      }
      if (
        this.getUserRole() === "patient" &&
        Object.keys(this.userData.all_payment_method).length > 0 &&
        this.appointmentFormObj.id === undefined
      ) {
        this.appointmentData.payment_mode = Object.keys(
          this.userData.all_payment_method
        )[0];
        this.loading = false;
        this.appointmentModel = true;
        return;
      }
      if (validateForm("appointmentDataForm")) {
        this.bookAppointmentHandle();
      }
    },
    closeAppointmentModal() {
      this.appointmentModel = false;
      this.loading = false;
      this.overlaySpinner = false;
    },
    bookAppointmentHandle: function () {
      let appointmentData = Object.assign({}, this.appointmentFormObj);
      appointmentData.appointment_start_date = moment(
        appointmentData.appointment_start_date
      ).format("YYYY-MM-DD");
      appointmentData.tax = this.taxes;
      post("appointment_save", appointmentData)
        .then((response) => {
          this.loading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            let checkWoocommerceCart = response.data;
            if (
              checkWoocommerceCart.woocommerce_cart_data !== undefined &&
              checkWoocommerceCart.woocommerce_cart_data != null
            ) {
              if (
                checkWoocommerceCart.woocommerce_cart_data
                  .woocommerce_redirect !== undefined
              ) {
                if (this.appointmentFormObj.payment_mode === "paymentPaypal") {
                  kiviOpenPaymentWindow(
                    checkWoocommerceCart.woocommerce_cart_data
                      .woocommerce_redirect
                  );
                  this.overlaySpinner = true;
                  this.timer = setInterval(this.checkChildWindow, 500);
                  return;
                } else {
                  location.href =
                    checkWoocommerceCart.woocommerce_cart_data.woocommerce_redirect;
                  return;
                }
              }
            } else {
              if (this.appointmentFormObj.payment_mode === "paymentRazorpay") {
                if (response.data.checkout_detail) {
                  kivicareCreateRazorpayCheckoutPage(
                    response.data.checkout_detail
                  );
                  this.overlaySpinner = true;
                  this.timer = setInterval(this.checkChildWindow, 500);
                } else {
                  displayErrorMessage(response.data.message);
                }
              } else if (
                this.appointmentFormObj.payment_mode === "paymentStripepay"
              ) {
                if (response.data.checkout_detail) {
                  kiviOpenPaymentWindow(
                    response.data.checkout_detail.payment_url
                  );
                  this.overlaySpinner = true;
                  this.timer = setInterval(this.checkChildWindow, 500);
                } else {
                  displayErrorMessage(response.data.message);
                }
              } else {
                displayMessage(response.data.message);
                this.$store.commit("appointmentModule/RESET_TIME_SLOT");
                this.$emit("appointmentSaved", response.data.data);
                if (this.patient_profile_id) {
                  this.$store.dispatch(
                    "appointmentModule/fetchAppointmentEncounterCount",
                    { id: this.patient_profile_id }
                  );
                }
                this.overlaySpinner = false;
                this.appointmentModel = false;
                this.loading = false;
              }
            }
          } else {
            displayErrorMessage(response.data.message);
            this.overlaySpinner = false;
            this.appointmentModel = false;
            this.loading = false;
          }
        })
        .catch((error) => {
          this.appointmentModel = false;
          console.log(error);
          this.overlaySpinner = false;
          this.loading = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    checkChildWindow() {
      let ele = document.getElementById("payment_status_child");
      if (ele !== null && ele.value !== "") {
        clearInterval(this.timer);
        if (ele.value === "failed") {
          displayErrorMessage(
            this.formTranslation.common.payment_transaction_failed
          );
          this.$refs.appointment_detail.loading = false;
          ele.value = "";
        } else if (ele.value === "approved") {
          ele.value = "";
          displayMessage(this.formTranslation.common.payment_transaction_saved);
          this.appointmentModel = false;
          this.$store.commit("appointmentModule/RESET_TIME_SLOT");
          this.$emit("appointmentSaved", {});
        } else {
          ele.value = "";
        }
        if (this.patient_profile_id) {
          this.$store.dispatch(
            "appointmentModule/fetchAppointmentEncounterCount",
            { id: this.patient_profile_id }
          );
        }
        this.overlaySpinner = false;
        this.loading = false;
      }
    },
    getDoctorsServices: function (doctorId) {
      this.serviceMultiselectLoader = true;
      this.appointmentTypes = [];
      let clinic_id =
        this.appointmentFormObj.clinic_id &&
        this.appointmentFormObj.clinic_id.id
          ? this.appointmentFormObj.clinic_id.id
          : this.userData.default_clinic_id;
      get("service_list", {
        module_type: "appointment_form",
        limit: 0,
        doctor_id: doctorId,
        clinic_id: clinic_id,
      })
        .then((response) => {
          this.serviceMultiselectLoader = false;
          this.appointmentTypes = JSON.parse(
            JSON.stringify(response.data.data)
          );
          this.appointmentTypes = response.data.data;
          if (
            this.appointmentTypes.length > 0 &&
            this.appointmentFormObj.id === undefined
          ) {
            this.appointmentFormObj.visit_type.push(this.appointmentTypes[0]);
            if (
              this.appointmentTypes[0].multiple !== undefined &&
              this.appointmentTypes[0].multiple == "no"
            ) {
              this.appointmentTypeMultiselect = false;
            }
          }
          this.dispatchTimeSlot();
          this.appointment_tax_data();
        })
        .catch((error) => {
          this.serviceMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getCustomFieldsValues: function (fieldsObj) {
      if (!fieldsObj || fieldsObj === undefined) {
        return false;
      }

      for (
        let index = 0;
        index < this.appointmentFormObj.custom_fields.length;
        index++
      ) {
        const customFielsElement = Object.assign(
          {},
          this.appointmentFormObj.custom_fields[index]
        );
        const customFielsElementValue = Object.assign({}, fieldsObj);
        customFielsElement.field_data =
          customFielsElementValue["custom_field_" + customFielsElement.id];
        this.appointmentFormObj.custom_fields[index] = customFielsElement;
      }

      //  previous version dead code
      // this.appointmentFormObj.custom_fields = fieldsObj;
      this.appointmentFormObj.custom_fields_data = fieldsObj;
    },
    getRequireFields: function (validateRequired) {
      this.requiredFields = validateRequired;
    },
    multiUploadProfile: function () {
      let _this = this;
      var custom_uploader = kivicareCustomImageUploader(
        this.formTranslation,
        "report",
        this.userData.addOns.kiviPro == true
      );

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .map((item) => {
            item.toJSON();
            return item;
          });
        if (_this.appointmentData.file == undefined) {
          _this.appointmentData.file = [];
        }
        attachment.map((report) => {
          _this.upload_appointment_report.push({
            name: report.attributes.filename,
            url: report.attributes.url,
          });
          _this.appointmentData.file.push(report.attributes.id);
        });
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    appointment_tax_data() {
      if (this.userData.addOns.kiviPro === false) {
        return;
      }
      this.taxes = [];
      post("tax_calculated_data", this.appointmentData)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.taxes = response.data.data;
            this.appointmentFormObj.tax = response.data.tax_total;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage("Internal server error");
        });
    },
  },
  computed: {
    canAddPatient() {
      return (
        ["administrator", "clinic_admin", "receptionist", "doctor"].includes(
          this.getUserRole()
        ) &&
        this.appointmentFormObj.id === undefined &&
        this.kcCheckPermission("patient_add")
      );
    },

    canShowClinicSelection() {
      return (
        this.userData.addOns.kiviPro === true &&
        ["administrator", "doctor", "patient"].includes(this.getUserRole())
      );
    },

    canAddService() {
      return (
        ["administrator", "clinic_admin", "receptionist", "doctor"].includes(
          this.getUserRole()
        ) &&
        this.appointmentFormObj.id === undefined &&
        this.kcCheckPermission("service_add")
      );
    },

    showPatientSelection() {
      return (
        this.getUserRole() !== "patient" &&
        this.$route.params.patient_id === undefined &&
        !this.patient_profile_id
      );
    },
    timeSlots() {
      return this.$store.state.appointmentModule.timeSlots;
    },
    // patients() {
    //   return this.$store.state.userDataModule.userDropDownData.patients;
    // },
    clinics() {
      this.clinicMultiselectLoader = false;
      if (this.$store.state.clinic.length > 0) {
        if (this.appointmentFormObj.id === undefined) {
          this.appointmentFormObj.clinic_id = this.$store.state.clinic[0];
          if (this.getUserRole() !== "doctor") {
            this.clinicChange(this.$store.state.clinic[0]);
          }
          // this.clinicChange(this.$store.state.clinic[0] );
        }
        return this.$store.state.clinic;
      } else {
        return [];
      }
    },
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
    teleMedEn() {
      return this.userData.addOns.telemed;
    },
    customFieldsData() {
      return this.appointmentFormObj.custom_fields
        ? this.appointmentFormObj.custom_fields
        : [];
    },
    appointmentDoctorId() {
      if (this.appointmentFormObj.doctor_id !== undefined) {
        if (typeof this.appointmentFormObj.doctor_id == "object") {
          return this.appointmentFormObj.doctor_id.id;
        }
        return this.appointmentFormObj.doctor_id;
      }
    },
    deleteAppointmentReport: function (appointmentData) {},
    fileUploadEnable() {
      return this.$store.state.appointmentModule.file_upload_status;
    },
    enableDisableAppointmentDescriptionStatus() {
      return this.$store.state.appointmentModule.description_status;
    },
  },
  watch: {
    appointmentData: {
      handler(newData) {
        if (Object.keys(newData).length > 0) {
          this.initFormData();
        }
      },
      deep: true,
    },
  },
};
</script>
<style scoped>
.appointment-widget-service-list {
  border: 1px solid #d0cece;
  padding: 10px 12px;
  border-radius: 0.25rem;
}
.badge {
  text-transform: unset !important;
}
</style>
