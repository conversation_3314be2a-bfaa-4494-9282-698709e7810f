<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plan Expired</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        
        .bg-gradient {
            background: linear-gradient(to bottom right, #f9fafb, #f3f4f6);
        }
        
        .feature-dot {
            width: 6px;
            height: 6px;
            background-color: #3b82f6;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .btn-primary {
            background-color: #2563eb;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            background-color: #1d4ed8;
            transform: translateY(-1px);
        }
        
        .alert-circle {
            width: 48px;
            height: 48px;
        }
        
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body>
    <div class="min-h-screen bg-gradient flex items-center justify-center p-4">
        <div class="max-w-md w-full bg-white rounded-lg card-shadow">
            <div class="p-6">
                <div class="text-center space-y-6">
                    <!-- Icon -->
                    <div class="flex justify-center">
                        <div class="bg-red-100 p-3 rounded-full">
                            <svg class="alert-circle text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Text Content -->
                    <div class="space-y-2">
                        <h1 class="text-2xl font-bold text-gray-900">Plan Expired</h1>
                        <p class="text-gray-600">
                            Your subscription has ended. Renew now to continue enjoying all premium features.
                        </p>
                    </div>

                    <!-- Features List -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <ul class="space-y-3">
                            <li class="flex items-center text-sm text-gray-600">
                                <div class="feature-dot"></div>
                                Unlimited access to all features
                            </li>
                            <li class="flex items-center text-sm text-gray-600">
                                <div class="feature-dot"></div>
                                Priority customer support
                            </li>
                            <li class="flex items-center text-sm text-gray-600">
                                <div class="feature-dot"></div>
                                Advanced analytics and reporting
                            </li>
                        </ul>
                    </div>

                    <!-- Action Button -->
                    <a href="<?php echo esc_url(pmpro_url( 'levels' )) ?>" class="w-full btn-primary text-white px-6 py-2 rounded-lg shadow-lg hover:shadow-xl flex items-center justify-center space-x-2">
                        <span>Renew Now</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>

                    <!-- Support Link -->
                    <p class="text-sm text-gray-500">
                        Need help? <a href="https://help.medroid.ai/" class="text-blue-600 hover:text-blue-700 font-medium">Contact support</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>