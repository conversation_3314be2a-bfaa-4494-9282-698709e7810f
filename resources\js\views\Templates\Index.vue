<template>
  <div>
    <!-- Loading State -->
    <div v-if="!userLoaded" class="flex justify-center items-center py-12">
      <svg class="animate-spin h-8 w-8 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
        viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
        </path>
      </svg>
    </div>

    <template v-else>
      <!-- Content -->
      <div class="kivi-widget kivicare-template-manager-card p-4">
  <div class="kivi-widget-title bg-white p-4 border-b border-gray-200 mb-4">
    <div class="flex justify-between items-center">
      <div class="flex items-center">
        <div class="kivi-widget-title-icon bg-blue-600 p-1 rounded">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <line x1="10" y1="9" x2="8" y2="9"></line>
          </svg>
        </div>
        <h4 class="kivi-widget-title-text ml-2 text-black text-xl font-bold">{{ $t("Template Manager") }}</h4>
      </div>
      <div class="flex">
        <button v-if="!hasSystemTemplates && getUserRole() === 'administrator'"
          class="px-4 py-2 mr-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow text-sm"
          @click="importSystemTemplates" :disabled="isLoading">
          <span v-if="isImporting" class="inline-block animate-spin mr-1">↻</span>
          {{ $t("Import Default Templates") }}
        </button>
        <button class="px-4 py-2 bg-black hover:bg-gray-800 text-white rounded-md shadow text-sm"
          @click="openCreateModal">
          {{ $t("New Template") }}
        </button>
      </div>
    </div>
  </div>

        <div class="kivi-widget-content">
          <!-- Filters -->
          <div class="flex items-center mb-4">
            <div class="mr-2 w-48">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("Category") }}</label>
              <select v-model="filters.category" class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
                @change="applyFilters">
                <option value="">{{ $t("All Categories") }}</option>
                <option value="general">{{ $t("General") }}</option>
                <option value="referral">{{ $t("Referral") }}</option>
                <option value="sick_note">{{ $t("Sick Note") }}</option>
                <option value="consultation">{{ $t("Consultation") }}</option>
                <option value="procedure">{{ $t("Procedure") }}</option>
              </select>
            </div>
            <div class="mr-2 w-48">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("Share Status") }}</label>
              <select v-model="filters.share_status"
                class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2" @change="applyFilters">
                <option value="">{{ $t("All") }}</option>
                <option value="private">{{ $t("Private") }}</option>
                <option value="clinic">{{ $t("Clinic") }}</option>
                <option value="public">{{ $t("Public") }}</option>
              </select>
            </div>
            <div class="mr-2 w-48" v-if="getUserRole() === 'administrator'">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("Type") }}</label>
              <select v-model="filters.type" class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
                @change="applyFilters">
                <option value="">{{ $t("All") }}</option>
                <option value="system">{{ $t("System") }}</option>
                <option value="user">{{ $t("User") }}</option>
              </select>
            </div>
            <div class="flex-grow">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("Search") }}</label>
              <input type="text" v-model="filters.search"
                class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
                :placeholder="$t('Search templates...')" @input="applyFilters" />
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="flex justify-center items-center py-12">
            <svg class="animate-spin h-8 w-8 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
          </div>

          <!-- Error State -->
          <div v-else-if="loadError" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 class="mt-2 text-lg font-medium text-red-800">{{ $t("Error loading templates") }}</h3>
            <p class="mt-1 text-sm text-red-600">{{ loadError }}</p>

            <!-- Additional help instructions if it's a 404 error -->
            <div v-if="loadError.includes('404') || loadError.includes('not found')"
              class="mt-3 p-4 bg-yellow-50 rounded-lg text-left mx-auto max-w-md">
              <p class="text-sm text-yellow-700 font-medium">{{ $t("What to do:") }}</p>
              <ol class="mt-2 text-sm text-yellow-700 list-decimal pl-4 space-y-1">
                <li>{{ $t("The API endpoint might still be initializing") }}</li>
                <li>{{ $t("Try refreshing the page") }}</li>
                <li>{{ $t("If the issue persists, please deactivate and reactivate the plugin") }}</li>
              </ol>
            </div>

            <div class="mt-6">
              <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow text-sm"
                @click="loadTemplates" :disabled="isRetrying">
                <span v-if="isRetrying" class="inline-block animate-spin mr-1">↻</span>
                {{ isRetrying ? $t("Retrying...") : $t("Try Again") }}
              </button>
            </div>
          </div>

          <!-- Templates List -->
          <div v-else-if="filteredTemplates.length > 0" class="space-y-4">
            <div v-for="template in filteredTemplates" :key="template.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="text-lg font-medium">{{ template.name }}</h4>
                  <div class="flex items-center mt-1">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mr-2" :class="{
                      'bg-blue-100 text-blue-800': template.category === 'general',
                      'bg-green-100 text-green-800': template.category === 'referral',
                      'bg-yellow-100 text-yellow-800': template.category === 'sick_note',
                      'bg-purple-100 text-purple-800': template.category === 'consultation',
                      'bg-pink-100 text-pink-800': template.category === 'procedure',
                    }">
                      {{ template.category_label }}
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mr-2" :class="{
                      'bg-gray-100 text-gray-800': template.share_status === 'private',
                      'bg-indigo-100 text-indigo-800': template.share_status === 'clinic',
                      'bg-green-100 text-green-800': template.share_status === 'public',
                    }">
                      {{ template.share_status_label }}
                    </span>
                    <span v-if="template.is_system"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {{ $t("System") }}
                    </span>
                  </div>
                  <div class="text-sm text-gray-500 mt-1">
                    <span v-if="template.doctor_name">
                      {{ $t("Created by") }}: {{ template.doctor_name }}
                    </span>
                    <span v-if="template.clinic_name">
                      {{ template.doctor_name ? ' | ' : '' }}{{ $t("Clinic") }}: {{ template.clinic_name }}
                    </span>
                  </div>
                </div>
                <div class="flex items-center">
                  <button class="text-blue-600 hover:text-blue-800 mr-2" @click="viewTemplate(template)">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button class="text-gray-600 hover:text-gray-800 mr-2" @click="cloneTemplate(template)">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                    </svg>
                  </button>
                  <button v-if="template.is_editable" class="text-indigo-600 hover:text-indigo-800 mr-2"
                    @click="editTemplate(template)">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button v-if="template.is_editable" class="text-red-600 hover:text-red-800"
                    @click="confirmDelete(template)">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"
              aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
            </svg>
            <h3 class="mt-2 text-lg font-medium text-gray-900">{{ $t("No templates found") }}</h3>
            <p class="mt-1 text-sm text-gray-500">{{ $t("Get started by creating a new template.") }}</p>
            <div class="mt-6 flex justify-center space-x-4">
              <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow text-sm"
                @click="openCreateModal">
                {{ $t("New Template") }}
              </button>
              <button v-if="getUserRole() === 'administrator'"
                class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md shadow text-sm"
                @click="importSystemTemplates" :disabled="isImporting">
                <span v-if="isImporting" class="inline-block animate-spin mr-1">↻</span>
                {{ $t("Import Default Templates") }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Create/Edit Template Modal -->
      <TemplateForm v-if="showFormModal" :show="showFormModal" :edit-mode="editMode" :template-data="currentTemplate"
        @close="closeFormModal" @saved="loadTemplates" />

      <!-- View Template Modal -->
      <TemplateView v-if="showViewModal" :show="showViewModal" :template-id="selectedTemplateId" @close="closeViewModal"
        @edit="openEditModalFromView" @clone="cloneTemplateFromView" @delete="handleDeleteFromView" />

      <!-- Delete Confirmation Modal -->
      <div v-if="showDeleteModal" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>
          <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
          <div
            class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div
                  class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $t("Delete Template") }}</h3>
                  <div class="mt-2">
                    <p class="text-sm text-gray-500">
                      {{ $t("Are you sure you want to delete this template? This action cannot be undone.") }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button type="button"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                @click="deleteTemplate">
                {{ $t("Delete") }}
              </button>
              <button type="button"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                @click="cancelDelete">
                {{ $t("Cancel") }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import axios from "axios";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import TemplateForm from "./TemplateForm.vue";
import TemplateView from "./TemplateView.vue";
import { get, post } from '../../config/request';

export default {
  components: {
    TemplateForm,
    TemplateView
  },
  data() {
    return {
      templates: [],
      filteredTemplates: [],
      isLoading: true,
      isImporting: false,
      isRetrying: false,
      showFormModal: false,
      showViewModal: false,
      showDeleteModal: false,
      editMode: false,
      currentTemplate: null,
      selectedTemplateId: null,
      templateToDelete: null,
      hasSystemTemplates: false,
      userLoaded: false, // Track if user data is loaded
      loadError: null, // Track loading errors
      retryCount: 0,
      filters: {
        category: "",
        share_status: "",
        type: "",
        search: ""
      }
    };
  },

  created() {
    // Simply set userLoaded to true always to bypass the loading state
    this.userLoaded = true;
  },

  mounted() {
    // Load templates immediately
    this.loadTemplates();
  },

  methods: {
    getUserRole() {
      try {
        // Default to an empty role if we can't determine the role
        if (!this.$root || !this.$root.current_user) {
          console.log("Current user data not available");
          return '';
        }
        return this.$root.current_user.role || '';
      } catch (e) {
        console.error("Error getting user role:", e);
        return '';
      }
    },

    async loadTemplates() {
      this.isLoading = true;

      get('templates_list')
        .then(response => {
          console.log('Templates response:', response);
          if (response.data.status) {
            this.templates = response.data.data;
            console.log("this.templates", this.templates);
            this.filteredTemplates = this.templates;
            this.hasSystemTemplates = this.templates.some(template => template.is_system);
            this.applyFilters();
          }
        })
        .catch(error => {
          console.error('Error loading templates:', error);
          this.loadError = error.message || "Error loading templates";
          this.templates = [];
          this.filteredTemplates = [];
          displayErrorMessage(this.loadError);
        })
        .finally(() => {
          this.isLoading = false;
          this.isRetrying = false;

          // Reset retry count after successful load
          if (!this.loadError) {
            this.retryCount = 0;
          }
        });
    },

    openCreateModal() {
      this.editMode = false;
      this.currentTemplate = {
        name: "",
        category: "general",
        content: "",
        share_status: "private"
      };
      this.showFormModal = true;
    },

    editTemplate(template) {
      this.editMode = true;
      this.currentTemplate = { ...template };
      this.showFormModal = true;
    },

    viewTemplate(template) {
      this.selectedTemplateId = template.id;
      this.showViewModal = true;
    },

    openEditModalFromView(template) {
      this.closeViewModal();
      this.editTemplate(template);
    },

    cloneTemplate(template) {
      console.log(`Cloning template with ID: ${template.id}`)

      this.isLoading = true

      // Log the data being sent for debugging
      const cloneData = {
        id: template.id
      }
      console.log('Cloning template data:', cloneData)

      post('clone_md_template', cloneData)
        .then(response => {
          console.log('Template clone response:', response)
          if (response.data.status) {
            displayMessage('Template cloned successfully')
            this.loadTemplates()
          } else {
            displayErrorMessage(response.data.message)
          }
        })
        .catch(error => {
          console.error('Template clone error:', error)
          displayErrorMessage(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    cloneTemplateFromView(templateId) {
      const template = this.templates.find(t => t.id === templateId);
      if (template) {
        this.cloneTemplate(template);
      }
      this.closeViewModal();
    },

    handleDeleteFromView(templateId) {
      // Reload templates after deletion from view modal
      this.loadTemplates();
    },

    confirmDelete(template) {
      this.templateToDelete = template;
      this.showDeleteModal = true;
    },

    deleteTemplate() {
      if (!this.templateToDelete) return

      this.isLoading = true

      // Log the data being sent for debugging
      const deleteData = {
        id: this.templateToDelete.id
      }
      console.log('Deleting template data:', deleteData)

      post('delete_md_template', deleteData)
        .then(response => {
          console.log('Template delete response:', response)
          if (response.data.status) {
            displayMessage('Template deleted successfully')
            this.loadTemplates()
          } else {
            displayErrorMessage(response.data.message)
          }
        })
        .catch(error => {
          console.error('Template delete error:', error)
          displayErrorMessage(error)
        })
        .finally(() => {
          this.isLoading = false
          this.cancelDelete()
        })
    },

    cancelDelete() {
      this.templateToDelete = null;
      this.showDeleteModal = false;
    },

    closeFormModal() {
      this.showFormModal = false;
      this.currentTemplate = null;

      // Automatically reload templates after a short delay
      setTimeout(() => {
        console.log('Auto-reloading templates after form close');
        this.loadTemplates();
      }, 500);
    },

    closeViewModal() {
      this.showViewModal = false;
      this.selectedTemplateId = null;
    },

    applyFilters() {
      let filtered = [...this.templates];

      // Apply category filter
      if (this.filters.category) {
        filtered = filtered.filter(t => t.category === this.filters.category);
      }

      // Apply share status filter
      if (this.filters.share_status) {
        filtered = filtered.filter(t => t.share_status === this.filters.share_status);
      }

      // Apply type filter
      if (this.filters.type) {
        filtered = filtered.filter(t =>
          this.filters.type === 'system' ? t.is_system : !t.is_system
        );
      }

      // Apply search filter
      if (this.filters.search) {
        const searchTerm = this.filters.search.toLowerCase();
        filtered = filtered.filter(t =>
          t.name.toLowerCase().includes(searchTerm) ||
          (t.doctor_name && t.doctor_name.toLowerCase().includes(searchTerm)) ||
          (t.clinic_name && t.clinic_name.toLowerCase().includes(searchTerm))
        );
      }

      this.filteredTemplates = filtered;
    },

    async importSystemTemplates() {
      if (this.isImporting) return;

      this.isImporting = true;
      try {
        const response = await axios.post("/wp-json/kivicare/api/v1/import_default_templates", {
          _ajax_nonce: window.kivicare.ajax_nonce
        });

        if (response.data.status) {
          displayMessage(response.data.message);
          this.loadTemplates();
        } else {
          throw new Error(response.data.message || "Failed to import templates");
        }
      } catch (error) {
        displayErrorMessage(error.message || "Error importing templates");
      } finally {
        this.isImporting = false;
      }
    }
  }
};
</script>