{"common": {"kivicare_Management_system": "KiviCare - Clinic & Patient Management System (EHR)", "no_appointments": "Aucun rendez-vous trouvé", "loading": "Chargement ...", "cancel": "Annuler", "date": "Date", "close": "<PERSON><PERSON><PERSON>", "closed": "<PERSON><PERSON><PERSON>", "select_option": "- Sélectionner une option -", "all": "<PERSON>ut", "back_to_wordpress": "Retour à Wordpress ", "update": "Aucun rendez-vous trouvé0", "my_profile": "Aucun rendez-vous trouvé1", "change_password": "Aucun rendez-vous trouvé2", "logout": "Aucun rendez-vous trouvé3", "full_screen": "Aucun rendez-vous trouvé4", "warning_zoom_configuration": "Aucun rendez-vous trouvé5", "zoom_configuration_link": "Aucun rendez-vous trouvé6", "dob": "Aucun rendez-vous trouvé7", "dob_required": "Aucun rendez-vous trouvé8", "gender": "Aucun rendez-vous trouvé9", "gender_required": "Chargement ...0", "male": "Chargement ...1", "female": "Chargement ...2", "other": "Chargement ...3", "service": "Chargement ...4", "services": "Chargement ...5", "sr_no": "Chargement ...6", "item_name": "Chargement ...7", "price": "Chargement ...8", "quantity": "Chargement ...9", "total": "Annuler0", "no_records_found": "Annuler1", "_note": "Annuler2", "note": "Annuler3", "status": "Annuler4", "action": "Annuler5", "title": "Annuler6", "name": "Annuler7", "doctor": "Annuler8", "doctors": "Annuler9", "patient": "Date0", "fname": "Date1", "fname_required": "Date2", "lname": "Date3", "lname_required": "Date4", "email": "Date5", "email_required": "Date6", "password": "Date7", "pwd_required": "Date8", "repeat_pwd": "Date9", "repeat_password_required": "Fermer0", "pwd_not_match": "Fermer1", "login_btn": "Fermer2", "sign_up": "Fermer3", "no": "Fermer4", "dr": "Dr.", "filters": "filtres", "back": "Retour", "save": "Enregistrer", "invalid_email": "Format d'e-mail non valide", "active": "Actif", "inactive": "Inactif", "name_required": "Le nom est requis", "email_address": "Adresse e-mail", "contact_info": "Informations de contact", "settings": "filtres0", "fname_validation_1": "filtres1", "fname_validation_2": "filtres2", "lname_validation_1": "filtres3", "lname_validation_2": "filtres4", "contact": "filtres5", "contact_required": "filtres6", "contact_validation_1": "filtres7", "contact_validation_2": "filtres8", "telemed": "filtres9", "to": "Retour0", "time": "Retour1", "contact_no": "Retour2", "contact_num_required": "Retour3", "city": "Retour4", "city_required": "Retour5", "city_validation_1": "Retour6", "city_validation_2": "Retour7", "state": "Retour8", "state_validation_1": "Retour9", "state_validation_2": "Enregistrer0", "country": "Enregistrer1", "country_required": "Enregistrer2", "country_validation_1": "Enregistrer3", "country_validation_2": "Enregistrer4", "address": "Enregistrer5", "address_required": "Enregistrer6", "postal_code": "Enregistrer7", "postal_code_required": "Enregistrer8", "postal_code_validation_1": "Enregistrer9", "postal_code_validation_2": "Format d'e-mail non valide0", "profile": "Format d'e-mail non valide1", "static_data": "Format d'e-mail non valide2", "handle_request": "Format d'e-mail non valide3", "email_to_get_help": "Format d'e-mail non valide4", "note_options": "Format d'e-mail non valide5", "note_1": "Format d'e-mail non valide6", "note_2": "Format d'e-mail non valide7", "wp_rollback": "Format d'e-mail non valide8", "plugin": "Format d'e-mail non valide9", "keep_improving": "Actif0", "currency_setting": "Actif1", "module": "Actif2", "i_understand": "Actif3", "version": "Actif4", "read_notice": "Veuillez lire ce journal ci-dessous avant d'aller de l'avant:", "faced_issue": "Problèmes rencontrés?", "if_use_older_version": "Si vous rencontrez des problèmes avec cette version et que vous souhaitez continuer avec l'ancienne version, veuillez installer et utiliser", "check_video": "Pour une migration en douceur vers la nouvelle version, vérifiez le guide vidéo suivant: ", "kivicare_v2": "Kivicare Upgrade V2.0.0", "appointment_flow": "<PERSON><PERSON><PERSON>vous", "basic_details": "<PERSON>é<PERSON> de base"}, "dashboard": {"dashboard": "Tableau de bord", "total_patients": "Nombre total de patients", "total_visited_patients": "Nombre total de patients visités", "total_doctors": "Nombre total de médecins", "total_clinic_doctors": "Nombre total de médecins de la clinique", "total_appointments": "Nombre total de rendez-vous", "total_clinic_appointments": "Nombre total de rendez-vous à la clinique", "latest_appointments": "Derniers rendez-vous", "reload": "Recharger", "view_all": "<PERSON><PERSON><PERSON><PERSON> tout", "weekly_appointments": "Nombre total de patients0", "weekly_total_appointments": "Nombre total de patients1", "today_appointment_list": "Nombre total de patients2", "total_revenue": "Nombre total de patients3", "total_clinic_revenue": "Nombre total de patients4", "total_generated_revenue": "Nombre total de patients5", "filter": "Nombre total de patients6", "reset": "Nombre total de patients7", "total_today_appointments": "Nombre total de patients8", "total_service": "Nombre total de patients9", "patients": "Nombre total de patients visités0", "medical_dashboard": "Nombre total de patients visités1"}, "doctor": {"doctor_name": "Nom du médecin", "doctor_specialization_required": "Une spécialisation du docteur est requise", "experience_year": "Expérience (en année)", "address_details": "<PERSON><PERSON><PERSON> de l'adresse", "degree": "Diplôme", "degree_required": "Le diplôme est requis", "degree_validation_1": "Le diplôme n'autorise qu'une valeur de caractère", "university": "Université", "university_required": "L'université est requise", "university_validation": "L'université autorise uniquement la valeur de caractère ", "year": "Une spécialisation du docteur est requise0", "select_year": "Une spécialisation du docteur est requise1", "year_required": "Une spécialisation du docteur est requise2", "college_university": "Une spécialisation du docteur est requise3", "api_key": "Une spécialisation du docteur est requise4", "api_secret": "Une spécialisation du docteur est requise5", "api_secret_required": "Une spécialisation du docteur est requise6", "api_key_required": "Une spécialisation du docteur est requise7", "zoom_configuration_guide": "Une spécialisation du docteur est requise8", "zoom_step1": "Une spécialisation du docteur est requise9", "zoom_step2": "Expérience (en année)0", "zoom_step3": "Expérience (en année)1", "zoom_step4": "Expérience (en année)2", "zoom_step5": "Expérience (en année)3", "other_detail": "Expérience (en année)4", "consultation_fees": "Expérience (en année)5", "video_consultation_fees": "Expérience (en année)6", "doctor_fees_required": "Expérience (en année)7", "zoom_market_place_portal": "Expérience (en année)8", "create_app": "Expérience (en année)9", "doctors_list": "Dé<PERSON> de l'adresse0", "other_details": "<PERSON>é<PERSON> de l'adresse1", "extra_detail": "Dé<PERSON> de l'adresse2", "add_doctor": "<PERSON><PERSON><PERSON> de l'adresse3", "edit_profile": "<PERSON><PERSON><PERSON> de l'adresse4", "basic_information": "<PERSON><PERSON><PERSON> de l'adresse5", "basic_settings": "<PERSON><PERSON><PERSON> de l'adresse6", "type": "<PERSON><PERSON><PERSON> de l'adresse7", "type_required": "Type obligatoire", "fees_type": "Type de frais", "range": "Fourchette", "fixed": "Fixe", "fees": "<PERSON><PERSON>", "fees_type_required": "Le type de frais est requis", "doc_fee_required": "Les frais de médecin sont requis", "doc_fee_validation_1": "Les frais de médecin doivent être supérieurs à zéro", "doc_fee_validation_2": "Les frais de médecin doivent être compris entre 0 et 1000000000000000000", "doc_fee_validation_3": " Honoraires du médecin Les honoraires minimums et les honoraires maximums sont exigés", "doc_fee_validation_4": "Type de frais0", "doc_fee_validation_5": "Type de frais1", "doc_fee_validation_6": "Type de frais2", "qualification_information": "Type de frais3", "qualification_speciality_details": "Type de frais4", "doctor_working_days_sessions": "Type de frais5", "charge_n_doc_selection": "Type de frais6", "doc_field_customization": "Type de frais7"}, "patient": {"patient_name": "Nom du patient", "add_patient": "Ajouter un patient", "patients_lists": "Listes des patients", "medical_report": "Rapport médical", "add_medical_report": "Ajouter un rapport médical", "upload_report": "Télécharger le rapport"}, "clinic": {"clinic": "Clinique", "receptionist": "Réceptionniste", "receptionists_list": "Liste des réceptionnistes", "add_receptionist": "Ajouter une réceptionniste", "clinic_name": "Nom de la clinique", "clinic_info": "Informations sur la clinique", "clinic_profile": "Profil de la clinique", "add_clinic": "Ajouter une clinique", "admin_profile": "Profil administrateur", "clinic_admin_detail": "Détails de l'administrateur de la clinique", "clinic_name_validation_1": "Réceptionniste0", "clinic_name_validation_2": "Réceptionniste1", "select_clinic": "Réceptionniste2", "speciality": "Réceptionniste3", "specialties": "Réceptionniste4", "specialities": "Réceptionniste5", "note_specialization": "Réceptionniste6", "clinic_specialities_required": "Réceptionniste7", "currency_prefix": "Réceptionniste8", "currency_postfix": "Réceptionniste9", "currency_decimals": "Liste des réceptionnistes0", "profile_img": "Liste des réceptionnistes1", "doctor_record_not_found": "Liste des réceptionnistes2", "blood_group": "Liste des réceptionnistes3", "select_blood_group": "Liste des réceptionnistes4", "update_profile": "Liste des réceptionnistes5"}, "appointments": {"appointment": "<PERSON><PERSON><PERSON>vous", "appointments": "<PERSON><PERSON><PERSON>vous", "description": "Description", "booked": "Réservé", "cancelled": "<PERSON><PERSON><PERSON>", "arrived": "<PERSON><PERSON><PERSON><PERSON>", "check_in": "Arrivée", "check_out": "<PERSON><PERSON><PERSON><PERSON>", "start": "D<PERSON>but", "join": "Rejoindre", "doc_required": "Rendez-vous0", "visit_type_required": "Rendez-vous1", "appointment_date": "Rendez-vous2", "appointment_date_required": "Rendez-vous3", "select_status": "<PERSON>dez-vous4", "status_required": "<PERSON>dez-vous5", "available_slot": "<PERSON>dez-vous6", "session": "Rendez-vous7", "no_time_slots_found": "<PERSON>dez-vous8", "time_slot_required": "<PERSON>dez-vous9", "appointment_details": "Description0", "appointment_type": "Description1", "completed": "Description2", "appointment_time": "Description3", "appointment_time_required": "Description4", "book_appointment": "Description5", "today_appointment": "Description6", "tomorrow_appointment": "Description7", "appointment_booking": "Description8", "available_appointments_on": "Description9", "appointment_visit_type_required": "Réservé0", "appointment_detail": "Réservé1", "save_appointment": "Réservé2", "appointment_list": "Réservé3"}, "clinic_schedule": {"schedule": "<PERSON><PERSON><PERSON>", "holiday_of": "<PERSON>ur f<PERSON><PERSON><PERSON>", "module_type_required": "Le type de module est requis", "schedule_date": "Date de planification", "schedule_date_required": "La date de planification est requise", "holiday_list": "Liste de jours fériés"}, "doctor_session": {"doc_sessions": "Availablity", "session_doc_required": "Session Doctor is required", "doc_already_added": "Le médecin sélectionné est déjà ajouté dans une autre session", "week_days": "Week days", "days_required": "Days is required", "days_already_exist": "Les jours sélectionnés existent déjà dans l'autre session", "morning_session": "Morning session", "start_time_required": "Start time is required", "start_time_smaller_then_end": "L'heure de début doit être inférieure à l'heure de fin", "end_time_required": "L'heure de fin est requise", "end_time_bigger_then_start": "Session Doctor is required0", "evening_session": "Session Doctor is required1", "start_time_smaller_then_first_session_end_time": "Session Doctor is required2", "set_session_for_doc": "Session Doctor is required3"}, "patient_encounter": {"encounters": "Rencontres", "encounter_dashboard": "Tableau de bord de rencontre", "is_required": "obligatoire", "note_prescription": "Remarque: tapez et appuyez sur Entrée pour créer une nouvelle prescription", "frequency": "<PERSON><PERSON><PERSON>", "frequency_required": "La fréquence est requise", "duration_Days": "<PERSON><PERSON><PERSON> (en jours)", "duration_required": "La durée est requise", "instruction": "Instruction", "duration": "<PERSON><PERSON><PERSON>", "no_prescription_found": "Tableau de bord de rencontre0", "add_prescription": "Tableau de bord de rencontre1", "encounter_date": "Tableau de bord de rencontre2", "encounter_date_required": "Tableau de bord de rencontre3", "encounter_module": "Tableau de bord de rencontre4", "prescription": "Tableau de bord de rencontre5", "encounter_details": "Tableau de bord de rencontre6"}, "medical_records": {"problem_type": "Type de problème", "problem_start_date_required": "La date de début du problème est requise", "problem_start_date": "Date de début du problème", "problem_end_date": "Date de fin du problème", "problem_outcome": "Résultat du problème", "medical_records": "Do<PERSON><PERSON> m<PERSON>", "add_medical_problems": "Ajouter des problèmes médicaux"}, "reports": {"reports": "Rapports", "filter_by": "Filtrer par", "clinic_revenue_overall": "Revenus de la clinique (global)", "clinic_revenue_detail": "<PERSON>enus de la clinique (détail)", "clinic_doctor_revenue": "Revenus des médecins de la clinique", "prescription_module": "Module de prescription", "report_required": "Un rapport est requis."}, "patient_front_widget": {"specialization": "Spécialisation", "username_email": "Nom d'utilisateur ou e-mail", "fill_form": "Veuillez remplir ce formulaire pour créer un compte.", "service": {"service_list": "Liste des services", "service_category": "Catégorie de service", "service_category_required": "La catégorie de service est requise", "note_category": "Remarque: tapez et appuyez sur Entrée pour ajouter une nouvelle catégorie", "service_name": "Nom du service", "service_name_required": "Le nom du service est requis", "service_validation": "La longueur du nom du service doit être comprise entre 2 et 100 caractères", "charges": "<PERSON><PERSON> ", "service_charge": "frais de service du module de service. ", "service_charges_required": "Des frais de service sont requis", "service_charge_length": "Catégorie de service0", "select_all": "Catégorie de service1"}, "patient_bill": {"invoice_id": "Identifiant de la facture:", "created_at": "<PERSON><PERSON><PERSON>:", "payment_status": "Statut du paiement:", "paid": "<PERSON><PERSON>", "unpaid": "Non payé", "patient_details": "<PERSON><PERSON><PERSON> du patient", "amount_due": "<PERSON><PERSON> dû", "print": "<PERSON><PERSON><PERSON><PERSON>", "service_required": "Le service est requis", "price_required": "Le prix est requis", "prize_greater_then_0": "<PERSON><PERSON>é <PERSON>:0", "prize_between_number": "Créé à:1", "quantity_required": "Créé à:2", "please_add_bill_items": "Créé à:3", "bill_total_required": "Créé à:4", "discount": "Créé à:5", "discount_amount": "C<PERSON>é à:6", "discount_required": "Créé à:7", "discount_greater_then_0": "Créé à:8", "discount_less_then_total_bill_amount": "Créé à:9", "payable_amount": "Statut du paiement:0", "bill_title": "Statut du paiement:1", "bill_title_required": "Statut du paiement:2", "bill_items": "Statut du paiement:3", "grand_total": "Statut du paiement:4", "grand_total_required": "Statut du paiement:5", "print_bill": "Statut du paiement:6", "billing_records": "Statut du paiement:7", "add_bill": "Statut du paiement:8", "patient_required": "Statut du paiement:9", "encounter_close": "Payé0", "bill_details": "Payé1", "other_info": "Payé2", "patients_encounter_list": "Payé3", "bills": "Payé4", "payment_setting": "Payé5", "woocommerce_payment_gateway": "Payé6", "amount": "Payé7", "items": "Payé8", "notes": "Payé9", "invoice_n_payment": "Non payé0", "currency": "Non payé1"}, "settings": {"general": "Général", "holidays": "Jours fériés", "configurations": "Configurations", "email_template": "Modèle d'e-mail", "sms_template": "<PERSON><PERSON><PERSON><PERSON>", "listings": "Listes", "custom_field": "<PERSON><PERSON>", "payment": "Paiement", "new_setting": "Nouveaux paramètres raffinés avec divers paramètres tels que l'e-mail, la facture, la devise, etc", "pro_settings": "Paramètres Pro", "language_settings": "Paramètres de langue"}, "pro_setting": {"set_site_logo": "Définir le logo du site", "set_language": "Définir la langue", "set_theme_color": "Définir la couleur du thème", "rtl_mode": "Mode RTL", "on": "activé", "twilo_sms_configration": "Twilo SMS Configration", "account_sid": "ACCOUNT SID", "auth_token": "AUTH TOKEN", "phone_number": "PHONE NUMBER (optional)", "twilo_sms_guide": "Twilo SMS guide", "twilio_step_1": "Définir la langue0", "twilo_sms_portal": "Définir la langue1", "twilio_step_2": "Définir la langue2", "get_console": "Définir la langue3", "unique_sid": "Définir la langue4", "twilio_step_3": "Définir la langue5", "twilio_step_4": "Définir la langue6", "head_on_console": "Définir la langue7", "phone_msg_sid": "Définir la langue8"}, "custom_field": {"label_name_required": "Le nom de l'étiquette est obligatoire", "label_name_validation": "Le nom de l'étiquette n'autorise que la valeur alphabétique", "where_it_look_like": "À quoi il ressemble", "shows_in_doc_creation_form": "Il apparaît dans le formulaire de création du médecin", "shows_in_patient_encounter_dashboard": "Il apparaît dans le tableau de bord de rencontre du patient", "shows_in_patient_creation_form": "Il apparaît dans le formulaire de création du patient", "filed_name": "Nom de fichier:", "invalid_label_name": " Nom d'étiquette non valide", "label_required": "L'étiquette est requise", "field_name_used": "Le nom du champ est déjà utilisé.", "input_type": "Le nom de l'étiquette n'autorise que la valeur alphabétique0", "input_type_required": "Le nom de l'étiquette n'autorise que la valeur alphabétique1", "placeholder": "Le nom de l'étiquette n'autorise que la valeur alphabétique2", "options": "Le nom de l'étiquette n'autorise que la valeur alphabétique3", "validation": "Le nom de l'étiquette n'autorise que la valeur alphabétique4", "mandatory_field": "Le nom de l'étiquette n'autorise que la valeur alphabétique5", "custom_field_list": "Le nom de l'étiquette n'autorise que la valeur alphabétique6", "add_custom_field": "Le nom de l'étiquette n'autorise que la valeur alphabétique7"}, "setup_wizard": {"previous": "Précédent", "add_session_details": "Ajouter les détails de la session", "session_doctors": "Médecins de session", "days": "Jours", "no_sessions_found": "Aucune session trouvée", "time_slot_minute": "<PERSON><PERSON> ho<PERSON> (en minutes)", "open_time": "Heure d'ouverture", "close_time": "Heure de fermeture", "session_demo": "Démo de session", "invalid_time_slot": "Plage horaire non valide trouvée. le créneau horaire non valide est", "doctor_list": "Ajouter les détails de la session0", "kivicare_ehr": "Ajouter les détails de la session1", "prev": "Ajouter les détails de la session2"}, "notification": {"notification": "Envoyer un e-mail de test", "test_sender_email_required": "Un e-mail de l'expéditeur de test est requis", "test_content": "Contenu du test", "test_content_required": "Un contenu de test est requis", "email_notification": "Activer / désactiver la notification par e-mail.", "forbidden_403": "403 | interdit"}, "static_data": {"listing_data": "<PERSON><PERSON><PERSON> de <PERSON>e", "terms_n_condition": "Te<PERSON><PERSON> et conditions", "version_update": "Mise à jour de la version (V2.0.0)", "new_filters_n_view": "Nouveaux filtres et affichage améliorés", "booking_widget_updated": "Le widget de réservation est mis à jour", "visit_type_replaced": "Le type de visite est remplacé par des services (veuillez vérifier l'onglet Service)", "appointment_flow_update": "Vérification des rendez-vous- flux d'entrée et de sortie mis à jour"}, "widgets": {"doc_not_found": "M<PERSON><PERSON>cin introuvable", "zoom_config": "Configuration du zoom", "terms_condition": "Conditions générales", "date_required": "La date est requise", "current_pwd": "Mot de passe actuel", "current_pwd_required": "Le mot de passe actuel est requis", "new_pwd": "Nouveau mot de passe", "appointment_info": "Informations sur le rendez-vous", "available_slots": "Emplacements disponibles", "service_detail": "Détails du service", "no_service_detail_found": "Configuration du zoom0", "book_now": "Configuration du zoom1", "registration_success": "Configuration du zoom2", "more_detail": "Configuration du zoom3", "username_email_required": "Configuration du zoom4", "new_pwd_required": "Configuration du zoom5", "confirm_pwd": "Configuration du zoom6", "confirm_pwd_required": "Configuration du zoom7", "pwd_validation": "Configuration du zoom8", "home": "Configuration du zoom9", "change_pwd": "Conditions générales0", "logging_out": "Conditions générales1", "total_visits": "Conditions générales2", "upcoming_visits": "Conditions générales3", "example_component": "Conditions générales4", "email_to_get_help_1": "Conditions générales5", "email_to_get_help_2": "Conditions générales6", "email_to_get_help_3": "Conditions générales7", "feedback_note": "Conditions générales8", "imp_version_update": "Conditions générales9", "replace_appointment": "La date est requise0", "option_as": "La date est requise1", "service_type": "La date est requise2", "add_charges": "La date est requise3", "manage_doctor": "La date est requise4", "send_test_email": "La date est requise5"}}}