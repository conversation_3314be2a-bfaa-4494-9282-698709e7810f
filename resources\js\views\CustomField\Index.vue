<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button @click="$router.back()"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="m12 19-7-7 7-7" />
            <path d="M19 12H5" />
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.custom_field.custom_field_list }}
          <a v-if="request_status == 'off'"
            href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#custom-fileds" target="_blank"
            class="ml-2 text-gray-400 hover:text-gray-600">
            <i class="fa fa-question-circle"></i>
          </a>
        </h1>
      </div>

      <div class="flex gap-2" v-if="kcCheckPermission('custom_field_add')">
        <!-- Import Modal Component -->
        <module-data-import v-if="
          userData.addOns.kiviPro &&
          kcCheckPermission('custom_field_add') &&
          kivicareCompareVersion(requireProVersion, userData.pro_version)
        " ref="module_data_import" @reloadList="getCustomFieldList" :required-data="[
          { label: formTranslation.common.module, value: 'module' },
          { label: formTranslation.common.label, value: 'label' },
          { label: formTranslation.common.input_type, value: 'input_type' },
          { label: formTranslation.common.options, value: 'options' },
        ]" :module-name="formTranslation.common.customField" module-type="customField" />

        <module-data-export :module-data="customFieldList.data"
          :module-name="formTranslation.custom_field.custom_field_list" module-type="custom_field" />
        <router-link :to="{ name: 'custom-field.create' }"
          class="px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800 flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <path d="M12 5v14M5 12h14" />
          </svg>
          <span>{{ formTranslation.custom_field.add_custom_field }}</span>
        </router-link>
      </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="space-y-6 mb-6">
      <!-- Search Bar -->
      <div class="relative">
        <svg xmlns="http://www.w3.org/2000/svg"
          class="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" viewBox="0 0 24 24"
          fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8" />
          <path d="m21 21-4.3-4.3" />
        </svg>
        <input type="text" v-model="serverParams.searchTerm" @input="handleSearch" :placeholder="formTranslation.common.search_custom_field_data_global_placeholder
          "
          class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
      </div>

      <!-- Filters Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <!-- ID Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            {{ formTranslation.common.id }}
          </label>
          <input type="text" v-model="serverParams.columnFilters.id" @input="handleColumnFilter"
            class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        </div>

        <!-- Input Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            {{ formTranslation.custom_field.input_type }}
          </label>
          <select v-model="serverParams.columnFilters.input_type" @change="handleColumnFilter"
            class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400">
            <option value="">{{ formTranslation.common.all }}</option>
            <option v-for="type in inputType" :key="type.value" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>

        <!-- Module Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            {{ formTranslation.custom_field.dt_lbl_type }}
          </label>
          <select v-model="serverParams.columnFilters.module_type" @change="handleColumnFilter"
            class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400">
            <option v-for="type in moduleType" :key="type.value" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            {{ formTranslation.service.dt_lbl_status }}
          </label>
          <select v-model="serverParams.columnFilters.status" @change="handleColumnFilter"
            class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400">
            <option value="">All Status</option>
            <option value="1">Active</option>
            <option value="0">Inactive</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <!-- Loading State -->
      <div v-if="pageLoader" class="min-h-[400px] flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>

      <template v-else>
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th v-for="column in customFieldList.column" :key="column.field"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ column.label }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="(row, index) in customFieldList.data" :key="row.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">{{ row.id }}</td>
              <td class="px-6 py-4">
                <div class="max-w-xs overflow-hidden">
                  <div class="truncate" :title="row.fields | fields">
                    {{ row.fields | fields }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ row.fields | input_type }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ row.module_type | typeFiled }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center gap-2">
                  <toggle-switch v-if="kcCheckPermission('custom_field_edit')"
                    :value="row.status === '1' ? 'on' : 'off'" @input="
                      (value) => {
                        row.status = value === 'on' ? '1' : '0';
                        changeModuleValueStatus({
                          module_type: 'custom_field',
                          id: row.id,
                          value: value === 'on' ? '1' : '0',
                        });
                      }
                    " on-value="on" off-value="off" />
                  <span v-if="row.status === '1'"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {{ formTranslation.common.active }}
                  </span>
                  <span v-else
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {{ formTranslation.common.inactive }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex gap-2">
                  <router-link v-if="kcCheckPermission('custom_field_edit')"
                    :to="{ name: 'custom-field.edit', params: { id: row.id } }" class="p-1 hover:bg-gray-100 rounded"
                    :title="formTranslation.common.edit">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-600" viewBox="0 0 24 24"
                      fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                    </svg>
                  </router-link>
                  <button v-if="kcCheckPermission('custom_field_delete')" @click="deleteCustomFieldData(index + 1)"
                    class="p-1 hover:bg-gray-100 rounded" :title="formTranslation.clinic_schedule.dt_lbl_dlt">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-red-500" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2">
                      <path d="M3 6h18" />
                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="customFieldList.data.length === 0">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                {{ formTranslation.common.no_data_found }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-700">Rows per page:</span>
            <select v-model="serverParams.perPage" @change="onPerPageChange"
              class="border border-gray-300 rounded-md text-sm p-1">
              <option :value="10">10</option>
              <option :value="25">25</option>
              <option :value="50">50</option>
            </select>
          </div>
          <div class="flex items-center gap-4">
            <span class="text-sm text-gray-700">
              Page {{ serverParams.page }} of
              {{ Math.ceil(totalRows / serverParams.perPage) }}
            </span>
            <div class="flex gap-2">
              <button @click="onPageChange({ currentPage: serverParams.page - 1 })" :disabled="serverParams.page === 1"
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <path d="m15 18-6-6 6-6" />
                </svg>
              </button>
              <button @click="onPageChange({ currentPage: serverParams.page + 1 })" :disabled="serverParams.page >=
                Math.ceil(totalRows / serverParams.perPage)
                " class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- Success Toast Notification -->
    <transition enter-active-class="transform ease-out duration-300 transition"
      enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
      enter-to-class="translate-y-0 opacity-100 sm:translate-x-0" leave-active-class="transition ease-in duration-100"
      leave-from-class="opacity-100" leave-to-class="opacity-0">
      <div v-if="showToast"
        class="fixed bottom-0 right-0 mb-4 mr-4 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
        <div class="p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-3 w-0 flex-1 pt-0.5">
              <p class="text-sm font-medium text-gray-900">
                {{ toastMessage }}
              </p>
            </div>
            <div class="ml-4 flex-shrink-0 flex">
              <button @click="showToast = false"
                class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <span class="sr-only">Close</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
  data: () => {
    return {
      pageLoader: true,
      customFieldList: {
        column: [],
        data: [],
      },
      showToast: false,
      customFieldRequest: {},
      serverParams: {
        columnFilters: {},
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      moduleType: [
        { value: "doctor_module", text: "Doctor module" },
        { value: "patient_module", text: "Patient module" },
        { value: "patient_encounter_module", text: "Patient encounter module" },
      ],
      request_status: "off",
      inputType: [
        { value: "text", text: "Text" },
        { value: "number", text: "Number" },
        { value: "textarea", text: "Textarea" },
        { value: "file_upload", text: "File Upload" },
        { value: "select", text: "Select" },
        { value: "multiselect", text: "Multi select" },
        { value: "radio", text: "Radio" },
        { value: "checkbox", text: "Checkbox" },
        { value: "calendar", text: "Calendar" },
      ],
    };
  },
  mounted() {
    if (["patient", "receptionist", "doctor"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.init();
    this.moduleType.unshift({
      value: "",
      text: this.formTranslation.common.all,
    });
    if (this.userData.addOns.kiviPro === true) {
      this.moduleType.push({
        value: "appointment_module",
        text: "Appointment module",
      });
    }
    this.getModule();
  },
  methods: {
    init: function () {
      this.customFieldList = this.defaultCustomFieldData();
      this.getCustomFieldList();
    },
    getCustomFieldList() {
      get("custom_field_list", this.serverParams)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            this.customFieldList.data = data.data.data;
            this.totalRows = data.data.total;
          } else {
            this.customFieldList.data = data.data.data;
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    updateParams: function (newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getCustomFieldList();
    },

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 300),
    onColumnFilter: _.debounce(function (params) {
      var emptyValue = true;
      var emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value, index, array) {
        if (value) {
          emptyValue = false;
        }
      });
      Object.values(this.oldServerParams.columnFilters).map(function (
        value,
        index,
        array
      ) {
        if (value) {
          emptyValue2 = false;
        }
      });
      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
          {},
          params.columnFilters
        );
        this.updateParams({
          columnFilters: params.columnFilters,
          perPage: this.serverParams.perPage,
          page: 1,
        });
      }
    }, 300),
    // Add these new methods for handling filters
    handleSearch: _.debounce(function () {
      this.serverParams.page = 1; // Reset to first page when searching
      this.getCustomFieldList();
    }, 300),

    handleColumnFilter: _.debounce(function () {
      this.serverParams.page = 1; // Reset to first page when filtering
      this.getCustomFieldList();
    }, 300),

    // Update the getCustomFieldList method to handle the response better
    async getCustomFieldList() {
      this.pageLoader = true;
      try {
        const response = await get("custom_field_list", this.serverParams);
        this.pageLoader = false;

        if (response.data.status) {
          this.customFieldList.data = response.data.data;
          this.totalRows = response.data.total;
        } else {
          this.customFieldList.data = [];
          this.totalRows = 0;
        }
      } catch (error) {
        console.error("Error fetching custom field list:", error);
        this.pageLoader = false;
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    // Update the defaultCustomFieldData method to match our table structure
    defaultCustomFieldData() {
      return {
        data: [],
        column: [
          {
            field: "id",
            label: this.formTranslation.common.id,
          },
          {
            field: "fields",
            label: this.formTranslation.custom_field.dt_lbl_field,
          },
          {
            field: "input_type",
            label: this.formTranslation.custom_field.input_type,
          },
          {
            field: "module_type",
            label: this.formTranslation.custom_field.dt_lbl_type,
          },
          {
            field: "status",
            label: this.formTranslation.service.dt_lbl_status,
          },
          {
            field: "actions",
            label: this.formTranslation.custom_field.dt_lbl_action,
          },
        ],
      };
    },
    onPageChange(params) {
      this.serverParams.page = params.currentPage;
      this.getCustomFieldList();
    },

    onPerPageChange(params) {
      if (this.serverParams.perPage === params.currentPerPage) return;

      this.serverParams.perPage = params.currentPerPage;
      this.serverParams.page = 1;
      this.getCustomFieldList();
    },
    deleteCustomFieldData: function (index) {
      if (this.customFieldList.data[index - 1] !== undefined) {
        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.py_delete_field,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: "Yes",
            cancelButtonText: "Cancel",
          })
          .then((result) => {
            if (result.isConfirmed) {
              // Show loading state
              this.$swal.fire({
                title: "Deleting...",
                text: "Please wait while we process your request",
                allowOutsideClick: false,
                didOpen: () => {
                  this.$swal.showLoading();
                },
              });

              get("custom_field_delete", {
                id: this.customFieldList.data[index - 1].id,
              })
                .then((data) => {
                  if (
                    data.data.status !== undefined &&
                    data.data.status === true
                  ) {
                    // Remove the item from the list
                    this.customFieldList.data.splice(index - 1, 1);

                    // Show success message
                    this.$swal.fire({
                      icon: "success",
                      title: "Deleted",
                      text: data.data.message,
                      showConfirmButton: false,
                      timer: 1500,
                    });
                  } else {
                    // Show error if status is not true
                    this.$swal.fire({
                      icon: "error",
                      title: "Error",
                      text: data.data.message || "Something went wrong",
                      showConfirmButton: true,
                    });
                  }
                })
                .catch((error) => {
                  // Handle error scenarios
                  const errorMessage =
                    error.response?.data?.message ||
                    this.formTranslation.common.internal_server_error;

                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: errorMessage,
                    showConfirmButton: true,
                  });
                });
            }
          });
      }
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },
  computed: {
    customFieldListExport() {
      return "Custom Field List - " + moment().format("YYYY-MM-DD");
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    // formTranslation: function () {
    //   return this.$store.state.staticDataModule.langTranslateData ;
    // }
  },
  filters: {
    fields: function (value) {
      let label = JSON.parse(value);
      let lableValue = label;
      if (label !== "" && label !== null) {
        if (label !== undefined && label["label"] !== undefined) {
          lableValue = label["label"];
          // lableValue = lableValue.replace(/_/gi, ' ');
        }
      }
      return lableValue;
    },
    input_type: function (value) {
      let type = JSON.parse(value);
      let typeValue = type;
      if (type !== "" && type !== null) {
        if (type !== undefined && type["type"] !== undefined) {
          typeValue = type["type"];
        }
      }
      return typeValue;
    },
    typeFiled: function (value) {
      let label = value;
      let lableValue = "";
      if (label !== "" && label !== null) {
        if (label !== undefined && label !== "") {
          lableValue = label;
          lableValue = lableValue.replace(/_/gi, " ");
        }
      }
      return lableValue;
    },
  },
};
</script>
