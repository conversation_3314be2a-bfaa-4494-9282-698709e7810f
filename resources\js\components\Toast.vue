<!-- Toast.vue -->
<template>
    <transition
      enter-active-class="transform ease-out duration-300 transition"
      enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
      enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
      leave-active-class="transition ease-in duration-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="show" class="fixed top-4 right-4 z-50">
        <div :class="[
          'rounded-lg p-4 pointer-events-auto shadow-lg border',
          variant === 'success' ? 'bg-white border-green-100' : '',
          variant === 'error' ? 'bg-white border-red-100' : '',
          variant === 'warning' ? 'bg-white border-yellow-100' : '',
          variant === 'info' ? 'bg-white border-blue-100' : '',
        ]">
          <div class="flex items-center gap-3">
            <!-- Success Icon -->
            <div v-if="variant === 'success'" class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <!-- Error Icon -->
            <div v-if="variant === 'error'" class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <!-- Warning Icon -->
            <div v-if="variant === 'warning'" class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <!-- Info Icon -->
            <div v-if="variant === 'info'" class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            
            <div class="ml-3 w-0 flex-1">
              <p class="text-sm font-medium text-gray-900">{{ title }}</p>
              <p v-if="message" class="mt-1 text-sm text-gray-500">{{ message }}</p>
            </div>
            
            <div class="ml-4 flex flex-shrink-0">
              <button
                @click="$emit('close')"
                class="inline-flex rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <span class="sr-only">Close</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </template>
  
  <script>
  export default {
    name: 'Toast',
    props: {
      show: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        required: true
      },
      message: {
        type: String,
        default: ''
      },
      variant: {
        type: String,
        default: 'success',
        validator: function(value) {
          return ['success', 'error', 'warning', 'info'].indexOf(value) !== -1
        }
      },
      duration: {
        type: Number,
        default: 3000
      }
    },
    watch: {
      show(newValue) {
        if (newValue && this.duration) {
          setTimeout(() => {
            this.$emit('close')
          }, this.duration)
        }
      }
    },
    emits: ['close']
  }
  </script>