<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div
      v-if="isAppointmentLoading"
      class="flex justify-center items-center min-h-screen"
    >
      <loader-component-2 />
    </div>

    <div v-else class="py-6">
      <div v-if="appointmentList.length > 0" class="space-y-4">
        <div
          v-for="(appointment, index) in appointmentList"
          :key="index"
          class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
        >
          <div class="p-4">
            <div
              class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
            >
              <!-- Patient & Time Info -->
              <div class="flex-1">
                <h3 class="text-lg font-medium text-blue-600">
                  {{ appointment.patient_name }}
                  <span class="text-sm font-normal text-gray-500 ml-2">
                    ({{ appointment.appointment_start_time }} -
                    {{ appointment.appointment_end_time }})
                  </span>
                </h3>
                <p class="text-sm text-gray-700">
                  {{ formTranslation.common.doctor }}:
                  <span class="text-blue-600">{{
                    appointment.doctor_name
                  }}</span>
                </p>
                <p
                  v-if="getUserRole() === 'administrator'"
                  class="text-sm text-gray-700"
                >
                  {{ formTranslation.clinic.clinic }}:
                  <span class="text-blue-600">{{
                    appointment.clinic_name
                  }}</span>
                </p>
                <p
                  v-if="enableDisableAppointmentDescriptionStatus == 'on'"
                  class="text-sm text-gray-600 mt-1"
                >
                  <span class="font-medium"
                    >{{ formTranslation.appointments.description }}:</span
                  >
                  {{ appointment.description || "not found" }}
                </p>
              </div>

              <!-- Service Type -->
              <div class="text-sm text-gray-600 font-medium">
                {{ appointment.type_label }}
              </div>

              <!-- Status Badge -->
              <div>
                <span
                  :class="{
                    'px-2 py-1 text-xs font-medium rounded-full': true,
                    'bg-blue-100 text-blue-800': appointment.status === '1',
                    'bg-red-100 text-red-800': appointment.status === '0',
                    'bg-green-100 text-green-800': appointment.status === '2',
                    'bg-emerald-100 text-emerald-800':
                      appointment.status === '4',
                    'bg-rose-100 text-rose-800': appointment.status === '3',
                  }"
                >
                  {{ getStatusText(appointment.status) }}
                </span>
              </div>

              <!-- Action Buttons -->
              <div class="flex gap-2">
                <button
                  v-if="appointment.encounter_id !== null"
                  class="inline-flex items-center px-3 py-1.5 text-sm border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50"
                  :to="{
                    name: 'patient-encounter.dashboard',
                    params: { encounter_id: appointment.encounter_id },
                  }"
                >
                  <i class="fa fa-tachometer-alt mr-1"></i>
                  Details
                </button>

                <button
                  v-if="canCheckIn(appointment)"
                  @click="handleAppointmentStatus(appointment, '4')"
                  class="inline-flex items-center px-3 py-1.5 text-sm border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50"
                >
                  <i class="fa fa-sign-in-alt mr-1"></i>
                  Check In
                </button>

                <button
                  v-if="canCheckOut(appointment)"
                  @click="handleAppointmentStatus(appointment, '3')"
                  class="inline-flex items-center px-3 py-1.5 text-sm border border-red-600 text-red-600 rounded-md hover:bg-red-50"
                >
                  <i class="fa fa-sign-out-alt mr-1"></i>
                  Check Out
                </button>

                <button
                  v-if="canDelete(appointment)"
                  @click="handleAppointmentDelete(appointment)"
                  class="inline-flex items-center px-3 py-1.5 text-sm border border-red-600 text-red-600 rounded-md hover:bg-red-50"
                >
                  <i class="fa fa-trash mr-1"></i>
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="flex justify-center items-center min-h-[350px]">
        <p class="text-lg text-gray-500">
          {{ formTranslation.common.no_appointments }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
export default {
  props: {
    isLoading: {
      type: [Boolean],
    },
  },
  name: "AppointmentList",
  data: () => {
    return {
      appointmentRequest: {},
      appointmentData: [],
      isAppointmentLoading: true,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init: function () {
      this.todayAppointmentList();
      this.appointmentRequest = this.defaultAppointmentRequest();
    },
    defaultAppointmentRequest: function () {
      return {
        date: new Date(),
      };
    },
    todayAppointmentList: function () {
      this.$store.dispatch("userDataModule/fetchUserForDropdown", {
        userRoleName: this.patientRoleName,
      });
      let filterData = Object.assign({}, this.appointmentRequest);
      filterData.date = moment(this.appointmentRequest.date).format(
        "YYYY-MM-DD"
      );
      this.$store
        .dispatch("appointmentModule/fetchAppointmentData", {
          filterData: filterData,
        })
        .then(() => {
          this.appointmentData =
            this.$store.state.appointmentModule.appointmentList;
        });
      setTimeout(() => {
        this.isAppointmentLoading = false;
      }, 500);
    },
    handleAppointmentEdit: function (appointment, collapseID) {
      this.editAppointment = true;
      this.$root.$emit("bv::toggle::collapse", collapseID);
      this.$store.commit("TOGGLE_APPOINTMENT_FORM", false);
      this.editId = appointment.id;
      setTimeout(() => {
        this.appointmentFormObj = Object.assign({}, appointment);
      }, 200);
    },
    handleAppointmentDelete: function (appointment) {
      if (appointment.id !== undefined) {
        let but = $("#appointment_delete_" + appointment.id);
        but.prop("disabled", true);
        but.html(`<i class='fa fa-sync fa-spin'> </i>`);
        $.confirm({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          content: this.formTranslation.common.press_yes_delete_billitems,
          type: "red",
          buttons: {
            ok: {
              text: this.formTranslation.common.yes,
              btnClass: "btn-danger",
              keys: ["enter"],
              action: () => {
                get("appointment_delete", {
                  id: appointment.id,
                })
                  .then((data) => {
                    but.prop("disabled", false);
                    but.html(`<i class='fa fa-trash'> </i>`);
                    if (
                      data.data.status !== undefined &&
                      data.data.status === true
                    ) {
                      displayMessage(data.data.message);
                      this.reloadAppointment();
                      this.$emit("refreshDashboard");
                    }
                  })
                  .catch((error) => {
                    but.prop("disabled", false);
                    but.html(`<i class='fa fa-trash'> </i>`);
                    console.log(error);
                    displayErrorMessage(
                      this.formTranslation.common.internal_server_error
                    );
                  });
              },
            },
            cancel: {
              text: this.formTranslation.common.cancel,
              action: () => {
                but.prop("disabled", false);
                but.html(`<i class='fa fa-trash'> </i>`);
              },
            },
          },
        });
      }
    },
    handleAppointmentStatus: function (appointment, status) {
      if (status === "3") {
        if (
          appointment.encounter_id !== null &&
          appointment.encounter_detail !== undefined &&
          [1, "1"].includes(appointment.encounter_detail.status)
        ) {
          displayErrorMessage(this.formTranslation.common.encounter_not_close);
          return;
        }
      }
      var element = $("#status_update_" + appointment.id).find("i");
      $("#status_update_" + appointment.id).prop("disabled", true);
      if (status === "4") {
        element.removeClass("fa fa-sign-in-alt");
      } else {
        element.removeClass("fa fa-sign-out-alt");
      }
      element.addClass("fa fa-spinner fa-spin");
      $.confirm({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        content: this.formTranslation.common.update_appointment_status,
        type: "green",
        buttons: {
          ok: {
            text: this.formTranslation.common.yes,
            btnClass: "btn-primary",
            keys: ["enter"],
            action: () => {
              get("appointment_update_status", {
                appointment_id: appointment.id,
                appointment_status: status,
              })
                .then((response) => {
                  $("#status_update_" + appointment.id).prop("disabled", false);
                  element.removeClass("fa fa-spinner fa-spin");
                  if (status === "4") {
                    element.addClass("fa fa-sign-in-alt");
                  } else {
                    element.addClass("fa fa-sign-out-alt");
                  }
                  if (
                    response.data.status !== undefined &&
                    response.data.status === true
                  ) {
                    this.reloadAppointment();
                    displayMessage(response.data.message);
                  } else {
                    displayErrorMessage(response.data.message);
                  }
                })
                .catch((error) => {
                  $("#status_update_" + appointment.id).prop("disabled", false);
                  element.removeClass("fa fa-spinner fa-spin");
                  if (status === "4") {
                    element.addClass("fa fa-sign-in-alt");
                  } else {
                    element.addClass("fa fa-sign-out-alt");
                  }
                  console.log(error);
                  displayErrorMessage(
                    this.formTranslation.common.internal_server_error
                  );
                });
            },
          },
          cancel: {
            text: this.formTranslation.common.cancel,
            action: () => {
              $("#status_update_" + appointment.id).prop("disabled", false);
              element.removeClass("fa fa-spinner fa-spin");
              if (status === "4") {
                element.addClass("fa fa-sign-in-alt");
              } else {
                element.addClass("fa fa-sign-out-alt");
              }
            },
          },
        },
      });
    },
    reloadAppointment: function () {
      // let filterData = Object.assign({}, this.appointmentRequest);
      // filterData.date = moment(this.appointmentRequest.date).format('YYYY-MM-DD');
      setTimeout(() => {
        this.$store.dispatch("appointmentModule/fetchAppointmentData", {
          filterData: { date: moment(new Date()).format("YYYY-MM-DD") },
        });
      }, 300);
    },
    refreshAppointment: function () {
      this.isAppointmentLoading = true;
      get("get_appointment_queue", {
        filterData: { date: moment(new Date()).format("YYYY-MM-DD") },
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit(
              "appointmentModule/FETCH_APPOINTMENT_DATA",
              response.data.data
            );
            this.$emit("isReloadTrue", false);
          }
        })
        .catch((error) => {
          this.isAppointmentLoading = false;
          console.log(error);
        });
    },
    getStatusText(status) {
      const statusMap = {
        0: this.formTranslation.appointments.cancelled,
        1: this.formTranslation.appointments.booked,
        2: this.formTranslation.appointments.pending,
        3: this.formTranslation.appointments.check_out,
        4: this.formTranslation.appointments.check_in,
      };
      return statusMap[status] || status;
    },
    canCheckIn(appointment) {
      return (
        this.kcCheckPermission("patient_appointment_status_change") &&
        !["3", "4", "0", "2"].includes(appointment.status) &&
        this.currentDate === appointment.appointment_end_date
      );
    },
    canCheckOut(appointment) {
      return (
        this.kcCheckPermission("patient_appointment_status_change") &&
        appointment.status === "4" &&
        this.currentDate === appointment.appointment_end_date
      );
    },
    canDelete(appointment) {
      return this.kcCheckPermission("appointment_delete");
    },
  },
  computed: {
    appointmentList: function () {
      return this.$store.state.appointmentModule.appointmentList;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    enableDisableAppointmentDescriptionStatus() {
      return this.$store.state.appointmentModule.description_status;
    },
  },
  watch: {
    isLoading: function (value) {
      if (value) {
        this.isAppointmentLoading = true;
        // this.refreshAppointment();
      } else {
        this.isAppointmentLoading = false;
      }
    },
    appointmentList: function () {
      this.$emit("csvData", this.appointmentList);
    },
  },
};
</script>
