<template>
  <b-row>
    <b-col sm="12">
      <b-overlay :show="!(userData?.addOns?.webhooks)" variant="white" :opacity="overlayOpacity">
        <template #overlay>
          <overlay-message :addon_type="name"></overlay-message>
        </template>
        <b-card class="p-0 shadow" body-class="p-0" header-tag="header" footer-tag="footer">
          <template v-slot:header>
            <b-row>
              <b-col sm="12" md="8" lg="8">
                <h3 class="mb-0">
                  {{ (webhook_data?.name ||  '' ) + ' ' + (formTranslation.webhooks?.webhooks_log_list || "webhook logs list") }}
                </h3>
              </b-col>
              <b-col sm="12" md="4" lg="4">
                <div class="d-flex justify-content-end">
                  <router-link class="btn btn-sm btn-primary" :to="{ name: `${name}` }">
                    <i class="fa fa-angle-double-left"></i>
                    {{ formTranslation.common.back }}
                  </router-link>
                </div>
              </b-col>
            </b-row>
          </template>
          <ModalPopup
              v-if="openModal"
              modalId="webhook_log_data"
              modalSize="lg"
              :openModal="openModal"
              :modalTitle="webhook_data?.name + (formTranslation.webhooks?.log_entry)"
              @closeModal="openModal = false; modalData = {}"
          >
            <div class="log-entry">
              <h3 class="text-center">Webhook details</h3>
              <div class="row">
                <div class="col-md-6">
                  <p class="mb-0"><span class="font-weight-bold">{{ $t('webhooks.id') }}:</span> {{ modalData.id || $t('webhooks.n_a') }}</p>
                </div>
                <div class="col-md-6">
                  <p class="mb-0"><span class="font-weight-bold">{{ $t('webhooks.module_id') }}:</span> {{ modalData.module_id || $t('webhooks.n_a') }}</p>
                </div>
                <div class="col-md-6">
                  <p class="mb-0"><span class="font-weight-bold">{{$t('webhooks.webhooks_id')}}:</span> {{ modalData.webhook_id || $t('webhooks.n_a') }}</p>
                </div>
                <div class="col-md-6">
                  <p class="mb-0"><span class="font-weight-bold">{{ $t('webhooks.created_at') }}:</span> {{ modalData.created_at || $t('webhooks.n_a') }}</p>
                </div>
              </div>
              <div v-if="modalData?.log_data?.request">
                <hr>
                <h3 class="text-center">{{ $t('webhooks.request') }}</h3>
                <div class="row">
                  <div class="col-12">
                    <vue-json-editor v-model="modalData.log_data.request" :showBtns="false" mode="code" :expandedOnStart="true" ></vue-json-editor>
                  </div>
                </div>
              </div>
              <div v-if="modalData?.log_data?.response">
                <hr>
                <h3 class="text-center">{{ $t('webhooks.response') }}</h3>
                <div class="row">
                  <div class="col-12">
                    <vue-json-editor v-model="modalData.log_data.response" :showBtns="false" mode="code" :expandedOnStart="true" ></vue-json-editor>
                  </div>
                </div>
              </div>
            </div>
          </ModalPopup>
          <b-row>
            <b-col sm="12" md="12" lg="12">
              <div v-show="isColumnLoading || isRowLoading">
                <loader-component-2></loader-component-2>
              </div>
              <div :id="name + 'Print'">
                <vue-good-table ref="dataTable" :columns="column" :rows="rows"
                                mode="remote" :search-options="{
                    enabled: true,
                    placeholder:
                      formTranslation.common.search_webhooks_log_data_global_placeholder,
                  }" @on-sort-change="onSortChange"
                                @on-column-filter="onColumnFilter"
                                @on-page-change="onPageChange" @on-per-page-change="onPerPageChange" :totalRows="totalRows"
                                :pagination-options="{
                      enabled: true,
                      mode: 'pages',
                    }" @on-search="globalFilter" @on-selected-rows-change="(selected_row) => {
                      globalCheckboxApplyData.data = selected_row;
                    }" :select-options="{
                      enabled: column,
                      selectOnCheckboxOnly: true, // only select when checkbox is clicked instead of the row
                      selectionInfoClass: 'text-primary bg-white',
                      selectionText: formTranslation.common.rows_selected,
                      clearSelectionText: formTranslation.common.clear,
                      disableSelectInfo: false, // disable the select info panel on top
                      selectAllByGroup: true, // when used in combination with a grouped table, add a checkbox in the header row to check/uncheck the entire group
                    }" styleClass="vgt-table striped" compactMode>
                  <div slot="emptystate" class="text-danger text-center">
                    {{ isColumnLoading || isRowLoading ? formTranslation.webhooks.loading : formTranslation.common.no_data_found }}
                  </div>
                  <div slot="selected-row-actions">
                    <div class="d-flex justify-content-end align-items-center">
                      <select class="form-control form-control-sm" v-model="globalCheckboxApplyData.action_perform">
                        <option v-for="(
                            option
                          ) in globalCheckboxApplyDataActions" :value="option.value">
                          {{ option.label }}
                        </option>
                      </select>
                      <button class="ml-2 btn btn-sm btn-primary" @click="(params) => helperGlobalAction(params,getTableData)">
                        {{ formTranslation.common.apply }}
                      </button>
                    </div>
                  </div>
                  <template slot="table-row" slot-scope="props">
                    <div class="btn-group" v-if="props.column.field === 'actions'">
                      <button class="btn btn-outline-primary btn-sm"  v-b-tooltip.hover :title="formTranslation.common.view"
                              @click="viewLogData(props.row)">
                        <i class="fa fa-eye"></i>
                      </button>
                      <button class="btn btn-outline-danger btn-sm" :id="delete_ele + props.row.id" v-b-tooltip.hover :title="formTranslation.clinic_schedule.dt_lbl_dlt"
                              @click="deleteData(props.row.id)">
                        <i class="fa fa-trash"></i>
                      </button>
                    </div>
                    <div v-else>
                      {{ props.formattedRow[props.column.field] }}
                    </div>
                  </template>
                </vue-good-table>
              </div>
            </b-col>
          </b-row>
        </b-card>
      </b-overlay>
    </b-col>
  </b-row>
</template>
<script>
import {
  helperModuleTableData
} from "../../utils/list";
import * as globalAction from "../../utils/globalAction";
import { globalDeleteModuleData } from "../../utils/delete";
import ModalPopup from "../../components/Modal/Index.vue";
import {mapActions} from "vuex";
import vueJsonEditor from 'vue-json-editor'

export default {
  components: {
    ModalPopup,
    vueJsonEditor
  },
  data: () => {
    return {
      name: 'webhooks',
      column: [],
      rows: [],
      totalRows: 0,
      isRowLoading: false,
      isColumnLoading: false,
      serverParams: {
        columnFilters: {
        },
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
        type: "list",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      deleteRowId: -1,
      apiEndpoint: {
        list: 'webhooks_log_list',
        column: 'webhooks_log_column',
        delete: 'webhooks_log_delete',
      },
      delete_ele: 'webhook_log_delete_',
      globalCheckboxApplyData: {},
      globalCheckboxApplyDataActions: [],
      webhook_id:-1,
      webhook_data:{},
      openModal:false,
      modalData:{}
    };
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    if (this?.userData?.addOns?.webhooks && this.$route.params.webhook_id) {
      this.webhook_id = this.$route.params.webhook_id;
      this.init();
    }
  },
  methods: {
    ...globalAction,
    init() {
      this.getTableColumn();
      this.getTableData();
      this.globalCheckboxApplyData = this.helperDefaultGlobalCheckboxApplyData(this.name);
      this.globalCheckboxApplyDataActions = this.helperDefaultGlobalCheckboxApplyDataActions();
    },
    async getTableData() {
      this.isRowLoading = true;
      this.serverParams.webhook_id = this.webhook_id;
      const data = await helperModuleTableData(this.apiEndpoint.list,this.serverParams)
      this.webhook_data = data?.response?.webhook_data || {};
      this.rows = data.rows || [];
      this.totalRows = data.totalRows || 0;
      this.isRowLoading = false;
    },
    deleteData(id) {
      const options = {
        id: id,
        delete_ele:this.delete_ele,
        content_message:this.formTranslation.webhooks.delete_webhooks,
        endpoint:this.apiEndpoint.delete
      }
      const successCallback = () => {
        this.getTableData();
      }
      const failedCallback = () => {
        this.deleteRowId = -1;
      }
      globalDeleteModuleData(options,successCallback,failedCallback);
    },
    updateParamsAndTable(newProps){
      this.serverParams = Object.assign(
          {},
          this.serverParams,
          newProps
      );
      this.getTableData();
    },
    onPageChange(params) {
      this.updateParamsAndTable({
        page: params.currentPage,
      });
    },
    onPerPageChange (params)  {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParamsAndTable( {
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },
    onSortChange:(params) => {
      this.updateParamsAndTable({
        sort: params,
      });
    },
    globalFilter:_.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParamsAndTable({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 500),
    onColumnFilter: _.debounce(function (params) {
      console.log(params);
      let emptyValue = true;
      let emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value) {
        if (value) {
          emptyValue = false;
        }
      });
      Object.values(this.oldServerParams.columnFilters).map(function (
          value
      ) {
        if (value) {
          emptyValue2 = false;
        }
      });
      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
            {},
            params.columnFilters
        );
        this.updateParamsAndTable({
          columnFilters: params.columnFilters,
          perPage: this.serverParams.perPage,
          page: 1,
        });
      }
    }, 300),
    viewLogData(data){
      this.modalData = data;
      this.openModal = true;
    },
    formatLogValue(value) {
      if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value)) {
          return value
              .map(item => `<div>${this.formatLogValue(item)}</div>`)
              .join('');
        } else {
          return `<pre>${JSON.stringify(value, null, 2)}</pre>`;
        }
      }
      return value;
    },
    ...mapActions('tableModule', ["fetchTableColumns"]),
    async getTableColumn() {
      this.isColumnLoading = true
      const columns = await this.fetchTableColumns({
        endpoint: this.apiEndpoint.column,
        module: 'webhooks_log'
      });
      if(columns){
        this.column = Object.values(columns);
      }
      this.isColumnLoading = false
    }
  },
  computed: {
    userData() {
      return this?.$store?.state?.userDataModule && this?.$store?.state?.userDataModule?.user
          ? this.$store.state.userDataModule.user
          : [];
    }
  },
};
</script>
