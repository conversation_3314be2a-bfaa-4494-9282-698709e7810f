<template>
    <div class="w-full px-4">
        <div class="grid grid-cols-1 gap-6">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                        <h4 class="font-medium text-lg text-gray-800">{{ $t('TDL Lab Test Results') }}</h4>
                        <div>
                            <router-link to="/tdl-import-results" class="inline-flex items-center px-3 py-1 text-sm border border-blue-600 rounded-md text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fa fa-file-import mr-1"></i>
                                {{ $t('Import Results') }}
                            </router-link>
                            <button 
                                @click="refreshResults" 
                                class="ml-2 inline-flex items-center px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                :disabled="refreshing"
                            >
                                <i :class="{'fa fa-sync mr-1': true, 'fa-spin': refreshing}"></i>
                                {{ $t('Refresh') }}
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Filters -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                            <div>
                                <div class="mb-4">
                                    <label for="patient_filter" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Patient') }}</label>
                                    <select
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="patient_filter"
                                        v-model="filters.patient_id"
                                        @change="applyFilters"
                                    >
                                        <option value="">{{ $t('All Patients') }}</option>
                                        <option v-for="patient in patients" :key="patient.id" :value="patient.id">
                                            {{ patient.first_name }} {{ patient.last_name }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <div class="mb-4">
                                    <label for="status_filter" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Status') }}</label>
                                    <select
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="status_filter"
                                        v-model="filters.result_status"
                                        @change="applyFilters"
                                    >
                                        <option value="">{{ $t('All Statuses') }}</option>
                                        <option value="received">{{ $t('Received') }}</option>
                                        <option value="reviewed">{{ $t('Reviewed') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <div class="mb-4">
                                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Date From') }}</label>
                                    <input
                                        type="date"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="date_from"
                                        v-model="filters.date_from"
                                        @change="applyFilters"
                                    />
                                </div>
                            </div>
                            <div>
                                <div class="mb-4">
                                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Date To') }}</label>
                                    <input
                                        type="date"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        id="date_to"
                                        v-model="filters.date_to"
                                        @change="applyFilters"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Results Table -->
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
                            <span class="ml-2">{{ $t('Loading...') }}</span>
                        </div>
                        <div v-else-if="results.length === 0" class="p-4 border rounded-md border-blue-200 bg-blue-50 text-blue-700 text-sm">
                            {{ $t('No test results found matching your criteria.') }}
                        </div>
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Order Number') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Result Date') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Patient') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Tests') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('Status') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">{{ $t('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="result in results" :key="result.id" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ result.order_number || 'N/A' }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatDate(result.result_date) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ result.patient_name }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                                                {{ result.tests_count }} {{ $t('tests') }}
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ result.biomarkers_count }} {{ $t('biomarkers') }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="px-2 py-1 text-xs font-medium rounded-full"
                                                :class="{
                                                    'bg-blue-100 text-blue-800': result.result_status === 'received',
                                                    'bg-green-100 text-green-800': result.result_status === 'reviewed'
                                                }"
                                            >
                                                {{ result.result_status === 'received' ? $t('Received') : $t('Reviewed') }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <router-link
                                                :to="'/tdl-result/' + result.id"
                                                class="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                                title="View Details"
                                            >
                                                <i class="fa fa-eye"></i>
                                            </router-link>
                                            
                                            <button
                                                v-if="result.result_status === 'received'"
                                                @click="markAsReviewed(result.id)"
                                                class="ml-1 inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                                title="Mark as Reviewed"
                                            >
                                                <i class="fa fa-check"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div v-if="totalPages > 1" class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-700">
                                {{ $t('Showing {start} to {end} of {total} results', {
                                    start: (currentPage - 1) * perPage + 1,
                                    end: Math.min(currentPage * perPage, total),
                                    total: total
                                }) }}
                            </div>
                            <nav aria-label="Page navigation">
                                <ul class="flex justify-center space-x-1">
                                    <li>
                                        <button
                                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                            :disabled="currentPage === 1"
                                            @click.prevent="changePage(currentPage - 1)"
                                        >
                                            {{ $t('Previous') }}
                                        </button>
                                    </li>
                                    <li v-for="page in pageNumbers" :key="page">
                                        <button
                                            class="relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                            :class="page === currentPage ? 'bg-blue-50 border-blue-500 text-blue-600 z-10' : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'"
                                            @click.prevent="changePage(page)"
                                        >
                                            {{ page }}
                                        </button>
                                    </li>
                                    <li>
                                        <button
                                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                            :disabled="currentPage === totalPages"
                                            @click.prevent="changePage(currentPage + 1)"
                                        >
                                            {{ $t('Next') }}
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
    name: 'TDLResults',
    data() {
        return {
            loading: true,
            refreshing: false,
            results: [],
            patients: [],
            currentPage: 1,
            perPage: 25,
            total: 0,
            totalPages: 0,
            filters: {
                patient_id: '',
                result_status: '',
                date_from: '',
                date_to: ''
            },
            processingIds: [] // Track which results are being processed
        };
    },
    computed: {
        pageNumbers() {
            const range = [];
            const showPages = 5;
            const halfShow = Math.floor(showPages / 2);
            
            let start = this.currentPage - halfShow;
            if (start < 1) start = 1;
            
            let end = start + showPages - 1;
            if (end > this.totalPages) {
                end = this.totalPages;
                start = Math.max(1, end - showPages + 1);
            }
            
            for (let i = start; i <= end; i++) {
                range.push(i);
            }
            
            return range;
        },
        currentLocale() {
            return this.$i18n.locale || 'en';
        }
    },
    created() {
        this.loadPatients();
        this.fetchResults();
    },
    methods: {
        loadPatients() {
            get('patient_list')
                .then(response => {
                    if (response.data.status === true) {
                        this.patients = response.data.data || [];
                    } else {
                        console.error('Error loading patients:', response.data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading patients:', error);
                });
        },
        fetchResults() {
            this.loading = true;
            
            const params = {
                page: this.currentPage,
                per_page: this.perPage,
                patient_id: this.filters.patient_id || '',
                result_status: this.filters.result_status || '',
                date_from: this.filters.date_from ? `${this.filters.date_from} 00:00:00` : '',
                date_to: this.filters.date_to ? `${this.filters.date_to} 23:59:59` : ''
            };
            
            get('tdl_get_test_results', params)
                .then(response => {
                    this.loading = false;
                    
                    if (response.data.status === true) {
                        this.results = response.data.data.results || [];
                        this.total = response.data.data.total || 0;
                        this.totalPages = response.data.data.total_pages || 0;
                        this.currentPage = response.data.data.current_page || 1;
                    } else {
                        this.results = [];
                        this.total = 0;
                        this.totalPages = 0;
                        
                        displayErrorMessage(response.data.message || this.$t('Failed to load test results.'));
                    }
                })
                .catch(error => {
                    this.loading = false;
                    console.error('Error fetching results:', error);
                    
                    displayErrorMessage(this.$t('Failed to load test results.'));
                });
        },
        formatDate(dateString) {
            if (!dateString) return 'N/A';
            
            try {
                const options = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                
                return new Date(dateString).toLocaleDateString(this.currentLocale, options);
            } catch (error) {
                console.error('Error formatting date:', error);
                return dateString || 'N/A';
            }
        },
        applyFilters() {
            this.currentPage = 1;
            this.fetchResults();
        },
        changePage(page) {
            if (page < 1 || page > this.totalPages) return;
            
            this.currentPage = page;
            this.fetchResults();
        },
        refreshResults() {
            if (this.refreshing) return;
            
            this.refreshing = true;
            
            get('tdl_refresh_test_results')
                .then(response => {
                    this.refreshing = false;
                    
                    if (response.data.status === true) {
                        displayMessage(response.data.message || this.$t('Test results refreshed successfully.'));
                        this.fetchResults();
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to refresh test results.'));
                    }
                })
                .catch(error => {
                    this.refreshing = false;
                    console.error('Error refreshing results:', error);
                    
                    displayErrorMessage(this.$t('Failed to refresh test results.'));
                });
        },
        markAsReviewed(resultId) {
            if (this.processingIds.includes(resultId)) return;
            
            this.processingIds.push(resultId);
            
            const params = {
                result_id: resultId,
                status: 'reviewed'
            };
            
            post('tdl_update_result_status', params)
                .then(response => {
                    // Remove from processing list
                    this.processingIds = this.processingIds.filter(id => id !== resultId);
                    
                    if (response.data.status === true) {
                        displayMessage(this.$t('Result marked as reviewed.'));
                        
                        // Update the result status in the local data
                        const resultIndex = this.results.findIndex(r => r.id === resultId);
                        if (resultIndex !== -1) {
                            this.results[resultIndex].result_status = 'reviewed';
                            
                            // Create a new array to trigger reactivity
                            this.results = [...this.results];
                        }
                    } else {
                        displayErrorMessage(response.data.message || this.$t('Failed to update result status.'));
                    }
                })
                .catch(error => {
                    // Remove from processing list
                    this.processingIds = this.processingIds.filter(id => id !== resultId);
                    
                    console.error('Error updating result status:', error);
                    displayErrorMessage(this.$t('Failed to update result status.'));
                });
        }
    }
};
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>