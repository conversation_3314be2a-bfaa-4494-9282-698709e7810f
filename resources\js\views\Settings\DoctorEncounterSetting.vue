<template>
  <div class="">
    <div class="">
      <div class="space-y-8">
        <form @submit.prevent="handleSubmit" :novalidate="true">
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Header Section -->
            <div class="border-b border-gray-200 bg-gray-50 px-6 py-4">
              <div class="flex items-center justify-between">
                <h2 class="text-2xl font-semibold text-gray-800">
                  {{ formTranslation.common.encounter_template_setting }}
                </h2>
              </div>
            </div>

            <!-- Loading State -->
            <div v-if="isLoading" class="flex justify-center items-center min-h-[400px]">
              <loader-component-2></loader-component-2>
            </div>

            <!-- Content Section -->
            <div v-else class="p-6 space-y-8">
              <!-- Header Image Section -->
              <div class="space-y-4">
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                  <div v-if="headerPreview" class="flex justify-center">
                    <img 
                      :src="headerPreview" 
                      alt="Header Preview" 
                      class="h-32 object-contain rounded-lg shadow-sm"
                    />
                  </div>
                  <div v-else class="h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                    <p class="text-gray-500">No header image uploaded</p>
                  </div>
                </div>
                <div class="flex space-x-4">
                  <button 
                    @click="uploadHeader" 
                    type="button"
                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  >
                    {{ formTranslation.common.upload_header }}
                  </button>
                  <button 
                    @click="removeHeader" 
                    type="button"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                  >
                    {{ formTranslation.common.remove_header }}
                  </button>
                </div>
              </div>

              <div class="border-t border-gray-200"></div>

              <!-- Footer Image Section -->
              <div class="space-y-4">
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                  <div v-if="footerPreview" class="flex justify-center">
                    <img 
                      :src="footerPreview" 
                      alt="Footer Preview" 
                      class="h-32 object-contain rounded-lg shadow-sm"
                    />
                  </div>
                  <div v-else class="h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                    <p class="text-gray-500">No footer image uploaded</p>
                  </div>
                </div>
                <div class="flex space-x-4">
                  <button 
                    @click="uploadFooter" 
                    type="button"
                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  >
                    {{ formTranslation.common.upload_footer }}
                  </button>
                  <button 
                    @click="removeFooter" 
                    type="button"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                  >
                    {{ formTranslation.common.remove_footer }}
                  </button>
                </div>
              </div>

              <div class="border-t border-gray-200"></div>

              <!-- Submit Button Section -->
              <div class="flex justify-start">
                <button 
                  v-if="!loading" 
                  type="submit"
                  class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center space-x-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  <span>{{ formTranslation.common.save }}</span>
                </button>
                <button 
                  v-else 
                  type="button"
                  disabled
                  class="px-6 py-2 bg-blue-400 text-white rounded-lg flex items-center space-x-2 cursor-not-allowed"
                >
                  <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>{{ formTranslation.common.loading }}</span>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import {get,post} from "../../config/request";

export default {
  name: "DoctorEncounterSetting",
    data:() => {
    return {
      isLoading:true,
      loading:false,
      headerPreview:'',
      footerPreview:'',
      header_id:'',
      footer_id:'',
    }
  },
  mounted() {
    this.init()
  },
  methods:{
    init(){
        get('get_doctor_encounter_template_setting').then((res)=>{
          this.isLoading=false;
          this.header_id=res.data.data.header.id
          this.headerPreview=res.data.data.header.url

          this.footer_id=res.data.data.footer.id
          this.footerPreview=res.data.data.footer.url
        })
    },
    uploadHeader(){
      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);
      custom_uploader.on('select',  ()=> {
        var attachment = custom_uploader.state().get('selection').first().toJSON();
        this.headerPreview = attachment.url;
        this.header_id = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    removeHeader(){
      this.headerPreview = '';
      this.header_id = '';
    },
    uploadFooter(){
      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);
      custom_uploader.on('select',  ()=> {
        var attachment = custom_uploader.state().get('selection').first().toJSON();
        this.footerPreview = attachment.url;
        this.footer_id = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    removeFooter(){
      this.footerPreview = '';
      this.footer_id = '';
    },
    handleSubmit(){
      this.loading=true
      post('save_doctor_encounter_template_setting',{header:this.header_id,footer:this.footer_id}).then((res)=>{
        this.loading=false
      })
    }
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    }
  }
}
</script>

<style scoped>

</style>