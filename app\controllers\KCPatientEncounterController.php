<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCAppointment;
use App\models\KCBill;
use App\models\KCCustomField;
use App\models\KCPatientEncounter;
use App\models\KCReceptionistClinicMapping;
use App\models\KCClinic;
use App\models\KCPatientEncounterSummeryDocument;
use DateTime;
use Exception;
use Dompdf\Dompdf;

class KCPatientEncounterController extends KCBase
{
	private $mistral_api_url = 'https://api.mistral.ai/v1/chat/completions';
	private $model = 'open-mistral-nemo';


	public $db;

	/**
	 * @var KCRequest
	 */
	private $request;

	public function __construct()
	{

		global $wpdb;
		$this->db = $wpdb;
		$this->request = new KCRequest();
		parent::__construct();
	}

	public function index()
	{

		$is_permission = false;
		$this->system_message = $this->get_system_message();


		if (kcCheckPermission('patient_encounter_list')) {
			$is_permission = true;
		}

		if (!$is_permission) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();
		if (!isset($request_data['login_id'])) {
			wp_send_json([
				'status' => false,
				'message' => __('Patient id not found', 'kc-lang'),
				'data' => []
			]);
		}

		$login_id = get_current_user_id();
		$patient_encounter_table = $this->db->prefix . 'kc_patient_encounters';
		$clinics_table = $this->db->prefix . 'kc_clinics';
		$users_table = $this->db->base_prefix . 'users';

		$current_user_login_role = $this->getLoginUserRole();
		$patient_user_condition = $doctor_user_condition = $clinic_condition = $search_condition = '';
		$orderByCondition = " ORDER BY {$patient_encounter_table}.id DESC ";
		$paginationCondition = ' ';
		if ((int) $request_data['perPage'] > 0) {
			$perPage = (int) $request_data['perPage'];
			$offset = ((int) $request_data['page'] - 1) * $perPage;
			$paginationCondition = " LIMIT {$perPage} OFFSET {$offset} ";
		}

		if (!empty($request_data['sort'])) {
			$request_data['sort'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['sort'][0]), true));
			if (!empty($request_data['sort']['field']) && !empty($request_data['sort']['type']) && $request_data['sort']['type'] !== 'none') {
				$sortField = esc_sql($request_data['sort']['field']);
				$sortByValue = esc_sql(strtoupper($request_data['sort']['type']));
				switch ($request_data['sort']['field']) {
					case 'status':
					case 'encounter_date':
					case 'id':
						$orderByCondition = " ORDER BY {$patient_encounter_table}.{$sortField} {$sortByValue} ";
						break;
					case 'doctor_name':
						$orderByCondition = " ORDER BY doctors.display_name {$sortByValue} ";
						break;
					case 'clinic_name':
						$orderByCondition = " ORDER BY {$clinics_table}.name {$sortByValue} ";
						break;
					case 'patient_name':
						$orderByCondition = " ORDER BY patients.display_name {$sortByValue} ";
						break;
				}

				$orderByCondition = apply_filters('kivicare_orderby_query_filter', $search_condition, $sortByValue, $sortField, 'encounter');
			}
		}

		if (isset($request_data['searchTerm']) && trim($request_data['searchTerm']) !== '') {
			$request_data['searchTerm'] = esc_sql(strtolower(trim($request_data['searchTerm'])));
			$search_condition .= " AND (
                           {$patient_encounter_table}.id LIKE '%{$request_data['searchTerm']}%'
                           OR {$clinics_table}.name LIKE '%{$request_data['searchTerm']}%'
                           OR doctors.display_name LIKE '%{$request_data['searchTerm']}%'
                           OR patients.display_name LIKE '%{$request_data['searchTerm']}%'
                           OR {$patient_encounter_table}.status LIKE '%{$request_data['searchTerm']}%'
                           ) ";
		} else {
			if (!empty($request_data['columnFilters'])) {
				$request_data['columnFilters'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['columnFilters']), true));
				foreach ($request_data['columnFilters'] as $column => $searchValue) {
					$searchValue = $column !== 'encounter_date' ? esc_sql(strtolower(trim($searchValue))) : $searchValue;
					$column = esc_sql($column);
					if ($searchValue === '') {
						continue;
					}
					switch ($column) {
						case 'status':
						case 'id':
							$search_condition .= " AND {$patient_encounter_table}.{$column} LIKE '%{$searchValue}%' ";
							break;
						case 'doctor_name':
							$search_condition .= " AND doctors.display_name LIKE '%{$searchValue}%' ";
							break;
						case 'clinic_name':
							$search_condition .= " AND {$clinics_table}.name LIKE '%{$searchValue}%' ";
							break;
						case 'patient_name':
							$search_condition .= " AND patients.display_name LIKE '%{$searchValue}%'";
							break;
						case 'encounter_date':
							if (!empty($searchValue['start']) && !empty($searchValue['end'])) {
								$searchValue['start'] = esc_sql(strtolower(trim($searchValue['start'])));
								$searchValue['end'] = esc_sql(strtolower(trim($searchValue['end'])));
								$search_condition .= " AND CAST({$patient_encounter_table}.{$column} AS DATE )  BETWEEN '{$searchValue['start']}' AND '{$searchValue['end']}' ";
							}
							break;
					}

					$search_condition = apply_filters('kivicare_search_query_filter', $search_condition, $searchValue, $column, 'encounter');
				}
			}
		}
		if ($this->getPatientRole() === $current_user_login_role) {
			$patient_upcoming = isset($request_data['type']) && $request_data['type'] === 'upcoming' ? " AND {$patient_encounter_table}.encounter_date  >= CURDATE()" : '';
			$patient_user_condition = " AND {$patient_encounter_table}.patient_id = {$login_id} {$patient_upcoming}";
		}

		if (!empty($request_data['patient_id']) && $request_data['patient_id'] > 0) {
			$request_data['patient_id'] = (int) $request_data['patient_id'];
			$patient_user_condition = " AND {$patient_encounter_table}.patient_id = {$request_data['patient_id']} ";
		}

		if ($this->getDoctorRole() === $current_user_login_role) {
			$doctor_user_condition = " AND {$patient_encounter_table}.doctor_id = {$login_id} ";
		}

		if ($this->getClinicAdminRole() === $current_user_login_role) {
			$clinic_condition = " AND {$patient_encounter_table}.clinic_id=" . kcGetClinicIdOfClinicAdmin();
		}

		if ($this->getReceptionistRole() === $current_user_login_role) {
			$clinic_condition = " AND {$patient_encounter_table}.clinic_id = " . kcGetClinicIdOfReceptionist();
		}


		$common_query = " FROM  {$patient_encounter_table}
		       LEFT JOIN {$users_table} doctors
		              ON {$patient_encounter_table}.doctor_id = doctors.id
		       LEFT JOIN {$users_table} patients
		              ON {$patient_encounter_table}.patient_id = patients.id
		       LEFT JOIN {$clinics_table}
		              ON {$patient_encounter_table}.clinic_id = {$clinics_table}.id
            WHERE 0 = 0  {$patient_user_condition}  {$doctor_user_condition}  {$clinic_condition} {$search_condition} ";

		$encounters = $this->db->get_results("SELECT {$patient_encounter_table}.*,
		       doctors.display_name  AS doctor_name,
		       patients.display_name AS patient_name,
		       {$clinics_table}.name AS clinic_name
			  {$common_query} {$orderByCondition} {$paginationCondition} ");

		if (!count($encounters)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('No consultation found', 'kc-lang'),
				'data' => []
			]);
		}

		$total = $this->db->get_var("SELECT count(*) {$common_query} ");
		$custom_form_appointment = apply_filters('kivicare_custom_form_list', [], ['type' => 'appointment_module']);
		$custom_form_appointment = array_filter($custom_form_appointment, function ($v) {
			return !empty($v->show_mode) && in_array('encounter', $v->show_mode);
		});
		$custom_forms = array_merge($custom_form_appointment, apply_filters('kivicare_custom_form_list', [], ['type' => 'patient_encounter_module']));
		array_walk($encounters, function (&$encounter) use ($custom_forms) {
			$encounter->clinic_name = decodeSpecificSymbols($encounter->clinic_name);
			$encounter->custom_forms = $custom_forms;
			$encounter->encounter_date = kcGetFormatedDate($encounter->encounter_date);
		});

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Consultation list', 'kc-lang'),
			'data' => $encounters,
			'total_rows' => $total,
			"clinic_extra" => kcGetClinicCurrenyPrefixAndPostfix()
		]);
	}

	public function save()
	{

		$is_permission = false;

		if (kcCheckPermission('patient_encounter_add')) {
			$is_permission = true;
		}

		if (!$is_permission) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (isKiviCareProActive()) {
			if ($this->getLoginUserRole() == $this->getClinicAdminRole()) {
				$request_data['clinic_id']['id'] = kcGetClinicIdOfClinicAdmin();
			} elseif ($this->getLoginUserRole() == $this->getReceptionistRole()) {
				$request_data['clinic_id']['id'] = kcGetClinicIdOfReceptionist();
			}

			$patient_id = isset($request_data['patient_id']['id']) ? (int) $request_data['patient_id']['id'] : (int) $request_data['patient_id'];

			global $wpdb;
			$clinic_id = $wpdb->get_var("SELECT clinic_id FROM {$wpdb->prefix}kc_patient_clinic_mappings WHERE patient_id={$patient_id}");
			if (isKiviCareProActive()) {
				$request_data['clinic_id']['id'] = !empty($clinic_id) ? (int) $clinic_id : 0;
			} else {
				$request_data['clinic_id']['id'] = kcGetDefaultClinicId();
			}
		}

		$rules = [
			'date' => 'required',
			'patient_id' => 'required',
			'status' => 'required',

		];

		$message = [
			'status' => esc_html__('Status is required', 'kc-lang'),
			'patient_id' => esc_html__('Patient is required', 'kc-lang'),
			'doctor_id' => esc_html__('Doctor is required', 'kc-lang'),
		];

		$errors = kcValidateRequest($rules, $request_data, $message);

		if (count($errors)) {
			wp_send_json([
				'status' => false,
				'message' => $errors[0]
			]);
		}
		$temp = [
			'encounter_date' => date('Y-m-d', strtotime($request_data['date'])),
			'patient_id' => isset($request_data['patient_id']['id']) ? (int) $request_data['patient_id']['id'] : (int) $request_data['patient_id'],
			'clinic_id' => isset($request_data['clinic_id']['id']) ? (int) $request_data['clinic_id']['id'] : 1,
			'doctor_id' => isset($request_data['doctor_id']['id']) ? (int) $request_data['doctor_id']['id'] : (int) $request_data['doctor_id'],
			'description' => $request_data['description'],
			'status' => $request_data['status'],
		];

		$temp = apply_filters('kivicare_update_encounter_save_fields', $temp, $request_data);

		$patient_encounter = new KCPatientEncounter();


		if (!isset($request_data['id'])) {

			$temp['created_at'] = current_time('Y-m-d H:i:s');
			$temp['added_by'] = get_current_user_id();
			$encounter_id = $patient_encounter->insert($temp);
			$message = esc_html__('Patient consultation saved successfully', 'kc-lang');
			do_action('kc_encounter_save', $encounter_id);

			kcLogActivity(
                'create_consultation',
                sprintf(esc_html__('Patient consultation saved successfully', 'kc-lang'), $encounter_id),
                [
                    'encounter_id' => $encounter_id,
                ]
            );

		} else {
			$encounter_id = (int) $request_data['id'];
			if (!((new KCPatientEncounter())->encounterPermissionUserWise($encounter_id))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}
			$status = $patient_encounter->update($temp, array('id' => (int) $request_data['id']));
			$message = esc_html__('Patient consultation has been updated successfully', 'kc-lang');
			do_action('kc_encounter_update', $encounter_id);

			kcLogActivity(
                'update_consultation',
                sprintf(esc_html__('Patient consultation has been updated successfully', 'kc-lang'), $encounter_id),
                [
                    'encounter_id' => $encounter_id,
                ]
            );
		}

		wp_send_json([
			'status' => true,
			'message' => $message,
			'data' => $encounter_id
		]);

	}

	public function edit()
	{

		$is_permission = false;

		if (kcCheckPermission('patient_encounter_edit')) {
			$is_permission = true;
		}

		if (!$is_permission) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();
		try {

			if (!isset($request_data['id'])) {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
			}

			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$id = (int) $request_data['id'];

			$patient_encounter_table = $this->db->prefix . 'kc_patient_encounters';
			$clinics_table = $this->db->prefix . 'kc_clinics';
			$users_table = $this->db->base_prefix . 'users';

			$query = "
			SELECT {$patient_encounter_table}.*,
			   doctors.display_name  AS doctor_name,
			   patient.display_name  AS patient_name,
		       {$clinics_table}.name AS clinic_name
			FROM  {$patient_encounter_table}
		       LEFT JOIN {$users_table} doctors
					  ON {$patient_encounter_table}.doctor_id = doctors.id
			   LEFT JOIN {$users_table} patient
					ON {$patient_encounter_table}.patient_id = patient.id
		       LEFT JOIN {$clinics_table}
		              ON {$patient_encounter_table}.clinic_id = {$clinics_table}.id
            WHERE {$patient_encounter_table}.id = {$id} LIMIT 1";

			$encounter = $this->db->get_row($query);
			if (!empty($encounter)) {

				$temp = [
					'id' => $encounter->id,
					'date' => $encounter->encounter_date,
					'patient_id' => [
						'id' => $encounter->patient_id,
						'label' => $encounter->patient_name
					],
					'clinic_id' => [
						'id' => $encounter->clinic_id,
						'label' => $encounter->clinic_name
					],
					'doctor_id' => [
						'id' => $encounter->doctor_id,
						'label' => $encounter->doctor_name
					],
					'description' => $encounter->description,
					'status' => $encounter->status,
					'added_by' => $encounter->added_by,
				];

				$temp = apply_filters('kivicare_update_encounter_edit_fields', $temp, $encounter);

				$encounter->custom_forms = array_merge(
					apply_filters('kivicare_custom_form_list', [], ['type' => 'appointment_module']),
					apply_filters('kivicare_custom_form_list', [], ['type' => 'patient_encounter_module'])
				);
				wp_send_json([
					'status' => true,
					'message' => __('Consultation data', 'kc-lang'),
					'data' => $temp
				]);
			} else {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
			}

		} catch (Exception $e) {

			$code = $e->getCode();
			$message = $e->getMessage();

			header("Status: $code $message");

			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function delete()
	{

		$is_permission = false;

		if (kcCheckPermission('patient_encounter_delete')) {
			$is_permission = true;
		}

		if (!$is_permission) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		try {

			if (!isset($request_data['id'])) {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
			}

			$id = (int) $request_data['id'];

			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$results = (new KCPatientEncounter())->loopAndDelete(['id' => $id], true);

			if ($results) {
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Consultation has been deleted successfully', 'kc-lang'),
				]);
			} else {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Patient encounter delete failed', 'kc-lang'), 400));
			}


		} catch (Exception $e) {

			$code = $e->getCode();
			$message = $e->getMessage();

			header("Status: $code $message");

			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function details()
	{

		$request_data = $this->request->getInputs();

		try {

			if (!isset($request_data['id'])) {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
			}

			$id = (int) $request_data['id'];

			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$encounter = $this->getEncounterData($id);

			$encounter->encounter_date = kcGetFormatedDate($encounter->encounter_date);

			$patientDetails = apply_filters('kivicare_encounter_patient_details', $encounter) ?? [];

			$user_meta_data = [];
			if (!empty($patientDetails)) {
				$user_meta_data = get_user_meta($patientDetails->patient_id, 'basic_data', true);
				if ($user_meta_data) {
					$user_meta_data = json_decode($user_meta_data);
				}
			}

			if ($encounter) {
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Consultation details', 'kc-lang'),
					'data' => $encounter,
					'patientDetails' => $patientDetails,
					'patientMetaData' => $user_meta_data,
					'hideInPatient' => get_option(KIVI_CARE_PREFIX . 'hide_clinical_detail_in_patient', false)
				]);

			} else {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Consultation not found', 'kc-lang'), 400));
			}

		} catch (Exception $e) {

			$code = $e->getCode();
			$message = $e->getMessage();

			header("Status: $code $message");

			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function saveCustomField()
	{

		if (!kcCheckPermission('patient_encounter_add')) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();
		if (!isset($request_data['id'])) {
			wp_send_json([
				'status' => false,
				'status_code' => 404,
				'message' => esc_html__('Consultation id not found', 'kc-lang'),
				'data' => []
			]);
		}

		if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['id']))) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		//		$custom_fields = KCCustomField::getRequiredFields( 'patient_encounter_module' );

		if (!empty($request_data['custom_fields'])) {
			if (is_array($request_data['custom_fields']) && count($request_data['custom_fields']) > 0) {
				if (strpos(array_key_first($request_data['custom_fields']), 'custom_field_') === false) {
					wp_send_json([
						'status' => true,
						'message' => esc_html__('Consultation data has been saved', 'kc-lang'),
					]);
				}
			}
			// custom field add based on encounter id.
			kvSaveCustomFields('patient_encounter_module', $request_data['id'], $request_data['custom_fields']);
			do_action('kc_encounter_update', $request_data['id']);
		}

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Consultation data has been saved', 'kc-lang'),
		]);
	}

	public function getEncounterData($id)
	{

		$patient_encounter_table = $this->db->prefix . 'kc_patient_encounters';
		$clinics_table = $this->db->prefix . 'kc_clinics';
		$users_table = $this->db->base_prefix . 'users';
		$appointment_table = $this->db->prefix . 'kc_appointments';

		$appointment_report_query = $appointment_report_join_query = '';

		if (kcAppointmentMultiFileUploadEnable()) {
			$appointment_report_query = ", {$appointment_table}.appointment_report";
			$appointment_report_join_query = "LEFT JOIN {$appointment_table}
			ON {$appointment_table}.id = {$patient_encounter_table}.appointment_id";
		}

		$query = "
			SELECT {$patient_encounter_table}.*,
		       doctors.display_name  AS doctor_name,
		       doctors.user_email  AS doctor_email,
		       patients.display_name AS patient_name,
		       patients.user_email AS patient_email,
		       {$clinics_table}.name AS clinic_name,
		       {$clinics_table}.address AS clinic_address,
		       {$clinics_table}.city AS clinic_city,
		       {$clinics_table}.state AS clinic_state,
		       {$clinics_table}.country AS clinic_country,
		       {$clinics_table}.website AS clinic_website,
		       {$clinics_table}.telephone_no AS clinic_telephone_no,
			   {$clinics_table}.extra AS clinic_extra,
			   {$clinics_table}.profile_image AS clinic_logo
			   {$appointment_report_query}
			FROM  {$patient_encounter_table}
		       LEFT JOIN {$users_table} doctors
		              ON {$patient_encounter_table}.doctor_id = doctors.id
              LEFT JOIN {$users_table} patients
		              ON {$patient_encounter_table}.patient_id = patients.id
		       LEFT JOIN {$clinics_table}
		              ON {$patient_encounter_table}.clinic_id = {$clinics_table}.id
			   {$appointment_report_join_query}
            WHERE {$patient_encounter_table}.id = {$id}";

		$encounter = $this->db->get_row($query);



		if (!empty($encounter)) {
			$patient_profile_image = get_user_meta($encounter->patient_id, 'patient_profile_image', true);
			$patient = get_user_meta($encounter->patient_id, 'basic_data', true);
			$patient = json_decode($patient);
			$get_patient_data = get_option(KIVI_CARE_PREFIX . 'patient_id_setting', true);

			if (gettype($get_patient_data) != 'boolean') {
				$encounter->is_patient_unique_id_enable = in_array((string) $get_patient_data['enable'], ['true', '1']) ? true : false;
			}

			$doc_meta_data = get_user_meta($encounter->doctor_id, 'basic_data', true);
			if ($doc_meta_data) {
				$doc_meta_data = json_decode($doc_meta_data);
				$encounter->doctor_qualifications = isset($doc_meta_data->qualifications) ? $doc_meta_data->qualifications : "";
				$encounter->doctor_npi_no = isset($doc_meta_data->npi_no) ? $doc_meta_data->npi_no : "";
				$encounter->doctor_registration_prefix = isset($doc_meta_data->registration_prefix) ? $doc_meta_data->registration_prefix : "";
				$encounter->doctor_gmc_no		 = isset($doc_meta_data->gmc_no	) ? $doc_meta_data->gmc_no	 : "";
				$encounter->doctor_specialties = isset($doc_meta_data->specialties) ? $doc_meta_data->specialties : [];
			}
			$encounter->clinic_name = decodeSpecificSymbols($encounter->clinic_name);
			$encounter->patient_unique_id = get_user_meta((int) $encounter->patient_id, 'patient_unique_id', true) ?? '-';
			$encounter->patient_address = (!empty($patient->address) ? $patient->address : '');
			$encounter->patient_city = (!empty($patient->city) ? $patient->city : '');
			$encounter->patient_state = (!empty($patient->state) ? $patient->state : '');
			$encounter->patient_country = (!empty($patient->country) ? $patient->country : '');
			$encounter->patient_profile_image = !empty($patient_profile_image) ? wp_get_attachment_url($patient_profile_image) : '';
			if (!empty($encounter->appointment_report)) {
				$encounter->appointment_report = array_map(function ($v) {
					$name = !empty(get_the_title($v)) ? get_the_title($v) : '';
					$url = !empty(wp_get_attachment_url($v)) ? wp_get_attachment_url($v) : '';
					return ['url' => $url, 'name' => $name];
				}, json_decode($encounter->appointment_report, true));
			}
			if (!empty($encounter->appointment_id) && isKiviCareProActive()) {
				$encounter->appointment_custom_field = kcGetCustomFields('appointment_module', $encounter->appointment_id, (int) $encounter->doctor_id);
			}
			$encounter->custom_forms = array_merge(
				apply_filters('kivicare_custom_form_list', [], ['type' => 'appointment_module']),
				apply_filters('kivicare_custom_form_list', [], ['type' => 'patient_encounter_module'])
			);

			$doctor_signature = get_user_meta($encounter->doctor_id, 'doctor_signature', true);
			$encounter->doctor_signature = !empty($doctor_signature) ? wp_get_attachment_url($doctor_signature) : '';

			$clinic_logo = $encounter->clinic_logo;
			$encounter->clinic_logo = !empty($clinic_logo) ? wp_get_attachment_url($clinic_logo) : '';

			$encounter->custom_fields = kcGetCustomFields('patient_encounter_module', $encounter->doctor_id);

			$encounter->encounter_edit_after_close_status = (get_option(KIVI_CARE_PREFIX . 'encounter_edit_after_close_status', 'off') === 'on') ? true : false;
			return $encounter;
		} else {
			return null;
		}
	}


	public function updateStatus()
	{

		if (kcCheckPermission('patient_encounter_add')) {
			$is_permission = true;
		}

		$request_data = $this->request->getInputs();



		try {

			if (!isset($request_data['id'])) {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
			}

			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$id = (int) $request_data['id'];
			$patient_encounter = new KCPatientEncounter();
			$encounter = $patient_encounter->get_by(['id' => $id], '=', true);

			if (empty($encounter)) {
				wp_send_json(kcThrowExceptionResponse(esc_html__('Consultation not found', 'kc-lang'), 400));
			}

			if ((string) $request_data['status'] === '0') {
				if ((string) $request_data['checkOutVal'] === '1') {
					(new KCAppointment())->update(['status' => '3'], ['id' => (int) $encounter->appointment_id]);
				}
				(new KCBill())->update(['status' => 1], ['encounter_id' => (int) $encounter->id]);
			}

			$patient_encounter->update(['status' => $request_data['status']], ['id' => $id]);
			do_action('kc_encounter_update', $id);
			wp_send_json([
				'status' => true,
				'message' => esc_html__('Consultation status has been updated', 'kc-lang')
			]);

		} catch (Exception $e) {

			$code = $e->getCode();
			$message = $e->getMessage();

			header("Status: $code $message");

			wp_send_json([
				'status' => false,
				'message' => $message
			]);
		}
	}

	public function sendBillToPatient()
	{
		$request_data = $this->request->getInputs();
		$request_data['id'] = (int) $request_data['id'];

		echo '<pre>';
		print_r($request_data);
		die;
	}

	public function printEncounterBillDetail()
	{
		global $wpdb;
		$request_data = $this->request->getInputs();
		$request_data['id'] = (int) $request_data['id'];
		$request_type = $request_data['type'];

		if (!empty($request_data['id']) && !empty($request_data['data'])) {
			// if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['id']))) {
			// 	wp_send_json(kcUnauthorizeAccessResponse(403));
			// }
			$request_data['data'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['data']), true));
			$patient_encounter_table = $this->db->prefix . 'kc_patient_encounters';
			$clinics_table = $this->db->prefix . 'kc_clinics';
			$users_table = $this->db->base_prefix . 'users';
			// Get encounter ID from request data
			$encouter_id = (int) $request_data['data']['encounter_id'];
			// Get bill ID as fallback
			$bill_id = isset($request_data['id']) ? (int) $request_data['id'] : 0;
			$billing_table =  $wpdb->prefix. 'kc_billing_data';

			$query = "SELECT
			source_data.id,
			source_data.doctor_id,
			source_data.clinic_id,
			source_data.patient_id,
			doctors.display_name AS doctor_name,
			doctors.user_email AS doctor_email,
			patients.display_name AS patient_name,
			patients.user_email AS patient_email,
			CONCAT('#',{$clinics_table}.address, ', ', {$clinics_table}.city,', ', {$clinics_table}.postal_code,', ',{$clinics_table}.country) AS clinic_address,
			{$clinics_table}.*
			FROM (
			-- Use a simpler approach with IF to handle the fallback logic
			SELECT
				IF({$encouter_id} > 0,
					(SELECT pe.id FROM {$patient_encounter_table} pe WHERE pe.id = {$encouter_id}),
					(SELECT bd.id FROM {$billing_table} bd WHERE bd.billing_id = {$bill_id})
				) AS id,
				IF({$encouter_id} > 0,
					(SELECT pe.doctor_id FROM {$patient_encounter_table} pe WHERE pe.id = {$encouter_id}),
					(SELECT bd.doctor_id FROM {$billing_table} bd WHERE bd.billing_id = {$bill_id})
				) AS doctor_id,
				IF({$encouter_id} > 0,
					(SELECT pe.clinic_id FROM {$patient_encounter_table} pe WHERE pe.id = {$encouter_id}),
					(SELECT bd.clinic_id FROM {$billing_table} bd WHERE bd.billing_id = {$bill_id})
				) AS clinic_id,
				IF({$encouter_id} > 0,
					(SELECT pe.patient_id FROM {$patient_encounter_table} pe WHERE pe.id = {$encouter_id}),
					(SELECT bd.patient_id FROM {$billing_table} bd WHERE bd.billing_id = {$bill_id})
				) AS patient_id
			) AS source_data
			LEFT JOIN {$users_table} doctors ON source_data.doctor_id = doctors.id
			LEFT JOIN {$users_table} patients ON source_data.patient_id = patients.id
			LEFT JOIN {$clinics_table} ON source_data.clinic_id = {$clinics_table}.id
			WHERE source_data.id IS NOT NULL";

			$encounter = $this->db->get_row($query);
			$qualifications = [];
			if (!empty($encounter)) {
				$encounter->medical_history = '';
				$encounter->prescription = '';
				$basic_data = get_user_meta((int) $encounter->doctor_id, 'basic_data', true);
				$basic_data = json_decode($basic_data);
				if (!empty($basic_data->qualifications)) {
					foreach ($basic_data->qualifications as $q) {
						$qualifications[] = $q->degree;
						$qualifications[] = $q->university;
					}
				}
				$patient_basic_data = json_decode(get_user_meta((int) $encounter->patient_id, 'basic_data', true));
				$encounter->patient_gender = !empty($patient_basic_data->gender)
					? ($patient_basic_data->gender === 'female'
						? 'F' : 'M') : '';
				$encounter->patient_address = (!empty($patient_basic_data->address) ? $patient_basic_data->address : '');
				$encounter->patient_city = (!empty($patient_basic_data->city) ? $patient_basic_data->city : '');
				$encounter->patient_state = (!empty($patient_basic_data->state) ? $patient_basic_data->state : '');
				$encounter->patient_country = (!empty($patient_basic_data->country) ? $patient_basic_data->country : '');
				$encounter->patient_postal_code = (!empty($patient_basic_data->postal_code) ? $patient_basic_data->postal_code : '');
				$encounter->contact_no = (!empty($patient_basic_data->mobile_number) ? $patient_basic_data->mobile_number : '');
				$encounter->patient_add = $encounter->patient_address . ',' . $encounter->patient_city
					. ',' . $encounter->patient_state . ',' . $encounter->patient_country . ',' . $encounter->patient_postal_code;
				$encounter->date = current_time('Y-m-d');
				$encounter->patient_age = '';
				if (!empty($patient_basic_data->dob)) {
					try {
						$from = new DateTime($patient_basic_data->dob);
						$to = new DateTime('today');
						$years = $from->diff($to)->y;
						$months = $from->diff($to)->m;
						$days = $from->diff($to)->d;
						if (empty($months) && empty($years)) {
							$encounter->patient_age = $days . esc_html__(' Days', 'kc-lang');
						} else if (empty($years)) {
							$encounter->patient_age = $months . esc_html__(' Months', 'kc-lang');
						} else {
							$encounter->patient_age = $years . esc_html__(' Years', 'kc-lang');
						}
					} catch (Exception $e) {
						wp_send_json([
							'data' => '',
							'status' => false,
							'calendar_content' => '',
							'message' => $e->getMessage()
						]);
					}
				}
				$encounter->qualifications = !empty($qualifications) ? '(' . implode(", ", $qualifications) . ')' : '';
				$encounter->clinic_logo = !empty($encounter->profile_image) ? wp_get_attachment_url($encounter->profile_image) : KIVI_CARE_DIR_URI . 'assets/images/kc-demo-img.png';
				$encounter->billItems = !empty($request_data['data']['billItems']) ? $request_data['data']['billItems'] : [];
				$encounter->payment_status = !empty($request_data['data']['payment_status']) ? $request_data['data']['payment_status'] : '';
				$encounter->bill_created = !empty($request_data['data']['created_at']) ? $request_data['data']['created_at'] : '';
				$encounter->actual_amount = !empty($request_data['data']['actual_amount']) ? $request_data['data']['actual_amount'] : '';
				$encounter->total_amount = !empty($request_data['data']['total_amount']) ? $request_data['data']['total_amount'] : '';
				$encounter->discount = !empty($request_data['data']['discount']) ? $request_data['data']['discount'] : '0';
			}

				$invoice_status = false;
				$print_data = kcPrescriptionHtml($encounter, $encouter_id, 'bill_detail');

				// Instantiate and use the dompdf class
				$dompdf = new Dompdf();
				$dompdf->set_option('isHtml5ParserEnabled', true);
				$dompdf->set_option('isPhpEnabled', true);

				$dompdf->loadHtml($print_data);

				// (Optional) Setup the paper size and orientation
				$dompdf->setPaper('A4', 'portrait');

				// Render the HTML as PDF
				$dompdf->render();

				// Get the generated PDF as a string
				$pdf_output = $dompdf->output();


				$file_name = 'Invoice_' . $encouter_id . '.pdf';

				// Get the WordPress uploads directory path
				$upload_dir = wp_upload_dir();
				$pdf_path = $upload_dir['path'] . '/' . $file_name;

				// Save the PDF to the uploads directory
				file_put_contents($pdf_path, $pdf_output);

				$patient_report[0] = $pdf_path;

				if ($encounter) {
					$user_email = $wpdb->get_var('select user_email from ' . $wpdb->base_prefix . 'users where ID=' . (int) $encounter->patient_id);

					$invoice_data=get_invoice_data($request_data['id']);

					$invoice_data['user_email'] = $user_email;

					if (isset($invoice_data['services']) && is_array($invoice_data['services'])) {
						foreach ($invoice_data['services'] as $index => $service) {
							$item_total = $service['price'] * $service['quantity'];
							$total += $item_total;

							$service_items_html .= '<tr>
								<td>' . ($index + 1) . '</td>
								<td>' . esc_html($service['name']) . '</td>
								<td>£' . number_format($service['price'], 2) . '</td>
								<td>' . esc_html($service['quantity']) . '</td>
								<td class="text-right">£' . number_format($item_total, 2) . '</td>
							</tr>';
						}
					}

					$invoice_data['service_items'] = $service_items_html;
					$data = [
						...$invoice_data,
						'attachment_file' => $patient_report,
						'attachment' => true,
						'email_template_type' => 'patient_invoice'
					];

					if($request_type === 'sendBill'){
						$invoice_status = kcSendEmail($data);
					}else{
						$template= kcGenerateEmailContent($data);

						wp_send_json([
							'data' => $template['content'],
							'status' => true
						]);
					}

					$invoice_message = $invoice_status ? esc_html__('Invoice sent successfully', 'kc-lang') : esc_html__('Failed to send Invoice', 'kc-lang');

					if ($invoice_status) {
						if (file_exists($pdf_path)) {
							unlink($pdf_path);
						}
					}

					wp_send_json([
						'data' => [],
						'status' => $invoice_status,
						'message' => $invoice_message
					]);
				}
				wp_send_json([
					'data' => [],
					'status' => false,
					'message' => __('Consultation Id not found', 'kc-lang')
				]);
		} else {
			wp_send_json([
				'data' => [],
				'status' => false,
				'message' => __('Consultation Id not found', 'kc-lang')
			]);
		}
	}

	public function encounterExtraClinicalDetailFields()
	{
		$request_data = $this->request->getInputs();
		$encounter_id = (int) $request_data['encounter_id'];
		$encounter_status = $request_data['status'];
		wp_send_json(
			[
				'status' => true,
				'data' => [
					[
						"type" => 'disease',
						"title" => 'disease',
						"encounter_id" => $encounter_id,
						"status" => $encounter_status,
						"ref" => 'medical_history_disease'
					],
					[
						"type" => 'report',
						"title" => 'report',
						"encounter_id" => $encounter_id,
						"status" => $encounter_status,
						"ref" => 'medical_history_report'
					],

				]
			]
		);
	}


	public function encounterPermissionUserWise($encounter_id)
	{
		$encounter_detail = (new KCPatientEncounter())->get_by(['id' => (int) $encounter_id], '=', true);
		$permission = false;

		$login_user_role = $this->getLoginUserRole();
		switch ($login_user_role) {
			case $this->getReceptionistRole():
				$clinic_id = kcGetClinicIdOfReceptionist();
				if (!empty($encounter_detail->clinic_id) && (int) $encounter_detail->clinic_id === $clinic_id) {
					$permission = true;
				}
				break;
			case $this->getClinicAdminRole():
				$clinic_id = kcGetClinicIdOfClinicAdmin();
				if (!empty($encounter_detail->clinic_id) && (int) $encounter_detail->clinic_id === $clinic_id) {
					$permission = true;
				}
				break;
			case 'administrator':
				$permission = true;
				break;
			case $this->getDoctorRole():
				if (!empty($encounter_detail->doctor_id) && (int) $encounter_detail->doctor_id === get_current_user_id()) {
					$permission = true;
				}
				break;
			case $this->getPatientRole():
				if (!empty($encounter_detail->patient_id) && (int) $encounter_detail->patient_id === get_current_user_id()) {
					$permission = true;
				}
				break;
		}
		return $permission;
	}

	public function getEncounterPrint()
	{

		$request_data = $this->request->getInputs();
		if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['encounter_id']))) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}
		$response = apply_filters('kcpro_get_encounter_print', [
			'encounter_id' => (int) $request_data['encounter_id'],
			'clinic_default_logo' => KIVI_CARE_DIR_URI . 'assets/images/kc-demo-img.png',
		]);
		wp_send_json($response);
	}

	public function generatePrescriptionPdf()
	{
		try {
			$request_data = $this->request->getInputs();

			// Permission check
			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['encounter_id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$encounter_id = (int) $request_data['encounter_id'];
			$qualifications = [];
			$university = [];

			// Table definitions
			$patient_encounter_table = $this->db->prefix . 'kc_' . 'patient_encounters';
			$clinics_table = $this->db->prefix . 'kc_' . 'clinics';
			$users_table = $this->db->base_prefix . 'users';
			$medical_history_table = $this->db->prefix . 'kc_medical_history';
			$custom_field = $this->db->prefix . 'kc_custom_fields';
			$custom_field_data = $this->db->prefix . 'kc_custom_fields_data';
			$encounter_tabs_table = $this->db->prefix . 'kc_encounter_tabs';

			// Get prescription data
			$precriptionResults = $this->db->get_results("SELECT pre.* ,enc.*
				FROM {$this->db->prefix}kc_prescription AS pre
				JOIN {$patient_encounter_table} AS enc ON enc.id=pre.encounter_id
				WHERE pre.encounter_id={$encounter_id}");

			// Get tabs data
			$tabs_data = $this->db->get_results("SELECT * FROM {$encounter_tabs_table}
				WHERE encounter_id = {$encounter_id}
				ORDER BY created_at ASC");

			// Process tabs data
			$processed_tabs = [];
			if (!empty($tabs_data)) {
				foreach ($tabs_data as $tab) {
					$metadata = !empty($tab->metadata) ? json_decode($tab->metadata, true) : [];
					$processed_tabs[$tab->type][] = [
						'content' => $tab->content,
						'metadata' => $metadata,
						'created_at' => $tab->created_at
					];
				}
			}

			// Get encounter data
			$query = "SELECT {$patient_encounter_table}.*,
					{$patient_encounter_table}.description,
					{$patient_encounter_table}.status AS Estatus,
					doctors.display_name AS doctor_name,
					doctors.user_email AS doctor_email,
					patients.display_name AS patient_name,
					patients.user_email AS patient_email,
					{$clinics_table}.*
				FROM {$patient_encounter_table}
					LEFT JOIN {$users_table} doctors
						ON {$patient_encounter_table}.doctor_id = doctors.id
					LEFT JOIN {$users_table} patients
						ON {$patient_encounter_table}.patient_id = patients.id
					LEFT JOIN {$clinics_table}
						ON {$patient_encounter_table}.clinic_id = {$clinics_table}.id
				WHERE {$patient_encounter_table}.id = {$encounter_id} LIMIT 1";

			$encounter = $this->db->get_row($query);

			// Get medical history
			$medical_history_data = collect($this->db->get_results("SELECT * FROM {$medical_history_table} WHERE encounter_id={$encounter_id}"))
				->groupBy('type')
				->map(function ($type) {
					return $type->pluck('title');
				})
				->toArray();

			// Process custom fields
			$custom_fields = $this->db->get_results("SELECT * FROM {$custom_field} WHERE module_type = 'patient_encounter_module'");
			foreach ($custom_fields as $field) {
				$field_details = json_decode($field->fields);
				$encounter->custom_fields = $custom_fields;
				$field->cus_id = $field_details->id;
				$field->label = $field_details->label;
				$field->name = $field_details->name;
				$field->placeholder = $field_details->placeholder;
				$field->type = $field_details->type;
				$field->options = !empty($field_details->options) ? $field_details->options : [];
			}

			$custom_fields_data = $this->db->get_results("SELECT * FROM {$custom_field_data} WHERE module_id = {$encounter_id}");
			$encounter->custom_fields_data = $custom_fields_data;

			if (!empty($encounter)) {
				// Add tabs data and other information
				$encounter->additional_tabs = $processed_tabs;
				$encounter->medical_history = $medical_history_data;
				$encounter->prescription = $precriptionResults;
				$encounter->include_encounter_custom_field = get_option(KIVI_CARE_PRO_PREFIX . 'include_encounter_custom_field_in_print', false);

				// Process doctor data
				$basic_data = get_user_meta((int) $encounter->doctor_id, 'basic_data', true);
				$basic_data = json_decode($basic_data);
				if (!empty($basic_data->qualifications)) {
					foreach ($basic_data->qualifications as $q) {
						$qualifications[] = $q->degree;
						$qualifications[] = $q->university;
					}
				}

				// Process patient data
				$patient_basic_data = json_decode(get_user_meta((int) $encounter->patient_id, 'basic_data', true));

				// Set patient details
				$encounter->patient_gender = !empty($patient_basic_data->gender) ?
					($patient_basic_data->gender === 'female' ? 'F' : 'M') : '';
				$encounter->patient_address = !empty($patient_basic_data->address) ? $patient_basic_data->address : '';
				$encounter->patient_city = !empty($patient_basic_data->city) ? $patient_basic_data->city : '';
				$encounter->patient_state = !empty($patient_basic_data->state) ? $patient_basic_data->state : '';
				$encounter->patient_country = !empty($patient_basic_data->country) ? $patient_basic_data->country : '';
				$encounter->patient_postal_code = !empty($patient_basic_data->postal_code) ? $patient_basic_data->postal_code : '';
				$encounter->contact_no = !empty($patient_basic_data->mobile_number) ? $patient_basic_data->mobile_number : '';

				// Combine address
				$encounter->patient_add = implode(',', array_filter([
					$encounter->patient_address,
					$encounter->patient_city,
					$encounter->patient_state,
					$encounter->patient_country,
					$encounter->patient_postal_code
				]));

				// Set current date and calculate age
				$encounter->date = current_time('Y-m-d');
				$encounter->patient_age = '';
				if (!empty($patient_basic_data->dob)) {
					$diff = abs(strtotime(current_time('Y-m-d')) - strtotime($patient_basic_data->dob));
					$years = floor($diff / (365 * 60 * 60 * 24));
					$months = floor(($diff - $years * 365 * 60 * 60 * 24) / (30 * 60 * 60 * 24));
					$days = floor(($diff - $years * 365 * 60 * 60 * 24 - $months * 30 * 60 * 60 * 24) / (60 * 60 * 24));

					if ($years > 0) {
						$encounter->patient_age = $years . esc_html__(' Years', 'kiviCare-clinic-&-patient-management-system-pro');
					} elseif ($months > 0) {
						$encounter->patient_age = $months . esc_html__(' Months', 'kiviCare-clinic-&-patient-management-system-pro');
					} else {
						$encounter->patient_age = $days . esc_html__(' Days', 'kiviCare-clinic-&-patient-management-system-pro');
					}
				}

				// Set other encounter details
				$encounter->qualifications = !empty($qualifications) ? '(' . implode(", ", $qualifications) . ')' : '';
				$encounter->clinic_address = "#{$encounter->clinic_id}, {$encounter->address}, {$encounter->city}";
				$default_clinic_log = KIVI_CARE_DIR_URI . 'assets/images/kc-demo-img.png';
				$encounter->clinic_logo = !empty($encounter->profile_image) ? wp_get_attachment_url($encounter->profile_image) : $default_clinic_log;

				wp_send_json([
					'data' => kcEncounterPrescriptionHtmlUpdated($encounter, $encounter_id, !empty($request_data['print_type']) ? $request_data['print_type'] : 'encounter'),
					'status' => true,
					'message' => esc_html__('Successfully print Consultation', 'kiviCare-clinic-&-patient-management-system-pro')
				]);
			}

			wp_send_json([
				'data' => '',
				'status' => false,
				'message' => esc_html__('No Data Found', 'kiviCare-clinic-&-patient-management-system-pro')
			]);

		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Failed to print', 'kiviCare-clinic-&-patient-management-system-pro')
			]);
		}
	}


	// summary functions
	private function get_system_message()
	{
		return "You are Symbi, an AI clinical assistant in the UK based on the SynapseX 1.0 model, designed to assist clinicians in their daily tasks. Your primary role is to help healthcare professionals by retrieving, summarizing, and organizing patient data from Electronic Health Records (EHRs), providing evidence-based clinical and medicolegal information, and supporting non-clinical workflows like appointment scheduling, transcription, and communications. You must ensure professionalism, empathy, and accuracy in all your responses. Under no circumstances should you hallucinate or fabricate information; always rely solely on data available from trusted sources.

Key Guidelines for Symbi:

Role and Boundaries:
- Healthcare Assistant, Not a Clinician:
  - Assist with healthcare tasks but do not provide clinical decisions or independent diagnoses.
  - Clarify that your suggestions or information support the clinician's decision-making.
- Final Decisions Belong to the Clinician:
  - Remind clinicians that the final decision lies with them.

Medicolegal Support:
- Proactive Medicolegal Guidance:
  - Provide relevant medicolegal information, such as guidelines on informed consent, documentation requirements, and compliance with healthcare regulations.
  - Prompt clinicians if an investigation, treatment, or documentation step may have been missed, highlighting potential medicolegal implications.
- Access to Up-to-Date Resources:
  - Reference current medicolegal guidelines and best practices.
  - Provide citations with accurate, working links when possible.

Function Calling:
- Trigger Functions When Needed:
  - Determine if a function needs to be called based on the user's request (e.g., sending emails, retrieving data).
  - Use the appropriate function aligned with the requested action.
- Ask for Clarification When Ambiguous:
  - If a request lacks detail, ask for additional information to ensure accuracy.
  - Example: If asked to send an email, confirm the recipient, subject, and body.

Evidence-Based Responses:
- Always Reference Trusted Sources:
  - Use only reputable sources like BNF, NICE guidelines, UpToDate, PubMed, or institutional EHR data.
  - Provide citations with working, accurate links wherever possible.
- Avoid Hallucination:
  - Do NOT fabricate information or provide answers based on assumptions.
  - Rely exclusively on actual patient data and trusted resources.

Medical Guidance and Queries:
- Patient Data Requests:
  - Pull only available EHR information when asked.
  - Provide clear, accurate summaries based solely on EHR data.
- Medical and Medicolegal Queries:
  - Cite official medical and legal guidelines when discussing conditions, treatments, or protocols.
  - Ensure all information is up-to-date and accurately sourced.
- Medication Interaction Information:
  - Provide drug interaction details from trusted sources.
  - Do not suggest medications or dosages.

Clinical Data and Transcription:
- Transcription of Medical Notes:
  - Transcribe audio files into editable text.
  - Note that it's a transcription service, not a finalized document.
- Summarizing Records and Notes:
  - Organize patient records and highlight key data without interpreting or making conclusions.
  - Derive information only from the integrated EHR.

Appointments and Scheduling:
- Assist with Scheduling:
  - Help schedule patient appointments.
  - Provide information on upcoming appointments via calendar or EHR integration.

Non-Healthcare-Related Queries:
- Stay Within Scope:
  - Do not answer unrelated questions.
  - Politely guide the user back to healthcare tasks.
- Suggested Response:
  - I'm sorry, but I am designed specifically for healthcare-related tasks. How may I assist you with your clinical work?

Guardrails:
- Prohibited Topics:
  - Do not engage with illegal activities, manipulative queries, violence, abuse, hate speech, or provide direct mental health interventions.
- Suggested Response for Out-of-Scope Requests:
  - I'm unable to assist with this request as it is outside my scope. If you have a healthcare-related task, I’m happy to help!

Professional Tone and Empathy:
- Maintain Professionalism:
  - Use a calm, empathetic tone.
- Handle Sensitive Data Carefully:
  - Display only requested information without oversharing.
- Crisis Situations:
  - Express empathy and guide clinicians toward appropriate protocols.
- Be Concise but Detailed:
  - Provide clear information without unnecessary details.
- Use of Humor:
  - Only use mild, appropriate humor when initiated by the user.

Handling Complex Conversations:
- Guide Through Multi-Step Queries:
  - Assist logically through each step.
- Role-Playing Requests:
  - Politely remind users of your functionality.
- Suggested Response:
  - I’m designed to assist you with real-time clinical tasks, but I’m happy to help with any genuine inquiries related to patient care or practice.

Integration with Tools, EHR, and Medicolegal Resources:
- EHR Integration:
  - Pull relevant patient data when requested.
  - Integrated with systems like SystmOne, Semble, TPP, EMIS.
  - Never make up information; rely solely on EHR data.
- Medicolegal Resources:
  - Access up-to-date legal guidelines and standards.
  - Provide accurate information on legal requirements in clinical practice.
- Third-Party APIs:
  - Communicate with APIs for transcription, scheduling, and documentation (e.g., Whisper API, Brevo).

Key Benefits of Symbi:
- Interoperability:
  - Integrates across healthcare systems for consistent access.
- Efficiency:
  - Reduces administrative time by automating tasks.
- Improved Decision Making:
  - Enhances clinician decisions with summarized data, guidelines, and medicolegal support.
- Enhanced Patient Care:
  - Allows clinicians to focus on patient outcomes.
- Medicolegal Risk Reduction:
  - Helps clinicians stay compliant with legal requirements, reducing negligence risks.
- Sensitive Data and Privacy Protection:
  - Follows data privacy regulations (e.g., GDPR, DPA 2018).
  - Retrieve and display patient records only when explicitly asked.
  - Never make assumptions; always ask for clarification if a request is ambiguous.

Importance of Accuracy and Reliability:
- Do Not Hallucinate or Fabricate Information:
  - Under no circumstances should you create or invent information.
- Verify All Information:
  - Ensure responses are accurate and based on verifiable data.
  - If uncertain, seek clarification or refer to authoritative sources.
- Communicate Uncertainties:
  - If information is unavailable or unclear, inform the clinician politely.
  - Suggest verifying with appropriate resources.


If clinicians email have + included, dont show the plus icon and other characters before @, for <NAME_EMAIL> in this case remove +11 and dont show (<NAME_EMAIL>). Just always show the formatted email <NAME_EMAIL>.

Your ultimate goal is to support clinicians by providing accurate, reliable, and helpful information, thereby enhancing patient safety, reducing the risk of medical negligence, and improving overall healthcare outcomes."; // Your full system message here
	}


	private function get_templates()
	{
		return array(

			"consultation_notes_template" => "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Consultation: [DD/MM/YYYY]\n\nPresenting Complaint:\n[Patient’s primary reason for the visit, e.g., “Patient reports chest pain for the last three days.”]\n\nHistory of Present Illness:\n[Detailed account of symptoms, duration, and severity, e.g., “Patient reports intermittent chest pain, radiating to the left arm, lasting for 5–10 minutes. Onset is gradual, and pain is relieved by rest.”]\n\nPast Medical History:\n[Relevant medical history, e.g., “Hypertension (diagnosed in 2019), Type 2 Diabetes (diagnosed in 2016)”]\nPast Surgical History:\n[Relevant surgical history, e.g., “Laparoscopy (performed in 2019)”]\nSocial History:\n[e.g., “Non-smoker, drinks alcohol occasionally, recently returned from travel to Spain, works as a teacher, lives alone.”]\nGynaecological History (for women):\n[e.g., “Menstruation regular, currently using oral contraception, last smear test in 2021, mother of two children.”]\n\nMedications:\n[Current medications, e.g., “Metformin 500mg twice daily, Lisinopril 10mg once daily”]\n\nAllergies:\n[Known allergies, e.g., “No known drug allergies”]\n\nExamination Findings:\nGeneral Appearance:\n[e.g., “Patient appears well, no acute distress, Height: [X] cm, Weight: [X] kg, BMI: [X].”]\n\nObservations:\nCardiovascular: [e.g., “Normal S1, S2, no murmurs”]\nRespiratory: [e.g., “Clear breath sounds bilaterally”]\nAbdominal: [e.g., “Soft, non-tender, no masses”]\nOthers (if applicable): [e.g., “No neurological deficits, normal gait”]\n\nImpression/Diagnosis:\n[e.g., “Likely angina pectoris, rule out myocardial infarction”]\n\nPlan:\nAdvice:\n[e.g., “Patient advised to avoid strenuous activity until further evaluation.”]\nInvestigations:\n[e.g., “Order ECG, complete blood count, lipid profile.”]\nTreatment:\n[e.g., “Start Aspirin 75mg daily, Nitroglycerin as needed.”]\nFollow-up:\n[e.g., “Review in two weeks or earlier if symptoms worsen.”]\nReferral to Another Speciality:\n[e.g., “Referral to cardiology for further evaluation.”]\n\nCopy to:\n[e.g., “Dr. [Specialist Name], Cardiologist, [Hospital Name]”]",
			"referral_letter_template" => "Receiving Specialist Information:\nName: [Specialist’s Name]\nSpecialty: [e.g., Cardiologist]\nPractice Address: [Specialist Clinic Address]\nContact Information: [Phone, Email]\n\nPatient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Referral: [DD/MM/YYYY]\n\nReason for Referral:\n[e.g., “Referral for further evaluation and management of suspected coronary artery disease.”]\n\nMedical History:\n[Summarise relevant medical history, e.g., “Patient with a 5-year history of hypertension, managed with Lisinopril 10mg daily.”]\n\nPresenting Symptoms:\n[e.g., “Patient presents with chest pain radiating to the left arm, onset three days ago, lasting 5–10 minutes per consultation.”]\n\nExamination Findings:\n[e.g., “Heart sounds are normal, no murmurs, clear breath sounds.”]\n\nInvestigations Done:\n[e.g., “ECG shows normal sinus rhythm, chest X-ray unremarkable.”]\n\nTreatment Given:\n[e.g., “Started on Aspirin 75mg and Nitroglycerin for chest pain relief.”]\n\nRequest for Specialist Input:\n[e.g., “Please evaluate for the possibility of coronary artery disease and advise further management, including the need for an angiogram.”]\n\nReferring Doctor Information:\nName: [Referring Doctor's Name]\nPractice Address: [Clinic Address]\nContact Information: [Phone, Email]",
			"gp_letter_template" => "Doctor Information:\nReferring Doctor: [Full Name]\nClinic: [Clinic Name]\nAddress: [Clinic Address]\nContact Information: [Phone, Email]\n\nPatient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Letter: [DD/MM/YYYY]\n\nSummary of Current Visit:\n[e.g., “Patient attended cardiology clinic on DD/MM/YYYY for evaluation of chest pain. Investigation results suggest mild coronary artery disease.”]\n\nBackground Medical History:\n[e.g., “Patient has a history of hypertension (diagnosed in 2017), Type 2 diabetes (diagnosed in 2018), and hyperlipidaemia (diagnosed in 2020).”]\n\nPlan:\nAdvice:\n[e.g., “Patient advised to avoid strenuous activity until further evaluation.”]\nInvestigations:\n[e.g., “Order ECG, complete blood count, lipid profile.”]\nTreatment:\n[e.g., “The patient has been started on Aspirin 75mg daily and Metoprolol 25mg twice daily.”]\nFollow-up:\n[e.g., “Review in two weeks or earlier if symptoms worsen.”]\nReferral to Another Speciality:\n[e.g., “Referral to cardiology for further evaluation.”]\n\nFollow-Up Recommendations:\n[e.g., “Patient to be reviewed by the GP in 4 weeks to assess response to treatment and any adverse effects.”]",
			"sick_medical_leave_note_template" => "Doctor Information:\nName: [Doctor's Full Name]\nClinic: [Clinic Name]\nContact Information: [Phone, Email]\n\nPatient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Issue: [DD/MM/YYYY]\n\nMedical Condition:\n[e.g., “Patient is suffering from acute bronchitis and is currently unable to attend work/study.”]\n\nRecommended Sick Leave:\n[e.g., “The patient requires rest and is advised to take 5 days of sick leave from work/study.”]\nStarting from Date: [DD/MM/YYYY]\nDuration: [e.g., “5 days”]\n\nReturn to Work/Study Date:\n[e.g., “Patient can return to work on DD/MM/YYYY, pending recovery.”]\n\nReturn to Work/Study Recommendations:\nFull return\nPhased return\nAdjustments at workplace or place of study: [e.g., “Light duties recommended for two weeks.”]",
			"operation_notes_procedure_report_template" => "Hospital Information:\nHospital Name: [Hospital Name]\nAddress: [Hospital Address]\nContact Information: [Phone, Email]\n\nPatient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Procedure: [DD/MM/YYYY]\n\nSurgeon:\nName: [Primary Surgeon’s Name]\nAssistants: [Names of any assisting personnel]\n\nAnaesthetist:\n[Name of Anaesthetist]\n\nProcedure:\n[e.g., “Coronary Angiography”]\n\nProcedure Codes:\n[Relevant procedure codes for billing or record-keeping purposes]\n\nIndication/Pre-Operative Diagnosis:\n[e.g., “Suspected coronary artery disease”]\n\nFindings:\n[e.g., “No significant occlusions, mild stenosis in the left anterior descending artery.”]\n\nProcedure Details:\n[Detailed description of the procedure, e.g., “The right radial artery was accessed, and a catheter was advanced to the coronary arteries. Contrast dye was injected, revealing 40% stenosis in the left anterior descending artery.”]\n\nComplications:\n[e.g., “None.”]\n\nPost-Op Plan:\n[e.g., “Home today, Clexane for 2 weeks, staples to be removed in 10 days.”]\n\nFollow-Up Plan:\n[e.g., “Patient to follow up with cardiology for medical management of coronary artery disease.”]",
			"clinic_follow_up_ward_rounds_notes_template" => "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Visit: [DD/MM/YYYY]\nWard/Clinic: [Ward/Clinic Name] (if applicable)\n\nPresenting Complaint:\n[e.g., “Patient continues to experience mild chest pain with exertion.”]\n\nHistory Since Last Review:\n[e.g., “Patient reports improvement in symptoms, no new complaints since the last visit.”]\n\nExamination Findings:\nGeneral Appearance: [e.g., “Patient appears well, no acute distress.”]\nCardiovascular: [e.g., “Heart sounds remain normal, no murmurs.”]\nRespiratory: [e.g., “Lungs clear on auscultation.”]\nAbdominal: [e.g., “Soft, non-tender, no masses.”]\nOther Systems (if applicable): [e.g., “No neurological deficits noted.”]\n\nInvestigations:\n[e.g., “ECG normal, blood tests pending.”]\n\nUpdates on Treatment:\n[e.g., “Patient reports no side effects from Metoprolol, but chest pain persists occasionally.”]\n\nPlan:\nTreatment Adjustments: [e.g., “Increase Metoprolol to 50mg daily.”]\nAdvice: [e.g., “Patient advised to continue current medications and avoid strenuous activity.”]\nFurther Investigations: [e.g., “Arrange for repeat blood tests in two weeks.”]\nReferral: [e.g., “Refer to cardiology for further evaluation.”]\nFollow-Up: [e.g., “Review in two weeks or earlier if symptoms worsen.”]",
			"patient_admission_clerking_template" => "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Consultation: [DD/MM/YYYY]\n\nPresenting Complaint:\n[Patient’s primary reason for the visit, e.g., “Patient reports chest pain for the last three days.”]\n\nHistory of Present Illness:\n[Detailed account of symptoms, duration, and severity, e.g., “Patient reports intermittent chest pain, radiating to the left arm, lasting for 5–10 minutes. Onset is gradual, and pain is relieved by rest.”]\n\nPast Medical History:\n[Relevant medical history, e.g., “Hypertension (diagnosed in 2019), Type 2 Diabetes (diagnosed in 2016)”]\nPast Surgical History:\n[Relevant surgical history, e.g., “Laparoscopy (performed in 2019)”]\nSocial History:\n[e.g., “Non-smoker, drinks alcohol occasionally, recently returned from travel to Spain, works as a teacher, lives alone.”]\nGynaecological History (for women):\n[e.g., “Menstruation regular, currently using oral contraception, last smear test in 2021, mother of two children.”]\n\nMedications:\n[Current medications, e.g., “Metformin 500mg twice daily, Lisinopril 10mg once daily”]\n\nAllergies:\n[Known allergies, e.g., “No known drug allergies”]\n\nExamination Findings:\nGeneral Appearance:\n[e.g., “Patient appears well, no acute distress, Height: [X] cm, Weight: [X] kg, BMI: [X].”]\n\nObservations:\nCardiovascular: [e.g., “Normal S1, S2, no murmurs”]\nRespiratory: [e.g., “Clear breath sounds bilaterally”]\nAbdominal: [e.g., “Soft, non-tender, no masses”]\nOthers (if applicable): [e.g., “No neurological deficits, normal gait”]\n\nImpression/Diagnosis:\n[e.g., “Likely angina pectoris, rule out myocardial infarction”]\n\nInitial Plan:\nAdvice:\n[e.g., “Patient advised to avoid strenuous activity until further evaluation.”]\nInvestigations:\n[e.g., “Order ECG, complete blood count, lipid profile.”]\nTreatment:\n[e.g., “Start Aspirin 75mg daily, Nitroglycerin as needed.”]\nFollow-up:\n[e.g., “Review in two weeks or earlier if symptoms worsen.”]\nReferral to Another Speciality:\n[e.g., “Referral to cardiology for further evaluation.”]",
			"ward_rounds_notes_template" => "Patient Information:\nName: [Full Name]\nDate of Birth: [DD/MM/YYYY]\nAddress: [Patient Address]\nNHS Number: [NHS/ID Number]\nDate of Visit: [DD/MM/YYYY]\nWard/Clinic: [Ward/Clinic Name] (if applicable)\n\nPresenting Complaint:\n[e.g., “Patient continues to experience mild chest pain with exertion.”]\n\nHistory Since Last Review:\n[e.g., “Patient reports improvement in symptoms, no new complaints since the last visit.”]\n\nExamination Findings:\nGeneral Appearance: [e.g., “Patient appears well, no acute distress.”]\nCardiovascular: [e.g., “Heart sounds remain normal, no murmurs.”]\nRespiratory: [e.g., “Lungs clear on auscultation.”]\nAbdominal: [e.g., “Soft, non-tender, no masses.”]\nOther Systems (if applicable): [e.g., “No neurological deficits noted.”]\n\nInvestigations:\n[e.g., “ECG normal, blood tests pending.”]\n\nUpdates on Treatment:\n[e.g., “Patient reports no side effects from Metoprolol, but chest pain persists occasionally.”]\n\nPlan:\nTreatment Adjustments: [e.g., “Increase Metoprolol to 50mg daily.”]\nAdvice: [e.g., “Patient advised to continue current medications and avoid strenuous activity.”]\nFurther Investigations: [e.g., “Arrange for repeat blood tests in two weeks.”]\nReferral: [e.g., “Refer to cardiology for further evaluation.”]\nFollow-Up: [e.g., “Review in two weeks or earlier if symptoms worsen.”]"
		);
	}


	public function handleGenerateSummary()
	{
		try {
			error_log('handleGenerateSummary: Starting summarization process');

			$request_data = $this->request->getInputs();
			error_log('Request data: ' . print_r($request_data, true));

            // Check for user templates first
            if (!empty($request_data['isUserTemplate']) && !empty($request_data['selectedTemplate']) && !empty($request_data['templateContent'])) {
                error_log('Processing user template: ' . $request_data['selectedTemplate'] . ' with content length: ' . strlen($request_data['templateContent']));

                // Apply template filter - this calls process_user_template in MDTemplateManagerController
                $data = apply_filters('kivicare_encounter_summarize', [], $request_data);

                // Check if the template content was processed successfully
                if (empty($data['template_content'])) {
                    error_log('No template content returned from filter. Raw template content: ' . substr($request_data['templateContent'], 0, 100) . '...');

                    // Fallback - if filter didn't work, try direct variable replacement
                    if (!empty($request_data['templateContent'])) {
                        // If MDTemplateManagerController::process_user_template didn't work,
                        // we still have the raw template content as a fallback
                        $template_content = $request_data['templateContent'];

                        // Implement simple variable replacement directly
                        $replacements = [];

                        // Patient variables
                        if (!empty($request_data['patient_details'])) {
                            $patient = $request_data['patient_details'];
                            $replacements['${patient.name}'] = $patient['patient_name'] ?? '';
                            $replacements['${patient.unique_id}'] = $patient['patient_unique_id'] ?? '';
                            $replacements['${patient.dob}'] = $patient['dob'] ?? '';
                            $replacements['${patient.gender}'] = $patient['gender'] ?? '';
                            $replacements['${patient.email}'] = $patient['patient_email'] ?? '';
                            $replacements['${patient.mobile_number}'] = $patient['mobile_number'] ?? '';

                            // Calculate age if DOB is available
                            if (!empty($patient['dob'])) {
                                $dob = new \DateTime($patient['dob']);
                                $now = new \DateTime();
                                $interval = $now->diff($dob);
                                $replacements['${patient.age}'] = $interval->y;
                            }
                        }

                        // Doctor variables
                        if (!empty($request_data['doctor_id'])) {
                            $doctor_id = $request_data['doctor_id'];
                            $doctor = get_userdata($doctor_id);
                            $replacements['${doctor.name}'] = $doctor ? $doctor->display_name : '';
                            $replacements['${doctor.speciality}'] = get_user_meta($doctor_id, 'specialties', true) ?? '';
                            $replacements['${doctor.qualification}'] = get_user_meta($doctor_id, 'qualifications', true) ?? '';
                        }

                        // Consultation data
                        if (!empty($request_data['consultationData'])) {
                            $consultation = $request_data['consultationData'];
                            // Handle array values properly
                            $replacements['${encounter.concerns}'] = is_array($consultation['concerns']) ? $this->formatArrayContent($consultation['concerns']) : ($consultation['concerns'] ?? '');
                            $replacements['${encounter.history}'] = is_array($consultation['history']) ? $this->formatArrayContent($consultation['history']) : ($consultation['history'] ?? '');
                            $replacements['${encounter.examination}'] = is_array($consultation['examination']) ? $this->formatArrayContent($consultation['examination']) : ($consultation['examination'] ?? '');
                            $replacements['${encounter.diagnosis}'] = is_array($consultation['diagnosis']) ? $this->formatArrayContent($consultation['diagnosis']) : ($consultation['diagnosis'] ?? '');
                            $replacements['${encounter.plan}'] = is_array($consultation['plan']) ? $this->formatArrayContent($consultation['plan']) : ($consultation['plan'] ?? '');
                        }

                        // Get encounter tabs data for medical_history, allergies, medications, safeguarding, etc.
                        if (!empty($request_data['encounter_id'])) {
                            $encounter_id = (int)$request_data['encounter_id'];
                            error_log('Getting encounter tabs data for encounter ID: ' . $encounter_id);

                            $tab_fields = ['medical_history', 'allergies', 'medications', 'safeguarding', 'family_history',
                                          'social_history', 'lifestyle', 'mental_health', 'preventative_care',
                                          'safety_netting', 'systems_review', 'notes', 'comments'];

                            foreach ($tab_fields as $field) {
                                // Get the tab content from the encounter_tabs table
                                $query = $this->db->prepare(
                                    "SELECT content FROM {$this->db->prefix}kc_encounter_tabs WHERE encounter_id = %d AND type = %s ORDER BY created_at ASC",
                                    $encounter_id,
                                    $field
                                );

                                error_log("Encounter tab query for {$field}: {$query}");

                                $tab_content = $this->db->get_results($query);

                                error_log("Found " . count($tab_content) . " tabs for {$field}");

                                if (!empty($tab_content)) {
                                    // Format the content from all tabs of this type
                                    $formatted_content = '';
                                    foreach ($tab_content as $tab) {
                                        if (!empty($tab->content)) {
                                            $formatted_content .= $tab->content . "\n\n";
                                            error_log("Added tab content for {$field}: " . substr($tab->content, 0, 50) . "...");
                                        }
                                    }
                                    $replacements['${encounter.' . $field . '}'] = trim($formatted_content);
                                    error_log("Set replacement for encounter.{$field} with content length: " . strlen(trim($formatted_content)));
                                }
                            }
                        } else {
                            error_log('No encounter_id found in request data');
                            // Check if encounterId is available instead
                            if (!empty($request_data['encounterId'])) {
                                $request_data['encounter_id'] = $request_data['encounterId'];
                                error_log('Using encounterId instead: ' . $request_data['encounter_id']);
                                // Recursively call this section with the updated request_data
                                // This is a simplified approach - in a real implementation, you might want to extract this logic to a separate method
                            }
                        }

                        // Date variables
                        $replacements['${date}'] = date('d/m/Y');
                        $replacements['${time}'] = date('H:i');

                        // Log all replacements for debugging
                        error_log('Replacements to be applied: ' . print_r(array_keys($replacements), true));

                        // Replace all variables in the template content
                        $template_content = str_replace(array_keys($replacements), array_values($replacements), $template_content);

                        // Log the final template content
                        error_log('Final template content (first 200 chars): ' . substr($template_content, 0, 200));

                        wp_send_json([
                            'status' => true,
                            'message' => esc_html__('Summary generated successfully using fallback method', 'kc-lang'),
                            'summary' => $template_content
                        ]);
                        return;
                    }

                    // If we reach here, both methods failed
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to process template', 'kc-lang')
                    ]);
                }

                error_log('Template processed successfully, returning summary');

                // Return the processed template content
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Summary generated successfully', 'kc-lang'),
                    'summary' => $data['template_content']
                ]);

                return; // Stop execution here
            }

			// Fall back to original method for system templates
			$selected_template = sanitize_text_field($request_data['selectedTemplate']);
			$consultation_data = $request_data['consultationData'];

			// Validate template
			$templates = $this->get_templates();
			if (!isset($templates[$selected_template])) {
				return new WP_Error(
					'invalid_template',
					'Invalid template selected',
					array('status' => 400)
				);
			}

			// Format consultation data for the prompt
			$formatted_data = $this->format_consultation_data($consultation_data);
			$prompt = sprintf(
				"Based on the following clinical information, generate a summary according to the template structure:\n\nClinical Information:\n%s\n\nTemplate structure:\n%s",
				$formatted_data,
				$templates[$selected_template]
			);

			// Call Mistral API
			$response = wp_remote_post(
				$this->mistral_api_url,
				array(
					'headers' => array(
						'Content-Type' => 'application/json',
						'Authorization' => 'Bearer vZabYNGkXr4F3XNidMLxH9QjZsYSH6a7'
					),
					'body' => json_encode(array(
						'model' => $this->model,
						'messages' => array(
							array(
								'role' => 'system',
								'content' => $this->get_system_message()
							),
							array(
								'role' => 'user',
								'content' => $prompt
							)
						),
						'temperature' => 0,
						'max_tokens' => 8000
					)),
					'timeout' => 60
				)
			);

			if (is_wp_error($response)) {
				return new WP_Error(
					'api_error',
					'Failed to connect to Mistral API: ' . $response->get_error_message(),
					array('status' => 500)
				);
			}

			$body = json_decode(wp_remote_retrieve_body($response), true);

			if (!isset($body['choices'][0]['message']['content'])) {
				return new WP_Error(
					'api_error',
					'Invalid response from Mistral API',
					array('status' => 500)
				);
			}

			return wp_send_json([
				'summary' => $body['choices'][0]['message']['content'],
			]);

		} catch (Exception $e) {
			error_log('Error in handleGenerateSummary: ' . $e->getMessage());
			error_log('Error trace: ' . $e->getTraceAsString());

			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	private function format_consultation_data($request_data)
	{
		// Get encounter ID and basic data
		$encounter_id = isset($request_data['encounter_id']) ? (int) $request_data['encounter_id'] : 0;

		if (!$encounter_id) {
			return '';
		}

		// Get encounter details
		$encounter = $this->getEncounterData($encounter_id);
		if (empty($encounter)) {
			return '';
		}
		$patient_user_meta = json_decode(get_user_meta($encounter->patient_id, 'basic_data', true) ?? '{}', true);

		$patient_dob = $patient_user_meta['dob'] ?? '';
		$patient_nhs_no = $patient_user_meta['nhs'] ?? '-';
		$patient_address = $patient_user_meta['address'] ?? '';
		$patient_postal_code = $patient_user_meta['postal_code'] ?? '';

		$formatted = "PATIENT INFORMATION:\n";
		$formatted .= "Name: {$encounter->patient_name}\n";
		$formatted .= "Email: {$encounter->patient_email}\n";
		$formatted .= "Date: " . kcGetFormatedDate($encounter->encounter_date) . "\n\n";
		$formatted .= "Date of Birth: {$patient_dob}\n";
		$formatted .= "Address: " . (empty($patient_address) && empty($patient_postal_code) ? '-' : $patient_address . ", " . $patient_postal_code) . "\n";
		$formatted .= "NHS Number: {$patient_nhs_no}\n";

		$doctor_user_meta = json_decode(get_user_meta($encounter->doctor_id, 'basic_data', true) ?? '{}', true);

		$formatted .= "CLINIC INFORMATION:\n";
		$formatted .= "Clinic: {$encounter->clinic_name}\n";
		$formatted .= "Doctor: {$encounter->doctor_name}\n\n";
		$formatted .= "Doctor Address: {$doctor_user_meta['address']}\n\n";
		$formatted .= "Doctor Contact Information: {$encounter->doctor_email} \n\n";

		// Get encounter tabs
		$tabs_query = $this->db->prepare(
			"SELECT * FROM {$this->db->prefix}kc_encounter_tabs WHERE encounter_id = %d ORDER BY created_at ASC",
			$encounter_id
		);
		$tabs = $this->db->get_results($tabs_query);

		// Format Problems
		if (!empty($request_data['concerns'])) {
			$formatted .= "PROBLEMS:\n";
			foreach ($request_data['concerns'] as $problem) {
				// $formatted .= "- " . sanitize_text_field($problem['title']) . "\n";
				if (!empty($problem['content'])) {
					$formatted .= "  Details: " . sanitize_textarea_field($problem['content']) . "\n";
				}
			}
			$formatted .= "\n";
		}

		// Format Problems
		if (!empty($request_data['plan'])) {
			$formatted .= "PLAN:\n";
			foreach ($request_data['plan'] as $problem) {
				// $formatted .= "- " . sanitize_text_field($problem['title']) . "\n";
				if (!empty($problem['content'])) {
					$formatted .= "  Details: " . sanitize_textarea_field($problem['content']) . "\n";
				}
			}
			$formatted .= "\n";
		}

		// Format Observations
		if (!empty($request_data['examination'])) {
			$formatted .= "OBSERVATIONS:\n";
			foreach ($request_data['observations'] as $observation) {
				// $formatted .= "- " . sanitize_text_field($observation['title']) . ": ";
				// $formatted .= sanitize_text_field($observation['value']) . "\n";
				if (!empty($observation['content'])) {
					$formatted .= "  Details: " . sanitize_textarea_field($observation['content']) . "\n";
				}
			}
			$formatted .= "\n";
		}

		// Get and format medical history
		$medical_history_table = $this->db->prefix . 'kc_encounter_tabs';
		$medical_history = $this->db->get_results($this->db->prepare(
			"SELECT * FROM {$medical_history_table} WHERE encounter_id = %d",
			$encounter_id
		));

		if (!empty($medical_history)) {
			$formatted .= "MEDICAL HISTORY:\n";
			foreach ($medical_history as $history) {
				$formatted .= "- " . sanitize_text_field($history->title) . "\n";
				if (!empty($history->description)) {
					$formatted .= "  " . sanitize_textarea_field($history->description) . "\n";
				}
			}
			$formatted .= "\n";
		}

		// Get and format prescription data
		$prescription_table = $this->db->prefix . 'kc_prescription';
		$prescriptions = $this->db->get_results($this->db->prepare(
			"SELECT * FROM {$prescription_table} WHERE encounter_id = %d",
			$encounter_id
		));

		if (!empty($prescriptions)) {
			$formatted .= "PRESCRIPTION:\n";
			foreach ($prescriptions as $prescription) {
				$formatted .= "- Medicine: " . sanitize_text_field($prescription->name) . "\n";
				$formatted .= "  Frequency: " . sanitize_text_field($prescription->frequency) . "\n";
				$formatted .= "  Duration: " . sanitize_text_field($prescription->duration) . "\n";
				if (!empty($prescription->instruction)) {
					$formatted .= "  Instructions: " . sanitize_textarea_field($prescription->instruction) . "\n";
				}
			}
			$formatted .= "\n";
		}

		// Format Clinical Notes
		if (!empty($request_data['notes'])) {
			$formatted .= "CLINICAL NOTES:\n";
			foreach ($request_data['notes'] as $note) {
				$formatted .= "- " . sanitize_textarea_field($note['note']) . "\n";
			}
			$formatted .= "\n";
		}

		// Format Additional Tabs
		if (!empty($tabs)) {
			$formatted .= "ADDITIONAL INFORMATION:\n";
			foreach ($tabs as $tab) {
				$formatted .= "- " . sanitize_text_field($tab->type) . ":\n";
				$formatted .= "  " . sanitize_textarea_field($tab->content) . "\n\n";
			}
		}

		return $formatted;
	}





	// Add these methods to KCPatientEncounterController class

	/**
	 * Save encounter tab data
	 * Handles saving of tab content and metadata for an encounter
	 */
	public function saveEncounterTab()
	{
		try {
			// Verify permissions
			if (!kcCheckPermission('patient_encounter_add')) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$request_data = $this->request->getInputs();

			// Validate required fields
			if (empty($request_data['encounter_id']) || empty($request_data['type']) || empty($request_data['content'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Missing required fields', 'kc-lang')
				]);
			}

			// Verify encounter access permission
			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['encounter_id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$encounter_id = (int) $request_data['encounter_id'];
			$tab_id = !empty($request_data['tab_id']) ? (int) $request_data['tab_id'] : 0;

			// Prepare tab data
			$tab_data = [
				'encounter_id' => $encounter_id,
				'type' => sanitize_text_field($request_data['type']),
				'content' => sanitize_textarea_field($request_data['content']),
				'metadata' => !empty($request_data['metadata']) ? json_encode($request_data['metadata']) : '{}',
				'updated_at' => current_time('Y-m-d H:i:s')
			];

			// Insert or update tab
			if ($tab_id > 0) {
				// Update existing tab
				$result = $this->db->update(
					$this->db->prefix . 'kc_encounter_tabs',
					$tab_data,
					['id' => $tab_id]
				);
			} else {
				// Insert new tab
				$tab_data['created_at'] = current_time('Y-m-d H:i:s');
				$result = $this->db->insert(
					$this->db->prefix . 'kc_encounter_tabs',
					$tab_data
				);
				$tab_id = $this->db->insert_id;
			}

			if ($result === false) {
				throw new Exception(esc_html__('Failed to save tab data', 'kc-lang'));
			}

			wp_send_json([
				'status' => true,
				'message' => esc_html__('Tab saved successfully', 'kc-lang'),
				'data' => [
					'tab_id' => $tab_id
				]
			]);

		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}



	/**
	 * Get encounter tabs
	 * Retrieves all tabs associated with an encounter
	 *
	 * @return void
	 */
	public function getEncounterTabs()
	{
		try {
			// Get request data
			$request_data = $this->request->getInputs();

			// Validate encounter_id
			if (empty($request_data['encounter_id'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Consultation ID is required', 'kc-lang')
				]);
			}

			$encounter_id = (int) $request_data['encounter_id'];

			// Check if encounter exists
			$encounter = (new KCPatientEncounter())->get_by(['id' => $encounter_id], '=', true);
			if (empty($encounter)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Consultation not found', 'kc-lang')
				]);
			}

			// Verify encounter access permission
			if (!((new KCPatientEncounter())->encounterPermissionUserWise($encounter_id))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			// Get table name with proper prefix
			$table_name = $this->db->prefix . 'kc_encounter_tabs';

			// Check if table exists
			$table_exists = $this->db->get_var(
				$this->db->prepare("SHOW TABLES LIKE %s", $table_name)
			);

			if (!$table_exists) {
				// Create table if it doesn't exist
				$charset_collate = $this->db->get_charset_collate();
				$sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `encounter_id` bigint(20) UNSIGNED NOT NULL,
                `type` varchar(50) NOT NULL,
                `content` text NOT NULL,
                `metadata` json DEFAULT NULL,
                `created_at` datetime NOT NULL,
                `updated_at` datetime NOT NULL,
                PRIMARY KEY (`id`),
                KEY `encounter_id` (`encounter_id`)
            ) {$charset_collate};";

				require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
				dbDelta($sql);
			}

			// Get tabs from database with error checking
			$query = $this->db->prepare(
				"SELECT * FROM {$table_name} WHERE encounter_id = %d ORDER BY created_at ASC",
				$encounter_id
			);

			$tabs = $this->db->get_results($query);

			// Check for database errors
			if ($this->db->last_error) {
				throw new Exception($this->db->last_error);
			}

			// Initialize empty array if no tabs found
			if (empty($tabs)) {
				$tabs = [];
			}

			// Process tabs data
			$processed_tabs = array_map(function ($tab) {
				// Handle metadata safely
				if (!empty($tab->metadata)) {
					try {
						$tab->metadata = json_decode($tab->metadata, true);
						if (json_last_error() !== JSON_ERROR_NONE) {
							$tab->metadata = [];
						}
					} catch (Exception $e) {
						$tab->metadata = [];
					}
				} else {
					$tab->metadata = [];
				}
				return $tab;
			}, $tabs);

			wp_send_json([
				'status' => true,
				'message' => esc_html__('Tabs retrieved successfully', 'kc-lang'),
				'data' => $processed_tabs
			]);

		} catch (Exception $e) {
			// Log the error for debugging
			error_log('Consultation Tabs Error: ' . $e->getMessage());

			wp_send_json([
				'status' => false,
				'message' => esc_html__('Failed to retrieve tabs. Please check error logs.', 'kc-lang'),
				'debug_message' => (WP_DEBUG) ? $e->getMessage() : null
			]);
		}
	}

	/**
	 * Delete encounter tab
	 * Removes a specific tab from an encounter
	 */
	public function deleteEncounterTab()
	{
		try {
			// Verify permissions
			if (!kcCheckPermission('patient_encounter_add')) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$request_data = $this->request->getInputs();

			if (empty($request_data['encounter_id']) || empty($request_data['tab_id'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Missing required fields', 'kc-lang')
				]);
			}

			// Verify encounter access permission
			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['encounter_id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$result = $this->db->delete(
				$this->db->prefix . 'kc_encounter_tabs',
				[
					'id' => (int) $request_data['tab_id'],
					'encounter_id' => (int) $request_data['encounter_id']
				]
			);

			if ($result === false) {
				throw new Exception(esc_html__('Failed to delete tab', 'kc-lang'));
			}

			wp_send_json([
				'status' => true,
				'message' => esc_html__('Tab deleted successfully', 'kc-lang')
			]);

		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function mailEncounter()
	{
		try {
			$request_data = $this->request->getInputs();
			$encounter_table = $this->db->prefix . 'kc_patient_encounters';
			$clinics_table = $this->db->prefix . 'kc_clinics';
			$users_table = $this->db->base_prefix . 'users';

			if (empty($request_data['encounter_id'])) {
				throw new Exception(esc_html__('Consultation ID is required', 'kc-lang'));
			}

			$encounter_id = (int) $request_data['encounter_id'];

			if (!((new KCPatientEncounter())->encounterPermissionUserWise($encounter_id))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			// patients.dob AS patient_dob
			$query = "SELECT {$encounter_table}.*,
                doctors.display_name AS doctor_name,
                doctors.user_email AS doctor_email,
                patients.display_name AS patient_name,
                patients.user_email AS patient_email,
                {$clinics_table}.*
            FROM {$encounter_table}
            LEFT JOIN {$users_table} doctors ON {$encounter_table}.doctor_id = doctors.id
            LEFT JOIN {$users_table} patients ON {$encounter_table}.patient_id = patients.id
            LEFT JOIN {$clinics_table} ON {$encounter_table}.clinic_id = {$clinics_table}.id
            WHERE {$encounter_table}.id = {$encounter_id}";

			$encounter = $this->db->get_row($query);

			$precriptionResults = $this->db->get_results("SELECT pre.* ,enc.*
                    FROM {$this->db->prefix}kc_prescription AS pre
                    JOIN {$encounter_table} AS enc ON enc.id=pre.encounter_id
                    WHERE pre.encounter_id={$encounter_id}");

			if (empty($encounter)) {
				throw new Exception(esc_html__('Consultation not found', 'kc-lang'));
			}


			$template = include(KIVI_CARE_DIR . 'templates/Consultation.php');
			$dompdf = new Dompdf(array('enable_remote' => true));
			$dompdf->set_option('isHtml5ParserEnabled', true);
			$dompdf->loadHtml($template);
			$dompdf->setPaper('A4', 'portrait');
			$dompdf->render();

			$pdf_content = $dompdf->output();

			// Base filename without extension
			$file_name_base = 'consultation-' . $request_data['encounter_id'];
			$file_extension = '.pdf';
			$upload_dir = wp_upload_dir();
			$file_path = $upload_dir['path'] . '/' . $file_name_base . $file_extension;

		$counter = 1;
		while (file_exists($file_path)) {
			$file_name = $file_name_base . '-' . $counter . $file_extension;
			$file_path = $upload_dir['path'] . '/' . $file_name;
			$counter++;
		}

			// Save the PDF content to a temporary file
			if (file_put_contents($file_path, $pdf_content)) {

				$data = [
					'user_email' => $encounter->patient_email,
					'patient_name' => $encounter->patient_name,
					'patient_dob' => $encounter->patient_dob,
					'consultation_date' => $encounter->encounter_date,
					'doctor_email' => $encounter->doctor_email,
					'doctor_name' => $encounter->doctor_name,
					'doctor_contact_number' => kcGetUserValueByKey('doctor', $encounter->doctor_id, 'mobile_number'),
					'clinic_name' => $encounter->name,
					'clinic_email' => $encounter->email,
					'clinic_contact_number' => $encounter->telephone_no,
					'clinic_address' => implode(', ', array_filter([$encounter->address, $encounter->city, $encounter->country])),
					'prescription_name' => $precriptionResults->name,
					'prescription_frequency' => $precriptionResults->frequency,
					'prescription_duration' => $precriptionResults->duration,
					'prescription_instruction' => $precriptionResults->instruction,
					'email_template_type' => 'medroid_episode_mail',
					'attachment_file' => $file_path,
					'attachment' => true,
				];
			}
			die;

			$status = MedroidSendEmail($data);

			wp_send_json([
				'status' => $status,
				'message' => $status ? esc_html__('Consultation sent successfully.', 'kc-lang')
					: esc_html__('Failed to send email', 'kc-lang')
			]);

		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}
	public function getPatientsByEmail()
	{
		$request_data = $this->request->getInputs();

		global $wpdb;

		// Escaping the email pattern for security
		$email_pattern = esc_sql($request_data['search']);

		// Querying the database for users whose email matches the pattern
		$query = $wpdb->prepare("
			SELECT user_email
			FROM {$wpdb->users}
			WHERE user_email LIKE %s
		", '%' . $email_pattern . '%');

		$results = $wpdb->get_col($query);

		if (empty($results)) {
			wp_send_json([
				'status' => false,
				'data' => [],
			]);
		}
		wp_send_json([
			'status' => true,
			'data' => $results,
		]);
	}

	public function sendDocumentSummeryTo()
	{
		$request_data = $this->request->getInputs();

		error_log("Send Document: Starting with data: " . json_encode($request_data));

		// Get encryption preference (default to true if not specified)
		$encrypt_document = isset($request_data['encrypt_document']) ? (bool)$request_data['encrypt_document'] : true;
		error_log("Send Document: Encryption preference: " . ($encrypt_document ? 'With password' : 'Without password'));

		// Debug the entire request structure
		error_log("Send Document: Complete request data structure:");
		foreach ($request_data as $key => $value) {
			if (is_array($value) || is_object($value)) {
				error_log("Send Document: Key '{$key}' = " . json_encode($value));
			} else {
				error_log("Send Document: Key '{$key}' = {$value}");
			}
		}

		// Get attachment details from request - handle both possible structures
		$attachment_id = null;
		$encounter_id = null;
		$document_name = '';

		if (isset($request_data['doc'])) {
			if (isset($request_data['doc']['attachment_id'])) {
				$attachment_id = $request_data['doc']['attachment_id'];
			} elseif (isset($request_data['doc']['document_id'])) {
				$attachment_id = $request_data['doc']['document_id'];
			} elseif (isset($request_data['doc']['id'])) {
				$attachment_id = $request_data['doc']['id'];
			}

			if (isset($request_data['doc']['encounter_id'])) {
				$encounter_id = $request_data['doc']['encounter_id'];
			} elseif (isset($request_data['doc']['appointment_id'])) {
				$encounter_id = $request_data['doc']['appointment_id'];
			}

			if (isset($request_data['doc']['name'])) {
				$document_name = $request_data['doc']['name'];
			} elseif (isset($request_data['doc']['title'])) {
				$document_name = $request_data['doc']['title'];
			}
		}

		error_log("Send Document: Extracted values - attachment_id: {$attachment_id}, encounter_id: {$encounter_id}, name: {$document_name}");

		// Validate inputs
		if (empty($attachment_id)) {
			wp_send_json(['status' => false, 'message' => 'Document ID is required']);
			return;
		}

		if (empty($request_data['emails']) || !is_array($request_data['emails'])) {
			wp_send_json(['status' => false, 'message' => 'Recipient email(s) are required']);
			return;
		}

		error_log("Send Document: Looking up document ID: " . $attachment_id);

		// APPROACH 1: First, try to get document details from the patient encounters summary documents table
		global $wpdb;
		$document_path = get_attached_file($attachment_id);

			// Try to get patient ID from encounter if available
			if (!empty($encounter_id)) {
				$encounter_detail = (new KCPatientEncounter())->get_by(['id' => (int)$encounter_id], '=', true);
				if ($encounter_detail) {
					$patient_id = $encounter_detail->patient_id;
				}
			}

		// Final attempt - try to look up the attachment URL and convert to path
		if (empty($document_path) || !file_exists($document_path)) {
			$url = wp_get_attachment_url($wp_attachment_id);
			if ($url) {
				error_log("Send Document: Got attachment URL: " . $url);
				$upload_dir = wp_upload_dir();
				$file_path_part = str_replace($upload_dir['baseurl'], '', $url);
				$potential_path = $upload_dir['basedir'] . $file_path_part;

				error_log("Send Document: Converted URL to potential path: " . $potential_path);
				if (file_exists($potential_path)) {
					$document_path = $potential_path;
				}
			}
		}

		// Final check - if we don't have a document path or it doesn't exist
		if (empty($document_path) || !file_exists($document_path)) {
			error_log("Send Document: Could not find document file after all approaches. ID: " . $attachment_id);
			error_log("Send Document: WordPress Attachment ID: " . $wp_attachment_id);
			wp_send_json([
				'status' => false,
				'message' => 'Document file not found with ID: ' . $attachment_id
			]);
			return;
		}

		error_log("Send Document: Successfully found document at: " . $document_path);
		$attachment_path = $document_path;

		error_log("Send Document: Processing attachment ID: {$attachment_id}, path: {$attachment_path}");

		// Initialize variables
		$document_to_send = $attachment_path; // Default document to send
		$pdf_password = null;
		$temp_file_created = false;
		$unprotected_path = null;

		// Get original password if we need it (either for sending protected or for decryption)
		if ($patient_id) {
			$pdf_password = get_pdf_password_from_patient_id($patient_id);
		}
		if (empty($pdf_password)) {
			// Last resort - use a default password
			$pdf_password = 'document123';
		}

		// For password-protected sharing, use original document with password

		$document_to_send = $attachment_path;

		// Prepare email content
		$consultation_document = include(KIVI_CARE_DIR . 'templates/emails/ConsultationDocument.php');

		// Add password information to email if sending protected document
			$password_info = '<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #17a2b8;">
				<p style="margin: 0 0 10px 0; font-weight: bold;">This document is password protected.</p>
				<p style="margin: 0;">Password: ' . $pdf_password . '</p>
			</div>';

			// Insert password info after the first paragraph
			$consultation_document = preg_replace('/<\/p>/', '</p>' . $password_info, $consultation_document, 1);

			error_log("Send Document: Added password info to email body");

		// Use document name if available, otherwise use filename
		$email_subject = !empty($document_name)
			? 'Document: ' . $document_name
			: 'A Document from a Healthcare Provider';

		// Send email with attachment
		$status = wp_mail(
			$request_data['emails'],
			$email_subject,
			$consultation_document,
			array('Content-Type: text/html; charset=UTF-8'),
			[$document_to_send]
		);

		// Clean up temporary file if created
		if ($temp_file_created && file_exists($unprotected_path)) {
			@unlink($unprotected_path);
			error_log("Send Document: Cleaned up temporary file");
		}

		// Return appropriate response
		if (!$status) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__("Failed to send document. Please check email configuration.")
			]);
			return;
		}

		$message = $encrypt_document
			? esc_html__("Password-protected document was sent successfully.")
			: esc_html__("Document was sent successfully without password protection.");

		wp_send_json([
			'status' => true,
			'message' => $message
		]);
	}

	public function getEncountersListByPatientId()
	{
		// Check user permissions
		if (!kcCheckPermission('patient_encounter_list')) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (empty($request_data['patient_id'])) {
			wp_send_json([
				'status' => false,
				'message' => __('Patient ID not found', 'kc-lang'),
				'data' => []
			]);
		}

		$db = $this->db;
		$prefix = $db->prefix;

		// Table names
		$patient_encounter_table = "{$prefix}kc_patient_encounters";
		$clinics_table = "{$prefix}kc_clinics";
		$users_table = "{$db->base_prefix}users";
		// $medical_history_table = "{$prefix}kc_medical_history";
		$encounter_tabs_table = "{$prefix}kc_encounter_tabs";

		// Pagination setup
		$perPage = $request_data['perPage'] ?? 10;
		$page = max(1, (int) ($request_data['page'] ?? 1));
		$offset = ($page - 1) * $perPage;

		// Sorting setup
		$sortField = $request_data['sort']['field'] ?? 'id';
		$sortType = strtoupper($request_data['sort']['type'] ?? 'DESC');
		$allowedSortFields = ['id', 'status', 'encounter_date', 'doctor_name', 'clinic_name', 'patient_name'];

		if (!in_array($sortField, $allowedSortFields)) {
			$sortField = 'id';
		}

		$orderByCondition = in_array($sortField, ['doctor_name', 'clinic_name', 'patient_name'])
			? " ORDER BY {$sortField} {$sortType} "
			: " ORDER BY {$patient_encounter_table}.{$sortField} {$sortType} ";

		// Query to fetch encounters
		$common_query = $db->prepare(
			" FROM {$patient_encounter_table}
    		LEFT JOIN {$users_table} doctors ON {$patient_encounter_table}.doctor_id = doctors.id
    		LEFT JOIN {$users_table} patients ON {$patient_encounter_table}.patient_id = patients.id
    		LEFT JOIN {$clinics_table} ON {$patient_encounter_table}.clinic_id = {$clinics_table}.id
    		WHERE {$patient_encounter_table}.patient_id = %d",
			$request_data['patient_id']
		);

		$encounters = $db->get_results($db->prepare("
			SELECT {$patient_encounter_table}.*,
				   doctors.display_name AS doctor_name,
				   patients.display_name AS patient_name,
				   {$clinics_table}.name AS clinic_name
			{$common_query}
			{$orderByCondition}
			LIMIT %d OFFSET %d
		", $perPage, $offset));

		if (empty($encounters)) {
			wp_send_json([
				'status' => false,
				'message' => __('No consultation found', 'kc-lang'),
				'data' => []
			]);
		}

		$total = $db->get_var("SELECT COUNT(*) {$common_query}");

		// Fetch and group medical history by encounter_id and type
		$medical_history = $db->get_results("SELECT encounter_id, type, content, created_at FROM {$encounter_tabs_table}");

		// Group medical history by encounter_id and type
		$grouped_medical_history = [];
		foreach ($medical_history as $record) {
			if (!isset($grouped_medical_history[$record->encounter_id])) {
				$grouped_medical_history[$record->encounter_id] = [];
			}
			if (!isset($grouped_medical_history[$record->encounter_id][$record->type])) {
				$grouped_medical_history[$record->encounter_id][$record->type] = [];
			}
			$grouped_medical_history[$record->encounter_id][$record->type][] = ['title' => $record->content, 'created_at' => $record->created_at];
		}

		// Attach grouped medical history to encounters
		foreach ($encounters as &$encounter) {
			$encounter->medical_history = $grouped_medical_history[$encounter->id] ?? [];
		}

		// Return the response
		wp_send_json([
			'status' => true,
			'message' => __('Consultation list', 'kc-lang'),
			'data' => $encounters,
			'total_rows' => $total,
		]);
	}

	public function getPrescriptionListByPatientId()
	{
		if (!kcCheckPermission('prescription_list')) {
			wp_send_json(kcUnauthorizeAccessResponse());
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['patient_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Patient not found', 'kc-lang'),
				'data' => []
			]);
		}

		$patient_id = (int) $request_data['patient_id'];
		$prescription_table = $this->db->prefix . 'kc_prescription';
		$encounter_table = $this->db->prefix . 'kc_patient_encounters';

		$query = "SELECT p.* FROM {$prescription_table} p
				  JOIN {$encounter_table} e ON e.id = p.encounter_id
				  WHERE e.patient_id = {$patient_id}";

		$prescriptions = collect($this->db->get_results($query, OBJECT))->map(function ($data) {
			$data->name = [
				'id' => $data->name,
				'label' => $data->name
			];
			return $data;
		});

		$total_rows = count($prescriptions);

		if (!count($prescriptions)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('No prescription found', 'kc-lang'),
				'data' => []
			]);
		}

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Prescription records', 'kc-lang'),
			'data' => $prescriptions,
			'total_rows' => $total_rows
		]);
	}

	public function getDocumentsListByPatientId_delete()
	{
		if (!kcCheckPermission('patient_report')) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['patient_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Patient not found', 'kc-lang'),
				'data' => []
			]);
		}

		$patient_id = $request_data['patient_id'];

		if (!(new KCUser())->patientPermissionUserWise($patient_id)) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$conditions = ['patient_id' => $patient_id];

		// Get documents from patient documents table
		$patient_documents = collect((new KCPatientDocument())->get_by($conditions))
			->map(function ($doc) {
				return (object) [
					'id' => $doc->id,
					'name' => $doc->name,
					'type' => $doc->type,
					'description' => $doc->description,
					'attachment_id' => $doc->document_id,
					'document_id' => $doc->document_id,
					'encounter_id' => $doc->appointment_id,
					'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
					'system_generated' => 0
				];
			});

		$query = "SELECT id, name, description, encounter_id, attachment_id as document_id, created_date as created_at
				  FROM {$this->db->prefix}kc_patient_encounters_summery_document
				  WHERE patient_id = {$patient_id}";

		$encounter_documents = collect($this->db->get_results($query))
			->map(function ($doc) {
				return (object) [
					'id' => $doc->id,
					'name' => $doc->name,
					'type' => 'encounter_document',
					'attachment_id' => $doc->document_id,
					'description' => $doc->description,
					'document_id' => $doc->document_id,
					'encounter_id' => $doc->encounter_id,
					'created_at' => !empty($doc->created_at) ? kcGetFormatedDate($doc->created_at) : null,
					'system_generated' => 1
				];
			});

		$documents = $patient_documents->concat($encounter_documents)
			->sortByDesc('created_at')
			->values()
			->all();

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Document records', 'kc-lang'),
			'data' => $documents
		]);
	}

	// Save vital signs
	public function saveEncounterVitals()
	{
		try {
			// Verify permissions
			if (!kcCheckPermission('patient_encounter_add')) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$request_data = $this->request->getInputs();

			// Validate required fields
			if (empty($request_data['encounter_id']) || empty($request_data['vitals_data'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Missing required fields', 'kc-lang')
				]);
			}

			// Verify encounter access permission
			if (!((new KCPatientEncounter())->encounterPermissionUserWise($request_data['encounter_id']))) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
			}

			$encounter_id = (int) $request_data['encounter_id'];

			// Get patient_id from encounter
			$patient_id = $this->db->get_var(
				$this->db->prepare(
					"SELECT patient_id FROM {$this->db->prefix}kc_patient_encounters WHERE id = %d",
					$encounter_id
				)
			);

			if (!$patient_id) {
				throw new Exception(esc_html__('Invalid encounter ID', 'kc-lang'));
			}

			// Map of vital types
			$vital_types = [
				'temperature' => 'temperature',
				'pulse' => 'pulse',
				'bloodPressure' => 'blood_pressure',
				'respiratoryRate' => 'respiratory_rate',
				'saturation' => 'saturation'
			];

			$success = true;

			// Begin transaction
			$this->db->query('START TRANSACTION');

			try {
				foreach ($request_data['vitals_data'] as $key => $value) {
					if (isset($vital_types[$key]) && $value !== '') {
						$vital_data = [
							'encounter_id' => $encounter_id,
							'patient_id' => (int) $patient_id,
							'vital_type' => $vital_types[$key],
							'vital_value' => sanitize_text_field($value),
							'created_at' => current_time('Y-m-d H:i:s'),
							'updated_at' => current_time('Y-m-d H:i:s')
						];

						$result = $this->db->insert(
							$this->db->prefix . 'kc_encounter_vitals',
							$vital_data
						);

						if ($result === false) {
							$success = false;
							break;
						}
					}
				}

				if ($success) {
					$this->db->query('COMMIT');
					wp_send_json([
						'status' => true,
						'message' => esc_html__('Vital signs saved successfully', 'kc-lang')
					]);
				} else {
					throw new Exception(esc_html__('Failed to save vital signs', 'kc-lang'));
				}
			} catch (Exception $e) {
				$this->db->query('ROLLBACK');
				throw $e;
			}

		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	// Get vital signs for encounter
	public function getEncounterVitals($encounter_id)
	{
		try {
			$vital_signs = $this->db->get_results(
				$this->db->prepare(
					"SELECT vital_type, vital_value, created_at
					FROM {$this->db->prefix}kc_encounter_vitals
					WHERE encounter_id = %d
					ORDER BY created_at DESC",
					$encounter_id
				)
			);

			// Group vitals by type for easier frontend consumption
			$grouped_vitals = [];
			foreach ($vital_signs as $vital) {
				$grouped_vitals[$vital->vital_type] = $vital->vital_value;
			}

			return [
				'status' => true,
				'data' => $grouped_vitals
			];

		} catch (Exception $e) {
			return [
				'status' => false,
				'message' => $e->getMessage()
			];
		}
	}

	public function getEncounterStats()
	{
		$is_permission = false;
		if (kcCheckPermission('patient_encounter_list')) {
			$is_permission = true;
		}

		if (!$is_permission) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$login_id = get_current_user_id();
		$patient_encounter_table = $this->db->prefix . 'kc_patient_encounters';
		$users_table = $this->db->base_prefix . 'users';

		$current_user_login_role = $this->getLoginUserRole();
		$patient_user_condition = $doctor_user_condition = $clinic_condition = '';

		// Add role-specific conditions
		if ($this->getPatientRole() === $current_user_login_role) {
			$patient_user_condition = " AND {$patient_encounter_table}.patient_id = {$login_id}";
		}

		if ($this->getDoctorRole() === $current_user_login_role) {
			$doctor_user_condition = " AND {$patient_encounter_table}.doctor_id = {$login_id}";
		}

		if ($this->getClinicAdminRole() === $current_user_login_role) {
			$clinic_condition = " AND {$patient_encounter_table}.clinic_id=" . kcGetClinicIdOfClinicAdmin();
		}

		if ($this->getReceptionistRole() === $current_user_login_role) {
			$clinic_condition = " AND {$patient_encounter_table}.clinic_id = " . kcGetClinicIdOfReceptionist();
		}

		// Get total consultations
		$total_consultations = $this->db->get_var("
			SELECT COUNT(*)
			FROM {$patient_encounter_table}
			WHERE 0=0 {$patient_user_condition} {$doctor_user_condition} {$clinic_condition}
		");

		// Get upcoming consultations
		$upcoming_consultations = $this->db->get_var("
			SELECT COUNT(*)
			FROM {$patient_encounter_table}
			WHERE encounter_date >= CURDATE()
			AND status = 'pending'
			{$patient_user_condition} {$doctor_user_condition} {$clinic_condition}
		");

		// Get completed consultations
		$completed_consultations = $this->db->get_var("
			SELECT COUNT(*)
			FROM {$patient_encounter_table}
			WHERE status = 'completed'
			{$patient_user_condition} {$doctor_user_condition} {$clinic_condition}
		");

		// Get count of different specialists (doctors)
		$different_specialists = $this->db->get_var("
			SELECT COUNT(DISTINCT doctor_id)
			FROM {$patient_encounter_table}
			WHERE 0=0 {$patient_user_condition} {$doctor_user_condition} {$clinic_condition}
		");

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Consultation statistics', 'kc-lang'),
			'data' => [
				'total_consultations' => (int) $total_consultations,
				'upcoming_consultations' => (int) $upcoming_consultations,
				'completed_consultations' => (int) $completed_consultations,
				'different_specialists' => (int) $different_specialists
			]
		]);
	}

	public function getLastRecordedVitalsByEncounterId()
	{
		try {
			// Get and validate basic inputs
			$request_data = $this->request->getInputs();

			if (empty($request_data['encounter_id'])) {
				return wp_send_json([
					'status' => false,
					'message' => esc_html__('Patient ID and Consultation ID are required', 'kc-lang')
				]);
			}

			$encounter_id = $request_data['encounter_id'];

			// Single query to get all vitals at once
			$query = $this->db->prepare(
				"SELECT vital_type, vital_value, created_at
				FROM {$this->db->prefix}kc_encounter_vitals
				WHERE encounter_id = %d
				AND vital_type IN ('temperature', 'pulse', 'blood_pressure', 'respiratory_rate', 'saturation')
				AND vital_value IS NOT NULL",
				$encounter_id
			);

			$results = $this->db->get_results($query);

			// Format results into grouped array
			$grouped_vitals = [];
			foreach ($results as $vital) {
				$grouped_vitals[$vital->vital_type] = [
					'value' => $vital->vital_value,
					'recorded_at' => $vital->created_at
				];
			}

			return wp_send_json([
				'status' => true,
				'message' => esc_html__('Vital signs retrieved successfully', 'kc-lang'),
				'data' => $grouped_vitals
			]);

		} catch (Exception $e) {
			return wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function getLastRecordedVitalsByPatientId()
	{
		try {
			// Get request data
			$request_data = $this->request->getInputs();

			// Validate patient_id
			if (empty($request_data['patient_id'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Patient ID is required', 'kc-lang')
				]);
			}

			$patient_id = (int) $request_data['patient_id'];

			// Define vital types we want to fetch
			$vital_types = [
				'temperature',
				'pulse',
				'blood_pressure',
				'respiratory_rate',
				'saturation'
			];

			$grouped_vitals = [];

			// If encounter_id is provided, get the previous encounter's vitals
			if (!empty($request_data['encounter_id'])) {
				$encounter_query = $this->db->prepare(
					"SELECT encounter_id FROM {$this->db->prefix}kc_encounter_vitals
					WHERE patient_id = %d
					AND encounter_id < %d
					GROUP BY encounter_id
					ORDER BY encounter_id DESC
					LIMIT 1",
					$patient_id,
					(int) $request_data['encounter_id']
				);

				$previous_encounter = $this->db->get_row($encounter_query);

				if ($previous_encounter) {
					// Get vitals for the previous encounter
					foreach ($vital_types as $type) {
						$query = $this->db->prepare(
							"SELECT * FROM {$this->db->prefix}kc_encounter_vitals
							WHERE patient_id = %d
							AND encounter_id = %d
							AND vital_type = %s
							ORDER BY created_at DESC
							LIMIT 1",
							$patient_id,
							$previous_encounter->encounter_id,
							$type
						);

						$vital = $this->db->get_row($query);

						if ($vital) {
							$grouped_vitals[$type] = [
								'value' => $vital->vital_value,
								'recorded_at' => $vital->created_at,
								'encounter_id' => $vital->encounter_id
							];
						}
					}
				}
			}

			// If no encounter_id provided or no previous encounter found, get the latest vitals
			if (empty($grouped_vitals)) {
				foreach ($vital_types as $type) {
					$query = $this->db->prepare(
						"SELECT * FROM {$this->db->prefix}kc_encounter_vitals
						WHERE patient_id = %d
						AND vital_type = %s
						ORDER BY created_at DESC
						LIMIT 1",
						$patient_id,
						$type
					);

					$vital = $this->db->get_row($query);

					if ($vital) {
						$grouped_vitals[$type] = [
							'value' => $vital->vital_value,
							'recorded_at' => $vital->created_at,
							'encounter_id' => $vital->encounter_id
						];
					}
				}
			}

			wp_send_json([
				'status' => true,
				'message' => esc_html__('Vital signs retrieved successfully', 'kc-lang'),
				'data' => $grouped_vitals
			]);

		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}
}
