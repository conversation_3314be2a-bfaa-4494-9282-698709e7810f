<?php
/**
 * Template Name: Kivicare Super Fullscreen Template
 * Description: A full-screen template for login and patient login pages
 * that completely removes theme elements
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Get the current page content
$page_content = '';
if (have_posts()) {
    while (have_posts()) {
        the_post();
        $page_content = get_the_content();
    }
}
$page_content = apply_filters('the_content', $page_content);

// Output only the minimal HTML structure needed
?><!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title('|', true, 'right'); ?></title>
    <?php
    // Enqueue only essential styles
    wp_enqueue_style('kc_login_fullscreen');
    wp_enqueue_style('kc_font_awesome');
    wp_enqueue_style('kc_google_fonts');
    wp_enqueue_style('kc_front_app_min_style');
    wp_enqueue_script('kc_login_fullscreen_js');
    wp_enqueue_script('jquery');
    
    // Extremely minimal wp_head - just essential styles and scripts
    do_action('wp_enqueue_scripts');
    wp_print_styles();
    wp_print_head_scripts();
    ?>
    <style type="text/css">
        html, body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100vh !important;
            max-width: 100% !important;
            overflow-x: hidden !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
        }
        
        /* Hide any potential theme elements */
        header, footer, aside, nav:not(.wp-block-navigation), .site-header, .site-footer, .header, .footer, 
        .sidebar, #header, #footer, #sidebar, #masthead, #colophon,
        .entry-header, .entry-footer, .post-navigation, .comments-area,
        [class*="header-"], [class*="footer-"], [id*="header"], [id*="footer"] {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            opacity: 0 !important;
        }
        
        /* Custom styling for content */
        .kivicare-fullscreen-content {
            width: 100% !important;
            height: 100vh !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background-color: #f5f7f9 !important;
        }
        
        .kivicare-content-wrapper {
            width: 100% !important;
            height: 100vh !important;
        }
        
        #wpadminbar {
            display: none !important;
        }
    </style>
</head>
<body <?php body_class('kivicare-fullscreen-page'); ?>>
    <div class="kivicare-fullscreen-content">
        <div class="kivicare-content-wrapper">
            <?php echo $page_content; ?>
        </div>
    </div>
    
    <?php
    // Minimal wp_footer - just essential scripts
    wp_print_footer_scripts();
    ?>
</body>
</html><?php
// Exit after rendering the template to prevent any theme code from executing
exit;
?>