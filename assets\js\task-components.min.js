/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 0);
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js":
/*!********************************************************************!*\
  !*** ./node_modules/vue-loader/lib/runtime/componentNormalizer.js ***!
  \********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return normalizeComponent; });\n/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nfunction normalizeComponent(\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier /* server only */,\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options =\n    typeof scriptExports === 'function' ? scriptExports.options : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) {\n    // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () {\n          injectStyles.call(\n            this,\n            (options.functional ? this.parent : this).$root.$options.shadowRoot\n          )\n        }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functional component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection(h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing ? [].concat(existing, hook) : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n\n\n//# sourceURL=webpack:///./node_modules/vue-loader/lib/runtime/componentNormalizer.js?");

/***/ }),

/***/ "./resources/js/components/Task/TaskCalendar.vue":
/*!*******************************************************!*\
  !*** ./resources/js/components/Task/TaskCalendar.vue ***!
  \*******************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _TaskCalendar_vue_vue_type_template_id_35f5621c_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TaskCalendar.vue?vue&type=template&id=35f5621c&scoped=true */ \"./resources/js/components/Task/TaskCalendar.vue?vue&type=template&id=35f5621c&scoped=true\");\n/* harmony import */ var _TaskCalendar_vue_vue_type_template_id_35f5621c_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_TaskCalendar_vue_vue_type_template_id_35f5621c_scoped_true__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _TaskCalendar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TaskCalendar.vue?vue&type=script&lang=js */ \"./resources/js/components/Task/TaskCalendar.vue?vue&type=script&lang=js\");\n/* empty/unused harmony star reexport *//* harmony import */ var _TaskCalendar_vue_vue_type_style_index_0_id_35f5621c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TaskCalendar.vue?vue&type=style&index=0&id=35f5621c&scoped=true&lang=css */ \"./resources/js/components/Task/TaskCalendar.vue?vue&type=style&index=0&id=35f5621c&scoped=true&lang=css\");\n/* harmony import */ var _TaskCalendar_vue_vue_type_style_index_0_id_35f5621c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_TaskCalendar_vue_vue_type_style_index_0_id_35f5621c_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n  _TaskCalendar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  _TaskCalendar_vue_vue_type_template_id_35f5621c_scoped_true__WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _TaskCalendar_vue_vue_type_template_id_35f5621c_scoped_true__WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  \"35f5621c\",\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"resources/js/components/Task/TaskCalendar.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskCalendar.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskCalendar.vue?vue&type=script&lang=js":
/*!*******************************************************************************!*\
  !*** ./resources/js/components/Task/TaskCalendar.vue?vue&type=script&lang=js ***!
  \*******************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'TaskCalendar',\n  props: {\n    tasks: {\n      type: Array,\n      required: true\n    },\n    currentMonth: {\n      type: Number,\n      required: true\n    },\n    currentYear: {\n      type: Number,\n      required: true\n    }\n  },\n  computed: {\n    daysOfWeek() {\n      return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n    },\n    \n    calendarDays() {\n      const days = [];\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      \n      // Get first day of the month\n      const firstDayOfMonth = new Date(this.currentYear, this.currentMonth, 1);\n      \n      // Get the day of week the month starts on (0-6)\n      const startDayOfWeek = firstDayOfMonth.getDay();\n      \n      // Get the last day of the month\n      const lastDayOfMonth = new Date(this.currentYear, this.currentMonth + 1, 0);\n      const daysInMonth = lastDayOfMonth.getDate();\n      \n      // Get the last day of the previous month\n      const prevMonthLastDay = new Date(this.currentYear, this.currentMonth, 0);\n      const prevMonthDays = prevMonthLastDay.getDate();\n      \n      // Fill in days from previous month\n      for (let i = startDayOfWeek - 1; i >= 0; i--) {\n        const date = new Date(this.currentYear, this.currentMonth - 1, prevMonthDays - i);\n        days.push({\n          date,\n          isCurrentMonth: false,\n          isToday: this.isSameDay(date, today),\n          tasks: this.getTasksForDate(date)\n        });\n      }\n      \n      // Fill in days of current month\n      for (let i = 1; i <= daysInMonth; i++) {\n        const date = new Date(this.currentYear, this.currentMonth, i);\n        days.push({\n          date,\n          isCurrentMonth: true,\n          isToday: this.isSameDay(date, today),\n          tasks: this.getTasksForDate(date)\n        });\n      }\n      \n      // Calculate how many days we need from next month to complete the calendar grid\n      // (We always show 6 rows of 7 days = 42 cells)\n      const remainingDays = 42 - days.length;\n      \n      // Fill in days from next month\n      for (let i = 1; i <= remainingDays; i++) {\n        const date = new Date(this.currentYear, this.currentMonth + 1, i);\n        days.push({\n          date,\n          isCurrentMonth: false,\n          isToday: this.isSameDay(date, today),\n          tasks: this.getTasksForDate(date)\n        });\n      }\n      \n      return days;\n    }\n  },\n  methods: {\n    getTasksForDate(date) {\n      return this.tasks.filter(task => {\n        if (!task.due_date) return false;\n        \n        const taskDate = new Date(task.due_date);\n        return this.isSameDay(taskDate, date);\n      });\n    },\n    \n    isSameDay(date1, date2) {\n      return date1.getDate() === date2.getDate() &&\n        date1.getMonth() === date2.getMonth() &&\n        date1.getFullYear() === date2.getFullYear();\n    },\n    \n    handleDayClick(date) {\n      this.$emit('day-click', this.formatDate(date));\n    },\n    \n    formatDate(date) {\n      // Format date as YYYY-MM-DD for task creation\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    }\n  }\n});\n\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskCalendar.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskCalendar.vue?vue&type=style&index=0&id=35f5621c&scoped=true&lang=css":
/*!***************************************************************************************************************!*\
  !*** ./resources/js/components/Task/TaskCalendar.vue?vue&type=style&index=0&id=35f5621c&scoped=true&lang=css ***!
  \***************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("throw new Error(\"Module parse failed: Unexpected token (2:0)\\nFile was processed with these loaders:\\n * ./node_modules/vue-loader/lib/index.js\\nYou may need an additional loader to handle the result of these loaders.\\n| \\n> .calendar-container {\\n|   user-select: none;\\n| }\");\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskCalendar.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskCalendar.vue?vue&type=template&id=35f5621c&scoped=true":
/*!*************************************************************************************************!*\
  !*** ./resources/js/components/Task/TaskCalendar.vue?vue&type=template&id=35f5621c&scoped=true ***!
  \*************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("throw new Error(\"Module parse failed: Unexpected token (2:0)\\nFile was processed with these loaders:\\n * ./node_modules/vue-loader/lib/index.js\\nYou may need an additional loader to handle the result of these loaders.\\n| \\n> <div class=\\\"bg-white rounded-lg shadow\\\">\\n|   <!-- Calendar View -->\\n|   <div class=\\\"calendar-container\\\">\");\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskCalendar.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskCard.vue":
/*!***************************************************!*\
  !*** ./resources/js/components/Task/TaskCard.vue ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _TaskCard_vue_vue_type_template_id_2f2103c4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TaskCard.vue?vue&type=template&id=2f2103c4 */ \"./resources/js/components/Task/TaskCard.vue?vue&type=template&id=2f2103c4\");\n/* harmony import */ var _TaskCard_vue_vue_type_template_id_2f2103c4__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_TaskCard_vue_vue_type_template_id_2f2103c4__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _TaskCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TaskCard.vue?vue&type=script&lang=js */ \"./resources/js/components/Task/TaskCard.vue?vue&type=script&lang=js\");\n/* harmony import */ var _TaskCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_TaskCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _TaskCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__) if([\"default\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _TaskCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));\n/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\n  _TaskCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1___default.a,\n  _TaskCard_vue_vue_type_template_id_2f2103c4__WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _TaskCard_vue_vue_type_template_id_2f2103c4__WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"resources/js/components/Task/TaskCard.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskCard.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskCard.vue?vue&type=script&lang=js":
/*!***************************************************************************!*\
  !*** ./resources/js/components/Task/TaskCard.vue?vue&type=script&lang=js ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("throw new Error(\"Module parse failed: Unexpected token (29:52)\\nFile was processed with these loaders:\\n * ./node_modules/vue-loader/lib/index.js\\nYou may need an additional loader to handle the result of these loaders.\\n|           const date = new Date(dateString);\\n|           if (isNaN(date)) return dateString;\\n>           return new Intl.DateTimeFormat(this.$i18n?.locale || 'en-US', {\\n|             month: 'short',\\n|             day: 'numeric'\");\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskCard.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskCard.vue?vue&type=template&id=2f2103c4":
/*!*********************************************************************************!*\
  !*** ./resources/js/components/Task/TaskCard.vue?vue&type=template&id=2f2103c4 ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("throw new Error(\"Module parse failed: Unexpected token (2:0)\\nFile was processed with these loaders:\\n * ./node_modules/vue-loader/lib/index.js\\nYou may need an additional loader to handle the result of these loaders.\\n| \\n> <div class=\\\"bg-white rounded-lg shadow-md p-4 transition-transform hover:scale-[1.02] border\\\">\\n|   <!-- Header -->\\n|   <div class=\\\"flex justify-between items-start\\\">\");\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskCard.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskKanban.vue":
/*!*****************************************************!*\
  !*** ./resources/js/components/Task/TaskKanban.vue ***!
  \*****************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _TaskKanban_vue_vue_type_template_id_4b40152a_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TaskKanban.vue?vue&type=template&id=4b40152a&scoped=true */ \"./resources/js/components/Task/TaskKanban.vue?vue&type=template&id=4b40152a&scoped=true\");\n/* harmony import */ var _TaskKanban_vue_vue_type_template_id_4b40152a_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_TaskKanban_vue_vue_type_template_id_4b40152a_scoped_true__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _TaskKanban_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TaskKanban.vue?vue&type=script&lang=js */ \"./resources/js/components/Task/TaskKanban.vue?vue&type=script&lang=js\");\n/* empty/unused harmony star reexport *//* harmony import */ var _TaskKanban_vue_vue_type_style_index_0_id_4b40152a_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TaskKanban.vue?vue&type=style&index=0&id=4b40152a&scoped=true&lang=css */ \"./resources/js/components/Task/TaskKanban.vue?vue&type=style&index=0&id=4b40152a&scoped=true&lang=css\");\n/* harmony import */ var _TaskKanban_vue_vue_type_style_index_0_id_4b40152a_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_TaskKanban_vue_vue_type_style_index_0_id_4b40152a_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n  _TaskKanban_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  _TaskKanban_vue_vue_type_template_id_4b40152a_scoped_true__WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _TaskKanban_vue_vue_type_template_id_4b40152a_scoped_true__WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  \"4b40152a\",\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"resources/js/components/Task/TaskKanban.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskKanban.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskKanban.vue?vue&type=script&lang=js":
/*!*****************************************************************************!*\
  !*** ./resources/js/components/Task/TaskKanban.vue?vue&type=script&lang=js ***!
  \*****************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _TaskCard_vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TaskCard.vue */ \"./resources/js/components/Task/TaskCard.vue\");\n\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'TaskKanban',\n  components: {\n    TaskCard: _TaskCard_vue__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n  },\n  props: {\n    tasks: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      draggedTaskId: null,\n      dragSourceColumn: null,\n      isDropTarget: null,\n      isDragging: false,\n      columns: [\n        { \n          status: 'pending', \n          icon: 'ri-time-line', \n          bgClass: 'bg-yellow-50', \n          borderClass: 'border-yellow-200',\n          textClass: 'text-yellow-600',\n          badgeClass: 'bg-yellow-100 text-yellow-800'\n        },\n        { \n          status: 'in-progress', \n          icon: 'ri-loader-line', \n          bgClass: 'bg-blue-50', \n          borderClass: 'border-blue-200',\n          textClass: 'text-blue-600',\n          badgeClass: 'bg-blue-100 text-blue-800'\n        },\n        { \n          status: 'completed', \n          icon: 'ri-check-line', \n          bgClass: 'bg-green-50', \n          borderClass: 'border-green-200',\n          textClass: 'text-green-600',\n          badgeClass: 'bg-green-100 text-green-800'\n        },\n        { \n          status: 'cancelled', \n          icon: 'ri-close-line', \n          bgClass: 'bg-red-50', \n          borderClass: 'border-red-200',\n          textClass: 'text-red-600',\n          badgeClass: 'bg-red-100 text-red-800'\n        }\n      ]\n    };\n  },\n  computed: {\n    tasksByStatus() {\n      return Object.fromEntries(\n        this.columns.map(column => [column.status, this.tasks.filter(task => task.status === column.status)])\n      );\n    }\n  },\n  methods: {\n    getStatusLabel(status) {\n      const labels = {\n        'pending': 'Pending',\n        'in-progress': 'In Progress',\n        'completed': 'Completed',\n        'cancelled': 'Cancelled'\n      };\n      \n      return labels[status] || status;\n    },\n\n    onDragStart(event, taskId, sourceColumn) {\n      // Prevent default to avoid any browser weird behavior\n      event.stopPropagation();\n      \n      // Set data for transfer\n      this.draggedTaskId = taskId;\n      this.dragSourceColumn = sourceColumn;\n      this.isDragging = true;\n      \n      // Set the data that will be transferred\n      event.dataTransfer.setData('text/plain', taskId);\n      \n      // Make drag image transparent to use our custom ghost\n      const img = new Image();\n      img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // Transparent 1x1 pixel\n      event.dataTransfer.setDragImage(img, 0, 0);\n      \n      // Set correct cursor\n      event.dataTransfer.effectAllowed = 'move';\n      \n      // Delay to ensure DOM is updated\n      setTimeout(() => {\n        const draggedEl = document.querySelector('.is-dragging task-card');\n        if (draggedEl && this.$refs.ghostEl) {\n          // Clone visual for ghost element (optional - requires more complex code)\n          // this.$refs.ghostEl.innerHTML = '';\n          // this.$refs.ghostEl.appendChild(draggedEl.cloneNode(true));\n        }\n      }, 0);\n    },\n    \n    handleDragOver(event, columnStatus) {\n      // Explicitly prevent default to allow drop\n      event.preventDefault();\n      \n      // Update visual cue\n      this.isDropTarget = columnStatus;\n      \n      // Set the drop effect\n      event.dataTransfer.dropEffect = 'move';\n    },\n    \n    handleDragLeave(event, columnStatus) {\n      // Only clear if it's not entering a child element\n      if (!event.currentTarget.contains(event.relatedTarget)) {\n        this.isDropTarget = null;\n      }\n    },\n    \n    onDragEnd() {\n      // Reset all states\n      this.isDragging = false;\n      this.isDropTarget = null;\n    },\n    \n    onDrop(event, newStatus) {\n      // Prevent default browser drop behavior\n      event.preventDefault();\n      \n      // Get the dragged task ID\n      const taskId = event.dataTransfer.getData('text/plain');\n      \n      // Only emit event if status actually changed (optimization)\n      if (taskId && this.dragSourceColumn !== newStatus) {\n        this.$emit('status-change', { taskId: parseInt(taskId), status: newStatus });\n      }\n      \n      // Reset states\n      this.draggedTaskId = null;\n      this.dragSourceColumn = null;\n      this.isDropTarget = null;\n      this.isDragging = false;\n    }\n  }\n});\n\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskKanban.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskKanban.vue?vue&type=style&index=0&id=4b40152a&scoped=true&lang=css":
/*!*************************************************************************************************************!*\
  !*** ./resources/js/components/Task/TaskKanban.vue?vue&type=style&index=0&id=4b40152a&scoped=true&lang=css ***!
  \*************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("throw new Error(\"Module parse failed: Unexpected token (2:0)\\nFile was processed with these loaders:\\n * ./node_modules/vue-loader/lib/index.js\\nYou may need an additional loader to handle the result of these loaders.\\n| \\n> .drop-highlight {\\n|   border-color: var(--black-color, #4f46e5);\\n|   box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);\");\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskKanban.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskKanban.vue?vue&type=template&id=4b40152a&scoped=true":
/*!***********************************************************************************************!*\
  !*** ./resources/js/components/Task/TaskKanban.vue?vue&type=template&id=4b40152a&scoped=true ***!
  \***********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("throw new Error(\"Module parse failed: Unexpected token (2:0)\\nFile was processed with these loaders:\\n * ./node_modules/vue-loader/lib/index.js\\nYou may need an additional loader to handle the result of these loaders.\\n| \\n> <div class=\\\"overflow-hidden pb-4\\\">\\n|   <!-- Loading state -->\\n|   <div v-if=\\\"loading\\\" class=\\\"flex justify-center items-center py-10\\\">\");\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskKanban.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskList.vue":
/*!***************************************************!*\
  !*** ./resources/js/components/Task/TaskList.vue ***!
  \***************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _TaskList_vue_vue_type_template_id_7fd3c252__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TaskList.vue?vue&type=template&id=7fd3c252 */ \"./resources/js/components/Task/TaskList.vue?vue&type=template&id=7fd3c252\");\n/* harmony import */ var _TaskList_vue_vue_type_template_id_7fd3c252__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_TaskList_vue_vue_type_template_id_7fd3c252__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _TaskList_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TaskList.vue?vue&type=script&lang=js */ \"./resources/js/components/Task/TaskList.vue?vue&type=script&lang=js\");\n/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\n  _TaskList_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  _TaskList_vue_vue_type_template_id_7fd3c252__WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _TaskList_vue_vue_type_template_id_7fd3c252__WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"resources/js/components/Task/TaskList.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskList.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskList.vue?vue&type=script&lang=js":
/*!***************************************************************************!*\
  !*** ./resources/js/components/Task/TaskList.vue?vue&type=script&lang=js ***!
  \***************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _config_request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../config/request */ \"./resources/js/config/request.js\");\n\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: 'TaskList',\n  props: {\n    tasks: {\n      type: Array,\n      required: true\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      selectedTasks: [],\n      selectAll: false,\n      statusDropdownOpen: null,\n      actionsDropdownOpen: null\n    }\n  },\n  mounted() {\n    // Add click outside event listener for the dropdowns\n    document.addEventListener('click', this.closeDropdowns);\n  },\n  beforeDestroy() {\n    // Remove click outside event listener\n    document.removeEventListener('click', this.closeDropdowns);\n  },\n  methods: {\n    toggleStatusDropdown(taskId) {\n      // Close any open dropdowns first\n      this.actionsDropdownOpen = null;\n      \n      // Toggle the status dropdown\n      if (this.statusDropdownOpen === taskId) {\n        this.statusDropdownOpen = null;\n      } else {\n        this.statusDropdownOpen = taskId;\n      }\n    },\n    \n    toggleActionsDropdown(taskId) {\n      // Close any open dropdowns first\n      this.statusDropdownOpen = null;\n      \n      // Toggle the actions dropdown\n      if (this.actionsDropdownOpen === taskId) {\n        this.actionsDropdownOpen = null;\n      } else {\n        this.actionsDropdownOpen = taskId;\n      }\n    },\n    \n    closeDropdowns(event) {\n      // Only close if click is outside of dropdown elements\n      if (!event.target.closest('.dropdown-toggle') && !event.target.closest('.dropdown-menu')) {\n        this.statusDropdownOpen = null;\n        this.actionsDropdownOpen = null;\n      }\n    },\n    \n    formatDate(dateString) {\n      if (!dateString) return '-';\n      \n      const date = new Date(dateString);\n      return new Intl.DateTimeFormat(this.$i18n.locale, {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      }).format(date);\n    },\n    \n    isTaskOverdue(task) {\n      if (task.status === 'completed' || task.status === 'cancelled' || !task.due_date) {\n        return false;\n      }\n      \n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const dueDate = new Date(task.due_date);\n      dueDate.setHours(0, 0, 0, 0);\n      \n      return dueDate < today;\n    },\n    \n    getPriorityLabel(priority) {\n      const labels = {\n        'low': this.$t('task.priority_low'),\n        'medium': this.$t('task.priority_medium'),\n        'high': this.$t('task.priority_high')\n      };\n      \n      return labels[priority] || priority;\n    },\n    \n    getStatusLabel(status) {\n      const labels = {\n        'pending': this.$t('task.status_pending'),\n        'in-progress': this.$t('task.status_in_progress'),\n        'completed': this.$t('task.status_completed'),\n        'cancelled': this.$t('task.status_cancelled')\n      };\n      \n      return labels[status] || status;\n    },\n\n    confirmDeleteTask(taskId) {\n      this.$swal.fire({\n        title: this.$t('task.confirm_delete'),\n        text: this.$t('task.delete_confirmation'),\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonText: this.$t('common.delete'),\n        confirmButtonColor: '#dc3545',\n        cancelButtonText: this.$t('common.cancel')\n      }).then((result) => {\n        if (result.isConfirmed) {\n          this.deleteTask(taskId);\n        }\n      });\n    },\n\n    async deleteTask(taskId) {\n      try {\n        const response = await Object(_config_request__WEBPACK_IMPORTED_MODULE_0__[\"post\"])(`delete_task`, { id: taskId });\n\n        if (response.data && response.data.status) {\n          this.$swal.fire({\n            icon: 'success',\n            title: \"Success\",\n            text: response.data.message || 'Task deleted successfully',\n            showConfirmButton: false,\n            timer: 1500\n          });\n\n          // Emit event to parent to refresh tasks\n          this.$emit('task-deleted', taskId);\n        } else {\n          this.$swal.fire({\n            icon: 'error',\n            title: \"Error\",\n            text: response.data.message || 'Failed to delete task'\n          });\n        }\n      } catch (error) {\n        console.error('Error deleting task:', error);\n        this.$swal.fire({\n          icon: 'error',\n          title: \"Error\",\n          text: 'Failed to delete task'\n        });\n      }\n    },\n    \n    limitedAssignees(assignees) {\n      return assignees.slice(0, 3);\n    },\n    \n    getInitials(name) {\n      if (!name) return '?';\n      \n      return name.split(' ')\n        .map(word => word.charAt(0).toUpperCase())\n        .slice(0, 2)\n        .join('');\n    },\n    \n    getAvatarColor(name) {\n      if (!name) return '#6c757d';\n      \n      // Generate consistent color based on name\n      let hash = 0;\n      for (let i = 0; i < name.length; i++) {\n        hash = name.charCodeAt(i) + ((hash << 5) - hash);\n      }\n      \n      const colors = [\n        '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',\n        '#1abc9c', '#d35400', '#c0392b', '#16a085', '#8e44ad',\n        '#27ae60', '#2980b9', '#f1c40f', '#e67e22', '#ecf0f1'\n      ];\n      \n      return colors[Math.abs(hash) % colors.length];\n    },\n    \n    truncateDescription(description, length = 100) {\n      if (!description) return '';\n      \n      if (description.length <= length) return description;\n      \n      return description.substring(0, length) + '...';\n    },\n    \n    toggleSelectAll() {\n      if (this.selectAll) {\n        this.selectedTasks = this.tasks.map(task => task.id);\n      } else {\n        this.selectedTasks = [];\n      }\n    },\n    \n    changeStatus(taskId, status) {\n      this.statusDropdownOpen = null;\n      this.$emit('status-change', { taskId, status });\n    },\n    \n    bulkComplete() {\n      if (!this.selectedTasks.length) return;\n      \n      this.$swal.fire({\n        title: this.$t('task.confirm_bulk_complete'),\n        text: this.$t('task.bulk_complete_confirmation', { count: this.selectedTasks.length }),\n        icon: 'question',\n        showCancelButton: true,\n        confirmButtonText: this.$t('common.yes'),\n        cancelButtonText: this.$t('common.cancel')\n      }).then((result) => {\n        if (result.isConfirmed) {\n          // Emit event for each selected task\n          this.selectedTasks.forEach(taskId => {\n            this.$emit('complete-task', taskId);\n          });\n          this.selectedTasks = [];\n          this.selectAll = false;\n        }\n      });\n    },\n    \n    bulkChangeStatus(status) {\n      if (!this.selectedTasks.length) return;\n      \n      this.$swal.fire({\n        title: this.$t('task.confirm_bulk_status_change'),\n        text: this.$t('task.bulk_status_change_confirmation', { \n          count: this.selectedTasks.length,\n          status: this.getStatusLabel(status)\n        }),\n        icon: 'question',\n        showCancelButton: true,\n        confirmButtonText: this.$t('common.yes'),\n        cancelButtonText: this.$t('common.cancel')\n      }).then((result) => {\n        if (result.isConfirmed) {\n          // Emit event for each selected task\n          this.selectedTasks.forEach(taskId => {\n            this.$emit('status-change', { taskId, status });\n          });\n          this.selectedTasks = [];\n          this.selectAll = false;\n        }\n      });\n    },\n    \n    bulkDelete() {\n      if (!this.selectedTasks.length) return;\n      \n      this.$swal.fire({\n        title: this.$t('task.confirm_bulk_delete'),\n        text: this.$t('task.bulk_delete_confirmation', { count: this.selectedTasks.length }),\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonText: this.$t('common.delete'),\n        confirmButtonColor: '#dc3545',\n        cancelButtonText: this.$t('common.cancel')\n      }).then((result) => {\n        if (result.isConfirmed) {\n          // Emit event for each selected task\n          this.selectedTasks.forEach(taskId => {\n            this.deleteTask(taskId);\n          });\n          this.selectedTasks = [];\n          this.selectAll = false;\n        }\n      });\n    }\n  },\n  watch: {\n    // Update selectAll state based on selection\n    selectedTasks(newVal) {\n      this.selectAll = newVal.length && newVal.length === this.tasks.length;\n    },\n    \n    // Reset selection when task list changes\n    tasks() {\n      this.selectedTasks = [];\n      this.selectAll = false;\n    }\n  }\n});\n\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskList.vue?");

/***/ }),

/***/ "./resources/js/components/Task/TaskList.vue?vue&type=template&id=7fd3c252":
/*!*********************************************************************************!*\
  !*** ./resources/js/components/Task/TaskList.vue?vue&type=template&id=7fd3c252 ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("throw new Error(\"Module parse failed: Unexpected token (2:0)\\nFile was processed with these loaders:\\n * ./node_modules/vue-loader/lib/index.js\\nYou may need an additional loader to handle the result of these loaders.\\n| \\n> <div class=\\\"task-list-container\\\">\\n|   <!-- Loading state -->\\n|   <div v-if=\\\"loading\\\" class=\\\"flex justify-center py-6\\\">\");\n\n//# sourceURL=webpack:///./resources/js/components/Task/TaskList.vue?");

/***/ }),

/***/ "./resources/js/config/request.js":
/*!****************************************!*\
  !*** ./resources/js/config/request.js ***!
  \****************************************/
/*! exports provided: siteBaseUrl, _patientAppointmentBook, _patientLogin, _patientRegister, _urlGetTimeSlot, _urlSaveAppointment, post, get, _axios_post, _getDoctors, _getDoctorsDetails, _getStaticData */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"siteBaseUrl\", function() { return siteBaseUrl; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_patientAppointmentBook\", function() { return _patientAppointmentBook; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_patientLogin\", function() { return _patientLogin; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_patientRegister\", function() { return _patientRegister; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_urlGetTimeSlot\", function() { return _urlGetTimeSlot; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_urlSaveAppointment\", function() { return _urlSaveAppointment; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"post\", function() { return post; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"get\", function() { return get; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_axios_post\", function() { return _axios_post; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_getDoctors\", function() { return _getDoctors; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_getDoctorsDetails\", function() { return _getDoctorsDetails; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"_getStaticData\", function() { return _getStaticData; });\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n// Determine base URL for the site, handling subdirectory installations\nvar getBaseUrl = function getBaseUrl() {\n  // First check if WordPress has provided a base URL in a global\n  if (typeof kivicare_base_url !== 'undefined') {\n    return kivicare_base_url;\n  }\n\n  // Try to determine from the URL path\n  var pathParts = window.location.pathname.split('/');\n  var baseIndex = pathParts.findIndex(function (part) {\n    return part === 'wp-admin' || part === 'wp-content';\n  });\n  if (baseIndex !== -1) {\n    // We found wp-admin or wp-content in the URL\n    return window.location.origin + pathParts.slice(0, baseIndex).join('/');\n  }\n\n  // Default to origin if we can't determine subfolder\n  return window.location.origin;\n};\n\n// Expose the base URL for other parts of the application to use\nvar siteBaseUrl = getBaseUrl();\n\n// Following is the base url of requested api\nvar _clinic_baseUrl = siteBaseUrl + '/wp-json/wp-medical/api/';\n// Following is the version of requested api\nvar apiVersion = \"v1\";\nvar _urlGetDoctors = _clinic_baseUrl + apiVersion + '/patient-appointment/get-doctors-data';\nvar _urlGetAppointmentTypes = _clinic_baseUrl + apiVersion + '/patient-appointment/get-static-data';\nvar _patientAppointmentBook = _clinic_baseUrl + apiVersion + '/patient/book-appointment';\nvar _patientLogin = _clinic_baseUrl + apiVersion + '/patient-auth/login';\nvar _patientRegister = _clinic_baseUrl + apiVersion + '/patient-auth/register';\nvar _urlGetDoctorDetails = _clinic_baseUrl + apiVersion + '/book-appointment/get-doctors-details';\nvar _urlGetTimeSlot = _clinic_baseUrl + apiVersion + '/book-appointment/get-time-slots';\nvar _urlSaveAppointment = _clinic_baseUrl + apiVersion + '/book-appointment/save-appointment';\nvar post = function post(route) {\n  var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  // Handle both the old and new function signature\n  var frontEnd = false;\n  var headers = {\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  };\n  var signal = null;\n  var isFormData = false;\n  var onUploadProgress = null;\n\n  // Handle old function signature (frontEnd parameter)\n  if (typeof options === 'boolean') {\n    frontEnd = options;\n  }\n  // Handle options object\n  else if (_typeof(options) === 'object') {\n    frontEnd = options.frontEnd || false;\n    if (options.headers) headers = options.headers;\n    if (options.signal) signal = options.signal;\n    if (options.isFormData) isFormData = options.isFormData;\n    if (options.onUploadProgress) onUploadProgress = options.onUploadProgress;\n  }\n  if (frontEnd) {\n    window.ajaxurl = window.ajaxData.ajaxurl;\n    window.request_data = window.ajaxData;\n  }\n  var url = ajaxurl;\n  if (data.action === undefined) {\n    url = ajaxurl + '?action=ajax_post';\n  }\n  if (route === undefined) {\n    return false;\n  }\n\n  // Handle FormData objects differently\n  if (data instanceof FormData) {\n    isFormData = true;\n    data.append('route_name', route);\n    data.append('_ajax_nonce', request_data.nonce);\n    // Don't set Content-Type for FormData as browser will set it with boundary\n    headers = {\n      headers: {}\n    };\n  } else if (data.append !== undefined) {\n    // Legacy handling for FormData objects\n    data.append('route_name', route);\n    data.append('_ajax_nonce', request_data.nonce);\n  } else {\n    // Regular JSON data\n    data.route_name = route;\n    data._ajax_nonce = request_data.nonce;\n  }\n\n  // Merge options including the signal if provided\n  var requestOptions = _objectSpread(_objectSpread({}, headers), {}, {\n    signal: signal\n  });\n\n  // Add upload progress if provided\n  if (onUploadProgress) {\n    requestOptions.onUploadProgress = onUploadProgress;\n  }\n\n  // Log info about the request for debugging\n  console.log('Request to ' + route + ':', {\n    isFormData: isFormData,\n    url: url,\n    dataType: data instanceof FormData ? 'FormData' : _typeof(data),\n    options: requestOptions\n  });\n  return new Promise(function (resolve, reject) {\n    axios.post(url, data, requestOptions).then(function (data) {\n      if (data.data.status_code !== undefined && data.data.status_code === 403) {\n        displayErrorMessage(data.data.message);\n        // Check if vm is defined before using it\n        if (typeof vm !== 'undefined') {\n          vm.$router.push({\n            name: \"403\"\n          });\n        }\n      }\n      console.log('API response from ' + route + ':', data.data);\n      resolve(data);\n    })[\"catch\"](function (error) {\n      console.error('API error from ' + route + ':', error);\n      reject(error);\n    });\n  });\n};\nvar get = function get(route) {\n  var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var frontEnd = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (frontEnd) {\n    window.ajaxurl = window.ajaxData.ajaxurl;\n    window.request_data = window.ajaxData;\n  }\n\n  // Ensure window.request_data is defined\n  if (!window.request_data || !window.request_data.get_nonce) {\n    console.error('Missing request_data or get_nonce:', window.request_data);\n    window.request_data = window.request_data || {};\n    window.request_data.get_nonce = window.request_data.get_nonce || '';\n  }\n  data._ajax_nonce = window.request_data.get_nonce;\n  var url = ajaxurl;\n  if (data.action === undefined) {\n    url = ajaxurl + '?action=ajax_get';\n  }\n  if (route === undefined) {\n    return false;\n  }\n  url = url + '&route_name=' + route;\n  // console.log('Making GET request to:', route, data, url);\n\n  return new Promise(function (resolve, reject) {\n    axios.get(url, {\n      params: data\n    }).then(function (data) {\n      if (data.data.status_code !== undefined && data.data.status_code === 403) {\n        displayErrorMessage(data.data.message);\n        if (typeof vm !== 'undefined') {\n          vm.$router.push({\n            name: \"403\"\n          });\n        }\n      }\n      // console.log('GET response from ' + route + ':', data.data);\n      resolve(data);\n    })[\"catch\"](function (error) {\n      console.error('GET error from ' + route + ':', error);\n      reject(error);\n    });\n  });\n};\nvar _axios_post = function _axios_post(url, data) {\n  return new Promise(function (resolve, reject) {\n    axios.post(url, data).then(function (data) {\n      resolve(data);\n    })[\"catch\"](function (error) {\n      reject(error);\n    });\n  });\n};\nvar _getDoctors = function _getDoctors() {\n  return new Promise(function (resolve, reject) {\n    axios.get(_urlGetDoctors).then(function (data) {\n      resolve(data);\n    })[\"catch\"](function (error) {\n      reject(error);\n    });\n  });\n};\nvar _getDoctorsDetails = function _getDoctorsDetails() {\n  return new Promise(function (resolve, reject) {\n    axios.get(_urlGetDoctorDetails).then(function (data) {\n      resolve(data.data);\n    })[\"catch\"](function (error) {\n      reject(error);\n    });\n  });\n};\nvar _getStaticData = function _getStaticData(requestObject) {\n  return new Promise(function (resolve, reject) {\n    axios.post(_urlGetAppointmentTypes, {\n      data_type: requestObject.data_type,\n      static_data_type: requestObject.static_data_type\n    }).then(function (data) {\n      resolve(data);\n    })[\"catch\"](function (error) {\n      reject(error);\n    });\n  });\n};\n\n//# sourceURL=webpack:///./resources/js/config/request.js?");

/***/ }),

/***/ 0:
/*!***************************************************************************************************************************************************************************************************!*\
  !*** multi ./resources/js/components/Task/TaskCalendar.vue ./resources/js/components/Task/TaskKanban.vue ./resources/js/components/Task/TaskList.vue ./resources/js/components/Task/TaskCard.vue ***!
  \***************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("__webpack_require__(/*! ./resources/js/components/Task/TaskCalendar.vue */\"./resources/js/components/Task/TaskCalendar.vue\");\n__webpack_require__(/*! ./resources/js/components/Task/TaskKanban.vue */\"./resources/js/components/Task/TaskKanban.vue\");\n__webpack_require__(/*! ./resources/js/components/Task/TaskList.vue */\"./resources/js/components/Task/TaskList.vue\");\nmodule.exports = __webpack_require__(/*! ./resources/js/components/Task/TaskCard.vue */\"./resources/js/components/Task/TaskCard.vue\");\n\n\n//# sourceURL=webpack:///multi_./resources/js/components/Task/TaskCalendar.vue_./resources/js/components/Task/TaskKanban.vue_./resources/js/components/Task/TaskList.vue_./resources/js/components/Task/TaskCard.vue?");

/***/ })

/******/ });