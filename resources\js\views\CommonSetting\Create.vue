<template>
    <div class="grid grid-cols-1">
        <div class="col-span-1">
            <!-- Card with shadow -->
            <div class="bg-white rounded-lg shadow-md">
                <!-- Header -->
                <div class="p-4 border-b">
                    <div class="grid grid-cols-1 md:grid-cols-12">
                        <div class="col-span-1 md:col-span-8">
                            <h2 class="text-xl font-semibold m-0 flex items-center">
                                {{ formTranslation.common.en_dis_module }}
                                <a v-if="request_status == 'off'" 
                                   href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#configurations" 
                                   target="_blank"
                                   class="ml-2">
                                    <i class="fa fa-question-circle text-gray-600"></i>
                                </a>
                            </h2>
                        </div>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-4">
                    <!-- Module Configuration Component -->
                    <div class="mb-4">
                        <ModuleConfig ref="moduleConfiguration"></ModuleConfig>
                    </div>
                    
                    <!-- Footer Actions -->
                    <div class="flex justify-end">
                        <button 
                            @click="saveCommonSettings"
                            class="px-4 py-2 bg-black text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-200"
                            v-html="saveSettingBtn">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ModuleConfig from "../../components/ModuleConfig/ModuleConfig";

export default {
    components: { ModuleConfig },
    data: () => ({
        saveSettingBtn: '<i class="fa fa-save"></i> Save',
        request_status: 'off',
    }),
    mounted() {
        if (!['administrator'].includes(this.getUserRole())) {
            this.$router.push({ name: "403" });
        }
        this.saveSettingBtn = '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
        this.init();
        this.getModule();
    },
    methods: {
        init() {},
        saveCommonSettings() {
            this.$refs.moduleConfiguration.saveSetting();
        },
        getModule() {
            if (window.request_data.link_show_hide !== undefined && window.request_data.link_show_hide !== '') {
                this.request_status = window.request_data.link_show_hide;
            }
        }
    }
}
</script>