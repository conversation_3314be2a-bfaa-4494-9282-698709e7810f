<template>
  <div>
    <main class="p-6">
      <div
        class="bg-white text-card-foreground rounded-xl border shadow mb-6 relative"
      >
        <!-- Coming Soon Badge -->
        <div class="absolute -top-3 right-6">
          <span
            class="bg-purple-100 text-purple-600 text-xs font-medium px-5 py-1 rounded-full"
          >
            Coming Soon
          </span>
        </div>
        <div class="flex flex-col space-y-1.5 p-6">
          <h3
            class="font-semibold leading-none tracking-tight flex items-center space-x-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-activity w-5 h-5 text-purple-600"
            >
              <path
                d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"
              ></path></svg
            ><span>AI Health Assistant</span>
          </h3>
        </div>
        <div class="p-6 pt-0">
          <div class="flex items-start space-x-4">
            <div
              class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-heart w-5 h-5 text-purple-600"
              >
                <path
                  d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"
                ></path>
              </svg>
            </div>
            <div class="flex-1 p-4 bg-gray-50 rounded-lg">
              <p class="text-gray-600">
                Based on your recent vitals, I recommend maintaining your
                current exercise routine. Your blood pressure is within normal
                range. Would you like me to schedule your annual checkup?
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- vitals -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
        <div
          v-for="vital in vitalFields"
          :key="vital.key"
          class="bg-white text-card-foreground rounded-xl border shadow hover:border-purple-200 transition-colors duration-300"
        >
          <div class="p-6 flex justify-between items-center">
            <div>
              <h3 class="font-semibold text-sm text-gray-600 mb-1">
                {{ vital.label }}
              </h3>
              <div class="flex items-baseline space-x-2">
                <p class="text-3xl font-bold text-gray-900">
                  {{
                    loading
                      ? "Loading..."
                      : getVitalValue(vital.vital_type).split(" ")[0]
                  }}
                </p>
                <span class="text-sm text-gray-500 font-medium">
                  {{ loading ? "" : vital.unit }}
                </span>
              </div>
            </div>

            <div class="flex flex-col items-end space-y-2">
              <div
                class="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center"
              >
                <svg
                  v-if="vital.vital_type === 'temperature'"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-purple-600"
                >
                  <path d="M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z"></path>
                </svg>
                <svg
                  v-else-if="vital.vital_type === 'pulse'"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-purple-600"
                >
                  <path
                    d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"
                  ></path>
                </svg>
                <svg
                  v-else-if="vital.vital_type === 'blood_pressure'"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-purple-600"
                >
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
                <svg
                  v-else-if="vital.vital_type === 'respiratory_rate'"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-purple-600"
                >
                  <path
                    d="M8.3 10a.7.7 0 0 1-.626-1.079L11.3 3a.7.7 0 0 1 1.252 0l3.626 5.921A.7.7 0 0 1 15.7 10H8.3z"
                  ></path>
                  <path
                    d="M8.3 14a.7.7 0 0 0-.626 1.079L11.3 21a.7.7 0 0 0 1.252 0l3.626-5.921A.7.7 0 0 0 15.7 14H8.3z"
                  ></path>
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-purple-600"
                >
                  <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>
                </svg>
              </div>
              <!-- <p class="text-xs text-gray-500">2h ago</p> -->
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white text-card-foreground rounded-xl border shadow">
          <div class="flex flex-col space-y-1.5 p-6">
            <h3 class="font-semibold leading-none tracking-tight">
              Upcoming Appointments
            </h3>
          </div>
          <div class="p-6 pt-0">
            <dashboard-appointment-list
              ref="appointment_list"
              :isLoading="isLoading"
              @isReloadTrue="appointmentReload"
              @refreshDashboard="getDashboardData"
            />
          </div>
        </div>
        <div class="bg-white text-card-foreground rounded-xl border shadow">
          <div class="flex flex-col space-y-1.5 p-6">
            <h3 class="font-semibold leading-none tracking-tight">
              Prescriptions
            </h3>
          </div>
          <div class="p-6 pt-0">
            <div class="space-y-4">

              <div v-if="groupedPrescriptions.lenght > 0">
                <DashboardPrescription
                v-for="(prescriptionGroup, encounterId) in groupedPrescriptions"
                :key="encounterId"
                :prescription-group="prescriptionGroup"
              />
              </div>
              <div v-else class="flex items-center justify-center h-64">
                No Prescriptions
              </div>
              
              <!-- <div
                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div>
                  <p class="font-semibold">Prescription Renewal</p>
                  <p class="text-sm text-gray-600">19-07-2014</p>
                </div>
                <button
                  class="px-4 py-2 text-sm text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100"
                >
                  Renew Now
                </button>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import AppointmentList from "../../components/appointment/AppointmentList";
import DashboardPrescription from "../../components/Dashboard/DashboardPrescription";
import { get, post } from "../../config/request";
import VueApexCharts from "vue-apexcharts";

export default {
  components: {
    AppointmentList,
    apexcharts: VueApexCharts,
    DashboardPrescription,
  },
  data: () => {
    return {
      isLoading: false,
      dashboardData: {},
      isAppointmentReload: false,
      appointmentRequest: {},
      reloadCalender: true,
      patientPrescriptionList: [],

      loading: false,
      vitals: {},
      vitalFields: [
        {
          label: "Temperature",
          vital_type: "temperature",
          key: "temp",
          unit: "°F",
        },
        {
          label: "Pulse",
          vital_type: "pulse",
          key: "pulse",
          unit: "BPM",
        },
        {
          label: "Blood Pressure",
          vital_type: "blood_pressure",
          key: "bp",
          unit: "mmHg",
        },
        {
          label: "Respiratory Rate",
          vital_type: "respiratory_rate",
          key: "resp",
          unit: "/min",
        },
        {
          label: "Saturation",
          vital_type: "saturation",
          key: "sat",
          unit: "%",
        },
      ],
    };
  },
  mounted() {
    this.init();
    this.fetchLastRecordedVitals();
    this.fetchPrescriptions();
  },
  computed: {
    groupedPrescriptions() {
      return this.patientPrescriptionList.reduce((acc, prescription) => {
        const encounterKey = prescription.encounter_id;
        if (!acc[encounterKey]) {
          acc[encounterKey] = [];
        }
        acc[encounterKey].push(prescription);
        return acc;
      }, {});
    },
  },
  methods: {
    init: function () {
      console.log(this.getUserRole());
      this.getDashboardData();
      this.dashboardData = this.defaultDashboardData();
      this.appointmentRequest = this.defaultAppointmentRequest();
      this.$nextTick(() => {
        // Add the component back in
        this.reloadCalender = true;
      });
    },
    defaultDashboardData: function () {
      return {
        appointment_count: 0,
        doctor_count: 0,
        patient_count: 0,
        revenue: 0,
        change_log: true,
        telemed_log: false,
      };
    },
    getDashboardData: function () {
      get("get_dashboard", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.dashboardData = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    defaultAppointmentRequest: function () {
      return {
        date: new Date(),
      };
    },
    appointmentReload: function () {
      this.isLoading = false;
    },

    async fetchLastRecordedVitals() {
      try {
        this.loading = true;
        const response = await get("get_last_recorded_vitals", {
          patient_id: this.login_id(),
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || "Failed to fetch vitals");
        }

        this.vitals = response.data.data;
      } catch (error) {
        console.error("Error fetching vitals:", error);
        if (this.$toast) {
          this.$toast.error(error.message || "Failed to fetch vital signs");
        }
      } finally {
        this.loading = false;
      }
    },

    async fetchPrescriptions() {
      try {
        this.loading = true;
        const response = await get("get_prescription_list_by_patient_id", {
          login_id: this.login_id(),
          patient_id: this.login_id(),
        });

        if (response.data.status) {
          this.patientPrescriptionList = response.data.data;
        } else {
          this.error = response.data.message || "Failed to fetch prescriptions";
        }
      } catch (error) {
        console.error("Error fetching prescriptions:", error);
        this.error = "An error occurred while fetching prescriptions";
      } finally {
        this.loading = false;
      }
    },

    getVitalValue(type) {
      // First get the vital field object that matches the type
      const vitalField = this.vitalFields.find(
        (field) => field.vital_type === type
      );

      // Get the value from vitals
      const value = this.vitals[type]?.value;

      // If no value, return just a dash
      if (!value) {
        return "--";
      }

      // Return value with unit
      return `${value} ${vitalField.unit}`;
    },

    login_id() {
      return this.$store.state.userDataModule.user.ID;
    },

    getVitalTimestamp(type) {
      if (!this.vitals[type]?.recorded_at) {
        return "No recent records";
      }

      try {
        const date = new Date(this.vitals[type].recorded_at);
        return `Recorded ${formatDistanceToNow(date)} ago`;
      } catch (error) {
        return "Invalid date";
      }
    },

    getUserId() {
      // Implement method to get current user's ID from your auth system
      return this.$store.state.userDataModule.user?.id;
    },
  },
};
</script>
