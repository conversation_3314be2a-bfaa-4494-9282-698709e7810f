// app/controllers/api/KCAIScribeController.php
<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCPatientEncounter;
use Exception;

class KCAIScribeController extends KCBase
{
    private $mistral_api_url = 'https://api.mistral.ai/v1/chat/completions';
    private $model = 'open-mistral-nemo';
    private $system_message;

    public $db;

    /**
     * @var KCRequest
     */
    private $request;

    public function __construct()
    {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        $this->system_message = $this->get_system_message();
        parent::__construct();
    }

    /**
     * Handles audio transcription using Whisper API
     */
    public function transcribeAudio()
    {
        try {
            if (!kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate request
            if (!isset($request_data['audio_file']) || empty($request_data['audio_file'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Audio file is required', 'kc-lang')
                ]);
            }
            
            if (!isset($request_data['encounter_id']) || empty($request_data['encounter_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Encounter ID is required', 'kc-lang')
                ]);
            }

            $encounter_id = (int) $request_data['encounter_id'];
            
            // Verify encounter permission
            if (!(new KCPatientEncounter())->encounterPermissionUserWise($encounter_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            // Get base64 encoded audio data
            $audio_data = $request_data['audio_file'];
            
            // Decode base64 to binary
            $decoded_audio = base64_decode(preg_replace('#^data:audio/\w+;base64,#i', '', $audio_data));
            
            // Create a temporary file
            $temp_file = wp_tempnam('whisper_audio_');
            file_put_contents($temp_file, $decoded_audio);
            
            // Prepare request to OpenAI Whisper API
            $curl = curl_init();
            
            curl_setopt_array($curl, [
                CURLOPT_URL => 'https://api.openai.com/v1/audio/transcriptions',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => [
                    'file' => new \CURLFile($temp_file, 'audio/mpeg', 'audio.mp3'),
                    'model' => 'whisper-1',
                    'response_format' => 'json',
                    'language' => 'en',
                ],
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . OPENAI_API_KEY
                ],
            ]);
            
            $response = curl_exec($curl);
            $err = curl_error($curl);
            
            curl_close($curl);
            unlink($temp_file); // Clean up temp file
            
            if ($err) {
                throw new Exception("cURL Error: " . $err);
            }
            
            $transcription_result = json_decode($response, true);
            
            if (!isset($transcription_result['text'])) {
                throw new Exception("Failed to transcribe audio");
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Audio transcribed successfully', 'kc-lang'),
                'data' => [
                    'transcript' => $transcription_result['text']
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Processes audio with Deepgram for speaker diarization
     */
    public function diarizeAudio()
    {
        try {
            if (!kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate request
            if (!isset($request_data['audio_file']) || empty($request_data['audio_file'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Audio file is required', 'kc-lang')
                ]);
            }
            
            if (!isset($request_data['encounter_id']) || empty($request_data['encounter_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Encounter ID is required', 'kc-lang')
                ]);
            }

            $encounter_id = (int) $request_data['encounter_id'];
            
            // Verify encounter permission
            if (!(new KCPatientEncounter())->encounterPermissionUserWise($encounter_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            // Get base64 encoded audio data
            $audio_data = $request_data['audio_file'];
            
            // Decode base64 to binary
            $decoded_audio = base64_decode(preg_replace('#^data:audio/\w+;base64,#i', '', $audio_data));
            
            // Create a temporary file
            $temp_file = wp_tempnam('deepgram_audio_');
            file_put_contents($temp_file, $decoded_audio);
            
            // Prepare request to Deepgram API
            $curl = curl_init();
            
            // KCAIScribeController.php -> diarizeAudio()

curl_setopt_array($curl, [
    CURLOPT_URL => 'https://api.deepgram.com/v1/listen?model=general&diarize=true&punctuate=true&language=en-US',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => file_get_contents($temp_file),
    CURLOPT_HTTPHEADER => [
      'Authorization: Token ' . DEEPGRAM_API_KEY,
      // <--- Force webm instead of audio/mpeg:
      'Content-Type: audio/webm'
    ],
  ]);
  
            
            $response = curl_exec($curl);
            $err = curl_error($curl);
            
            curl_close($curl);
            unlink($temp_file); // Clean up temp file
            
            if ($err) {
                throw new Exception("cURL Error: " . $err);
            }
            
            $diarization_result = json_decode($response, true);
            
            if (!isset($diarization_result['results'])) {
                throw new Exception("Failed to diarize audio");
            }
            
            // Process the diarization results to format by speaker
            $processed_transcript = $this->processDiarizedTranscript($diarization_result);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Audio diarized successfully', 'kc-lang'),
                'data' => [
                    'diarized_transcript' => $processed_transcript,
                    'full_transcript' => $this->getFullTranscript($processed_transcript)
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Helper function to process diarized transcript
     */
    private function processDiarizedTranscript($diarization_result)
    {
        $transcript = [];
        
        if (isset($diarization_result['results']['utterances'])) {
            foreach ($diarization_result['results']['utterances'] as $utterance) {
                $transcript[] = [
                    'speaker' => $utterance['speaker'],
                    'text' => $utterance['transcript'],
                    'start' => $utterance['start'],
                    'end' => $utterance['end']
                ];
            }
        } else {
            // Alternative processing if utterances aren't available
            $channels = $diarization_result['results']['channels'][0];
            $current_speaker = null;
            $current_text = "";
            $start_time = 0;
            
            foreach ($channels['alternatives'][0]['words'] as $word) {
                if ($current_speaker === null) {
                    $current_speaker = $word['speaker'];
                    $current_text = $word['word'] . " ";
                    $start_time = $word['start'];
                } else if ($current_speaker !== $word['speaker']) {
                    $transcript[] = [
                        'speaker' => $current_speaker,
                        'text' => trim($current_text),
                        'start' => $start_time,
                        'end' => $word['start']
                    ];
                    $current_speaker = $word['speaker'];
                    $current_text = $word['word'] . " ";
                    $start_time = $word['start'];
                } else {
                    $current_text .= $word['word'] . " ";
                }
            }
            
            // Add the last speaker's text
            if ($current_speaker !== null) {
                $transcript[] = [
                    'speaker' => $current_speaker,
                    'text' => trim($current_text),
                    'start' => $start_time,
                    'end' => isset($word['end']) ? $word['end'] : $start_time + 1
                ];
            }
        }
        
        return $transcript;
    }

    /**
     * Combine diarized transcript into a single text
     */
    private function getFullTranscript($diarized_transcript) 
    {
        $full_text = '';
        foreach ($diarized_transcript as $utterance) {
            $speaker_label = $utterance['speaker'] === 0 ? 'Doctor: ' : 'Patient: ';
            $full_text .= $speaker_label . $utterance['text'] . "\n\n";
        }
        return trim($full_text);
    }

    /**
     * Analyzes transcript with Mistral Nemo API to extract medical information
     */
    public function analyzeTranscript()
    {
        try {
            if (!kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate request
            if (!isset($request_data['transcript']) || empty($request_data['transcript'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Transcript is required', 'kc-lang')
                ]);
            }
            
            if (!isset($request_data['encounter_id']) || empty($request_data['encounter_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Encounter ID is required', 'kc-lang')
                ]);
            }
            
            $transcript = $request_data['transcript'];
            $encounter_id = (int) $request_data['encounter_id'];
            
            // Verify encounter permission
            if (!(new KCPatientEncounter())->encounterPermissionUserWise($encounter_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            // Get encounter details to provide context
            $patientEncounterController = new KCPatientEncounterController();
            $encounter = $patientEncounterController->getEncounterData($encounter_id);
            if (!$encounter) {
                throw new Exception("Encounter not found");
            }
            
            // Prepare the prompt for Mistral Nemo API
            $user_prompt = "I am a doctor performing a consultation. Please analyze the following transcript and extract relevant medical information categorized into the following sections:
            
            1. Present Concerns: The main issues the patient is experiencing
            2. History: Relevant medical history related to the current concerns
            3. Examination: Any examination findings mentioned
            4. Allergies: Any allergies mentioned
            5. Medications: Current medications being taken
            6. Plan: Any treatments, tests, or follow-ups discussed
            
            Return the information in JSON format with these categories as keys.
            
            Transcript:
            $transcript";
            
            // Call Mistral Nemo API
            $curl = curl_init();
            
            curl_setopt_array($curl, [
                CURLOPT_URL => $this->mistral_api_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode([
                    'model' => $this->model,
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => $this->system_message
                        ],
                        [
                            'role' => 'user',
                            'content' => $user_prompt
                        ]
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 4000
                ]),
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . MISTRAL_API_KEY,
                    'Content-Type: application/json'
                ],
            ]);
            
            $response = curl_exec($curl);
            $err = curl_error($curl);
            
            curl_close($curl);
            
            if ($err) {
                throw new Exception("cURL Error: " . $err);
            }
            
            $analysis_result = json_decode($response, true);
            
            if (!isset($analysis_result['choices'][0]['message']['content'])) {
                throw new Exception("Failed to analyze transcript");
            }
            
            // Parse the response and extract structured medical information
            $content = $analysis_result['choices'][0]['message']['content'];
            
            // Handle whether the response is already JSON or needs to be extracted
            if (strpos($content, '{') === 0) {
                $extracted_data = json_decode($content, true);
            } else {
                // Try to extract JSON from text response
                preg_match('/```json(.*?)```/s', $content, $matches);
                if (isset($matches[1])) {
                    $extracted_data = json_decode(trim($matches[1]), true);
                } else {
                    // Fallback to trying to extract structured data from text
                    $extracted_data = $this->extractStructuredDataFromText($content);
                }
            }
            
            if (!$extracted_data) {
                throw new Exception("Failed to parse structured data from analysis");
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Transcript analyzed successfully', 'kc-lang'),
                'data' => $extracted_data
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Helper function to extract structured data from text when JSON parsing fails
     */
    private function extractStructuredDataFromText($text)
    {
        $categories = [
            'Vital Signs' => 'vitals',
            'Present Concerns' => 'concerns',
            'History' => 'history',
            'Examination' => 'examination',
            'Systems Review' => 'systems_review',
            'Allergies' => 'allergies',
            'Family History' => 'family_history',
            'Past Medical History' => 'medical_history',
            'Medications' => 'medications',
            'Social History' => 'social_history',
            'Mental Health' => 'mental_health',
            'Lifestyle' => 'lifestyle',
            'Safeguarding' => 'safeguarding',
            'Notes' => 'notes',
            'Comments' => 'comments',
            'Safety Netting' => 'safety_netting',
            'Preventative Care' => 'preventative_care',
            'Plan' => 'plan'
        ];
        
        $data = [];
        
        foreach ($categories as $category => $key) {
            $pattern = "/$category:(.*?)(?=(?:" . implode('|', array_keys($categories)) . "):|$)/s";
            if (preg_match($pattern, $text, $matches)) {
                $data[$key] = trim($matches[1]);
            } else {
                $data[$key] = '';
            }
        }
        
        return $data;
    }

    /**
     * Populates encounter tabs with extracted data
     */
    public function populateEncounterTabs()
    {
        try {
            if (!kcCheckPermission('patient_encounter_add')) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $request_data = $this->request->getInputs();
            
            // Validate request
            if (!isset($request_data['data']) || empty($request_data['data'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('No data provided', 'kc-lang')
                ]);
            }
            
            if (!isset($request_data['encounter_id']) || empty($request_data['encounter_id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Encounter ID is required', 'kc-lang')
                ]);
            }
            
            $data = json_decode(stripslashes($request_data['data']), true);
            $encounter_id = (int) $request_data['encounter_id'];
            
            // Verify encounter permission
            if (!(new KCPatientEncounter())->encounterPermissionUserWise($encounter_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            // Map the extracted data to encounter tabs
            $tabMappings = [
                'concerns' => 'concerns',
                'history' => 'history',
                'examination' => 'examination',
                'systems_review' => 'systems_review',
                'allergies' => 'allergies',
                'family_history' => 'family_history',
                'medical_history' => 'medical_history',
                'medications' => 'medications',
                'social_history' => 'social_history',
                'mental_health' => 'mental_health',
                'lifestyle' => 'lifestyle',
                'safeguarding' => 'safeguarding',
                'notes' => 'notes',
                'comments' => 'comments',
                'safety_netting' => 'safety_netting',
                'preventative_care' => 'preventative_care',
                'plan' => 'plan',
                'vitals' => 'vitals'
            ];
            
            $updated_tabs = [];
            
            foreach ($tabMappings as $dataKey => $tabType) {
                if (!empty($data[$dataKey])) {
                    // Check if tab already exists
                    $existing_tab = $this->db->get_row(
                        $this->db->prepare(
                            "SELECT * FROM {$this->db->prefix}kc_encounter_tabs 
                            WHERE encounter_id = %d AND type = %s 
                            ORDER BY id ASC LIMIT 1",
                            $encounter_id,
                            $tabType
                        )
                    );
                    
                    $tab_data = [
                        'encounter_id' => $encounter_id,
                        'type' => $tabType,
                        'content' => $data[$dataKey],
                        'metadata' => '{}',
                        'updated_at' => current_time('Y-m-d H:i:s')
                    ];
                    
                    if ($existing_tab) {
                        // Update existing tab
                        $this->db->update(
                            $this->db->prefix . 'kc_encounter_tabs',
                            $tab_data,
                            ['id' => $existing_tab->id]
                        );
                        
                        $updated_tabs[] = [
                            'id' => $existing_tab->id,
                            'type' => $tabType,
                            'status' => 'updated'
                        ];
                    } else {
                        // Insert new tab
                        $tab_data['created_at'] = current_time('Y-m-d H:i:s');
                        $this->db->insert(
                            $this->db->prefix . 'kc_encounter_tabs',
                            $tab_data
                        );
                        
                        $updated_tabs[] = [
                            'id' => $this->db->insert_id,
                            'type' => $tabType,
                            'status' => 'created'
                        ];
                    }
                }
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Encounter tabs populated successfully', 'kc-lang'),
                'data' => [
                    'updated_tabs' => $updated_tabs
                ]
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get system message for Mistral AI
     */
    private function get_system_message()
    {
        return "# Medical Transcript Analysis Prompt

Analyze the provided medical transcript and extract only the information that is explicitly mentioned. Organize the data into the following sections:

## Instructions
1. Extract only information that is explicitly stated in the transcript
2. Do not infer, assume, or add any information not directly mentioned
3. Use the exact wording from the transcript when possible
4. If a section has no information in the transcript, omit that section entirely
5. Do not include placeholders or notes about missing information
6. Include only factual information from the transcript

## Output Format

```
## Vital Signs
Temperature: [exact value if mentioned]
Pulse: [exact value if mentioned]
Blood Pressure: [exact value if mentioned]
Respiratory Rate: [exact value if mentioned]
Saturation: [exact value if mentioned]

## Present Concerns
[List exactly as mentioned in transcript]

## Present History
[Exact details from transcript]

## Examination
[Exact findings mentioned in transcript]

## Systems Review
[Any system reviews mentioned in transcript]

## Allergies
[Any allergies mentioned in transcript]

## Family History
[Any family history mentioned in transcript]

## Past Medical History
[Any past medical history mentioned in transcript]

## Medications
[Any medications mentioned in transcript]

## Social History
[Any social history mentioned in transcript]

## Mental Health
[Any mental health information mentioned in transcript]

## Lifestyle
[Any lifestyle information mentioned in transcript]

## Safeguarding
[Any safeguarding concerns or issues mentioned in transcript]

## Notes
[Any general notes from the consultation mentioned in transcript]

## Comments
[Any comments or remarks made by the clinician in transcript]

## Safety Netting
[Any safety netting advice or guidance mentioned in transcript]

## Preventative Care
[Any preventative care measures or advice mentioned in transcript]

## Plan
[Any management plan, follow-ups, or recommendations mentioned in transcript]
```

Remember to only include sections that have information explicitly mentioned in the transcript. If there is no information for a particular section, omit that section completely.";
    }
}