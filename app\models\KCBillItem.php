<?php


namespace App\models;

use App\baseClasses\KCModel;

class KCBillItem extends KCModel {

	public function __construct()
	{
		parent::__construct('bill_items');
	}
	public static function createAppointmentBillItem($appointment_id,$payment_status='unpaid'){
		$appointment_doctor_id = (new KCAppointment())->get_var([ 'id' => (int)$appointment_id], 'doctor_id');
        if(!empty($appointment_doctor_id)){
			$appointment_service = (new KCAppointmentServiceMapping())->get_by([ 'appointment_id' => (int)$appointment_id], '=', false);
            if(!empty($appointment_service)){
				$total_amount = 0;
                foreach ( $appointment_service  as $data ) {
                    // After migration, service_id in appointment mappings points to new services table ID
                    $get_service = (new KCService())->get_by([ 'id' => (int)$data->service_id],'=',true);
                    $data->service_charges = (float)$get_service->charges;
                    $total_amount = $total_amount + (float)$get_service->charges;
                }

                $patient_encounter_id = (new KCPatientEncounter())->get_var([ 'appointment_id' => (int)$appointment_id], 'id');
				if(empty($patient_encounter_id)){
                    return;
                }

                // Set actual_amount based on payment status
                $actual_amount = $total_amount;
                if ($payment_status === 'unpaid') {
                    // For unpaid bills, actual_amount should be the total amount (amount due)
                    $actual_amount = $total_amount;
                } else if ($payment_status === 'paid') {
                    // For paid bills, actual_amount should be 0 (no amount due)
                    $actual_amount = 0;
                }

                $patient_bill = (new KCBill())->insert([
					'encounter_id' =>(int)$patient_encounter_id,
					'appointment_id'=> (int)$appointment_id,
					'total_amount'=>$total_amount,
					'discount'=>0,
					'actual_amount'=>$actual_amount,
					'status'=>0,
					'payment_status'=>$payment_status,
					'created_at'=>current_time( 'Y-m-d H:i:s' )
				]);

				if($patient_bill){
                    foreach ( $appointment_service as $key => $data ) {
                        (new self())->insert([
                            'bill_id' => (int)$patient_bill,
                            'price'   => (float)$data->service_charges,
                            'qty'     => 1,
                            'item_id' => (int)$data->service_id,
                            'payment_status' => $payment_status, // Ensure bill items have the same payment status as the bill
                            'created_at' => current_time( 'Y-m-d H:i:s' )
                        ]);
                    }

				}

			}
		}
	}
}