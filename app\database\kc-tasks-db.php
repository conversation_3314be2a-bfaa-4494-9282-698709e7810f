<?php
require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

global $wpdb;
$kc_charset_collate = $wpdb->get_charset_collate();

// Tasks table
$table_name = $wpdb->prefix . 'kc_tasks'; 

$sql = "CREATE TABLE `{$table_name}` (
    id bigint(20) NOT NULL AUTO_INCREMENT,    
    title varchar(191) NOT NULL,   
    description text NULL,
    clinic_id bigint(20) UNSIGNED NOT NULL,
    creator_id bigint(20) UNSIGNED NOT NULL,
    patient_id bigint(20) UNSIGNED NULL,
    priority varchar(50) NOT NULL DEFAULT 'medium',
    status varchar(50) NOT NULL DEFAULT 'pending',
    due_date datetime NULL,
    created_at datetime NOT NULL,
    updated_at datetime NULL,
    reminder_date datetime NULL,
    repeating varchar(50) NULL DEFAULT 'none',
    category varchar(191) NULL,
    is_archived tinyint(1) UNSIGNED NOT NULL DEFAULT 0,
    PRIMARY KEY  (id)
) $kc_charset_collate;";

maybe_create_table($table_name, $sql);

// Task assignees table
$assignees_table = $wpdb->prefix . 'kc_task_assignees';

$sql = "CREATE TABLE `{$assignees_table}` (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    task_id bigint(20) NOT NULL,
    assignee_id bigint(20) UNSIGNED NOT NULL,
    assigned_at datetime NOT NULL,
    completed_at datetime NULL,
    PRIMARY KEY  (id),
    KEY task_id (task_id)
) $kc_charset_collate;";

maybe_create_table($assignees_table, $sql);

// Task comments table
$comments_table = $wpdb->prefix . 'kc_task_comments';

$sql = "CREATE TABLE `{$comments_table}` (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    task_id bigint(20) NOT NULL,
    user_id bigint(20) UNSIGNED NOT NULL,
    comment text NOT NULL,
    created_at datetime NOT NULL,
    PRIMARY KEY  (id),
    KEY task_id (task_id)
) $kc_charset_collate;";

maybe_create_table($comments_table, $sql);

// Task attachments table
$attachments_table = $wpdb->prefix . 'kc_task_attachments';

$sql = "CREATE TABLE `{$attachments_table}` (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    task_id bigint(20) NOT NULL,
    file_name varchar(191) NOT NULL,
    file_url varchar(255) NOT NULL,
    attachment_id bigint(20) UNSIGNED NOT NULL,
    uploaded_by bigint(20) UNSIGNED NOT NULL,
    uploaded_at datetime NOT NULL,
    PRIMARY KEY  (id),
    KEY task_id (task_id)
) $kc_charset_collate;";

maybe_create_table($attachments_table, $sql);