<template>
  <div class="iq-card">
    <div class="iq-card-header flex justify-between">
      <div class="iq-header-title">
        <h4 class="card-title">{{ $t('Dictation') }}</h4>
      </div>

      <div class="flex items-center">
        <button 
          class="bg-primary text-white rounded-md py-2 px-4 flex items-center"
          @click="openNewDictation()"
        >
          <i class="ri-add-line ri-lg mr-1"></i>
          {{ $t('New Dictation') }}
        </button>
      </div>
    </div>

    <div class="iq-card-body">
      <!-- Filters -->
      <div class="flex flex-wrap items-center justify-between mb-4">
        <div class="flex flex-wrap items-center">
          <!-- Search -->
          <input
            v-model="searchTerm"
            :placeholder="$t('Search...')"
            class="mb-2 mr-2 border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            style="width: 250px"
            @keyup.enter="searchDictations"
          />

          <!-- Patient filter -->
          <div v-if="isDoctor || isClinicAdmin || isAdmin" class="mb-2 mr-2" style="width: 250px">
            <select 
              v-model="selectedPatient"
              class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 text-left relative"
              @change="onPatientSelect($event.target.value)"
            >
              <option value="">{{ $t('Filter by Patient') }}</option>
              <option v-for="patient in patients" :key="patient.id" :value="patient.id">
                {{ patient.label }}
              </option>
            </select>
          </div>

          <!-- Search button -->
          <button 
            class="bg-primary text-white rounded-md py-2 px-4 mb-2 mr-2"
            @click="searchDictations"
          >
            <i class="ri-search-line"></i> {{ $t('Search') }}
          </button>

          <!-- Reset button -->
          <button 
            class="border border-primary text-primary hover:bg-gray-100 rounded-md py-2 px-4 mb-2"
            @click="resetFilters"
          >
            <i class="ri-refresh-line"></i> {{ $t('Reset') }}
          </button>
        </div>
      </div>

      <!-- Dictation list -->
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
          <thead>
            <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
              <th class="py-3 px-4 text-left">{{ $t('Title') }}</th>
              <th class="py-3 px-4 text-left">{{ $t('Preview') }}</th>
              <th class="py-3 px-4 text-left">{{ $t('Patient') }}</th>
              <th class="py-3 px-4 text-left">{{ $t('Doctor') }}</th>
              <th class="py-3 px-4 text-left">{{ $t('Created') }}</th>
              <th class="py-3 px-4 text-left">{{ $t('Actions') }}</th>
            </tr>
          </thead>
          <tbody class="text-gray-600 text-sm">
            <tr v-if="isLoading" class="border-b border-gray-200 hover:bg-gray-50">
              <td colspan="6" class="py-4 px-4 text-center">
                <div class="flex justify-center">
                  <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-12 w-12 mb-4"></div>
                </div>
                <p>{{ $t('Loading dictations...') }}</p>
              </td>
            </tr>
            <tr v-else-if="dictations.length === 0" class="border-b border-gray-200">
              <td colspan="6" class="py-4 px-4 text-center">{{ $t('No dictations found') }}</td>
            </tr>
            <tr v-for="item in dictations" :key="item.id" class="border-b border-gray-200 hover:bg-gray-50">
              <td class="py-3 px-4">
                <a
                  href="#"
                  class="text-primary font-medium"
                  @click.prevent="viewDictation(item)"
                >
                  {{ item.title }}
                </a>
              </td>
              <td class="py-3 px-4">{{ item.content_preview }}</td>
              <td class="py-3 px-4">
                <span v-if="item.patient_name">{{ item.patient_name }}</span>
                <span v-else class="text-gray-400">{{ $t('No patient') }}</span>
              </td>
              <td class="py-3 px-4">{{ item.doctor_name }}</td>
              <td class="py-3 px-4">{{ item.created_at_formatted }}</td>
              <td class="py-3 px-4">
                <div class="flex">
                  <button
                    class="mr-2 text-gray-600 hover:text-primary"
                    @click="viewDictation(item)"
                    title="View"
                  >
                    <i class="ri-eye-line text-lg"></i>
                  </button>
                  <button
                    class="mr-2 text-gray-600 hover:text-primary"
                    @click="editDictation(item)"
                    :disabled="!item.is_owner && !isAdmin && !isClinicAdmin"
                    title="Edit"
                  >
                    <i class="ri-edit-line text-lg"></i>
                  </button>
                  <button
                    class="text-gray-600 hover:text-red-500"
                    @click="confirmDelete(item)"
                    :disabled="!item.is_owner && !isAdmin && !isClinicAdmin"
                    title="Delete"
                  >
                    <i class="ri-delete-bin-line text-lg"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex justify-between items-center mt-4">
        <div class="flex-1 flex justify-center">
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button
              v-for="page in totalPages"
              :key="page"
              :class="[
                'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium hover:bg-gray-50',
                page === currentPage ? 'text-primary border-primary z-10' : 'text-gray-500',
              ]"
              @click="handlePageChange(page)"
            >
              {{ page }}
            </button>
          </nav>
        </div>

        <select
          v-model="perPage"
          class="ml-3 border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          style="width: 100px"
          @change="handlePerPageChange"
        >
          <option v-for="option in perPageOptions" :key="option" :value="option">
            {{ option }} / page
          </option>
        </select>
      </div>
    </div>

    <!-- Dictation Modal -->
    <KCModal
      v-if="showDictationModal"
      :title="modalTitle"
      size="xl"
      @close="showDictationModal = false"
      @hidden="resetModal"
    >
      <div class="dictation-modal p-4">
        <!-- Patient selection (when creating new) -->
        <div v-if="isCreateMode" class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Patient (optional)') }}</label>
          <select 
            v-model="form.patient_id"
            class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 text-left relative"
          >
            <option value="">{{ $t('Select Patient') }}</option>
            <option v-for="patient in patients" :key="patient.id" :value="patient.id">
              {{ patient.label }}
            </option>
          </select>
          <small class="text-gray-500">{{ $t('Leave empty for general dictation without patient context') }}</small>
        </div>

        <!-- Title field -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Title') }} <span class="text-red-500">*</span></label>
          <input
            v-model="form.title"
            :placeholder="$t('Enter dictation title')"
            :disabled="isViewMode"
            class="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>

        <!-- Dictation content -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('Content') }} <span class="text-red-500">*</span></label>
          
          <!-- Audio recording tools (only in edit/create mode) -->
          <div v-if="!isViewMode" class="audio-controls mb-3 bg-gray-50 p-4 rounded-md border border-gray-200">
            <div class="flex items-center">
              <!-- Record button -->
              <button
                :class="[
                  'flex items-center rounded-md py-2 px-4 font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
                  isRecording ? 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500' : 'bg-primary text-white hover:bg-blue-600 focus:ring-primary'
                ]"
                @click="toggleRecording"
              >
                <i :class="[isRecording ? 'ri-stop-fill' : 'ri-mic-fill', 'mr-1']"></i>
                {{ isRecording ? $t('Stop Recording') : $t('Start Dictation') }}
              </button>
              
              <!-- Pause/Resume button (when recording) -->
              <button
                v-if="isRecording"
                :class="[
                  'ml-2 flex items-center rounded-md py-2 px-4 font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
                  isPaused ? 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500' : 'border border-yellow-500 text-yellow-500 hover:bg-yellow-50 focus:ring-yellow-500'
                ]"
                @click="togglePause"
              >
                <i :class="[isPaused ? 'ri-play-fill' : 'ri-pause-fill', 'mr-1']"></i>
                {{ isPaused ? $t('Resume') : $t('Pause') }}
              </button>

              <!-- Recording status -->
              <div v-if="isRecording" class="flex items-center ml-3">
                <span class="recording-indicator mr-2"></span>
                <span>{{ formattedRecordingTime }}</span>
              </div>
            </div>

            <!-- Transcription status -->
            <div v-if="isTranscribing" class="mt-3 p-3 bg-gray-100 rounded-md">
              <div class="flex items-center">
                <div class="loader ease-linear rounded-full border-2 border-t-2 border-gray-200 h-5 w-5 mr-2"></div>
                <span>{{ $t('Transcribing audio...') }}</span>
              </div>
            </div>
            
            <!-- Post-transcription help message -->
            <div v-if="!isTranscribing && form.content && !isViewMode" class="mt-3 p-3 bg-blue-50 border border-blue-100 rounded-md text-blue-700">
              <div class="flex items-center">
                <i class="ri-information-line text-blue-500 mr-2 text-lg"></i>
                <div>
                  <p class="font-medium">{{ $t('Transcription complete') }}</p>
                  <p class="text-sm mt-1">{{ $t('Please review the text and click "Save" to save your dictation.') }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Rich text editor - simplified for this implementation -->
          <div v-if="!isViewMode" class="border border-gray-300 rounded-md">
            <!-- Simple toolbar -->
            <div class="flex items-center px-2 py-1 border-b border-gray-300 bg-gray-50">
              <button @click="formatDoc('bold')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-bold"></i>
              </button>
              <button @click="formatDoc('italic')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-italic"></i>
              </button>
              <button @click="formatDoc('underline')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-underline"></i>
              </button>
              <div class="h-4 border-r border-gray-300 mx-1"></div>
              <button @click="formatDoc('insertUnorderedList')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-list-unordered"></i>
              </button>
              <button @click="formatDoc('insertOrderedList')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-list-ordered"></i>
              </button>
              <div class="h-4 border-r border-gray-300 mx-1"></div>
              <button @click="formatDoc('formatBlock', '<h1>')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-h-1"></i>
              </button>
              <button @click="formatDoc('formatBlock', '<h2>')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-h-2"></i>
              </button>
              <button @click="formatDoc('formatBlock', '<h3>')" class="p-1 text-gray-700 hover:text-primary">
                <i class="ri-h-3"></i>
              </button>
            </div>
            
            <!-- Editor area -->
            <div 
              ref="editor"
              contenteditable="true"
              class="min-h-[200px] p-3 focus:outline-none overflow-auto"
              @input="updateContent"
            ></div>
          </div>
          
          <!-- View mode -->
          <div v-else class="p-3 border rounded-md bg-gray-50" v-html="form.content"></div>
        </div>

        <!-- Dictation metadata in view mode -->
        <div v-if="isViewMode" class="mt-4 border-t border-gray-200 pt-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="mb-2"><span class="font-medium">{{ $t('Created by:') }}</span> {{ currentDictation.doctor_name }}</p>
              <p class="mb-2"><span class="font-medium">{{ $t('Created on:') }}</span> {{ currentDictation.created_at_formatted }}</p>
              <p v-if="currentDictation.updated_at_formatted" class="mb-2">
                <span class="font-medium">{{ $t('Last updated:') }}</span> {{ currentDictation.updated_at_formatted }}
              </p>
            </div>
            <div>
              <p v-if="currentDictation.patient_name" class="mb-2">
                <span class="font-medium">{{ $t('Patient:') }}</span> {{ currentDictation.patient_name }}
              </p>
              <p v-if="currentDictation.clinic_name" class="mb-2">
                <span class="font-medium">{{ $t('Clinic:') }}</span> {{ currentDictation.clinic_name }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Fixed save button that appears at the bottom of the content -->
      <div v-if="!isViewMode && form.content" class="fixed-save-button">
        <button
          class="bg-green-600 hover:bg-green-700 text-white rounded-md py-3 px-6 flex items-center text-base font-medium shadow-lg"
          @click="saveDictation"
          :disabled="isLoading || !form.title || !form.content"
        >
          <i class="ri-save-line mr-2 text-lg"></i> {{ $t('Save Dictation') }}
        </button>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <button
            v-if="!isViewMode"
            class="bg-primary text-white rounded-md py-2 px-4 flex items-center save-button"
            @click="saveDictation"
            :disabled="isLoading || !form.title || !form.content"
          >
            <i class="ri-save-line mr-1"></i> {{ $t('Save') }}
          </button>
          <button
            v-if="isViewMode && (currentDictation?.is_owner || isAdmin || isClinicAdmin)"
            class="bg-primary text-white rounded-md py-2 px-4 flex items-center"
            @click="editDictation(currentDictation)"
          >
            <i class="ri-edit-line mr-1"></i> {{ $t('Edit') }}
          </button>
          <button
            class="border border-gray-300 text-gray-700 rounded-md py-2 px-4 flex items-center"
            @click="showDictationModal = false"
          >
            <i class="ri-close-line mr-1"></i> {{ $t('Close') }}
          </button>
        </div>
      </template>
    </KCModal>

    <!-- Delete confirmation modal -->
    <KCModal
      v-if="showDeleteModal"
      title="Confirm Delete"
      @close="showDeleteModal = false"
      @confirm="deleteDictation"
    >
      <p>{{ $t('Are you sure you want to delete this dictation?') }}</p>
      <p class="font-medium mt-2">{{ dictationToDelete ? dictationToDelete.title : '' }}</p>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <button 
            class="bg-red-500 text-white rounded-md py-2 px-4"
            @click="deleteDictation"
          >
            {{ $t('Delete') }}
          </button>
          <button 
            class="border border-gray-300 text-gray-700 rounded-md py-2 px-4"
            @click="showDeleteModal = false"
          >
            {{ $t('Cancel') }}
          </button>
        </div>
      </template>
    </KCModal>
  </div>
</template>

<script>
import SearchableSelect from '../../components/Common/SearchableSelect.vue';
import KCModal from '../../components/Modal/Index.vue';
import Recorder from '../../components/NewAppointment/recorder.js';
import {post, get} from '../../config/request.js';
import {mapGetters} from 'vuex';

export default {
  name: 'Dictation',
  
  components: {
    SearchableSelect,
    KCModal
  },
  
  data() {
    return {
      isLoading: false,
      isTranscribing: false,
      dictations: [],
      currentDictation: null,
      dictationToDelete: null,
      showDictationModal: false,
      showDeleteModal: false,
      modalMode: 'create', // 'create', 'edit', 'view'
      
      // Search and filters
      searchTerm: '',
      selectedPatient: null,
      
      // Pagination
      currentPage: 1,
      perPage: 10,
      totalRows: 0,
      perPageOptions: [5, 10, 25, 50, 100],
      
      // Patients list for dropdown
      patients: [],
      
      // Form
      form: {
        id: null,
        title: '',
        content: '',
        patient_id: null,
        clinic_id: null
      },
      
      // Audio recording
      recorder: null,
      isRecording: false,
      isPaused: false,
      recordingStartTime: 0,
      recordingTime: 0,
      recordingTimer: null,
      audioChunks: []
    };
  },
  
  computed: {
    user() {
      return this.$store.state.userDataModule?.user || {};
    },
  
    isAdmin() {
      return this.user?.user_role === 'administrator';
    },
    
    isClinicAdmin() {
      return this.user?.user_role === 'kiviCare_clinic_admin';
    },
    
    isDoctor() {
      return this.user?.user_role === 'kiviCare_doctor';
    },
    
    isCreateMode() {
      return this.modalMode === 'create';
    },
    
    isEditMode() {
      return this.modalMode === 'edit';
    },
    
    isViewMode() {
      return this.modalMode === 'view';
    },
    
    modalTitle() {
      if (this.isCreateMode) return this.$t('New Dictation');
      if (this.isEditMode) return this.$t('Edit Dictation');
      return this.$t('View Dictation');
    },
    
    formattedRecordingTime() {
      const minutes = Math.floor(this.recordingTime / 60);
      const seconds = this.recordingTime % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },
    
    totalPages() {
      return Math.ceil(this.totalRows / this.perPage) || 1;
    },
    
    patientOptions() {
      return this.patients || [];
    }
  },
  
  created() {
    this.loadDictations();
    this.loadPatients();
  },
  
  beforeDestroy() {
    // Clean up any recording resources
    if (this.recorder) {
      if (this.isRecording) {
        this.stopRecording();
      }
      this.recorder = null;
    }
    
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  },
  
  methods: {
    // Rich text editor commands
    formatDoc(command, value = null) {
      document.execCommand(command, false, value);
      this.$refs.editor.focus();
      this.updateContent();
    },
    
    // Track content changes
    updateContent() {
      if (this.$refs.editor) {
        const content = this.$refs.editor.innerHTML;
        console.log('Content updated, length:', content.length);
        this.form.content = content;
        
        // Force a re-render of components that depend on form.content
        this.$forceUpdate();
      }
    },
    
    // Helper to set cursor to end of contenteditable
    setCursorToEnd(element) {
      try {
        // Create a range and selection
        const range = document.createRange();
        const selection = window.getSelection();
        
        // Set range to end of element
        range.selectNodeContents(element);
        range.collapse(false); // false means collapse to end
        
        // Apply selection
        selection.removeAllRanges();
        selection.addRange(range);
        
        // Focus the element
        element.focus();
      } catch (error) {
        console.error('Error setting cursor position:', error);
      }
    },
    
    // Load dictations with pagination
    async loadDictations() {
      this.isLoading = true;
      
      try {
        const postData = {
          page: this.currentPage,
          perPage: this.perPage,
          searchTerm: this.searchTerm
        };
        
        // Add patient filter if selected
        if (this.selectedPatient) {
          postData.patient_id = this.selectedPatient;
        }
        
        const response = await post('dictation/index', postData);
        
        if (response.data.status) {
          this.dictations = response.data.data || [];
          this.totalRows = response.data.total || 0;
        } else {
          this.dictations = [];
          this.totalRows = 0;
          if (this.$toast) {
            this.$toast.error(response.data.message);
          } else {
            console.error(response.data.message);
          }
        }
      } catch (error) {
        console.error('Error loading dictations:', error);
        if (this.$toast) {
          this.$toast.error(this.$t('Failed to load dictations'));
        }
      } finally {
        this.isLoading = false;
      }
    },
    
    // Load patients for filters and selection
    async loadPatients() {
      try {
        // Initialize empty array to prevent errors
        this.patients = [];
        
        console.log('Loading patients...');
        const response = await get('patient_list');
        console.log('Patient list response:', response);
        
        if (response && response.data && response.data.status) {
          // Map patients to the format expected by SearchableSelect
          const patientData = response.data.data || [];
          console.log('Raw patient data:', patientData);
          
          // Safely map patient data with error handling for each patient
          this.patients = patientData.map(patient => {
            // Make sure all needed fields exist before accessing them
            if (patient && typeof patient === 'object') {
              // Convert ID to string safely
              const id = patient.id ? patient.id.toString() : '';
              
              // Safely concatenate name fields
              const firstName = patient.first_name || '';
              const lastName = patient.last_name || '';
              
              const formattedPatient = {
                id: id,
                label: `${firstName} ${lastName}`.trim()
              };
              
              console.log('Formatted patient:', formattedPatient);
              return formattedPatient;
            }
            
            // Return empty placeholder for invalid entries
            return { id: '', label: 'Unknown Patient' };
          }).filter(p => p.id !== ''); // Filter out invalid entries
          
          console.log('Final patient list:', this.patients);
          
          // Force a re-render of the component to update the dropdown
          this.$forceUpdate();
        } else {
          console.warn('Failed to load patients:', response?.data ? response.data.message : 'Invalid response');
        }
      } catch (error) {
        console.error('Error loading patients:', error);
        this.patients = []; // Ensure we have a fallback
      }
    },
    
    // Search dictations
    searchDictations() {
      this.currentPage = 1;
      this.loadDictations();
    },
    
    // Reset filters
    resetFilters() {
      this.searchTerm = '';
      this.selectedPatient = null;
      this.currentPage = 1;
      this.loadDictations();
    },
    
    // Handle page change
    handlePageChange(page) {
      this.currentPage = page;
      this.loadDictations();
    },
    
    // Handle per page change
    handlePerPageChange() {
      this.currentPage = 1;
      this.loadDictations();
    },
    
    // Select patient for filter
    onPatientSelect(patientId) {
      // patientId can be empty string or a string ID
      this.selectedPatient = patientId;
      this.searchDictations();
    },
    
    // Open new dictation modal
    openNewDictation() {
      this.modalMode = 'create';
      this.resetForm();
      this.showDictationModal = true;
      
      // Initialize recorder
      this.initializeRecorder();
      
      // Wait for DOM to update and editor to be available
      this.$nextTick(() => {
        if (this.$refs.editor) {
          this.$refs.editor.innerHTML = '';
        }
      });
    },
    
    // View dictation details
    async viewDictation(dictation) {
      this.isLoading = true;
      
      try {
        const response = await post('dictation/get-details', {
          id: dictation.id
        });
        
        if (response.data.status) {
          this.currentDictation = response.data.data;
          this.form = {
            id: this.currentDictation.id,
            title: this.currentDictation.title,
            content: this.currentDictation.content,
            patient_id: this.currentDictation.patient_id,
            clinic_id: this.currentDictation.clinic_id
          };
          
          this.modalMode = 'view';
          this.showDictationModal = true;
        } else {
          if (this.$toast) {
            this.$toast.error(response.data.message);
          } else {
            console.error(response.data.message);
          }
        }
      } catch (error) {
        console.error('Error loading dictation details:', error);
        if (this.$toast) {
          this.$toast.error(this.$t('Failed to load dictation details'));
        }
      } finally {
        this.isLoading = false;
      }
    },
    
    // Edit dictation
    editDictation(dictation) {
      if (this.isViewMode) {
        // If currently in view mode, switch to edit mode
        this.modalMode = 'edit';
        
        // Initialize recorder
        this.initializeRecorder();
        
        // Wait for DOM to update before setting editor content
        this.$nextTick(() => {
          if (this.$refs.editor) {
            this.$refs.editor.innerHTML = this.form.content;
          }
        });
      } else {
        // Load the dictation first
        this.viewDictation(dictation).then(() => {
          this.modalMode = 'edit';
          
          // Initialize recorder
          this.initializeRecorder();
          
          // Wait for DOM to update before setting editor content
          this.$nextTick(() => {
            if (this.$refs.editor) {
              this.$refs.editor.innerHTML = this.form.content;
            }
          });
        });
      }
    },
    
    // Initialize audio recorder
    async initializeRecorder() {
      // Don't initialize if already initialized
      if (this.recorder) return;
      
      try {
        this.recorder = new Recorder();
        
        try {
          await this.recorder.initialize();
          
          // Set up event handlers
          this.recorder.onStop = () => {
            this.processRecording();
          };
          
          this.recorder.onDataAvailable = (data) => {
            this.audioChunks.push(data);
          };
          
          this.recorder.onError = (error) => {
            console.error('Recording error:', error);
            if (this.$toast) {
              this.$toast.error(this.$t('Audio recording error. Please try again.'));
            } else {
              console.error('Audio recording error. Please try again.');
            }
            this.isRecording = false;
            this.stopRecordingTimer();
          };
        } catch (initError) {
          console.error('Failed to initialize recorder:', initError);
          if (this.$toast) {
            this.$toast.error(this.$t('Microphone access denied. Please check browser permissions.'));
          } else {
            console.error('Microphone access denied. Please check browser permissions.');
          }
          this.recorder = null; // Reset recorder if initialization failed
        }
      } catch (error) {
        console.error('Failed to create recorder:', error);
        if (this.$toast) {
          this.$toast.error(this.$t('Failed to initialize audio recording. Please check microphone permissions.'));
        } else {
          console.error('Failed to initialize audio recording. Please check microphone permissions.');
        }
      }
    },
    
    // Toggle recording on/off
    toggleRecording() {
      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },
    
    // Start recording
    startRecording() {
      if (!this.recorder) {
        this.initializeRecorder().then(() => {
          if (this.recorder) {
            this.startRecordingInternal();
          } else {
            if (this.$toast) {
              this.$toast.error(this.$t('Audio recorder not initialized. Please check browser permissions.'));
            } else {
              console.error('Audio recorder not initialized. Please check browser permissions.');
            }
          }
        }).catch(err => {
          console.error('Error initializing recorder:', err);
          if (this.$toast) {
            this.$toast.error(this.$t('Failed to initialize audio recording. Please check microphone permissions.'));
          } else {
            console.error('Failed to initialize audio recording. Please check microphone permissions.');
          }
        });
        return;
      }
      
      this.startRecordingInternal();
    },
    
    // Internal method to start recording after initialization
    startRecordingInternal() {
      try {
        this.audioChunks = [];
        this.isRecording = true;
        this.isPaused = false;
        this.recorder.start();
        
        // Start recording timer
        this.recordingStartTime = Date.now();
        this.recordingTime = 0;
        this.recordingTimer = setInterval(() => {
          this.recordingTime = Math.floor((Date.now() - this.recordingStartTime) / 1000);
        }, 1000);
      } catch (error) {
        console.error('Error starting recording:', error);
        this.isRecording = false;
        if (this.$toast) {
          this.$toast.error(this.$t('Failed to start recording. Please refresh the page and try again.'));
        } else {
          console.error('Failed to start recording. Please refresh the page and try again.');
        }
      }
    },
    
    // Stop recording
    stopRecording() {
      if (!this.recorder) return;
      
      try {
        this.recorder.stop();
        this.isRecording = false;
        this.isPaused = false;
      } catch (error) {
        console.error('Error stopping recording:', error);
      } finally {
        this.stopRecordingTimer();
      }
    },
    
    // Toggle pause/resume
    togglePause() {
      if (!this.recorder || !this.isRecording) return;
      
      if (this.isPaused) {
        this.recorder.resume();
        this.isPaused = false;
        
        // Adjust start time for accurate timer
        this.recordingStartTime = Date.now() - (this.recordingTime * 1000);
      } else {
        this.recorder.pause();
        this.isPaused = true;
      }
    },
    
    // Stop recording timer
    stopRecordingTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },
    
    // Process recorded audio
    async processRecording() {
      if (!this.audioChunks.length) {
        if (this.$toast) {
          this.$toast.warning(this.$t('No audio recorded'));
        } else {
          console.warn('No audio recorded');
        }
        return;
      }
      
      this.isTranscribing = true;
      
      try {
        // Create a blob from all audio chunks
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
        
        // Convert to base64
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        
        reader.onloadend = async () => {
          const base64Audio = reader.result;
          
          // Send to server for dictation transcription
          const response = await post('dictation/process-audio', {
            audio_file: base64Audio
          });
          
          if (response.data.status) {
            // Extract the transcribed text
            const fullTranscript = response.data.data.full_transcript;
            
            // Insert the transcript into the editor - using more direct approach
            if (this.$refs.editor) {
              try {
                // Direct approach - more reliable than selection API
                console.log('Inserting transcript:', fullTranscript);
                
                // Get current content
                const currentContent = this.$refs.editor.innerHTML || '';
                
                // Add a space if there's already content and it doesn't end with space
                const spacer = currentContent && !currentContent.endsWith(' ') ? ' ' : '';
                
                // Set the content directly
                this.$refs.editor.innerHTML = currentContent + spacer + fullTranscript;
                
                // Set cursor at the end
                this.setCursorToEnd(this.$refs.editor);
                
                console.log('Transcript inserted, new length:', this.$refs.editor.innerHTML.length);
              } catch (error) {
                console.error('Error handling text insertion:', error);
                
                // Extra fallback attempt
                try {
                  // Simplest approach as last resort
                  this.$refs.editor.innerHTML += fullTranscript;
                  console.log('Used fallback insertion');
                } catch (fallbackError) {
                  console.error('Even fallback insertion failed:', fallbackError);
                }
              }
              
              // Update form content - more aggressively
              this.form.content = this.$refs.editor.innerHTML;
              console.log('Updated form content:', this.form.content.length);
              
              // Auto-generate a title if empty
              if (!this.form.title) {
                // Create a title from the first few words (max 8 words or 60 chars)
                const titleText = fullTranscript.split(/\s+/).slice(0, 8).join(' ');
                this.form.title = titleText.substring(0, 60) + (titleText.length > 60 ? '...' : '');
              }
              
              // Add a large, floating save button directly in the editor
              this.$nextTick(() => {
                // Create an eye-catching save button that will appear in the editor
                const bigSaveButton = document.createElement('div');
                bigSaveButton.className = 'floating-save-btn';
                bigSaveButton.innerHTML = `
                  <button class="bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg shadow-xl flex items-center text-lg font-bold">
                    <i class="ri-save-line mr-2"></i> ${this.$t('SAVE NOW')}
                  </button>
                `;
                
                // Make clicking the floating button trigger the main save function
                bigSaveButton.querySelector('button').addEventListener('click', () => {
                  this.saveDictation();
                });
                
                // Find where to insert it - directly after the editor
                const editorContainer = this.$refs.editor.parentElement;
                if (editorContainer && editorContainer.parentElement) {
                  editorContainer.parentElement.appendChild(bigSaveButton);
                }
                
                // Force rendering of fixed save button
                this.$forceUpdate();
                
                // Show success alert
                alert(this.$t('Transcription complete! Click the SAVE NOW button to save your dictation.'));
                
                // Scroll to show the save button
                const modalBody = this.$el.querySelector('.dictation-modal');
                if (modalBody) {
                  modalBody.scrollTop = modalBody.scrollHeight;
                }
              });
              
              // Add a success banner at the top too
              const successBanner = document.createElement('div');
              successBanner.className = 'transcription-success-banner';
              successBanner.innerHTML = `
                <div class="flex items-center p-4 mb-4 text-green-800 border-t-4 border-green-300 bg-green-50" role="alert">
                  <svg class="flex-shrink-0 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                  <div class="ml-3 text-sm font-medium">
                    <strong class="text-base">${this.$t('Transcription complete')}</strong> - ${this.$t('Use the green SAVE NOW button to save your work')}
                  </div>
                </div>
              `;
              
              // Find a good place to insert the banner
              const audioControls = this.$el.querySelector('.audio-controls');
              if (audioControls) {
                audioControls.parentNode.insertBefore(successBanner, audioControls.nextSibling);
              }
            }
            
            if (this.$toast) {
              // Use longer duration for this important message (5 seconds)
              const toastOptions = { duration: 5000 };
              this.$toast.success(
                this.$t('Audio transcribed successfully') + ' - ' + this.$t('Ready to save'),
                toastOptions
              );
            } else {
              console.log('Audio transcribed successfully - Ready to save');
            }
          } else {
            if (this.$toast) {
              this.$toast.error(response.data.message || this.$t('Failed to transcribe audio'));
            } else {
              console.error(response.data.message || 'Failed to transcribe audio');
            }
          }
        };
      } catch (error) {
        console.error('Error processing recording:', error);
        if (this.$toast) {
          this.$toast.error(this.$t('Failed to process audio recording'));
        } else {
          console.error('Failed to process audio recording');
        }
      } finally {
        this.isTranscribing = false;
        this.audioChunks = [];
      }
    },
    
    // Save dictation
    async saveDictation() {
      if (!this.form.title || !this.form.content) {
        if (this.$toast) {
          this.$toast.error(this.$t('Title and content are required'));
        } else {
          console.error('Title and content are required');
        }
        return;
      }
      
      this.isLoading = true;
      
      try {
        const response = await post('dictation/save', this.form);
        
        if (response.data.status) {
          if (this.$toast) {
            this.$toast.success(response.data.message);
          } else {
            console.log(response.data.message);
          }
          this.showDictationModal = false;
          this.loadDictations();
        } else {
          if (this.$toast) {
            this.$toast.error(response.data.message);
          } else {
            console.error(response.data.message);
          }
        }
      } catch (error) {
        console.error('Error saving dictation:', error);
        if (this.$toast) {
          this.$toast.error(this.$t('Failed to save dictation'));
        } else {
          console.error('Failed to save dictation');
        }
      } finally {
        this.isLoading = false;
      }
    },
    
    // Confirm delete
    confirmDelete(dictation) {
      this.dictationToDelete = dictation;
      this.showDeleteModal = true;
    },
    
    // Delete dictation
    async deleteDictation() {
      if (!this.dictationToDelete) return;
      
      this.isLoading = true;
      
      try {
        const response = await post('dictation/delete', {
          id: this.dictationToDelete.id
        });
        
        if (response.data.status) {
          if (this.$toast) {
            this.$toast.success(response.data.message);
          } else {
            console.log(response.data.message);
          }
          this.showDeleteModal = false;
          this.loadDictations();
        } else {
          if (this.$toast) {
            this.$toast.error(response.data.message);
          } else {
            console.error(response.data.message);
          }
        }
      } catch (error) {
        console.error('Error deleting dictation:', error);
        if (this.$toast) {
          this.$toast.error(this.$t('Failed to delete dictation'));
        } else {
          console.error('Failed to delete dictation');
        }
      } finally {
        this.isLoading = false;
        this.dictationToDelete = null;
      }
    },
    
    // Reset form
    resetForm() {
      this.form = {
        id: null,
        title: '',
        content: '',
        patient_id: null,
        clinic_id: null
      };
    },
    
    // Reset modal on close
    resetModal() {
      // Stop recording if active
      if (this.isRecording) {
        this.stopRecording();
      }
      
      this.resetForm();
      this.currentDictation = null;
      
      // Small delay to allow modal to close fully before destroying recorder
      setTimeout(() => {
        if (this.recorder) {
          this.recorder = null;
        }
      }, 300);
    }
  }
};
</script>

<style scoped>
.recording-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ef4444;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.loader {
  animation: spin 1s linear infinite;
  border-top-color: #3b82f6;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Fixed save button styles */
.fixed-save-button {
  position: sticky;
  bottom: 20px;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  z-index: 50;
  animation: bounce-in 0.5s ease-out;
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  70% {
    transform: translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.fixed-save-button button {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(22, 163, 74, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(22, 163, 74, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(22, 163, 74, 0);
  }
}

/* Save button highlight animation */
.save-button-highlight {
  animation: highlight-save 1.5s ease;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
}

@keyframes highlight-save {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0);
  }
}

/* Editor styles */
[contenteditable="true"] {
  min-height: 200px;
}

[contenteditable="true"]:focus {
  outline: none;
}

/* Dictation modal adjustments */
.dictation-modal {
  padding-bottom: 80px; /* Add padding at bottom to ensure space for fixed save button */
}

/* Floating save button */
.floating-save-btn {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  animation: bounce-attention 2s infinite;
}

@keyframes bounce-attention {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.transcription-success-banner {
  position: sticky;
  top: 0;
  z-index: 40;
  margin-bottom: 15px;
}
</style>