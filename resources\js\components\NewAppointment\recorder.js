// recorder.js - Custom audio recorder implementation for Deepgram Nova 3
export default class Recorder {
    constructor() {
      this.mediaRecorder = null;
      this.stream = null;
      this.chunks = [];
      this.mimeType = 'audio/webm'; // WebM is well supported by Deepgram Nova 3
      this.onDataAvailable = null;
      this.onStop = null;
      this.onError = null;
      this.customRecorderEnabled = false;
      this.isPaused = false;
    }
  
    /**
     * Initialize the recorder by requesting access to the user's microphone
     * with optimal settings for medical transcription
     */
    async initialize() {
      try {
        console.log('Initializing recorder for Medroid Scribe Nova 3...');
        
        // Configure audio constraints for better quality with medical terminology
        const audioConstraints = {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100, // Higher sample rate for better accuracy with medical terms
          channelCount: 1    // Mono is sufficient and more reliable for transcription
        };
        
        // Try to get access to the microphone using standard APIs first
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          console.log('Using standard MediaDevices API');
          this.stream = await navigator.mediaDevices.getUserMedia({ 
            audio: audioConstraints 
          });
        } 
        // Try legacy API fallbacks
        else if (navigator.getUserMedia) {
          console.log('Using legacy getUserMedia API');
          this.stream = await new Promise((resolve, reject) => {
            navigator.getUserMedia({ audio: audioConstraints }, resolve, reject);
          });
        }
        // Try webkit-prefixed version
        else if (navigator.webkitGetUserMedia) {
          console.log('Using webkit getUserMedia API');
          this.stream = await new Promise((resolve, reject) => {
            navigator.webkitGetUserMedia({ audio: audioConstraints }, resolve, reject);
          });
        }
        // Try mozilla-prefixed version
        else if (navigator.mozGetUserMedia) {
          console.log('Using moz getUserMedia API');
          this.stream = await new Promise((resolve, reject) => {
            navigator.mozGetUserMedia({ audio: audioConstraints }, resolve, reject);
          });
        }
        // If all else fails, use audio worklet or scriptprocessor fallback
        else {
          console.log('No native recording APIs available, using custom implementation');
          await this.initializeCustomRecorder();
          return;
        }
        
        // If we got a stream, setup MediaRecorder
        if (this.stream) {
          console.log('Audio stream obtained');
          
          // Determine best mime type for recorder optimized for Deepgram Nova 3
          // Deepgram Nova 3 works best with WAV or high-quality formats
          // Default preference order is now wav > webm > ogg > mp4
          
          if (MediaRecorder.isTypeSupported('audio/wav')) {
            this.mimeType = 'audio/wav'; // Best for compatibility
          } else if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
            this.mimeType = 'audio/webm;codecs=opus'; // Good option for Deepgram
          } else if (MediaRecorder.isTypeSupported('audio/webm')) {
            this.mimeType = 'audio/webm';
          } else if (MediaRecorder.isTypeSupported('audio/ogg;codecs=opus')) {
            this.mimeType = 'audio/ogg;codecs=opus'; 
          } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
            this.mimeType = 'audio/mp4';
          }
          
          console.log('Selected MIME type for recording:', this.mimeType);
          
          // Configure for better audio quality with bit rate options
          const options = { 
            mimeType: this.mimeType,
            audioBitsPerSecond: 128000 // 128 kbps for clear audio
          };
          
          this.mediaRecorder = new MediaRecorder(this.stream, options);
          
          // Set up event handlers
          this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              this.chunks.push(event.data);
              if (this.onDataAvailable) {
                this.onDataAvailable(event.data);
              }
            }
          };
          
          this.mediaRecorder.onstop = () => {
            if (this.onStop) {
              this.onStop();
            }
          };
          
          this.mediaRecorder.onerror = (event) => {
            console.error('MediaRecorder error:', event);
            if (this.onError) {
              this.onError(event.error);
            }
          };
          
          console.log('Recorder initialized successfully for Medroid Scribe Nova 3');
        } else {
          throw new Error('Could not get audio stream');
        }
      } catch (error) {
        console.error('Error initializing recorder:', error);
        
        // Try fallback to custom recorder implementation
        try {
          console.log('Trying custom recorder implementation');
          await this.initializeCustomRecorder();
        } catch (fallbackError) {
          console.error('All recording methods failed:', fallbackError);
          throw new Error('Could not initialize audio recording: ' + error.message);
        }
      }
    }
  
    /**
     * Initialize a custom recorder implementation using AudioContext
     * This is a last resort fallback when MediaRecorder is not available
     */
    async initializeCustomRecorder() {
      try {
        // Get audio context based on browser support
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (!AudioContext) {
          throw new Error('AudioContext not supported');
        }
        
        // Create audio context
        const audioContext = new AudioContext();
        
        // Use getUserMedia if available to get microphone access
        let stream;
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          stream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
              sampleRate: 44100
            } 
          });
        } else if (navigator.getUserMedia) {
          stream = await new Promise((resolve, reject) => {
            navigator.getUserMedia({ audio: true }, resolve, reject);
          });
        } else {
          throw new Error('Cannot access microphone');
        }
        
        this.stream = stream;
        
        // Create source node from the stream
        const source = audioContext.createMediaStreamSource(stream);
        
        // Set up script processor node for audio processing
        // This is deprecated but has wider support than AudioWorklet
        const processor = audioContext.createScriptProcessor(4096, 1, 1);
        
        // Connect nodes
        source.connect(processor);
        processor.connect(audioContext.destination);
        
        // Flag to track recording state
        let isRecording = false;
        
        // Set up raw audio data collection
        const audioData = [];
        
        processor.onaudioprocess = (e) => {
          if (!isRecording) return;
          
          // Get audio data from input channel
          const channelData = e.inputBuffer.getChannelData(0);
          const buffer = new Float32Array(channelData.length);
          buffer.set(channelData);
          audioData.push(buffer);
          
          // Create a blob and send it via onDataAvailable
          if (audioData.length > 10) { // Send chunks periodically
            const mergedData = this.mergeBuffers(audioData);
            const wavBlob = this.createWaveBlob(mergedData, audioContext.sampleRate);
            
            if (this.onDataAvailable) {
              this.onDataAvailable(wavBlob);
            }
            
            // Reset the collection for next chunk
            audioData.length = 0;
          }
        };
        
        // Set up custom API that mimics MediaRecorder
        this.customStart = () => {
          isRecording = true;
          this.chunks = [];
          console.log('Custom recorder started');
        };
        
        this.customStop = () => {
          isRecording = false;
          
          // Process any remaining audio data
          if (audioData.length > 0) {
            const mergedData = this.mergeBuffers(audioData);
            const wavBlob = this.createWaveBlob(mergedData, audioContext.sampleRate);
            
            if (this.onDataAvailable) {
              this.onDataAvailable(wavBlob);
            }
            
            audioData.length = 0;
          }
          
          // Trigger onStop callback
          if (this.onStop) {
            setTimeout(() => this.onStop(), 100);
          }
          
          // Clean up
          processor.disconnect();
          source.disconnect();
          
          // Stop all tracks in the stream
          if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
          }
          
          console.log('Custom recorder stopped');
        };
        
        this.customRecorderEnabled = true;
        this.mimeType = 'audio/wav'; // Custom recorder outputs WAV
        console.log('Custom recorder initialized successfully');
      } catch (error) {
        console.error('Failed to initialize custom recorder:', error);
        throw error;
      }
    }
  
    /**
     * Start recording audio
     */
    start() {
      if (this.customRecorderEnabled) {
        this.customStart(); // Call the custom implementation's start method
        return;
      }
      
      if (this.mediaRecorder && this.mediaRecorder.state === 'inactive') {
        this.chunks = [];
        this.mediaRecorder.start(1000); // Collect data in 1-second chunks
        console.log('Recording started');
      } else {
        console.warn('Cannot start recording: MediaRecorder not ready or already recording');
      }
    }
  
    /**
     * Stop recording audio
     */
    stop() {
      if (this.customRecorderEnabled) {
        this.customStop(); // Call the custom implementation's stop method
        return;
      }
      
      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop();
        console.log('Recording stopped');
        
        // Stop all tracks in the stream
        if (this.stream) {
          this.stream.getTracks().forEach(track => track.stop());
        }
      } else {
        console.warn('Cannot stop recording: MediaRecorder not recording');
      }
    }
    
    /**
     * Pause recording (if browser supports it)
     */
    pause() {
      this.isPaused = true;
      
      if (this.customRecorderEnabled) {
        // For custom recorder, we'll handle pausing by disabling tracks
        if (this.stream) {
          this.stream.getTracks().forEach(track => {
            track.enabled = false;
          });
        }
        console.log('Custom recorder paused');
        return;
      }
      
      // Try to use the native pause method if available (newer browsers)
      if (this.mediaRecorder && this.mediaRecorder.state === 'recording' && this.mediaRecorder.pause) {
        try {
          this.mediaRecorder.pause();
          console.log('Recording paused');
          return;
        } catch (e) {
          console.warn('MediaRecorder pause failed, using fallback:', e);
        }
      }
      
      // Fallback to disabling tracks if pause isn't supported
      if (this.stream) {
        this.stream.getTracks().forEach(track => {
          track.enabled = false;
        });
        console.log('Recording paused (tracks disabled)');
      }
    }
    
    /**
     * Resume recording (if previously paused)
     */
    resume() {
      this.isPaused = false;
      
      if (this.customRecorderEnabled) {
        // For custom recorder, re-enable tracks
        if (this.stream) {
          this.stream.getTracks().forEach(track => {
            track.enabled = true;
          });
        }
        console.log('Custom recorder resumed');
        return;
      }
      
      // Try to use the native resume method if available
      if (this.mediaRecorder && this.mediaRecorder.state === 'paused' && this.mediaRecorder.resume) {
        try {
          this.mediaRecorder.resume();
          console.log('Recording resumed');
          return;
        } catch (e) {
          console.warn('MediaRecorder resume failed, using fallback:', e);
        }
      }
      
      // Fallback to re-enabling tracks
      if (this.stream) {
        this.stream.getTracks().forEach(track => {
          track.enabled = true;
        });
        console.log('Recording resumed (tracks enabled)');
      }
    }
  
    /**
     * Merge multiple audio buffers into a single buffer
     */
    mergeBuffers(bufferArray) {
      let totalLength = 0;
      
      // Calculate total length
      for (let i = 0; i < bufferArray.length; i++) {
        totalLength += bufferArray[i].length;
      }
      
      // Create a new buffer of the total length
      const result = new Float32Array(totalLength);
      let offset = 0;
      
      // Copy each buffer into the result
      for (let i = 0; i < bufferArray.length; i++) {
        result.set(bufferArray[i], offset);
        offset += bufferArray[i].length;
      }
      
      return result;
    }
  
    /**
     * Create a WAV blob from Float32Array audio data
     */
    createWaveBlob(audioData, sampleRate) {
      // Create WAV header
      const numChannels = 1;
      const bitsPerSample = 16;
      const bytesPerSample = bitsPerSample / 8;
      const blockAlign = numChannels * bytesPerSample;
      const byteRate = sampleRate * blockAlign;
      const dataSize = audioData.length * bytesPerSample;
      const buffer = new ArrayBuffer(44 + dataSize);
      const view = new DataView(buffer);
      
      // Write WAV header
      // "RIFF" chunk descriptor
      this.writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + dataSize, true);
      this.writeString(view, 8, 'WAVE');
      
      // "fmt " sub-chunk
      this.writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true); // fmt chunk size
      view.setUint16(20, 1, true); // audio format (1 for PCM)
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, byteRate, true);
      view.setUint16(32, blockAlign, true);
      view.setUint16(34, bitsPerSample, true);
      
      // "data" sub-chunk
      this.writeString(view, 36, 'data');
      view.setUint32(40, dataSize, true);
      
      // Write audio data
      this.floatTo16BitPCM(view, 44, audioData);
      
      // Create blob and return
      return new Blob([view], { type: 'audio/wav' });
    }
  
    /**
     * Write a string into a DataView at the specified offset
     */
    writeString(view, offset, string) {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    }
  
    /**
     * Convert floating point audio data to 16-bit PCM
     */
    floatTo16BitPCM(output, offset, input) {
      for (let i = 0; i < input.length; i++, offset += 2) {
        const s = Math.max(-1, Math.min(1, input[i]));
        output.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
      }
    }
}