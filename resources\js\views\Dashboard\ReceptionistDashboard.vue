<template>
  <main class="p-6">
    <!-- Patient Check-In Link Card -->
    <div class="bg-white rounded-lg shadow-md mb-6 p-5">
      <h5 class="text-lg font-semibold mb-3">{{ formTranslation.common.patient_check_in_link || 'Patient Check-In Link' }}</h5>
      
      <div v-if="fetchingToken" class="flex items-center justify-center p-4">
        <div class="animate-spin h-6 w-6 border-3 border-primary border-t-transparent rounded-full mr-3"></div>
        <span>{{ formTranslation.common.generating_link || 'Generating secure check-in link...' }}</span>
      </div>
      
      <div v-else class="flex space-x-2">
        <div class="flex-1 bg-gray-100 rounded-lg p-3 pl-4 pr-4 relative">
          <input type="text" class="bg-transparent w-full text-gray-700 pr-10" readonly
            :value="checkInUrl"
            ref="checkInUrlInput">
          <button 
            @click="copyCheckInUrl" 
            class="absolute right-2 top-2 p-1.5 text-primary hover:text-primary-dark focus:outline-none"
            :class="{'text-green-600': copied}">
            <i class="fas" :class="copied ? 'fa-check' : 'fa-copy'"></i>
          </button>
        </div>
        <a :href="checkInUrl" target="_blank" :disabled="fetchingToken" class="bg-primary hover:bg-primary-dark text-white px-3 py-2 rounded-lg flex items-center">
          <i class="fas fa-external-link-alt mr-2"></i>
          {{ formTranslation.common.open || 'Open' }}
        </a>
      </div>
      
      <div v-if="!fetchingToken" class="mt-2">
        <div class="flex items-center">
          <button @click="fetchClinicToken" class="text-sm text-primary hover:text-primary-dark flex items-center">
            <i class="fas fa-sync-alt mr-1"></i> {{ formTranslation.common.refresh_link || 'Refresh link' }}
          </button>
          <p class="text-sm text-gray-600 ml-4 flex-1">
            {{ formTranslation.common.share_check_in_link || 'Share this link with patients to allow them to check in for their appointments. This link will expire in 20 days for security.' }}
          </p>
        </div>
        <div class="mt-2 py-1 px-2 bg-blue-50 border border-blue-200 rounded-md">
          <p class="text-xs text-blue-700">
            <i class="fas fa-info-circle mr-1"></i> {{ formTranslation.common.link_expires_notice || 'Note: Check-in links expire after 20 days for security reasons. You can generate a new link anytime.' }}
          </p>
        </div>
      </div>
    </div>
    
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 mb-6">
      <!-- Total Patients Card -->
      <div v-if="kcCheckPermission('dashboard_total_patient')" class="w-full">
        <router-link :to="{ name: 'patient' }" class="block">
          <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
            <div class="p-4">
              <div class="flex justify-between items-center">
                <div>
                  <h5 class="text-sm text-gray-600 uppercase mb-1">
                    {{ formTranslation.dashboard.patients }}
                  </h5>
                  <span v-if="isdataLoading" class="text-2xl font-bold">
                    <i class="fas fa-spinner fa-spin"></i>
                  </span>
                  <span v-else class="text-2xl font-bold">
                    {{ dashboardData.patient_count }}
                  </span>
                </div>
                <div class="flex-shrink-0">
                  <div class="p-3 bg-gradient-to-r from-red-500 to-red-600 rounded-full text-white shadow-md">
                    <i class="fas fa-user-injured text-xl"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-600">
                {{ formTranslation.dashboard.total_visited_patients }}
              </p>
            </div>
          </div>
        </router-link>
      </div>

      <!-- Total Doctors Card -->
      <div v-if="kcCheckPermission('dashboard_total_doctor')" class="w-full">
        <router-link :to="{ name: 'doctor' }" class="block">
          <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
            <div class="p-4">
              <div class="flex justify-between items-center">
                <div>
                  <h5 class="text-sm text-gray-600 uppercase mb-1">
                    {{ formTranslation.dashboard.total_doctors }}
                  </h5>
                  <span v-if="isdataLoading" class="text-2xl font-bold">
                    <i class="fas fa-spinner fa-spin"></i>
                  </span>
                  <span v-else class="text-2xl font-bold">
                    {{ dashboardData.doctor_count }}
                  </span>
                </div>
                <div class="flex-shrink-0">
                  <div class="p-3 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full text-white shadow-md">
                    <i class="fas fa-user-md text-xl"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-600">
                {{ formTranslation.dashboard.total_clinic_doctors }}
              </p>
            </div>
          </div>
        </router-link>
      </div>

      <!-- Total Appointments Card -->
      <div v-if="kcCheckPermission('dashboard_total_appointment')" class="w-full">
        <router-link :to="{ name: 'appointment-list.index' }" class="block">
          <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
            <div class="p-4">
              <div class="flex justify-between items-center">
                <div>
                  <h5 class="text-sm text-gray-600 uppercase mb-1">
                    {{ formTranslation.dashboard.total_appointments }}
                  </h5>
                  <span v-if="isdataLoading" class="text-2xl font-bold">
                    <i class="fas fa-spinner fa-spin"></i>
                  </span>
                  <span v-else class="text-2xl font-bold">
                    {{ dashboardData.appointment_count }}
                  </span>
                </div>
                <div class="flex-shrink-0">
                  <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full text-white shadow-md">
                    <i class="fas fa-calendar-check text-xl"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-600">
                {{ formTranslation.dashboard.total_clinic_appointments }}
              </p>
            </div>
          </div>
        </router-link>
      </div>

      <!-- Total Revenue Card -->
      <div v-if="kcCheckPermission('dashboard_total_revenue')" class="w-full">
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
          <div class="p-4">
            <div class="flex justify-between items-center">
              <div>
                <h5 class="text-sm text-gray-600 uppercase mb-1">
                  {{ formTranslation.dashboard.total_revenue }}
                </h5>
                <span v-if="isdataLoading" class="text-2xl font-bold">
                  <i class="fas fa-spinner fa-spin"></i>
                </span>
                <span v-else class="text-2xl font-bold">
                  {{ dashboardData.revenue }}
                </span>
              </div>
              <div class="flex-shrink-0">
                <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full text-white shadow-md">
                  <i class="fas fa-money-check-alt text-xl"></i>
                </div>
              </div>
            </div>
            <p class="mt-3 text-sm text-gray-600">
              {{ formTranslation.dashboard.total_clinic_revenue }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Section -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <appointment-calender 
        v-if="reloadCalender" 
        @reloadAppointment="init"
      ></appointment-calender>
    </div>
  </main>
</template>

<script>

import AppointmentList from "../../components/appointment/AppointmentList";
import {get, post} from "../../config/request";
import VueApexCharts from 'vue-apexcharts'

export default {
  components: {
    AppointmentList,
    apexcharts: VueApexCharts
  },
  data: () => {
    return {
      isLoading: false,
      dashboardData: {},
      isAppointmentReload: false,
      appointmentRequest: {},
      reloadCalender: true,
      isdataLoading:true,
      totalCardEnable:0,
      copied: false,
      tokenUrl: '',
      fetchingToken: false
    }
  },
  mounted() {
    this.init();
    if(this.kcCheckPermission('dashboard_total_patient')){
      this.totalCardEnable++;
    }
    if(this.kcCheckPermission('dashboard_total_doctor')){
      this.totalCardEnable++;
    }
    if(this.kcCheckPermission('dashboard_total_appointment')){
      this.totalCardEnable++;
    }
    if(this.kcCheckPermission('dashboard_total_revenue')){
      this.totalCardEnable++;
    }
    if(this.totalCardEnable > 0){
      this.totalCardEnable = 12/this.totalCardEnable;
    }
    
    // Wait a moment for reactive data to become available before fetching clinic token
    setTimeout(() => {
      // Fetch the clinic token
      this.fetchClinicToken();
    }, 2000);
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    checkInUrl() {
      // Return the token URL if available, otherwise show loading or fallback
      if (this.fetchingToken) {
        return this.formTranslation.common.loading || 'Loading...';
      }
      
      if (this.tokenUrl) {
        return this.tokenUrl;
      }
      
      // If we're in a loading state but not fetching token, show a placeholder
      if (this.isdataLoading) {
        return this.formTranslation.common.loading || 'Loading clinic information...';
      }
      
      // Try to get clinicId from various sources for a fallback URL
      let clinicId = null;
      
      // First check if we have dashboard data response
      if (this.dashboardData) {
        // Try to get clinic ID directly
        if (this.dashboardData.clinic_id) {
          clinicId = this.dashboardData.clinic_id;
          console.log('checkInUrl: Found clinic_id in dashboardData:', clinicId);
        }
        // Look for clinic_details object
        else if (this.dashboardData.clinic_details) {
          if (this.dashboardData.clinic_details.id) {
            clinicId = this.dashboardData.clinic_details.id;
            console.log('checkInUrl: Found clinic id in clinic_details:', clinicId);
          }
        }
      }
      
      // If still no clinic ID, try other sources
      if (!clinicId) {
        // Try state store
        if (this.$store.state.userDataModule.clinic_id) {
          clinicId = this.$store.state.userDataModule.clinic_id;
          console.log('checkInUrl: Using clinic ID from store:', clinicId);
        } 
        // Try user data
        else if (this.userData && this.userData.clinic_id) {
          clinicId = this.userData.clinic_id;
          console.log('checkInUrl: Using clinic ID from userData:', clinicId);
        } 
        // Try user in store
        else if (this.$store.state.userDataModule.user && this.$store.state.userDataModule.user.clinic_id) {
          clinicId = this.$store.state.userDataModule.user.clinic_id;
          console.log('checkInUrl: Using clinic ID from store user data:', clinicId);
        } 
        // Try window globals
        else if (window.kc_globals && window.kc_globals.clinic_id) {
          clinicId = window.kc_globals.clinic_id;
          console.log('checkInUrl: Using clinic ID from window.kc_globals:', clinicId);
        }
      }
      
      // Always default to clinic ID 1 if nothing else works or if it's 'auto'
      // This is a critical fallback for stability
      if (!clinicId || clinicId === 'auto' || !parseInt(clinicId)) {
        clinicId = 1;  // Default clinic ID 
        console.log('checkInUrl: Using default clinic ID (1) as final fallback');
      }
      
      // Generate a frontend token - this will always be the same for the same clinic ID
      // This ensures the token doesn't keep changing
      const simpleToken = this.generateFrontendToken(clinicId);
      
      // Return a token-based URL for better security
      return `${window.location.origin}/check-in/?token=${simpleToken}`;
    }
  },
  methods: {
    init: function () {
      this.getDashboardData();
      this.dashboardData = this.defaultDashboardData();
      this.appointmentRequest = this.defaultAppointmentRequest();
       this.$nextTick(() => {
                // Add the component back in
                this.reloadCalender = true;
        });
    },
    copyCheckInUrl() {
      const input = this.$refs.checkInUrlInput;
      input.select();
      document.execCommand('copy');
      
      // Show success state
      this.copied = true;
      
      // Reset after 2 seconds
      setTimeout(() => {
        this.copied = false;
      }, 2000);
    },
    // Generate a fixed token for each clinic - same token every time for the same clinic
    generateFrontendToken(clinicId) {
      // Create a simple hash based only on the clinic ID
      // This ensures the same clinic always gets the same token
      
      // Convert clinic ID to a string and pad it
      const paddedID = clinicId.toString().padStart(5, '0');
      
      // Create a base string with the clinic ID
      const baseString = `kivicare_clinic_${paddedID}_fixed_token`;
      
      // Generate a hash from this string
      let hash = 0;
      for (let i = 0; i < baseString.length; i++) {
        const char = baseString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      
      // Make it positive and convert to base36 string
      hash = Math.abs(hash);
      const tokenA = hash.toString(36);
      
      // Generate a second hash for added complexity
      let hash2 = 0;
      const reverseString = baseString.split('').reverse().join('');
      for (let i = 0; i < reverseString.length; i++) {
        const char = reverseString.charCodeAt(i);
        hash2 = ((hash2 << 5) - hash2) + char;
        hash2 = hash2 & hash2;
      }
      
      // Make it positive and convert to base36 string
      hash2 = Math.abs(hash2);
      const tokenB = hash2.toString(36);
      
      // Combine the two hashes for the final token
      return `${tokenA}-${tokenB}-${clinicId}`;
    },
    
    fetchClinicToken() {
      this.fetchingToken = true;
      console.log('Starting clinic ID detection process...');
      
      // Function to handle the token fetch with a determined clinic ID
      const fetchWithClinicId = (clinicId) => {
        console.log('Fetching token for clinic ID:', clinicId);
        
        // Save the determined clinic ID to various locations for future reference
        if (!this.dashboardData) {
          this.dashboardData = {};
        }
        this.dashboardData.clinic_id = clinicId;
        
        // Try to store in localStorage for persistence
        try {
          localStorage.setItem('kivicare_last_clinic_id', clinicId);
        } catch (e) {
          console.error('Could not save clinic ID to localStorage:', e);
        }
        
        // Set in store if possible
        if (this.$store && this.$store.state && this.$store.state.userDataModule) {
          this.$store.state.userDataModule.clinic_id = clinicId;
        }
        
        // Check if WordPress AJAX globals are available
        if (!window.kc_globals || !window.kc_globals.ajax_url) {
          console.log('WordPress AJAX globals not available, using fallback methods');
          
          // Generate a frontend token when server is unavailable
          const simpleToken = this.generateFrontendToken(clinicId);
          this.tokenUrl = `${window.location.origin}/check-in/?token=${simpleToken}`;
          console.log('Using frontend-generated token URL:', this.tokenUrl);
          this.fetchingToken = false;
          return;
        }
        
        // Make the AJAX request to get a server-generated token
        jQuery.ajax({
          url: window.kc_globals.ajax_url,
          type: 'POST',
          timeout: 15000, // 15 second timeout
          data: {
            action: 'kivicare_get_clinic_token',
            clinic_id: clinicId,
            security: window.kc_globals.nonce || ''
          },
          success: (response) => {
            console.log('Token response:', response);
            if (response.success) {
              this.tokenUrl = response.data.check_in_url;
              
              // Update our store with the confirmed clinic_id from the response
              if (response.data.clinic_id) {
                console.log('Server confirmed clinic ID:', response.data.clinic_id);
                this.dashboardData.clinic_id = response.data.clinic_id;
                try {
                  localStorage.setItem('kivicare_last_clinic_id', response.data.clinic_id);
                } catch (e) {
                  console.warn('Could not save confirmed clinic ID to localStorage');
                }
              }
            } else {
              console.error('Error getting token:', response.data ? response.data.message : 'Unknown error');
              // Fallback to frontend token
              const simpleToken = this.generateFrontendToken(clinicId);
              this.tokenUrl = `${window.location.origin}/check-in/?token=${simpleToken}`;
            }
            this.fetchingToken = false;
          },
          error: (error) => {
            console.error('Error fetching clinic token:', error);
            // Fallback to frontend token
            const simpleToken = this.generateFrontendToken(clinicId);
            this.tokenUrl = `${window.location.origin}/check-in/?token=${simpleToken}`;
            this.fetchingToken = false;
          }
        });
      };
      
      // Step 1: Try to get the clinic ID directly from the API
      // This is the most reliable method as it makes a direct request to the server
      const getClinicDirectly = () => {
        console.log('Making direct API request to get clinic profile...');
        
        return new Promise((resolve, reject) => {
          jQuery.ajax({
            url: window.location.origin + '/wp-admin/admin-ajax.php',
            type: 'POST',
            async: true,
            timeout: 8000, // 8 second timeout for this request
            data: {
              action: 'ajax_get',
              route_name: 'get_clinic_profile',
              _wpnonce: window.kc_globals ? window.kc_globals.nonce : ''
            },
            success: (response) => {
              console.log('Clinic profile API response:', response);
              if (response.data && response.data.id) {
                const clinicId = parseInt(response.data.id);
                console.log('Successfully got clinic ID directly from API:', clinicId);
                resolve(clinicId);
              } else {
                console.log('API response did not contain clinic ID');
                reject('No clinic ID in response');
              }
            },
            error: (error) => {
              console.error('Error fetching clinic profile from API:', error);
              reject(error);
            }
          });
        });
      };
      
      // Collect all possible clinic IDs from various sources
      const collectClinicIds = () => {
        const sources = {};
        let clinicId = null;
        
        // Check localStorage first for a previously saved clinic ID
        try {
          const savedClinicId = localStorage.getItem('kivicare_last_clinic_id');
          if (savedClinicId && parseInt(savedClinicId)) {
            sources.localStorage = parseInt(savedClinicId);
          }
        } catch (e) {
          console.log('Could not access localStorage:', e);
        }
        
        // Check our dashboardData
        if (this.dashboardData) {
          if (this.dashboardData.clinic_id) {
            sources.dashboardData = parseInt(this.dashboardData.clinic_id);
          } else if (this.dashboardData.clinic_details && this.dashboardData.clinic_details.id) {
            sources.dashboardDetails = parseInt(this.dashboardData.clinic_details.id);
          }
        }
        
        // Check Vue store
        if (this.$store && this.$store.state && this.$store.state.userDataModule) {
          if (this.$store.state.userDataModule.clinic_id) {
            sources.storeClinicId = parseInt(this.$store.state.userDataModule.clinic_id);
          }
          
          if (this.$store.state.userDataModule.user && this.$store.state.userDataModule.user.clinic_id) {
            sources.storeUserClinicId = parseInt(this.$store.state.userDataModule.user.clinic_id);
          }
        }
        
        // Check userData directly
        if (this.userData) {
          if (this.userData.clinic_id) {
            sources.userData = parseInt(this.userData.clinic_id);
          }
        }
        
        // Check window globals
        if (window.kc_globals && window.kc_globals.clinic_id) {
          sources.windowGlobals = parseInt(window.kc_globals.clinic_id);
        }
        
        // Look for hidden inputs
        const hiddenClinicInput = document.querySelector('input[name="clinic_id"]');
        if (hiddenClinicInput && hiddenClinicInput.value) {
          sources.hiddenInput = parseInt(hiddenClinicInput.value);
        }
        
        // Try to extract from URL if available
        const urlParams = new URLSearchParams(window.location.search);
        const urlClinicId = urlParams.get('clinic_id');
        if (urlClinicId) {
          sources.urlParam = parseInt(urlClinicId);
        }
        
        console.log('Collected possible clinic IDs from various sources:', sources);
        
        // Prioritize sources (the order matters)
        const priorityOrder = [
          'storeClinicId',      // Highest priority - direct from store
          'dashboardData',      // From current API response
          'dashboardDetails',   // Also from API response
          'windowGlobals',      // From global JS variables 
          'urlParam',           // From URL parameters
          'userData',           // From user object
          'storeUserClinicId',  // From user in store
          'localStorage',       // From persistent storage
          'hiddenInput'         // Lowest priority - from hidden form field
        ];
        
        // Find the first valid clinic ID from our priority list
        for (const source of priorityOrder) {
          if (sources[source] && parseInt(sources[source]) > 0) {
            clinicId = sources[source];
            console.log(`Using clinic ID ${clinicId} from source: ${source}`);
            break;
          }
        }
        
        return clinicId;
      };

      // Main execution flow
      // First try the direct API call, then fall back to collecting from various sources
      getClinicDirectly()
        .then(clinicId => {
          console.log('Successfully determined clinic ID from API:', clinicId);
          fetchWithClinicId(clinicId);
        })
        .catch(error => {
          console.log('Could not get clinic ID from API, trying fallback methods');
          
          // Try to collect from various sources
          let clinicId = collectClinicIds();
          
          // If still no valid clinic ID, default to 1
          if (!clinicId || !parseInt(clinicId) || clinicId === 'auto') {
            clinicId = 1;
            console.log('Using default clinic ID (1) as final fallback');
          }
          
          fetchWithClinicId(clinicId);
        });
    },
    defaultDashboardData: function () {
      return {
        appointment_count: 0,
        doctor_count: 0,
        patient_count: 0,
        revenue: 0,
        change_log: true,
        telemed_log: false
      }
    },
    getDashboardData: function () {
      get('get_dashboard', {})
          .then((response) => {
            this.isdataLoading = false;
            if (response.data.status !== undefined && response.data.status === true) {
              this.dashboardData = response.data.data
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          })
    },
    defaultAppointmentRequest: function () {
      return {
        date: new Date()
      }
    },
    mounted() {
        this.$router.push({ name: 'appointment-list.index' });
        this.init();
    },
    appointmentReload: function () {
      this.isLoading = false
    }
  }
}
</script>
