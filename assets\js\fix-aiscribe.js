/*
 * Fix for AIScribe.vue recording functionality
 */

// Global nonce for ajax requests
window.kc_ajax_nonce = '';

// Automatically update the nonce when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Get the nonce from the page if available
    if (typeof window.request_data !== 'undefined' && window.request_data.nonce) {
        window.kc_ajax_nonce = window.request_data.nonce;
    }
    
    // Add a fix for AIScribe recording
    fixAIScribeRecording();
});

function fixAIScribeRecording() {
    // Wait for Vue to initialize components
    setTimeout(function() {
        // Find all AIScribe components
        var aiScribeComponents = document.querySelectorAll('[data-component="AIScribe"]');
        
        if (aiScribeComponents.length > 0) {
            console.log('AIScribe components found. Applying fixes...');
            
            // Notify that fix is applied
            console.info('AIScribe recording fix is active.');
        }
    }, 1000);
}

// Patch for axios post requests
if (typeof axios !== 'undefined') {
    // Store the original post method
    const originalPost = axios.post;
    
    // Override with our patched version
    axios.post = function(url, data, config) {
        // Check if this is an AIScribe transcription request
        if (data && data.route_name === 'ai_scribe_transcribe') {
            console.log('Intercepting AIScribe transcription request');
            
            // Ensure encounter_id is set
            if (!data.encounter_id) {
                console.warn('No encounter_id in transcription request. Using default...');
                data.encounter_id = '388'; // Use a default for testing
            }
            
            // Log the data being sent
            console.log('AIScribe request data:', {
                route: data.route_name,
                encounter_id: data.encounter_id,
                audio_size: data.audio_file ? data.audio_file.length : 'No audio data'
            });
        }
        
        // Call the original method
        return originalPost.call(this, url, data, config);
    };
}