<?php
/*
* Add this temporary plugin to check activity logs
* Plugin Name: KiviCare Activity Logs Check
* Description: Temporary plugin to check activity logs
* Version: 1.0
*/

// Add admin menu
add_action('admin_menu', 'kc_activity_logs_check_menu');

function kc_activity_logs_check_menu() {
    add_menu_page(
        'Check Activity Logs', 
        'Check Logs', 
        'manage_options', 
        'kc-check-logs', 
        'kc_activity_logs_check_page',
        'dashicons-list-view',
        99
    );
}

// Admin page content
function kc_activity_logs_check_page() {
    global $wpdb;
    
    echo '<div class="wrap">';
    echo '<h1>Activity Logs Check</h1>';
    
    $table_name = $wpdb->prefix . 'kc_activity_logs';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    echo '<p><strong>Table Status:</strong> ' . ($table_exists ? 'Exists' : 'Does not exist') . '</p>';
    
    if (!$table_exists) {
        echo '<p>Attempting to create table...</p>';
        require_once(plugin_dir_path(__FILE__) . 'app/database/kc-activity-log-db.php');
        kivicareCreateActivityLogTable();
        
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        echo '<p><strong>Table Status after creation attempt:</strong> ' . ($table_exists ? 'Successfully created' : 'Creation failed') . '</p>';
    }
    
    // Create a test log using direct DB insert
    if (isset($_POST['create_test_log'])) {
        $user_id = get_current_user_id();
        $user_data = get_userdata($user_id);
        $user_role = reset($user_data->roles);
        
        $result = $wpdb->insert(
            $table_name,
            [
                'user_id' => $user_id,
                'user_type' => $user_role,
                'activity_type' => 'test_activity',
                'activity_description' => 'Test activity log entry (direct DB insert)',
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'created_at' => current_time('mysql')
            ]
        );
        
        echo '<p><strong>Test log creation (direct DB):</strong> ' . ($result ? 'Success' : 'Failed - ' . $wpdb->last_error) . '</p>';
    }
    
    // Create a test log using kcLogActivity helper
    if (isset($_POST['create_test_log_helper'])) {
        $user_id = get_current_user_id();
        
        // Include the helper file
        require_once(plugin_dir_path(__FILE__) . 'utils/kc_helpers.php');
        
        // Test if function exists
        if (function_exists('kcLogActivity')) {
            $result = kcLogActivity(
                $user_id,
                'test_activity',
                'Test activity log entry using kcLogActivity helper',
                [
                    'clinic_id' => 1,
                    'resource_type' => 'test'
                ]
            );
            
            echo '<p><strong>Test log creation (kcLogActivity):</strong> ' . ($result ? 'Success' : 'Failed') . '</p>';
        } else {
            echo '<p><strong>Error:</strong> kcLogActivity function does not exist!</p>';
            
            // Try to find the function
            $helper_file = file_get_contents(plugin_dir_path(__FILE__) . 'utils/kc_helpers.php');
            if (strpos($helper_file, 'function kcLogActivity') !== false) {
                echo '<p>Function definition found in file but not loadable!</p>';
            } else {
                echo '<p>Function definition not found in helpers file!</p>';
            }
        }
    }
    
    // Check activity logs API endpoint
    if (isset($_POST['test_api_endpoint'])) {
        // Print route definition
        echo '<h3>Route Definition Check</h3>';
        $routes_file = plugin_dir_path(__FILE__) . 'app/baseClasses/KCRoutes.php';
        if (file_exists($routes_file)) {
            $routes_content = file_get_contents($routes_file);
            if (preg_match('/\'activity_logs\'.*?=>.*?\[.*?\'method\'.*?=>.*?\'(.*?)\'/', $routes_content, $matches)) {
                echo '<p><strong>Route Method:</strong> ' . $matches[1] . '</p>';
                echo '<p>The ActivityLogs route uses the ' . strtoupper($matches[1]) . ' method, but we\'re attempting to use POST.</p>';
            } else {
                echo '<p>Could not find route definition in KCRoutes.php</p>';
            }
        } else {
            echo '<p>Routes file not found</p>';
        }
        
        $api_url = admin_url('admin-ajax.php');
        
        // Try with page=1 and proper parameters
        $body = [
            'action' => 'ajax_post',
            'route_name' => 'activity_logs',
            'page' => 1,
            'per_page' => 10
        ];
        
        // If there are filters, add them as JSON
        if (!empty($_POST['use_filters'])) {
            $body['filters'] = wp_json_encode([
                'activity_type' => 'test_activity'
            ]);
        }
        
        // Use GET or POST based on the checkbox
        if (!empty($_POST['use_get'])) {
            echo '<p><strong>Using GET method</strong></p>';
            $api_url = add_query_arg($body, $api_url);
            $response = wp_remote_get($api_url);
        } else {
            echo '<p><strong>Using POST method</strong></p>';
            $response = wp_remote_post($api_url, [
                'body' => $body
            ]);
        }
        
        echo '<h3>API Endpoint Test</h3>';
        if (is_wp_error($response)) {
            echo '<p><strong>Error:</strong> ' . $response->get_error_message() . '</p>';
        } else {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body);
            
            echo '<p><strong>Status Code:</strong> ' . wp_remote_retrieve_response_code($response) . '</p>';
            echo '<p><strong>Response:</strong></p>';
            echo '<pre>' . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . '</pre>';
        }
    }
    
    // Display logs if table exists
    if ($table_exists) {
        $rows = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY id DESC LIMIT 20");
        
        echo '<form method="post">';
        echo '<p>';
        echo '<button type="submit" name="create_test_log" class="button button-primary">Create Test Log Entry (Direct DB)</button> ';
        echo '<button type="submit" name="create_test_log_helper" class="button button-secondary">Create Test Log Entry (Helper)</button> ';
        echo '<button type="submit" name="test_api_endpoint" class="button">Test API Endpoint</button>';
        echo ' <label><input type="checkbox" name="use_filters" value="1"> Use Filters</label>';
        echo ' <label><input type="checkbox" name="use_get" value="1"> Use GET method</label>';
        echo '</p>';
        echo '</form>';
        
        echo '<h2>Recent Activity Logs (' . count($rows) . ')</h2>';
        
        if (empty($rows)) {
            echo '<p>No activity logs found in the database.</p>';
        } else {
            echo '<table class="widefat striped">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>ID</th>';
            echo '<th>User ID</th>';
            echo '<th>User Type</th>';
            echo '<th>Activity Type</th>';
            echo '<th>Description</th>';
            echo '<th>Date/Time</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($rows as $row) {
                echo '<tr>';
                echo '<td>' . esc_html($row->id) . '</td>';
                echo '<td>' . esc_html($row->user_id) . '</td>';
                echo '<td>' . esc_html($row->user_type) . '</td>';
                echo '<td>' . esc_html($row->activity_type) . '</td>';
                echo '<td>' . esc_html($row->activity_description) . '</td>';
                echo '<td>' . esc_html($row->created_at) . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
        }
    }
    
    echo '</div>';
}