<template>
  <div>
    <!-- Admin Warning Alert -->
    <div
      v-if="getUserRole() === 'administrator' && clinicSessionNotice.status"
      class="mb-4 bg-yellow-50 border-l-4 border-yellow-400 p-4"
    >
      <div class="flex items-center justify-between">
        <p class="text-yellow-700 font-medium">{{ clinicSessionNotice.msg }}</p>
        <router-link
          v-if="kcCheckPermission('doctor_session_list')"
          :to="{ name: 'doctor-session.create' }"
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          {{ formTranslation.common.add_session }}
        </router-link>
      </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white rounded-xl shadow-sm">
      <!-- Header Section -->
      <div class="px-6 py-4 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">
              {{ formTranslation.appointments.appointments }}
            </h1>
            <p class="text-sm text-gray-500 mt-1">
              Manage and track your appointments
            </p>
          </div>
          <div class="flex items-center gap-3">
            <!-- Action Buttons -->
            <module-data-export
              v-if="kcCheckPermission('appointment_export')"
              :module_data="csvAppointmentData"
              :module_name="formTranslation.appointments.appointments"
              module_type="appointment"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" x2="12" y1="15" y2="3"></line>
              </svg>
              Export
            </module-data-export>

            <button
              v-if="
                addAppointmentButton && kcCheckPermission('appointment_add')
              "
              @click="handleAppointmentForm({})"
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium bg-black text-white px-4 py-2 hover:bg-gray-800"
              v-html="appBtnText"
            ></button>
          </div>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="border-b border-gray-100 bg-gray-50/50">
        <div class="px-6 py-3 flex items-center justify-between">
          <div class="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-4 h-4 text-gray-500"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <polygon
                points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"
              ></polygon>
            </svg>
            <h2 class="text-sm font-medium">
              {{ formTranslation.common.filters }}
            </h2>
          </div>
          <button
            @click="filterOpenClose"
            class="inline-flex items-center justify-center rounded-md px-3 text-xs h-8 hover:bg-accent"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-4 h-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="m18 15-6-6-6 6"></path>
            </svg>
          </button>
        </div>

        <!-- Filter Fields -->
        <div v-if="filterOpen" class="px-6 pb-4 grid grid-cols-5 gap-3">
          <div v-if="showClinicFilter">
            <input
              type="date"
              v-model="filterData.date"
              class="w-full h-9 px-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>

          <div
            v-for="(filter, index) in activeFilters"
            :key="index"
            class="relative"
          >
            <select
              v-model="filterData[filter.key]"
              @change="handleFilterChange(filter.type)"
              class="w-full h-9 px-3 bg-white border border-gray-200 rounded-lg appearance-none focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">{{ filter.placeholder }}</option>
              <option
                v-for="option in filter.options"
                :key="option.id"
                :value="option.id"
              >
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Appointments List -->
      <div class="overflow-x-auto">
        <AppointmentCard
          :appointment-data="appointmentList"
          :appointmentDate="filterData.date"
          :filter_status="filterData.status"
          :enable_delete_multiple="enableMultiDelete"
          :delete_multiple_appointment="deleteMultipleAppointment"
          @enabledDeleteBtn="deleteBtnEnabled"
          @reloadAppointment="handleFilterChange"
          @closeFilterForm="closeFilterForm"
          @updateAppointmentList="updateAppointmentList"
          :isLoading="isLoading"
          :patient_profile_id="patient_profile_id"
          ref="appointmentCardRef"
        />
      </div>

      <!-- Pagination -->
      <div
        class="px-6 py-4 border-t border-gray-100 flex items-center justify-between"
      >
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600">Rows per page:</span>
          <select
            v-model="perPage"
            class="border border-gray-200 rounded px-2 py-1 text-sm"
          >
            <option v-for="n in [10, 20, 50]" :key="n" :value="n">
              {{ n }}
            </option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-600">
            Page {{ currentPage }} of
            {{ Math.ceil(appointmentTotalRows / perPage) }}
          </span>
          <div class="flex gap-1">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="inline-flex items-center justify-center rounded-md border border-gray-200 w-8 h-8 hover:bg-gray-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </button>
            <button
              @click="currentPage++"
              :disabled="
                currentPage >= Math.ceil(appointmentTotalRows / perPage)
              "
              class="inline-flex items-center justify-center rounded-md border border-gray-200 w-8 h-8 hover:bg-gray-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      v-if="isAddAppointmentModalOpen"
      class="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
      @click="isAddAppointmentModalOpen = false"
    >
    </div>

    <!-- Modal Container -->
    <div
      v-if="isAddAppointmentModalOpen"
      class="fixed inset-0 z-50 overflow-y-auto"
    >
      <div class="min-h-screen px-4 py-6 flex items-center justify-center">
        <div class="w-full max-w-3xl bg-white rounded-xl shadow-xl">
          <!-- Modal Header (Fixed) -->
          <div
            class="sticky top-0 z-10 flex items-center justify-between p-6 border-b border-gray-100 bg-white rounded-t-xl"
          >
            <h3 class="text-xl font-semibold text-gray-900">
              {{ formTranslation.appointments.add_appointment_btn }}
            </h3>
            <button
              @click="isAddAppointmentModalOpen = false"
              class="text-gray-400 hover:text-gray-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                class="w-6 h-6"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <!-- Modal Body (Scrollable) -->
          <div class="p-6">
            <AppointmentForm
              v-if="appointmentFormObj.id === undefined"
              :patient_profile_id="patient_profile_id"
              :appointmentData="appointmentFormObj"
              @appointmentSaved="handleAppointmentSave"
              @closeAppointmentForm="isAddAppointmentModalOpen = false"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AppointmentCard from "../../components/appointment/AppointmentCard";
import AppointmentForm from "../../components/appointment/AppointmentForm";
import { get } from "../../config/request";
import moment from "moment";

export default {
  components: {
    AppointmentCard,
    AppointmentForm,
  },

  props: {
    patient_profile_id: {
      type: [Number, String],
      default: "",
    },
  },

  data() {
    return {
      isAddAppointmentModalOpen: false,
      filterOpen: false,
      enableMultiDelete: false,
      deleteMultipleAppointment: false,
      isLoading: false,
      filterData: this.getDefaultFilterData(),
      perPage: 10,
      currentPage: 1,
      appointmentFormObj: {},
      appBtnText: '<i class="fa fa-plus"></i> Add Appointment',
      addAppointmentButton: true,
      clinicSessionNotice: {
        status: false,
        msg: "",
      },
      doctors: [],
      patients: [],
      csvAppointmentData: [],
      allStatus: [
        { label: "all", value: "all" },
        { label: "upcoming", value: "1" },
        { label: "completed", value: "3" },
        { label: "cancelled", value: "0" },
        { label: "checkin", value: "4" },
        { label: "pending", value: "2" },
      ],
    };
  },

  computed: {
    appointmentList() {
      return this.$store.state.appointmentModule.appointmentList || [];
    },

    appointmentTotalRows() {
      return this.$store.state.appointmentModule.totalrows || 0;
    },

    showClinicFilter() {
      return (
        this.userData?.addOns?.kiviPro &&
        ["administrator", "doctor"].includes(this.getUserRole())
      );
    },

    showPatientFilter() {
      return (
        this.getUserRole() !== "patient" &&
        !this.$route.params.patient_id &&
        !this.patient_profile_id
      );
    },

    showDoctorFilter() {
      return this.getUserRole() !== "doctor";
    },

    activeFilters() {
      const filters = [];

      if (this.showClinicFilter) {
        filters.push({
          key: "clinic_id",
          type: "clinic",
          options: this.clinics,
          placeholder:
            this.formTranslation?.appointments?.select_clinic ||
            "Select Clinic",
        });
      }

      if (this.showPatientFilter) {
        filters.push({
          key: "patient_id",
          type: "patient",
          options: this.patients,
          placeholder:
            this.formTranslation?.appointments?.select_patient ||
            "Select Patient",
        });
      }

      if (this.showDoctorFilter) {
        filters.push({
          key: "doctor_id",
          type: "doctor",
          options: this.doctors,
          placeholder:
            this.formTranslation?.patient_encounter?.tag_select_doctor ||
            "Select Doctor",
        });
      }

      return filters;
    },

    userData() {
      return this.$store.state.userDataModule?.user || {};
    },
  },

  methods: {
    getDefaultFilterData() {
      const patient_id =
        this.$route.params.patient_id || this.patient_profile_id || "";
      return {
        date: new Date(),
        patient_id: patient_id,
        status: "1",
        clinic_id: "",
        doctor_id: "",
      };
    },

    async init() {
      try {
        await this.checkIfClinicHaveSession();
        await this.loadAppointments();

        if (this.getUserRole() !== "patient") {
          await this.getClinicPatients("");
        }

        if (this.getUserRole() !== "doctor") {
          await this.doctorListDropDown("");
        }
      } catch (error) {
        console.error("Initialization error:", error);
      }
    },

    async handleFilterChange(type) {
      this.isLoading = true;

      try {
        if (type === "clinic") {
          await Promise.all([
            this.getClinicPatients(this.filterData.clinic_id),
            this.doctorListDropDown(this.filterData.clinic_id),
          ]);
        }

        await this.loadAppointments();
      } catch (error) {
        console.error("Filter change error:", error);
      } finally {
        this.isLoading = false;
      }
    },

    async loadAppointments() {
      const filterData = {
        ...this.filterData,
        pagination: this.currentPage,
        date: this.filterData.date
          ? moment(this.filterData.date).format("YYYY-MM-DD")
          : moment().format("YYYY-MM-DD"),
      };

      await this.$store.dispatch("appointmentModule/fetchAppointmentData", {
        filterData,
      });
    },

    handleAppointmentForm(appointment = {}) {
      this.appointmentFormObj = appointment;
      this.isAddAppointmentModalOpen = true;
    },

    handleAppointmentSave() {
      this.isAddAppointmentModalOpen = false;
      this.loadAppointments();
    },

    async checkIfClinicHaveSession() {
      try {
        const response = await get("clinic-sessions/check");
        this.clinicSessionNotice = {
          status: response.data?.status || false,
          msg: response.data?.message || "",
        };
      } catch (error) {
        console.error("Error checking clinic session:", error);
      }
    },

    filterOpenClose() {
      this.filterOpen = !this.filterOpen;
    },

    closeFilterForm() {
      this.filterOpen = false;
    },

    deleteBtnEnabled(enabled) {
      this.enableMultiBtnDelete = enabled;
    },

    updateAppointmentList(data) {
      this.csvAppointmentData = data;
    },

    getUserRole() {
      return this.userData?.role || "";
    },

    async getClinicPatients(clinic_id) {
      try {
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: "patient",
          request_clinic_id: clinic_id,
        });

        if (response.data?.status) {
          this.patients = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinic patients:", error);
      }
    },

    async doctorListDropDown(clinic_id) {
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          clinic_id: clinic_id,
          module_type: "appointment_filter",
        });

        if (response.data?.status) {
          this.doctors = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching doctors:", error);
      }
    },

    handleClickOutside(event) {
      // Get all dropdown buttons and menus
      const dropdowns = document.querySelectorAll(".dropdown-menu");
      const buttons = document.querySelectorAll(".dropdown-button");

      // If click is outside dropdown and buttons, close all dropdowns
      if (
        !event.target.closest(".dropdown-menu") &&
        !event.target.closest(".dropdown-button")
      ) {
        this.closeAllDropdowns();
      }
    },

    closeAllDropdowns() {
      // Add your logic to close dropdowns here
      // For example:
      this.openDropdownId = null; // if you're tracking open dropdown with an ID
      // or
      this.isDropdownOpen = false; // if you're using a boolean
    },
  },

  mounted() {
    this.init();
  },

  beforeDestroy() {
    // Clean up listener
    document.removeEventListener("click", this.handleClickOutside);
  },

  watch: {
    currentPage() {
      this.loadAppointments();
    },

    "filterData.date"() {
      this.loadAppointments();
    },
  },
};
</script>
<style>
.kivicare-pagination .page-item.active button {
  border-color: var(--primary) !important;
  background-color: var(--primary) !important;
}
.kivicare-pagination .page-link {
  border-color: var(--primary) !important;
}
.font-size-10 {
  font-size: 10px !important;
}
.kc-row-gap {
  row-gap: 1em;
}
</style>
