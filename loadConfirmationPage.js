// async loadConfirmationPage() {
//   try {
//     // Use the ajaxurl and nonce for the WordPress AJAX API
//     const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
//     let nonce = '';

//     // Try to get nonce from different possible sources
//     if (window.ajaxData && window.ajaxData.post_nonce) {
//       nonce = window.ajaxData.post_nonce;
//     } else if (window.ajaxData && window.ajaxData.nonce) {
//       nonce = window.ajaxData.nonce;
//     } else if (window.request_data && window.request_data.nonce) {
//       nonce = window.request_data.nonce;
//     }

//     // Get the selected clinic, doctor, service, time and date from the booking data
//     const clinicId = this.bookingData.clinic?.id || '';
//     const doctorId = this.bookingData.doctor?.id || '';
//     const serviceList = this.bookingData.service?.id ? [this.bookingData.service.service_id] : [];
//     let time = this.bookingData.time || '';
//     const date = this.bookingData.date || '';
//     const description = this.bookingData.description || '';
//     const customField = this.bookingData.customField || {};

//     // Format time to AM/PM format if needed
//     if (time && !time.toLowerCase().includes('am') && !time.toLowerCase().includes('pm')) {
//       const timeParts = time.split(':');
//       const hours = parseInt(timeParts[0]);
//       const minutes = timeParts[1] || '00';
//       const ampm = hours >= 12 ? 'pm' : 'am';
//       const formattedHours = hours % 12 || 12;
//       time = `${formattedHours}:${minutes} ${ampm}`;
//     }

//     // Prepare the data for the API call
//     const params = {
//       action: 'ajax_post',
//       route_name: 'appointment_confirm_page',
//       clinic_id: clinicId,
//       doctor_id: doctorId,
//       service_list: serviceList,
//       time: time,
//       date: date,
//       description: description,
//       file: [],
//       custom_field: customField,
//       _ajax_nonce: nonce
//     };

//     // Make the API request
//     const response = await fetch(ajaxurl, {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/x-www-form-urlencoded',
//       },
//       body: new URLSearchParams(params)
//     });

//     const responseData = await response.json();

//     if (responseData.status) {
//       // Store the confirmation page HTML in the parent component
//       this.$emit('confirmation-page-loaded', {
//         html: responseData.data,
//         taxDetails: responseData.tax_details || []
//       });
//     }
//   } catch (error) {
//     console.error('Error loading confirmation page:', error);
//   }
// }
