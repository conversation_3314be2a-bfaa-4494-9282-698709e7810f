<!-- Template Section -->
<template>
  <div>
    <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
      <!-- Loading Overlay -->
      <div
        v-if="loading"
        class="fixed inset-0 bg-white/60 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <div
          class="animate-spin rounded-full h-12 w-12 border-4 border-purple-500 border-t-transparent"
        ></div>
      </div>

      <!-- Error <PERSON>ert -->
      <div
        v-if="error"
        class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg"
      >
        {{ error }}
        <button @click="error = null" class="float-right">&times;</button>
      </div>

      <!-- Header Section -->
      <div
        class="mb-8 flex flex-col md:flex-row md:items-center md:justify-between gap-4"
      >
        <div class="flex items-center gap-4">
          <button
            @click="$router.go(-1)"
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg shadow-sm hover:bg-gray-800 transition"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-4 h-4"
            >
              <path d="m12 19-7-7 7-7"></path>
              <path d="M19 12H5"></path>
            </svg>
            <span>{{ formTranslation?.common?.back || "Back" }}</span>
          </button>
          <h1 class="text-2xl font-semibold text-gray-800">
            {{ encounterName }}
          </h1>
        </div>

        <div class="flex gap-2">
          <!-- Add Consultation Button -->
          <button
            v-if="canAddEncounter"
            @click="handleEncounterForm"
            :disabled="loading"
            class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <span v-if="!visible" class="text-lg">+</span>
            <span v-else class="text-lg">-</span>
            <span>{{
              visible
                ? formTranslation.encounter_dashboard.close_form
                : formTranslation.encounter_dashboard.add_encounter
            }}</span>
          </button>

          <!-- Add Template Button -->
          <button
            v-if="canAddTemplate"
            @click="handleEncounterTemplateForm"
            :disabled="loading"
            class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <span class="text-lg">+</span>
            <span>{{ formTranslation.common.add_encounter_template }}</span>
          </button>
        </div>
      </div>

      <!-- Create/Edit Form Section -->
      <div
        v-if="showForm"
        class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
      >
        <ModalPopup @close="closeEncounterForm">
          <div class="p-6">
            <h2 class="text-xl font-semibold mb-4">
              {{
                isEditing
                  ? formTranslation?.common?.edit || "Edit Consultation"
                  : formTranslation?.common?.add || "Add New Consultation"
              }}
            </h2>
            <Create
              :encounter-id="encounterId"
              :encounter-data="selectedEncounter"
              :is-edit="isEditing"
              :patient-field="patientField"
              :clinic-field="clinicField"
              @getPatientEncountersData="fetchEncounters"
              @closeEncounterForm="closeEncounterForm"
            />
          </div>
        </ModalPopup>
      </div>

      <!-- Filters Section -->
      <div
        class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto p-4"
      >
        <div class="mb-4">
          <div class="relative">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input
              @input="handleGlobalSearch"
              :placeholder="
                formTranslation.common
                  .search_encounter_field_data_global_placeholder
              "
              class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              type="text"
              v-model="serverParams.searchTerm"
            />
          </div>
        </div>

        <!-- Advanced Filters Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <!-- Individual Filters -->
          <div
            v-for="(column, index) in tableColumns"
            :key="index"
            v-show="!column.hidden && column.field !== 'actions'"
          >
            <!-- Date Filter -->
            <div v-if="column.field === 'encounter_date'" class="relative">
              <vc-date-picker
                v-model="serverParams.columnFilters.encounter_date"
                mode="range"
                :popover="{ visibility: 'click' }"
                @input="updateColumnFilter('encounter_date', $event)"
                class="w-full"
              >
                <template v-slot="{ inputValue, inputEvents }">
                  <input
                    :value="inputValue"
                    v-on="inputEvents"
                    :placeholder="formTranslation.appointments.plh_date"
                    class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  />
                </template>
              </vc-date-picker>
              <button
                v-if="serverParams.columnFilters.encounter_date"
                @click="clearDateFilter"
                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                &times;
              </button>
            </div>

            <!-- Status Filter -->
            <select
              v-else-if="column.field === 'status'"
              v-model="serverParams.columnFilters.status"
              @change="updateColumnFilter('status', $event.target.value)"
              class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
            >
              <option value="">
                {{ formTranslation.static_data.dt_lbl_plh_sr_fltr_status }}
              </option>
              <option value="1">{{ formTranslation.common.active }}</option>
              <option value="0">{{ formTranslation.common.closed }}</option>
            </select>

            <!-- Text Filters -->
            <input
              v-else
              :placeholder="
                column.filterOptions?.placeholder || `Filter by ${column.label}`
              "
              class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              type="text"
              v-model="serverParams.columnFilters[column.field]"
              @input="updateColumnFilter(column.field, $event.target.value)"
            />
          </div>
        </div>
      </div>

      <!-- Table Section -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
      >
        <!-- Selected Rows Actions -->
        <div
          v-if="selectedRows.length"
          class="bg-purple-50 px-4 py-2 flex items-center justify-between border-b border-gray-200"
        >
          <span class="text-sm text-purple-700">
            {{ selectedRows.length }} {{ formTranslation.common.rows_selected }}
          </span>
          <div class="flex items-center gap-2">
            <select
              v-model="bulkAction"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
            >
              <option value="">Select Action</option>
              <option value="delete">
                {{ formTranslation.clinic_schedule.dt_lbl_dlt }}
              </option>
            </select>
            <button
              @click="handleBulkAction"
              :disabled="!bulkAction || loading"
              class="px-3 py-1 bg-purple-600 text-white rounded-md text-sm hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ formTranslation.common.apply }}
            </button>
          </div>
        </div>

        <!-- Table -->
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                <input
                  type="checkbox"
                  @change="toggleSelectAll"
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
              </th>
              <th
                v-for="column in tableColumns"
                :key="column.field"
                v-show="!column.hidden"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                @click="column.sortable !== false && handleSort(column.field)"
              >
                <div class="flex items-center gap-2">
                  {{ column.label }}
                  <div v-if="column.sortable !== false" class="flex flex-col">
                    <svg
                      :class="[
                        'w-3 h-3 transition-colors',
                        serverParams.sort.field === column.field &&
                        serverParams.sort.type === 'asc'
                          ? 'text-purple-600'
                          : 'text-gray-400',
                      ]"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 4l-8 8h16z" fill="currentColor" />
                    </svg>
                    <svg
                      :class="[
                        'w-3 h-3 transition-colors',
                        serverParams.sort.field === column.field &&
                        serverParams.sort.type === 'desc'
                          ? 'text-purple-600'
                          : 'text-gray-400',
                      ]"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 20l-8-8h16z" fill="currentColor" />
                    </svg>
                  </div>
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="row in patientEncounterList.data"
              :key="row.id"
              class="hover:bg-gray-50"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  v-model="selectedRows"
                  :value="row.id"
                  class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
              </td>
              <td
                v-for="column in tableColumns"
                :key="column.field"
                v-show="!column.hidden"
                class="px-6 py-4 whitespace-nowrap"
              >
                <!-- Status Column -->
                <template v-if="column.field === 'status'">
                  <span
                    :class="[
                      'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                      row.status === '1'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800',
                    ]"
                  >
                    {{
                      row.status === "1"
                        ? formTranslation.common.active
                        : formTranslation.common.closed
                    }}
                  </span>
                </template>

                <!-- Actions Column -->
                <template v-else-if="column.field === 'actions'">
                  <div class="flex gap-2">
                    <!-- Edit Button -->
                    <button
                      v-if="canEditEncounter(row)"
                      @click="editEncounterData(row)"
                      class="p-1.5 hover:bg-gray-100 rounded-lg transition tooltip"
                      :data-tooltip="formTranslation.common.edit"
                    >
                      <svg class="w-4 h-4 text-gray-600" viewBox="0 0 24 24">
                        <path
                          d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"
                          stroke="currentColor"
                          fill="none"
                          stroke-width="2"
                        />
                      </svg>
                    </button>

                    <!-- Dashboard Link -->
                    <router-link
                      v-if="canViewDashboard(row)"
                      :to="getDashboardRoute(row)"
                      class="p-1.5 hover:bg-gray-100 rounded-lg transition tooltip"
                      :data-tooltip="
                        formTranslation.patient_encounter.encounter_dashboard
                      "
                    >
                      <svg class="w-4 h-4 text-gray-600" viewBox="0 0 24 24">
                        <path
                          d="M3 3h18v18H3V3zm4 4h4v4H7V7zm0 6h4v4H7v-4zm6-6h4v4h-4V7zm6 0h4v4h-4V7z"
                          stroke="currentColor"
                          fill="none"
                          stroke-width="2"
                        />
                      </svg>
                    </router-link>

                    <!-- Body Chart Link -->
                    <router-link
                      v-if="canViewBodyChart(row)"
                      :to="getBodyChartRoute(row)"
                      class="p-1.5 hover:bg-gray-100 rounded-lg transition tooltip"
                      :data-tooltip="formTranslation.common.body_chart"
                    >
                      <svg class="w-4 h-4 text-gray-600" viewBox="0 0 24 24">
                        <path
                          d="M13 2a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2m-6 4v4a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4"
                          stroke="currentColor"
                          fill="none"
                          stroke-width="2"
                        />
                      </svg>
                    </router-link>

                    <!-- Bill Details Button -->
                    <button
                      v-if="canViewBillDetails(row)"
                      @click="openBillDetails(row)"
                      class="p-1.5 hover:bg-gray-100 rounded-lg transition tooltip"
                      :data-tooltip="formTranslation.patient_bill.bill_details"
                    >
                      <svg class="w-4 h-4 text-gray-600" viewBox="0 0 24 24">
                        <path
                          d="M4 4h16M4 8h16M4 12h16m-8 4h8m-8 4h8"
                          stroke="currentColor"
                          fill="none"
                          stroke-width="2"
                        />
                      </svg>
                    </button>

                    <!-- Custom Forms -->
                    <button
                      v-for="(form, index) in row.custom_forms"
                      :key="index"
                      v-if="canViewCustomForm(row, form)"
                      @click="openCustomForm(row, form)"
                      class="p-1.5 hover:bg-gray-100 rounded-lg transition tooltip"
                      :data-tooltip="form.name?.text || ''"
                    >
                      <i
                        :class="[
                          form.name?.icon || 'fas fa-book-medical',
                          'w-4 h-4 text-gray-600',
                        ]"
                      ></i>
                    </button>

                    <!-- Delete Button -->
                    <button
                      v-if="canDeleteEncounter(row)"
                      @click="confirmDelete(row.id)"
                      class="p-1.5 hover:bg-gray-100 rounded-lg transition tooltip"
                      :data-tooltip="formTranslation.clinic_schedule.dt_lbl_dlt"
                    >
                      <svg class="w-4 h-4 text-red-600" viewBox="0 0 24 24">
                        <path
                          d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                          stroke="currentColor"
                          fill="none"
                          stroke-width="2"
                        />
                      </svg>
                    </button>
                  </div>
                </template>

                <!-- Default Column Display -->
                <template v-else>
                  {{ formatColumnValue(row[column.field], column) }}
                </template>
              </td>
            </tr>

            <!-- Empty State -->
            <tr v-if="!loading && patientEncounterList.data.length === 0">
              <td
                :colspan="tableColumns.length + 1"
                class="px-6 py-8 text-center text-gray-500"
              >
                {{ formTranslation.common.no_data_found }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div
          class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
        >
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-700"
              >{{ formTranslation.common.rows_per_page }}:</span
            >
            <select
              v-model="serverParams.perPage"
              @change="handlePerPageChange"
              class="border border-gray-300 rounded-md text-sm p-1 focus:outline-none focus:ring-2 focus:ring-purple-400"
            >
              <option v-for="size in pageSizes" :key="size" :value="size">
                {{ size }}
              </option>
            </select>
          </div>

          <div class="flex items-center gap-4">
            <span class="text-sm text-gray-700">
              {{ paginationText }}
            </span>
            <div class="flex gap-2">
              <button
                @click="previousPage"
                :disabled="!canGoPrevious"
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition"
              >
                <svg class="w-5 h-5 text-gray-600" viewBox="0 0 24 24">
                  <path
                    d="m15 18-6-6 6-6"
                    stroke="currentColor"
                    fill="none"
                    stroke-width="2"
                  />
                </svg>
              </button>
              <button
                @click="nextPage"
                :disabled="!canGoNext"
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition"
              >
                <svg class="w-5 h-5 text-gray-600" viewBox="0 0 24 24">
                  <path
                    d="m9 18 6-6-6-6"
                    stroke="currentColor"
                    fill="none"
                    stroke-width="2"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <!-- Create/Edit Modal -->
    <!-- <ModalPopup v-if="showForm" @close="closeForm">
      <Create
        :encounter-data="selectedEncounter"
        :is-edit="isEditing"
        @saved="handleSaved"
        @cancelled="closeForm"
      />
    </ModalPopup> -->

    <!-- Template Form Modal -->
    <ModalPopup v-if="showTemplateForm" @close="closeTemplateForm">
      <div class="p-6">
        <h2 class="text-xl font-semibold mb-4">
          {{ formTranslation.common.add_encounter_template }}
        </h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              {{ formTranslation.encounter_dashboard.template_name }}
              <span class="text-red-500">*</span>
            </label>
            <input
              v-model="templateForm.name"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              :class="{ 'border-red-500': templateErrors.name }"
              :placeholder="
                formTranslation.encounter_dashboard.template_name_placeholder
              "
              type="text"
              required
            />
            <p v-if="templateErrors.name" class="mt-1 text-sm text-red-600">
              {{ templateErrors.name }}
            </p>
          </div>

          <div class="flex justify-end gap-3">
            <button
              @click="closeTemplateForm"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition"
            >
              {{ formTranslation.common.cancel }}
            </button>
            <button
              @click="saveTemplate"
              :disabled="!templateForm.name || loading"
              class="px-4 py-2 bg-purple-600 text-white rounded-md text-sm font-medium hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition"
            >
              {{ formTranslation.encounter_dashboard.add_btn }}
            </button>
          </div>
        </div>
      </div>
    </ModalPopup>

    <!-- Bill Details Modal -->
    <ModalPopup v-if="billDetailsModel" @close="closeBillDetails">
      <BillDetails
        :encounter-id="selectedEncounterId"
        :clinic-extra="clinic_extra"
        @onBillCancel="closeBillDetails"
      />
    </ModalPopup>

    <!-- Custom Form Modal -->
    <ModalPopup v-if="showCustomForm" @close="closeCustomForm">
      <CustomForm
        :data="customFormData"
        :view-mode="customFormViewMode"
        @closeModal="closeCustomForm"
      />
    </ModalPopup>
  </div>
</template>

<style>
.tooltip {
  position: relative;
}

.tooltip:before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.25rem 0.5rem;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.tooltip:hover:before {
  opacity: 1;
  visibility: visible;
}

@media (max-width: 576px) {
  .vgt-compact td:before {
    padding-left: 0;
  }
}
</style>

<script>
import { get, post } from "../../config/request";
import Create from "./Create";
import BillDetails from "../../components/PatientBill/BillDetails";
import ModalPopup from "../../components/Modal/Index";
import CustomForm from "../CustomForm/Form.vue";
import { debounce } from "lodash";

export default {
  name: "PatientEncounters",

  components: {
    Create,
    BillDetails,
    ModalPopup,
    CustomForm,
  },

  data() {
    return {
      // Core Data States
      patientEncounterList: {
        data: [],
        totalRows: 0,
      },
      loading: false,
      error: null,

      // UI States
      visible: false,
      // showAddForm: false,
      // showEditForm: false,
      showForm: false, // Added this
      showTemplateForm: false,
      showCustomForm: false,
      billDetailsModel: false,
      isEditing: false,

      // Selected Items
      selectedRows: [],
      selectedEncounter: null,
      selectedEncounterId: null,
      encounterId: -1,
      bulkAction: "",

      // Server Parameters
      serverParams: {
        columnFilters: {},
        sort: {
          field: "",
          type: "",
        },
        page: 1,
        perPage: 10,
        searchTerm: "",
      },

      // Request Parameters
      patientEncountersRequest: {
        login_id: 0,
        patient_id: 0,
      },

      // Forms Data
      templateForm: {
        name: "",
        clinic_id: null,
      },
      templateErrors: {
        name: null,
      },
      customFormData: null,
      customFormViewMode: false,

      // Field Visibility
      doctorField: false,
      patientField: false,
      clinicField: true,

      // Configuration
      pageSizes: [10, 25, 50, 100],
      isEncounterTemp: false,
      clinic_extra: {
        prefix: "",
        postfix: "",
      },
    };
  },

  computed: {
    encounterName() {
      return this.isEncounterTemp
        ? this.formTranslation?.patient_bill?.encounter_template_list
        : this.formTranslation?.patient_bill?.patients_encounter_list;
    },

    // Table Configuration
    tableColumns() {
      if (this.isEncounterTemp) {
        return [
          {
            field: "id",
            label: this.formTranslation?.common?.id || "ID",
            sortable: true,
          },
          {
            field: "encounters_template_name",
            label:
              this.formTranslation?.patient_encounter_template?.dt_lbl_name ||
              "Name",
            sortable: true,
          },
          {
            field: "actions",
            label:
              this.formTranslation?.patient_encounter?.dt_lbl_action ||
              "Actions",
            sortable: false,
          },
        ];
      }

      return [
        {
          field: "id",
          label: this.formTranslation?.common?.id || "ID",
          sortable: true,
        },
        {
          field: "doctor_name",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_doc_name ||
            "Doctor",
          hidden: this.doctorField,
          sortable: true,
        },
        {
          field: "clinic_name",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_clinic || "Clinic",
          sortable: true,
        },
        {
          field: "patient_name",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_patient ||
            "Patient",
          sortable: true,
        },
        {
          field: "encounter_date",
          label: this.formTranslation?.patient_encounter?.dt_lbl_name || "Date",
          sortable: true,
        },
        {
          field: "status",
          label: this.formTranslation?.common?.status || "Status",
          sortable: true,
        },
        {
          field: "actions",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_action || "Actions",
          sortable: false,
        },
      ];
    },

    // Permissions & Conditions
    canAddEncounter() {
      return (
        !this.isEncounterTemp && this.kcCheckPermission("patient_encounter_add")
      );
    },

    canAddTemplate() {
      return (
        this.isEncounterTemp &&
        this.kcCheckPermission("encounters_template_add")
      );
    },

    // Selection States
    isAllSelected() {
      return (
        this.patientEncounterList.data.length > 0 &&
        this.selectedRows.length === this.patientEncounterList.data.length
      );
    },

    isIndeterminate() {
      return (
        this.selectedRows.length > 0 &&
        this.selectedRows.length < this.patientEncounterList.data.length
      );
    },

    // Pagination
    canGoPrevious() {
      return this.serverParams.page > 1;
    },

    canGoNext() {
      return (
        this.serverParams.page <
        Math.ceil(
          this.patientEncounterList.totalRows / this.serverParams.perPage
        )
      );
    },

    paginationText() {
      const start =
        (this.serverParams.page - 1) * this.serverParams.perPage + 1;
      const end = Math.min(
        start + this.serverParams.perPage - 1,
        this.patientEncounterList.totalRows
      );
      return `${start}-${end} of ${this.patientEncounterList.totalRows}`;
    },

    // Store Getters
    login_id() {
      return this.$store.state.userDataModule.user.ID;
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },
  },

  watch: {
    $route(to, from) {
      this.initializeComponent();
    },

    "templateForm.name"(newVal) {
      this.templateErrors.name = !newVal
        ? this.formTranslation.patient_bill.encounter_template_name_required
        : null;
    },
  },

  mounted() {
    try {
      this.initializeComponent();

      if ("patient_id" in this.$route.params) {
        this.patientField = false;
        this.clinicField = false;
        this.patientEncountersRequest.patient_id =
          this.$route.params.patient_id;
      } else {
        this.patientField = true;
        this.clinicField = true;
      }
    } catch (error) {
      console.error("Initialization error:", error);
      if (typeof displayErrorMessage === "function") {
        displayErrorMessage(error.message || "Error initializing component");
      }
    }
  },

  methods: {
    // Initialization
    async initializeComponent() {
      try {
        this.setTemplateMode(false);
        this.setupUserSpecificFields();
        this.initializeRequestParameters();
        await this.fetchEncounters();
      } catch (error) {
        console.error("Component initialization error:", error);
        throw error;
      }
    },

    setTemplateMode(isRouterChange) {
      try {
        const routeName = isRouterChange
          ? isRouterChange.name
          : this.$route.name;
        this.isEncounterTemp = routeName === "encounter-template";
      } catch (error) {
        console.error("Error setting template mode:", error);
        throw error;
      }
    },

    setupUserSpecificFields() {
      const userRole = this.getUserRole();
      this.doctorField = userRole === "doctor";
      this.patientField = userRole === "patient";
      this.clinicField = !this.$route.params.patient_id;
    },

    initializeRequestParameters() {
      if (this.$route.params.patient_id === undefined) {
        this.patientEncountersRequest.login_id = this.login_id;
        this.patientEncountersRequest.patient_id = 0;
      } else {
        this.patientEncountersRequest.patient_id =
          this.$route.params.patient_id;
        this.patientEncountersRequest.login_id = 0;
      }
    },

    // Data Fetching
    async fetchEncounters() {
      this.loading = true;
      this.error = null;

      try {
        const endpoint = this.isEncounterTemp
          ? "get_encounter_templates"
          : "patient_encounter_list";

        const params = {
          ...this.patientEncountersRequest,
          ...this.serverParams,
        };

        const response = await get(endpoint, params);

        if (response?.data?.status || response?.data?.success) {
          // Check both status and success
          if (this.isEncounterTemp) {
            this.patientEncounterList.data = response.data.data?.list || [];
          } else {
            this.patientEncounterList.data = response.data.data || [];
          }

          this.patientEncounterList.totalRows =
            response.data.data?.total_rows || 0;

          if (response.data.clinic_extra) {
            this.clinic_extra = response.data.clinic_extra;
          }
        } else {
          console.error("API Response:", response);
          throw new Error(
            response?.data?.message || "Failed to fetch encounters"
          );
        }
      } catch (err) {
        console.error("Error:", err);
        this.error = err.message || "Error fetching encounters";

        // Instead of using Vuex action, use your app's error display method
        // This might be different based on your setup. Common examples:
        if (typeof displayErrorMessage === "function") {
          displayErrorMessage(this.error);
        } else if (this.$toast) {
          this.$toast.error(this.error);
        } else {
          alert(this.error); // Fallback
        }
      } finally {
        this.loading = false;
      }
    },

    // Event Handlers
    handleGlobalSearch: debounce(function (event) {
      this.serverParams.page = 1;
      this.fetchEncounters();
    }, 300),

    handleSort(field) {
      if (this.serverParams.sort.field === field) {
        if (this.serverParams.sort.type === "asc") {
          this.serverParams.sort.type = "desc";
        } else if (this.serverParams.sort.type === "desc") {
          this.serverParams.sort.field = "";
          this.serverParams.sort.type = "";
        } else {
          this.serverParams.sort.type = "asc";
        }
      } else {
        this.serverParams.sort.field = field;
        this.serverParams.sort.type = "asc";
      }
      this.fetchEncounters();
    },

    updateColumnFilter: debounce(function (field, value) {
      if (value === "" || value === null) {
        delete this.serverParams.columnFilters[field];
      } else {
        this.serverParams.columnFilters[field] = value;
      }
      this.serverParams.page = 1;
      this.fetchEncounters();
    }, 300),

    handlePerPageChange() {
      this.serverParams.page = 1;
      this.fetchEncounters();
    },

    // Navigation
    previousPage() {
      if (this.canGoPrevious) {
        this.serverParams.page--;
        this.fetchEncounters();
      }
    },

    nextPage() {
      if (this.canGoNext) {
        this.serverParams.page++;
        this.fetchEncounters();
      }
    },

    // Form Handlers
    handleEncounterForm() {
      if (!this.showForm) {
        this.visible = true;
        this.showForm = true;
        this.isEditing = false;
        this.selectedEncounter = null;
      } else {
        this.closeEncounterForm();
      }
    },

    closeEncounterForm() {
      this.visible = false;
      this.showForm = false;
      this.isEditing = false;
      this.selectedEncounter = null;
      this.encounterId = -1;
    },

    handleEncounterTemplateForm() {
      this.showTemplateForm = true;
      this.templateForm = {
        name: "",
        clinic_id: this.$store.state.activeClinicId,
      };
    },

    async saveTemplate() {
      if (!this.templateForm.name) {
        this.templateErrors.name =
          this.formTranslation.patient_bill.encounter_template_name_required;
        return;
      }

      try {
        this.loading = true;
        const response = await post("add_encounter_temp", {
          template_name: this.templateForm.name,
          added_by: this.login_id,
          clinic_id: this.templateForm.clinic_id,
        });

        if (response?.data?.success) {
          this.closeTemplateForm();
          this.fetchEncounters();
          if (typeof displayMessage === "function") {
            displayMessage(response.data.message);
          }
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (err) {
        if (typeof displayErrorMessage === "function") {
          displayErrorMessage(
            err.response?.data?.message || "Failed to create template"
          );
        }
      } finally {
        this.loading = false;
      }
    },

    closeTemplateForm() {
      this.showTemplateForm = false;
      this.templateForm = {
        name: "",
        clinic_id: null,
      };
      this.templateErrors = {
        name: null,
      };
    },

    // Row Actions
    editEncounterData(encounter) {
      this.selectedEncounter = { ...encounter };
      this.encounterId = encounter.id;
      this.isEditing = true;
      this.showForm = true;
      this.visible = true;
      window.scrollTo({ top: 0, behavior: "smooth" });
    },

    async deleteEncounter(id) {
      try {
        this.loading = true;
        const endpoint = this.isEncounterTemp
          ? "delete_encounter_temp"
          : "patient_encounter_delete";

        const response = await get(endpoint, { id });

        if (response?.data?.success) {
          this.selectedRows = this.selectedRows.filter((rowId) => rowId !== id);
          await this.fetchEncounters();
          // Show success message using SweetAlert2
          await this.$swal.fire({
            icon: "success",
            title: "Deleted!",
            text:
              response.data.message ||
              "Consultation has been deleted successfully",
            timer: 1500,
            showConfirmButton: false,
          });
          this.$store.dispatch("showSuccessMessage", response.data.message);
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (err) {
        this.$store.dispatch(
          "showErrorMessage",
          err.response?.data?.message || "Failed to delete encounter"
        );
      } finally {
        this.loading = false;
      }
    },

    async confirmDelete(id) {
      if (this.loading) return;

      try {
        const result = await this.$swal.fire({
          title: this.formTranslation?.clinic_schedule?.dt_are_you_sure,
          text: this.formTranslation?.common?.py_delete,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: this.formTranslation?.common?.yes || "Yes",
          cancelButtonText: this.formTranslation?.common?.cancel || "Cancel",
        });

        if (result.isConfirmed) {
          await this.deleteEncounter(id);
        }
      } catch (error) {
        console.error("Confirmation dialog error:", error);
        if (typeof displayErrorMessage === "function") {
          displayErrorMessage("Error showing confirmation dialog");
        }
      }
    },

    // Bulk Actions
    async handleBulkAction() {
      if (!this.bulkAction || !this.selectedRows.length || this.loading) return;

      if (this.bulkAction === "delete") {
        this.$root.$emit("show-confirmation-dialog", {
          title: this.formTranslation?.clinic_schedule?.dt_are_you_sure,
          message: this.formTranslation?.common?.py_delete,
          confirmText: this.formTranslation?.common?.yes,
          cancelText: this.formTranslation?.common?.cancel,
          type: "danger",
          onConfirm: this.deleteBulkEncounters,
        });
      }
    },

    async deleteBulkEncounters() {
      try {
        this.loading = true;
        const response = await post("module_wise_multiple_data_update", {
          action_perform: "delete",
          module: this.isEncounterTemp
            ? "patient_encounter_template"
            : "patient_encounter_list",
          data: this.selectedRows,
        });

        if (response?.data?.success) {
          this.selectedRows = [];
          this.bulkAction = "";
          await this.fetchEncounters();
          this.$store.dispatch("showSuccessMessage", response.data.message);
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (err) {
        this.$store.dispatch(
          "showErrorMessage",
          err.response?.data?.message || "Failed to delete encounters"
        );
      } finally {
        this.loading = false;
      }
    },

    // Selection Handlers
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedRows = [];
      } else {
        this.selectedRows = this.patientEncounterList.data.map((row) => row.id);
      }
    },

    // Custom Form Handlers
    openCustomForm(row, formData) {
      this.customFormData = {
        ...formData,
        module_id:
          formData.module_type === "appointment_module"
            ? row.appointment_id
            : row.id,
      };
      this.customFormViewMode = row.status === "0";
      this.showCustomForm = true;
    },

    closeCustomForm() {
      this.showCustomForm = false;
      this.customFormData = null;
      this.customFormViewMode = false;
    },

    // Bill Details Handlers
    openBillDetails(row) {
      this.selectedEncounterId = row.id;
      this.billDetailsModel = true;
    },

    closeBillDetails() {
      this.billDetailsModel = false;
      this.selectedEncounterId = null;
    },

    // Permission Checks
    canEditEncounter(encounter) {
      return this.isEncounterTemp
        ? this.kcCheckPermission("encounters_template_edit")
        : this.kcCheckPermission("patient_encounter_edit") &&
            this.getUserRole() !== "patient";
    },

    canDeleteEncounter(encounter) {
      return this.isEncounterTemp
        ? this.kcCheckPermission("encounters_template_delete")
        : this.kcCheckPermission("patient_encounter_delete") &&
            this.getUserRole() !== "patient";
    },

    canViewDashboard(encounter) {
      return this.isEncounterTemp
        ? this.kcCheckPermission("encounters_template_view")
        : this.kcCheckPermission("patient_encounter_view");
    },

    canViewBodyChart(encounter) {
      return (
        this.userData.addOns.bodyChart &&
        this.kcCheckPermission("body_chart_list")
      );
    },

    canViewBillDetails(encounter) {
      return this.checkEnableModule("billing") && encounter.status === "0";
    },

    canViewCustomForm(encounter, form) {
      return (
        this.userData.addOns.kiviPro &&
        this.customFormCondition(encounter, form)
      );
    },

    // Utility Methods
    formatColumnValue(value, column) {
      if (column.field === "encounter_date") {
        return this.formatDate(value);
      }
      return value;
    },

    formatDate(date) {
      if (!date) return "";
      return new Date(date).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },

    customFormCondition(encounter, form) {
      return (
        form &&
        (!form.clinic_ids?.length ||
          form.clinic_ids.includes(encounter.clinic_id)) &&
        ((form.module_type === "appointment_module" &&
          encounter.appointment_id) ||
          form.module_type === "patient_encounter_module")
      );
    },

    getDashboardRoute(row) {
      return {
        name: "patient-encounter.dashboard",
        params: { encounter_id: row.id },
        query: { isEncounterTemp: this.isEncounterTemp },
      };
    },

    getBodyChartRoute(row) {
      return {
        name: "patient-encounter.body-chart",
        params: { encounter_id: row.id },
      };
    },

    canViewBodyChart(row) {
      return (
        this.userData.addOns.bodyChart === true &&
        this.kcCheckPermission("body_chart_list")
      );
    },
  },
};
</script>
