{"name": "medroid/ehr", "authors": [{"name": "medroid.ai", "email": "<EMAIL>"}], "require": {"symfony/http-foundation": "^5.1", "illuminate/collections": "^9", "ext-json": "*", "nesbot/carbon": "^2.45", "league/omnipay": "^3", "omnipay/paypal": "^3", "10quality/wp-query-builder": "^1.0", "dompdf/dompdf": "^3.0", "mpdf/mpdf": "^8.2"}, "license": "GPL-3.0", "autoload": {"psr-4": {"App\\": "./app"}, "files": ["utils/kc_helpers.php", "utils/KC_PMP_Extented.php", "utils/medriod_PDF_helpers.php", "utils/medriod_Task_helpers.php", "app/filters/kc-service-import-fix.php"]}, "config": {"allow-plugins": {"php-http/discovery": true, "platform-check": false}, "platform": {"php": "8.2"}}}