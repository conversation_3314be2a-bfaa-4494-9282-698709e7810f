@import url("https://fonts.googleapis.com/css2?family=Heebo:wght@100;400;500;600;700;800;900&family=Roboto+Slab&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Heebo:wght@100;400;500;600;700;800;900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto+Slab&display=swap");

:root {
  --iq-primary-light: #F0F4FD;
  --iq-primary: #7093E5;
  --iq-primary-dark: #5f84d9;
  --iq-primary-rgb: 112, 147, 229;
  --iq-secondary-light: #fda09f;
  --iq-secondary: #F68685;
  --iq-secondary-dark: #e38685;
  --iq-success: #13C39C;
  --iq-white: #ffffff;
  --iq-dark: #171C26;
  --iq-light: #b5b9c4;
  --iq-black: #000000;
  --iq-body-color: #6E7990;
  --iq-body-bg: #e7ecf1;
  --iq-body-font-family: Poppins, sans-serif;
  --iq-heading-font-family: Heebo, sans-serif;
  --wp--preset--font-family--heading: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

.kivi-widget {
  /*padding: 16px;*/
  /* background: #e7ecf1; */
  font-family: var(--iq-body-font-family);
  font-size: 12px;
  color: var(--iq-body-color);
  box-shadow: 0px 14px 54px rgba(0, 0, 0, 0.05);

  /* Lists (Nested) */
  /* Definition Lists */
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.kivi-widget * {
  box-sizing: border-box;
}

.kivi-widget .form-group {
    margin: 0;
}

.kivi-widget .widget-layout .widget-content {
  height: 515px;
}

.kivi-widget .widget-layout .loader-class {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kivi-widget .widget-layout .mb-1 {
  margin-bottom: 0.5em !important;
}

.kivi-widget .widget-layout .mb-2 {
  margin-bottom: 1em !important;
}

.kivi-widget .widget-layout .mb-3 {
  margin-bottom: 1.5em !important;
}

.kivi-widget .widget-layout .mb-4 {
  margin-bottom: 2em !important;
}

.kivi-widget .widget-layout .iq-bg-primary {
  background-color: var(--iq-primary) !important;
}

.kivi-widget .widget-layout .iq-bg-secondary {
  background-color: var(--iq-secondary) !important;
}

.kivi-widget .widget-layout .iq-bg-white {
  background-color: var(--iq-white) !important;
}

.kivi-widget .widget-layout .iq-bg-primary-light {
  background-color: var(--iq-primary-light) !important;
}

.kivi-widget .widget-layout .iq-color-primary {
  color: var(--iq-primary) !important;
}

.kivi-widget .widget-layout .kc-services-total {
  font-weight: var(--font-weight-bold);
}

.kivi-widget .widget-layout .iq-color-primarya:hover,
.kivi-widget .widget-layout .iq-color-primarybutton:hover,
.kivi-widget .widget-layout .iq-color-primarya:focus,
.kivi-widget .widget-layout .iq-color-primarybutton:focus {
  color: var(--iq-primary-dark) !important;
}

.kivi-widget .widget-layout .iq-color-secondary {
  color: var(--iq-secondary) !important;
}

.kivi-widget .widget-layout .iq-color-secondarya:hover,
.kivi-widget .widget-layout .iq-color-secondarybutton:hover,
.kivi-widget .widget-layout .iq-color-secondarya:focus,
.kivi-widget .widget-layout .iq-color-secondarybutton:focus {
  color: var(--iq-secondary-dark) !important;
}

.kivi-widget .widget-layout .iq-color-body {
  color: var(--iq-body-color) !important;
}

.kivi-widget .widget-layout .iq-color-white {
  color: var(--iq-white) !important;
}

.kivi-widget .widget-layout .iq-color-dark {
  color: var(--iq-dark) !important;
}

.kivi-widget .widget-layout .iq-text-uppercase {
  text-transform: uppercase !important;
}

.kivi-widget .widget-layout .iq-letter-spacing-1 {
  letter-spacing: 1px !important;
  font-weight: 500;
}

.kivi-widget .widget-layout .iq-letter-spacing-2 {
  letter-spacing: 2px !important;
}

.kivi-widget .widget-layout .border {
  border: 1px solid !important;
}

.kivi-widget .widget-layout .border-2 {
  border: 2px solid !important;
}

.kivi-widget .widget-layout .border-4 {
  border: 4px solid !important;
}

.kivi-widget .widget-layout .border-color-primary {
  border-color: var(--iq-primary) !important;
}

.kivi-widget .widget-layout .border-color-secondary {
  border-color: var(--iq-secondary) !important;
}

.kivi-widget .widget-layout .border-color-white {
  border-color: var(--iq-white) !important;
}

.kivi-widget .widget-layout .d-flex {
  display: flex !important;
}

.kivi-widget .widget-layout .d-grid {
  display: grid !important;
}

.kivi-widget .widget-layout .d-none {
  display: none !important;
}

.kivi-widget .widget-layout .flex-column {
  flex-direction: column;
}

.kivi-widget .widget-layout .flex-wrap {
  flex-wrap: wrap;
}

.kivi-widget .widget-layout .gap-1 {
  gap: 1rem;
}

.kivi-widget .widget-layout .place-content-center {
  place-content: center !important;
}

.kivi-widget .widget-layout .justify-content-center {
  justify-content: center !important;
}

.kivi-widget .widget-layout .justify-content-between {
  justify-content: space-between !important;
}

.kivi-widget .widget-layout .justify-content-end {
  justify-content: end !important;
}

.kivi-widget .widget-layout .justify-content-evenly {
  justify-content: space-evenly !important;
}

.kivi-widget .widget-layout .align-items-center {
  align-items: center !important;
}

.kivi-widget .widget-layout .align-self-center {
  align-self: center !important;
}

.kivi-widget .widget-layout .gap-05 {
  gap: 8px !important;
}

.kivi-widget .widget-layout .gap-1 {
  gap: 16px !important;
}

.kivi-widget .widget-layout .gap-1-5 {
  gap: 20px !important;
}

.kivi-widget .widget-layout .gap-2 {
  gap: 32px !important;
}

.kivi-widget .widget-layout .grid-template-2 {
  gap: 16px;
  grid-template-columns: repeat(2, minmax(0%, 1fr));
}

.kivi-widget .widget-layout .grid-template-3 {
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 10px;
}

.kivi-widget .widget-layout .grid-col-2 {
  grid-column: span 2;
}

.kivi-widget .widget-layout .grid-col-3 {
  grid-column: span 3;
}

.kivi-widget .widget-layout .grid-row-2 {
  grid-row: span 2;
}

.kivi-widget .widget-layout .grid-row-3 {
  grid-row: span 3;
}

.kivi-widget .widget-layout .kivi-row {
  display: flex;
  flex-wrap: wrap;
  margin-left: 0 !important;
  width: 100%;
}

.kivi-widget .widget-layout .kivi-col-6 {
  flex: 0 0 auto;
  width: 50%;
  padding: 0 8px 0 0;
  max-width: 100%;
}

.kivi-widget .widget-layout .text-center {
  text-align: center !important;
}

.kivi-widget .widget-layout .mt-0 {
  margin-top: 0 !important;
}

.kivi-widget .widget-layout .mt-1 {
  margin-top: 8px !important;
}

.kivi-widget .widget-layout .mt-2 {
  margin-top: 12px !important;
}

.widget-content .card-list-data .form-group textarea {
  margin-bottom: 12px !important;
}

.kivi-widget .widget-layout .mt-3 {
  margin-top: 16px !important;
}

.kivi-widget .widget-layout .mt-4 {
  margin-top: 24px !important;
}

.kivi-widget .widget-layout .my-3 {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}

.kivi-widget .widget-layout .my-4 {
  margin-top: 24px !important;
  margin-bottom: 24px !important;
}

.kivi-widget .widget-layout .pt-0 {
  padding-top: 0 !important;
}

.kivi-widget .widget-layout .pt-1 {
  padding-top: 8px !important;
}

.kivi-widget .widget-layout .pt-2 {
  padding-top: 12px !important;
}

.kivi-widget .widget-layout .pt-3 {
  padding-top: 16px !important;
}

.kivi-widget .widget-layout .pt-4 {
  padding-top: 24px !important;
}

.kivi-widget .widget-layout .pe-1 {
  padding-right: 8px !important;
}

.kivi-widget .widget-layout .pe-2 {
  padding-right: 12px !important;
}

.kivi-widget .widget-layout .position-relative {
  position: relative !important;
}

.kivi-widget .widget-layout a {
  color: var(--iq-primary);
  outline: none;
  text-decoration: none !important;
  transition: color 300ms ease-in-out;
}

.kivi-widget .widget-layout a:hover,
.kivi-widget .widget-layout a:focus {
  color: var(--iq-primary-dark);
  outline: none;
  transition: color 300ms ease-in-out;
}

.kivi-widget .widget-layout ins {
  background: #fff9c0;
  text-decoration: none;
}

.kivi-widget .widget-layout img {
  max-width: 100%;
  height: auto;
}

.kivi-widget .widget-layout pre {
  background: #eee;
  padding: 15px;
  border: 1px solid #eee;
}

.kivi-widget .widget-layout hr {
  margin: 20px 0;
  padding: 0px;
  border-bottom: 1px solid #e0e0e0 !important;
  border-top: 0px;
  background-color: unset;
  max-width: 100%;
  height: unset;
}

.kivi-widget .widget-layout blockquote {
  padding: 15px 30px;
  border: 1px solid #f2f2f2;
  border-radius: 5px;
  border-left: 5px solid #0d1e67;
  margin-bottom: 30px;
}

.kivi-widget .widget-layout blockquote cite {
  font-family: "Poppins", sans-serif;
  font-weight: bold;
  color: #0d1e67;
}

.kivi-widget .widget-layout blockquote ol:last-child,
.kivi-widget .widget-layout blockquote p:last-child,
.kivi-widget .widget-layout blockquote ul:last-child {
  margin-bottom: 0;
}

.kivi-widget .widget-layout blockquote .blockquote-footer {
  font-style: italic;
  color: #6c757d;
  font-size: 14px;
  margin-top: 10px;
}

.kivi-widget .widget-layout blockquote .blockquote-footer cite {
  color: #0d1e67;
}

.kivi-widget .widget-layout blockquote span.text-right {
  text-align: right;
  display: block;
  color: #0d1e67;
}

.kivi-widget .widget-layout ol,
.kivi-widget .widget-layout ul {
  padding-left: 25px;
  margin-bottom: 16px;
}

.kivi-widget .widget-layout ol li {
  list-style: decimal;
}

.kivi-widget .widget-layout .widget-tabs li.tab-item {
  margin: 8px 0 0 48px;
}

.kivi-widget .widget-layout ol ol {
  padding-left: 25px;
}

.kivi-widget .widget-layout ul li {
  list-style: inherit;
  margin: 0;
}

.kivi-widget .widget-layout dl dd {
  margin-bottom: 15px;
}

.kivi-widget .widget-layout dl dd:last-child {
  margin-bottom: 0px;
}

.kivi-widget .widget-layout h1,
.kivi-widget .widget-layout h2,
.kivi-widget .widget-layout h3,
.kivi-widget .widget-layout h4,
.kivi-widget .widget-layout h5,
.kivi-widget .widget-layout h6 {
  font-family: var(--iq-heading-font-family);
  font-weight: 500;
  color: var(--iq-dark);
  font-style: normal;
  text-transform: none;
}

.kivi-widget .widget-layout h1 {
  font-size: 30px;
  margin-bottom: 0px !important;
}

.kivi-widget .widget-layout h2 {
  font-size: 24px;
  margin-bottom: 0px !important;
}

.kivi-widget .widget-layout h3 {
  margin-bottom: 0px !important;
  font-size: 20px;
}

.kivi-widget .widget-layout h4 {
  margin-bottom: 0px !important;
  font-size: 16px;
}

.kivi-widget .widget-layout h5 {
  margin-bottom: 0px !important;
  font-size: 16px;
}

.kivi-widget .widget-layout .w-100 {
  width: 100%;
}

.kivi-widget .widget-layout h1,
.kivi-widget .widget-layout h2,
.kivi-widget .widget-layout h3,
.kivi-widget .widget-layout h4,
.kivi-widget .widget-layout h5,
.kivi-widget .widget-layout h6 {
  margin: 0 !important;
}

.kivi-widget .widget-layout h6 {
  margin-bottom: 0px !important;
  font-size: 14px;
}

.kivi-widget .widget-layout p {
  font-size: 12px;
  font-weight: 500;
  color: unset;
  margin: 0px !important;
  word-break: break-word;
}

.kivi-widget .widget-layout .fw-normal {
  font-weight: 400;
}

.kivi-widget .widget-layout .fw-bolder {
  font-weight: 500;
}

.kivi-widget .widget-layout .fw-bold {
  font-weight: 600;
}

.kivi-widget .widget-layout .iq-badge {
  display: inline-block;
  border-radius: 40px;
  padding: 8px 14px;
}

.kivi-widget .widget-layout .iq-doctor-badge {
  overflow: hidden;
  text-align: center;
}

.kivi-widget .widget-layout .iq-doctor-badge:before,
.kivi-widget .widget-layout .iq-doctor-badge:after {
  background-color: #e0e0e0;
  content: "";
  display: inline-block;
  height: 1px;
  position: relative;
  vertical-align: middle;
  width: 50%;
}

.kivi-widget .widget-layout .iq-doctor-badge:before {
  right: 8px;
  margin-left: -50%;
}

.kivi-widget .widget-layout .iq-doctor-badge:after {
  left: 8px;
  margin-right: -50%;
}

.kivi-widget .widget-layout .alert {
  padding: 16px 16px;
}

.kivi-widget .widget-layout .alert-relative {
  position: relative;
}

.kivi-widget .widget-layout .alert.alert-popup:not(.error) {
  position: absolute;
  z-index: 10;
  width: 96%;
  opacity: 1;
  top: 3%;
  left: 2%;
}

.kivi-widget .widget-layout .alert-success.alert-left {
  border: unset;
  border-left: 4px solid #1aa053;
  background: #2bb768;
  color: white;
  border-radius: 6px;
  /* padding: 8px 16px; */
}

.kivi-widget .widget-layout .alert-danger.alert-left {
  border: unset;
  border-left: 4px solid #721c24;
  background: #bf3939;
  color: white !important;
  border-radius: 6px;
  margin-bottom: unset;
  padding: 8px 16px;
  width: 50%;
}

.kivi-widget .widget-layout .alert.alert-danger.border-start {
  border: unset;
  border-left: 4px solid;
  border-radius: 6px;
  padding: 10px 32px;
}

.kivi-widget .widget-layout .iq-button {
  padding: 8px 32px;
  font-size: 14px;
  border: 0;
  border-radius: 4px;
  display: inline-block;
  cursor: pointer;
  text-transform: uppercase !important;
  transition: background-color 400ms ease-in-out;
  font-weight: 600;
}

.kivi-widget .widget-layout #timeSlotLists {
  margin-top: 15px;
}

.kivi-widget .widget-layout #timeSlotLists .iq-button {
  font-size: 12px;
  width: 100%;
  text-align: center;
  text-transform: unset !important;
}

.kivi-widget .widget-layout .iq-button.iq-button-primary {
  background-color: var(--iq-primary);
  color: var(--iq-white);
}

.kivi-widget .widget-layout .iq-button.iq-button-primary:hover {
  background-color: var(--iq-primary-dark) !important;
}

.kivi-widget .widget-layout .iq-button.iq-button-secondary {
  background-color: var(--iq-secondary);
  color: var(--iq-white);
}

.kivi-widget .widget-layout .iq-button.iq-button-secondary:hover {
  background-color: var(--iq-secondary-dark) !important;
}

.kivi-widget .widget-layout .iq-button.iq-button-white {
  background-color: var(--iq-white);
  color: var(--iq-dark);
}

.kivi-widget .widget-layout .iq-button.iq-button-white:hover,
.kivi-widget .widget-layout .iq-button.iq-button-white.active {
  background-color: var(--iq-primary);
  color: var(--iq-white);
}

.kivi-widget .widget-layout .iq-button:focus,
.kivi-widget .widget-layout .iq-button:hover {
  transition: background-color 400ms ease-in-out;
}

.kivi-widget .widget-layout .iq-btn-lg {
  padding: 16px 32px !important;
}

.kivi-widget .widget-layout .iq-time-select {
  font-size: 15px;
}

.kivi-widget .widget-layout .widget-pannel .iq-kivicare-form-control {
  display: block;
  padding: 12px 16px;
  outline: none;
  border: 1px solid transparent;
  transition: border 400ms ease-in-out, color 400ms ease-in-out;
  width: 100%;
  background-color: var(--iq-white) !important;
  color: var(--iq-body-color);
  border: 1px solid #eee;
  border-radius: 5px;
  background: unset;
  margin: 0;
  /* height: unset; */
  /* height: 100%; */
}

.kivi-widget .widget-layout .widget-pannel .iq-kivicare-form-control:focus {
  border-color: var(--iq-primary);
  transition: border 400ms ease-in-out, color 400ms ease-in-out;
}

.kivi-widget .widget-layout .widget-pannel .iq-kivicare-form-control::-moz-placeholder {
  color: var(--iq-light);
}

.kivi-widget .widget-layout .widget-pannel .iq-kivicare-form-control:-ms-input-placeholder {
  color: var(--iq-light);
}

.kivi-widget .widget-layout .widget-pannel .iq-kivicare-form-control::placeholder {
  color: var(--iq-light);
}

.kivi-widget .widget-layout .iq-form-label {
  color: var(--iq-dark);
}

.kivi-widget .widget-layout label {
  margin-bottom: 0;
  box-shadow: 0px 14px 54px rgba(0, 0, 0, 0.05);
}

.kivi-widget .widget-layout .card-checkbox {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

/* .kivi-widget .widget-layout .form-group{
  height: unset;
  width: unset;
} */
/* .kivi-widget .widget-layout .form-group input{
    width: 0;
    height: 65%;
    opacity: 1;
} */
.kivi-widget .widget-layout ul,
.kivi-widget .widget-layout ol,
.kivi-widget .widget-layout dl {
  font-size: unset;
}

.kivi-widget .widget-layout .custom-radio .custom-control-input~.custom-control-label {
  font-size: unset;
  display: inline-block;
}

.kivi-widget .widget-layout input[type="text"],
.kivi-widget .widget-layout input[type="password"],
.kivi-widget .widget-layout input[type="email"],
.kivi-widget .widget-layout input[type="url"],
.kivi-widget .widget-layout input[type="date"],
.kivi-widget .widget-layout input[type="month"],
.kivi-widget .widget-layout input[type="time"],
.kivi-widget .widget-layout input[type="datetime"],
.kivi-widget .widget-layout input[type="datetime-local"],
.kivi-widget .widget-layout input[type="week"],
.kivi-widget .widget-layout input[type="number"],
.kivi-widget .widget-layout input[type="search"],
.kivi-widget .widget-layout input[type="tel"],
.kivi-widget .widget-layout input[type="color"],
.kivi-widget .widget-layout textarea {
  font-size: unset;
}

.kivi-widget .widget-layout input,
.kivi-widget .widget-layout input[type=text],
.kivi-widget .widget-layout input[type=email],
.kivi-widget .widget-layout input[type=search],
.kivi-widget .widget-layout input[type=password],
.kivi-widget .widget-layout textarea {
  line-height: unset;
  height: auto;
}

/*.kivi-widget .widget-layout .flatpickr-monthDropdown-months{*/
/*  */
/*}*/
.kivi-widget .widget-layout .flatpickr-current-month {
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-top: 5px;
}

.kivi-widget .widget-layout .form-group input[type=file] {
  height: unset;
  line-height: inherit;
}

.kivi-widget .widget-layout .form-group .form-label {
  color: var(--iq-dark);
  display: inline-block;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  line-height: 1.2;
}

.kivi-widget .widget-layout #appointment-descriptions .form-group textarea {
  margin-bottom: 2rem !important;
}


.kivi-widget .widget-layout #file-upload .form-group input {
  margin-bottom: 1rem;
}

.kivi-widget .widget-layout .widget-pannel .iq-kivi-search {
  position: relative;
  min-width: 300px;
}

.kivi-widget .widget-layout .widget-pannel .iq-kivi-search .iq-kivi-icon {
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 1em;
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.kivi-widget .widget-layout .widget-pannel .iq-kivi-search .iq-kivicare-form-control {
  padding-left: 40px;
  height: 100%;
  /* height: unset; */
}

.kivi-widget .widget-layout .iq-card {
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 0 0 auto;
  padding: 16px;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--iq-white);
  background-clip: border-box;
  border: 1px solid;
  border-color: transparent;
  border-radius: 8px;
}

.kivi-widget .widget-layout .iq-card.iq-card-sm {
  padding: 14px;
}

.kivi-widget .widget-layout .iq-card.iq-card-lg {
  padding: 25px;
}

.kivi-widget .widget-layout .iq-card.iq-card-border {
  border-color: #ececec;
}

.kivi-widget .widget-layout .iq-card.iq-card-bg>.iq-media {
  padding-top: 16px;
}

.kivi-widget #kivi_confirm_payment_page .kc-confirmation-info-section .card-list {
  display: grid;
  grid-template-columns: repeat(1, minmax(0%, 1fr));
  gap: 24px;
}

.kivi-widget .widget-layout .card-list {
  display: grid;
  grid-template-columns: repeat(2, minmax(0%, 1fr));
  gap: 24px;
}

.kivi-widget .widget-layout table td,
.kivi-widget .widget-layout table th {
  text-align: start;
}

.kivi-widget .widget-layout .border-0 {
  border-width: 0 !important;
}

.kivi-widget .widget-layout .card-list-data {
  position: relative;
  height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
}


.kivi-widget .widget-layout .iq-category-list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0%, 1fr));
  gap: 32px;
  text-align: center;
}

.kivi-widget .widget-layout .iq-category-list .iq-client-widget .iq-card .avatar {
  background: var(--iq-primary-light);
  color: var(--iq-primary);
  border-radius: 50%;
  transition: background-color 0.3s ease-in-out;
}

.kivi-widget .widget-layout .iq-category-list .iq-client-widget .iq-card:hover .avatar {
  background: var(--iq-primary);
  color: var(--iq-white);
  transition: background-color 0.3s ease-in-out;
}

.kivi-widget .widget-layout .iq-category-list .iq-client-widget .iq-card:hover .iq-dentist-price {
  transition: background-color 0.3s ease-in-out;
  color: var(--iq-primary);
}

.kivi-widget .widget-layout .card-widget-footer {
  /* position: absolute; */
  bottom: 2%;
  right: 5%;
  width: 99%;
  display: flex;
}

#patientEmail p {
  inline-size: 150px;
}

.kivi-widget .widget-layout #kivicare_error_msg_confirm+button {
  margin-right: 8px;
}

@media (max-width: 576px) {
  .kivi-widget .widget-layout .card-widget-footer {
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding-top: 32px;
    position: unset;
    bottom: unset;
    right: unset;
    width: unset;
    flex-wrap: wrap;
  }

  .kivi-widget .widget-layout .card-widget-footer div {
    margin: auto !important;
  }

  .kivi-widget .widget-layout .card-widget-footer span {
    width: 100% !important;
  }

  .kivi-widget .widget-layout .widget-pannel .iq-kivi-search {
    min-width: unset;
  }
}

.kivi-widget .widget-layout .iq-client-widget .service-content {
  height: 100%;
}

.kivi-widget .widget-layout .iq-client-widget .card-checkbox:checked+.btn-border01 .iq-fancy-design {
  background-color: transparent;
  border-color: var(--iq-primary);
}

.kivi-widget .widget-layout .iq-client-widget .card-checkbox:checked+.btn-border01 .iq-fancy-design::after {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  background: var(--iq-primary);
  border: 2px solid var(--iq-white);
  border-radius: 50%;
  height: 20px;
  width: 20px;
  z-index: 1;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
}

.kivi-widget .widget-layout .iq-client-widget .card-checkbox:checked+.btn-border01 .iq-fancy-design .avatar {
  background: var(--iq-primary);
  color: var(--iq-white);
  transition: background-color 0.3s ease-in-out;
}

.kivi-widget .widget-layout .iq-client-widget .card-checkbox:checked+.btn-border01 .iq-fancy-design .iq-dentist-price {
  transition: background-color 0.3s ease-in-out;
  color: var(--iq-primary);
}

.kivi-widget .widget-layout .iq-client-widget .card-checkbox:checked+.btn-border01 .iq-fancy-design.iq-doctor-widget:not(:hover) {
  background-color: unset;
  border-color: var(--iq-primary);
}

.kivi-widget .widget-layout .iq-client-widget .btn-border01 .iq-fancy-design {
  cursor: pointer;
  transition: background-color 0.3s ease-in-out, border 0.3s ease-in-out;
}

.kivi-widget .widget-layout .iq-client-widget .btn-border01 .iq-fancy-design:hover {
  border: 1px solid var(--iq-primary);
}

.kivi-widget .widget-layout .my-2 {
  margin: 10px 0
}

.kivi-widget .widget-layout .iq-client-widget .btn-border01 .iq-fancy-design.iq-doctor-widget:hover .profile-bg {
  /* background: var(--iq-primary-dark); */
}

.kivi-widget .widget-layout .iq-doctor-widget.iq-card .iq-navbar-header .profile-bg {
  position: absolute;
  width: 100%;
  height: 21%;
  top: 0;
  left: 0;
  border-radius: 8px 8px 0px 0px;
  background: var(--iq-primary-light);
  transition: background-color 0.3s ease-in-out, border 0.3s ease-in-out;
}

.kivi-widget .widget-layout .iq-doctor-widget.iq-card .media {
  z-index: 1;
  margin-top: -90px;
}

.kivi-widget .widget-layout .iq-doctor-widget.iq-card .media img {
  border: 3px solid var(--iq-white);
}

.kivi-widget .widget-layout .iq-grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 32px;
}

.kivi-widget .widget-layout .iq-grid-container .item-img-1 {
  grid-row-end: span 2;
}

.kivi-widget .widget-layout .iq-grid-container .iq-preview-details,
.kivi-widget .widget-layout .kivi-row .iq-preview-details {
  background-color: var(--iq-primary-light);
}

.kivi-widget .widget-layout hr::before,
.kivi-widget .widget-layout hr::after {
  height: 0 !important;
}

.kivi-widget .widget-layout .iq-inline-datepicker {
  border-radius: 8px;
  background-color: var(--iq-primary-light);
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-calendar {
  width: 100%;
  box-shadow: none;
  background-color: var(--iq-primary-light);
}

.iq-kivi-calendar-slot .flatpickr-innerContainer,
.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-innerContainer {
  justify-content: center;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-innerContainer .flatpickr-days {
  width: 100%;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-innerContainer .flatpickr-days .dayContainer {
  width: 100%;
  min-width: 225px;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months {
  margin-bottom: 16px;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-current-month {
  padding-top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-next-month,
.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-prev-month {
  padding: 0 8px;
  color: #7093e5 !important;
  fill: #7093e5 !important;
  display: flex;
  align-items: center;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-next-month svg,
.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-prev-month svg {
  width: 24px;
  height: 24px;
  border: 1px solid;
  padding: 6px;
  border-radius: 50%;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-next-month.flatpickr-disabled,
.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-prev-month.flatpickr-disabled {
  display: none;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-next-month:hover svg,
.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-months .flatpickr-prev-month:hover svg {
  fill: #7093e5 !important;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-day {
  width: 100% !important;
  margin: auto !important;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-rContainer {
  width: 100%;
}

.kivi-widget .widget-layout .iq-inline-datepicker .flatpickr-innerContainer .flatpickr-days .dayContainer {
  max-width: 100%;
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.kivi-widget .widget-layout .scrollbar-track-y {
  width: 5px !important;
}

.kivi-widget .widget-layout .scrollbar-thumb {
  width: 4px !important;
}

.kivi-widget .widget-layout ::-webkit-scrollbar {
  width: 5px;
}

/* Track */
.kivi-widget .widget-layout ::-webkit-scrollbar-track {
  background: var(--iq-primary-light);
}

/* Handle */
.kivi-widget .widget-layout ::-webkit-scrollbar-thumb {
  background: var(--iq-primary);
}

.kivi-widget .widget-layout .body-scroll-hidden {
  overflow: hidden;
  overflow-y: auto;
}

.kivi-widget .widget-layout .flatpickr-day.selected,
.kivi-widget .widget-layout .flatpickr-day.startRange,
.kivi-widget .widget-layout .flatpickr-day.endRange,
.kivi-widget .widget-layout .flatpickr-day.selected.inRange,
.kivi-widget .widget-layout .flatpickr-day.startRange.inRange,
.kivi-widget .widget-layout .flatpickr-day.endRange.inRange,
.kivi-widget .widget-layout .flatpickr-day.selected:focus,
.kivi-widget .widget-layout .flatpickr-day.startRange:focus,
.kivi-widget .widget-layout .flatpickr-day.endRange:focus,
.kivi-widget .widget-layout .flatpickr-day.selected:hover,
.kivi-widget .widget-layout .flatpickr-day.startRange:hover,
.kivi-widget .widget-layout .flatpickr-day.endRange:hover,
.kivi-widget .widget-layout .flatpickr-day.selected.prevMonthDay,
.kivi-widget .widget-layout .flatpickr-day.startRange.prevMonthDay,
.kivi-widget .widget-layout .flatpickr-day.endRange.prevMonthDay,
.kivi-widget .widget-layout .flatpickr-day.selected.nextMonthDay,
.kivi-widget .widget-layout .flatpickr-day.startRange.nextMonthDay,
.kivi-widget .widget-layout .flatpickr-day.endRange.nextMonthDay {
  background: var(--iq-primary);
  border-color: var(--iq-primary);
}

.kivi-widget .container-fluid,
.kivi-widget .container-sm,
.kivi-widget .container-md,
.kivi-widget .container-lg,
.kivi-widget .container-xl {
  max-width: 100%;
  margin-right: 0;
  margin-left: 0;
  padding-left: 0;
  padding-right: 0;
}

.kivi-widget .widget-layout {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  -moz-column-gap: 8px;
  column-gap: 8px;
}

.kivi-widget .widget-layout .widget-pannel {
  grid-column: span 2;
}

.kivi-widget .widget-layout .iq-top-left-ribbon {
  position: absolute;
  top: 0;
  left: 25px;
  height: 36px;
  width: 36px;
  background-color: var(--iq-primary);
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
}

.kivi-widget .widget-layout .iq-top-left-ribbon svg {
  margin: 0 auto;
  padding: 8px 0;
}

.kivi-widget .widget-layout .iq-top-left-ribbon-service {
  position: absolute;
  top: 0;
  left: 25px;
  height: 30px;
  width: 30px;
  background-color: var(--iq-primary);
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
}

.kivi-widget .widget-layout .iq-top-left-ribbon-service svg {
  margin: 0 auto;
  padding: 5px 0;
}

.kivi-widget .widget-layout .nav-tabs {
  list-style: none;
  padding: 0;
  display: flex;
  margin: 0;
  border-bottom: none;
}

.kivi-widget .widget-layout .nav-tabs .tab-item {
  font-weight: 500;
  /*text-transform: uppercase;*/
  padding: 10px 40px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  transition: background-color 0.2s ease-in-out;
}

.kivi-widget .widget-layout .nav-tabs .tab-item a {
  color: var(--iq-body-color);
  font-size: 16px;
  border: none;
  background: transparent
}

.kivi-widget .widget-layout .nav-tabs .tab-item.active a {
  color: var(--iq-primary);
}

.kivi-widget .widget-layout .nav-tabs .tab-item.active {
  background-color: var(--iq-primary-light);
  color: var(--iq-primary);
  border-top: 3px solid var(--iq-primary);
}

.kivi-widget .widget-layout #login-register-panel {
  background-color: var(--iq-primary-light);
  padding: 30px 25px;
}

.kivi-widget .widget-layout #confirmed.iq-tab-pannel.active {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90%;
}

@-webkit-keyframes fade {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fade {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.kivi-widget .widget-layout .tab-content {
  background-color: var(--iq-primary-light);
  padding: 32px;
  height: 100%;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid #e0e0e0;
}

.kivi-widget .widget-layout .tab-content .iq-tab-pannel {
  display: none;
}

.kivi-widget .widget-layout .tab-content .iq-tab-pannel.iq-fade.active {
  -webkit-animation: fade 50ms ease-in-out;
  animation: fade 50ms ease-in-out;
}

.kivi-widget .widget-layout .tab-content .iq-tab-pannel.active {
  display: block !important;
}

.kivi-widget .widget-layout .widget-tabs ul {
  font-size: unset;
}

.kivi-widget .widget-layout .widget-tabs ul li a p {
  font-size: 12px;
  font-family: "Poppins", sans-serif;
}

.kivi-widget .widget-layout small {
  font-size: 14px;
}

.kivi-widget .widget-layout .widget-tabs .tab-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 16px;
  padding-left: 0 !important;
  justify-content: center;
}

.kivi-widget .widget-layout .widget-tabs .tab-item {
  list-style: none;
  position: relative;
  transition: opacity 400ms ease-in-out;
  cursor: pointer;
}

.kivi-widget .widget-layout .widget-tabs .tab-item:not(.active) {
  opacity: 0.8;
  transition: opacity 400ms ease-in-out;
}

.kivi-widget .widget-layout .widget-tabs .tab-item::before {
  content: "";
  position: absolute;
  top: 1px;
  left: -47px;
  width: 25px;
  height: 25px;
  border: 1px solid;
  /* padding: 8px; */
  border-color: var(--iq-white);
  background-color: var(--iq-primary);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-6 -6 12 12'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
  border-radius: 50%;
  transition: background-color 400ms ease-in-out;
}

.kivi-widget .widget-layout .widget-tabs .tab-item:not(:last-child):after {
  content: "";
  position: absolute;
  top: 35px;
  left: -35px;
  width: 1px;
  height: calc(100% - 25px);
  background-color: var(--iq-white);
}

.kivi-widget .widget-layout .flatpickr-calendar {
  width: 100%;
  height: 85%;
}

.kivi-widget .widget-layout .flatpickr-weekdays,
.kivi-widget .widget-layout .flatpickr-calendar .flatpickr-days {
  width: 100%;
  margin-top: 10px;
  padding: 0 15px;
}

.kivi-widget .widget-layout ul,
.kivi-widget .widget-layout ol {
  margin: 0;
}

.kivi-widget .widget-layout .flatpickr-calendar .dayContainer {
  width: 100%;
  min-width: 100%;
}

.kivi-widget .widget-layout .widget-pannel .iq-search-bg-color {
  background-color: #f0f4fc !important;
  border: unset !important;
}

@media (max-width: 576px) {
  .kivi-widget .widget-layout .flatpickr-calendar {
    width: 100%;
    height: 100%;
  }

  .kivi-widget .widget-layout .flatpickr-weekdays,
  .kivi-widget .widget-layout .flatpickr-calendar .flatpickr-days {
    width: 100%;
  }

  .kivi-widget .widget-layout .flatpickr-calendar .dayContainer {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }
}



.kivi-widget .widget-layout .widget-tabs .tab-item.active::before {
  transition: background-color 400ms ease-in-out;
  background-color: var(--iq-white);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-6 -6 12 12'%3e%3ccircle r='2' fill='%237093E5'/%3e%3c/svg%3e");
}

.kivi-widget .widget-layout .widget-tabs .tab-item:not(.active)[data-check=true] {
  opacity: 1;
}

.kivi-widget .widget-layout .widget-tabs .tab-item:not(.active)[data-check=true]::before {
  transition: background-color 400ms ease-in-out;
  background-color: var(--iq-success);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}

.kivi-widget .widget-layout .widget-tabs .tab-item .tab-link,
.kivi-widget .widget-layout .widget-tabs .tab-item .tab-link h6,
.kivi-widget .widget-layout .widget-tabs .tab-item .tab-link h5,
.kivi-widget .widget-layout .widget-tabs .tab-item .tab-link p {
  transition: opacity 0.2s ease-in-out;
  color: var(--iq-white);
  margin-bottom: 0px;
}

.kivi-widget .widget-layout .widget-pannel>.tab-content {
  background-color: var(--iq-white);
}

.kivi-widget .widget-layout .widget-pannel>.tab-content form {
    padding: 0;
    text-align: left;
}

.kivi-widget .widget-layout .iq-time-slot .selected-time:checked+.iq-button-white {
  background-color: var(--iq-primary);
  color: var(--iq-white);
}

.kivi-widget .widget-layout .iq-time-slot .iq-button-white {
  padding: 8px 6px;
  color: #6e7990;
}

.kivi-widget .widget-layout .avatar-70 {
  height: 70px;
  width: 70px;
  min-width: 70px;
  text-align: center;
  line-height: 70px;
  border-radius: 4px;
}

.kivi-widget .widget-layout .avatar-90 {
  height: 80px;
  width: 80px;
  min-width: 80px;
  text-align: center;
  line-height: 80px;
  border-radius: 4px;
}

.kivi-widget .widget-layout .rounded-circle {
  border-radius: 50% !important;
}

.kivi-widget .widget-layout .icon-img {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1200px) {
  .kivi-widget .widget-layout .iq-card {
    padding: 30px;
  }

  .kivi-widget .widget-layout .iq-card.iq-card-sm {
    padding: 16px;
  }

  .kivi-widget .widget-layout .iq-card.iq-hover-card {
    padding: 40px;
  }

  .kivi-widget .widget-layout .iq-card.iq-card-lg {
    padding: 32px;
  }
}

@media (max-width: 1024px) {
  .kivi-widget .widget-layout .widget-tabs {
    min-height: unset;
  }

  .kivi-widget .widget-layout .widget-tabs .tab-list {
    flex-direction: row;
    justify-content: center;
  }

  .kivi-widget .widget-layout .widget-tabs .tab-item {
    padding: 16px 16px;
  }

  .kivi-widget .widget-layout .widget-tabs .tab-item .tab-link {
    display: none;
  }

  .kivi-widget .widget-layout .widget-tabs .tab-item::before {
    width: 5px;
    height: 5px;
    padding: 8px;
  }

  .kivi-widget .widget-layout .widget-tabs li.tab-item {
    margin: 8px 0 0 0;
  }

  .kivi-widget .widget-layout .widget-tabs .tab-item::before {
    top: 1px;
    left: 0px;
  }

  .kivi-widget .widget-layout .widget-tabs .tab-item:not(:last-child):after {
    top: 10px;
    left: 22px;
    width: calc(100% - 15px);
    height: 2px;
  }

  .kivi-widget .widget-layout .iq-calendar-card {
    height: unset;
    overflow-y: unset;
    overflow-x: unset;
  }

  .kivi-widget .widget-layout {
    grid-template-columns: repeat(1, 1fr);
    row-gap: 16px;
    -moz-column-gap: unset;
    column-gap: unset;
  }

  .kivi-widget .widget-layout .card-list-data {
    /* height: 283px; */
  }
}

@media (max-width: 768px) {
  .kivi-widget .widget-layout .iq-card {
    padding: 16px;
  }

  .kivi-widget .widget-layout .iq-card.iq-card-sm {
    padding: 16px;
  }

  .kivi-widget .widget-layout .iq-card.iq-hover-card {
    padding: 40px;
  }

  .kivi-widget .widget-layout .iq-card.iq-card-lg {
    padding: 16px;
  }

  .kivi-widget .widget-layout .grid-template-2 {
    grid-template-columns: repeat(1, 1fr);
    gap: 16px;
  }

  .kivi-widget .widget-layout .iq-grid-container {
    grid-template-columns: 1fr;
  }

  .kivi-widget .widget-layout .iq-category-list {
    grid-template-columns: repeat(2, minmax(0%, 1fr));
  }

  .kivi-widget .widget-layout .kivi-col-6 {
    flex: unset;
    width: 100%;
    max-width: 100%;
  }
}

.kivi-widget .widget-layout .iq-kivi-calendar-slot .time-slots {
  height: 400px;
  overflow: auto;
}

.kivi-widget .widget-layout .card-list-data.iq-kivi-calendar-slot {
  overflow-x: unset;
  overflow-y: unset;
}

@media (max-width: 576px) {
  .kivi-widget .widget-layout .card-list-data {
    height: 470px;
  }

  .kivi-widget .widget-layout .card-list {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .kivi-widget .widget-layout .card-list-data.iq-kivi-calendar-slot {
    overflow-x: unset;
    overflow-y: auto;
  }
}

@media (min-width: 576px) {
  .kivi-widget .widget-layout .iq-doctor-widget.iq-card .iq-navbar-header .profile-bg {
    height: 25%;
  }
}

.kivi-widget .widget-layout .sidebar-heading-text {
  font-size: 18px !important;
  font-weight: 500;
  font-family: "Heebo", sans-serif;
}

.kivi-widget .widget-layout .iq-table-border tbody th,
.kivi-widget .widget-layout .iq-table-border tbody td {
  border: unset !important;
}

.kivi-widget .widget-layout .container-fluid,
.kivi-widget .widget-layout .container-sm,
.kivi-widget .widget-layout .container-md,
.kivi-widget .widget-layout .container-lg,
.kivi-widget .widget-layout .container-xl {
  padding: unset !important;
}

.kivi-widget {
  min-width: 980px;
  width: 980px;
  height: 100%;
  margin: auto;
}

@media (max-width: 768px) {
  .kivi-widget {
    padding: 8px;
    min-width: unset;
    width: unset;
  }

  .kivi-widget .widget-layout .widget-tabs {
    height: unset;
  }
}

.kivi-widget .widget-layout .flatpickr-months .flatpickr-prev-month:hover svg,
.kivi-widget .widget-layout .flatpickr-months .flatpickr-next-month:hover svg {
  fill: var(--iq-primary-dark);
}

.kivi-widget .widget-layout .time-slots::-webkit-scrollbar {
  height: 0px;
}

.widget-pannel .iq-card .iq-tab-pannel .iq-preview-details table tr td h1,
.widget-pannel .iq-card .iq-tab-pannel .iq-preview-details table tr td h2,
.widget-pannel .iq-card .iq-tab-pannel .iq-preview-details table tr td h3,
.widget-pannel .iq-card .iq-tab-pannel .iq-preview-details table tr td h4,
.widget-pannel .iq-card .iq-tab-pannel .iq-preview-details table tr td h5,
.widget-pannel .iq-card .iq-tab-pannel .iq-preview-details table tr td h6 {
  word-break: initial;
}

.kivi-widget .widget-content .service-content .kc-service-name h5 {
  font-size: 20px;
  padding-bottom: 5px;
}

.kivi-widget .widget-content .service-content small {
  font-size: 16px;
  padding-bottom: 20px;
}

.kivi-widget .widget-content .service-content p {
  font-size: 14px;
  font-weight: 500;
}

#kivicare-animate-ul li {
  animation: fadeIn 0.5s linear;
  animation-fill-mode: both;
}

#kivicare-animate-ul li:nth-child(1) {
  animation-delay: 0.10s;
}

#kivicare-animate-ul li:nth-child(2) {
  animation-delay: 0.20s;
}

#kivicare-animate-ul li:nth-child(3) {
  animation-delay: 0.30s;
}

#kivicare-animate-ul li:nth-child(4) {
  animation-delay: 0.40s;
}

#kivicare-animate-ul li:nth-child(5) {
  animation-delay: 0.50s;
}

#kivicare-animate-ul li:nth-child(6) {
  animation-delay: 0.60s;
}

#kivicare-animate-ul li:nth-child(7) {
  animation-delay: 0.70s;
}

#kivicare_logout_btn {
  animation: fadeIn 0.5s linear;
  animation-fill-mode: both;
  animation-delay: 0.80s;
}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
    top: 100px;
  }

  75% {
    opacity: 0.5;
    top: 0px;
  }

  100% {
    opacity: 1;
  }
}

#kivicare-login .form-group {
  margin-bottom: 5px;
}

.kivicare-login-form-data a {
  font-size: 12px !important;
  margin-top: 5px !important;
}

.widget-content .iq-kivi-calendar-slot .flatpickr-day.today {
  background-color: #f0f4fd;
  border-color: var(--iq-primary);
}

.widget-pannel .tab-content #file-uploads-custom .widget-content .iq-kivicare-form-control {
  border: 0px;
  background-color: #f0f4fd !important;
}

.widget-content .card-list .iq-client-widget .iq-card .kc-clinic-address {
  padding-top: 5px !important;
}

.widget-content .card-list .iq-client-widget .iq-card .kc-clinic-name {
  padding-top: 20px !important;
}

.kivi-widget .widget-layout .kc-confirmation-hr {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

.kivi-widget .widget-layout .kc-check-email {
  margin-top: 10px !important;
}

.kivi-widget .widget-layout .kc-confirmation-buttons .iq-button {
  padding: 10px 32px !important;
}

.kivi-widget .widget-layout .iq-client-widget .kc-service-card {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.widget-content .iq-kivi-calendar-slot .flatpickr-day.today.selected:hover,
.widget-content .iq-kivi-calendar-slot .flatpickr-day.selected {
  color: var(--iq-white);
}

.widget-content .iq-kivi-calendar-slot .flatpickr-day.today.selected {
  color: var(--iq-black);
}

.widget-content #kivi_confirm_page .iq-card .kc-total-price h6 {
  font-weight: 700 !important;
}

.widget-content #login-register-panel .iq-kivicare-form-control {
  border: 0px !important;
}

.kivi-widget .widget-layout .iq-client-widget .service-content .iq-fancy-design {
  min-width: 168px;
}

.widget-content .iq-kivi-calendar-slot .flatpickr-day.today:hover,
.widget-content .iq-kivi-calendar-slot .flatpickr-day.today:focus {
  color: var(--iq-black);
}

@media (max-width: 767px) {
  .kivi-widget .widget-layout .iq-client-widget .service-content .iq-fancy-design {
    min-width: auto;
  }
}

/*===================================
animataion
=======================================*/

.kivi-widget .flatpickr-calendar {
  background: var(--iq-primary-light);
  box-shadow: none;
  -webkit-box-shadow: none;
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

.kivi-widget .flatpickr-day.flatpickr-disabled,
.kivi-widget .flatpickr-day.flatpickr-disabled:hover {
  color: var(--iq-secondary);
}

.kivi-widget .widget-layout .kc-confirmation-info-section {
  margin-bottom: 2rem;
}

.kivi-widget #kivicare-file-upload-form .card-list-data,
.kivi-widget #confirm .card-list-data {
  -webkit-animation: fade-in 1.0s ease-in-out both;
  animation: fade-in 1.0s ease-in-out both;
}

.kivi-widget .iq-kivi-calendar-slot .time-slots .iq-card {
  -webkit-animation: fade-in 1.8s ease-in-out both;
  animation: fade-in 1.8s ease-in-out both;
}

.kivi-widget .widget-layout .tab-item[data-check=false] .tab-link .sidebar-heading-text,
.kivi-widget .widget-layout .widget-tabs .tab-item[data-check=false] .tab-link p {
  color: #C6D6FF;
}

.kivi-widget .widget-layout .tab-item[data-check=true] .tab-link .sidebar-heading-text,
.kivi-widget .widget-layout .widget-tabs .tab-item[data-check=true] .tab-link p,
.kivi-widget .widget-layout .tab-item.active .tab-link .sidebar-heading-text,
.kivi-widget .widget-layout .widget-tabs .tab-item.active .tab-link p {
  color: var(--iq-white);
}

.widget-content .iq-kivi-calendar-slot .flatpickr-day:hover {
  font-size: 16px;
  background: var(--iq-white);
  border-color: transparent;
}

.kivi-widget .iq-kivi-tab-panel-title-animation {
  overflow: hidden;
}

.iq-kivi-tab-panel-title {
  -webkit-animation: fade-in-left 0.60s ease-in-out both;
  animation: fade-in-left 0.60s ease-in-out both;
  opacity: 0;
}

.kivi-widget .widget-layout .iq-client-widget .btn-border01 .iq-fancy-design,
.iq-fancy-design {
  -webkit-animation: fade-in 0.75s ease-in-out both;
  animation: fade-in 0.75s ease-in-out both;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
  opacity: 0;
}

.iq-fancy-design:hover {
  -moz-transform: translateY(-5px);
  -webkit-transform: translateY(-5px);
  -o-transform: translateY(-5px);
  -ms-transform: translateY(-5px);
  transform: translateY(-5px);
}

.kivi-widget .widget-layout .iq-calendar-card.d-grid {
  -webkit-animation: fade-in-bottom 0.60s ease-in-out both;
  animation: fade-in-bottom 0.60s ease-in-out both;
  opacity: 0;
}


/*===========
fade-in-right
===============*/

@-webkit-keyframes fade-in-left {
  0% {
    -webkit-transform: translateX(-50px);
    transform: translateX(-50px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-in-left {
  0% {
    -webkit-transform: translateX(-50px);
    transform: translateX(-50px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

/*===========
fade-in
===============*/
@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/*===========
fade-in-bottom
===============*/

@-webkit-keyframes fade-in-bottom {
  0% {
    -webkit-transform: translateY(50px);
    transform: translateY(50px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in-bottom {
  0% {
    -webkit-transform: translateY(50px);
    transform: translateY(50px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

.kivi-widget .widget-layout .widget-pannel button:focus,
.kivi-widget .widget-layout .widget-tabs button:focus,
.kivi-widget .widget-layout button:focus {
  outline: 0;
}

*,
*:before {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}



.double-lines-spinner {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  position: relative;
}

.double-lines-spinner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.05);
}

.double-lines-spinner::before {
  border-right: 2px solid var(--iq-primary);
  animation: spin 0.5s 0s linear infinite;
}


@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

.booknetic_service_card {
  width: 100%;
  height: 80px;
  background-color: #fff;
  border-radius: 2px;
  margin-bottom: 10px;
  cursor: pointer;
  -webkit-box-shadow: 0 0 30px 0 rgb(0 0 0 / 5%);
  -moz-box-shadow: 0 0 30px 0 rgba(0, 0, 0, .05);
  box-shadow: 0 30px 0 rgb(0 0 0 / 5%);
}

.booknetic_service_card_image {
  float: left;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 50px;
  height: 50px;
  overflow: hidden;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  margin: 15px 20px;
}

.booknetic_service_card_title {
  float: left;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 240px;
  height: 100%;
  text-align: left;
  line-height: 18px;
}

.booknetic_service_card_description {
  height: 100%;
  float: left;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #828f9a;
  font-size: 12px;
  line-height: 19px;
  max-width: 200px;
  overflow: hidden;
}

.booknetic_service_card_price {
  float: right;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 20px;
  font-weight: 300;
  text-align: right;
  color: #4fbf65;
  font-size: 26px;
}

/* @media (max-width: 480px){
  .kivi-widget .widget-layout .alert.alert-popup:not(.error){
      top: 80%;
      width: 96%;
  }
} */

.d-none {
  display: none !important;
}

.kivi-widget .widget-layout .time-slots .iq-card {
  overflow: hidden;
}

.mfp-fade.mfp-bg.mfp-ready {
  opacity: .5 !important;
}

.kivi-widget-popup {
  min-width: unset !important;
  width: unset !important;
  height: unset !important;
  ;
  margin: unset !important;

}

.mfp-content .kivi-widget {
  background: #e7ecf1;
  border-radius: 15px;
}

.kivi-position-relative {
  position: relative;
}

.kivi-widget-close {
  display: inline-block;
  background: #7093e5;
  color: #fff !important;
  border: 3px solid #e7ecf1;
  height: 35px;
  width: 35px;
  text-align: center;
  position: absolute;
  top: -30px;
  right: -30px;
  opacity: 1;
  padding: 0;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  z-index: 999;
  cursor: pointer;
}

.kivi-widget-close:focus {
  border-radius: 3px solid #e7ecf1;
  outline: none;
}

.white-popup .mfp-close,
.mfp-close-btn-in .mfp-close {
  display: none;
}

.atcb_list {
  position: fixed !important;
  width: 16em !important;
  left: 50% !important;
  top: 50% !important;
  transform: translateY(-50%) translateX(-50%) !important;
}

@media (max-width: 1024px) {
  .kivi-widget-close {
    top: -18px;
    right: -18px;
  }

  .white-popup {
    padding: 30px 16px;
  }
}

#serviceLists [type="checkbox"]:checked.selected-service+label:before,
#serviceLists [type="checkbox"]:not(:checked).selected-service+label:before {
  display: none;
}

#serviceLists [type="checkbox"]:checked.selected-service+label,
#serviceLists [type="checkbox"]:not(:checked).selected-service+label {
  padding-left: 0;
}


.kivi-stars span.checked::before {
  color: var(--iq-secondary);
}

.kivi-stars span::before {
  color: var(--iq-body-color);
}

.kivi-star {
  font-size: 16px;
}


.kivi-star[data-star] {
  text-align: left;
  font-style: normal;
  display: inline-block;
  position: relative;
  unicode-bidi: bidi-override;
}

.kivi-star[data-star]::before {
  display: block;
  content: '★★★★★';
  color: var(--iq-body-color);
}

.kivi-star[data-star]::after {
  white-space: nowrap;
  position: absolute;
  top: 0;
  left: 0;
  content: '★★★★★';
  width: 0;
  color: var(--iq-secondary);
  overflow: hidden;
  height: 100%;
}

.kivi-star[data-star^="0.2"]::after,
[data-star^=".2"]::after {
  width: 4%
}

.kivi-star[data-star^="0.3"]::after,
[data-star^=".3"]::after {
  width: 6%
}

.kivi-star[data-star^="0.1"]::after,
[data-star^=".1"]::after {
  width: 2%
}

.kivi-star[data-star^="0.4"]::after,
[data-star^=".4"]::after {
  width: 8%
}

.kivi-star[data-star^="0.5"]::after,
[data-star^=".5"]::after {
  width: 10%
}

.kivi-star[data-star^="0.6"]::after,
[data-star^=".6"]::after {
  width: 12%
}

.kivi-star[data-star^="0.7"]::after,
[data-star^=".7"]::after {
  width: 14%
}

.kivi-star[data-star^="0.8"]::after,
[data-star^=".8"]::after {
  width: 16%
}

.kivi-star[data-star^="0.9"]::after,
[data-star^=".9"]::after {
  width: 18%
}

.kivi-star[data-star^="1"]::after {
  width: 20%
}

.kivi-star[data-star^="1.1"]::after {
  width: 22%
}

.kivi-star[data-star^="1.2"]::after {
  width: 24%
}

.kivi-star[data-star^="1.3"]::after {
  width: 26%
}

.kivi-star[data-star^="1.4"]::after {
  width: 28%
}

.kivi-star[data-star^="1.5"]::after {
  width: 30%
}

.kivi-star[data-star^="1.6"]::after {
  width: 32%
}

.kivi-star[data-star^="1.7"]::after {
  width: 34%
}

.kivi-star[data-star^="1.8"]::after {
  width: 36%
}

.kivi-star[data-star^="1.9"]::after {
  width: 38%
}

.kivi-star[data-star^="2"]::after {
  width: 40%
}

.kivi-star[data-star^="2.1"]::after {
  width: 42%
}

.kivi-star[data-star^="2.2"]::after {
  width: 44%
}

.kivi-star[data-star^="2.3"]::after {
  width: 46%
}

.kivi-star[data-star^="2.4"]::after {
  width: 48%
}

.kivi-star[data-star^="2.5"]::after {
  width: 50%
}

.kivi-star[data-star^="2.6"]::after {
  width: 52%
}

.kivi-star[data-star^="2.7"]::after {
  width: 54%
}

.kivi-star[data-star^="2.8"]::after {
  width: 56%
}

.kivi-star[data-star^="2.9"]::after {
  width: 58%
}

.kivi-star[data-star^="3"]::after {
  width: 60%
}

.kivi-star[data-star^="3.1"]::after {
  width: 62%
}

.kivi-star[data-star^="3.2"]::after {
  width: 64%
}

.kivi-star[data-star^="3.3"]::after {
  width: 66%
}

.kivi-star[data-star^="3.4"]::after {
  width: 68%
}

.kivi-star[data-star^="3.5"]::after {
  width: 70%
}

.kivi-star[data-star^="3.6"]::after {
  width: 72%
}

.kivi-star[data-star^="3.7"]::after {
  width: 74%
}

.kivi-star[data-star^="3.8"]::after {
  width: 76%
}

.kivi-star[data-star^="3.9"]::after {
  width: 78%
}

.kivi-star[data-star^="4"]::after {
  width: 80%
}

.kivi-star[data-star^="4.1"]::after {
  width: 82%
}

.kivi-star[data-star^="4.2"]::after {
  width: 84%
}

.kivi-star[data-star^="4.3"]::after {
  width: 86%
}

.kivi-star[data-star^="4.4"]::after {
  width: 88%
}

.kivi-star[data-star^="4.5"]::after {
  width: 90%
}

.kivi-star[data-star^="4.6"]::after {
  width: 92%
}

.kivi-star[data-star^="4.7"]::after {
  width: 94%
}

.kivi-star[data-star^="4.8"]::after {
  width: 96%
}

.kivi-star[data-star^="4.9"]::after {
  width: 98%
}

.kivi-star[data-star^="5"]::after {
  width: 100%
}


/* rtl */
.kivi-widget [dir="rtl"] .widget-layout .widget-tabs .tab-list {
  padding-right: 0 !important;
}

.kivi-widget [dir="rtl"] .widget-layout .widget-tabs li.tab-item {
  margin: 8px 48px 0 0;
  text-align: right;
}

.kivi-widget [dir="rtl"] .widget-layout .widget-tabs .tab-item::before {
  right: -47px;
  left: auto;
}

.kivi-widget [dir="rtl"] .widget-layout .widget-tabs .tab-item:not(:last-child):after {
  right: -35px;
  left: auto;
}

.kivi-widget [dir="rtl"] .widget-layout .widget-pannel .iq-kivi-search .iq-kivicare-form-control {
  padding-right: 40px;
  padding-left: 16px;
}

.kivi-widget [dir="rtl"] .widget-layout .widget-pannel .iq-kivi-search .iq-kivi-icon {
  right: 1em;
  left: auto;
}

.kivi-widget [dir="rtl"] .widget-layout .card-widget-footer {
  left: 5%;
  right: auto;
}

.kivi-widget [dir="rtl"] .widget-layout .card-widget-footer .d-flex {
  margin-right: auto;
  margin-left: 0 !important;
}

.kivi-widget [dir="rtl"] .widget-layout .iq-doctor-badge::before {
  left: 8px;
  right: auto;
  margin-right: -50%;
  margin-left: unset;
}

.kivi-widget [dir="rtl"] .widget-layout .iq-doctor-badge::after {
  right: 8px;
  left: auto;
  margin-left: -50%;
  margin-right: unset;
}

.kivi-widget [dir="rtl"] .widget-layout h5.iq-letter-spacing-1 {
  text-align: right;
}

[dir="rtl"] #appointment-descriptions .form-group,
[dir="rtl"] #kivicare-register-form .form-group,
[dir="rtl"] #login .form-group {
  text-align: right;
}


.wp-block-kivi-care-book-appointment-widget [dir="rtl"] #confirm_detail_form,
.wp-block-kivi-care-book-appointment-widget [dir="rtl"] #payment_mode_form
 {
  text-align: right;
}

@media (max-width: 1024px) {
  .kivi-widget [dir="rtl"] .widget-layout .widget-tabs li.tab-item {
    margin: 8px 0 0 0;
  }

  .kivi-widget [dir="rtl"] .widget-layout .widget-tabs .tab-item::before {
    right: 0;
    left: auto;
  }

  .kivi-widget [dir="rtl"] .widget-layout .widget-tabs .tab-item:not(:last-child):after {
    right: 22px;
    left: auto;
  }
}

@media (max-width: 576px) {
  .kivi-widget [dir="rtl"] .widget-layout .card-widget-footer .d-flex {
    margin: auto !important;
  }

}

.pl-1 {
  padding-left: 0 !important;
  padding-right: .25em !important;
}

.contact-box-inline {
  display: flex;
  gap: 1em;
}

.contact-box-inline .select2-container--default .select2-selection--single {
  background-color: #fff !important;
  color: #6E7990;
  border: 1px solid #eee;
  border-radius: 5px;
  padding: 12px 16px;
  height: auto;
}

.contact-box-inline .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #6E7990;
}

.contact-box-inline .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 100%;
}

.select2-container--open .select2-dropdown--below:not(.kivicare-custom-dropdown-width), .select2-container--open .select2-dropdown--above:not(.kivicare-custom-dropdown-width) {
width: var(--kc-country-code-width) !important;
}

.multiselect .select2-container--default {
  display: block;
}

.kivi-widget .select2-container--default .select2-selection--single .select2-selection__arrow:before {
  opacity: 0;
}

.kivi-widget .widget-layout #kivicare_error_msg_login_register {
  position: absolute;
  bottom: 60px;
  margin: 0 auto;
  text-align: center;
  left: 0;
  right: 0;
}

.kivi-widget .widget-layout .widget-pannel .kivicare-register-form-data .iq-kivicare-form-control.nice-select{
  display: none;
}

[dir=rtl] input, [dir=rtl] textarea,[dir=rtl] [type=email], [dir=rtl] [type=number], [dir=rtl] [type=tel], [dir=rtl] [type=url] {
  direction: ltr;
  text-align: right;
}

[dir=rtl] input, [dir=rtl] textarea,[dir=rtl] [type=email], [dir=rtl] [type=number], [dir=rtl] [type=tel], [dir=rtl] [type=url] {
  direction: ltr;
  text-align: right;
}

input, input[type=color], input[type=date], input[type=datetime-local], input[type=datetime], input[type=email], input[type=month], input[type=number], input[type=password], input[type=range], input[type=search], input[type=tel], input[type=text], input[type=time], input[type=url], input[type=week], textarea{
  float: none;
}

[dir="rtl"] .password-toggle{
  right: auto !important;
  left: 10px;
}

.flatpickr-days .flatpickr-day{
  max-width: 100% !important;
}

.object-cover{
  object-fit: cover;
}

.contact-box-inline .select2-container--default .select2-selection--single,
.select2-container .select2-selection--single{
  height: auto !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered{
  line-height: normal !important;
}
