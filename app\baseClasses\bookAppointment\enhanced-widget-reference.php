<?php
/**
 * Enhanced Booking Widget Reference Implementation
 * 
 * This file serves as a reference for implementing an improved booking widget
 * with the flow: Clinic → Category → Services → Date/Time → Patient Details → Confirmation
 */
?>
<style>
  :root {
    --primary-color: #4F46E5;
    --primary-color-hover: #4338CA;
    --secondary-color: #10B981;
    --secondary-color-hover: #059669;
    --danger-color: #EF4444;
    --danger-color-hover: #DC2626;
    --light-gray: #F3F4F6;
    --gray: #6B7280;
    --dark-gray: #374151;
    --black: #111827;
    --white: #FFFFFF;
    --radius: 0.5rem;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  }

  .kivi-booking-widget {
    font-family: var(--font-family);
    color: var(--dark-gray);
    max-width: 1024px;
    margin: 0 auto;
  }

  .kivi-booking-widget * {
    box-sizing: border-box;
  }

  .kivi-booking-container {
    background-color: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
  }

  .kivi-booking-header {
    background-color: var(--light-gray);
    border-bottom: 1px solid rgba(229, 231, 235, 1);
  }

  .kivi-step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    position: relative;
  }

  .kivi-step-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--light-gray);
    transform: translateY(-50%);
    z-index: 1;
  }

  .kivi-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
  }

  .kivi-step-dot {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: var(--light-gray);
    border: 2px solid var(--gray);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray);
    transition: all 0.3s ease;
  }

  .kivi-step.active .kivi-step-dot {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
  }

  .kivi-step.completed .kivi-step-dot {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
  }

  .kivi-step-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--gray);
    text-align: center;
    max-width: 100px;
  }

  .kivi-step.active .kivi-step-label,
  .kivi-step.completed .kivi-step-label {
    color: var(--black);
    font-weight: 600;
  }

  .kivi-booking-body {
    padding: 1.5rem;
  }

  .kivi-booking-step {
    display: none;
  }

  .kivi-booking-step.active {
    display: block;
  }

  .kivi-step-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--black);
  }

  .kivi-step-subtitle {
    font-size: 0.875rem;
    color: var(--gray);
    margin-bottom: 1.5rem;
  }

  .kivi-form-group {
    margin-bottom: 1rem;
  }

  .kivi-form-label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
  }

  .kivi-form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(229, 231, 235, 1);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out;
  }

  .kivi-form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }

  .kivi-search-input {
    position: relative;
  }

  .kivi-search-input .kivi-search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
  }

  .kivi-search-input input {
    padding-left: 2.5rem;
  }

  .kivi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }

  .kivi-card {
    border: 2px solid rgba(229, 231, 235, 1);
    border-radius: var(--radius);
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
  }

  .kivi-card:hover {
    border-color: rgba(79, 70, 229, 0.4);
    background-color: rgba(79, 70, 229, 0.02);
  }

  .kivi-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(79, 70, 229, 0.05);
  }

  .kivi-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
  }

  .kivi-card-title {
    font-weight: 600;
    color: var(--black);
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }

  .kivi-card-subtitle {
    font-size: 0.75rem;
    color: var(--gray);
  }

  .kivi-card-price {
    font-weight: 600;
    color: var(--primary-color);
  }

  .kivi-card-body {
    font-size: 0.875rem;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
  }

  .kivi-card-footer {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .kivi-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .kivi-badge-blue {
    background-color: rgba(59, 130, 246, 0.1);
    color: rgba(29, 78, 216, 1);
  }

  .kivi-badge-green {
    background-color: rgba(16, 185, 129, 0.1);
    color: rgba(5, 150, 105, 1);
  }

  .kivi-badge-purple {
    background-color: rgba(124, 58, 237, 0.1);
    color: rgba(109, 40, 217, 1);
  }

  .kivi-date-picker {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .kivi-calendar {
    border: 1px solid rgba(229, 231, 235, 1);
    border-radius: var(--radius);
    padding: 1rem;
  }

  .kivi-time-slots {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .kivi-time-slot {
    padding: 0.5rem 0.75rem;
    border: 1px solid rgba(229, 231, 235, 1);
    border-radius: var(--radius);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .kivi-time-slot:hover {
    border-color: var(--primary-color);
    background-color: rgba(79, 70, 229, 0.05);
  }

  .kivi-time-slot.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: var(--white);
  }

  .kivi-booking-footer {
    display: flex;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(229, 231, 235, 1);
    background-color: var(--light-gray);
  }

  .kivi-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .kivi-btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
  }

  .kivi-btn-primary:hover {
    background-color: var(--primary-color-hover);
  }

  .kivi-btn-secondary {
    background-color: var(--white);
    color: var(--dark-gray);
    border: 1px solid rgba(229, 231, 235, 1);
  }

  .kivi-btn-secondary:hover {
    background-color: var(--light-gray);
  }

  .kivi-btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .kivi-booking-summary {
    background-color: var(--light-gray);
    border-radius: var(--radius);
    padding: 1rem;
    margin-top: 1rem;
  }

  .kivi-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .kivi-summary-label {
    font-weight: 500;
    color: var(--dark-gray);
  }

  .kivi-summary-value {
    font-weight: 600;
    color: var(--black);
  }

  .kivi-summary-total {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(229, 231, 235, 1);
    font-weight: 600;
    font-size: 1rem;
  }

  .kivi-loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 0;
  }

  .kivi-loader {
    border: 3px solid rgba(229, 231, 235, 1);
    border-radius: 50%;
    border-top: 3px solid var(--primary-color);
    width: 2rem;
    height: 2rem;
    animation: kivi-spin 1s linear infinite;
  }

  @keyframes kivi-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .kivi-booking-container {
      border-radius: 0;
    }

    .kivi-step-label {
      display: none;
    }

    .kivi-grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<div class="kivi-booking-widget">
  <div class="kivi-booking-container">
    <!-- Booking Widget Header with Step Indicator -->
    <div class="kivi-booking-header">
      <div class="kivi-step-indicator">
        <div class="kivi-step active" data-step="clinic">
          <div class="kivi-step-dot">1</div>
          <div class="kivi-step-label">Select Clinic</div>
        </div>
        <div class="kivi-step" data-step="category">
          <div class="kivi-step-dot">2</div>
          <div class="kivi-step-label">Select Category</div>
        </div>
        <div class="kivi-step" data-step="services">
          <div class="kivi-step-dot">3</div>
          <div class="kivi-step-label">Select Services</div>
        </div>
        <div class="kivi-step" data-step="datetime">
          <div class="kivi-step-dot">4</div>
          <div class="kivi-step-label">Date & Time</div>
        </div>
        <div class="kivi-step" data-step="details">
          <div class="kivi-step-dot">5</div>
          <div class="kivi-step-label">Patient Details</div>
        </div>
        <div class="kivi-step" data-step="confirm">
          <div class="kivi-step-dot">6</div>
          <div class="kivi-step-label">Confirmation</div>
        </div>
      </div>
    </div>

    <!-- Booking Widget Body -->
    <div class="kivi-booking-body">
      <!-- Step 1: Clinic Selection -->
      <div class="kivi-booking-step active" id="step-clinic">
        <h2 class="kivi-step-title">Select a Clinic</h2>
        <p class="kivi-step-subtitle">Choose a clinic for your appointment.</p>
        
        <div class="kivi-form-group kivi-search-input">
          <div class="kivi-search-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input type="text" class="kivi-form-input" id="clinic-search" placeholder="Search clinics...">
        </div>
        
        <div class="kivi-grid" id="clinic-list">
          <!-- Clinics will be loaded here dynamically -->
          <!-- Example Clinic Card -->
          <div class="kivi-card clinic-card" data-clinic-id="1">
            <div class="kivi-card-header">
              <div>
                <h3 class="kivi-card-title">Main Street Clinic</h3>
                <div class="kivi-card-subtitle">123 Main St, City</div>
              </div>
            </div>
            <div class="kivi-card-body">
              <p>Full-service medical clinic with multiple specialties.</p>
            </div>
            <div class="kivi-card-footer">
              <span class="kivi-badge kivi-badge-blue">5 Doctors</span>
              <span class="kivi-badge kivi-badge-green">20+ Services</span>
            </div>
          </div>
          <!-- End Example Clinic Card -->
        </div>
      </div>

      <!-- Step 2: Category Selection -->
      <div class="kivi-booking-step" id="step-category">
        <h2 class="kivi-step-title">Select a Service Category</h2>
        <p class="kivi-step-subtitle">Choose a category to see available services.</p>
        
        <div class="kivi-form-group kivi-search-input">
          <div class="kivi-search-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input type="text" class="kivi-form-input" id="category-search" placeholder="Search categories...">
        </div>
        
        <div class="kivi-grid" id="category-list">
          <!-- Service Categories will be loaded here dynamically -->
          <!-- Example Category Card -->
          <div class="kivi-card category-card" data-category-id="general_service">
            <div class="kivi-card-header">
              <div>
                <h3 class="kivi-card-title">General Services</h3>
                <div class="kivi-card-subtitle">6 services available</div>
              </div>
            </div>
            <div class="kivi-card-footer">
              <span class="kivi-badge kivi-badge-purple">In-clinic</span>
              <span class="kivi-badge kivi-badge-green">Virtual</span>
            </div>
          </div>
          <!-- End Example Category Card -->
        </div>
      </div>

      <!-- Step 3: Service Selection -->
      <div class="kivi-booking-step" id="step-services">
        <h2 class="kivi-step-title">Select Services</h2>
        <p class="kivi-step-subtitle">Choose one or more services for your appointment.</p>
        
        <div class="kivi-form-group">
          <div class="selected-category-info">
            <div class="kivi-form-label">Selected Category: <span id="selected-category-name">General Services</span></div>
            <button type="button" class="kivi-btn kivi-btn-secondary" id="change-category-btn">Change</button>
          </div>
        </div>
        
        <div class="kivi-form-group kivi-search-input">
          <div class="kivi-search-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input type="text" class="kivi-form-input" id="service-search" placeholder="Search services...">
        </div>
        
        <div class="kivi-form-group">
          <div class="kivi-form-label">Filter By:</div>
          <div class="kivi-filter-buttons">
            <button type="button" class="kivi-btn kivi-btn-secondary active" data-filter="all">All</button>
            <button type="button" class="kivi-btn kivi-btn-secondary" data-filter="virtual">Virtual</button>
            <button type="button" class="kivi-btn kivi-btn-secondary" data-filter="clinic">In-Clinic</button>
          </div>
        </div>
        
        <div class="kivi-grid" id="service-list">
          <!-- Services will be loaded here dynamically -->
          <!-- Example Service Card -->
          <div class="kivi-card service-card" data-service-id="1" data-service-type="clinic">
            <div class="kivi-card-header">
              <div>
                <h3 class="kivi-card-title">General Consultation</h3>
                <div class="kivi-card-subtitle">Dr. John Smith</div>
              </div>
              <div class="kivi-card-price">$100</div>
            </div>
            <div class="kivi-card-body">
              <p>Initial consultation to assess your health concerns.</p>
            </div>
            <div class="kivi-card-footer">
              <span class="kivi-badge kivi-badge-blue">30 min</span>
              <span class="kivi-badge kivi-badge-green">In-Clinic</span>
            </div>
          </div>
          <!-- End Example Service Card -->
        </div>
      </div>

      <!-- Step 4: Date and Time Selection -->
      <div class="kivi-booking-step" id="step-datetime">
        <h2 class="kivi-step-title">Select Date and Time</h2>
        <p class="kivi-step-subtitle">Choose when you'd like to schedule your appointment.</p>
        
        <div class="kivi-date-picker">
          <div class="kivi-calendar" id="appointment-calendar">
            <!-- Calendar will be rendered here -->
          </div>
          
          <div class="kivi-form-group">
            <label class="kivi-form-label">Available Time Slots</label>
            <div class="kivi-time-slots" id="time-slot-list">
              <!-- Time slots will be loaded here dynamically -->
              <div class="kivi-time-slot" data-time="09:00">9:00 AM</div>
              <div class="kivi-time-slot" data-time="09:30">9:30 AM</div>
              <div class="kivi-time-slot" data-time="10:00">10:00 AM</div>
              <div class="kivi-time-slot" data-time="10:30">10:30 AM</div>
              <div class="kivi-time-slot" data-time="11:00">11:00 AM</div>
              <div class="kivi-time-slot" data-time="11:30">11:30 AM</div>
              <div class="kivi-time-slot" data-time="13:00">1:00 PM</div>
              <div class="kivi-time-slot" data-time="13:30">1:30 PM</div>
              <div class="kivi-time-slot" data-time="14:00">2:00 PM</div>
              <div class="kivi-time-slot" data-time="14:30">2:30 PM</div>
              <div class="kivi-time-slot" data-time="15:00">3:00 PM</div>
              <div class="kivi-time-slot" data-time="15:30">3:30 PM</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 5: Patient Details -->
      <div class="kivi-booking-step" id="step-details">
        <h2 class="kivi-step-title">Patient Details</h2>
        <p class="kivi-step-subtitle">Please provide your information for the appointment.</p>
        
        <div class="kivi-form-group">
          <label class="kivi-form-label" for="patient-name">Full Name</label>
          <input type="text" class="kivi-form-input" id="patient-name" placeholder="Your full name">
        </div>
        
        <div class="kivi-form-group">
          <label class="kivi-form-label" for="patient-email">Email</label>
          <input type="email" class="kivi-form-input" id="patient-email" placeholder="Your email address">
        </div>
        
        <div class="kivi-form-group">
          <label class="kivi-form-label" for="patient-phone">Phone Number</label>
          <input type="tel" class="kivi-form-input" id="patient-phone" placeholder="Your phone number">
        </div>
        
        <div class="kivi-form-group">
          <label class="kivi-form-label" for="patient-notes">Additional Notes</label>
          <textarea class="kivi-form-input" id="patient-notes" rows="3" placeholder="Any additional information you'd like to provide"></textarea>
        </div>
      </div>

      <!-- Step 6: Confirmation -->
      <div class="kivi-booking-step" id="step-confirm">
        <h2 class="kivi-step-title">Confirm Your Appointment</h2>
        <p class="kivi-step-subtitle">Please review your appointment details before confirming.</p>
        
        <div class="kivi-booking-summary">
          <div class="kivi-summary-item">
            <div class="kivi-summary-label">Clinic:</div>
            <div class="kivi-summary-value" id="summary-clinic">Main Street Clinic</div>
          </div>
          
          <div class="kivi-summary-item">
            <div class="kivi-summary-label">Service(s):</div>
            <div class="kivi-summary-value" id="summary-services">General Consultation</div>
          </div>
          
          <div class="kivi-summary-item">
            <div class="kivi-summary-label">Date:</div>
            <div class="kivi-summary-value" id="summary-date">June 15, 2023</div>
          </div>
          
          <div class="kivi-summary-item">
            <div class="kivi-summary-label">Time:</div>
            <div class="kivi-summary-value" id="summary-time">10:00 AM</div>
          </div>
          
          <div class="kivi-summary-item">
            <div class="kivi-summary-label">Patient:</div>
            <div class="kivi-summary-value" id="summary-patient">John Doe</div>
          </div>
          
          <div class="kivi-summary-item kivi-summary-total">
            <div class="kivi-summary-label">Total:</div>
            <div class="kivi-summary-value" id="summary-total">$100.00</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Booking Widget Footer -->
    <div class="kivi-booking-footer">
      <button type="button" class="kivi-btn kivi-btn-secondary" id="prev-step-btn">Previous</button>
      <button type="button" class="kivi-btn kivi-btn-primary" id="next-step-btn">Next</button>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize booking widget
    const bookingWidget = {
      currentStep: 1,
      totalSteps: 6,
      stepNames: ['clinic', 'category', 'services', 'datetime', 'details', 'confirm'],
      selectedClinic: null,
      selectedCategory: null,
      selectedServices: [],
      selectedDate: null,
      selectedTime: null,
      patientData: {},
      
      init: function() {
        this.bindEvents();
        this.setupSearch();
        this.updateButtonStates();
      },
      
      bindEvents: function() {
        const self = this;
        
        // Next button click
        document.getElementById('next-step-btn').addEventListener('click', function() {
          self.nextStep();
        });
        
        // Previous button click
        document.getElementById('prev-step-btn').addEventListener('click', function() {
          self.prevStep();
        });
        
        // Clinic selection
        document.querySelectorAll('.clinic-card').forEach(function(card) {
          card.addEventListener('click', function() {
            self.selectClinic(this);
          });
        });
        
        // Category selection
        document.querySelectorAll('.category-card').forEach(function(card) {
          card.addEventListener('click', function() {
            self.selectCategory(this);
          });
        });
        
        // Service selection
        document.querySelectorAll('.service-card').forEach(function(card) {
          card.addEventListener('click', function() {
            self.toggleService(this);
          });
        });
        
        // Time slot selection
        document.querySelectorAll('.kivi-time-slot').forEach(function(slot) {
          slot.addEventListener('click', function() {
            self.selectTimeSlot(this);
          });
        });
        
        // Change category button
        document.getElementById('change-category-btn').addEventListener('click', function() {
          self.goToStep(2);
        });
        
        // Service filters
        document.querySelectorAll('.kivi-filter-buttons button').forEach(function(button) {
          button.addEventListener('click', function() {
            self.filterServices(this.dataset.filter);
            
            // Toggle active class
            document.querySelectorAll('.kivi-filter-buttons button').forEach(btn => {
              btn.classList.remove('active');
            });
            this.classList.add('active');
          });
        });
      },
      
      setupSearch: function() {
        const self = this;
        
        // Clinic search
        document.getElementById('clinic-search').addEventListener('input', function() {
          const searchTerm = this.value.toLowerCase();
          document.querySelectorAll('.clinic-card').forEach(function(card) {
            const clinicName = card.querySelector('.kivi-card-title').textContent.toLowerCase();
            const clinicAddress = card.querySelector('.kivi-card-subtitle').textContent.toLowerCase();
            
            if (clinicName.includes(searchTerm) || clinicAddress.includes(searchTerm)) {
              card.style.display = 'block';
            } else {
              card.style.display = 'none';
            }
          });
        });
        
        // Category search
        document.getElementById('category-search').addEventListener('input', function() {
          const searchTerm = this.value.toLowerCase();
          document.querySelectorAll('.category-card').forEach(function(card) {
            const categoryName = card.querySelector('.kivi-card-title').textContent.toLowerCase();
            
            if (categoryName.includes(searchTerm)) {
              card.style.display = 'block';
            } else {
              card.style.display = 'none';
            }
          });
        });
        
        // Service search
        document.getElementById('service-search').addEventListener('input', function() {
          const searchTerm = this.value.toLowerCase();
          document.querySelectorAll('.service-card').forEach(function(card) {
            const serviceName = card.querySelector('.kivi-card-title').textContent.toLowerCase();
            const serviceDescription = card.querySelector('.kivi-card-body').textContent.toLowerCase();
            
            if (serviceName.includes(searchTerm) || serviceDescription.includes(searchTerm)) {
              card.style.display = 'block';
            } else {
              card.style.display = 'none';
            }
          });
        });
      },
      
      nextStep: function() {
        if (this.validateCurrentStep()) {
          if (this.currentStep < this.totalSteps) {
            this.goToStep(this.currentStep + 1);
          }
        }
      },
      
      prevStep: function() {
        if (this.currentStep > 1) {
          this.goToStep(this.currentStep - 1);
        }
      },
      
      goToStep: function(stepNumber) {
        // Hide all steps
        document.querySelectorAll('.kivi-booking-step').forEach(function(step) {
          step.classList.remove('active');
        });
        
        // Show the current step
        document.getElementById(`step-${this.stepNames[stepNumber - 1]}`).classList.add('active');
        
        // Update step indicators
        document.querySelectorAll('.kivi-step').forEach(function(step, index) {
          if (index + 1 < stepNumber) {
            step.classList.add('completed');
            step.classList.remove('active');
          } else if (index + 1 === stepNumber) {
            step.classList.add('active');
            step.classList.remove('completed');
          } else {
            step.classList.remove('active', 'completed');
          }
        });
        
        this.currentStep = stepNumber;
        this.updateButtonStates();
        
        // Special handling for confirmation step
        if (stepNumber === 6) {
          this.updateSummary();
        }
      },
      
      updateButtonStates: function() {
        const prevBtn = document.getElementById('prev-step-btn');
        const nextBtn = document.getElementById('next-step-btn');
        
        // Update Previous button
        if (this.currentStep === 1) {
          prevBtn.classList.add('kivi-btn-disabled');
        } else {
          prevBtn.classList.remove('kivi-btn-disabled');
        }
        
        // Update Next button
        if (this.currentStep === this.totalSteps) {
          nextBtn.textContent = 'Confirm Appointment';
        } else {
          nextBtn.textContent = 'Next';
        }
        
        // Disable Next button if current step isn't valid
        if (!this.isCurrentStepValid()) {
          nextBtn.classList.add('kivi-btn-disabled');
        } else {
          nextBtn.classList.remove('kivi-btn-disabled');
        }
      },
      
      validateCurrentStep: function() {
        switch (this.currentStep) {
          case 1: // Clinic
            return this.selectedClinic !== null;
          case 2: // Category
            return this.selectedCategory !== null;
          case 3: // Services
            return this.selectedServices.length > 0;
          case 4: // Date & Time
            return this.selectedDate !== null && this.selectedTime !== null;
          case 5: // Patient Details
            return this.validatePatientData();
          case 6: // Confirmation
            return true;
          default:
            return false;
        }
      },
      
      isCurrentStepValid: function() {
        return this.validateCurrentStep();
      },
      
      validatePatientData: function() {
        const name = document.getElementById('patient-name').value;
        const email = document.getElementById('patient-email').value;
        const phone = document.getElementById('patient-phone').value;
        
        return name !== '' && email !== '' && phone !== '';
      },
      
      selectClinic: function(clinicCard) {
        // Remove selected class from all clinic cards
        document.querySelectorAll('.clinic-card').forEach(function(card) {
          card.classList.remove('selected');
        });
        
        // Add selected class to the clicked clinic card
        clinicCard.classList.add('selected');
        
        // Update selected clinic
        this.selectedClinic = {
          id: clinicCard.dataset.clinicId,
          name: clinicCard.querySelector('.kivi-card-title').textContent
        };
        
        this.updateButtonStates();
      },
      
      selectCategory: function(categoryCard) {
        // Remove selected class from all category cards
        document.querySelectorAll('.category-card').forEach(function(card) {
          card.classList.remove('selected');
        });
        
        // Add selected class to the clicked category card
        categoryCard.classList.add('selected');
        
        // Update selected category
        this.selectedCategory = {
          id: categoryCard.dataset.categoryId,
          name: categoryCard.querySelector('.kivi-card-title').textContent
        };
        
        // Update the category name in the services step
        document.getElementById('selected-category-name').textContent = this.selectedCategory.name;
        
        this.updateButtonStates();
      },
      
      toggleService: function(serviceCard) {
        // Toggle selected class
        serviceCard.classList.toggle('selected');
        
        const serviceId = serviceCard.dataset.serviceId;
        const serviceName = serviceCard.querySelector('.kivi-card-title').textContent;
        const servicePrice = serviceCard.querySelector('.kivi-card-price').textContent;
        
        // Check if service is already selected
        const serviceIndex = this.selectedServices.findIndex(service => service.id === serviceId);
        
        if (serviceIndex === -1) {
          // Add service to selected services
          this.selectedServices.push({
            id: serviceId,
            name: serviceName,
            price: servicePrice,
            type: serviceCard.dataset.serviceType
          });
        } else {
          // Remove service from selected services
          this.selectedServices.splice(serviceIndex, 1);
        }
        
        this.updateButtonStates();
      },
      
      selectTimeSlot: function(timeSlot) {
        // Remove selected class from all time slots
        document.querySelectorAll('.kivi-time-slot').forEach(function(slot) {
          slot.classList.remove('selected');
        });
        
        // Add selected class to the clicked time slot
        timeSlot.classList.add('selected');
        
        // Update selected time
        this.selectedTime = timeSlot.dataset.time;
        
        this.updateButtonStates();
      },
      
      filterServices: function(filterType) {
        document.querySelectorAll('.service-card').forEach(function(card) {
          if (filterType === 'all' || card.dataset.serviceType === filterType) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      },
      
      updateSummary: function() {
        // Update clinic
        document.getElementById('summary-clinic').textContent = this.selectedClinic.name;
        
        // Update services
        const serviceNames = this.selectedServices.map(service => service.name).join(', ');
        document.getElementById('summary-services').textContent = serviceNames;
        
        // Update date
        document.getElementById('summary-date').textContent = this.selectedDate || 'June 15, 2023'; // Placeholder date
        
        // Update time
        document.getElementById('summary-time').textContent = this.selectedTime || '10:00 AM'; // Placeholder time
        
        // Update patient
        const patientName = document.getElementById('patient-name').value;
        document.getElementById('summary-patient').textContent = patientName;
        
        // Update total
        let total = 0;
        this.selectedServices.forEach(service => {
          const price = parseFloat(service.price.replace(/[^0-9.-]+/g, ''));
          total += price;
        });
        document.getElementById('summary-total').textContent = `$${total.toFixed(2)}`;
      }
    };
    
    // Initialize the booking widget
    bookingWidget.init();
  });
</script>