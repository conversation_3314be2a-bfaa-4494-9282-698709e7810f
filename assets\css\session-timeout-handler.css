/**
 * Kivicare Session Timeout Handler Styles
 */

/* Session expired notification */
#kivicare-session-expired {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
    padding: 15px 20px !important;
    text-align: center !important;
    z-index: 999999 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
    border-bottom: 3px solid #a71e2a !important;
    animation: slideDown 0.3s ease-out !important;
}

/* Animation for notification appearance */
@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Container for notification content */
#kivicare-session-expired .notification-content {
    max-width: 800px !important;
    margin: 0 auto !important;
}

/* Title styling */
#kivicare-session-expired strong {
    font-size: 16px !important;
    font-weight: 600 !important;
    display: inline-block !important;
    margin-bottom: 8px !important;
}

/* Message text */
#kivicare-session-expired p {
    margin: 8px 0 !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    opacity: 0.95 !important;
}

/* Refresh button */
#kivicare-refresh-page {
    margin: 5px 10px !important;
    background: white !important;
    color: #dc3545 !important;
    border: none !important;
    padding: 10px 20px !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    font-weight: bold !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

#kivicare-refresh-page:hover {
    background: #f8f9fa !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

#kivicare-refresh-page:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Countdown text */
#kivicare-session-expired .countdown-text {
    font-size: 12px !important;
    opacity: 0.9 !important;
    margin-top: 5px !important;
    display: inline-block !important;
}

#kivicare-countdown {
    font-weight: bold !important;
    color: #fff3cd !important;
}

/* Loading state */
#kivicare-session-expired.loading {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
}

#kivicare-session-expired.loading .loading-spinner {
    display: inline-block !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #ffffff !important;
    border-radius: 50% !important;
    border-top-color: transparent !important;
    animation: spin 1s ease-in-out infinite !important;
    margin-right: 10px !important;
    vertical-align: middle !important;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    #kivicare-session-expired {
        padding: 12px 15px !important;
    }
    
    #kivicare-session-expired strong {
        font-size: 14px !important;
    }
    
    #kivicare-session-expired p {
        font-size: 13px !important;
    }
    
    #kivicare-refresh-page {
        padding: 8px 16px !important;
        font-size: 13px !important;
        margin: 5px 5px !important;
    }
    
    #kivicare-session-expired .countdown-text {
        font-size: 11px !important;
        display: block !important;
        margin-top: 8px !important;
    }
}

@media (max-width: 480px) {
    #kivicare-session-expired {
        padding: 10px 12px !important;
    }
    
    #kivicare-session-expired strong {
        font-size: 13px !important;
    }
    
    #kivicare-session-expired p {
        font-size: 12px !important;
        margin: 6px 0 !important;
    }
    
    #kivicare-refresh-page {
        padding: 6px 12px !important;
        font-size: 12px !important;
        display: block !important;
        margin: 8px auto 5px !important;
        width: auto !important;
        max-width: 200px !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    #kivicare-session-expired {
        background: #000000 !important;
        border-bottom: 3px solid #ffffff !important;
    }
    
    #kivicare-refresh-page {
        background: #ffffff !important;
        color: #000000 !important;
        border: 2px solid #000000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #kivicare-session-expired {
        animation: none !important;
    }
    
    #kivicare-refresh-page {
        transition: none !important;
    }
    
    #kivicare-refresh-page:hover {
        transform: none !important;
    }
    
    #kivicare-session-expired.loading .loading-spinner {
        animation: none !important;
        border-top-color: #ffffff !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    #kivicare-session-expired {
        background: linear-gradient(135deg, #b91c1c, #991b1b) !important;
        border-bottom-color: #7f1d1d !important;
    }
}

/* Print styles - hide notification when printing */
@media print {
    #kivicare-session-expired {
        display: none !important;
    }
}
