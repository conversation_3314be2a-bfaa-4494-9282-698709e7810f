import { get, post } from "../config/request";

// services/patient.js

export const updatePatientDetails = async (payload) => {
  try {
    const response = await post("patient_save", {
      ...payload,
      is_encounter_temp: payload.is_encounter_temp
    });
    
    if (!response?.data) {
      throw new Error('No response from server');
    }
    
    return response.data;
  } catch (error) {
    console.error("Error in updatePatientDetails:", error);
    throw error;
  }
};
