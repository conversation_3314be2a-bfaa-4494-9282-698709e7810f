<template>
  <div>
    <!-- Loading State -->
    <div
      v-if="formLoader"
      class="flex items-center justify-center min-h-screen"
    >
      <loader-component-2></loader-component-2>
    </div>

    <!-- Main Content -->
    <div v-else class="flex flex-col lg:flex-row gap-6">
      <!-- Form Section -->
      <div class="lg:w-3/4">
        <form
          id="receptionistDataForm"
          @submit.prevent="handleSubmit"
          :novalidate="true"
        >
          <div class="bg-white rounded-lg shadow">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-xl font-semibold">
                {{ formTranslation.doctor.edit_profile }}
              </h3>
            </div>

            <!-- Form Content -->
            <div class="p-6">
              <!-- Basic Information Section -->
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.doctor.basic_information }}
              </h6>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- First Name -->
                <div>
                  <label
                    for="first_name"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.fname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="first_name"
                    v-model="receptionistData.first_name"
                    :class="{
                      'border-red-500':
                        submitted && $v.receptionistData.first_name.$error,
                      'border-gray-300':
                        !submitted || !$v.receptionistData.first_name.$error,
                    }"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.receptionist.fname_plh"
                    type="text"
                  />
                  <p
                    v-if="submitted && !$v.receptionistData.first_name.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.fname_required }}
                  </p>
                  <p
                    v-else-if="
                      submitted && !$v.receptionistData.first_name.alpha
                    "
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.fname_validation_1 }}
                  </p>
                </div>

                <!-- Last Name -->
                <div>
                  <label
                    for="last_name"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.lname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="last_name"
                    v-model="receptionistData.last_name"
                    :class="{
                      'border-red-500':
                        submitted && $v.receptionistData.last_name.$error,
                      'border-gray-300':
                        !submitted || !$v.receptionistData.last_name.$error,
                    }"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.receptionist.lname_plh"
                    type="text"
                  />
                  <p
                    v-if="submitted && !$v.receptionistData.last_name.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.lname_required }}
                  </p>
                </div>

                <!-- Email -->
                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.email_address }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    v-model="receptionistData.user_email"
                    :class="{
                      'border-red-500':
                        submitted && $v.receptionistData.user_email.$error,
                      'border-gray-300':
                        !submitted || !$v.receptionistData.user_email.$error,
                    }"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.receptionist.email_plh"
                    type="email"
                  />
                  <p
                    v-if="submitted && !$v.receptionistData.user_email.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.email_required }}
                  </p>
                </div>

                <!-- Phone Number -->
                <div>
                  <label
                    for="telephone_no"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <VuePhoneNumberInput
                    v-model="receptionistData.mobile_number"
                    id="telephone_no"
                    clearable
                    :default-country-code="defaultCountryCode"
                    @update="contactUpdateHandaler"
                    no-example
                    class="phone-input"
                  />
                  <p
                    v-if="
                      submitted && !$v.receptionistData.mobile_number.required
                    "
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.contact_num_required }}
                  </p>
                </div>

                <!-- Date of Birth -->
                <div>
                  <label
                    for="dob"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.dob }}
                  </label>
                  <input
                    type="date"
                    id="doc_birthdate"
                    v-model="receptionistData.dob"
                    :max="new Date().toISOString().slice(0, 10)"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <!-- Gender -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ formTranslation.common.gender }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="space-y-2">
                    <label class="inline-flex items-center mr-4">
                      <input
                        type="radio"
                        v-model="receptionistData.gender"
                        value="male"
                        class="form-radio text-blue-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.male
                      }}</span>
                    </label>
                    <label class="inline-flex items-center mr-4">
                      <input
                        type="radio"
                        v-model="receptionistData.gender"
                        value="female"
                        class="form-radio text-blue-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.female
                      }}</span>
                    </label>
                    <label
                      v-if="defaultUserRegistrationFormSettingData === 'on'"
                      class="inline-flex items-center"
                    >
                      <input
                        type="radio"
                        v-model="receptionistData.gender"
                        value="other"
                        class="form-radio text-blue-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.other
                      }}</span>
                    </label>
                  </div>
                  <p
                    v-if="submitted && !$v.receptionistData.gender.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.gender_required }}
                  </p>
                </div>
              </div>

              <!-- Contact Information Section -->
              <hr class="my-6 border-gray-200" />
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.common.contact_info }}
              </h6>

              <div class="space-y-6">
                <!-- Address -->
                <div>
                  <label
                    for="address"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.address }}
                  </label>
                  <textarea
                    id="address"
                    v-model="receptionistData.address"
                    rows="3"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.receptionist.plh_clinic_add"
                  ></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <!-- City -->
                  <div>
                    <label
                      for="city"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.common.city }}
                    </label>
                    <input
                      id="city"
                      v-model="receptionistData.city"
                      type="text"
                      class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="formTranslation.receptionist.city_plh"
                    />
                  </div>

                  <!-- Country -->
                  <div>
                    <label
                      for="country"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.common.country }}
                    </label>
                    <input
                      id="country"
                      v-model="receptionistData.country"
                      type="text"
                      class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="formTranslation.receptionist.country_plh"
                    />
                  </div>

                  <!-- Postal Code -->
                  <div>
                    <label
                      for="postal_code"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.common.postal_code }}
                    </label>
                    <input
                      id="postal_code"
                      v-model="receptionistData.postal_code"
                      type="text"
                      class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="formTranslation.receptionist.pcode_plh"
                    />
                  </div>

                  <!-- Language Selection -->
                  <div
                    v-if="
                      $store.state.userDataModule.user.addOns.kiviPro !== false
                    "
                  >
                    <label
                      for="choose_language"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.common.choose_language }}
                    </label>
                    <multi-select
                      deselect-label=""
                      select-label=""
                      v-model="receptionistData.choose_language"
                      id="choose_language"
                      :placeholder="formTranslation.common.choose_language"
                      label="label"
                      track-by="lang"
                      :options="kc_available_translations"
                      :multiple="false"
                      class="w-full"
                    ></multi-select>
                  </div>
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div class="px-6 py-4 border-t border-gray-200">
              <div class="flex justify-end">
                <button
                  type="submit"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  :disabled="loading"
                >
                  <template v-if="!loading">
                    <i class="fa fa-save mr-2"></i>
                    {{ formTranslation.common.save }}
                  </template>
                  <template v-else>
                    <i class="fa fa-sync fa-spin mr-2"></i>
                    {{ formTranslation.common.loading }}
                  </template>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Profile Card -->
      <div class="lg:w-1/4">
        <div class="bg-white rounded-lg shadow">
          <div class="flex justify-center pt-6">
            <div class="relative">
              <div
                class="w-32 h-32 rounded-full bg-cover bg-center border-4 border-white shadow"
                :style="'background-image: url(' + profileImage + ');'"
              ></div>
              <button
                @click="uploadProfile"
                class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50"
              >
                <i class="fas fa-pencil-alt"></i>
              </button>
            </div>
          </div>

          <div class="p-6 text-center">
            <h5 class="text-xl font-semibold">
              {{ receptionistData.display_name }}
            </h5>
            <div class="text-gray-600 mt-2">
              {{ receptionistData.user_email }}
            </div>
            <div class="mt-4" v-if="receptionistData.address">
              {{ receptionistData.address }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import {
  required,
  numeric,
  maxLength,
  alpha,
  minLength,
} from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import {
  alphaSpace,
  objToTime,
  phoneNumber,
  postalCode,
  validateForm,
  emailValidate,
} from "../../config/helper";

export default {
  name: "ReceptionistProfile",

  components: {
    VuePhoneNumberInput,
  },

  data: () => ({
    isEditProfile: false,
    receptionistData: {},
    loading: false,
    submitted: false,
    cardTitle: "Edit Profile",
    buttonText: '<i class="fa fa-plus"></i> Add',
    editProfileBtnText: '<i class="fa fa-pen-fancy"></i> Edit Profile',
    profileImage: "",
    formLoader: true,
    defaultCountryCode: null,
    defaultUserRegistrationFormSettingData: "on",
  }),

  validations: {
    receptionistData: {
      first_name: {
        required,
        alpha,
        minLength: minLength(2),
        maxLength: maxLength(15),
      },
      last_name: {
        required,
        alpha,
        minLength: minLength(2),
        maxLength: maxLength(15),
      },
      user_email: {
        required,
        emailValidate,
      },
      mobile_number: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      gender: {
        required,
      },
    },
    custom_fields: {},
  },

  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();
    this.receptionistData = this.defaultReceptionistData();
    this.init();
    this.profileImage =
      window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png";
  },

  methods: {
    contactUpdateHandaler(val) {
      this.receptionistData.country_code = val.countryCode;
      this.receptionistData.country_calling_code = val.countryCallingCode;
    },

    init() {
      if (this.$store.state.userDataModule.user.ID !== undefined) {
        let profileID = this.$store.state.userDataModule.user.ID;
        this.editProfileData(profileID);
      } else {
        this.$store.dispatch("userDataModule/fetchUserData", {});
        setTimeout(() => {
          let profileID = this.$store.state.userDataModule.user.ID;
          this.editProfileData(profileID);
        }, 1000);
      }
    },

    defaultReceptionistData() {
      return {
        first_name: "",
        last_name: "",
        username: "",
        user_email: "",
        user_pass: "",
        country_code: "",
        country_calling_code: "",
        mobile_number: "",
        gender: "",
        dob: "",
        address: "",
        city: "",
        state: "",
        country: "",
        postal_code: "",
        user_status: 0,
        display_name: "",
        profile_image: null,
        choose_language: null,
      };
    },

    uploadProfile() {
      const _this = this;
      const custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        const attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.profileImage = attachment.url;
        _this.receptionistData.profile_image = attachment.id;
      });

      custom_uploader.open();
    },

    handleSubmit() {
      this.loading = true;
      this.submitted = true;

      this.$v.$touch();
      this.$nextTick(() => {
        const invalidElement =
          document.querySelector(".border-red-500") ||
          document.querySelector(".text-red-500");
        if (invalidElement) {
          invalidElement.scrollIntoView({
            block: "center",
            behavior: "smooth",
          });
        }
      });

      if (this.$v.receptionistData.$invalid) {
        this.loading = false;
        return;
      }

      if (validateForm("receptionistDataForm")) {
        post("receptionist_save", this.receptionistData)
          .then((response) => {
            this.loading = false;
            this.submitted = true;

            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              if (response.data.choose_language_updated) {
                this.$store.dispatch(
                  "staticDataModule/refreshDashboardLocale",
                  { self: this }
                );
              }
              displayMessage(response.data.message);
              this.isEditProfile = false;
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            this.submitted = true;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },

    editProfileData(editId) {
      if (editId !== undefined) {
        this.cardTitle = this.formTranslation.doctor.edit_profile;
        this.buttonText =
          '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
        this.formLoader = true;

        get("receptionist_edit", { id: editId })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              setTimeout(() => {
                this.receptionistData = response.data.data;

                if (
                  response.data.data.country_calling_code !== "" &&
                  response.data.data.country_calling_code !== undefined
                ) {
                  this.defaultCountryCode = response.data.data.country_code;
                }

                if (this.receptionistData.user_profile) {
                  this.profileImage = this.receptionistData.user_profile;
                }

                // Ensure the date is in the correct format
                const date = new Date(this.receptionistData.dob);
                if (!isNaN(date.getTime())) {
                  this.receptionistData.dob = date.toISOString().slice(0, 10);
                }

                this.receptionistData.choose_language =
                  this.kc_available_translations.find(
                    (el) => el.lang === response.data.data.choose_language
                  );

                this.isEditProfile = false;
              }, 200);
            }
            this.formLoader = false;
          })
          .catch((error) => {
            console.log(error);
            this.formLoader = false;
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      } else {
        displayErrorMessage(
          this.formTranslation.receptionist.login_user_not_found
        );
      }
    },

    getCountryCodeData() {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultCountryCode = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    getUserRegistrationFormData() {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
  },

  computed: {
    kc_available_translations() {
      return this.$store.state.userDataModule.user.kc_available_translations;
    },
    formattedDob: {
      get() {
        if (!this.receptionistData.dob) return "";
        // Convert the date string to YYYY-MM-DD format
        const date = new Date(this.receptionistData.dob);
        if (isNaN(date.getTime())) return ""; // Return empty if invalid date
        return date.toISOString().slice(0, 10);
      },
      set(value) {
        this.receptionistData.dob = value;
      },
    },
  },
};
</script>
<style scoped>
[type="date"] {
  background: #fff
    url(https://cdn1.iconfinder.com/data/icons/cc_mono_icon_set/blacks/16x16/calendar_2.png)
    97% 50% no-repeat;
}
[type="date"]::-webkit-inner-spin-button {
  display: none;
}
[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}
label {
  display: block;
}
#doc_birthdate {
  border: 1px solid #c4c4c4;
  border-radius: 5px;
  background-color: #fff;
  padding: 3px 5px;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.1);
  /* width: 190px; */
  width: 100%;
  height: 45px;
  color: #8c9cad;
}
#doc_birthdate ::placeholder {
  color: #8c9cad;
}
</style>
