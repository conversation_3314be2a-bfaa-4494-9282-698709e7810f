<!-- CustomFieldModal.vue -->
<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen px-4">
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black opacity-50"></div>
  
        <!-- Modal -->
        <div class="relative bg-white rounded-lg shadow-xl max-w-3xl w-full">
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
              {{ cardTitle }}
            </h3>
            <button
              @click="closeModal"
              class="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
  
          <!-- Body -->
          <div class="px-6 py-4">
            <form @submit.prevent="handleSubmit" id="customFieldDataForm">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Module Type -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.module }} <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="customFieldData.module_type"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      :class="{ 'border-red-500': submitted && $v.customFieldData.module_type.$error }"
                      @change="onChangeModule"
                    >
                      <option value="">{{ formTranslation.custom_field.select_module_plh }}</option>
                      <option v-for="type in getUserRole() !== 'doctor' ? moduleType : doctorType" 
                              :key="type.id" 
                              :value="type">
                        {{ type.label }}
                      </option>
                    </select>
                  </div>
                  <p v-if="submitted && $v.customFieldData.module_type.$error" 
                     class="mt-1 text-sm text-red-600">
                    {{ formTranslation.clinic_schedule.module_type_required }}
                  </p>
                </div>
  
                <!-- Doctor Selection -->
                <div v-if="getUserRole() !== 'doctor'">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.doctor }}
                  </label>
                  <div class="relative">
                    <multi-select
                      v-model="customFieldData.module_id"
                      :options="doctors"
                      :searchable="true"
                      :multiple="multiple"
                      :taggable="taggable"
                      :loading="doctorMultiselectLoader"
                      :disabled="!isDoctorSelectionEnabled || $route.params.id !== undefined"
                      label="label"
                      track-by="id"
                      class="w-full"
                      :class="{ 'border-red-500': submitted && $v.customFieldData.module_id.$error }"
                    />
                  </div>
                </div>
  
                <!-- Label -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.custom_field.label }} <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    v-model="fieldData.label"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    :class="{ 'border-red-500': submitted && $v.fieldData.label.$error }"
                    :placeholder="formTranslation.custom_field.field_label_plh"
                  />
                  <p v-if="submitted && $v.fieldData.label.$error" 
                     class="mt-1 text-sm text-red-600">
                    {{ formTranslation.custom_field.label_required }}
                  </p>
                </div>
  
                <!-- Input Type -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.custom_field.input_type }} <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="fieldData.type"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      :class="{ 'border-red-500': submitted && $v.fieldData.type.$error }"
                    >
                      <option value="">{{ formTranslation.custom_field.input_type_plh }}</option>
                      <option v-for="type in inputType" 
                              :key="type.id" 
                              :value="type">
                        {{ type.label }}
                      </option>
                    </select>
                  </div>
                  <p v-if="submitted && $v.fieldData.type.$error" 
                     class="mt-1 text-sm text-red-600">
                    {{ formTranslation.custom_field.input_type_required }}
                  </p>
                </div>
  
                <!-- File Upload Type -->
                <div v-if="selected_field_type_file_upload">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.file_type }} <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <multi-select
                      v-model="fieldData.file_upload_type"
                      :options="file_upload_type_options"
                      :multiple="true"
                      label="text"
                      track-by="id"
                      class="w-full"
                    />
                  </div>
                  <p v-if="submitted && !file_upload_type_status" 
                     class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.file_type + ' ' + formTranslation.common.required }}
                  </p>
                </div>
  
                <!-- Options -->
                <div v-if="!selected_field_type_file_upload" class="col-span-2">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.custom_field.options }}
                  </label>
                  <div class="relative">
                    <multi-select
                      v-model="fieldData.options"
                      :options="fieldOptions"
                      :multiple="true"
                      :taggable="true"
                      :disabled="selectDisabled"
                      @tag="addOption"
                      label="text"
                      track-by="id"
                      class="w-full"
                    />
                  </div>
                  <p class="mt-1 text-sm text-purple-600">
                    {{ formTranslation.common.note_options }}
                  </p>
                </div>
  
                <!-- Required Field -->
                <div>
                  <label class="flex items-center">
                    <input
                      type="checkbox"
                      v-model="fieldData.isRequired"
                      class="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">
                      {{ formTranslation.custom_field.mandatory_field }}
                    </span>
                  </label>
                </div>
  
                <!-- Status -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ formTranslation.common.status }} <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="fieldData.status"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      :class="{ 'border-red-500': submitted && $v.fieldData.status.$error }"
                    >
                      <option :value="{ id: 1, label: formTranslation.common.active }">
                        {{ formTranslation.common.active }}
                      </option>
                      <option :value="{ id: 0, label: formTranslation.common.inactive }">
                        {{ formTranslation.common.inactive }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
  
              <!-- Footer -->
              <div class="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  @click="closeModal"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-md"
                >
                  {{ formTranslation.common.cancel }}
                </button>
                <button
                  type="submit"
                  class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md"
                  :disabled="loading"
                >
                  <i v-if="loading" class="fa fa-sync fa-spin mr-2"></i>
                  {{ loading ? formTranslation.common.loading : formTranslation.common.save }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import { required, requiredIf } from 'vuelidate/lib/validators'
  import { post, get } from '../../config/request'
  import { validateForm, specialCharacters } from '../../config/helper'
  
  export default {
    name: 'CustomFieldModal',
    props: {
      isOpen: {
        type: Boolean,
        required: true
      }
    },
    data() {
      return {
        cardTitle: 'Add custom field',
        loading: false,
        submitted: false,
        moduleType: [
          { id: 'doctor_module', label: 'Doctor module' },
          { id: 'patient_module', label: 'Patient module' },
          { id: 'patient_encounter_module', label: 'Patient encounter module' }
        ],
        doctorType: [
          { id: 'patient_module', label: 'Patient module' },
          { id: 'patient_encounter_module', label: 'Patient encounter module' }
        ],
        inputType: [
          { id: 'text', label: 'Text' },
          { id: 'number', label: 'Number' },
          { id: 'textarea', label: 'Textarea' },
          { id: 'file_upload', label: 'File Upload' },
          { id: 'select', label: 'Select' },
          { id: 'multiselect', label: 'Multi select' },
          { id: 'radio', label: 'Radio' },
          { id: 'checkbox', label: 'Checkbox' },
          { id: 'calendar', label: 'Calendar' }
        ],
        customFieldData: {},
        fieldData: {},
        fieldOptions: [],
        doctors: [],
        doctorMultiselectLoader: true,
        file_upload_type_options: [],
        file_upload_type_status: true,
        multiple: true,
        taggable: false,
        showDoctorModule: true,
        showEncounterModule: false,
        showPatientModule: false,
        showAppointmentModule: false
      }
    },
    validations: {
      customFieldData: {
        module_type: { required }
      },
      fieldData: {
        label: { required },
        type: { required },
        status: { required }
      }
    },
    mounted() {
      this.init()
      this.customFieldData = this.defaultCustomFieldData()
      this.fieldData = this.defaultFieldData()
      
      if (this.userData.addOns.kiviPro === true) {
        if (this.getUserRole() !== 'doctor') {
          this.moduleType.push({ 
            id: 'appointment_module', 
            label: this.formTranslation.common.appointment_module 
          })
        } else {
          this.doctorType.push({ 
            id: 'appointment_module', 
            label: this.formTranslation.common.appointment_module 
          })
        }
      }
    },
    methods: {
      init() {
        this.getDoctorsData()
        this.fileUploadData()
        
        if (this.$route.params.id !== undefined) {
          this.cardTitle = this.formTranslation.custom_field.edit_custom_field
          this.multiple = false
          
          get('custom_field_edit', { id: this.$route.params.id })
            .then(response => {
              if (response.data.status) {
                this.customFieldData = response.data.data
                this.fieldData = response.data.data.fields
              }
            })
            .catch(error => {
              console.error(error)
              displayErrorMessage(this.formTranslation.custom_field.record_not_found)
            })
        }
      },
      defaultCustomFieldData() {
        return {
          module_type: '',
          module_id: '',
          fields: {}
        }
      },
      defaultFieldData() {
        return {
          id: '',
          label: '',
          type: '',
          name: '',
          options: [],
          isRequired: 0,
          priority: 0,
          placeholder: '',
          status: { id: 1, label: this.formTranslation.common.active }
        }
      },
      onChangeModule(selectedOption) {
        this.multiple = true
        this.taggable = false
        this.showDoctorModule = selectedOption.id === 'doctor_module'
        this.showPatientModule = selectedOption.id === 'patient_module'
        this.showEncounterModule = selectedOption.id === 'patient_encounter_module'
        this.showAppointmentModule = selectedOption.id === 'appointment_module'
      },
      addOption(newOption) {
        const tag = {
          id: newOption,
          text: newOption
        }
        this.fieldOptions.push(tag)
        this.fieldData.options.push(tag)
      },
      closeModal() {
        this.$emit('update:isOpen', false)
        this.customFieldData = this.defaultCustomFieldData()
        this.fieldData = this.defaultFieldData()
      },
      async handleSubmit() {
        this.loading = true
        this.submitted = true
        this.addFieldData()
        
        this.$v.$touch()
        
        if (this.$v.fieldData.$invalid) {
          this.loading = false
          return
        }
        
        if (this.fieldData.type?.id === 'file_upload' && 
            (!this.fieldData.file_upload_type || this.fieldData.file_upload_type.length < 1)) {
          this.file_upload_type_status = false
          this.loading = false
          return
        }
        
        if (this.$v.customFieldData.$invalid) {
          this.loading = false
          return
        }
  
        if (validateForm("customFieldDataForm")) {
          try {
            const response = await post('custom_field_save', this.customFieldData)
            if (response.data.status) {
              displayMessage(response.data.message)
              this.$emit('saved')
              this.closeModal()
            } else {
              displayErrorMessage(response.data.message)
            }
          } catch (error) {
            console.error(error)
            displayErrorMessage(this.formTranslation.common.internal_server_error)
          } finally {
            this.loading = false
          }
        }
      },
      addFieldData() {
        this.customFieldData.fields = [this.fieldData]
      },
      async getDoctorsData() {
        this.doctorMultiselectLoader = true
        try {
          const data = await get('get_static_data', { data_type: 'doctors' })
          if (data.data.status) {
            this.doctors = data.data.data
          }
        } catch (error) {
          console.error(error)
          displayErrorMessage(this.formTranslation.common.internal_server_error)
        } finally {
          this.doctorMultiselectLoader = false
        }
      },
      async fileUploadData() {
        try {
          const data = await get('custom_field_file_upload_data', {})
          if (data.data.status) {
            this.file_upload_type_options = data.data.data.file_type_options
          }
        } catch (error) {
          console.error(error)
          displayErrorMessage(this.formTranslation.common.internal_server_error)
        }
      }
    },
    computed: {
      selectDisabled() {
        return this.fieldData.type?.id === 'text' || 
               this.fieldData.type?.id === 'number' || 
               this.fieldData.type?.id === 'textarea'
      },
      isDoctorSelectionEnabled() {
        return !(this.getUserRole() !== 'doctor' && 
                this.customFieldData.module_type && 
                ['doctor_module', 'patient_module', 'patient_encounter_module']
                  .includes(this.customFieldData.module_type.id))
      },
      userData() {
        return this.$store.state.userDataModule.user
      },
      selected_field_type_file_upload() {
        return this.fieldData.type?.id === 'file_upload'
      }
    },
    watch: {
      'customFieldData.module_type'(newVal) {
        if (newVal?.id === 'appointment_module') {
          if (this.fieldData.type?.id === 'file_upload') {
            this.fieldData.type = ''
          }
          return
        }
        if (!this.inputType.find(item => item.id === 'file_upload')) {
          this.inputType.push({ label: 'File upload', id: 'file_upload' })
        }
      },
      'fieldData.file_upload_type'(newVal) {
        if (!this.file_upload_type_status && newVal?.length > 0) {
          this.file_upload_type_status = true
        }
      }
    }
  }
  </script>