<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                {{ editMode ? $t("Edit Template") : $t("Create Template") }}
              </h3>
              <div class="mt-4">
                <form @submit.prevent="saveTemplate">

                  <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <label for="templateName" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $t("Template Name") }} *
                      </label>
                      <input type="text" id="templateName" v-model="form.name"
                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        :placeholder="$t('Enter template name')" required />
                    </div>
                    <div>
                      <label for="templateCategory" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $t("Category") }} *
                      </label>
                      <select id="templateCategory" v-model="form.category"
                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                        <option value="general">{{ $t("General") }}</option>
                        <option value="referral">{{ $t("Referral") }}</option>
                        <option value="sick_note">{{ $t("Sick Note") }}</option>
                        <option value="consultation">{{ $t("Consultation") }}</option>
                        <option value="procedure">{{ $t("Procedure") }}</option>
                      </select>
                    </div>

                    <div>
                      <label for="templateShare" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $t("Share Status") }} *
                      </label>
                      <select id="templateShare" v-model="form.share_status"
                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                        <option value="private" v-if="getUserRole() !== 'administrator'">{{ $t("Private") }}</option>
                        <option value="clinic">{{ $t("Clinic") }}</option>
                        <option value="public" v-if="getUserRole() === 'administrator'">{{ $t("Public") }}</option>
                      </select>
                    </div>

                    <!-- Show clinic selector for administrators -->
                    <div v-if="getUserRole() === 'administrator' && form.share_status === 'clinic'">
                      <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("Select Clinic") }}</label>
                      <select v-model="form.clinic_id"
                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">{{ $t("Select a clinic") }}</option>
                        <option v-for="clinic in clinics" :key="clinic.id" :value="clinic.id">
                          {{ clinic.label }}
                        </option>
                      </select>
                    </div>
                  </div>

                  <div class="mb-4">
                    <label for="templateContent" class="block text-sm font-medium text-gray-700 mb-1">
                      {{ $t("Template Content") }} *
                    </label>
                    <div class="relative">
                      <!-- Variable Selector Panel -->
                      <div class="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                        <div class="flex justify-between items-center mb-2">
                          <h4 class="text-sm font-medium text-gray-700">{{ $t("Insert Variables") }}</h4>
                          <div>
                            <button type="button"
                              class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                              @click="showAllVariables = !showAllVariables">
                              {{ showAllVariables ? $t("Show Less") : $t("Show All") }}
                            </button>
                          </div>
                        </div>

                        <!-- Variable Categories -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <!-- Patient Information -->
                          <div class="space-y-1">
                            <h5 class="text-xs font-semibold text-gray-600 uppercase">{{ $t("Patient") }}</h5>
                            <div class="flex flex-wrap gap-1">
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.name')">
                                {{ $t("Name") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.unique_id')">
                                {{ $t("ID") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.dob')">
                                {{ $t("DOB") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.age')">
                                {{ $t("Age") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.gender')">
                                {{ $t("Gender") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.email')">
                                {{ $t("Email") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.mobile_number')">
                                {{ $t("Phone") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.address')">
                                {{ $t("Address") }}
                              </button>
                            </div>

                            <!-- Extended patient fields (if Show All is clicked) -->
                            <div v-if="showAllVariables" class="flex flex-wrap gap-1 mt-1">
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.city')">
                                {{ $t("City") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.country')">
                                {{ $t("Country") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('patient.postal_code')">
                                {{ $t("Postal Code") }}
                              </button>
                            </div>
                          </div>

                          <!-- Doctor & Clinic -->
                          <div class="space-y-1">
                            <h5 class="text-xs font-semibold text-gray-600 uppercase">{{ $t("Doctor & Clinic") }}</h5>
                            <div class="flex flex-wrap gap-1">
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('doctor.name')">
                                {{ $t("Doctor Name") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('doctor.speciality')">
                                {{ $t("Speciality") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('doctor.qualification')">
                                {{ $t("Qualification") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('clinic.name')">
                                {{ $t("Clinic Name") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('clinic.address')">
                                {{ $t("Clinic Address") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('clinic.contact')">
                                {{ $t("Clinic Contact") }}
                              </button>
                            </div>
                          </div>

                          <!-- Show more advanced variables if the toggle is on -->
                          <div v-if="showAllVariables" class="space-y-1 md:col-span-2">
                            <h5 class="text-xs font-semibold text-gray-600 uppercase">{{ $t("Consultation Data") }}</h5>
                            <div class="flex flex-wrap gap-1">
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.id')">
                                {{ $t("Encounter ID") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.concerns')">
                                {{ $t("Concerns") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.history')">
                                {{ $t("History") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.examination')">
                                {{ $t("Examination") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.diagnosis')">
                                {{ $t("Diagnosis") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.plan')">
                                {{ $t("Plan") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.medical_history')">
                                {{ $t("Medical History") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.allergies')">
                                {{ $t("Allergies") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.medications')">
                                {{ $t("Medications") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.safeguarding')">
                                {{ $t("Safeguarding") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.notes')">
                                {{ $t("Notes") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.comments')">
                                {{ $t("Comments") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.family_history')">
                                {{ $t("Family History") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.safety_netting')">
                                {{ $t("Safety Netting") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.social_history')">
                                {{ $t("Social History") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.systems_review')">
                                {{ $t("Systems Review") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.preventative_care')">
                                {{ $t("Preventative Care") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.mental_health')">
                                {{ $t("Mental Health") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.lifestyle')">
                                {{ $t("Lifestyle") }}
                              </button>
                            </div>
                          </div>

                          <!-- Date and Other Info -->
                          <div class="space-y-1 md:col-span-2">
                            <h5 class="text-xs font-semibold text-gray-600 uppercase">{{ $t("Date & Other") }}</h5>
                            <div class="flex flex-wrap gap-1">
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('date')">
                                {{ $t("Today's Date") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('time')">
                                {{ $t("Current Time") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('appointment.date')">
                                {{ $t("Appointment Date") }}
                              </button>
                              <button type="button"
                                class="px-2 py-1 bg-white border border-gray-200 rounded-md text-xs text-blue-600 hover:bg-blue-50 hover:border-blue-200"
                                @click="insertVariable('encounter.date')">
                                {{ $t("Encounter Date") }}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Previous simple buttons - REMOVED -->
                      <!-- <div class="absolute right-0 -top-8 flex space-x-2">
                        <button
                          type="button"
                          class="text-sm text-blue-600 hover:text-blue-800"
                          @click="insertVariable('patient.name')"
                        >
                          + {{ $t("Patient Name") }}
                        </button>
                        <button
                          type="button"
                          class="text-sm text-blue-600 hover:text-blue-800"
                          @click="insertVariable('patient.dob')"
                        >
                          + {{ $t("DOB") }}
                        </button>
                        <button
                          type="button"
                          class="text-sm text-blue-600 hover:text-blue-800"
                          @click="insertVariable('clinic.name')"
                        >
                          + {{ $t("Clinic") }}
                        </button>
                        <button
                          type="button"
                          class="text-sm text-blue-600 hover:text-blue-800"
                          @click="insertVariable('doctor.name')"
                        >
                          + {{ $t("Doctor") }}
                        </button>
                        <button
                          type="button"
                          class="text-sm text-blue-600 hover:text-blue-800"
                          @click="insertVariable('date')"
                        >
                          + {{ $t("Date") }}
                        </button>
                      </div> -->
                      <vue-editor v-model="form.content" :editor-toolbar="customToolbar" class="custom-editor h-[400px]"
                        ref="templateEditor"></vue-editor>
                    </div>
                    <div class="mt-3 text-sm text-gray-600">
                      <p class="font-medium mb-1">{{ $t("How to use variables:") }}</p>
                      <ul class="list-disc pl-5 space-y-1 text-gray-500">
                        <li>{{ $t("Insert variables by clicking the buttons above") }}</li>
                        <li>{{ $t("Variables will appear in the format ${variable.name}") }}</li>
                        <li>{{ $t("They will be automatically replaced with real data when the template is used") }}
                        </li>
                        <li>{{ $t("Use 'Show All' to see additional variables for patient details and clinical information") }}</li>
                      </ul>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button type="button"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-black text-base font-medium text-white hover:bg-gray-800 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
            @click="saveTemplate" :disabled="isLoading">
            <template v-if="isLoading">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              {{ $t("Saving...") }}
            </template>
            <template v-else>
              {{ $t("Save") }}
            </template>
          </button>
          <button type="button"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            @click="$emit('close')" :disabled="isLoading">
            {{ $t("Cancel") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import { get, post } from '../../config/request';

export default {
  props: {
    show: {
      type: Boolean,
      required: true
    },
    editMode: {
      type: Boolean,
      default: false
    },
    templateData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isLoading: false,
      showAllVariables: false, // Toggle to show additional variables
      clinics: [], // Will store available clinics for admins
      form: {
        id: null,
        name: "",
        category: "general",
        content: "",
        share_status: "private",
        clinic_id: null
      },
      customToolbar: [
        [
          {
            header: [false, 1, 2, 3, 4, 5, 6]
          }
        ],
        ["bold", "italic", "underline", "strike"],
        [
          {
            align: ""
          },
          {
            align: "center"
          },
          {
            align: "right"
          },
          {
            align: "justify"
          }
        ],
        ["blockquote", "code-block"],
        [
          {
            list: "ordered"
          },
          {
            list: "bullet"
          },
          {
            list: "check"
          }
        ],
        [
          {
            indent: "-1"
          },
          {
            indent: "+1"
          }
        ],
        [
          {
            color: []
          },
          {
            background: []
          }
        ]
      ]
    };
  },
  watch: {
    templateData: {
      immediate: true,
      handler(val) {
        console.log("Template data received:", val);
        if (val) {
          this.form = {
            id: val.id || null,
            name: val.name || "",
            category: val.category || "general",
            content: val.content || "",
            share_status: val.share_status || "private",
            clinic_id: val.clinic_id || null
          };
          console.log("Form after update:", this.form);
        }
      }
    }
  },
  mounted() {
    // Load clinics if user is admin
    if (this.getUserRole() === 'administrator') {
      this.loadClinics();
    }
  },

  computed: {
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
  },

  methods: {
    async loadClinics() {
      // this.isLoadingClinics = true;
      get("get_static_data", {
        data_type: "clinic_list",
      })
        .then((response) => {
          // this.clinicMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.clinics = response.data.data;
          }
        })
        .catch((error) => {
          // this.clinicMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    saveTemplate() {

      this.isLoading = true

      // If admin selected clinic sharing but didn't select a clinic, use default
      if (this.getUserRole() === 'administrator' && this.form.share_status === 'clinic' && !this.form.clinic_id) {
        // Use the first clinic in the list or null if none
        this.form.clinic_id = this.clinics.length > 0 ? this.clinics[0].id : null
      } else {
        this.form.clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole()) ? (Array.isArray(this.userData.user_clinic_id)
          ? this.userData.user_clinic_id[0]
          : this.userData.user_clinic_id) : (
          this.form?.clinic_id !== undefined &&
            this.form?.clinic_id?.id !== undefined
            ? this.form?.clinic_id?.id
            : this.userData.default_clinic_id);
      }

      // Log the data being sent for debugging
      console.log('Saving template data:', this.form)

      post('save_md_template', this.form)
        .then(response => {
          console.log('Template save response:', response)
          if (response.data.status) {
            displayMessage(this.editMode
              ? 'Template updated successfully'
              : 'Template added successfully'
            )
            this.$emit('saved')
            this.$emit('close')
          } else {
            displayErrorMessage(response.data.message)
          }
        })
        .catch(error => {
          console.error('Template save error:', error)
          displayErrorMessage(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    insertVariable(variable) {
      // Get the editor instance
      const editor = this.$refs.templateEditor.quill;
      const range = editor.getSelection();

      if (range) {
        // Insert the variable at the current cursor position with normal black text
        editor.insertText(range.index, `\${${variable}}`, {
          color: '#000000', // Black color
          bold: false      // Not bold
        });
      } else {
        // If no selection, insert at the end with normal black text
        editor.insertText(editor.getLength() - 1, `\${${variable}}`, {
          color: '#000000', // Black color
          bold: false      // Not bold
        });
      }
    }
  }
};
</script>

<style>
.custom-editor {
  height: 400px !important;
}

.ql-editor {
  height: 360px !important;
  overflow-y: auto;
}
</style>