<!-- Template Part -->
<template>
  <div>
    <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
      <!-- Header Section -->
      <div class="mb-8 flex justify-between items-center">
        <div class="flex items-center gap-4">
          <router-link
            :to="{ path: '/' }"
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg shadow-sm hover:bg-gray-800"
          >
            <i class="fa fa-arrow-left w-4 h-4"></i>
            <span>Back</span>
          </router-link>
          <h1 class="text-2xl font-semibold text-gray-800">
            {{ formTranslation.doctor.doctors_list }}
          </h1>
        </div>
        <div class="flex gap-2">
          <button
            v-if="userData.addOns.kiviPro && kivicareCompareVersion(requireProVersion, userData.pro_version)"
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            @click="$refs.module_data_import.openModel = true"
          >
            <i class="fas fa-file-import"></i>
            {{ formTranslation.common.import_data }}
          </button>
          <router-link
            v-if="kcCheckPermission('doctor_add')"
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            :to="{ name: 'doctor.create' }"
          >
            <i class="fa fa-plus"></i>
            {{ formTranslation.doctor.add_doctor }}
          </router-link>
        </div>
      </div>

      <!-- Search Bar -->
      <div class="relative mb-6">
        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"></i>
        <input
          v-model="serverParams.searchTerm"
          @input="globalFilter({ searchTerm: serverParams.searchTerm })"
          :placeholder="formTranslation.common.search_doctor_global_placeholder"
          class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text"
        />
      </div>

      <!-- Filters Grid -->
      <div class="grid grid-cols-6 gap-4 mb-6">
        <input
          v-model="serverParams.columnFilters.ID"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.common.id"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.display_name"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.doctor.dt_name_filter_plh"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.clinic_name"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.doctor.dt_lbl_clinic_name"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.user_email"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          :placeholder="formTranslation.doctor.dt_email_fltr_plh"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <input
          v-model="serverParams.columnFilters.user_registered"
          @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          type="date"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <select
          v-model="serverParams.columnFilters.user_status"
          @change="onColumnFilter({ columnFilters: serverParams.columnFilters })"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        >
          <option value="">{{ formTranslation.static_data.dt_lbl_plh_sr_fltr_status }}</option>
          <option value="0">{{ formTranslation.common.active }}</option>
          <option value="1">{{ formTranslation.common.inactive }}</option>
        </select>
      </div>

      <!-- Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input
                  type="checkbox"
                  class="rounded border-gray-300"
                  v-model="selectAllChecked"
                  @change="handleSelectAll"
                />
              </th>
              <th
                v-for="column in doctorsList.column"
                :key="column.field"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                @click="onSortChange({ field: column.field })"
              >
                {{ column.label }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="(row, index) in doctorsList.data"
              :key="row.ID"
              class="hover:bg-gray-50"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  class="rounded border-gray-300"
                  v-model="selectedRows"
                  :value="row.ID"
                  @change="handleRowSelection"
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.ID }}</td>
              <td class="px-6 py-4 whitespace-nowrap flex items-center gap-3">
                <img
                  v-if="row.profile_image"
                  :src="row.profile_image"
                  class="h-8 w-8 rounded-full object-cover"
                  alt="profile"
                />
                <span
                  v-else
                  class="h-8 w-8 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center font-medium"
                >
                  {{ getInitials(row.display_name) }}
                </span>
                {{ row.display_name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.clinic_name }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.user_email }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ row.mobile_number }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                {{ getSpeciality(row.specialties) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                    row.user_status === '0'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800',
                  ]"
                >
                  {{ row.user_status === "0" ? formTranslation.common.active : formTranslation.common.inactive }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex gap-2">
                  <router-link
                    v-if="kcCheckPermission('doctor_edit')"
                    v-b-tooltip.hover
                    :title="formTranslation.common.edit"
                    class="p-1 hover:bg-gray-100 rounded"
                    :to="{ name: 'doctor.edit', params: { id: row.ID } }"
                  >
                    <i class="fa fa-pen text-gray-600"></i>
                  </router-link>

                  <button
                    v-if="kcCheckPermission('doctor_session_add')"
                    v-b-tooltip.hover
                    :title="formTranslation.common.add_session"
                    class="p-1 hover:bg-gray-100 rounded"
                    @click="$router.push({ name: 'doctor-session.create', params: { id: row.ID } })"
                  >
                    <i class="fa fa-calendar text-gray-600"></i>
                  </button>

                  <button
                    v-if="kcCheckPermission('service_add')"
                    v-b-tooltip.hover
                    :title="formTranslation.common.service_add"
                    class="p-1 hover:bg-gray-100 rounded"
                    @click="addService(row.ID)"
                  >
                    <i class="fa fa-server text-gray-600"></i>
                  </button>

                  <button
                    v-if="row.user_deactivate === 'no' && getUserRole() === 'administrator'"
                    v-b-tooltip.hover
                    :title="formTranslation.common.verify"
                    class="p-1 hover:bg-gray-100 rounded"
                    @click="openVerifyModal(row)"
                  >
                    <i class="fas fa-check-circle text-gray-600"></i>
                  </button>

                  <button
                    v-if="kcCheckPermission('doctor_delete')"
                    v-b-tooltip.hover
                    :title="formTranslation.clinic_schedule.dt_lbl_dlt"
                    class="p-1 hover:bg-gray-100 rounded"
                    @click="deleteDoctorData(index + 1)"
                  >
                    <i class="fa fa-trash text-red-500"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-700">Rows per page:</span>
            <select
              v-model="serverParams.perPage"
              @change="onPerPageChange({ currentPerPage: serverParams.perPage })"
              class="border border-gray-300 rounded-md text-sm p-1"
            >
              <option>10</option>
              <option>25</option>
              <option>50</option>
            </select>
          </div>
          <div class="flex items-center gap-4">
            <span class="text-sm text-gray-700">
              Page {{ serverParams.page }} of {{ Math.ceil(totalRows / serverParams.perPage) }}
            </span>
            <div class="flex gap-2">
              <button
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                :disabled="serverParams.page === 1"
                @click="onPageChange({ currentPage: serverParams.page - 1 })"
              >
                <i class="fa fa-chevron-left text-gray-600"></i>
              </button>
              <button
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                :disabled="serverParams.page >= Math.ceil(totalRows / serverParams.perPage)"
                @click="onPageChange({ currentPage: serverParams.page + 1 })"
              >
                <i class="fa fa-chevron-right text-gray-600"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Modals -->
      <module-data-import
        v-if="userData.addOns.kiviPro && kcCheckPermission('doctor_add')"
        ref="module_data_import"
        @reloadList="getDoctorList"
        :required_data="importRequiredData"
        :module_name="formTranslation.common.doctors"
        module_type="doctor"
      />

      <!-- Other existing modals -->
      <doctor-service v-if="doctorServiceOpen" :doctor_id="serviceDoctorId" @close="closeServiceModal" />
      
      <!-- Verify Modal -->
      <modal-popup v-if="verifyPopupModal" @close="verifyPopupModal = false">
        <!-- Verify modal content -->
      </modal-popup>

      <!-- Other modals -->
      <appointment-review-card
        v-if="appointmentReviewModal"
        :doctor_id="select_doctor_id"
        @close="appointmentReviewModal = false"
      />
      
      <custom-form
        v-if="doctorCustomFormModal"
        :data="doctorCustomFormData"
        :viewMode="doctorCustomFormViewMode"
        @close="doctorCustomFormModal = false"
      />
    </div>
  </div>
</template>
<script>
import { post, get } from "../../config/request";
import doctorService from "../Service/Create";
import { required } from "vuelidate/lib/validators";
import AppointmentReviewCard from "../../components/appointment/AppointmentReviewCard";
import ModalPopup from "../../components/Modal/Index";
import CustomForm from "../CustomForm/Form.vue";
export default {
  components: {
    doctorService,
    AppointmentReviewCard,
    ModalPopup,
    CustomForm,
  },
  data: () => {
    return {
      isSending: false,
      isLoading: true,
      doctorsList: {
        data: [],
        column: [],
      },
      clinic: [],
      filterClinic: [],
      doctorRequest: {},
      doctorSpecialization: [],
      selectedRows: [], // Add this for row selection
      selectAllChecked: false, // Add this for select all checkbox
      serverParams: {
        columnFilters: {
          specialties: "",
        },
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
        type: "list",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      verifyModalData: {},
      verifyPopupModal: false,
      verifyButtonLoading: false,
      doctorServiceOpen: false,
      serviceDoctorId: -1,
      telemed: {},
      telemedSubmitted: false,
      doctorTelemedConfigurationModal: false,
      doctorTelemedConfigurationLoader: true,
      telemedFormLoader: false,
      appointmentReviewModal: false,
      select_doctor_id: 0,
      globalCheckboxApplyData: {},
      globalCheckboxApplyDataActions: [],
      doctorCustomFormData: {},
      doctorCustomFormModal: false,
      doctorCustomFormViewMode: false,
    };
  },
  validations: {
    telemed: {
      api_key: { required },
      api_secret: { required },
      // video_price: {required}
    },
  },
  mounted() {
    this.init();
    this.telemed = this.defaultZoomConfifurationValue();
  },
  methods: {
    init: function () {
      this.getDoctorList();
      this.doctorsList = this.defaultDoctorDataList(this.formTranslation);
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData();
      this.globalCheckboxApplyDataActions =
        this.defaultGlobalCheckboxApplyDataActions();

      setTimeout(() => {
        this.clinic = this.clinics;
        this.clinic.forEach((element) => {
          this.filterClinic.push({ value: element.id, text: element.label });
        });
      }, 1000);
    },
    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: "delete",
        module: "doctors",
        data: [],
      };
    },
    defaultGlobalCheckboxApplyDataActions: function () {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active,
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive,
        },
        {
          value: "resend_credential",
          label: this.formTranslation.receptionist.resend_credential,
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt,
        },
      ];
    },
    getDoctorList: function () {
      get("doctor_list", this.serverParams)
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.isLoading = false;
            this.doctorsList.data = data.data.data;
            this.totalRows = data.data.total_rows;
          } else {
            this.isLoading = false;
            this.doctorsList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          this.isLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    defaultDoctorDataList: function () {
      return {
        column: [
          {
            field: "ID",
            label: this.formTranslation.common.id,
            width: "70px",
            filterOptions: {
              enabled: true, // enable filter for this column
              placeholder: this.formTranslation.common.id,
              filterValue: "",
            },
          },
          {
            field: "display_name",
            width: "100px",
            label: this.formTranslation.doctor.dt_lbl_name,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.doctor.dt_name_filter_plh,
              filterValue: "",
            },
          },
          {
            field: "clinic_name",
            label: this.formTranslation.doctor.dt_lbl_clinic_name,
            sortable: false,
            filterOptions: {
              enabled:
                this.userData.addOns.kiviPro &&
                ["administrator"].includes(this.getUserRole()),
              filterValue: "",
              filterDropdownItems: this.filterClinic,
            },
          },
          {
            field: "user_email",
            label: this.formTranslation.doctor.dt_lbl_email,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.doctor.dt_email_fltr_plh,
              filterValue: "",
            },
          },
          {
            field: "mobile_number",
            label: this.formTranslation.doctor.dt_lbl_mobile_number,
            width: "150px",
            sortable: false,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.doctor.dt_mobile_fltr_number,
              filterValue: "",
            },
          },
          {
            field: "specialties",
            label: this.formTranslation.doctor.dt_lbl_specialties,
            sortable: false,
            width: "200px",
            filterOptions: {
              enabled: true, // enable filter for this column
              placeholder:
                this.formTranslation.doctor.dt_specialities_filter_plh,
              filterValue: "",
            },
            html: true,
          },
          {
            field: "user_status",
            label: this.formTranslation.service.dt_lbl_status,
            filterOptions: {
              enabled: true, // enable filter for this column
              placeholder:
                this.formTranslation.static_data.dt_lbl_plh_sr_fltr_status,
              filterDropdownItems: [
                { value: "0", text: this.formTranslation.common.active },
                { value: "1", text: this.formTranslation.common.inactive },
              ],
              filterValue: "",
            },
            html: true,
          },
          {
            field: "actions",
            label: this.formTranslation.doctor.dt_lbl_actions,
            sortable: false,
          },
        ],
        data: [],
      };
    },
    updateParams: function (newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getDoctorList();
    },

    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 300),
    onColumnFilter: _.debounce(function (params) {
      var emptyValue = true;
      var emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value, index, array) {
        if (value) {
          emptyValue = false;
        }
      });
      Object.values(this.oldServerParams.columnFilters).map(function (
        value,
        index,
        array
      ) {
        if (value) {
          emptyValue2 = false;
        }
      });
      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
          {},
          params.columnFilters
        );
        this.updateParams({
          columnFilters: params.columnFilters,
          perPage: this.serverParams.perPage,
          page: 1,
        });
      }
    }, 300),
    deleteDoctorData: function (index) {
      if (this.doctorsList.data[index - 1] !== undefined) {
        let ele = $("#user_delete_" + index);
        $.confirm({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          content:
            this.formTranslation.clinic_schedule.dt_delete_doctor_appointment,
          type: "red",
          buttons: {
            ok: {
              text: this.formTranslation.common.yes,
              btnClass: "btn-danger",
              keys: ["enter"],
              action: () => {
                ele.prop("disabled", true);
                $(ele).find("i").removeClass("fa fa-trash");
                $(ele).find("i").addClass("fa fa-sync fa-spin");
                get("doctor_delete", {
                  id: this.doctorsList.data[index - 1].ID,
                })
                  .then((data) => {
                    if (this.getUserRole() === "administrator") {
                      this.$store.dispatch("userDataModule/fetchUserData", {});
                    }
                    ele.prop("disabled", false);
                    $(ele).find("i").removeClass("fa fa-sync fa-spin");
                    $(ele).find("i").addClass("fa fa-trash");
                    if (
                      data.data.status !== undefined &&
                      data.data.status === true
                    ) {
                      this.doctorsList.data.splice(index - 1, 1);
                      displayMessage(data.data.message);
                    }
                  })
                  .catch((error) => {
                    ele.prop("disabled", false);
                    $(ele).find("i").removeClass("fa fa-sync fa-spin");
                    $(ele).find("i").addClass("fa fa-trash");
                    console.log(error);
                    displayErrorMessage(
                      this.formTranslation.common.internal_server_error
                    );
                  });
              },
            },
            cancel: {
              text: this.formTranslation.common.cancel,
            },
          },
        });
      }
    },

    confirmDelete() {
      let content = "";
      if (this.globalCheckboxApplyData.action_perform === "delete") {
        content = this.formTranslation.common.py_delete;
      } else if (
        this.globalCheckboxApplyData.action_perform === "resend_credential"
      ) {
        content = this.formTranslation.common.py_resend_credential;
      } else if (
        this.globalCheckboxApplyData.action_perform === "active" ||
        this.globalCheckboxApplyData.action_perform === "inactive"
      ) {
        content = this.formTranslation.common.py_status;
      }
      $.confirm({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        content: content,
        type: "red",
        buttons: {
          ok: {
            text: this.formTranslation.common.yes,
            btnClass: "btn-danger",
            keys: ["enter"],
            action: () => {
              this.globalCheckboxApply();
            },
          },
          cancel: {
            text: this.formTranslation.common.cancel,
          },
        },
      });
    },

    globalCheckboxApply() {
      this.isLoading = true;
      post("module_wise_multiple_data_update", this.globalCheckboxApplyData)
        .then((data) => {
          this.isLoading = false;
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
            this.getDoctorList();
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          this.isLoading = true;
          console.log(error);
        });
    },
    addService(id) {
      this.serviceDoctorId = id;
      this.doctorServiceOpen = true;
    },
    closeServiceModal() {
      this.doctorServiceOpen = false;
    },
    resendRequest: function (id) {
      var element = $("#resend_" + id).find("i");
      element.removeClass("fa fa-paper-plane ");
      element.addClass("fa fa-spinner fa-spin");
      post("resend_credential", { id: id })
        .then((data) => {
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-paper-plane");
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    verifyUser(data) {
      this.verifyButtonLoading = true;
      post("verify_user", { data: data })
        .then((response) => {
          this.verifyButtonLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            this.verifyPopupModal = false;
            this.getDoctorList();
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.verifyButtonLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    defaultZoomConfifurationValue() {
      return {
        enableTeleMed: "off",
        api_key: "",
        api_secret: "",
        zoom_id: "",
        video_price: "",
      };
    },
    handleZoomConfigurationSubmit() {
      this.$v.$touch();
      this.telemedSubmitted = true;
      if (this.$v.telemed.$invalid) {
        return;
      }
      this.telemedSubmitted = false;
      this.telemedFormLoader = true;
      let configRequest = {
        api_key: this.telemed.api_key,
        api_secret: this.telemed.api_secret,
        doctor_id: this.telemed.doctor_id,
        enableTeleMed: this.telemed.enableTeleMed,
        video_price: this.telemed.video_price,
      };
      post("save_doctor_zoom_configuration", configRequest)
        .then((response) => {
          this.telemedFormLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            this.telemedConfigurationModalClose();
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.telemedFormLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    telemedConfigurationModalClose() {
      this.telemedSubmitted = false;
      this.doctorTelemedConfigurationModal = false;
      this.telemed = this.defaultZoomConfifurationValue();
      this.doctorTelemedConfigurationLoader = true;
      this.telemedFormLoader = false;
    },
    openTelemedConfigurationModal(id) {
      this.doctorTelemedConfigurationModal = true;
      this.getConfigurationData(id);
    },
    getConfigurationData: function (id) {
      this.doctorTelemedConfigurationLoader = true;
      get("get_doctor_zoom_configuration", {
        user_id: id,
      })
        .then((response) => {
          this.doctorTelemedConfigurationLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.telemed = response.data.data;
            this.telemed.doctor_id = id;
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          this.doctorTelemedConfigurationLoader = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getInitials(name) {
      if (name !== undefined && name !== "" && name !== null) {
        const patient_name = name.split(" ");
        const initials = patient_name.map((patient_name) =>
          patient_name.charAt(0).toUpperCase()
        );
        return initials.join("");
      } else {
        return " - ";
      }
    },
    customFormOpen(props, custom_form_data) {
      this.doctorCustomFormData = custom_form_data;
      this.doctorCustomFormData.module_id = props.ID;
      this.doctorCustomFormViewMode = false;
      this.doctorCustomFormModal = true;
    },
    customFormCondition(props, custom_form_data) {
      return (
        props.custom_forms &&
        props.custom_forms.length &&
        (custom_form_data.clinic_ids.length === 0 ||
          custom_form_data.clinic_ids.some((value) =>
            props.clinic_id.includes(value)
          ))
      );
    },
  },
  computed: {
    doctorListExport() {
      return "Doctor List - " + moment().format("YYYY-MM-DD");
    },
    getSpeciality: function () {
      return (salut) => {
        if (salut !== undefined && salut !== null && salut.length > 0) {
          let specialties = "";
          if (typeof salut !== "string") {
            salut.map(function (spec, index) {
              specialties +=
                salut.length === index + 1 ? spec.label : spec.label + ", ";
              return spec;
            });
            return specialties;
          } else {
            return salut;
          }
        }
        return " - ";
      };
    },
    clinics() {
      return this.$store.state.clinic;
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    specialization() {
      if (
        this.$store.state.staticDataModule.static_data.specialization !==
          undefined &&
        this.$store.state.staticDataModule.static_data.specialization.length > 0
      ) {
        return this.$store.state.staticDataModule.static_data.specialization;
      }
      return [];
    },
    importRequiredData(){

    },
    handleRowSelection(){

    },
    handleSelectAll(){

    },
  },
  watch: {},
};
</script>
<style>
#verifymodal header {
  min-height: unset;
}
</style>