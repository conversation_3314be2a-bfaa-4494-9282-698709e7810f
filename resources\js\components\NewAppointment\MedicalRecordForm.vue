<template>
  <div class="bg-white rounded border p-3">
    <div class="flex justify-between items-center mb-2">
      <div class="flex items-center gap-2">
        <h2 class="font-medium">{{ form?.title }}</h2>
        <!-- AI populated badge - shows when content has been populated by AI and not yet approved -->
        <div v-if="isAIGenerated"
          class="px-2 py-0.5 rounded-full bg-blue-100 text-blue-600 text-xs font-medium flex items-center gap-1">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-sparkles">
            <path
              d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
            <path d="M5 3v4" />
            <path d="M19 17v4" />
            <path d="M3 5h4" />
            <path d="M17 19h4" />
          </svg>
          AI populated
        </div>
      </div>

      <div class="flex gap-2">

        <!-- Clone button -->
        <button v-if="displayPlusBtn" @click="handleClone" class="text-blue-500 hover:text-blue-600">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-plus w-4 h-4">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>
        </button>
      </div>
    </div>

    <div class="flex flex-col gap-2">
      <textarea v-model="localContent" class="w-full h-28 text-sm resize-none focus:outline-none border-b"
        :placeholder="'Enter ' + form?.title.toLowerCase() + '...'" @input="handleContentChange"></textarea>

      <div class="flex justify-between items-center w-full">
        <!-- Prefill Button -->
        <button @click="handlePrefill" class="text-sm text-gray-500 hover:text-gray-600"
          :disabled="!hasPrefillTemplate">
          Prefill
        </button>

        <!-- Right side container with flex -->
        <div class="flex items-center gap-3">
          <!-- Reset Button - shows only when AI populated -->
          <button v-if="isAIGenerated" @click="handleResetContent"
            class="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-150">
            Reset
          </button>

          <!-- Approve AI button - only shown when content has been AI populated -->
          <button v-if="isAIGenerated" @click="handleApproveAI"
            class="py-1 pl-1 pr-1.5 hover:bg-green-600 hover:text-white text-green-500 border border-1 border-green-500 rounded-xs flex items-center text-sm transition-colors duration-150 shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
              class="flex-shrink-0">
              <path d="M5 12L10 17L19 8" />
            </svg>
            Approve
          </button>
        </div>
      </div>

      <div v-if="showTemplates" class="mt-2 border-t pt-2">
        <h4 class="text-sm font-medium mb-2">Quick Templates</h4>
        <div class="flex flex-wrap gap-2">
          <button v-for="template in form.templates" :key="template" @click="applyTemplate(template)"
            class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
            {{ template }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { prefillTemplates } from "../../utils/prefillTemplates";

export default {
  name: "MedicalRecordForm",

  props: {
    form: {
      type: [Object, null],
      validator: (value) => {
        return (
          value &&
          typeof value.title === "string" &&
          typeof value.type === "string" &&
          Array.isArray(value.templates)
        );
      },
    },
    displayPlusBtn: {
      type: Boolean,
      default: false,
    },
    // Optional prop for AI population data
    aiData: {
      type: [String, Object],
      default: null
    }
  },

  data() {
    return {
      localContent: "",
      showTemplates: false,
      saveTimeout: null,
      isAIGenerated: false,
      originalContent: "", // Store original content before AI population
      pendingAiContent: null, // Store AI content while waiting for approval
      localApproved: false,  // Local tracking of approval state
      isManuallyEdited: false, // Track if user has manually edited the content
    };
  },

  computed: {
    hasPrefillTemplate() {
      return !!prefillTemplates[this.form.type];
    },
  },

  mounted() {
    // Check if this instance was previously approved (from localStorage)
    const instanceId = this.form?.instanceId;
    if (instanceId) {
      this.localApproved = localStorage.getItem(`approved_${instanceId}`) === 'true';

      // If locally marked as approved, ensure isAIGenerated is false
      if (this.localApproved) {
        this.isAIGenerated = false;
      }
    }
  },

  watch: {
    "form.content": {
      immediate: true,
      handler(newContent) {
        if ((!this.localContent || this.localContent === "") && newContent !== this.localContent) {
          this.localContent = newContent || "";
          this.originalContent = newContent || "";
        }
      },
    },
    "form.templates": {
      immediate: true,
      handler(newTemplates) {
        this.showTemplates = Array.isArray(newTemplates) && newTemplates.length > 0;
      },
    },
    // Watch for AI data changes and populate content
    aiData: {
      immediate: true,
      handler(newData, oldData) {
        if (newData &&
          this.form &&
          (!this.localContent || this.localContent.trim().length < 10) &&
          !this.isManuallyEdited &&
          !this.localApproved) {
          this.populateWithAI(newData);
        }
      },
      deep: true
    }
  },

  methods: {
    // Handle normal content changes
    handleContentChange() {
      // Mark as manually edited
      this.isManuallyEdited = true;

      // If the user modifies content significantly, consider it user-edited
      // Only switch off AI flag if content is substantially different
      if (this.isAIGenerated &&
        this.pendingAiContent &&
        Math.abs(this.localContent.length - this.pendingAiContent.length) > 20) {
        // Only disable AI generated flag if content is substantially modified
        this.isAIGenerated = false;
        this.pendingAiContent = null;
      }

      // Emit the content update event - this triggers saving
      this.$emit("update:content", {
        type: this.form.type,
        content: this.localContent,
        instanceId: this.form.instanceId,
        isAIGenerated: this.isAIGenerated
      });

      // Also trigger a save after debounce
      if (this.saveTimeout) {
        clearTimeout(this.saveTimeout);
      }

      this.saveTimeout = setTimeout(() => {
        this.handleSave();
      }, 1500);
    },

    populateWithAI(aiData) {
      // If this content was previously approved, manually edited, or aiData is null, exit early
      if (this.localApproved || this.isManuallyEdited || !aiData) return;

      let contentToUse = "";
      let isAIGenerated = true;

      if (typeof aiData === 'string') {
        contentToUse = aiData;
      } else if (typeof aiData === 'object') {
        if (aiData.content) {
          contentToUse = aiData.content;
          if (aiData.isAIGenerated !== undefined) {
            isAIGenerated = aiData.isAIGenerated;
          }
        } else {
          contentToUse = aiData[this.form.type] || "";
        }
      }

      if (contentToUse.trim()) {
        // Ensure these flags are set correctly
        this.isAIGenerated = true; // Force this to true regardless of the input value
        this.originalContent = this.localContent || "";
        this.pendingAiContent = contentToUse;
        this.localContent = contentToUse;

        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: true
        });
      }
    },

    // Handle AI approval
    handleApproveAI() {
      if (this.isAIGenerated) {
        this.isAIGenerated = false;
        this.pendingAiContent = null;
        this.localApproved = true;  // Set local approval state

        // Store in localStorage
        const instanceId = this.form?.instanceId;
        if (instanceId) {
          localStorage.setItem(`approved_${instanceId}`, 'true');
        }

        // Emit events
        this.$emit("ai-approved", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId
        });

        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: false
        });

        // Trigger a save immediately
        this.handleSave();
      }
    },

    // Reset to pre-AI content
    handleResetContent() {
      if (this.isAIGenerated) {
        this.localContent = this.originalContent || "";
        this.isAIGenerated = false;
        this.pendingAiContent = null;

        // Mark as manually edited to prevent re-population
        this.isManuallyEdited = true;

        // Emit the update
        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: false
        });

        // Trigger save
        this.handleSave();
      }
    },

    handleSave() {
      if (this.localContent.trim()) {
        // The parent component expects 'save' event for explicit save actions
        this.$emit("save", {
          type: this.form.type,
          content: this.localContent.trim(),
          instanceId: this.form.instanceId,
          isAIGenerated: this.isAIGenerated
        });

        // Also emit the update:content event to ensure data is marked for saving in parent
        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent.trim(),
          instanceId: this.form.instanceId,
          isAIGenerated: this.isAIGenerated
        });
      }
    },

    handleClone() {
      this.$emit("clone", {
        type: this.form.type,
        content: this.localContent,
        isMainForm: this.form.isMainForm,
        metadata: this.form.metadata,
      });
    },

    handlePrefill() {
      const template = prefillTemplates[this.form?.type];
      if (template) {
        this.localContent = this.localContent
          ? `${this.localContent}\n${template}`
          : template;
        this.handleContentChange();
      }
    },

    applyTemplate(template) {
      const templateContent = this.getTemplateContent(template);
      this.localContent = this.localContent
        ? `${this.localContent}\n${templateContent}`
        : templateContent;
      this.handleContentChange();
    },

    getTemplateContent(template) {
      const templateMap = {
        CVS: `Cardiovascular Examination:
* Heart sounds:
* Rhythm:
* Peripheral pulses:
* Edema:`,
        Resp: `Respiratory Examination:
* Breath sounds:
* Percussion:
* Chest movement:
* Respiratory effort:`,
        Gastro: `Gastrointestinal Examination:
* Abdomen inspection:
* Palpation:
* Bowel sounds:
* Tenderness:`,
        Neuro: `Neurological Examination:
* Mental status:
* Cranial nerves:
* Motor function:
* Sensory function:
* Reflexes:`,
        MSK: `Musculoskeletal Examination:
* Range of motion:
* Strength:
* Tenderness:
* Deformity:`,
        Derm: `Dermatological Examination:
* Skin appearance:
* Lesions:
* Distribution:
* Associated symptoms:`,
        ENT: `ENT Examination:
* Ears:
* Nose:
* Throat:
* Lymph nodes:`,
      };
      return templateMap[template] || `${template} examination:`;
    },
  },

  beforeDestroy() {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }
  },
};
</script>