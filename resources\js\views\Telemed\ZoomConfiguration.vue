<template>
  <div>
    <div class="page-loader-section" v-if="formLoader">
      <loader-component-2></loader-component-2>
    </div>
    <div v-else class="space-y-6">
      <!-- Zoom OAuth Section -->
      <div
        v-if="userData.addOns.telemed"
        class="bg-white rounded-lg shadow-sm overflow-hidden"
      >
        <div class="border-b border-gray-200">
          <button
            v-b-toggle.zoom-telemed-oauth
            class="w-full px-6 py-4 flex items-center justify-between bg-white hover:bg-gray-50 transition-colors duration-150"
          >
            <div class="flex items-center space-x-3">
              <div class="bg-black p-2 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-white"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"
                  />
                </svg>
              </div>
              <span class="font-medium text-gray-900">{{
                formTranslation.zoom_telemed.zoom_telemed_oauth
              }}</span>
            </div>
            <svg
              class="h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>

        <b-collapse
          id="zoom-telemed-oauth"
          :visible="userData.addOns.telemed"
          accordion="my-accordion"
          role="tabpanel"
        >
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-12 gap-8">
              <!-- Configuration Form -->
              <div class="md:col-span-5">
                <form
                  id="googleCalform"
                  @submit.prevent="handleCalenderSubmit"
                  :novalidate="true"
                  enctype="multipart/form-data"
                >
                  <!-- Enable/Disable Switch -->
                  <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                      <label class="flex items-center cursor-pointer">
                        <b-form-checkbox
                          size="md"
                          id="google-meet-configuration"
                          v-model="zoomConfigData.enableCal"
                          name="google-meet-configuration"
                          value="Yes"
                          :disabled="
                            userData.is_enable_doctor_zoom_telemed == 'on'
                          "
                          unchecked-value="No"
                          switch
                          class="mr-3"
                        />
                        <span class="font-medium text-gray-900">{{
                          formTranslation.zoom_telemed.zoom_configuration
                        }}</span>
                      </label>
                    </div>
                  </div>

                  <!-- Configuration Fields -->
                  <div
                    v-if="zoomConfigData.enableCal == 'Yes'"
                    class="space-y-6"
                  >
                    <!-- Client ID -->
                    <div class="form-group">
                      <label
                        class="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {{ formTranslation.zoom_telemed.zoom_client_id }}
                      </label>
                      <input
                        type="text"
                        id="client_id"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm"
                        :class="{
                          'border-red-500':
                            zoomTelemedSubmitted &&
                            !$v.zoomConfigData.client_id.required,
                        }"
                        name="client_id"
                        :disabled="
                          userData.is_enable_doctor_zoom_telemed == 'on'
                        "
                        v-model="zoomConfigData.client_id"
                      />
                      <p
                        v-if="
                          zoomTelemedSubmitted &&
                          !$v.zoomConfigData.client_id.required
                        "
                        class="mt-1 text-sm text-red-600"
                      >
                        {{
                          formTranslation.zoom_telemed.zoom_client_id_required
                        }}
                      </p>
                    </div>

                    <!-- Client Secret -->
                    <div class="form-group">
                      <label
                        class="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {{ formTranslation.zoom_telemed.zoom_client_secret }}
                      </label>
                      <input
                        type="text"
                        id="client_secret"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm"
                        :class="{
                          'border-red-500':
                            zoomTelemedSubmitted &&
                            !$v.zoomConfigData.client_secret.required,
                        }"
                        name="client_secret"
                        v-model="zoomConfigData.client_secret"
                        :disabled="
                          userData.is_enable_doctor_zoom_telemed == 'on'
                        "
                      />
                      <p
                        v-if="
                          zoomTelemedSubmitted &&
                          !$v.zoomConfigData.client_secret.required
                        "
                        class="mt-1 text-sm text-red-600"
                      >
                        {{
                          formTranslation.zoom_telemed
                            .zoom_client_secret_required
                        }}
                      </p>
                    </div>

                    <!-- Redirect URL -->
                    <div class="form-group">
                      <label
                        class="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {{ formTranslation.zoom_telemed.redirect_url }}
                      </label>
                      <div class="flex">
                        <input
                          type="text"
                          id="redirect_url"
                          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm"
                          :class="{
                            'border-red-500':
                              zoomTelemedSubmitted &&
                              !$v.zoomConfigData.redirect_url.required,
                          }"
                          name="redirect_url"
                          v-model="zoomConfigData.redirect_url"
                          :disabled="
                            userData.is_enable_doctor_zoom_telemed == 'on'
                          "
                        />
                      </div>
                      <p
                        v-if="
                          zoomTelemedSubmitted &&
                          !$v.zoomConfigData.redirect_url.required
                        "
                        class="mt-1 text-sm text-red-600"
                      >
                        {{ formTranslation.zoom_telemed.redirect_url_required }}
                      </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between mt-6">
                      <button
                        v-if="userData.is_enable_doctor_zoom_telemed == 'on'"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                        type="button"
                        :disabled="
                          userData.is_enable_zoom_telemed_setting == 'off'
                        "
                        @click="closeConnectionZoomOauth"
                      >
                        <svg
                          class="mr-2 h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                          />
                        </svg>
                        {{ formTranslation.common.disconnect }}
                      </button>

                      <button
                        v-else
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                        type="button"
                        :disabled="
                          userData.is_enable_zoom_telemed_setting == 'off'
                        "
                        @click="connectZoomOauth"
                      >
                        {{ formTranslation.pro_setting.connect_with_zoom }}
                        <img
                          alt="img"
                          class="ml-2 h-4 w-4"
                          :src="zoomSignInImage"
                        />
                      </button>

                      <button
                        type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                        :disabled="
                          userData.is_enable_doctor_zoom_telemed == 'on' ||
                          loading
                        "
                      >
                        <span v-if="loading">
                          <svg
                            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              class="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              stroke-width="4"
                            />
                            <path
                              class="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                          </svg>
                          {{ formTranslation.common.loading }}
                        </span>
                        <span v-else>
                          <svg
                            class="mr-2 h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                            />
                          </svg>
                          {{ formTranslation.common.save }}
                        </span>
                      </button>
                    </div>
                  </div>
                </form>
              </div>

              <!-- Configuration Guide -->
              <div
                class="md:col-span-7"
                v-if="
                  ['true', true].includes(true) &&
                  zoomConfigData.enableCal == 'Yes'
                "
              >
                <div class="bg-gray-50 rounded-lg p-6">
                  <h4 class="text-lg font-medium text-gray-900 mb-4">
                    {{ formTranslation.doctor.zoom_configuration_guide }}
                  </h4>
                  <div class="space-y-3">
                    <div
                      v-for="(step, index) in [
                        {
                          text: formTranslation.doctor.zoom_step1,
                          link: 'https://marketplace.zoom.us/',
                          linkText:
                            formTranslation.doctor.zoom_market_place_portal,
                        },
                        {
                          text: formTranslation.doctor.zoom_step2,
                          link: 'https://marketplace.zoom.us/develop/create',
                          linkText: formTranslation.doctor.create_app,
                        },
                        { text: formTranslation.doctor.zoom_oauth_step3 },
                        { text: formTranslation.doctor.zoom_oauth_step4 },
                        { text: formTranslation.doctor.zoom_oauth_step5 },
                      ]"
                      :key="index"
                      class="flex items-start"
                    >
                      <div
                        class="flex-shrink-0 h-6 w-6 flex items-center justify-center rounded-full bg-black text-white text-sm font-medium"
                      >
                        {{ index + 1 }}
                      </div>
                      <div class="ml-3">
                        <p class="text-sm text-gray-600">
                          {{ step.text }}
                          <a
                            v-if="step.link"
                            :href="step.link"
                            target="_blank"
                            class="text-blue-600 hover:text-blue-800"
                          >
                            {{ step.linkText }}
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Connection Status -->
            <div v-if="zoomConfigData.enableCal == 'Yes'" class="mt-6">
              <div
                v-if="userData.is_enable_doctor_zoom_telemed == 'off'"
                class="bg-yellow-50 rounded-lg p-4"
              >
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-5 w-5 text-yellow-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p
                      class="text-sm text-yellow-700"
                      v-if="userData.is_enable_zoom_telemed_setting == 'off'"
                    >
                      {{ formTranslation.common.please_enable_google_meet }}
                    </p>
                    <p class="text-sm text-yellow-700" v-else>
                      {{
                        formTranslation.zoom_telemed
                          .please_connect_zoom_telemed_service
                      }}
                    </p>
                  </div>
                </div>
              </div>
              <div v-else class="bg-green-50 rounded-lg p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-5 w-5 text-green-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p
                      class="text-sm text-green-700"
                      v-if="userData.is_enable_zoom_telemed_setting == 'off'"
                    >
                      {{ formTranslation.common.please_enable_google_meet }}
                    </p>
                    <p class="text-sm text-green-700" v-else>
                      {{
                        formTranslation.zoom_telemed
                          .connected_zoom_telemed_service
                      }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </b-collapse>
      </div>

      <!-- Zoom JWT Section (Deprecated) -->
      <div
        v-if="userData.addOns.telemed"
        class="bg-white rounded-lg shadow-sm overflow-hidden"
      >
        <div class="border-b border-gray-200">
          <button
            v-b-toggle.zoom-collapse
            class="w-full px-6 py-4 flex items-center justify-between bg-white hover:bg-gray-50 transition-colors duration-150"
          >
            <div class="flex items-center space-x-3">
              <div class="bg-gray-900 p-2 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-white"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm3 2h6v4H7V5zm8 8v2h1v-2h-1zm-2-2H7v4h6v-4zm2 0h1V9h-1v2zm1-4V5h-1v2h1zM5 5v2H4V5h1zm0 4H4v2h1V9zm-1 4h1v2H4v-2z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <span class="font-medium text-gray-900">Zoom JWT</span>
                <span
                  class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800"
                >
                  Deprecated
                </span>
              </div>
            </div>
            <svg
              class="h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>

        <b-collapse
          id="zoom-collapse"
          :visible="userData.addOns.telemed"
          accordion="my-accordion"
          role="tabpanel"
        >
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-12 gap-8">
              <!-- JWT Configuration Form -->
              <div class="md:col-span-5">
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                  <div class="flex items-center justify-between">
                    <b-form-checkbox
                      size="lg"
                      v-model="configurationData.enableTeleMed"
                      name="check-button"
                      @change="zoomStatusChangeEvent"
                      switch
                      class="flex items-center"
                    >
                      <span class="ml-2 text-sm text-gray-600">
                        {{ formTranslation.common.status }}:
                        <span
                          class="font-medium"
                          :class="{
                            'text-green-600':
                              configurationData.enableTeleMed === true ||
                              configurationData.enableTeleMed === 'true',
                            'text-red-600': !(
                              configurationData.enableTeleMed === true ||
                              configurationData.enableTeleMed === 'true'
                            ),
                          }"
                        >
                          {{
                            configurationData.enableTeleMed === true ||
                            configurationData.enableTeleMed === "true"
                              ? "On"
                              : "Off"
                          }}
                        </span>
                      </span>
                    </b-form-checkbox>
                  </div>
                </div>

                <!-- API Key Input -->
                <div class="space-y-6">
                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ formTranslation.doctor.api_key }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="api_key"
                      v-model="configurationData.api_key"
                      :placeholder="formTranslation.zoom_config.plh_api_key"
                      name="api_key"
                      type="text"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm"
                      :class="{
                        'border-red-500':
                          submitted && $v.configurationData.api_key.$error,
                      }"
                    />
                    <p
                      v-if="submitted && !$v.configurationData.api_key.required"
                      class="mt-1 text-sm text-red-600"
                    >
                      {{ formTranslation.doctor.api_key_required }}
                    </p>
                  </div>

                  <!-- API Secret Input -->
                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ formTranslation.doctor.api_secret }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="api_secret"
                      v-model="configurationData.api_secret"
                      :placeholder="formTranslation.zoom_config.plh_api_secret"
                      name="api_secret"
                      type="text"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm"
                      :class="{
                        'border-red-500':
                          submitted && $v.configurationData.api_secret.$error,
                      }"
                    />
                    <p
                      v-if="
                        submitted && !$v.configurationData.api_secret.required
                      "
                      class="mt-1 text-sm text-red-600"
                    >
                      {{ formTranslation.doctor.api_secret_required }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Configuration Guide -->
              <div class="md:col-span-7">
                <div class="bg-gray-50 rounded-lg p-6">
                  <h4 class="text-lg font-medium text-gray-900 mb-4">
                    {{ formTranslation.doctor.zoom_configuration_guide }}
                  </h4>
                  <div class="space-y-4">
                    <div
                      v-for="(step, index) in [
                        {
                          text: formTranslation.doctor.zoom_step1,
                          link: 'https://marketplace.zoom.us/',
                          linkText:
                            formTranslation.doctor.zoom_market_place_portal,
                        },
                        {
                          text: formTranslation.doctor.zoom_step2,
                          link: 'https://marketplace.zoom.us/develop/create',
                          linkText: formTranslation.doctor.create_app,
                        },
                        { text: formTranslation.doctor.zoom_step3 },
                        { text: formTranslation.doctor.zoom_step4 },
                        { text: formTranslation.doctor.zoom_step5 },
                      ]"
                      :key="index"
                      class="flex items-start"
                    >
                      <div
                        class="flex-shrink-0 h-6 w-6 flex items-center justify-center rounded-full bg-black text-white text-sm font-medium"
                      >
                        {{ index + 1 }}
                      </div>
                      <div class="ml-3">
                        <p class="text-sm text-gray-600">
                          {{ step.text }}
                          <a
                            v-if="step.link"
                            :href="step.link"
                            target="_blank"
                            class="text-blue-600 hover:text-blue-800"
                          >
                            {{ step.linkText }}
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex justify-end">
              <button
                v-if="!loading"
                @click="handleSubmit"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                type="submit"
                v-html="buttonText"
              ></button>
              <button
                v-else
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black"
                type="submit"
                disabled
              >
                <svg
                  class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  />
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                {{ formTranslation.common.loading }}
              </button>
            </div>
          </div>
        </b-collapse>
      </div>

      <!-- Google Meet Section -->
      <div
        v-if="userData.addOns.googlemeet"
        class="bg-white rounded-lg shadow-sm overflow-hidden"
      >
        <div class="border-b border-gray-200">
          <button
            v-b-toggle.google-meet-collapse
            class="w-full px-6 py-4 flex items-center justify-between bg-white hover:bg-gray-50 transition-colors duration-150"
          >
            <div class="flex items-center space-x-3">
              <div class="bg-black p-2 rounded-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-white"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"
                  />
                </svg>
              </div>
              <span class="font-medium text-gray-900">{{
                formTranslation.googlemeet.googlemeet
              }}</span>
            </div>
            <svg
              class="h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>

        <b-collapse
          id="google-meet-collapse"
          :visible="userData.addOns.googlemeet"
          accordion="my-accordion"
          role="tabpanel"
        >
          <div class="p-6">
            <!-- Not Connected State -->
            <div
              v-if="userData.is_enable_doctor_gmeet == 'off'"
              class="bg-gray-50 rounded-lg p-6"
            >
              <div
                class="flex flex-col md:flex-row justify-between items-center gap-4"
              >
                <div>
                  <p
                    v-if="userData.is_enable_googleMeet == 'off'"
                    class="text-red-600 font-medium"
                  >
                    {{ formTranslation.common.please_enable_google_meet }}
                  </p>
                  <p v-else class="font-medium text-gray-900">
                    {{
                      formTranslation.common
                        .please_connect_google_meet_automatically
                    }}
                  </p>
                </div>
                <button
                  @click="connect"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                  :disabled="userData.is_enable_googleMeet == 'off'"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                    />
                  </svg>
                  {{ formTranslation.pro_setting.connect_with_google }}
                </button>
              </div>
            </div>

            <!-- Connected State -->
            <div v-else class="bg-green-50 rounded-lg p-6">
              <div
                class="flex flex-col md:flex-row justify-between items-center gap-4"
              >
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div
                      class="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center"
                    >
                      <svg
                        class="h-6 w-6 text-green-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <p
                      v-if="userData.is_enable_googleMeet == 'off'"
                      class="text-red-600 font-medium"
                    >
                      {{ formTranslation.common.please_enable_google_meet }}
                    </p>
                    <p v-else class="font-medium text-gray-900">
                      {{ formTranslation.common.connected_with_google_meet }}
                    </p>
                    <p class="text-sm text-gray-500 mt-1">
                      Your account is successfully connected with Google Meet
                    </p>
                  </div>
                </div>
                <button
                  @click="closeConnection"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                  :disabled="userData.is_enable_googleMeet == 'off'"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  {{ formTranslation.common.disconnect }}
                </button>
              </div>
            </div>
          </div>
        </b-collapse>
      </div>
    </div>
  </div>
</template>
<script>
import { required } from "vuelidate/lib/validators";
import { validateForm } from "../../config/helper";
import { post, get, _axios_post } from "../../config/request";

export default {
  name: "ZoomConfiguration",
  validations: {
    configurationData: {
      api_key: { required },
      api_secret: { required },
      // video_price:{required}
    },

    zoomConfigData: {
      redirect_url: { required },
      client_id: { required },
      client_secret: { required },
    },

    googlemeet: {
      // video_price:{required}
    },
  },
  data: () => {
    return {
      buttonText: '<i class="fa fa-save"></i> Save configuration',
      configurationData: {
        enableTeleMed: false,
      },
      googleMeet: {
        enableTeleMed: false,
      },
      loading: false,
      submitted: false,
      cardTitle: "Telemed configuration",
      showLogin: false,
      data: {},
      disconnect: false,
      text: "Anim pariatur asdadsadsadasd asdasdasdasasd asdadasd",
      isTabActive: {
        zoomCollapse: true,
        googleMeetCollapse: false,
      },
      formLoader: true,
      googlemeetloading: false,
      googlemeetSubmitted: false,
      googlemeet: {
        video_price: 0,
        telemed_service_id: 0,
        doctor_id: 0,
      },
      zoomSignInImage: "",
      zoomOauthAuthorizationCode: null,
      from: window.location.href + "?",
      zoom_telemedloading: false,
      zoomTelemedSubmitted: false,
      zoom_telemed: {
        video_price: 0,
        telemed_service_id: 0,
        doctor_id: 0,
      },
      zoomConfigData: {
        enableCal: false,
        redirect_url: "",
        client_id: "",
        client_secret: "",
      },
    };
  },
  mounted() {
    if (!["doctor"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.init();
    this.telemedCollapseArrow();
    this.isTabActive.googleMeetCollapse =
      this.userData.addOns.googlemeet && !this.userData.addOns.telemed;
    this.isTabActive.zoomCollapse = this.userData.addOns.telemed;
    this.googlemeet = {
      video_price: this.userData.doctor_telemed_price,
      telemed_service_id: this.userData.telemed_service_id,
      doctor_id: this.userData.ID,
    };
    this.getZoomTelemedConfig();
  },
  methods: {
    telemedCollapseArrow() {
      this.$root.$on("bv::collapse::state", (collapseId, isJustShown) => {
        if (collapseId === "zoom-collapse") {
          if (isJustShown) {
            this.isTabActive.zoomCollapse = true;
          } else {
            this.isTabActive.zoomCollapse = false;
          }
        } else {
          if (isJustShown) {
            this.isTabActive.googleMeetCollapse = true;
          } else {
            this.isTabActive.googleMeetCollapse = false;
          }
        }
      });
    },
    zoomStatusChangeEvent(value) {
      if (value && this.userData.is_enable_doctor_gmeet == "on") {
        this.$swal
          .fire({
            title: "Enable Zoom",
            text: "Note: You can use one meeting service at the time. We are disabling Google Meet service.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              this.googleMeet.enableTeleMed = false;
            } else {
              this.configurationData.enableTeleMed = false;
            }
          });
      }
    },
    gmeetStatusChange(value) {
      if (value && this.configurationData.enableTeleMed) {
        this.$swal
          .fire({
            title: "Enable Google Meet",
            text: "Note: You can use one meeting service at the time. We are disabling Zoom Service.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              this.configurationData.enableTeleMed = false;
            } else {
              this.googleMeet.enableTeleMed = false;
            }
          });
      }
    },
    start() {
      const id = this.userData;
      let calendarConfig = {
        client_id: id.googlemeet_client_id,
        scope: "https://www.googleapis.com/auth/calendar",
      };
      if (id.googlemeet_app_name) {
        calendarConfig.plugin_name = id.googlemeet_app_name;
      }
      gapi.load("auth2", function () {
        var auth2 = gapi.auth2.init(calendarConfig).then((response) => {});
      });
    },
    init: function () {
      this.zoomSignInImage =
        window.pluginBASEURL + "assets/images/logo-zoom-blue.svg";

      this.configurationData = this.defaultConfigurationData();
      this.getConfigurationData();
      this.start();
    },
    connectGoogle: function () {
      post("get_all_lang_option", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getConfigurationData: function () {
      this.formLoader = true;
      get("get_doctor_zoom_configuration", {
        user_id: this.userId,
      })
        .then((response) => {
          this.formLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.configurationData = response.data.data;
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          this.formLoader = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    defaultConfigurationData: function () {
      return {
        api_key: "",
        api_secret: "",
        doctor_id: "",
        enableTeleMed: false,
        video_price: 0,
      };
    },
    handleSubmit: function () {
      this.loading = true;
      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();

      if (this.$v.configurationData.$invalid) {
        this.loading = false;
        return;
      }

      if (validateForm("configurationDataForm")) {
        this.configurationData.doctor_id = this.userId;

        let configRequest = {
          api_key: this.configurationData.api_key,
          api_secret: this.configurationData.api_secret,
          doctor_id: this.configurationData.doctor_id,
          enableTeleMed: this.configurationData.enableTeleMed,
          video_price: this.configurationData.video_price,
        };

        post("save_doctor_zoom_configuration", configRequest)
          .then((response) => {
            this.loading = false;
            this.submitted = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.$store.dispatch("userDataModule/fetchUserData");
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            this.submitted = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    connect() {
      if (this.configurationData.enableTeleMed) {
        this.$swal
          .fire({
            title: "Enable Google Meet",
            text: "Note: You can use one meeting service at the time. We are disabling Zoom Service.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              this.googleConnetct();
              this.configurationData.enableTeleMed = false;
            } else {
              this.googleMeet.enableTeleMed = false;
            }
          });
      } else {
        this.googleConnetct();
      }
    },
    googleConnetct() {
      let calendarConfig_new = {
        client_id: this.userData.googlemeet_client_id,
        scope: "https://www.googleapis.com/auth/calendar",
      };
      if (this.userData.googlemeet_app_name) {
        calendarConfig_new.plugin_name = this.userData.googlemeet_app_name;
      }
      var auth2 = gapi.auth2.init(calendarConfig_new);

      auth2.grantOfflineAccess().then(this.signInCallback);
    },
    closeConnection() {
      let doctor_id = this.userId;
      post("diconnect_meet_doctor", { doctor_id: doctor_id })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.dispatch("userDataModule/fetchUserData", {});
            this.disconnect = false;
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    signInCallback(authResult) {
      if (authResult["code"]) {
        let doctor_id = this.userId;
        let _this = this;
        $.ajax({
          url: window.request_data.ajaxurl,
          type: "POST",
          data: {
            route_name: "connect_meet_doctor",
            doctor_id: doctor_id,
            _ajax_nonce: window.request_data.nonce,
            code: authResult["code"],
            action: "ajax_post",
          },
          success: function (data) {
            // data = JSON.parse(data);
            if (data.status !== undefined && data.status === true) {
              _this.$store.dispatch("userDataModule/fetchUserData", {});
              _this.disconnect = true;
              displayMessage(data.message);
            }
          },
        });

        // post("connect_meet_doctor", {doctor_id: doctor_id,code:authResult['code']})
        //   .then((response) => {
        //     if (
        //         response.data.status !== undefined &&
        //         response.data.status === true
        //     ) {
        //       this.$store.dispatch("userDataModule/fetchUserData", {});
        //       this.disconnect = true;
        //       displayMessage(response.data.message);
        //     }
        //   })
        //   .catch((error) => {
        //     console.log(error);
        //     displayErrorMessage(this.formTranslation.widgets.record_not_found);
        //   });
      } else {
        console.log("error");
      }
    },
    connectZoomOauth: function (from) {
      let zoom_telemed_client_id = this.zoomConfigData.client_id;
      let zoom_telemed_setting_redirect_url = this.zoomConfigData.redirect_url;

      const popup = window.open(
        "https://zoom.us/oauth/authorize?client_id=" +
          zoom_telemed_client_id +
          "&response_type=code&redirect_uri=" +
          zoom_telemed_setting_redirect_url,
        "oauth",
        "width=600,height=600"
      );
      const this_ = this;
      var interval = setInterval(function () {
        try {
          const urlParams = new URLSearchParams(popup.location.search);
          let code = urlParams.get("code");
          if (code != undefined) {
            this_.zoomOauthAuthorizationCode = code;
            popup.close();
          }
        } catch (error) {
          // console.log(error.message);
        }

        if (popup.closed) {
          clearInterval(interval);
        }
      }, 500);
    },
    closeConnectionZoomOauth: function () {
      let _this = this;
      get("disconnect_doctor_zoom_oauth", {
        authentication_token: this.zoomOauthAuthorizationCode,
      }).then(function (data) {
        displayMessage(data.data.data.message);
        _this.$store.dispatch("userDataModule/fetchUserData");
      });
    },
    setDoctorZoomOauthToken: function () {
      this.$v.$touch();
      this.loading = true;

      if (this.$v.zoomConfigData.$invalid) {
        this.loading = false;
        return;
      }

      let _this = this;
      post("generate_doctor_zoom_oauth_token", {
        code: this.zoomOauthAuthorizationCode,
        grant_type: "authorization_code",
      }).then(function (data) {
        displayMessage(data.data.data.message);
        _this.loading = false;
        _this.$store.dispatch("userDataModule/fetchUserData");
      });
    },
    handleCalenderSubmit: function () {
      if (this.userData.addOns.telemed != true) {
        return;
      }
      this.zoomTelemedSubmitted = true;

      this.loading = true;

      if (this.$v.zoomConfigData.$invalid) {
        this.loading = false;
        return;
      }

      post("zoom_telemed_save_oauth_config", this.zoomConfigData)
        .then((response) => {
          this.zoomTelemedSubmitted = false;
          if (
            response.data.success !== undefined &&
            response.data.success === true
          ) {
            displayMessage(response.data.data.message);
          } else {
            displayErrorMessage(response.data.data.message);
          }
          this.loading = false;
        })
        .catch((error) => {
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getZoomTelemedConfig: function () {
      this.formLoader = true;
      get("get_zoom_telemed_config", {})
        .then((response) => {
          if (
            response.data.success !== undefined &&
            response.data.success === true
          ) {
            this.zoomConfigData = response.data.data;
          }

          this.formLoader = false;
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
        });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    userId() {
      return this.$store.state.userDataModule.user.ID;
    },
    teleMedStatus() {
      return this.$store.state.userDataModule.user.teleMedStatus;
    },
  },
  watch: {
    zoomOauthAuthorizationCode() {
      if (this.zoomOauthAuthorizationCode.length > 0) {
        this.setDoctorZoomOauthToken();
      }
    },
  },
};
</script>
