/*! For license information please see 6.js.LICENSE.txt */
(window.webpackJsonp=window.webpackJsonp||[]).push([[6],{780:function(e,t,n){e.exports=function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:r}=Object;let{freeze:i,seal:a,create:l}=Object,{apply:c,construct:s}="undefined"!=typeof Reflect&&Reflect;i||(i=function(e){return e}),a||(a=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),s||(s=function(e,t){return new e(...t)});const u=w(Array.prototype.forEach),m=w(Array.prototype.lastIndexOf),p=w(Array.prototype.pop),f=w(Array.prototype.push),d=w(Array.prototype.splice),h=w(String.prototype.toLowerCase),g=w(String.prototype.toString),T=w(String.prototype.match),y=w(String.prototype.replace),E=w(String.prototype.indexOf),A=w(String.prototype.trim),_=w(Object.prototype.hasOwnProperty),S=w(RegExp.prototype.test),N=(b=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s(b,t)});var b;function w(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return c(e,t,o)}}function R(e,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:h;t&&t(e,null);let i=o.length;for(;i--;){let t=o[i];if("string"==typeof t){const e=r(t);e!==t&&(n(o)||(o[i]=e),t=e)}e[t]=!0}return e}function O(e){for(let t=0;t<e.length;t++)_(e,t)||(e[t]=null);return e}function D(t){const n=l(null);for(const[o,r]of e(t))_(t,o)&&(Array.isArray(r)?n[o]=O(r):r&&"object"==typeof r&&r.constructor===Object?n[o]=D(r):n[o]=r);return n}function L(e,t){for(;null!==e;){const n=r(e,t);if(n){if(n.get)return w(n.get);if("function"==typeof n.value)return w(n.value)}e=o(e)}return function(){return null}}const v=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),C=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),x=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),k=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),I=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),M=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),U=i(["#text"]),z=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),P=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),H=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),F=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),B=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),W=a(/<%[\w\W]*|[\w\W]*%>/gm),G=a(/\$\{[\w\W]*/gm),Y=a(/^data-[\-\w.\u00B7-\uFFFF]+$/),j=a(/^aria-[\-\w]+$/),X=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),q=a(/^(?:\w+script|data):/i),$=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),K=a(/^html$/i),V=a(/^[a-z][.\w]*(-[.\w]+)+$/i);var J=Object.freeze({__proto__:null,ARIA_ATTR:j,ATTR_WHITESPACE:$,CUSTOM_ELEMENT:V,DATA_ATTR:Y,DOCTYPE_NAME:K,ERB_EXPR:W,IS_ALLOWED_URI:X,IS_SCRIPT_OR_DATA:q,MUSTACHE_EXPR:B,TMPLIT_EXPR:G});const Z=function(){return"undefined"==typeof window?null:window},Q=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;t&&t.hasAttribute("data-tt-policy-suffix")&&(n=t.getAttribute("data-tt-policy-suffix"));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return null}};return function t(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Z();const o=e=>t(e);if(o.version="3.2.5",o.removed=[],!n||!n.document||9!==n.document.nodeType||!n.Element)return o.isSupported=!1,o;let{document:r}=n;const a=r,c=a.currentScript,{DocumentFragment:s,HTMLTemplateElement:b,Node:w,Element:O,NodeFilter:B,NamedNodeMap:W=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:G,DOMParser:Y,trustedTypes:j}=n,q=O.prototype,$=L(q,"cloneNode"),V=L(q,"remove"),ee=L(q,"nextSibling"),te=L(q,"childNodes"),ne=L(q,"parentNode");if("function"==typeof b){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let oe,re="";const{implementation:ie,createNodeIterator:ae,createDocumentFragment:le,getElementsByTagName:ce}=r,{importNode:se}=a;let ue={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof e&&"function"==typeof ne&&ie&&void 0!==ie.createHTMLDocument;const{MUSTACHE_EXPR:me,ERB_EXPR:pe,TMPLIT_EXPR:fe,DATA_ATTR:de,ARIA_ATTR:he,IS_SCRIPT_OR_DATA:ge,ATTR_WHITESPACE:Te,CUSTOM_ELEMENT:ye}=J;let{IS_ALLOWED_URI:Ee}=J,Ae=null;const _e=R({},[...v,...C,...x,...I,...U]);let Se=null;const Ne=R({},[...z,...P,...H,...F]);let be=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),we=null,Re=null,Oe=!0,De=!0,Le=!1,ve=!0,Ce=!1,xe=!0,ke=!1,Ie=!1,Me=!1,Ue=!1,ze=!1,Pe=!1,He=!0,Fe=!1;const Be="user-content-";let We=!0,Ge=!1,Ye={},je=null;const Xe=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let qe=null;const $e=R({},["audio","video","img","source","image","track"]);let Ke=null;const Ve=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Je="http://www.w3.org/1998/Math/MathML",Ze="http://www.w3.org/2000/svg",Qe="http://www.w3.org/1999/xhtml";let et=Qe,tt=!1,nt=null;const ot=R({},[Je,Ze,Qe],g);let rt=R({},["mi","mo","mn","ms","mtext"]),it=R({},["annotation-xml"]);const at=R({},["title","style","font","a","script"]);let lt=null;const ct=["application/xhtml+xml","text/html"],st="text/html";let ut=null,mt=null;const pt=r.createElement("form"),ft=function(e){return e instanceof RegExp||e instanceof Function},dt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!mt||mt!==e){if(e&&"object"==typeof e||(e={}),e=D(e),lt=-1===ct.indexOf(e.PARSER_MEDIA_TYPE)?st:e.PARSER_MEDIA_TYPE,ut="application/xhtml+xml"===lt?g:h,Ae=_(e,"ALLOWED_TAGS")?R({},e.ALLOWED_TAGS,ut):_e,Se=_(e,"ALLOWED_ATTR")?R({},e.ALLOWED_ATTR,ut):Ne,nt=_(e,"ALLOWED_NAMESPACES")?R({},e.ALLOWED_NAMESPACES,g):ot,Ke=_(e,"ADD_URI_SAFE_ATTR")?R(D(Ve),e.ADD_URI_SAFE_ATTR,ut):Ve,qe=_(e,"ADD_DATA_URI_TAGS")?R(D($e),e.ADD_DATA_URI_TAGS,ut):$e,je=_(e,"FORBID_CONTENTS")?R({},e.FORBID_CONTENTS,ut):Xe,we=_(e,"FORBID_TAGS")?R({},e.FORBID_TAGS,ut):{},Re=_(e,"FORBID_ATTR")?R({},e.FORBID_ATTR,ut):{},Ye=!!_(e,"USE_PROFILES")&&e.USE_PROFILES,Oe=!1!==e.ALLOW_ARIA_ATTR,De=!1!==e.ALLOW_DATA_ATTR,Le=e.ALLOW_UNKNOWN_PROTOCOLS||!1,ve=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Ce=e.SAFE_FOR_TEMPLATES||!1,xe=!1!==e.SAFE_FOR_XML,ke=e.WHOLE_DOCUMENT||!1,Ue=e.RETURN_DOM||!1,ze=e.RETURN_DOM_FRAGMENT||!1,Pe=e.RETURN_TRUSTED_TYPE||!1,Me=e.FORCE_BODY||!1,He=!1!==e.SANITIZE_DOM,Fe=e.SANITIZE_NAMED_PROPS||!1,We=!1!==e.KEEP_CONTENT,Ge=e.IN_PLACE||!1,Ee=e.ALLOWED_URI_REGEXP||X,et=e.NAMESPACE||Qe,rt=e.MATHML_TEXT_INTEGRATION_POINTS||rt,it=e.HTML_INTEGRATION_POINTS||it,be=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(be.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(be.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(be.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ce&&(De=!1),ze&&(Ue=!0),Ye&&(Ae=R({},U),Se=[],!0===Ye.html&&(R(Ae,v),R(Se,z)),!0===Ye.svg&&(R(Ae,C),R(Se,P),R(Se,F)),!0===Ye.svgFilters&&(R(Ae,x),R(Se,P),R(Se,F)),!0===Ye.mathMl&&(R(Ae,I),R(Se,H),R(Se,F))),e.ADD_TAGS&&(Ae===_e&&(Ae=D(Ae)),R(Ae,e.ADD_TAGS,ut)),e.ADD_ATTR&&(Se===Ne&&(Se=D(Se)),R(Se,e.ADD_ATTR,ut)),e.ADD_URI_SAFE_ATTR&&R(Ke,e.ADD_URI_SAFE_ATTR,ut),e.FORBID_CONTENTS&&(je===Xe&&(je=D(je)),R(je,e.FORBID_CONTENTS,ut)),We&&(Ae["#text"]=!0),ke&&R(Ae,["html","head","body"]),Ae.table&&(R(Ae,["tbody"]),delete we.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');oe=e.TRUSTED_TYPES_POLICY,re=oe.createHTML("")}else void 0===oe&&(oe=Q(j,c)),null!==oe&&"string"==typeof re&&(re=oe.createHTML(""));i&&i(e),mt=e}},ht=R({},[...C,...x,...k]),gt=R({},[...I,...M]),Tt=function(e){let t=ne(e);t&&t.tagName||(t={namespaceURI:et,tagName:"template"});const n=h(e.tagName),o=h(t.tagName);return!!nt[e.namespaceURI]&&(e.namespaceURI===Ze?t.namespaceURI===Qe?"svg"===n:t.namespaceURI===Je?"svg"===n&&("annotation-xml"===o||rt[o]):Boolean(ht[n]):e.namespaceURI===Je?t.namespaceURI===Qe?"math"===n:t.namespaceURI===Ze?"math"===n&&it[o]:Boolean(gt[n]):e.namespaceURI===Qe?!(t.namespaceURI===Ze&&!it[o])&&!(t.namespaceURI===Je&&!rt[o])&&!gt[n]&&(at[n]||!ht[n]):!("application/xhtml+xml"!==lt||!nt[e.namespaceURI]))},yt=function(e){f(o.removed,{element:e});try{ne(e).removeChild(e)}catch(t){V(e)}},Et=function(e,t){try{f(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){f(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Ue||ze)try{yt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},At=function(e){let t=null,n=null;if(Me)e="<remove></remove>"+e;else{const t=T(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===lt&&et===Qe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=oe?oe.createHTML(e):e;if(et===Qe)try{t=(new Y).parseFromString(o,lt)}catch(e){}if(!t||!t.documentElement){t=ie.createDocument(et,"template",null);try{t.documentElement.innerHTML=tt?re:o}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),et===Qe?ce.call(t,ke?"html":"body")[0]:ke?t.documentElement:i},_t=function(e){return ae.call(e.ownerDocument||e,e,B.SHOW_ELEMENT|B.SHOW_COMMENT|B.SHOW_TEXT|B.SHOW_PROCESSING_INSTRUCTION|B.SHOW_CDATA_SECTION,null)},St=function(e){return e instanceof G&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof W)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Nt=function(e){return"function"==typeof w&&e instanceof w};function bt(e,t,n){u(e,e=>{e.call(o,t,n,mt)})}const wt=function(e){let t=null;if(bt(ue.beforeSanitizeElements,e,null),St(e))return yt(e),!0;const n=ut(e.nodeName);if(bt(ue.uponSanitizeElement,e,{tagName:n,allowedTags:Ae}),e.hasChildNodes()&&!Nt(e.firstElementChild)&&S(/<[/\w!]/g,e.innerHTML)&&S(/<[/\w!]/g,e.textContent))return yt(e),!0;if(7===e.nodeType)return yt(e),!0;if(xe&&8===e.nodeType&&S(/<[/\w]/g,e.data))return yt(e),!0;if(!Ae[n]||we[n]){if(!we[n]&&Ot(n)){if(be.tagNameCheck instanceof RegExp&&S(be.tagNameCheck,n))return!1;if(be.tagNameCheck instanceof Function&&be.tagNameCheck(n))return!1}if(We&&!je[n]){const t=ne(e)||e.parentNode,n=te(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=$(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,ee(e))}}return yt(e),!0}return e instanceof O&&!Tt(e)?(yt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!S(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ce&&3===e.nodeType&&(t=e.textContent,u([me,pe,fe],e=>{t=y(t,e," ")}),e.textContent!==t&&(f(o.removed,{element:e.cloneNode()}),e.textContent=t)),bt(ue.afterSanitizeElements,e,null),!1):(yt(e),!0)},Rt=function(e,t,n){if(He&&("id"===t||"name"===t)&&(n in r||n in pt))return!1;if(De&&!Re[t]&&S(de,t));else if(Oe&&S(he,t));else if(!Se[t]||Re[t]){if(!(Ot(e)&&(be.tagNameCheck instanceof RegExp&&S(be.tagNameCheck,e)||be.tagNameCheck instanceof Function&&be.tagNameCheck(e))&&(be.attributeNameCheck instanceof RegExp&&S(be.attributeNameCheck,t)||be.attributeNameCheck instanceof Function&&be.attributeNameCheck(t))||"is"===t&&be.allowCustomizedBuiltInElements&&(be.tagNameCheck instanceof RegExp&&S(be.tagNameCheck,n)||be.tagNameCheck instanceof Function&&be.tagNameCheck(n))))return!1}else if(Ke[t]);else if(S(Ee,y(n,Te,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==E(n,"data:")||!qe[e])if(Le&&!S(ge,y(n,Te,"")));else if(n)return!1;return!0},Ot=function(e){return"annotation-xml"!==e&&T(e,ye)},Dt=function(e){bt(ue.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||St(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Se,forceKeepAttr:void 0};let r=t.length;for(;r--;){const i=t[r],{name:a,namespaceURI:l,value:c}=i,s=ut(a);let m="value"===a?c:A(c);if(n.attrName=s,n.attrValue=m,n.keepAttr=!0,n.forceKeepAttr=void 0,bt(ue.uponSanitizeAttribute,e,n),m=n.attrValue,!Fe||"id"!==s&&"name"!==s||(Et(a,e),m=Be+m),xe&&S(/((--!?|])>)|<\/(style|title)/i,m)){Et(a,e);continue}if(n.forceKeepAttr)continue;if(Et(a,e),!n.keepAttr)continue;if(!ve&&S(/\/>/i,m)){Et(a,e);continue}Ce&&u([me,pe,fe],e=>{m=y(m,e," ")});const f=ut(e.nodeName);if(Rt(f,s,m)){if(oe&&"object"==typeof j&&"function"==typeof j.getAttributeType)if(l);else switch(j.getAttributeType(f,s)){case"TrustedHTML":m=oe.createHTML(m);break;case"TrustedScriptURL":m=oe.createScriptURL(m)}try{l?e.setAttributeNS(l,a,m):e.setAttribute(a,m),St(e)?yt(e):p(o.removed)}catch(e){}}}bt(ue.afterSanitizeAttributes,e,null)},Lt=function e(t){let n=null;const o=_t(t);for(bt(ue.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)bt(ue.uponSanitizeShadowNode,n,null),wt(n),Dt(n),n.content instanceof s&&e(n.content);bt(ue.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,i=null,l=null;if(tt=!e,tt&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Nt(e)){if("function"!=typeof e.toString)throw N("toString is not a function");if("string"!=typeof(e=e.toString()))throw N("dirty is not a string, aborting")}if(!o.isSupported)return e;if(Ie||dt(t),o.removed=[],"string"==typeof e&&(Ge=!1),Ge){if(e.nodeName){const t=ut(e.nodeName);if(!Ae[t]||we[t])throw N("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof w)n=At("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),1===r.nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?n=r:n.appendChild(r);else{if(!Ue&&!Ce&&!ke&&-1===e.indexOf("<"))return oe&&Pe?oe.createHTML(e):e;if(n=At(e),!n)return Ue?null:Pe?re:""}n&&Me&&yt(n.firstChild);const c=_t(Ge?e:n);for(;i=c.nextNode();)wt(i),Dt(i),i.content instanceof s&&Lt(i.content);if(Ge)return e;if(Ue){if(ze)for(l=le.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(Se.shadowroot||Se.shadowrootmode)&&(l=se.call(a,l,!0)),l}let m=ke?n.outerHTML:n.innerHTML;return ke&&Ae["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&S(K,n.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+m),Ce&&u([me,pe,fe],e=>{m=y(m,e," ")}),oe&&Pe?oe.createHTML(m):m},o.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};dt(e),Ie=!0},o.clearConfig=function(){mt=null,Ie=!1},o.isValidAttribute=function(e,t,n){mt||dt({});const o=ut(e),r=ut(t);return Rt(o,r,n)},o.addHook=function(e,t){"function"==typeof t&&f(ue[e],t)},o.removeHook=function(e,t){if(void 0!==t){const n=m(ue[e],t);return-1===n?void 0:d(ue[e],n,1)[0]}return p(ue[e])},o.removeHooks=function(e){ue[e]=[]},o.removeAllHooks=function(){ue={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}()}()}}]);