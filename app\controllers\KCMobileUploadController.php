<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use WP_REST_Controller;
use WP_REST_Server;
use WP_REST_Response;
use WP_Error;

class KCMobileUploadController extends WP_REST_Controller
{
    protected $namespace = 'kivicare/api/v1';
    
    /**
     * Constructor
     */
    public function __construct()
    {
        add_action('rest_api_init', [$this, 'register_routes']);
    }

    /**
     * Register routes
     */
    public function register_routes()
    {
        register_rest_route(
            $this->namespace,
            '/patient-mobile-upload/(?P<session_id>[a-zA-Z0-9_-]+)',
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'render_upload_page'],
                'permission_callback' => [$this, 'permissions_check'],
                'args'                => [
                    'session_id' => [
                        'required'          => true,
                        'validate_callback' => function($param) {
                            return is_string($param) && !empty($param);
                        }
                    ],
                ],
            ]
        );

        // Also register a route for handling the file uploads via AJAX
        register_rest_route(
            $this->namespace,
            '/patient-mobile-upload/upload',
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'handle_upload'],
                'permission_callback' => [$this, 'permissions_check'],
            ]
        );
    }

    /**
     * Permission check
     * 
     * @param \WP_REST_Request $request
     * @return bool
     */
    public function permissions_check($request)
    {
        // Allow access to all for the mobile upload page
        return true;
    }

    /**
     * Render the upload page
     * 
     * @param \WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function render_upload_page($request)
    {
        $session_id = $request->get_param('session_id');
        
        // Get session data from transient
        $session_data = get_transient('kc_mobile_upload_' . $session_id);
        
        if (!$session_data) {
            return new WP_Error(
                'session_not_found',
                'Upload session not found or expired. Please request a new QR code.',
                ['status' => 404]
            );
        }
        
        // Check if session is expired
        if (time() > $session_data['expires_at']) {
            delete_transient('kc_mobile_upload_' . $session_id);
            return new WP_Error(
                'session_expired',
                'Upload session has expired. Please request a new QR code.',
                ['status' => 410]
            );
        }

        // Set up content type for HTML
        header('Content-Type: text/html; charset=UTF-8');
        
        // Include the template
        ob_start();
        include_once(KIVI_CARE_DIR . 'templates/mobile-upload-rest.php');
        $content = ob_get_clean();
        
        // Return HTML content
        return new WP_REST_Response($content, 200, [
            'Content-Type' => 'text/html; charset=UTF-8'
        ]);
    }

    /**
     * Handle file upload
     * 
     * @param \WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function handle_upload($request)
    {
        $session_id = $request->get_param('session_id');
        
        // Get session data from transient
        $session_data = get_transient('kc_mobile_upload_' . $session_id);
        
        if (!$session_data) {
            return new WP_Error(
                'session_not_found',
                'Upload session not found or expired',
                ['status' => 404]
            );
        }
        
        // Check if session is expired
        if (time() > $session_data['expires_at']) {
            delete_transient('kc_mobile_upload_' . $session_id);
            return new WP_Error(
                'session_expired',
                'Upload session has expired',
                ['status' => 410]
            );
        }

        // Handle file upload or camera capture
        $patient_id = intval($session_data['patient_id']);
        $encounter_id = intval($session_data['encounter_id']);
        
        $result = [
            'success' => false,
            'message' => 'No file data received'
        ];
        
        // Handle file upload
        if (!empty($_FILES['document'])) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            
            $file_id = media_handle_upload('document', 0);
            
            if (is_wp_error($file_id)) {
                return new WP_Error(
                    'upload_error',
                    $file_id->get_error_message(),
                    ['status' => 400]
                );
            }
            
            $doc_name = isset($_POST['document_name']) ? sanitize_text_field($_POST['document_name']) : pathinfo($_FILES['document']['name'], PATHINFO_FILENAME);
            $doc_type = isset($_POST['document_type']) ? sanitize_text_field($_POST['document_type']) : 'medical_report';
            $doc_description = isset($_POST['document_description']) ? sanitize_textarea_field($_POST['document_description']) : '';
            
            global $wpdb;
            
            $insert_result = $wpdb->insert($wpdb->prefix . 'kc_patient_document', [
                'name' => $doc_name,
                'type' => $doc_type,
                'description' => $doc_description,
                'patient_id' => $patient_id,
                'document_id' => $file_id,
                'appointment_id' => $encounter_id,
                'created_by' => 1,
                'created_at' => current_time('mysql')
            ]);
            
            if ($insert_result) {
                // Update session data with new upload
                $session_data['uploads'][] = [
                    'id' => $wpdb->insert_id,
                    'name' => $doc_name,
                    'type' => $doc_type,
                    'uploaded_at' => current_time('mysql')
                ];
                set_transient('kc_mobile_upload_' . $session_id, $session_data, $session_data['expires_at'] - time());
                
                $result = [
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'file_id' => $wpdb->insert_id,
                    'document_url' => wp_get_attachment_url($file_id)
                ];
            } else {
                wp_delete_attachment($file_id, true);
                $result = [
                    'success' => false,
                    'message' => 'Error saving document to patient record'
                ];
            }
        }
        // Handle audio data
        else if (isset($_POST['audio_data'])) {
            $audio_data = $_POST['audio_data'];
            
            // Remove the data URL prefix
            $audio_data = str_replace('data:audio/webm;base64,', '', $audio_data);
            $audio_data = str_replace('data:audio/mp4;base64,', '', $audio_data);
            $audio_data = str_replace('data:audio/wav;base64,', '', $audio_data);
            $audio_data = str_replace('data:audio/ogg;base64,', '', $audio_data);
            $audio_data = str_replace(' ', '+', $audio_data);
            
            // Decode the base64 data
            $decoded_audio = base64_decode($audio_data);
            
            if ($decoded_audio === false) {
                return new WP_Error(
                    'decode_error',
                    'Error decoding audio data',
                    ['status' => 400]
                );
            }
            
            $upload_dir = wp_upload_dir();
            $doc_name = isset($_POST['document_name']) ? sanitize_text_field($_POST['document_name']) : 'Audio Recording ' . date('Y-m-d H:i:s');
            $doc_type = isset($_POST['document_type']) ? sanitize_text_field($_POST['document_type']) : 'medical_audio';
            $doc_description = isset($_POST['document_description']) ? sanitize_textarea_field($_POST['document_description']) : 'Voice recording';
            
            // Generate a unique filename
            $filename = 'audio_recording_' . time() . '.wav';
            $upload_path = $upload_dir['path'] . '/' . $filename;
            $upload_url = $upload_dir['url'] . '/' . $filename;
            
            // Save the file
            $save_result = file_put_contents($upload_path, $decoded_audio);
            
            if ($save_result === false) {
                return new WP_Error(
                    'save_error',
                    'Error saving audio recording',
                    ['status' => 500]
                );
            }
            
            // Create an attachment
            $attachment = [
                'guid' => $upload_url,
                'post_mime_type' => 'audio/wav',
                'post_title' => $doc_name,
                'post_content' => '',
                'post_status' => 'inherit'
            ];
            
            $file_id = wp_insert_attachment($attachment, $upload_path);
            
            if (is_wp_error($file_id)) {
                return new WP_Error(
                    'attachment_error',
                    $file_id->get_error_message(),
                    ['status' => 500]
                );
            }
            
            // Generate attachment metadata
            $attachment_data = wp_generate_attachment_metadata($file_id, $upload_path);
            wp_update_attachment_metadata($file_id, $attachment_data);
            
            global $wpdb;
            
            $insert_result = $wpdb->insert($wpdb->prefix . 'kc_patient_document', [
                'name' => $doc_name,
                'type' => $doc_type,
                'description' => $doc_description,
                'patient_id' => $patient_id,
                'document_id' => $file_id,
                'appointment_id' => $encounter_id,
                'created_by' => 1,
                'created_at' => current_time('mysql')
            ]);
            
            if ($insert_result) {
                // Update session data with new upload
                $session_data['uploads'][] = [
                    'id' => $wpdb->insert_id,
                    'name' => $doc_name,
                    'type' => $doc_type,
                    'uploaded_at' => current_time('mysql')
                ];
                set_transient('kc_mobile_upload_' . $session_id, $session_data, $session_data['expires_at'] - time());
                
                $result = [
                    'success' => true,
                    'message' => 'Audio recorded and uploaded successfully',
                    'file_id' => $wpdb->insert_id,
                    'document_url' => wp_get_attachment_url($file_id)
                ];
            } else {
                wp_delete_attachment($file_id, true);
                $result = [
                    'success' => false,
                    'message' => 'Error saving audio recording to patient record'
                ];
            }
        }
        // Handle image data
        else if (isset($_POST['image_data'])) {
            $image_data = $_POST['image_data'];
            
            // Remove the data URL prefix
            $image_data = str_replace('data:image/jpeg;base64,', '', $image_data);
            $image_data = str_replace('data:image/png;base64,', '', $image_data);
            $image_data = str_replace(' ', '+', $image_data);
            
            // Decode the base64 data
            $decoded_image = base64_decode($image_data);
            
            if ($decoded_image === false) {
                return new WP_Error(
                    'decode_error',
                    'Error decoding image data',
                    ['status' => 400]
                );
            }
            
            $upload_dir = wp_upload_dir();
            $doc_name = isset($_POST['document_name']) ? sanitize_text_field($_POST['document_name']) : 'Camera Capture ' . date('Y-m-d H:i:s');
            $doc_type = isset($_POST['document_type']) ? sanitize_text_field($_POST['document_type']) : 'medical_report';
            $doc_description = isset($_POST['document_description']) ? sanitize_textarea_field($_POST['document_description']) : '';
            
            // Generate a unique filename
            $filename = 'mobile_capture_' . time() . '.jpg';
            $upload_path = $upload_dir['path'] . '/' . $filename;
            $upload_url = $upload_dir['url'] . '/' . $filename;
            
            // Save the file
            $save_result = file_put_contents($upload_path, $decoded_image);
            
            if ($save_result === false) {
                return new WP_Error(
                    'save_error',
                    'Error saving captured image',
                    ['status' => 500]
                );
            }
            
            // Create an attachment
            $attachment = [
                'guid' => $upload_url,
                'post_mime_type' => 'image/jpeg',
                'post_title' => $doc_name,
                'post_content' => '',
                'post_status' => 'inherit'
            ];
            
            $file_id = wp_insert_attachment($attachment, $upload_path);
            
            if (is_wp_error($file_id)) {
                return new WP_Error(
                    'attachment_error',
                    $file_id->get_error_message(),
                    ['status' => 500]
                );
            }
            
            // Generate attachment metadata
            $attachment_data = wp_generate_attachment_metadata($file_id, $upload_path);
            wp_update_attachment_metadata($file_id, $attachment_data);
            
            global $wpdb;
            
            $insert_result = $wpdb->insert($wpdb->prefix . 'kc_patient_document', [
                'name' => $doc_name,
                'type' => $doc_type,
                'description' => $doc_description,
                'patient_id' => $patient_id,
                'document_id' => $file_id,
                'appointment_id' => $encounter_id,
                'created_by' => 1,
                'created_at' => current_time('mysql')
            ]);
            
            if ($insert_result) {
                // Update session data with new upload
                $session_data['uploads'][] = [
                    'id' => $wpdb->insert_id,
                    'name' => $doc_name,
                    'type' => $doc_type,
                    'uploaded_at' => current_time('mysql')
                ];
                set_transient('kc_mobile_upload_' . $session_id, $session_data, $session_data['expires_at'] - time());
                
                $result = [
                    'success' => true,
                    'message' => 'Image captured and uploaded successfully',
                    'file_id' => $wpdb->insert_id,
                    'document_url' => wp_get_attachment_url($file_id)
                ];
            } else {
                wp_delete_attachment($file_id, true);
                $result = [
                    'success' => false,
                    'message' => 'Error saving document to patient record'
                ];
            }
        }
        
        return new WP_REST_Response($result);
    }
}

// Initialize the controller
new KCMobileUploadController();