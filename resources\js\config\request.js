// Determine base URL for the site, handling subdirectory installations
const getBaseUrl = () => {
    // First check if WordPress has provided a base URL in a global
    if (typeof kivicare_base_url !== 'undefined') {
        return kivicare_base_url;
    }
    
    // Try to determine from the URL path
    const pathParts = window.location.pathname.split('/');
    const baseIndex = pathParts.findIndex(part => part === 'wp-admin' || part === 'wp-content');
    
    if (baseIndex !== -1) {
        // We found wp-admin or wp-content in the URL
        return window.location.origin + pathParts.slice(0, baseIndex).join('/');
    }
    
    // Default to origin if we can't determine subfolder
    return window.location.origin;
};

// Expose the base URL for other parts of the application to use
export const siteBaseUrl = getBaseUrl();

// Create apiCall object for the enhanced booking widget to use
import axios from 'axios';

export const apiCall = {
    get: (endpoint, options = {}) => {
        // Always use the admin-ajax.php endpoint for our widget
        console.log('Enhanced booking widget API GET request for:', endpoint);
        
        // Build parameters from options.params
        const params = options.params || {};
        
        // Special handling for columnFilters, sort, and other objects
        if (params.columnFilters) {
            params.columnFilters = JSON.stringify(params.columnFilters);
        }
        if (params.sort) {
            params.sort = JSON.stringify(params.sort);
        }
        
        params.action = 'ajax_get';
        params.route_name = endpoint;
        
        // Get the nonce from the global variable
        if (window.ajaxData && window.ajaxData.get_nonce) {
            params._ajax_nonce = window.ajaxData.get_nonce;
        }
        
        // Use the WordPress admin-ajax.php endpoint
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        
        // Debug logging
        console.log('Using admin-ajax endpoint:', ajaxurl, 'with params:', params);
        
        return axios.get(ajaxurl, { params });
    },
    post: (endpoint, data = {}, options = {}) => {
        // Always use the admin-ajax.php endpoint for our widget
        console.log('Enhanced booking widget API POST request for:', endpoint);
        
        // Prepare the data
        const postData = { ...data };
        postData.action = 'ajax_post';
        postData.route_name = endpoint;
        
        // Get the nonce from the global variable (use the backend-provided nonce)
        if (window.ajaxData && window.ajaxData.nonce) {
            postData._ajax_nonce = window.ajaxData.nonce;
        } else if (window.ajaxData && window.ajaxData.post_nonce) {
            postData._ajax_nonce = window.ajaxData.post_nonce;
        }
        
        // Use the WordPress admin-ajax.php endpoint
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        
        // Debug logging
        console.log('Using admin-ajax endpoint:', ajaxurl, 'with data:', postData);
        
        return axios.post(ajaxurl, postData);
    }
};

// Following is the base url of requested api
const _clinic_baseUrl = siteBaseUrl + '/wp-json/wp-medical/api/'
// Following is the version of requested api
const apiVersion = "v1"
const _urlGetDoctors = _clinic_baseUrl + apiVersion + '/patient-appointment/get-doctors-data'
const _urlGetAppointmentTypes = _clinic_baseUrl + apiVersion + '/patient-appointment/get-static-data'
export const _patientAppointmentBook = _clinic_baseUrl + apiVersion + '/patient/book-appointment'
export const _patientLogin = _clinic_baseUrl + apiVersion + '/patient-auth/login'
export const _patientRegister = _clinic_baseUrl + apiVersion + '/patient-auth/register'


const _urlGetDoctorDetails = _clinic_baseUrl + apiVersion + '/book-appointment/get-doctors-details'
export const _urlGetTimeSlot = _clinic_baseUrl + apiVersion + '/book-appointment/get-time-slots'
export const _urlSaveAppointment = _clinic_baseUrl + apiVersion + '/book-appointment/save-appointment'

export const post = (route, data = {}, options = {}) => {
    // Handle both the old and new function signature
    let frontEnd = false;
    let headers = { headers: { 'Content-Type': 'application/json' } };
    let signal = null;
    let isFormData = false;
    let onUploadProgress = null;
    
    // Handle old function signature (frontEnd parameter)
    if (typeof options === 'boolean') {
        frontEnd = options;
    } 
    // Handle options object
    else if (typeof options === 'object') {
        frontEnd = options.frontEnd || false;
        if (options.headers) headers = options.headers;
        if (options.signal) signal = options.signal;
        if (options.isFormData) isFormData = options.isFormData;
        if (options.onUploadProgress) onUploadProgress = options.onUploadProgress;
    }

    // Initialize ajaxurl and request_data before using them
    if (!window.ajaxurl || !window.request_data) {
        // Default to admin-ajax.php if not set
        if (typeof window.ajaxData !== 'undefined') {
            window.ajaxurl = window.ajaxData.ajaxurl;
            window.request_data = window.ajaxData;
        } else {
            // Fallback to constructing ajax URL from base
            window.ajaxurl = siteBaseUrl + '/wp-admin/admin-ajax.php';
            window.request_data = window.request_data || { nonce: '' };
        }
    }

    let url = window.ajaxurl;
    if (data.action === undefined) {
        url = window.ajaxurl + '?action=ajax_post';
    }

    if (route === undefined) {
        return false
    }

    // Handle FormData objects differently
    if (data instanceof FormData) {
        isFormData = true;
        data.append('route_name', route);
        data.append('_ajax_nonce', request_data.nonce);
        // Don't set Content-Type for FormData as browser will set it with boundary
        headers = { headers: {} };
    } else if (data.append !== undefined) {
        // Legacy handling for FormData objects
        data.append('route_name', route);
        data.append('_ajax_nonce', request_data.nonce);
    } else {
        // Regular JSON data
        data.route_name = route;
        data._ajax_nonce = window.request_data.nonce;
    }

    // Merge options including the signal if provided
    const requestOptions = {
        ...headers,
        signal: signal
    };

    // Add upload progress if provided
    if (onUploadProgress) {
        requestOptions.onUploadProgress = onUploadProgress;
    }

    // Log info about the request for debugging
    console.log('Request to ' + route + ':', {
        isFormData: isFormData,
        url: url,
        dataType: data instanceof FormData ? 'FormData' : typeof data,
        options: requestOptions
    });

    return new Promise((resolve, reject) => {
        axios.post(url, data, requestOptions)
            .then((data) => {
                if (data.data.status_code !== undefined && data.data.status_code === 403) {
                    displayErrorMessage(data.data.message);
                    // Check if vm is defined before using it
                    if (typeof vm !== 'undefined') {
                        vm.$router.push({ name: "403"});
                    }
                }
                console.log('API response from ' + route + ':', data.data);
                resolve(data)
            })
            .catch((error) => {
                console.error('API error from ' + route + ':', error);
                reject(error)
            });
    })
}

export const get = (route, data={}, frontEnd = false) => {
    
    // Initialize ajaxurl and request_data before using them
    if (!window.ajaxurl || !window.request_data) {
        // Default to admin-ajax.php if not set
        if (typeof window.ajaxData !== 'undefined') {
            window.ajaxurl = window.ajaxData.ajaxurl;
            window.request_data = window.ajaxData;
        } else {
            // Fallback to constructing ajax URL from base
            window.ajaxurl = siteBaseUrl + '/wp-admin/admin-ajax.php';
            window.request_data = window.request_data || { nonce: '', get_nonce: '' };
        }
    }
    
    // Ensure window.request_data is defined
    if (!window.request_data || !window.request_data.get_nonce) {
        console.error('Missing request_data or get_nonce:', window.request_data);
        window.request_data = window.request_data || {};
        window.request_data.get_nonce = window.request_data.get_nonce || '';
    }
    
    data._ajax_nonce = window.request_data.get_nonce;
    let url = window.ajaxurl;
    if (data.action === undefined) {
        url = window.ajaxurl + '?action=ajax_get';
    }

    if (route === undefined) {
        return false
    }

    url = url + '&route_name=' + route;
    // console.log('Making GET request to:', route, data, url);
    
    return new Promise((resolve, reject) => {
        axios.get(url, { params: data })
            .then((data) => {
                if (data.data.status_code !== undefined && data.data.status_code === 403) {
                    displayErrorMessage(data.data.message);
                    if (typeof vm !== 'undefined') {
                        vm.$router.push({ name: "403"});
                    }
                }
                // console.log('GET response from ' + route + ':', data.data);
                resolve(data)
            })
            .catch((error) => {
                console.error('GET error from ' + route + ':', error);
                reject(error)
            });
    })
}

export const _axios_post = (url, data) => {
    return new Promise((resolve, reject) => {
        axios.post(url, data)
            .then((data) => {
                resolve(data)
            })
            .catch((error) => {
                reject(error)
            });
    })
}


export const _getDoctors = () => {
    return new Promise((resolve, reject) => {
        axios.get(_urlGetDoctors)
            .then((data) => {
                resolve(data)
            })
            .catch((error) => {
                reject(error)
            });
    })
}

export const _getDoctorsDetails = () => {
    return new Promise((resolve, reject) => {
        axios.get(_urlGetDoctorDetails)
            .then((data) => {
                resolve(data.data)
            })
            .catch((error) => {
                reject(error)
            });
    })
}

export const _getStaticData = (requestObject) => {
    return new Promise((resolve, reject) => {
        axios.post(_urlGetAppointmentTypes, {
            data_type: requestObject.data_type,
            static_data_type: requestObject.static_data_type
        })
            .then((data) => {
                resolve(data)
            })
            .catch((error) => {
                reject(error)
            });
    })
}
