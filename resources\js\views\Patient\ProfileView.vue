<template>
  <div class="flex flex-wrap">
    <div class="w-full">
      <div class="">
        <!-- Header -->
        <header class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-xl font-semibold">
              {{ formTranslation.common.profile }}
            </h3>
            <button
              class="px-4 py-2 text-sm text-white bg-black rounded hover:bg-black focus:outline-none focus:ring-2 focus:ring-black"
              @click="$router.go(-1)">
              <i class="fa fa-angle-double-left mr-2"></i>
              {{ formTranslation.common.back }}
            </button>
          </div>
        </header>

        <!-- Loading State -->
        <div v-if="pageLoader" class="p-4">
          <loader-component-2></loader-component-2>
        </div>

        <!-- Content -->
        <div v-else class="flex flex-wrap">
          <!-- Left Sidebar -->
          <div class="w-full md:w-1/4 p-4">
            <!-- Profile Card -->
            <div class="bg-white rounded-lg shadow-md p-4">
              <!-- Avatar Upload -->
              <div class="flex justify-center mt-4">
                <div class="relative">
                  <div class="w-32 h-32 rounded-full overflow-hidden">
                    <div class="w-full h-full bg-cover bg-center" :style="'background-image: url(' +
                      (patientData.user_profile
                        ? patientData.user_profile
                        : profileImage) +
                      ');'
                      "></div>
                  </div>
                  <button
                    class="absolute bottom-0 right-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700"
                    @click="uploadProfile" :title="formTranslation.clinic.edit_profile_img">
                    <i class="fas fa-pencil-alt"></i>
                  </button>
                </div>
              </div>

              <!-- Profile Info -->
              <div class="text-center mt-4">
                <h5 class="text-lg font-semibold">
                  {{ patientData.display_name }}
                </h5>
                <div class="text-gray-600">{{ patientData.user_email }}</div>
              </div>

              <!-- Navigation Menu -->
              <nav class="mt-6">
                <ul class="space-y-2">
                  <li class="flex items-center px-4 py-2 rounded cursor-pointer transition-colors duration-200" :class="currentRouteModule === 'profile'
                      ? 'bg-blue-50 text-blue-600'
                      : 'hover:bg-gray-50'
                    " @click="currentRouteModule = 'profile'">
                    <i class="fa fa-user mr-3"></i>
                    <span>{{ formTranslation.common.profile }}</span>
                  </li>

                  <li v-for="(custom_form_data, key) in patientData.custom_forms" :key="key" v-if="
                    userData.addOns.kiviPro &&
                    patientData.custom_forms?.length
                  " class="flex items-center px-4 py-2 rounded cursor-pointer transition-colors duration-200" :class="currentRouteModule ===
                        'custom_form_' + custom_form_data.id
                        ? 'bg-blue-50 text-blue-600'
                        : 'hover:bg-gray-50'
                      " @click="
                      currentRouteModule = 'custom_form_' + custom_form_data.id
                      ">
                    <i :class="[
                      'mr-3',
                      custom_form_data.name?.icon || 'fas fa-book-medical',
                    ]"></i>
                    <span>{{ custom_form_data.name?.text || "" }}</span>
                  </li>

                  <!-- <li
                    v-if="
                      userData.addOns.kiviPro &&
                      kcCheckPermission('patient_report')
                    "
                    class="flex items-center px-4 py-2 rounded cursor-pointer transition-colors duration-200"
                    :class="
                      currentRouteModule === 'report'
                        ? 'bg-blue-50 text-blue-600'
                        : 'hover:bg-gray-50'
                    "
                    @click="currentRouteModule = 'report'"
                  >
                    <i class="fa fa-file mr-3"></i>
                    <span>{{ formTranslation.reports.reports }}</span>
                  </li> -->

                  <li v-if="kcCheckPermission('appointment_list')"
                    class="flex items-center px-4 py-2 rounded cursor-pointer transition-colors duration-200" :class="currentRouteModule === 'appointment'
                        ? 'bg-blue-50 text-blue-600'
                        : 'hover:bg-gray-50'
                      " @click="currentRouteModule = 'appointment'">
                    <i class="fas fa-calendar-week mr-3"></i>
                    <span>{{ formTranslation.appointments.appointments }}</span>
                  </li>
                </ul>
              </nav>
            </div>
          </div>

          <!-- Main Content Area -->
          <div class="w-full md:w-3/4 p-4">
            <!-- Stats Cards -->

            <!-- Profile Form -->
            <div v-if="currentRouteModule === 'profile'" class="">
              <div class="bg-white rounded-lg shadow-md">
                <div class="p-6">
                  <h6 class="text-gray-600 mb-4">
                    {{ formTranslation.doctor.basic_information }}
                  </h6>

                  <!-- Basic Information Form -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- First Name -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="first_name">
                        {{ formTranslation.common.fname }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input id="first_name" type="text" v-model="patientData.first_name" :class="[
                        'w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500',
                        {
                          'border-red-500':
                            submitted && $v.patientData.first_name.$error,
                          'border-gray-300':
                            !submitted || !$v.patientData.first_name.$error,
                        },
                      ]" :placeholder="formTranslation.patient.fname_plh" />
                      <p v-if="submitted && !$v.patientData.first_name.required" class="mt-1 text-sm text-red-500">
                        {{ formTranslation.common.fname_required }}
                      </p>
                    </div>

                    <!-- Last Name -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="last_name">
                        {{ formTranslation.common.lname }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input id="last_name" type="text" v-model="patientData.last_name" :class="[
                        'w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500',
                        {
                          'border-red-500':
                            submitted && $v.patientData.last_name.$error,
                          'border-gray-300':
                            !submitted || !$v.patientData.last_name.$error,
                        },
                      ]" :placeholder="formTranslation.patient.lname_placeholder" />
                      <p v-if="submitted && !$v.patientData.last_name.required" class="mt-1 text-sm text-red-500">
                        {{ formTranslation.common.lname_required }}
                      </p>
                    </div>

                    <!-- Email -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="email">
                        {{ formTranslation.common.email_address }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input id="email" type="email" v-model="patientData.user_email" :class="[
                        'w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500',
                        {
                          'border-red-500':
                            submitted && $v.patientData.user_email.$error,
                          'border-gray-300':
                            !submitted || !$v.patientData.user_email.$error,
                        },
                      ]" :placeholder="formTranslation.patient.address_placeholder
                          " />
                      <p v-if="submitted && !$v.patientData.user_email.required" class="mt-1 text-sm text-red-500">
                        {{ formTranslation.common.email_required }}
                      </p>
                      <p v-else-if="
                        submitted && !$v.patientData.user_email.emailValidate
                      " class="mt-1 text-sm text-red-500">
                        {{ formTranslation.common.invalid_email }}
                      </p>
                    </div>

                    <!-- Phone Number -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="telephone_no">
                        {{ formTranslation.common.contact_no }}
                        <span class="text-red-500">*</span>
                      </label>
                      <VuePhoneNumberInput v-model="patientData.mobile_number" id="telephone_no"
                        :default-country-code="defaultCountryCode" @update="contactUpdateHandaler" :class="{
                          'border-red-500':
                            submitted && $v.patientData.mobile_number.$error,
                        }" clearable no-example />
                      <p v-if="
                        submitted && !$v.patientData.mobile_number.required
                      " class="mt-1 text-sm text-red-500">
                        {{ formTranslation.common.contact_num_required }}
                      </p>
                      <p v-else-if="
                        submitted &&
                        (!$v.patientData.mobile_number.minLength ||
                          !$v.patientData.mobile_number.maxLength)
                      " class="mt-1 text-sm text-red-500">
                        {{ formTranslation.common.contact_validation_1 }}
                      </p>
                    </div>

                    <!-- Date of Birth -->
                    <div v-if="!hideFields.includes('dob')">
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="dob">
                        {{ formTranslation.common.dob }}
                      </label>
                      <input type="date" id="dob" v-model="patientData.dob" :max="new Date().toISOString().slice(0, 10)"
                        class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                    </div>

                    <!-- Clinic Selection -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="clinic_id">
                        {{ formTranslation.patient.select_clinic }}
                        <span class="text-red-500">*</span>
                      </label>
                      <multi-select v-model="patientData.clinic_id" :options="clinics" :multiple="true" :taggable="true"
                        label="label" track-by="id" :loading="clinicMultiselectLoader" :placeholder="formTranslation.patient.search_placeholder
                          " class="w-full" />
                    </div>

                    <!-- Gender Selection -->
                    <div v-if="!hideFields.includes('blood_group')">
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{ formTranslation.common.gender }}
                        <span class="text-red-500">*</span>
                      </label>
                      <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                          <input type="radio" v-model="patientData.gender" value="male"
                            class="form-radio text-blue-600 h-4 w-4" />
                          <span class="ml-2">{{
                            formTranslation.common.male
                          }}</span>
                        </label>
                        <label class="inline-flex items-center">
                          <input type="radio" v-model="patientData.gender" value="female"
                            class="form-radio text-blue-600 h-4 w-4" />
                          <span class="ml-2">{{
                            formTranslation.common.female
                          }}</span>
                        </label>
                        <label v-if="defaultUserRegistrationFormSettingData === 'on'" class="inline-flex items-center">
                          <input type="radio" v-model="patientData.gender" value="other"
                            class="form-radio text-blue-600 h-4 w-4" />
                          <span class="ml-2">{{
                            formTranslation.common.other
                          }}</span>
                        </label>
                      </div>
                    </div>

                    <!-- NHS Number -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="nhs">
                        {{ formTranslation.common.nhs }}
                      </label>
                      <input id="nhs" type="text" v-model="patientData.nhs"
                        :placeholder="formTranslation.common.nhs_placeholder"
                        class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                    </div>

                    <!-- Registration Date -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Registered On
                      </label>
                      <div class="w-full rounded-md bg-gray-50 px-3 py-2 text-gray-700">
                        {{ patientData.user_registered | formatDate }}
                      </div>
                    </div>

                    <!-- Status Toggle -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{ formTranslation.common.status }}
                      </label>
                      <div class="flex items-center space-x-2">
                        <div class="relative inline-block w-10 mr-2 align-middle select-none">
                          <input type="checkbox" v-model="patientData.user_status" :true-value="'0'" :false-value="'1'"
                            class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
                          <label
                            class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                        </div>
                        <span class="px-2 py-1 text-xs font-semibold rounded" :class="{
                          'bg-green-100 text-green-800':
                            patientData.user_status === '0',
                          'bg-red-100 text-red-800':
                            patientData.user_status === '1',
                        }">
                          {{
                            patientData.user_status === "0"
                              ? formTranslation.common.active
                              : formTranslation.common.inactive
                          }}
                        </span>
                      </div>
                    </div>

                    <!-- GP Name -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="registered_gp_name">
                        {{ formTranslation.common.registered_gp_name }}
                      </label>
                      <input id="registered_gp_name" type="text" v-model="patientData.registered_gp_name"
                        :placeholder="formTranslation.common.registered_gp_name"
                        class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                    </div>

                    <!-- GP Address -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="registered_gp_address">
                        {{ formTranslation.common.registered_gp_address }}
                      </label>
                      <input id="registered_gp_address" type="text" v-model="patientData.registered_gp_address"
                        :placeholder="formTranslation.common.registered_gp_address"
                        class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                    </div>
                  </div>

                  <!-- Insurance Section -->
                  <div v-if="
                    !hideFields.includes('address') ||
                    !hideFields.includes('city') ||
                    !hideFields.includes('country') ||
                    !hideFields.includes('postal_code')
                  " class="mt-8">
                    <hr class="my-6" />
                    <h6 class="text-gray-600 mb-4">
                      {{ formTranslation.common.insurance_info }}
                    </h6>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Insurance provider Selection -->
                      <div v-if="
                        $store.state.userDataModule.user.addOns.kiviPro !==
                        false
                      ">
                        <label class="block text-sm font-medium text-gray-700 mb-1" for="choose_insurance_provider">
                          {{ formTranslation.common.insurance_provider }}
                        </label>
                        <multi-select v-model="patientData.insurance_provider" :options="kc_insurers" label="label"
                          track-by="insurance_provider" :multiple="false" :placeholder="formTranslation.common.insurance_provider
                            " class="w-full" />
                      </div>

                      <!-- insurance_no -->
                      <div v-if="!hideFields.includes('insurance_no')">
                        <label class="block text-sm font-medium text-gray-700 mb-1" for="insurance_no">
                          {{ formTranslation.common.insurance_no }}
                        </label>
                        <input id="insurance_no" type="text" v-model="patientData.insurance_no"
                          :placeholder="formTranslation.common.insurance_no"
                          class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                      </div>
                    </div>
                  </div>

                  <!-- Contact Information Section -->
                  <div v-if="
                    !hideFields.includes('address') ||
                    !hideFields.includes('city') ||
                    !hideFields.includes('country') ||
                    !hideFields.includes('postal_code')
                  " class="mt-8">
                    <hr class="my-6" />
                    <h6 class="text-gray-600 mb-4">
                      {{ formTranslation.common.contact_info }}
                    </h6>

                    <!-- Address -->
                    <div v-if="!hideFields.includes('address')" class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1" for="address">
                        {{ formTranslation.common.address }}
                      </label>
                      <textarea id="address" v-model="patientData.address" :placeholder="formTranslation.patient.address_placeholder
                        "
                        class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows="3"></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- City -->
                      <div v-if="!hideFields.includes('city')">
                        <label class="block text-sm font-medium text-gray-700 mb-1" for="city">
                          {{ formTranslation.common.city }}
                        </label>
                        <input id="city" type="text" v-model="patientData.city" :placeholder="formTranslation.patient.city_placeholder
                          "
                          class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                      </div>

                      <!-- Country -->
                      <div v-if="!hideFields.includes('country')">
                        <label class="block text-sm font-medium text-gray-700 mb-1" for="country">
                          {{ formTranslation.common.country }}
                        </label>
                        <input id="country" type="text" v-model="patientData.country" :placeholder="formTranslation.patient.country_placeholder
                          "
                          class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                      </div>

                      <!-- Postal Code -->
                      <div v-if="!hideFields.includes('postal_code')">
                        <label class="block text-sm font-medium text-gray-700 mb-1" for="postal_code">
                          {{ formTranslation.common.postal_code }}
                        </label>
                        <input id="postal_code" type="text" v-model="patientData.postal_code" :placeholder="formTranslation.patient.pcode_placeholder
                          "
                          class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                      </div>

                      <!-- Language Selection -->
                      <div v-if="
                        $store.state.userDataModule.user.addOns.kiviPro !==
                        false
                      ">
                        <label class="block text-sm font-medium text-gray-700 mb-1" for="choose_language">
                          {{ formTranslation.common.choose_language }}
                        </label>
                        <multi-select v-model="patientData.choose_language" :options="kc_available_translations"
                          label="label" track-by="lang" :multiple="false"
                          :placeholder="formTranslation.common.choose_language" class="w-full" />
                      </div>
                    </div>
                  </div>

                  <!-- Custom Fields Section -->
                  <!-- <div v-if="showCustomField" class="mt-8">
                    <hr class="my-6" />
                    <h6 class="text-gray-600 mb-4">
                      {{ formTranslation.doctor.extra_detail }}
                    </h6>
                    <edit-custom-fields module_type="" :module_id="String(patientData.ID)"
                      @bindCustomField="getCustomFieldsValues" :fieldsValue="custom_fields !== undefined ? custom_fields : []
                        " :customFieldsObj="custom_fields !== undefined ? custom_fields : []
                        " @requiredCustomField="getRequireFields" />
                  </div> -->
                </div>

                <!-- Form Footer -->
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                  <div class="flex justify-end">
                    <button v-if="!loading" @click="handleSubmit"
                      class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                      <i class="fa fa-save mr-2"></i>
                      {{ formTranslation.common.save }}
                    </button>
                    <button v-else
                      class="px-4 py-2 bg-blue-600 text-white rounded opacity-75 cursor-not-allowed flex items-center"
                      disabled>
                      <i class="fa fa-sync fa-spin mr-2"></i>
                      {{ formTranslation.common.loading }}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Reports Section -->
            <div v-if="
              currentRouteModule === 'report' &&
              userData.addOns.kiviPro &&
              kcCheckPermission('patient_report')
            ">
              <patientReport :patient_profile_id="$route.params.id" />
            </div>

            <!-- Appointments Section -->
            <div v-if="
              currentRouteModule === 'appointment' &&
              userData.addOns.kiviPro &&
              kcCheckPermission('appointment_list')
            ">
              <Appointment :patient_profile_id="$route.params.id" />
            </div>

            <!-- Custom Forms Section -->
            <div v-for="(custom_form_data, key) in patientData.custom_forms"
              v-if="currentRouteModule === 'custom_form_' + custom_form_data.id" :key="key"
              class="bg-white rounded-lg shadow-md">
              <div class="p-6">
                <CustomForm :data="customFormDataUpdate(custom_form_data, patientData.ID)" :viewMode="false"
                  :customFormModal="currentRouteModule === 'custom_form_' + custom_form_data.id
                    " :useModal="false" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { get, post } from "../../config/request";
import { required, minLength, maxLength } from "vuelidate/lib/validators";
import { emailValidate, validateForm } from "../../config/helper";
import patientReport from "./Report";
import Appointment from "../Appointment/AllappointmentList";
import CustomForm from "../CustomForm/Form.vue";

export default {
  name: "ProfileView",
  components: { patientReport, Appointment, VuePhoneNumberInput, CustomForm },
  data: () => {
    return {
      currentRouteModule: "profile",
      pageLoader: false,
      loading: false,
      profileImage:
        window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png",
      patientData: {},
      submitted: false,
      cardTitle: "Edit Profile",
      qualification: {},
      bloodGroups: ["A+", "B+", "AB+", "O+", "A-", "B-", "AB-", "O-"],
      file: "",
      hideFields: [],
      formLoader: true,
      showCustomField: false,
      custom_fields: [],
      defaultCountryCode: null,
      requiredFields: [],
      clinicMultiselectLoader: true,
      clinics: [],
      defaultUserRegistrationFormSettingData: "on",
    };
  },
  validations: {
    patientData: {
      first_name: {
        required,
      },
      last_name: {
        required,
      },
      user_email: {
        required,
        emailValidate,
      },
      mobile_number: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      clinic_id: {
        required,
      },
    },
  },
  filters: {
    formatDate(value) {
      if (!value) return "N/A";
      return new Date(value).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },
  },
  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();
    this.init();
    this.getClinics();
    this.patientData = this.defaultData();

    // Get patient ID either from params or directly from URL
    let patientId = this.$route.params.id;

    // If patient ID is not provided, show error message
    if (!patientId || patientId === undefined) {
      this.pageLoader = false;
      displayErrorMessage(this.formTranslation?.common?.patient_id_required || "Patient ID is required");
      return;
    }

    // Load patient details
    this.patientDetails(patientId);
    this.$store.dispatch("appointmentModule/fetchAppointmentEncounterCount", {
      id: patientId,
    });

    this.getHideFieldsArrayFromFilter();
  },
  methods: {
    contactUpdateHandaler: function (val) {
      this.patientData.country_code = val.countryCode;
      this.patientData.country_calling_code = val.countryCallingCode;
    },
    init() { },
    defaultData() {
      return {
        first_name: "",
        last_name: "",
        username: "",
        user_email: "",
        user_pass: "",
        country_calling_code: "",
        country_code: "",
        mobile_number: "",
        gender: "",
        dob: "",
        about_me: "",
        address: "",
        city: "",
        state: "",
        country: "",
        postal_code: "",
        blood_group: "default",
        profile_image: "",
        nhs: "",
        insurance_provider: "",
        insurance_no: "",
      };
    },
    patientDetails(id) {
      this.pageLoader = true;
      get("patient_edit", {
        id: id,
        request_type: "patient_profile",
      })
        .then((response) => {
          this.pageLoader = false;
          if (response.data.status && response.data.status === true && response.data.data) {
            // Ensure we have valid data
            if (!response.data.data.ID || !response.data.data.first_name) {
              displayErrorMessage(this.formTranslation?.common?.invalid_patient_data || "Invalid patient data");
              return;
            }

            this.patientData = response.data.data;

            // Get all user meta data for this patient to check for unique ID
            get("get_static_data", {
              data_type: "user_meta_data",
              user_id: id
            }).then((userMetaResponse) => {
              if (userMetaResponse.data.status && userMetaResponse.data.data) {
                // Set patient unique ID if it exists
                let metaData = userMetaResponse.data.data;
                if (metaData.patient_unique_id) {
                  this.patientData.u_id = metaData.patient_unique_id;
                } else {
                  // Generate a unique ID if patient doesn't have one
                  this.getNewUniqueId();
                }
              }
            }).catch(error => {
              console.error("Error getting patient meta data:", error);
            });

            if (response.data.data.country_calling_code) {
              this.defaultCountryCode = response.data.data.country_code;
            }
            this.showCustomField = true;
            this.custom_fields = response.data.custom_filed || [];

            // Set language if available
            if (this.kc_available_translations && this.kc_available_translations.length && response.data.data.choose_language) {
              this.patientData.choose_language = this.kc_available_translations.find(
                (el) => el.lang === response.data.data.choose_language
              );
            }
          } else {
            displayErrorMessage(this.formTranslation?.widgets?.record_not_found || "Patient record not found");
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          console.log(error);
          displayErrorMessage(this.formTranslation?.widgets?.record_not_found || "Patient record not found");
        });
    },

    getNewUniqueId() {
      // Always try to use the backend API first to get a properly formatted and unique ID
      get("generate_patient_unique_id", {})
        .then(response => {
          if (response.data.status) {
            this.patientData.u_id = response.data.data;
          } else {
            // If the API fails, generate a local ID and check if it exists
            this.generateLocalUniqueId();
          }
        })
        .catch(error => {
          console.error("Error getting patient unique ID:", error);
          // If the API call fails, generate a local ID and check if it exists
          this.generateLocalUniqueId();
        });
    },

    generateLocalUniqueId() {
      // Generate a local ID in the correct format
      const prefix = "M";
      const randomDigits = Math.floor(100000 + Math.random() * 900000); // 6-digit number
      const generatedId = `${prefix}${randomDigits}`;

      // Check if this ID already exists in the system
      get("check_patient_id_exists", { patient_id: generatedId })
        .then(response => {
          if (response.data && response.data.exists === false) {
            // ID doesn't exist, safe to use
            this.patientData.u_id = generatedId;
          } else {
            // ID exists or couldn't verify, try again with a different number
            this.generateLocalUniqueId(); // Recursively try again
          }
        })
        .catch(error => {
          console.error("Error checking patient ID existence:", error);
          // In case of error, add timestamp to make collision extremely unlikely
          const timestamp = new Date().getTime().toString().slice(-3);
          this.patientData.u_id = `${prefix}${randomDigits}${timestamp}`.substring(0, 9);
        });
    },
    getCustomFieldsValues: function (fieldsObj) {
      if (!fieldsObj || fieldsObj === undefined) {
        return false;
      }
      this.patientData.custom_fields = fieldsObj;
    },
    getHideFieldsArrayFromFilter: function () {
      get("get_hide_fields_array_from_filter", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.hideFields = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    uploadProfile() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.patientData.user_profile = attachment.url;
        _this.patientData.profile_image = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    handleSubmit: function () {
      this.loading = true;

      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      this.$nextTick(() => {
        if (
          document.querySelector(".is-invalid") !== null &&
          document.querySelector(".is-invalid") !== undefined
        ) {
          document
            .querySelector(".is-invalid")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        } else if (
          document.querySelector(".invalid-feedback") !== null &&
          document.querySelector(".invalid-feedback") !== undefined
        ) {
          document
            .querySelector(".invalid-feedback")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        }
      });
      if (this.$v.patientData.$invalid) {
        this.loading = false;
        return;
      }

      if (this.requiredFields.length > 0) {
        this.loading = false;
        displayErrorMessage(
          this.formTranslation.common.all_required_field_validation
        );
        return;
      }

      // Check if patient unique ID is required and generate one if missing
      if (!this.patientData.u_id) {
        // Make a request to get a new unique ID
        get("generate_patient_unique_id", {})
          .then(response => {
            if (response.data.status) {
              this.patientData.u_id = response.data.data;
              this.performSave();
            } else {
              this.loading = false;
              displayErrorMessage(
                this.formTranslation?.common?.patient_id_required || "Patient Unique ID is required"
              );
            }
          })
          .catch(error => {
            console.error("Error getting patient unique ID:", error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation?.common?.patient_id_required || "Patient Unique ID is required"
            );
          });
      } else {
        this.performSave();
      }
    },

    performSave: function() {
      // This function contains the actual save logic that was originally in handleSubmit

      if (validateForm("patientDataForm")) {
        post("patient_save", this.patientData)
          .then((response) => {
            this.loading = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              if (response.data.choose_language_updated) {
                this.$store.dispatch(
                  "staticDataModule/refreshDashboardLocale",
                  { self: this }
                );
              }
              // this.patientData.dob = new Date(moment(this.patientData.dob).format("YYYY-MM-DD"));
              displayMessage(response.data.message);
              this.isEditProfile = false;
            } else {
              // this.patientData.dob = new Date(moment(this.patientData.dob).format("YYYY-MM-DD"));
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            // this.patientData.dob = new Date(moment(this.patientData.dob).format("YYYY-MM-DD"));
            console.log(error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    getCountryCodeData: function () {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultCountryCode = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getRequireFields: function (validateRequired) {
      this.requiredFields = validateRequired;
    },
    getClinics: function () {
      this.clinicMultiselectLoader = true;
      get("get_static_data", {
        data_type: "clinic_list",
      })
        .then((response) => {
          this.clinicMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.clinics = response.data.data;
          }
        })
        .catch((error) => {
          this.clinicMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getUserRegistrationFormData: function () {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    customFormDataUpdate(custom_form_data, id) {
      custom_form_data.module_id = id;
      return custom_form_data;
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    appointment_encounter_data() {
      return this.$store.state.appointmentModule.appointment_encounter_data;
    },
    kc_available_translations() {
      return this.$store.state.userDataModule.user.kc_available_translations;
    },
    kc_insurers() {
      return [
        { label: "Bupa", insurance_provider: "Bupa" },
        {
          label: "AXA PPP Healthcare",
          insurance_provider: "AXA PPP Healthcare",
        },
        { label: "Aviva Health UK", insurance_provider: "Aviva Health UK" },
        { label: "VitalityHealth", insurance_provider: "VitalityHealth" },
        { label: "WPA Healthcare", insurance_provider: "WPA Healthcare" },
        { label: "Cigna Healthcare", insurance_provider: "Cigna Healthcare" },
        { label: "Simply Health", insurance_provider: "Simply Health" },
        { label: "The Exeter", insurance_provider: "The Exeter" },
        {
          label: "Freedom Health Insurance",
          insurance_provider: "Freedom Health Insurance",
        },
        { label: "National Friendly", insurance_provider: "National Friendly" },
        { label: "Other", insurance_provider: "Other" },
      ];
    },
  },
};
</script>

<style scoped>
li {
  border-radius: 4px;
  cursor: pointer;
}

.active-li {
  background-color: var(--primary) !important;
}

.active-li i {
  color: #fff !important;
}

.active-li span {
  color: #fff !important;
}
.phone-input-dropdown {
  z-index: 50; /* Adjust this value as needed */
}
</style>
